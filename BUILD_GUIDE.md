# BUILD_GUIDE.md

## Secure Build Creation for Distribution

This guide provides comprehensive instructions for creating secure builds of the MobileAppAutomation project for distribution across Windows, macOS, and Linux. The process uses PyInstaller for packaging and python-minifier for code obfuscation to ensure security.

### Security Considerations (General)
- **Code Obfuscation**: All builds use python-minifier to obfuscate source code, removing docstrings, annotations, and combining imports. Fallback to base64 encoding if minification fails.
- **Integrity Verification**: Post-build scanning ensures no plain source files remain. Use `scan_obfuscation.py` to verify.
- **Runtime Security**: Builds set `SECURE_BUILD=True`, binding to localhost (127.0.0.1) and disabling Flask debug mode.
- **Dependency Management**: Only include verified dependencies; scan for vulnerabilities.
- **Signing and Notarization**: OS-specific signing to prevent tampering warnings.
- **Best Practices**: Build in a clean environment, use version control for sources, and verify builds with checksums.

### System Requirements for Building
- Python 3.8+
- PyInstaller and python-minifier installed
- OS-specific tools (e.g., Xcode for macOS)
- Clean virtual environment recommended

### Step-by-Step Build Instructions

#### 1. Windows
**Security Considerations**: Use Windows Defender exclusions during build; sign executables with signtool to avoid SmartScreen warnings. Ensure no sensitive data in build artifacts.

**Steps**:
1. Install Python 3.8+ from python.org.
2. Create and activate a virtual environment: `python -m venv venv` then `venv\Scripts\activate`.
3. Install dependencies: `pip install -r requirements.txt` (include PyInstaller and python-minifier).
4. Navigate to `secure_distribution_app/`.
5. Run the build script: `python build_final_secure_app.py`.
6. Verify obfuscation: `python security/scan_obfuscation.py`.
7. Sign the executable using signtool (requires certificate).
8. Output: Executable in `dist/SecureMobileAppAutomation.exe`.

**Troubleshooting**:
- Dependency errors: Ensure Visual C++ Build Tools are installed.
- Obfuscation failures: Check python-minifier version; retry with fallback options.

#### 2. macOS
**Security Considerations**: Notarize the app bundle with Apple to pass Gatekeeper. Use hardened runtime and entitlements for security.

**Steps**:
1. Install Python 3.8+ via Homebrew or python.org.
2. Create and activate venv: `python3 -m venv venv` then `source venv/bin/activate`.
3. Install dependencies: `pip install -r requirements.txt`.
4. Install macOS-specific tools: `brew install libimobiledevice ideviceinstaller`.
5. Run `python3 build_final_secure_app.py` in `secure_distribution_app/`.
6. Verify with `python3 security/scan_obfuscation.py`.
7. Codesign and notarize: Use `codesign` and `xcrun notarytool`.
8. Output: App bundle in `dist/SecureMobileAppAutomation.app`.

**Troubleshooting**:
- Xcode requirements: Ensure Command Line Tools are installed.
- Notarization failures: Check Apple Developer account and entitlements file.

#### 3. Linux
**Security Considerations**: Use AppArmor or SELinux profiles for runtime security. Generate and verify checksums for distribution packages.

**Steps**:
1. Install Python 3.8+ via package manager (e.g., `sudo apt install python3.8 python3.8-venv` on Ubuntu).
2. Create venv: `python3.8 -m venv venv` then `source venv/bin/activate`.
3. Install dependencies: `pip install -r requirements.txt`.
4. Install system deps: `sudo apt install build-essential libssl-dev` (Ubuntu example).
5. Run `python3 build_final_secure_app.py`.
6. Verify obfuscation.
7. Package as needed (e.g., into .deb or .rpm).
8. Output: Executable in `dist/SecureMobileAppAutomation`.

**Troubleshooting**:
- Library issues: Install missing dev packages (e.g., python3-dev).
- Permission errors: Run build as non-root; use sudo only for system installs.

### Best Practices for Deployment
- Build on the target OS for compatibility.
- Use CI/CD pipelines for automated builds.
- Generate and publish SHA256 checksums.
- Test builds in virtual machines.
- Document version-specific changes.

For installation of built packages, see INSTALLATION_GUIDE.md.