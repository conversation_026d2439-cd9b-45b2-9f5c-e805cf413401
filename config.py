import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# Allow CLI tools to bypass centralized directory DB if requested
USE_DIRECTORY_DB = os.environ.get('SKIP_DIRECTORY_DB', 'false').lower() != 'true'

DirectoryPathsDB = None
if USE_DIRECTORY_DB:
    try:
        from app.utils.directory_paths_db import DirectoryPathsDB  # type: ignore
    except Exception as exc:  # pragma: no cover - defensive fallback
        logger.warning("Falling back to default directories (DirectoryPathsDB import failed: %s)", exc)
        DirectoryPathsDB = None


BASE_DIR = Path(__file__).resolve().parent
WORKSPACE_ROOT = Path(os.environ.get('AUTOMATION_WORKSPACE', Path.home() / 'MobileAutomationWorkspace'))
IOS_WORKSPACE = WORKSPACE_ROOT / 'ios'
SHARED_WORKSPACE = WORKSPACE_ROOT / 'shared'

directory_paths_db = None
if DirectoryPathsDB:
    try:
        directory_paths_db = DirectoryPathsDB()
    except Exception as exc:  # pragma: no cover - defensive fallback
        logger.warning("DirectoryPathsDB initialisation failed, using defaults: %s", exc)
        directory_paths_db = None


def _ensure_directory(path: Path) -> Path:
    path.mkdir(parents=True, exist_ok=True)
    return path


def _resolve_directory(key: str, default_subpath: str) -> Path:
    if directory_paths_db:
        override = directory_paths_db.get_path(key)
        if override:
            override_path = Path(override)
            if not override_path.is_absolute():
                override_path = WORKSPACE_ROOT / override_path
            return _ensure_directory(override_path)

    return _ensure_directory(IOS_WORKSPACE / default_subpath)


DIRECTORIES = {
    'TEST_CASES': _resolve_directory('TEST_CASES', 'test_cases'),
    'REPORTS': _resolve_directory('REPORTS', 'reports'),
    'SCREENSHOTS': _resolve_directory('SCREENSHOTS', 'screenshots'),
    'REFERENCE_IMAGES': _resolve_directory('REFERENCE_IMAGES', 'reference_images'),
    'TEST_SUITES': _resolve_directory('TEST_SUITES', 'test_suites'),
    'RESULTS': _resolve_directory('RESULTS', 'reports/suites'),
    'RECORDINGS': _resolve_directory('RECORDINGS', 'recordings'),
    'TEMP_FILES': _resolve_directory('TEMP_FILES', 'temp'),
}

FILES_TO_PUSH_DIR = _resolve_directory('FILES_TO_PUSH', '../shared/files_to_push')

# iOS scaling configuration
IOS_SCALE_FACTORS = {
    'default': 0.33,
    'iPhone Simulator': 0.33,
    'iPhone X': 0.33,
    'iPhone 11': 0.33,
    'iPhone 12': 0.33,
    'iPhone 13': 0.33,
    'iPhone 14': 0.33,
    'iPhone 15': 0.33,
    'iPad Simulator': 0.33,
}

IOS_TEMPLATE_SCALES = {
    'default': 1.0,
    'iPhone X': 1.0,
    'iPhone 11': 1.0,
    'iPhone 12': 1.0,
    'iPhone 13': 1.0,
    'iPhone 14': 1.0,
    'iPhone 15': 1.0,
    'iPad Simulator': 1.0,
}

GLOBAL_VALUES = {
    'Auto Rerun Failed': False,
    'Connection Retry Attempts': 3,
    'Connection Retry Delay': 2,
    'Max Step Execution Time': 300,
    'Test Case Delay': 15,
    'Test Run Retry': 3,
    'default_element_timeout': 60,
}

DEFAULT_FLASK_PORT = 8080
DEFAULT_APPIUM_PORT = 4723
DEFAULT_WDA_PORT = 8200

FLASK_PORT = int(os.environ.get('FLASK_PORT', DEFAULT_FLASK_PORT))
APPIUM_PORT = int(os.environ.get('APPIUM_PORT', DEFAULT_APPIUM_PORT))
WDA_PORT = int(os.environ.get('WDA_PORT', DEFAULT_WDA_PORT))

APPIUM_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': APPIUM_PORT,
    'BASE_PATH': '/wd/hub'
}

WDA_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': WDA_PORT,
    'BASE_PATH': ''
}

ADB_CONFIG = {
    'TIMEOUT': 10,
    'MAX_RETRIES': 3
}


class FlaskConfig:
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'mobile-automation-secret-key')
    SESSION_COOKIE_NAME = 'ios_session'
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_SECURE = False
    TESTING = False
    DEBUG = False
    PORT = FLASK_PORT
    HOST = '0.0.0.0'
    TEST_CASES_DIR = str(DIRECTORIES['TEST_CASES'])
    REPORTS_DIR = str(DIRECTORIES['REPORTS'])
    SCREENSHOTS_DIR = str(DIRECTORIES['SCREENSHOTS'])
    TEMP_DIR = str(DIRECTORIES['TEMP_FILES'])
