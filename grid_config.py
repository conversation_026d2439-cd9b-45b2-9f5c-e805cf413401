#!/usr/bin/env python3
"""
Appium Grid Configuration Module

This module provides configuration and utilities for connecting to Appium Grid
instead of direct Appium server connections. It enables session isolation
and parallel testing across iOS and Android platforms.
"""

import os
import requests
import logging
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)

class GridConfig:
    """Configuration class for Appium Grid setup"""
    
    # Grid Hub configuration
    GRID_HUB_HOST = "127.0.0.1"
    GRID_HUB_PORT = 4444
    GRID_HUB_URL = f"http://{GRID_HUB_HOST}:{GRID_HUB_PORT}/wd/hub"
    
    # Node configurations
    IOS_NODE_PORT = 4723
    ANDROID_NODE_PORT = 4724
    
    # Direct Appium URLs (fallback)
    IOS_DIRECT_URL = f"http://127.0.0.1:{IOS_NODE_PORT}/wd/hub"
    ANDROID_DIRECT_URL = f"http://127.0.0.1:{ANDROID_NODE_PORT}/wd/hub"
    
    # Grid status URLs
    GRID_STATUS_URL = f"http://{GRID_HUB_HOST}:{GRID_HUB_PORT}/wd/hub/status"
    IOS_NODE_STATUS_URL = f"http://127.0.0.1:{IOS_NODE_PORT}/wd/hub/status"
    ANDROID_NODE_STATUS_URL = f"http://127.0.0.1:{ANDROID_NODE_PORT}/wd/hub/status"
    
    # Environment variable to enable/disable grid
    GRID_ENABLED_ENV = "APPIUM_GRID_ENABLED"
    
    @classmethod
    def is_grid_enabled(cls) -> bool:
        """Check if Grid mode is enabled via environment variable"""
        return os.getenv(cls.GRID_ENABLED_ENV, "true").lower() in ("true", "1", "yes", "on")
    
    @classmethod
    def is_grid_available(cls) -> bool:
        """Check if the Grid Hub is running and available"""
        try:
            response = requests.get(cls.GRID_STATUS_URL, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('value', {}).get('ready', False)
        except Exception as e:
            logger.debug(f"Grid availability check failed: {e}")
        return False
    
    @classmethod
    def is_node_available(cls, platform: str) -> bool:
        """Check if a specific node is available"""
        if platform.lower() == 'ios':
            url = cls.IOS_NODE_STATUS_URL
        elif platform.lower() == 'android':
            url = cls.ANDROID_NODE_STATUS_URL
        else:
            return False
            
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('value', {}).get('ready', False)
        except Exception as e:
            logger.debug(f"{platform} node availability check failed: {e}")
        return False
    
    @classmethod
    def get_connection_url(cls, platform: str) -> str:
        """Get the appropriate connection URL based on grid availability"""
        platform = platform.lower()
        
        # If grid is disabled, use direct connection
        if not cls.is_grid_enabled():
            logger.info(f"Grid disabled, using direct connection for {platform}")
            if platform == 'ios':
                return cls.IOS_DIRECT_URL
            elif platform == 'android':
                return cls.ANDROID_DIRECT_URL
            else:
                raise ValueError(f"Unsupported platform: {platform}")
        
        # Check if grid is available
        if cls.is_grid_available():
            logger.info(f"Grid available, using Grid Hub for {platform}")
            return cls.GRID_HUB_URL
        else:
            logger.warning(f"Grid not available, falling back to direct connection for {platform}")
            if platform == 'ios':
                return cls.IOS_DIRECT_URL
            elif platform == 'android':
                return cls.ANDROID_DIRECT_URL
            else:
                raise ValueError(f"Unsupported platform: {platform}")
    
    @classmethod
    def get_grid_status(cls) -> Dict:
        """Get comprehensive grid status information"""
        status = {
            'grid_enabled': cls.is_grid_enabled(),
            'grid_available': False,
            'hub_url': cls.GRID_HUB_URL,
            'nodes': {
                'ios': {
                    'available': False,
                    'url': cls.IOS_DIRECT_URL,
                    'port': cls.IOS_NODE_PORT
                },
                'android': {
                    'available': False,
                    'url': cls.ANDROID_DIRECT_URL,
                    'port': cls.ANDROID_NODE_PORT
                }
            },
            'recommended_urls': {
                'ios': cls.get_connection_url('ios'),
                'android': cls.get_connection_url('android')
            }
        }
        
        # Check grid availability
        status['grid_available'] = cls.is_grid_available()
        
        # Check node availability
        status['nodes']['ios']['available'] = cls.is_node_available('ios')
        status['nodes']['android']['available'] = cls.is_node_available('android')
        
        return status
    
    @classmethod
    def validate_setup(cls) -> Tuple[bool, str]:
        """Validate the grid setup and return status with message"""
        if not cls.is_grid_enabled():
            return True, "Grid disabled - using direct connections"
        
        if not cls.is_grid_available():
            return False, "Grid Hub is not available. Please start the grid with: ./grid/start-grid.sh"
        
        ios_available = cls.is_node_available('ios')
        android_available = cls.is_node_available('android')
        
        if not ios_available and not android_available:
            return False, "Neither iOS nor Android nodes are available"
        elif not ios_available:
            return False, "iOS node is not available"
        elif not android_available:
            return False, "Android node is not available"
        else:
            return True, "Grid setup is fully operational"


def enable_grid():
    """Enable grid mode"""
    os.environ[GridConfig.GRID_ENABLED_ENV] = "true"
    logger.info("Appium Grid enabled")


def disable_grid():
    """Disable grid mode"""
    os.environ[GridConfig.GRID_ENABLED_ENV] = "false"
    logger.info("Appium Grid disabled")


def get_connection_info(platform: str) -> Dict:
    """Get connection information for a platform"""
    return {
        'platform': platform,
        'url': GridConfig.get_connection_url(platform),
        'grid_enabled': GridConfig.is_grid_enabled(),
        'grid_available': GridConfig.is_grid_available(),
        'node_available': GridConfig.is_node_available(platform)
    }


if __name__ == "__main__":
    # CLI interface for testing
    import sys
    import json
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "status":
            status = GridConfig.get_grid_status()
            print(json.dumps(status, indent=2))
        elif command == "validate":
            is_valid, message = GridConfig.validate_setup()
            print(f"Valid: {is_valid}")
            print(f"Message: {message}")
        elif command == "enable":
            enable_grid()
            print("Grid enabled")
        elif command == "disable":
            disable_grid()
            print("Grid disabled")
        elif command in ["ios", "android"]:
            info = get_connection_info(command)
            print(json.dumps(info, indent=2))
        else:
            print("Usage: python grid_config.py [status|validate|enable|disable|ios|android]")
    else:
        # Default: show status
        status = GridConfig.get_grid_status()
        print(json.dumps(status, indent=2))