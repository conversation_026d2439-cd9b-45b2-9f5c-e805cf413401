-- Mobile App Automation - Supabase Database Setup
-- Run this SQL in your Supabase SQL Editor to set up the required tables

-- =============================================================================
-- 1. CREATE USER PROFILES TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    license_number VARCHAR(50),
    license_expiry TIMESTAMPTZ,
    hardware_fingerprint VARCHAR(64),
    hardware_bound_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMES<PERSON>MPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_login TIMESTAMPTZ,
    last_validation TIMESTAMPTZ,
    total_logins INTEGER DEFAULT 0,
    
    UNIQUE(user_id),
    UNIQUE(license_number)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_license ON user_profiles(license_number);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- =============================================================================
-- 2. CREATE USER SESSIONS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE,
    client_fingerprint VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT true
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active, expires_at);

-- =============================================================================
-- 3. CREATE AUDIT LOGS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- =============================================================================
-- 4. CREATE DOWNLOAD LOGS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS download_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    app_id VARCHAR(100) NOT NULL,
    app_name VARCHAR(255),
    download_started_at TIMESTAMPTZ DEFAULT NOW(),
    download_completed_at TIMESTAMPTZ,
    file_size BIGINT,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT false,
    error_message TEXT
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_download_logs_user_id ON download_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_download_logs_app_id ON download_logs(app_id);
CREATE INDEX IF NOT EXISTS idx_download_logs_started_at ON download_logs(download_started_at);

-- =============================================================================
-- 5. ENABLE ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE download_logs ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- 6. CREATE SECURITY POLICIES
-- =============================================================================

-- User profiles: Users can only see/edit their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- User sessions: Users can only see their own sessions
DROP POLICY IF EXISTS "Users can view own sessions" ON user_sessions;
CREATE POLICY "Users can view own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own sessions" ON user_sessions;
CREATE POLICY "Users can insert own sessions" ON user_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own sessions" ON user_sessions;
CREATE POLICY "Users can update own sessions" ON user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- Audit logs: Users can only see their own logs
DROP POLICY IF EXISTS "Users can view own audit logs" ON audit_logs;
CREATE POLICY "Users can view own audit logs" ON audit_logs
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert audit logs" ON audit_logs;
CREATE POLICY "Users can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Download logs: Users can only see their own downloads
DROP POLICY IF EXISTS "Users can view own downloads" ON download_logs;
CREATE POLICY "Users can view own downloads" ON download_logs
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own downloads" ON download_logs;
CREATE POLICY "Users can insert own downloads" ON download_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Service role can access everything (for admin functions)
DROP POLICY IF EXISTS "Service role full access profiles" ON user_profiles;
CREATE POLICY "Service role full access profiles" ON user_profiles
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

DROP POLICY IF EXISTS "Service role full access sessions" ON user_sessions;
CREATE POLICY "Service role full access sessions" ON user_sessions
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

DROP POLICY IF EXISTS "Service role full access audit" ON audit_logs;
CREATE POLICY "Service role full access audit" ON audit_logs
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

DROP POLICY IF EXISTS "Service role full access downloads" ON download_logs;
CREATE POLICY "Service role full access downloads" ON download_logs
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================================================
-- 7. CREATE DATABASE FUNCTIONS
-- =============================================================================

-- Function to create user profile when user signs up
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (user_id, email, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile
DROP TRIGGER IF EXISTS create_user_profile_trigger ON auth.users;
CREATE TRIGGER create_user_profile_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION create_user_profile();

-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for user_profiles
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- =============================================================================
-- 8. CREATE TEST USER (OPTIONAL)
-- =============================================================================

-- Uncomment and modify these lines to create a test user
-- Replace with your actual email and desired password

/*
-- Create test user
INSERT INTO auth.users (
    id, email, encrypted_password, email_confirmed_at, created_at, updated_at
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    crypt('testpassword123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Create test user profile
INSERT INTO user_profiles (
    user_id, email, first_name, last_name, license_number, is_active
) VALUES (
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    '<EMAIL>',
    'Test',
    'User',
    'TEST-LICENSE-001',
    true
) ON CONFLICT (user_id) DO NOTHING;
*/

-- =============================================================================
-- 9. VERIFICATION QUERIES
-- =============================================================================

-- Check if tables were created successfully
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_profiles', 'user_sessions', 'audit_logs', 'download_logs');

-- Check RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('user_profiles', 'user_sessions', 'audit_logs', 'download_logs');

-- Check policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('user_profiles', 'user_sessions', 'audit_logs', 'download_logs');

-- =============================================================================
-- SETUP COMPLETE
-- =============================================================================å

-- Your database is now ready for the Mobile App Automation system!
-- 
-- Next steps:
-- 1. Test the connection with the debug script
-- 2. Create your first user account
-- 3. Configure the application environment variables
-- 4. Test user registration and login
