#!/usr/bin/env python3
"""
Port Cleanup Script for Mobile App Automation
Kills processes on iOS and Android platform ports before launching
"""

import subprocess
import sys
import platform

def kill_processes_on_port(port):
    """Kill all processes listening on the specified port"""
    killed_pids = []
    
    try:
        if platform.system().lower() == 'windows':
            # Windows: Use PowerShell to find and kill processes
            cmd = f"(Get-NetTCPConnection -LocalPort {port} -ErrorAction SilentlyContinue).OwningProcess"
            result = subprocess.run(['powershell', '-NoProfile', '-Command', cmd], 
                                  capture_output=True, text=True, timeout=10)
            
            for line in result.stdout.strip().splitlines():
                pid = line.strip()
                if pid.isdigit():
                    try:
                        subprocess.run(['taskkill', '/PID', pid, '/F'], 
                                     check=False, capture_output=True)
                        killed_pids.append(int(pid))
                        print(f"   ✅ Killed PID {pid} on port {port}")
                    except Exception as e:
                        print(f"   ⚠️  Failed to kill PID {pid}: {e}")
        else:
            # macOS/Linux: Use lsof and kill
            result = subprocess.run(f"lsof -tiTCP:{port} -sTCP:LISTEN", 
                                  shell=True, capture_output=True, text=True, timeout=5)
            
            for line in result.stdout.strip().split():
                if line.isdigit():
                    try:
                        subprocess.run(['kill', '-9', line], 
                                     check=False, capture_output=True)
                        killed_pids.append(int(line))
                        print(f"   ✅ Killed PID {line} on port {port}")
                    except Exception as e:
                        print(f"   ⚠️  Failed to kill PID {line}: {e}")
                        
    except subprocess.TimeoutExpired:
        print(f"   ⚠️  Timeout while checking port {port}")
    except Exception as e:
        print(f"   ⚠️  Error checking port {port}: {e}")
    
    return killed_pids

def cleanup_ios_ports():
    """Clean up all iOS-related ports"""
    print("🧹 Cleaning up iOS ports...")
    ios_ports = [8090, 4723, 8200]  # Flask, Appium, WDA
    total_killed = 0
    
    for port in ios_ports:
        print(f"   Checking port {port}...")
        killed = kill_processes_on_port(port)
        total_killed += len(killed)
        
        if not killed:
            print(f"   ✅ Port {port} is free")
    
    return total_killed

def cleanup_android_ports():
    """Clean up all Android-related ports"""
    print("🧹 Cleaning up Android ports...")
    android_ports = [8091, 4724, 8300]  # Flask, Appium, WDA
    total_killed = 0
    
    for port in android_ports:
        print(f"   Checking port {port}...")
        killed = kill_processes_on_port(port)
        total_killed += len(killed)
        
        if not killed:
            print(f"   ✅ Port {port} is free")
    
    return total_killed

def main():
    """Main cleanup function"""
    print("🚀 Mobile App Automation - Port Cleanup")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        platform_arg = sys.argv[1].lower()
        if platform_arg == 'ios':
            total = cleanup_ios_ports()
        elif platform_arg == 'android':
            total = cleanup_android_ports()
        else:
            print("Usage: python cleanup_ports.py [ios|android]")
            print("Or run without arguments to clean both platforms")
            return
    else:
        # Clean both platforms
        ios_killed = cleanup_ios_ports()
        android_killed = cleanup_android_ports()
        total = ios_killed + android_killed
    
    print(f"\n🎉 Cleanup complete! Killed {total} processes.")
    
    if total > 0:
        print("✅ Platform ports are now available for launching")
    else:
        print("✅ All ports were already free")

if __name__ == "__main__":
    main()
