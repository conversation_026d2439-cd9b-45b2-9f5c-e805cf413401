#!/usr/bin/env python3
"""
Test Case File Standardization Script
=====================================

This script updates all existing JSON test case files to include the test_case_id field
matching the corresponding database entry. It performs bidirectional synchronization
between the file system and database.

Features:
- Reads from test_cases table and injects test_case_id into JSON files
- Handles cases where files exist but database entries don't
- Handles cases where database entries exist but files don't
- Creates missing database entries for orphaned files
- Updates existing files with proper test_case_id
- Validates JSON structure and fixes common issues

Usage: python standardize_test_case_files.py [--dry-run] [--verbose]
"""

import os
import sys
import json
import logging
import sqlite3
import argparse
from datetime import datetime
import uuid

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestCaseStandardizer:
    def __init__(self, dry_run=False, verbose=False):
        self.dry_run = dry_run
        self.verbose = verbose
        self.test_cases_dir = "app/test_cases"
        self.db_path = "app/data/ios_test_suites.db"
        self.stats = {
            'files_processed': 0,
            'files_updated': 0,
            'database_entries_created': 0,
            'database_entries_updated': 0,
            'errors': 0,
            'orphaned_files': 0,
            'orphaned_db_entries': 0
        }
        
    def connect_database(self):
        """Connect to the database and return connection"""
        if not os.path.exists(self.db_path):
            logger.error(f"Database not found: {self.db_path}")
            return None
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_all_test_cases_from_db(self):
        """Get all test cases from database"""
        conn = self.connect_database()
        if not conn:
            return []
        
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT test_case_id, name, file_path, status FROM test_cases")
            results = cursor.fetchall()
            return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Error reading from database: {e}")
            return []
        finally:
            conn.close()
    
    def get_all_json_files(self):
        """Get all JSON files from test cases directory"""
        if not os.path.exists(self.test_cases_dir):
            logger.error(f"Test cases directory not found: {self.test_cases_dir}")
            return []
        
        json_files = []
        for filename in os.listdir(self.test_cases_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.test_cases_dir, filename)
                json_files.append({
                    'filename': filename,
                    'filepath': filepath,
                    'exists': os.path.exists(filepath)
                })
        
        return json_files
    
    def read_json_file(self, filepath):
        """Read and parse JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in {filepath}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error reading {filepath}: {e}")
            return None
    
    def write_json_file(self, filepath, data):
        """Write JSON data to file"""
        if self.dry_run:
            logger.info(f"[DRY RUN] Would write to {filepath}")
            return True
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Error writing to {filepath}: {e}")
            return False
    
    def create_database_entry(self, filename, test_case_data):
        """Create a new database entry for an orphaned file"""
        if self.dry_run:
            logger.info(f"[DRY RUN] Would create database entry for {filename}")
            return True
        
        conn = self.connect_database()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            test_case_id = self.generate_test_case_id()
            name = test_case_data.get('name', filename.replace('.json', ''))
            
            cursor.execute("""
                INSERT INTO test_cases (test_case_id, name, filename, status, created_at, updated_at)
                VALUES (?, ?, ?, 'active', ?, ?)
            """, (test_case_id, name, filename, datetime.now(), datetime.now()))
            
            conn.commit()
            logger.info(f"Created database entry: {test_case_id} for {filename}")
            self.stats['database_entries_created'] += 1
            return test_case_id
        except Exception as e:
            logger.error(f"Error creating database entry for {filename}: {e}")
            return False
        finally:
            conn.close()
    
    def generate_test_case_id(self):
        """Generate a unique test case ID"""
        # Use the same format as the existing system
        import string
        import random
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choices(chars, k=6))
    
    def standardize_file(self, file_info, db_entry=None):
        """Standardize a single JSON file"""
        filepath = file_info['filepath']
        filename = file_info['filename']
        
        logger.info(f"Processing {filename}...")
        self.stats['files_processed'] += 1
        
        # Read the JSON file
        test_case_data = self.read_json_file(filepath)
        if test_case_data is None:
            self.stats['errors'] += 1
            return False
        
        # Check if test_case_id already exists and is correct
        current_test_case_id = test_case_data.get('test_case_id')
        expected_test_case_id = db_entry['test_case_id'] if db_entry else None
        
        needs_update = False
        
        if db_entry:
            # File has corresponding database entry
            if current_test_case_id != expected_test_case_id:
                logger.info(f"Updating test_case_id in {filename}: {current_test_case_id} -> {expected_test_case_id}")
                test_case_data['test_case_id'] = expected_test_case_id
                needs_update = True
        else:
            # Orphaned file - create database entry
            logger.warning(f"Orphaned file found: {filename}")
            self.stats['orphaned_files'] += 1
            
            if current_test_case_id:
                # File has test_case_id but no database entry - create database entry
                expected_test_case_id = current_test_case_id
            else:
                # File has no test_case_id - create both
                expected_test_case_id = self.create_database_entry(filename, test_case_data)
                if expected_test_case_id:
                    test_case_data['test_case_id'] = expected_test_case_id
                    needs_update = True
        
        # Ensure other required fields exist
        if 'name' not in test_case_data:
            test_case_data['name'] = filename.replace('.json', '')
            needs_update = True
        
        if 'actions' not in test_case_data:
            test_case_data['actions'] = []
            needs_update = True
        
        # Write file if needed
        if needs_update:
            if self.write_json_file(filepath, test_case_data):
                self.stats['files_updated'] += 1
                logger.info(f"Updated {filename} with test_case_id: {expected_test_case_id}")
            else:
                self.stats['errors'] += 1
                return False
        else:
            if self.verbose:
                logger.info(f"No changes needed for {filename}")
        
        return True
    
    def find_orphaned_database_entries(self, db_entries, json_files):
        """Find database entries without corresponding files"""
        json_filenames = {f['filename'] for f in json_files}
        orphaned = []
        
        for db_entry in db_entries:
            if db_entry['filename'] not in json_filenames:
                orphaned.append(db_entry)
                self.stats['orphaned_db_entries'] += 1
        
        return orphaned
    
    def run(self):
        """Run the standardization process"""
        logger.info("Starting test case file standardization...")
        
        if self.dry_run:
            logger.info("DRY RUN MODE - No changes will be made")
        
        # Get data from both sources
        db_entries = self.get_all_test_cases_from_db()
        json_files = self.get_all_json_files()
        
        logger.info(f"Found {len(db_entries)} database entries and {len(json_files)} JSON files")
        
        # Create lookup for database entries by filename
        db_lookup = {entry['filename']: entry for entry in db_entries}
        
        # Process each JSON file
        for file_info in json_files:
            filename = file_info['filename']
            db_entry = db_lookup.get(filename)
            self.standardize_file(file_info, db_entry)
        
        # Find orphaned database entries
        orphaned_db_entries = self.find_orphaned_database_entries(db_entries, json_files)
        if orphaned_db_entries:
            logger.warning(f"Found {len(orphaned_db_entries)} orphaned database entries:")
            for entry in orphaned_db_entries:
                logger.warning(f"  - {entry['test_case_id']}: {entry['filename']} (status: {entry['status']})")
        
        # Print summary
        self.print_summary()
        
        return self.stats['errors'] == 0
    
    def print_summary(self):
        """Print standardization summary"""
        logger.info("\n" + "="*60)
        logger.info("STANDARDIZATION SUMMARY")
        logger.info("="*60)
        logger.info(f"Files processed: {self.stats['files_processed']}")
        logger.info(f"Files updated: {self.stats['files_updated']}")
        logger.info(f"Database entries created: {self.stats['database_entries_created']}")
        logger.info(f"Database entries updated: {self.stats['database_entries_updated']}")
        logger.info(f"Orphaned files: {self.stats['orphaned_files']}")
        logger.info(f"Orphaned database entries: {self.stats['orphaned_db_entries']}")
        logger.info(f"Errors: {self.stats['errors']}")
        
        if self.stats['errors'] == 0:
            logger.info("\n✅ Standardization completed successfully!")
        else:
            logger.error(f"\n❌ Standardization completed with {self.stats['errors']} errors")

def main():
    # DB-only enforcement: disable JSON scanning unless explicitly allowed
    if os.getenv('ALLOW_JSON_SCANNING', '').lower() != 'true':
        logger.info('Skipping: JSON scanning is disabled by default (DB-only mode). Set ALLOW_JSON_SCANNING=true to enable.')
        sys.exit(0)

    parser = argparse.ArgumentParser(description='Standardize test case files with database')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    parser.add_argument('--verbose', action='store_true', help='Show detailed output')

    args = parser.parse_args()

    standardizer = TestCaseStandardizer(dry_run=args.dry_run, verbose=args.verbose)
    success = standardizer.run()

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
