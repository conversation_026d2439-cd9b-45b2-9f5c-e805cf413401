# SaaS Platform Dependencies
# Core Flask and Web Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3
Flask-Limiter==3.5.0
Flask-Mail==0.9.1
Flask-SocketIO==5.3.6

# Database
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21
redis==5.0.1

# Authentication and Security
PyJWT==2.8.0
bcrypt==4.0.1
cryptography==41.0.4
passlib==1.7.4

# API and Validation
marshmallow==3.20.1
WTForms==3.0.1
requests==2.31.0

# File Storage and Cloud Services
boto3==1.34.0
Pillow==10.0.1

# Monitoring and Logging
prometheus-client==0.17.1
structlog==23.1.0
sentry-sdk==1.32.0

# Task Queue and Background Jobs
celery==5.3.6
kombu==5.3.4

# WebSocket and Real-time Communication
python-socketio==5.8.0
websockets==11.0.3

# Development and Testing
gunicorn==21.2.0
gevent==23.7.0
pytest==7.4.2
pytest-flask==1.2.0
factory-boy==3.3.0

# Utilities
python-dotenv==1.0.0
click==8.1.7
PyYAML==6.0.1
tqdm==4.66.1

# Mobile Testing Dependencies (from original requirements.txt)
numpy==1.24.3
opencv-python==4.8.1.78
Appium-Python-Client==3.1.0
pure-python-adb==0.3.0.dev0
facebook-wda==1.4.6
selenium==4.15.0

# OCR and Image Processing
easyocr==1.7.0
pytesseract==0.3.10

# Additional utilities
requests-toolbelt==1.0.0
urllib3==1.26.18
certifi==2023.7.22