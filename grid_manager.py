#!/usr/bin/env python3
"""
Appium Grid Manager

A comprehensive manager for Appium Grid operations, device management,
and session handling. This module provides enhanced functionality for
managing mobile device connections through Selenium Grid.
"""

import os
import time
import json
import logging
import requests
import subprocess
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from grid_config import GridConfig

logger = logging.getLogger(__name__)

class DevicePlatform(Enum):
    """Supported device platforms"""
    IOS = "iOS"
    ANDROID = "Android"

class NodeStatus(Enum):
    """Node status enumeration"""
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    UNKNOWN = "unknown"

@dataclass
class DeviceInfo:
    """Device information structure"""
    platform: DevicePlatform
    device_name: str
    platform_version: str
    automation_name: str
    node_url: str
    status: NodeStatus
    max_sessions: int
    active_sessions: int

@dataclass
class SessionInfo:
    """Session information structure"""
    session_id: str
    platform: DevicePlatform
    device_name: str
    node_id: str
    start_time: float
    capabilities: Dict[str, Any]

class GridManager:
    """Enhanced Appium Grid Manager"""
    
    def __init__(self, timeout: int = 30, retry_attempts: int = 3):
        self.timeout = timeout
        self.retry_attempts = retry_attempts
        self.active_sessions: Dict[str, SessionInfo] = {}
        
    def start_grid(self) -> Tuple[bool, str]:
        """Start the Appium Grid services"""
        try:
            grid_dir = os.path.join(os.getcwd(), 'grid')
            start_script = os.path.join(grid_dir, 'start-grid.sh')
            
            if not os.path.exists(start_script):
                return False, "Grid start script not found"
            
            logger.info("Starting Appium Grid...")
            result = subprocess.run(
                ['bash', start_script],
                cwd=grid_dir,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                # Wait for grid to be fully ready
                if self.wait_for_grid_ready():
                    return True, "Grid started successfully"
                else:
                    return False, "Grid started but not ready within timeout"
            else:
                return False, f"Grid start failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, "Grid start timed out"
        except Exception as e:
            return False, f"Error starting grid: {str(e)}"
    
    def stop_grid(self) -> Tuple[bool, str]:
        """Stop the Appium Grid services"""
        try:
            grid_dir = os.path.join(os.getcwd(), 'grid')
            stop_script = os.path.join(grid_dir, 'stop-grid.sh')
            
            if not os.path.exists(stop_script):
                return False, "Grid stop script not found"
            
            logger.info("Stopping Appium Grid...")
            result = subprocess.run(
                ['bash', stop_script],
                cwd=grid_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                return True, "Grid stopped successfully"
            else:
                return False, f"Grid stop failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, "Grid stop timed out"
        except Exception as e:
            return False, f"Error stopping grid: {str(e)}"
    
    def wait_for_grid_ready(self, max_wait: int = 60) -> bool:
        """Wait for the Grid to be fully ready"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                # Check hub status
                if not self.is_hub_ready():
                    time.sleep(2)
                    continue
                
                # Check if nodes are registered
                nodes = self.get_registered_nodes()
                if len(nodes) >= 2:  # Expecting iOS and Android nodes
                    logger.info(f"Grid ready with {len(nodes)} nodes")
                    return True
                
                time.sleep(2)
                
            except Exception as e:
                logger.debug(f"Waiting for grid: {e}")
                time.sleep(2)
        
        return False
    
    def is_hub_ready(self) -> bool:
        """Check if the Grid Hub is ready"""
        try:
            response = requests.get(GridConfig.GRID_STATUS_URL, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_registered_nodes(self) -> List[Dict]:
        """Get list of registered nodes"""
        try:
            response = requests.get(GridConfig.GRID_STATUS_URL, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data.get('value', {}).get('nodes', [])
        except Exception as e:
            logger.error(f"Error getting registered nodes: {e}")
        return []
    
    def get_device_info(self) -> List[DeviceInfo]:
        """Get information about available devices"""
        devices = []
        nodes = self.get_registered_nodes()
        
        for node in nodes:
            slots = node.get('slots', [])
            for slot in slots:
                stereotype = slot.get('stereotype', {})
                platform_name = stereotype.get('platformName', '')
                
                if platform_name in ['iOS', 'Android']:
                    platform = DevicePlatform.IOS if platform_name == 'iOS' else DevicePlatform.ANDROID
                    
                    device = DeviceInfo(
                        platform=platform,
                        device_name=stereotype.get('appium:deviceName', 'Unknown'),
                        platform_version=stereotype.get('appium:platformVersion', 'Unknown'),
                        automation_name=stereotype.get('appium:automationName', 'Unknown'),
                        node_url=node.get('uri', ''),
                        status=NodeStatus.ONLINE if slot.get('session') is None else NodeStatus.BUSY,
                        max_sessions=slot.get('maxSessions', 1),
                        active_sessions=1 if slot.get('session') is not None else 0
                    )
                    devices.append(device)
        
        return devices
    

    
    def verify_device_connections(self) -> Dict[str, Any]:
        """Verify device connections and provide detailed status"""
        verification_result = {
            'grid_available': False,
            'ios_devices': [],
            'android_devices': [],
            'total_devices': 0,
            'issues': [],
            'recommendations': []
        }
        
        try:
            # Check Grid availability
            verification_result['grid_available'] = self.is_hub_ready()
            
            if verification_result['grid_available']:
                devices = self.get_device_info()
                
                for device in devices:
                    device_info = {
                        'device_name': device.device_name,
                        'platform_version': device.platform_version,
                        'status': device.status.value,
                        'available': device.status == NodeStatus.ONLINE,
                        'active_sessions': device.active_sessions,
                        'max_sessions': device.max_sessions
                    }
                    
                    if device.platform == DevicePlatform.IOS:
                        verification_result['ios_devices'].append(device_info)
                    elif device.platform == DevicePlatform.ANDROID:
                        verification_result['android_devices'].append(device_info)
                
                verification_result['total_devices'] = len(devices)
                
                # Check for issues
                if not verification_result['ios_devices']:
                    verification_result['issues'].append('No iOS devices registered with Grid')
                    verification_result['recommendations'].append('Connect iOS device or start iOS simulator')
                
                if not verification_result['android_devices']:
                    verification_result['issues'].append('No Android devices registered with Grid')
                    verification_result['recommendations'].append('Connect Android device or start Android emulator')
                
                # Check device availability
                unavailable_devices = [d for d in devices if d.status != NodeStatus.ONLINE]
                if unavailable_devices:
                    verification_result['issues'].append(f'{len(unavailable_devices)} device(s) are busy or offline')
            else:
                verification_result['issues'].append('Grid Hub is not available')
                verification_result['recommendations'].append('Start the Appium Grid using start-grid.sh')
                
        except Exception as e:
            verification_result['issues'].append(f'Verification failed: {str(e)}')
            logger.error(f"Device verification error: {e}")
        
        return verification_result
    
    def get_connection_url_with_retry(self, platform: str) -> str:
        """Get connection URL with retry logic and fallback"""
        for attempt in range(self.retry_attempts):
            try:
                if GridConfig.is_grid_enabled() and self.is_hub_ready():
                    # Check if appropriate nodes are available
                    devices = self.get_device_info()
                    platform_devices = [
                        d for d in devices 
                        if d.platform.value == platform and d.status == NodeStatus.ONLINE
                    ]
                    
                    if platform_devices:
                        logger.info(f"Using Grid connection for {platform} (attempt {attempt + 1})")
                        return GridConfig.GRID_HUB_URL
                    else:
                        logger.warning(f"No available {platform} nodes in Grid")
                
            except Exception as e:
                logger.warning(f"Grid check failed (attempt {attempt + 1}): {e}")
            
            if attempt < self.retry_attempts - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
        
        # Fallback to direct connection
        logger.info(f"Falling back to direct connection for {platform}")
        return GridConfig.get_connection_url(platform)
    
    def create_session(self, platform: str, capabilities: Dict) -> Optional[str]:
        """Create a new session with enhanced error handling"""
        connection_url = self.get_connection_url_with_retry(platform)
        
        try:
            # Add Grid-specific capabilities if using Grid
            if connection_url == GridConfig.GRID_HUB_URL:
                capabilities['platformName'] = platform
                if platform == 'iOS':
                    capabilities.setdefault('appium:automationName', 'XCUITest')
                else:
                    capabilities.setdefault('appium:automationName', 'UiAutomator2')
            
            # Session creation would be handled by the WebDriver
            # This is a placeholder for session tracking
            session_id = f"session_{int(time.time())}_{platform}"
            
            session_info = SessionInfo(
                session_id=session_id,
                platform=DevicePlatform.IOS if platform == 'iOS' else DevicePlatform.ANDROID,
                device_name=capabilities.get('appium:deviceName', 'Unknown'),
                node_id='unknown',
                start_time=time.time(),
                capabilities=capabilities
            )
            
            self.active_sessions[session_id] = session_info
            logger.info(f"Session created: {session_id} for {platform}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create session for {platform}: {e}")
            return None
    
    def end_session(self, session_id: str) -> bool:
        """End a session and clean up"""
        if session_id in self.active_sessions:
            session_info = self.active_sessions.pop(session_id)
            logger.info(f"Session ended: {session_id} for {session_info.platform.value}")
            return True
        return False
    
    def get_active_sessions(self) -> List[SessionInfo]:
        """Get list of active sessions"""
        return list(self.active_sessions.values())
    
    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health_status = {
            'timestamp': time.time(),
            'grid_enabled': GridConfig.is_grid_enabled(),
            'hub_status': 'unknown',
            'nodes': [],
            'active_sessions': len(self.active_sessions),
            'issues': []
        }
        
        try:
            # Check hub status
            if self.is_hub_ready():
                health_status['hub_status'] = 'online'
                
                # Check nodes
                devices = self.get_device_info()
                health_status['nodes'] = [
                    {
                        'platform': device.platform.value,
                        'device_name': device.device_name,
                        'status': device.status.value,
                        'active_sessions': device.active_sessions
                    }
                    for device in devices
                ]
                
                # Check for issues
                ios_nodes = [d for d in devices if d.platform == DevicePlatform.IOS]
                android_nodes = [d for d in devices if d.platform == DevicePlatform.ANDROID]
                
                if not ios_nodes:
                    health_status['issues'].append('No iOS nodes available')
                if not android_nodes:
                    health_status['issues'].append('No Android nodes available')
                    
            else:
                health_status['hub_status'] = 'offline'
                health_status['issues'].append('Grid Hub is not responding')
                
        except Exception as e:
            health_status['hub_status'] = 'error'
            health_status['issues'].append(f'Health check error: {str(e)}')
        
        return health_status
    
    def restart_grid(self) -> Tuple[bool, str]:
        """Restart the entire Grid"""
        logger.info("Restarting Appium Grid...")
        
        # Stop grid
        stop_success, stop_message = self.stop_grid()
        if not stop_success:
            return False, f"Failed to stop grid: {stop_message}"
        
        # Wait a moment
        time.sleep(5)
        
        # Start grid
        start_success, start_message = self.start_grid()
        if not start_success:
            return False, f"Failed to start grid: {start_message}"
        
        return True, "Grid restarted successfully"

# Global instance
grid_manager = GridManager()

def get_grid_manager() -> GridManager:
    """Get the global Grid Manager instance"""
    return grid_manager

if __name__ == "__main__":
    import sys
    
    manager = GridManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "start":
            success, message = manager.start_grid()
            print(f"Start: {success} - {message}")
        elif command == "stop":
            success, message = manager.stop_grid()
            print(f"Stop: {success} - {message}")
        elif command == "restart":
            success, message = manager.restart_grid()
            print(f"Restart: {success} - {message}")
        elif command == "health":
            health = manager.health_check()
            print(json.dumps(health, indent=2))
        elif command == "devices":
            devices = manager.get_device_info()
            device_data = [
                {
                    'platform': d.platform.value,
                    'device_name': d.device_name,
                    'platform_version': d.platform_version,
                    'status': d.status.value,
                    'active_sessions': d.active_sessions
                }
                for d in devices
            ]
            print(json.dumps(device_data, indent=2))
        elif command == "sessions":
            sessions = manager.get_active_sessions()
            session_data = [
                {
                    'session_id': s.session_id,
                    'platform': s.platform.value,
                    'device_name': s.device_name,
                    'start_time': s.start_time
                }
                for s in sessions
            ]
            print(json.dumps(session_data, indent=2))
        else:
            print("Usage: python grid_manager.py [start|stop|restart|health|devices|sessions]")
    else:
        health = manager.health_check()
        print(json.dumps(health, indent=2))