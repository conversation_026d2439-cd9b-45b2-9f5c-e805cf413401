<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Auto-Test Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
        }
        
        .platform-selection {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 800px;
            width: 100%;
        }
        
        .auth-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .platform-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
            height: 100%;
        }
        
        .platform-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
        }
        
        .platform-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .ios-card .platform-icon {
            color: #007AFF;
        }
        
        .android-card .platform-icon {
            color: #3DDC84;
        }
        
        .platform-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .platform-description {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }
        
        .btn-platform {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            border: 2px solid;
            transition: all 0.3s ease;
        }
        
        .btn-ios {
            color: #007AFF;
            border-color: #007AFF;
            background: transparent;
        }
        
        .btn-ios:hover {
            background: #007AFF;
            color: white;
        }
        
        .btn-android {
            color: #3DDC84;
            border-color: #3DDC84;
            background: transparent;
        }
        
        .btn-android:hover {
            background: #3DDC84;
            color: white;
        }
        
        .user-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: white;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .hidden {
            display: none !important;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Authentication Section -->
    <div id="authSection" class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1 class="mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Mobile Auto-Test
                </h1>
                <p class="mb-0 mt-2 opacity-75">Secure Authentication Portal</p>
            </div>
            <div class="auth-body">
                <div id="alertContainer"></div>
                
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                <i class="fas fa-eye" id="passwordToggle"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <span class="loading-spinner">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                        </span>
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </span>
                    </button>
                </form>
                
                <div class="text-center">
                    <small class="text-muted">
                        Demo Credentials:<br>
                        <strong>Email:</strong> <EMAIL><br>
                        <strong>Password:</strong> password123
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Platform Selection Section -->
    <div id="platformSection" class="auth-container hidden">
        <div class="platform-selection">
            <div class="auth-header">
                <div class="user-info">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-user-circle me-2"></i>
                            Welcome, <span id="userName">User</span>
                        </div>
                        <button class="btn btn-sm btn-outline-light" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </button>
                    </div>
                </div>
                <h1 class="mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Choose Testing Platform
                </h1>
                <p class="mb-0 mt-2 opacity-75">Select your mobile automation platform</p>
            </div>
            
            <div class="auth-body">
                <div class="row g-4">
                    <!-- iOS Platform -->
                    <div class="col-md-6">
                        <div class="platform-card ios-card" onclick="selectPlatform('ios')">
                            <div class="platform-icon">
                                <i class="fab fa-apple"></i>
                            </div>
                            <h3 class="platform-title">iOS Automation</h3>
                            <p class="platform-description">
                                Test iOS applications with advanced automation capabilities including device simulation and real device testing.
                            </p>
                            <button class="btn btn-ios" type="button">
                                <i class="fas fa-play me-2"></i>Launch iOS Testing
                            </button>
                        </div>
                    </div>
                    
                    <!-- Android Platform -->
                    <div class="col-md-6">
                        <div class="platform-card android-card" onclick="selectPlatform('android')">
                            <div class="platform-icon">
                                <i class="fab fa-android"></i>
                            </div>
                            <h3 class="platform-title">Android Automation</h3>
                            <p class="platform-description">
                                Test Android applications with comprehensive automation tools for emulators and physical devices.
                            </p>
                            <button class="btn btn-android" type="button">
                                <i class="fas fa-play me-2"></i>Launch Android Testing
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Both platforms support real devices and simulators/emulators
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 id="loadingText">Initializing platform...</h5>
                    <p class="text-muted mb-0" id="loadingSubtext">Please wait while we set up your testing environment</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Authentication state
        let isAuthenticated = false;
        let currentUser = null;
        
        // Demo users for authentication
        const demoUsers = [
            { email: '<EMAIL>', password: 'password123', name: 'Test User' },
            { email: '<EMAIL>', password: 'admin123', name: 'Admin User' },
            { email: '<EMAIL>', password: 'demo123', name: 'Demo User' }
        ];
        
        // Check for existing session on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedUser = localStorage.getItem('currentUser');
            if (savedUser) {
                currentUser = JSON.parse(savedUser);
                isAuthenticated = true;
                showPlatformSelection();
            }
        });
        
        // Login form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Show loading state
            setLoadingState(true);
            
            // Simulate authentication delay
            setTimeout(() => {
                const user = demoUsers.find(u => u.email === email && u.password === password);
                
                if (user) {
                    // Successful authentication
                    currentUser = user;
                    isAuthenticated = true;
                    
                    if (rememberMe) {
                        localStorage.setItem('currentUser', JSON.stringify(user));
                    }
                    
                    showAlert('Login successful! Redirecting...', 'success');
                    
                    setTimeout(() => {
                        showPlatformSelection();
                    }, 1000);
                } else {
                    // Failed authentication
                    showAlert('Invalid email or password. Please try again.', 'danger');
                }
                
                setLoadingState(false);
            }, 1500);
        });
        
        function setLoadingState(loading) {
            const submitBtn = document.querySelector('#loginForm button[type="submit"]');
            const spinner = submitBtn.querySelector('.loading-spinner');
            const btnText = submitBtn.querySelector('.btn-text');
            
            if (loading) {
                spinner.style.display = 'inline';
                btnText.style.display = 'none';
                submitBtn.disabled = true;
            } else {
                spinner.style.display = 'none';
                btnText.style.display = 'inline';
                submitBtn.disabled = false;
            }
        }
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alert);
            
            // Auto-dismiss success alerts
            if (type === 'success') {
                setTimeout(() => {
                    alert.remove();
                }, 3000);
            }
        }
        
        function showPlatformSelection() {
            document.getElementById('authSection').classList.add('hidden');
            document.getElementById('platformSection').classList.remove('hidden');
            document.getElementById('platformSection').classList.add('fade-in');
            
            // Update user name
            document.getElementById('userName').textContent = currentUser.name;
        }
        
        function selectPlatform(platform) {
            if (!isAuthenticated) {
                showAlert('Please log in first.', 'warning');
                return;
            }
            
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');
            const loadingSubtext = document.getElementById('loadingSubtext');
            
            if (platform === 'ios') {
                loadingText.textContent = 'Launching iOS Testing Platform...';
                loadingSubtext.textContent = 'Initializing iOS automation environment';
                loadingModal.show();
                
                // Execute run.py for iOS
                fetch('/api/launch/ios', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ user: currentUser })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to iOS app
                        window.location.href = data.redirect_url || '/app';
                    } else {
                        loadingModal.hide();
                        showAlert('Failed to launch iOS platform: ' + (data.error || 'Unknown error'), 'danger');
                    }
                })
                .catch(error => {
                    loadingModal.hide();
                    showAlert('Error launching iOS platform: ' + error.message, 'danger');
                });
                
            } else if (platform === 'android') {
                loadingText.textContent = 'Launching Android Testing Platform...';
                loadingSubtext.textContent = 'Initializing Android automation environment';
                loadingModal.show();
                
                // Execute run_android.py for Android
                fetch('/api/launch/android', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ user: currentUser })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to Android app
                        window.location.href = data.redirect_url || '/app_android';
                    } else {
                        loadingModal.hide();
                        showAlert('Failed to launch Android platform: ' + (data.error || 'Unknown error'), 'danger');
                    }
                })
                .catch(error => {
                    loadingModal.hide();
                    showAlert('Error launching Android platform: ' + error.message, 'danger');
                });
            }
        }
        
        function logout() {
            localStorage.removeItem('currentUser');
            currentUser = null;
            isAuthenticated = false;
            
            document.getElementById('platformSection').classList.add('hidden');
            document.getElementById('authSection').classList.remove('hidden');
            document.getElementById('authSection').classList.add('fade-in');
            
            // Clear form
            document.getElementById('loginForm').reset();
            document.getElementById('alertContainer').innerHTML = '';
            
            showAlert('You have been logged out successfully.', 'info');
        }
        
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordToggle = document.getElementById('passwordToggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordToggle.className = 'fas fa-eye';
            }
        }
        
        // Security: Clear sensitive data on page unload
        window.addEventListener('beforeunload', function() {
            if (!document.getElementById('rememberMe').checked) {
                localStorage.removeItem('currentUser');
            }
        });
        
        // Auto-logout after inactivity (30 minutes)
        let inactivityTimer;
        function resetInactivityTimer() {
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(() => {
                if (isAuthenticated) {
                    logout();
                    showAlert('Session expired due to inactivity.', 'warning');
                }
            }, 30 * 60 * 1000); // 30 minutes
        }
        
        // Reset timer on user activity
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetInactivityTimer, true);
        });
        
        // Initialize inactivity timer
        resetInactivityTimer();
    </script>
</body>
</html>