#!/usr/bin/env python3
"""
Main entry point for the Android Mobile App Automation Tool using consolidated database.
This version uses the consolidated database instead of separate database files.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the Android application."""
    try:
        # Import consolidated configuration for Android
        import app_android.config_consolidated as config
        
        # Set environment variables for the application
        os.environ['FLASK_PORT'] = str(config.PORT)
        os.environ['APPIUM_PORT'] = str(config.APPIUM_PORT)
        os.environ['WDA_PORT'] = str(config.WDA_PORT)
        os.environ['PLATFORM'] = config.PLATFORM
        
        # Set instance DB suffix for port-specific databases (if still needed)
        os.environ['INSTANCE_DB_SUFFIX'] = f'_port_{config.PORT}'
        
        logger.info("Starting Android Mobile App Automation Tool with consolidated database...")
        logger.info(f"Configuration:")
        logger.info(f"  - Platform: {config.PLATFORM}")
        logger.info(f"  - Flask server port: {config.PORT}")
        logger.info(f"  - Appium server port: {config.APPIUM_PORT}")
        logger.info(f"  - WebDriverAgent port: {config.WDA_PORT}")
        logger.info(f"  - Using consolidated database")
        
        # Import and start the Android app
        from app_android import app
        
        logger.info(f"Open your web browser and navigate to: http://localhost:{config.PORT}")
        
        # Start the Flask application
        app.run(
            host='0.0.0.0',
            port=config.PORT,
            debug=True,
            use_reloader=False  # Disable reloader to avoid issues with threading
        )
        
    except Exception as e:
        logger.error(f"Failed to start Android application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()