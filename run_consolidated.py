#!/usr/bin/env python3
"""
Main entry point for the Mobile App Automation Tool using consolidated database.
This version uses the consolidated database instead of separate database files.
"""

import os
import sys
import logging
from pathlib import Path

# Set environment variables to use consolidated configuration
os.environ['USE_CONSOLIDATED_CONFIG'] = 'true'
os.environ['USE_CONSOLIDATED_DB'] = 'true'

# Add the project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Replace config module in sys.modules before any imports
import config_consolidated
sys.modules['config'] = config_consolidated

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the application."""
    try:
        # Import consolidated configuration
        import config_consolidated as config
        
        # Replace the config module in sys.modules so all imports use consolidated config
        sys.modules['config'] = config
        
        # Set environment variables for the application
        os.environ['FLASK_PORT'] = str(config.PORT)
        os.environ['APPIUM_PORT'] = str(config.APPIUM_PORT)
        os.environ['WDA_PORT'] = str(config.WDA_PORT)
        os.environ['PLATFORM'] = config.PLATFORM
        
        # Set instance DB suffix for port-specific databases (if still needed)
        os.environ['INSTANCE_DB_SUFFIX'] = f'_port_{config.PORT}'
        
        logger.info("Starting Mobile App Automation Tool with consolidated database...")
        logger.info(f"Configuration:")
        logger.info(f"  - Platform: {config.PLATFORM}")
        logger.info(f"  - Flask server port: {config.PORT}")
        logger.info(f"  - Appium server port: {config.APPIUM_PORT}")
        logger.info(f"  - WebDriverAgent port: {config.WDA_PORT}")
        logger.info(f"  - Using consolidated database")
        
        # Import and start the appropriate app based on platform
        if config.PLATFORM == 'ios':
            print("About to import app...")
            from app.app import app
            print("App imported successfully")
            app_name = 'iOS App'
        else:
            from app_android import app
            app_name = 'Android App'
        
        logger.info(f"Open your web browser and navigate to: http://localhost:{config.PORT}")
        
        # Start the Flask application
        print(f"Starting Flask app on 0.0.0.0:{config.PORT}")
        try:
            app.run(
                host='0.0.0.0',
                port=config.PORT,
                debug=False
            )
        except Exception as e:
            print(f"Error starting Flask app: {e}")
            import traceback
            traceback.print_exc()
            raise
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()