# How to See Database Records During Test Execution

## Current Situation

You ran a test suite and saw this in output.txt:
```
[2025-10-06 00:00:23,871] INFO in optimized_screenshot_manager: OptimizedScreenshotManager initialized
[2025-10-06 00:00:23,985] INFO in player: 🔵 DATABASE-FIRST: Player Wrapper invoked
```

But **NO database records were created** in the new tables.

## Why?

The player.py is still using the **OLD file-based tracking system**. The new database-only utilities are created and tested, but **NOT YET INTEGRATED** into the actual test execution flow.

---

## Quick Test: Verify Database-Only System Works

Run this command to prove the database-only system works:

```bash
cd /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android
python3 test_execution_with_database.py
```

**Expected Output:**
```
✅ Execution started: exec_20251006_XXXXXX
✅ Step 0 tracked: ID=XX
✅ Step 1 tracked: ID=XX
...
✅ Execution completed successfully
✅ Database records verified
```

**Verify Database Records:**
```bash
sqlite3 /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db

# Check execution_reports
SELECT test_execution_id, suite_id, platform, status, start_time 
FROM execution_reports 
WHERE test_execution_id LIKE 'exec_20251006%' 
ORDER BY created_at DESC 
LIMIT 5;

# Check execution_tracking
SELECT id, test_execution_id, test_case_id, step_idx, action_type, status 
FROM execution_tracking 
WHERE test_execution_id LIKE 'exec_20251006%' 
ORDER BY step_idx;
```

This proves the database-only system **WORKS PERFECTLY** when called directly.

---

## To See Database Records During Real Test Execution

You need to integrate the ExecutionTrackerWrapper into player.py. Here's the minimal change needed:

### Step 1: Find player.py Initialization

**File:** `app_android/utils/player.py`

**Find this section** (around line 84):
```python
def __init__(self, device_controller, socketio=None, test_cases_dir=None, test_idx=None):
    self.device_controller = device_controller
    self.socketio = socketio
    self.test_cases_dir = test_cases_dir
    self.current_test_idx = test_idx
    self.suite_id = None
    # ... more initialization ...
```

**Add this AFTER the existing initialization:**
```python
    # DATABASE-ONLY: Initialize execution tracker wrapper
    try:
        from .execution_tracker_wrapper import ExecutionTrackerWrapper
        self.execution_wrapper = ExecutionTrackerWrapper()
        self.current_execution_id = None
        self.logger.info("✅ DATABASE-ONLY: ExecutionTrackerWrapper initialized")
    except Exception as e:
        self.logger.warning(f"⚠️  DATABASE-ONLY: Failed to initialize wrapper: {e}")
        self.execution_wrapper = None
```

### Step 2: Find Test Suite Start

**Search for:** Where test suite execution starts (likely a method like `run_test_suite` or `execute_test_suite`)

**Add this at the START of test suite execution:**
```python
# DATABASE-ONLY: Start execution tracking
if hasattr(self, 'execution_wrapper') and self.execution_wrapper:
    self.current_execution_id = self.execution_wrapper.start_execution(
        suite_id=suite_id,
        suite_name=suite_name,
        platform='Android'
    )
    self.logger.info(f"✅ DATABASE-ONLY: Started tracking: {self.current_execution_id}")
```

### Step 3: Find Step Execution

**Search for:** Where individual test steps are executed and tracked

**Add this AFTER each step execution:**
```python
# DATABASE-ONLY: Track step
if hasattr(self, 'execution_wrapper') and self.execution_wrapper and self.current_execution_id:
    self.execution_wrapper.track_step(
        execution_id=self.current_execution_id,
        test_case_id=test_case_id,
        step_idx=step_idx,
        action_type=action_type,
        status='passed' if success else 'failed',
        screenshot_filename=screenshot_filename,
        action_id=action_id,
        suite_id=suite_id,
        filename=test_case_filename,
        error_message=error_message if not success else None
    )
```

### Step 4: Find Test Suite Completion

**Search for:** Where test suite execution completes

**Add this at the END of test suite execution:**
```python
# DATABASE-ONLY: Complete execution tracking
if hasattr(self, 'execution_wrapper') and self.execution_wrapper and self.current_execution_id:
    report_data = self.execution_wrapper.complete_execution(
        execution_id=self.current_execution_id,
        status='completed'
    )
    self.logger.info(f"✅ DATABASE-ONLY: Completed tracking: {self.current_execution_id}")
```

---

## Alternative: Use the Test Script

If you don't want to modify player.py yet, you can use the test script to see database records:

```bash
# Run the test script
cd /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android
python3 test_execution_with_database.py

# Check the database
sqlite3 /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db

# Query execution_reports
SELECT * FROM execution_reports ORDER BY created_at DESC LIMIT 5;

# Query execution_tracking
SELECT * FROM execution_tracking ORDER BY id DESC LIMIT 10;

# Exit sqlite
.quit
```

---

## Use the API Endpoints

The API endpoints are already registered and working. You can test them:

```bash
# Start the Flask app (if not already running)
cd /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android
python3 app.py

# In another terminal, test the endpoints:

# List all executions
curl http://localhost:8081/api/executions

# Get specific execution data
curl http://localhost:8081/api/executions/exec_20251006_000722

# View HTML report
curl http://localhost:8081/api/executions/exec_20251006_000722/report > report.html
open report.html

# Delete execution
curl -X DELETE http://localhost:8081/api/executions/exec_20251006_000722
```

---

## Summary

### What's Working Now:
- ✅ Database-only utilities (DatabaseExecutionTracker, ScreenshotManagerDB, ReportGeneratorDB)
- ✅ Database migrations applied
- ✅ API endpoints registered
- ✅ Test scripts working
- ✅ Database records created when using test scripts

### What's NOT Working Yet:
- ❌ Player.py integration (not calling the new utilities)
- ❌ Real test execution doesn't create database records
- ❌ Frontend doesn't have "View Report" buttons

### To See Database Records During Real Test Execution:
1. **Option A:** Integrate ExecutionTrackerWrapper into player.py (4 small changes)
2. **Option B:** Use the test script to verify database-only system works
3. **Option C:** Use API endpoints to view existing test data

### Recommended Next Step:
Run the test script to verify everything works:
```bash
cd /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android
python3 test_execution_with_database.py
```

Then check the database:
```bash
sqlite3 /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db "SELECT * FROM execution_reports ORDER BY created_at DESC LIMIT 5;"
```

You should see records! 🎉

---

## Need Help?

If you want me to:
1. **Make the player.py changes** - I can do this now
2. **Create a detailed integration guide** - I can provide step-by-step instructions
3. **Test with real execution** - I can help verify it works

Just let me know!

