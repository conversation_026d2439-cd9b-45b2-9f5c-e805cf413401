# End User Guide - Mobile App Automation

## Welcome to Mobile App Automation

This guide will help you get started with the Mobile App Automation platform, a powerful tool for automating mobile application testing on both iOS and Android devices.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Installation](#installation)
3. [First-Time Setup](#first-time-setup)
4. [Getting Started](#getting-started)
5. [Using the Web Interface](#using-the-web-interface)
6. [Creating Test Cases](#creating-test-cases)
7. [Running Tests](#running-tests)
8. [Viewing Results](#viewing-results)
9. [Troubleshooting](#troubleshooting)
10. [Support](#support)

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Network**: Internet connection for device communication
- **USB**: Available USB ports for device connections

### Mobile Device Requirements
- **iOS**: iOS 12.0 or later, with Developer Mode enabled
- **Android**: Android 7.0 (API level 24) or later, with USB Debugging enabled

## Installation

### Download and Install

1. **Download** the application from the provided link
2. **Verify** the download using the provided SHA256 checksum:
   ```bash
   # Windows (PowerShell)
   Get-FileHash SecureMobileAppAutomation.exe -Algorithm SHA256
   
   # macOS/Linux
   sha256sum SecureMobileAppAutomation
   ```
3. **Install** the application:
   - **Windows**: Run `SecureMobileAppAutomation.exe` as Administrator
   - **macOS**: Open `SecureMobileAppAutomation.app` (may require right-click → Open first time)
   - **Linux**: Make executable and run: `chmod +x SecureMobileAppAutomation && ./SecureMobileAppAutomation`

### First Launch
The application will automatically:
- Create necessary directories
- Set up the local database
- Start the web server on `http://localhost:5000`
- Open your default browser to the interface

## First-Time Setup

### 1. Device Preparation

#### iOS Devices
1. **Enable Developer Mode**:
   - Go to Settings → Privacy & Security → Developer Mode
   - Toggle on Developer Mode
   - Restart your device when prompted

2. **Trust Computer**:
   - Connect your iOS device via USB
   - Tap "Trust" when prompted on the device
   - Enter your device passcode

3. **Install WebDriverAgent** (automatic):
   - The application will guide you through WDA installation
   - Follow the on-screen instructions

#### Android Devices
1. **Enable Developer Options**:
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings → Developer Options

2. **Enable USB Debugging**:
   - In Developer Options, enable "USB Debugging"
   - Connect device via USB
   - Allow USB debugging when prompted

3. **Verify ADB Connection**:
   - The application will automatically detect your device
   - Ensure "File Transfer" mode is selected on your device

### 2. Application Configuration

1. **Launch the Application**
2. **Access Web Interface**: Open `http://localhost:5000` in your browser
3. **Complete Setup Wizard**:
   - Select your primary platform (iOS/Android)
   - Configure device settings
   - Test device connection

## Getting Started

### Accessing the Interface

1. **Start the Application**:
   - Double-click the application icon
   - Wait for the "Server started" message
   - The web interface will open automatically

2. **Manual Access**:
   - Open your web browser
   - Navigate to `http://localhost:5000`
   - You should see the Mobile App Automation dashboard

### Dashboard Overview

The main dashboard provides:
- **Device Status**: Connected devices and their status
- **Recent Tests**: Your latest test executions
- **Quick Actions**: Common tasks and shortcuts
- **System Status**: Application health and performance

## Using the Web Interface

### Navigation Menu

- **Dashboard**: Overview and quick access
- **Test Cases**: Create and manage test cases
- **Test Suites**: Group and organize tests
- **Devices**: Manage connected devices
- **Reports**: View test results and analytics
- **Settings**: Configure application preferences

### Device Management

1. **Connect Device**:
   - Ensure device is connected via USB
   - Click "Refresh Devices" if not detected
   - Verify device appears in the device list

2. **Device Actions**:
   - **Install App**: Upload and install APK/IPA files
   - **Launch App**: Start applications on the device
   - **Take Screenshot**: Capture current screen
   - **View Logs**: Access device logs

## Creating Test Cases

### Basic Test Case Creation

1. **Navigate to Test Cases** → **Create New**
2. **Fill in Test Details**:
   - **Name**: Descriptive test name
   - **Description**: What the test does
   - **Platform**: iOS or Android
   - **App**: Target application

3. **Add Test Steps**:
   - Click "Add Step"
   - Choose action type (tap, swipe, input, etc.)
   - Configure step parameters
   - Use element inspector to select UI elements

### Action Types

- **Tap**: Touch a specific element
- **Input**: Enter text into fields
- **Swipe**: Gesture actions (up, down, left, right)
- **Wait**: Pause execution
- **Assert**: Verify expected conditions
- **Screenshot**: Capture screen at specific points

### Element Selection

1. **Use Element Inspector**:
   - Click "Inspect Element" button
   - Tap the element on your device screen
   - The element details will be captured automatically

2. **Manual Element Entry**:
   - Enter XPath, ID, or other locators manually
   - Use the element validation tool to verify

### Test Case Best Practices

- **Use descriptive names** for test cases and steps
- **Add assertions** to verify expected behavior
- **Include screenshots** at key verification points
- **Keep tests focused** on specific functionality
- **Use wait conditions** for dynamic content

## Running Tests

### Single Test Execution

1. **Select Test Case** from the test cases list
2. **Choose Target Device** from connected devices
3. **Click "Run Test"**
4. **Monitor Progress** in real-time
5. **View Results** when complete

### Test Suite Execution

1. **Create Test Suite**:
   - Go to Test Suites → Create New
   - Add multiple test cases
   - Configure execution order

2. **Run Test Suite**:
   - Select the test suite
   - Choose execution mode (sequential/parallel)
   - Start execution

### Execution Options

- **Sequential**: Run tests one after another
- **Parallel**: Run multiple tests simultaneously (multiple devices)
- **Retry on Failure**: Automatically retry failed tests
- **Continue on Error**: Don't stop suite on individual test failures

## Viewing Results

### Test Reports

1. **Access Reports** section
2. **Select Report Type**:
   - Individual test results
   - Test suite summaries
   - Historical trends
   - Performance metrics

### Report Details

Each report includes:
- **Execution Summary**: Pass/fail status, duration
- **Step-by-Step Results**: Detailed action outcomes
- **Screenshots**: Visual evidence of test execution
- **Logs**: Detailed execution logs
- **Performance Data**: Response times, resource usage

### Exporting Results

- **PDF Reports**: Comprehensive test documentation
- **Excel/CSV**: Data for analysis
- **JSON**: Raw data for integration
- **Screenshots**: Individual images

## Troubleshooting

### Common Issues

#### Device Not Detected

**Symptoms**: Device doesn't appear in device list

**Solutions**:
1. Check USB connection and cable
2. Ensure USB debugging is enabled (Android)
3. Trust the computer (iOS)
4. Restart the application
5. Try a different USB port

#### Test Execution Fails

**Symptoms**: Tests fail to start or complete

**Solutions**:
1. Verify app is installed on device
2. Check element locators are correct
3. Ensure device is unlocked
4. Verify network connectivity
5. Check application logs

#### Performance Issues

**Symptoms**: Slow test execution or interface

**Solutions**:
1. Close unnecessary applications
2. Restart the automation application
3. Check available system resources
4. Reduce parallel test execution
5. Clear application cache

### Error Messages

#### "Port already in use"
- **Cause**: Another instance is running
- **Solution**: Close other instances or restart computer

#### "Device unauthorized"
- **Cause**: USB debugging not authorized
- **Solution**: Disconnect/reconnect device and authorize

#### "App not found"
- **Cause**: Target app not installed
- **Solution**: Install app on device first

### Getting Help

1. **Check Logs**:
   - Application logs in the Logs section
   - Device logs for mobile-specific issues

2. **Restart Application**:
   - Close and restart the automation application
   - Refresh the web browser

3. **Reset Configuration**:
   - Go to Settings → Reset to Defaults
   - Reconfigure device connections

## Advanced Features

### Custom Scripts

For advanced users, the platform supports:
- **Custom Python scripts** for complex automation
- **API integration** for external tool connectivity
- **Database queries** for data-driven testing

### Continuous Integration

The application can be integrated with CI/CD pipelines:
- **Command-line interface** for automated execution
- **REST API** for external control
- **Report generation** for build systems

## Command-Line Quick Start

For local development and secure builds you may run the web experience directly.

rm -rf .appium-python
PATH=$HOME/.nvm/versions/node/v20.19.5/bin:$PATH \
APPIUM_HOME=$PWD/.appium-python \
appium driver install --source=local \
    $PWD/node_modules/appium-uiautomator2-driver \
    $PWD/node_modules/appium-xcuitest-driver

1. **Activate the Python environment**
   ```bash
   source venv/bin/activate
   ```
2. **Expose the Node runtime and Appium home**
   ```bash
   export NODE_BIN_DIR=$HOME/.nvm/versions/node/v20.19.5/bin
   export PATH=$NODE_BIN_DIR:$PATH
   export APPIUM_HOME=$PWD/.appium-python
   ```
3. **Provision (or repair) Appium drivers** – first time only
   ```bash
   scripts/setup_appium_environment.sh
   ```
4. **Launch the dashboards**
   ```bash
   # Android
   python run_android.py

   # iOS
   python run.py
   ```

   The Appium Inspector plugin is installed and enabled by default. To override
   the plugin list, export `APPIUM_USE_PLUGINS="plugin-a,plugin-b"` before
   running the launchers.

Additional details are available in `docs/appium_setup.md`.

## Security and Privacy

### Data Protection
- All data is stored locally on your machine
- No data is transmitted to external servers
- Test results and screenshots remain private

### Network Security
- Application binds only to localhost (127.0.0.1)
- No external network access required for core functionality
- Secure communication with connected devices

## Support

### Self-Help Resources
1. **Built-in Help**: Click the "?" icon in the interface
2. **Video Tutorials**: Access from the Help menu
3. **Sample Test Cases**: Pre-built examples to learn from

### Documentation
- **API Documentation**: For integration developers
- **Advanced Configuration**: For power users
- **Troubleshooting Guide**: Detailed problem resolution

### Getting Support
For additional assistance:
1. Check the troubleshooting section first
2. Review application logs for error details
3. Ensure you're using the latest version
4. Contact support with specific error messages and steps to reproduce

## Tips for Success

1. **Start Simple**: Begin with basic test cases before complex scenarios
2. **Use Real Devices**: Physical devices provide more accurate results than simulators
3. **Regular Updates**: Keep your mobile apps and OS updated
4. **Backup Tests**: Export test cases regularly
5. **Monitor Performance**: Watch for trends in test execution times
6. **Collaborate**: Share test cases and results with your team

## Conclusion

The Mobile App Automation platform provides a powerful, user-friendly solution for mobile application testing. By following this guide, you'll be able to create comprehensive test suites, execute them reliably, and generate detailed reports to improve your mobile application quality.

Remember to start with simple test cases and gradually build more complex automation scenarios as you become familiar with the platform's capabilities.
