#!/bin/bash

# Script to restart Android app with proper port cleanup
# This addresses the UiAutomator2 port conflict issue

echo "=== Restarting Android App with Port Conflict Resolution ==="

# Kill existing Appium processes
echo "Killing existing Appium processes..."
pkill -f "appium" || true
sleep 2

# Kill any existing Android automation processes
echo "Cleaning up Android automation processes..."
adb shell "pkill -f uiautomator" 2>/dev/null || true
adb shell "pkill -f appium" 2>/dev/null || true
sleep 1

# Clear any stuck Android ports
echo "Clearing Android system ports..."
adb forward --remove-all 2>/dev/null || true
adb reverse --remove-all 2>/dev/null || true

# Kill any Python processes running the Android app
echo "Stopping existing Android app..."
pkill -f "run_android.py" || true
sleep 2

# Start fresh Android app with new port configuration
echo "Starting Android app with updated ports..."
cd "$(dirname "$0")"

# Use specific ports to avoid conflicts
python run_android.py --port 8081 --appium-port 4724 --wda-port 8300 &
ANDROID_PID=$!

echo "Android app started with PID: $ANDROID_PID"
echo "Access at: http://localhost:8081"
echo "Appium server on port: 4724"
echo "UiAutomator2 system port: 8201 (was 8200)"
echo "MJPEG server port: 7811 (was 7810)"
echo ""
echo "Press Ctrl+C to stop the Android app"

# Wait for the process
trap 'kill $ANDROID_PID 2>/dev/null; exit' INT TERM
wait