#!/usr/bin/env python3
"""
App Package Preparation Script

This script prepares the mobile app automation projects for secure distribution
by creating encrypted packages that can be downloaded by the secure distribution client.
"""

import os
import sys
import json
import zipfile
import hashlib
import logging
from pathlib import Path
from datetime import datetime
import shutil
import tempfile
from cryptography.fernet import Fernet
import base64
import uuid

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AppPackageManager:
    """Manages preparation and encryption of app packages for distribution"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.packages_dir = self.project_root / 'distribution_packages'
        self.packages_dir.mkdir(exist_ok=True)
        
        # Directories to include in packages
        self.ios_app_dirs = ['app', 'static', 'templates', 'utils', 'actions', 'routes']
        self.android_app_dirs = ['app_android', 'static', 'templates', 'utils', 'actions', 'routes']
        
        # Files to include in packages
        self.common_files = [
            'requirements.txt',
            'package.json',
            'package-lock.json',
            'config.py',
            'run.py',
            'run_android.py'
        ]
    
    def create_ios_package(self) -> dict:
        """Create iOS app package"""
        try:
            logger.info("Creating iOS app package...")
            
            package_info = {
                'id': str(uuid.uuid4()),
                'name': 'iOS Mobile App Automation',
                'description': 'Complete iOS mobile app testing and automation platform with Appium integration, element detection, and test recording capabilities.',
                'platform': 'ios',
                'version': '1.0.0',
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Create temporary directory for package
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                package_dir = temp_path / 'ios_app_package'
                package_dir.mkdir()
                
                # Copy iOS app files
                self._copy_app_files(package_dir, 'ios')
                
                # Create package zip
                zip_path = self._create_zip_package(package_dir, 'ios_app')
                
                # Encrypt package
                encrypted_path, encryption_key = self._encrypt_package(zip_path)
                
                # Move to packages directory
                final_path = self.packages_dir / f"ios_app_{package_info['id']}.encrypted"
                shutil.move(encrypted_path, final_path)
                
                # Calculate file hash
                file_hash = self._calculate_file_hash(final_path)
                
                package_info.update({
                    'file_path': str(final_path),
                    'file_hash': file_hash,
                    'file_size': final_path.stat().st_size,
                    'encryption_key': encryption_key
                })
            
            logger.info(f"iOS package created: {package_info['id']}")
            return package_info
            
        except Exception as e:
            logger.error(f"Failed to create iOS package: {e}")
            raise
    
    def create_android_package(self) -> dict:
        """Create Android app package"""
        try:
            logger.info("Creating Android app package...")
            
            package_info = {
                'id': str(uuid.uuid4()),
                'name': 'Android Mobile App Automation',
                'description': 'Complete Android mobile app testing and automation platform with Appium integration, element detection, and test recording capabilities.',
                'platform': 'android',
                'version': '1.0.0',
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Create temporary directory for package
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                package_dir = temp_path / 'android_app_package'
                package_dir.mkdir()
                
                # Copy Android app files
                self._copy_app_files(package_dir, 'android')
                
                # Create package zip
                zip_path = self._create_zip_package(package_dir, 'android_app')
                
                # Encrypt package
                encrypted_path, encryption_key = self._encrypt_package(zip_path)
                
                # Move to packages directory
                final_path = self.packages_dir / f"android_app_{package_info['id']}.encrypted"
                shutil.move(encrypted_path, final_path)
                
                # Calculate file hash
                file_hash = self._calculate_file_hash(final_path)
                
                package_info.update({
                    'file_path': str(final_path),
                    'file_hash': file_hash,
                    'file_size': final_path.stat().st_size,
                    'encryption_key': encryption_key
                })
            
            logger.info(f"Android package created: {package_info['id']}")
            return package_info
            
        except Exception as e:
            logger.error(f"Failed to create Android package: {e}")
            raise
    
    def create_unified_package(self) -> dict:
        """Create unified package with both iOS and Android apps"""
        try:
            logger.info("Creating unified app package...")
            
            package_info = {
                'id': str(uuid.uuid4()),
                'name': 'Mobile App Automation Suite',
                'description': 'Complete mobile app testing and automation platform supporting both iOS and Android with Appium integration, element detection, test recording, and comprehensive reporting.',
                'platform': 'both',
                'version': '1.0.0',
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Create temporary directory for package
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                package_dir = temp_path / 'unified_app_package'
                package_dir.mkdir()
                
                # Copy both iOS and Android app files
                self._copy_app_files(package_dir, 'both')
                
                # Create package zip
                zip_path = self._create_zip_package(package_dir, 'unified_app')
                
                # Encrypt package
                encrypted_path, encryption_key = self._encrypt_package(zip_path)
                
                # Move to packages directory
                final_path = self.packages_dir / f"unified_app_{package_info['id']}.encrypted"
                shutil.move(encrypted_path, final_path)
                
                # Calculate file hash
                file_hash = self._calculate_file_hash(final_path)
                
                package_info.update({
                    'file_path': str(final_path),
                    'file_hash': file_hash,
                    'file_size': final_path.stat().st_size,
                    'encryption_key': encryption_key
                })
            
            logger.info(f"Unified package created: {package_info['id']}")
            return package_info
            
        except Exception as e:
            logger.error(f"Failed to create unified package: {e}")
            raise
    
    def _copy_app_files(self, package_dir: Path, platform: str):
        """Copy app files to package directory"""
        try:
            if platform == 'ios':
                # Copy iOS-specific files
                for dir_name in self.ios_app_dirs:
                    src_dir = self.project_root / dir_name
                    if src_dir.exists():
                        dst_dir = package_dir / dir_name
                        shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                
                # Copy iOS-specific run script
                ios_run_script = self.project_root / 'run.py'
                if ios_run_script.exists():
                    shutil.copy2(ios_run_script, package_dir / 'run.py')
            
            elif platform == 'android':
                # Copy Android-specific files
                for dir_name in self.android_app_dirs:
                    src_dir = self.project_root / dir_name
                    if src_dir.exists():
                        dst_dir = package_dir / dir_name
                        shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                
                # Copy Android-specific run script
                android_run_script = self.project_root / 'run_android.py'
                if android_run_script.exists():
                    shutil.copy2(android_run_script, package_dir / 'run.py')
            
            elif platform == 'both':
                # Copy all app directories
                all_dirs = set(self.ios_app_dirs + self.android_app_dirs)
                for dir_name in all_dirs:
                    src_dir = self.project_root / dir_name
                    if src_dir.exists():
                        dst_dir = package_dir / dir_name
                        shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                
                # Copy both run scripts
                for script_name in ['run.py', 'run_android.py']:
                    script_path = self.project_root / script_name
                    if script_path.exists():
                        shutil.copy2(script_path, package_dir / script_name)
            
            # Copy common files
            for file_name in self.common_files:
                src_file = self.project_root / file_name
                if src_file.exists():
                    shutil.copy2(src_file, package_dir / file_name)
            
            # Copy additional directories
            additional_dirs = ['utils', 'grid', 'scripts', 'docs']
            for dir_name in additional_dirs:
                src_dir = self.project_root / dir_name
                if src_dir.exists():
                    dst_dir = package_dir / dir_name
                    shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            
            # Create README for the package
            readme_content = f"""# Mobile App Automation - {platform.upper()}

This package contains the mobile app automation platform for {platform} testing.

## Installation

1. Install Python 3.8 or higher
2. Install Node.js and npm
3. Run: pip install -r requirements.txt
4. Run: npm install
5. Start the application: python run.py

## Features

- Mobile app testing and automation
- Element detection and interaction
- Test recording and playback
- Comprehensive reporting
- Multi-device support

## Support

For support and documentation, please contact your system administrator.
"""
            
            readme_file = package_dir / 'README.md'
            with open(readme_file, 'w') as f:
                f.write(readme_content)
            
            logger.info(f"App files copied for platform: {platform}")
            
        except Exception as e:
            logger.error(f"Failed to copy app files: {e}")
            raise
    
    def _create_zip_package(self, package_dir: Path, package_name: str) -> Path:
        """Create zip package from directory"""
        try:
            zip_path = package_dir.parent / f"{package_name}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for root, dirs, files in os.walk(package_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_name = file_path.relative_to(package_dir)
                        zip_file.write(file_path, arc_name)
            
            logger.info(f"Zip package created: {zip_path}")
            return zip_path
            
        except Exception as e:
            logger.error(f"Failed to create zip package: {e}")
            raise
    
    def _encrypt_package(self, zip_path: Path) -> tuple:
        """Encrypt the package and return encrypted path and key info"""
        try:
            # Generate encryption key
            key = Fernet.generate_key()
            fernet = Fernet(key)
            
            # Read and encrypt the zip file
            with open(zip_path, 'rb') as f:
                zip_data = f.read()
            
            encrypted_data = fernet.encrypt(zip_data)
            
            # Write encrypted file
            encrypted_path = zip_path.with_suffix('.encrypted')
            with open(encrypted_path, 'wb') as f:
                f.write(encrypted_data)
            
            # Prepare key information
            key_info = {
                'key_data': base64.b64encode(key).decode('utf-8'),
                'salt': base64.b64encode(os.urandom(32)).decode('utf-8')
            }
            
            logger.info(f"Package encrypted: {encrypted_path}")
            return encrypted_path, key_info
            
        except Exception as e:
            logger.error(f"Failed to encrypt package: {e}")
            raise
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of a file"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            
            return sha256_hash.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to calculate file hash: {e}")
            raise
    
    def save_package_manifest(self, packages: list):
        """Save package manifest for server configuration"""
        try:
            manifest = {
                'created_at': datetime.utcnow().isoformat(),
                'packages': packages
            }
            
            manifest_file = self.packages_dir / 'package_manifest.json'
            with open(manifest_file, 'w') as f:
                json.dump(manifest, f, indent=2)
            
            logger.info(f"Package manifest saved: {manifest_file}")
            
        except Exception as e:
            logger.error(f"Failed to save package manifest: {e}")
            raise

def main():
    """Main entry point"""
    try:
        logger.info("Starting app package preparation...")
        
        manager = AppPackageManager()
        packages = []
        
        # Create iOS package
        ios_package = manager.create_ios_package()
        packages.append(ios_package)
        
        # Create Android package
        android_package = manager.create_android_package()
        packages.append(android_package)
        
        # Create unified package
        unified_package = manager.create_unified_package()
        packages.append(unified_package)
        
        # Save package manifest
        manager.save_package_manifest(packages)
        
        logger.info("App package preparation completed successfully!")
        logger.info(f"Packages created: {len(packages)}")
        logger.info(f"Output directory: {manager.packages_dir}")
        
        print("\\n✅ Package preparation completed!")
        print(f"📁 Output directory: {manager.packages_dir}")
        print(f"📦 Packages created: {len(packages)}")
        print("\\n📋 Next steps:")
        print("1. Upload packages to your server")
        print("2. Configure the SaaS server with package information")
        print("3. Set up user permissions for app access")
        
    except Exception as e:
        logger.error(f"Package preparation failed: {e}")
        print(f"\\n❌ Package preparation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
