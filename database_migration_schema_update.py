#!/usr/bin/env python3
"""
Database Schema Migration Script for Database-First Architecture

This script updates the database schema to support:
1. Screenshot BLOB storage in screenshots table
2. Execution ID linking in screenshots table
3. Indexes for performance optimization

Run this script BEFORE deploying the new architecture.

Usage:
    python3 database_migration_schema_update.py
"""

import sqlite3
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database paths
ANDROID_DB = 'db-data/android.db'
IOS_DB = 'app/data/ios_automation.db'

def migrate_database(db_path, platform_name):
    """
    Migrate database schema to support database-first architecture
    
    Args:
        db_path (str): Path to the database file
        platform_name (str): Platform name for logging (Android/iOS)
    """
    if not os.path.exists(db_path):
        logger.warning(f"{platform_name} database not found at {db_path}, skipping...")
        return False
    
    logger.info(f"=" * 80)
    logger.info(f"MIGRATING {platform_name} DATABASE: {db_path}")
    logger.info(f"=" * 80)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # ============================================================
        # 1. Update screenshots table
        # ============================================================
        logger.info("\n1. Updating screenshots table schema...")
        
        # Check existing columns
        cursor.execute("PRAGMA table_info(screenshots)")
        existing_columns = {row[1] for row in cursor.fetchall()}
        logger.info(f"   Existing columns: {existing_columns}")
        
        # Add test_execution_id if missing
        if 'test_execution_id' not in existing_columns:
            logger.info("   Adding test_execution_id column...")
            cursor.execute('ALTER TABLE screenshots ADD COLUMN test_execution_id TEXT')
            logger.info("   ✅ Added test_execution_id column")
        else:
            logger.info("   ✅ test_execution_id column already exists")
        
        # Add test_case_id if missing
        if 'test_case_id' not in existing_columns:
            logger.info("   Adding test_case_id column...")
            cursor.execute('ALTER TABLE screenshots ADD COLUMN test_case_id TEXT')
            logger.info("   ✅ Added test_case_id column")
        else:
            logger.info("   ✅ test_case_id column already exists")
        
        # Add screenshot_blob if missing
        if 'screenshot_blob' not in existing_columns:
            logger.info("   Adding screenshot_blob column...")
            cursor.execute('ALTER TABLE screenshots ADD COLUMN screenshot_blob BLOB')
            logger.info("   ✅ Added screenshot_blob column")
        else:
            logger.info("   ✅ screenshot_blob column already exists")
        
        # Add screenshot_mime if missing
        if 'screenshot_mime' not in existing_columns:
            logger.info("   Adding screenshot_mime column...")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")
            logger.info("   ✅ Added screenshot_mime column")
        else:
            logger.info("   ✅ screenshot_mime column already exists")
        
        # ============================================================
        # 2. Create indexes for performance
        # ============================================================
        logger.info("\n2. Creating performance indexes...")
        
        indexes_to_create = [
            ('idx_execution_tracking_execution_id', 'execution_tracking', 'test_execution_id'),
            ('idx_execution_tracking_suite_id', 'execution_tracking', 'suite_id'),
            ('idx_execution_tracking_test_case_id', 'execution_tracking', 'test_case_id'),
            ('idx_screenshots_execution_id', 'screenshots', 'test_execution_id'),
            ('idx_screenshots_action_id', 'screenshots', 'action_id'),
            ('idx_execution_reports_execution_id', 'execution_reports', 'test_execution_id'),
            ('idx_execution_reports_suite_id', 'execution_reports', 'suite_id'),
        ]
        
        for index_name, table_name, column_name in indexes_to_create:
            try:
                cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name})')
                logger.info(f"   ✅ Created index: {index_name}")
            except Exception as e:
                logger.warning(f"   ⚠️  Could not create index {index_name}: {e}")
        
        # ============================================================
        # 3. Verify execution_reports table has report_data column
        # ============================================================
        logger.info("\n3. Verifying execution_reports table...")
        
        cursor.execute("PRAGMA table_info(execution_reports)")
        report_columns = {row[1] for row in cursor.fetchall()}
        
        if 'report_data' in report_columns:
            logger.info("   ✅ report_data column exists in execution_reports")
        else:
            logger.info("   Adding report_data column to execution_reports...")
            cursor.execute('ALTER TABLE execution_reports ADD COLUMN report_data TEXT')
            logger.info("   ✅ Added report_data column")
        
        # ============================================================
        # 4. Add timestamp column to execution_tracking if missing
        # ============================================================
        logger.info("\n4. Verifying execution_tracking table...")
        
        cursor.execute("PRAGMA table_info(execution_tracking)")
        tracking_columns = {row[1] for row in cursor.fetchall()}
        
        if 'timestamp' not in tracking_columns:
            logger.info("   Adding timestamp column...")
            cursor.execute('ALTER TABLE execution_tracking ADD COLUMN timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            logger.info("   ✅ Added timestamp column")
        else:
            logger.info("   ✅ timestamp column already exists")
        
        # ============================================================
        # 5. Commit changes
        # ============================================================
        conn.commit()
        logger.info("\n✅ Database migration completed successfully!")
        
        # ============================================================
        # 6. Display final schema
        # ============================================================
        logger.info("\n" + "=" * 80)
        logger.info("FINAL SCHEMA VERIFICATION")
        logger.info("=" * 80)
        
        cursor.execute("PRAGMA table_info(screenshots)")
        logger.info("\nscreenshots table columns:")
        for row in cursor.fetchall():
            logger.info(f"   {row[1]} ({row[2]})")
        
        cursor.execute("PRAGMA table_info(execution_tracking)")
        logger.info("\nexecution_tracking table columns:")
        for row in cursor.fetchall():
            logger.info(f"   {row[1]} ({row[2]})")
        
        cursor.execute("PRAGMA table_info(execution_reports)")
        logger.info("\nexecution_reports table columns:")
        for row in cursor.fetchall():
            logger.info(f"   {row[1]} ({row[2]})")
        
        # Display indexes
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%' ORDER BY name")
        logger.info("\nIndexes created:")
        for row in cursor.fetchall():
            logger.info(f"   {row[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error migrating {platform_name} database: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """Main migration function"""
    logger.info("=" * 80)
    logger.info("DATABASE SCHEMA MIGRATION FOR DATABASE-FIRST ARCHITECTURE")
    logger.info("=" * 80)
    logger.info("\nThis script will update the database schema to support:")
    logger.info("  1. Screenshot BLOB storage")
    logger.info("  2. Execution ID linking")
    logger.info("  3. Performance indexes")
    logger.info("  4. Timestamp tracking")
    logger.info("\n" + "=" * 80)
    
    # Migrate Android database
    android_success = migrate_database(ANDROID_DB, "Android")
    
    # Migrate iOS database
    ios_success = migrate_database(IOS_DB, "iOS")
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("MIGRATION SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Android database: {'✅ SUCCESS' if android_success else '❌ FAILED'}")
    logger.info(f"iOS database:     {'✅ SUCCESS' if ios_success else '❌ FAILED'}")
    logger.info("=" * 80)
    
    if android_success or ios_success:
        logger.info("\n✅ Migration completed! You can now deploy the database-first architecture.")
    else:
        logger.error("\n❌ Migration failed! Please review errors above.")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())

