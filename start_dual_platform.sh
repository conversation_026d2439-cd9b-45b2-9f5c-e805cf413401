#!/bin/bash

# Dual Platform Startup Script
# Starts both iOS and Android backends simultaneously

set -e

# Default configuration
IOS_PORT=8080
ANDROID_PORT=8081
IOS_APPIUM_PORT=4723
ANDROID_APPIUM_PORT=4724
IOS_WDA_PORT=8100

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ios-port)
            IOS_PORT="$2"
            shift 2
            ;;
        --android-port)
            ANDROID_PORT="$2"
            shift 2
            ;;
        --ios-appium-port)
            IOS_APPIUM_PORT="$2"
            shift 2
            ;;
        --android-appium-port)
            ANDROID_APPIUM_PORT="$2"
            shift 2
            ;;
        --ios-wda-port)
            IOS_WDA_PORT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --ios-port PORT          iOS Flask server port (default: 8080)"
            echo "  --android-port PORT      Android Flask server port (default: 8081)"
            echo "  --ios-appium-port PORT   iOS Appium server port (default: 4723)"
            echo "  --android-appium-port PORT Android Appium server port (default: 4724)"
            echo "  --ios-wda-port PORT      iOS WebDriverAgent port (default: 8100)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Starting Dual Platform Mobile Automation Tool..."
echo "iOS Backend: http://localhost:$IOS_PORT (Appium: $IOS_APPIUM_PORT, WDA: $IOS_WDA_PORT)"
echo "Android Backend: http://localhost:$ANDROID_PORT (Appium: $ANDROID_APPIUM_PORT)"
echo ""

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Error: Virtual environment 'venv' not found."
    echo "Please run setup.sh first."
    exit 1
fi

# Function to start iOS backend
start_ios_backend() {
    echo "Starting iOS backend..."
    if command -v osascript &> /dev/null; then
        # macOS
        osascript -e "tell application \"Terminal\" to do script \"cd '$PWD' && source venv/bin/activate && echo 'Starting iOS Backend...' && python run.py --port $IOS_PORT --appium-port $IOS_APPIUM_PORT --wda-port $IOS_WDA_PORT\""
    elif command -v gnome-terminal &> /dev/null; then
        # Linux with GNOME
        gnome-terminal --title="iOS Backend" -- bash -c "cd '$PWD' && source venv/bin/activate && echo 'Starting iOS Backend...' && python run.py --port $IOS_PORT --appium-port $IOS_APPIUM_PORT --wda-port $IOS_WDA_PORT; exec bash"
    else
        echo "Please run manually in a new terminal:"
        echo "cd '$PWD' && source venv/bin/activate && python run.py --port $IOS_PORT --appium-port $IOS_APPIUM_PORT --wda-port $IOS_WDA_PORT"
    fi
}

# Function to start Android backend
start_android_backend() {
    echo "Starting Android backend..."
    if command -v osascript &> /dev/null; then
        # macOS
        osascript -e "tell application \"Terminal\" to do script \"cd '$PWD' && source venv/bin/activate && echo 'Starting Android Backend...' && python run_android.py --port $ANDROID_PORT --appium-port $ANDROID_APPIUM_PORT\""
    elif command -v gnome-terminal &> /dev/null; then
        # Linux with GNOME
        gnome-terminal --title="Android Backend" -- bash -c "cd '$PWD' && source venv/bin/activate && echo 'Starting Android Backend...' && python run_android.py --port $ANDROID_PORT --appium-port $ANDROID_APPIUM_PORT; exec bash"
    else
        echo "Please run manually in a new terminal:"
        echo "cd '$PWD' && source venv/bin/activate && python run_android.py --port $ANDROID_PORT --appium-port $ANDROID_APPIUM_PORT"
    fi
}

# Start both backends
start_ios_backend
sleep 2
start_android_backend

echo ""
echo "Both backends are starting..."
echo "Access points:"
echo "  iOS Backend: http://localhost:$IOS_PORT"
echo "  Android Backend: http://localhost:$ANDROID_PORT"
echo "  Unified Interface: http://localhost:$IOS_PORT (automatically routes to appropriate backend)"
echo ""
echo "To stop both backends, close the terminal windows or press Ctrl+C in each."