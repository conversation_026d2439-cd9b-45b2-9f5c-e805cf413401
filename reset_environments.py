#!/usr/bin/env python3
"""
Script to reset environment variables for both iOS and Android apps.
Deletes all existing environments and creates new AU and NZ environments
from the provided JSON files.
"""

import json
import sys
import os
import logging
from pathlib import Path
from typing import Dict, Iterable, Optional, Tuple

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

WORKSPACE_ROOT = Path(os.environ.get('AUTOMATION_WORKSPACE', Path.home() / 'MobileAutomationWorkspace'))
ENVIRONMENT_TEMPLATE_FILENAMES = {
    'AU': 'environment_au.json',
    'NZ': 'environment_nz.json',
}


def _iter_environment_template_dirs() -> Iterable[Path]:
    """Yield candidate directories that may contain environment templates."""
    env_override = os.environ.get('AUTOMATION_ENV_TEMPLATE_DIR')
    if env_override:
        override_path = Path(env_override).expanduser()
        yield override_path

    script_dir = Path(__file__).resolve().parent
    yield script_dir / 'data' / 'environment_templates'
    yield script_dir / 'data' / 'environments'
    yield WORKSPACE_ROOT / 'shared' / 'environments'
    yield WORKSPACE_ROOT / 'shared' / 'environment_templates'
    yield WORKSPACE_ROOT / 'ios' / 'environments'
    yield WORKSPACE_ROOT / 'android' / 'environments'
    yield Path.cwd() / 'data' / 'environment_templates'

def load_json_environment(file_path: Path) -> Tuple[Optional[str], list]:
    """Load environment variables from JSON file"""
    try:
        file_path = Path(file_path)
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Handle different JSON structures
        if 'data' in data and 'variables' in data['data']:
            # Structure from environment_undefined_2025-08-13.json
            variables = data['data']['variables']
            name = data['data'].get('name', 'Unknown')
        elif 'variables' in data:
            # Structure from environment_NZ-PROD-IP14_2025-08-08.json
            variables = data['variables']
            name = data.get('name', 'Unknown')
        else:
            logger.error(f"Unknown JSON structure in {file_path}")
            return None, []
        
        logger.info(f"Loaded {len(variables)} variables from {file_path} (original name: {name})")
        return name, variables
    except Exception as e:
        logger.error(f"Error loading JSON file {file_path}: {e}")
        return None, []


def load_environment_template(env_key: str) -> Tuple[Optional[str], list, Optional[Path]]:
    """Resolve and load an environment template for the requested key."""
    filename = ENVIRONMENT_TEMPLATE_FILENAMES.get(env_key.upper())
    if not filename:
        logger.error(f"No template filename configured for environment '{env_key}'")
        return None, [], None

    searched: Dict[Path, bool] = {}
    for directory in _iter_environment_template_dirs():
        if not directory:
            continue
        if directory in searched:
            continue
        searched[directory] = True
        if not directory.exists():
            continue
        candidate = directory / filename
        if candidate.exists():
            logger.info(f"Using {env_key} environment template: {candidate}")
            name, variables = load_json_environment(candidate)
            return name or env_key, variables, candidate

    logger.warning(
        "No environment template found for %s. Searched directories: %s",
        env_key,
        ", ".join(str(path) for path in searched),
    )
    return None, [], None

def reset_ios_environments():
    """Reset iOS environments and variables"""
    try:
        # Import iOS database handler
        sys.path.append('app/utils')
        from directory_paths_db import directory_paths_db as ios_db

        logger.info("Resetting iOS environments...")

        # Get all existing environments
        existing_envs = ios_db.get_all_environments()
        logger.info(f"Found {len(existing_envs)} existing iOS environments")

        # Delete all existing environments (this will cascade delete variables)
        for env in existing_envs:
            logger.info(f"Deleting iOS environment: {env['name']} (ID: {env['id']})")
            ios_db.delete_environment(env['id'])

        au_env_id = None
        loaded_templates: Dict[str, Optional[Path]] = {}

        # Load AU environment data
        au_name, au_variables, au_source = load_environment_template('AU')
        if au_variables:
            loaded_templates['AU'] = au_source
            # Create AU environment
            au_env_id = ios_db.create_environment('AU')
            if au_env_id:
                logger.info(f"Created iOS AU environment with ID: {au_env_id}")

                # Add variables to AU environment
                for var in au_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = ios_db.add_environment_variable(
                        au_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added iOS AU variable: {name} = {current_value}")
                
                logger.info(f"Added {len(au_variables)} variables to iOS AU environment")
        
        # Load NZ environment data
        nz_env_id = None
        nz_name, nz_variables, nz_source = load_environment_template('NZ')
        if nz_variables:
            loaded_templates['NZ'] = nz_source
            # Create NZ environment
            nz_env_id = ios_db.create_environment('NZ')
            if nz_env_id:
                logger.info(f"Created iOS NZ environment with ID: {nz_env_id}")
                
                # Add variables to NZ environment
                for var in nz_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = ios_db.add_environment_variable(
                        nz_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added iOS NZ variable: {name} = {current_value}")
                
                logger.info(f"Added {len(nz_variables)} variables to iOS NZ environment")
        
        # Set AU as the active environment for iOS
        if au_env_id:
            ios_db.set_active_environment(au_env_id)
            logger.info("Set AU as active iOS environment")

        logger.info("✅ iOS environment reset completed successfully")
        return {'success': True, 'templates': loaded_templates}

    except Exception as e:
        logger.error(f"Error resetting iOS environments: {e}")
        return {'success': False, 'templates': {}}

def reset_android_environments():
    """Reset Android environments and variables"""
    try:
        # Import Android database handler
        sys.path.append('app_android/utils')
        from directory_paths_db import directory_paths_db as android_db

        logger.info("Resetting Android environments...")

        # Get all existing environments
        existing_envs = android_db.get_all_environments()
        logger.info(f"Found {len(existing_envs)} existing Android environments")

        # Delete all existing environments (this will cascade delete variables)
        for env in existing_envs:
            logger.info(f"Deleting Android environment: {env['name']} (ID: {env['id']})")
            android_db.delete_environment(env['id'])

        au_env_id = None
        loaded_templates: Dict[str, Optional[Path]] = {}

        # Load AU environment data
        au_name, au_variables, au_source = load_environment_template('AU')
        if au_variables:
            loaded_templates['AU'] = au_source
            # Create AU environment
            au_env_id = android_db.create_environment('AU')
            if au_env_id:
                logger.info(f"Created Android AU environment with ID: {au_env_id}")
                
                # Add variables to AU environment
                for var in au_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = android_db.add_environment_variable(
                        au_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added Android AU variable: {name} = {current_value}")
                
                logger.info(f"Added {len(au_variables)} variables to Android AU environment")
        
        # Load NZ environment data
        nz_env_id = None
        nz_name, nz_variables, nz_source = load_environment_template('NZ')
        if nz_variables:
            loaded_templates['NZ'] = nz_source
            # Create NZ environment
            nz_env_id = android_db.create_environment('NZ')
            if nz_env_id:
                logger.info(f"Created Android NZ environment with ID: {nz_env_id}")
                
                # Add variables to NZ environment
                for var in nz_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = android_db.add_environment_variable(
                        nz_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added Android NZ variable: {name} = {current_value}")
                
                logger.info(f"Added {len(nz_variables)} variables to Android NZ environment")
        
        # Set AU as the active environment for Android
        if au_env_id:
            android_db.set_active_environment(au_env_id)
            logger.info("Set AU as active Android environment")

        logger.info("✅ Android environment reset completed successfully")
        return {'success': True, 'templates': loaded_templates}

    except Exception as e:
        logger.error(f"Error resetting Android environments: {e}")
        return {'success': False, 'templates': {}}

def main():
    """Main function to reset both iOS and Android environments"""
    print("🚀 Starting environment reset for both iOS and Android...")
    logger.info("🚀 Starting environment reset for both iOS and Android...")

    # Reset iOS environments
    print("Resetting iOS environments...")
    ios_result = reset_ios_environments()
    ios_success = ios_result['success']
    print(f"iOS reset result: {ios_success}")

    # Reset Android environments
    print("Resetting Android environments...")
    android_result = reset_android_environments()
    android_success = android_result['success']
    print(f"Android reset result: {android_success}")

    if ios_success and android_success:
        print("🎉 Environment reset completed successfully for both platforms!")
        logger.info("🎉 Environment reset completed successfully for both platforms!")
        logger.info("📋 Summary:")
        logger.info("   - Deleted all existing environments and variables")
        for platform, result in (('iOS', ios_result), ('Android', android_result)):
            for env_key, template_path in result['templates'].items():
                if template_path:
                    logger.info(f"   - {platform} {env_key} environment created from {template_path}")
                else:
                    logger.info(f"   - {platform} {env_key} environment used built-in defaults")
        logger.info("   - Set 'AU' as the active environment for both platforms")
        return True
    else:
        print("❌ Environment reset failed for one or both platforms")
        logger.error("❌ Environment reset failed for one or both platforms")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
