#!/usr/bin/env python3
import sqlite3
import random
import string
import datetime

def generate_unique_id():
    """Generate a unique 6-digit alphanumeric ID"""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

def robust_update():
    result_file = f"robust_update_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    try:
        # Connect to database
        conn = sqlite3.connect('app_android/data/android_test_suites.db')
        cursor = conn.cursor()
        
        # First, check current state
        cursor.execute("SELECT COUNT(*) FROM test_cases")
        total_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM test_cases WHERE LENGTH(test_case_id) = 6 AND test_case_id GLOB '[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]'")
        valid_before = cursor.fetchone()[0]
        
        # Get all test cases that need updating
        cursor.execute("SELECT id, name FROM test_cases WHERE NOT (LENGTH(test_case_id) = 6 AND test_case_id GLOB '[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]')")
        cases_to_update = cursor.fetchall()
        
        updated_count = 0
        used_ids = set()
        
        # Get existing valid IDs to avoid duplicates
        cursor.execute("SELECT test_case_id FROM test_cases WHERE LENGTH(test_case_id) = 6 AND test_case_id GLOB '[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]'")
        existing_ids = cursor.fetchall()
        for (existing_id,) in existing_ids:
            used_ids.add(existing_id)
        
        # Update each case that needs it
        for case_id, name in cases_to_update:
            # Generate unique ID
            new_id = generate_unique_id()
            while new_id in used_ids:
                new_id = generate_unique_id()
            used_ids.add(new_id)
            
            # Update the test case
            cursor.execute("UPDATE test_cases SET test_case_id = ? WHERE id = ?", (new_id, case_id))
            updated_count += 1
        
        # Commit changes
        conn.commit()
        
        # Verify final state
        cursor.execute("SELECT COUNT(*) FROM test_cases")
        total_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM test_cases WHERE LENGTH(test_case_id) = 6 AND test_case_id GLOB '[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]'")
        valid_after = cursor.fetchone()[0]
        
        # Get sample of updated IDs
        cursor.execute("SELECT test_case_id, name FROM test_cases LIMIT 10")
        samples = cursor.fetchall()
        
        conn.close()
        
        # Write comprehensive results
        with open(result_file, 'w') as f:
            f.write(f"ROBUST UPDATE RESULTS - {datetime.datetime.now()}\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("BEFORE UPDATE:\n")
            f.write(f"  Total test cases: {total_before}\n")
            f.write(f"  Valid 6-digit IDs: {valid_before}\n")
            f.write(f"  Cases needing update: {len(cases_to_update)}\n\n")
            
            f.write("UPDATE PROCESS:\n")
            f.write(f"  Cases updated: {updated_count}\n\n")
            
            f.write("AFTER UPDATE:\n")
            f.write(f"  Total test cases: {total_after}\n")
            f.write(f"  Valid 6-digit IDs: {valid_after}\n")
            f.write(f"  Success rate: {(valid_after/total_after)*100:.1f}%\n\n")
            
            if valid_after == total_after:
                f.write("✅ SUCCESS: ALL TEST CASES NOW HAVE VALID 6-DIGIT ALPHANUMERIC IDs!\n\n")
            else:
                f.write(f"❌ ISSUE: {total_after - valid_after} test cases still don't have valid IDs\n\n")
            
            f.write("SAMPLE TEST_CASE_ID VALUES:\n")
            f.write("-" * 40 + "\n")
            for test_id, name in samples:
                f.write(f"{test_id} (len:{len(test_id)}) - {name[:30]}...\n")
        
        print(f"Update completed. Results written to {result_file}")
        return result_file
        
    except Exception as e:
        error_msg = f"Error during robust update: {str(e)}"
        with open(result_file, 'w') as f:
            f.write(f"ERROR: {error_msg}\n")
        print(error_msg)
        return result_file

if __name__ == "__main__":
    result_file = robust_update()
    print(f"Check results in: {result_file}")