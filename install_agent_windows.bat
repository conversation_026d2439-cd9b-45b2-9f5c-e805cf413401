@echo off
REM Mobile Testing SaaS - Windows Local Device Agent Installer
REM This batch file provides a Windows-native installation experience

setlocal enabledelayedexpansion

REM Set colors (if supported)
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "PURPLE=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "NC=%ESC%[0m"

REM Configuration
set "AGENT_VERSION=2.0.0"
set "AGENT_DIR=%LOCALAPPDATA%\MobileTestingAgent"
set "PYTHON_MIN_VERSION=3.8"
set "NODE_MIN_VERSION=14"

REM Print header
echo %BLUE%============================================================%NC%
echo %BLUE%  Mobile Testing SaaS - Windows Agent Installer%NC%
echo %BLUE%  Version: %AGENT_VERSION%%NC%
echo %BLUE%============================================================%NC%
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo %GREEN%[INFO]%NC% Running with administrator privileges
) else (
    echo %YELLOW%[WARNING]%NC% Not running as administrator. Some features may require elevation.
)

REM Check Python installation
echo %GREEN%[STEP]%NC% Checking Python installation...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%[ERROR]%NC% Python is not installed or not in PATH
    echo %YELLOW%[INFO]%NC% Please install Python 3.8+ from https://python.org
    echo %YELLOW%[INFO]%NC% Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%[SUCCESS]%NC% Found Python %PYTHON_VERSION%

REM Check Python version compatibility
python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%[ERROR]%NC% Python 3.8+ is required. Found: %PYTHON_VERSION%
    pause
    exit /b 1
)

REM Check Node.js installation
echo %GREEN%[STEP]%NC% Checking Node.js installation...
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% Node.js not found
    echo %YELLOW%[INFO]%NC% Please install Node.js from https://nodejs.org
    echo %YELLOW%[INFO]%NC% After installation, restart this installer
    pause
    exit /b 1
) else (
    for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
    echo %GREEN%[SUCCESS]%NC% Found Node.js !NODE_VERSION!
)

REM Create agent directory
echo %GREEN%[STEP]%NC% Creating agent directory...
if not exist "%AGENT_DIR%" (
    mkdir "%AGENT_DIR%"
    mkdir "%AGENT_DIR%\config"
    echo %GREEN%[SUCCESS]%NC% Created directory: %AGENT_DIR%
) else (
    echo %BLUE%[INFO]%NC% Agent directory already exists
)

REM Create virtual environment
echo %GREEN%[STEP]%NC% Creating Python virtual environment...
if not exist "%AGENT_DIR%\venv" (
    cd /d "%AGENT_DIR%"
    python -m venv venv
    if %errorLevel% neq 0 (
        echo %RED%[ERROR]%NC% Failed to create virtual environment
        pause
        exit /b 1
    )
    echo %GREEN%[SUCCESS]%NC% Virtual environment created
) else (
    echo %BLUE%[INFO]%NC% Virtual environment already exists
)

REM Activate virtual environment and install dependencies
echo %GREEN%[STEP]%NC% Installing Python dependencies...
cd /d "%AGENT_DIR%"
call venv\Scripts\activate.bat

REM Upgrade pip
python -m pip install --upgrade pip

REM Install dependencies
echo %BLUE%[INFO]%NC% Installing core dependencies...
pip install flask>=2.3.3
pip install flask-socketio>=5.3.6
pip install requests>=2.31.0
pip install websockets>=11.0.3
pip install appium-python-client>=3.1.0
pip install pure-python-adb>=0.3.0.dev0
pip install opencv-python>=********
pip install numpy>=1.24.3
pip install pillow>=10.0.1
pip install pyyaml>=6.0.1
pip install python-dotenv>=1.0.0

if %errorLevel% neq 0 (
    echo %RED%[ERROR]%NC% Failed to install Python dependencies
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Python dependencies installed

REM Install Appium
echo %GREEN%[STEP]%NC% Installing Appium...
appium --version >nul 2>&1
if %errorLevel% neq 0 (
    echo %BLUE%[INFO]%NC% Installing Appium globally...
    npm install -g appium
    npm install -g appium-doctor
    if %errorLevel% neq 0 (
        echo %RED%[ERROR]%NC% Failed to install Appium
        pause
        exit /b 1
    )
) else (
    echo %BLUE%[INFO]%NC% Appium already installed
)

REM Install Appium drivers
echo %BLUE%[INFO]%NC% Installing Appium drivers...
appium driver install uiautomator2
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% Failed to install UiAutomator2 driver
)

echo %GREEN%[SUCCESS]%NC% Appium installation completed

REM Check Android tools
echo %GREEN%[STEP]%NC% Checking Android development tools...
adb version >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% Android Debug Bridge (ADB) not found
    echo %YELLOW%[INFO]%NC% Please install Android SDK Platform Tools from:
    echo %YELLOW%[INFO]%NC% https://developer.android.com/studio/releases/platform-tools
    echo %YELLOW%[INFO]%NC% Add the platform-tools directory to your PATH environment variable
) else (
    echo %GREEN%[SUCCESS]%NC% Android Debug Bridge (ADB) is available
)

REM Create agent configuration
echo %GREEN%[STEP]%NC% Creating agent configuration...
set CONFIG_FILE=%AGENT_DIR%\config\agent.conf
if not exist "%CONFIG_FILE%" (
    (
        echo {
        echo   "agent_id": "",
        echo   "api_key": "",
        echo   "platform_url": "ws://localhost:8080",
        echo   "platform": "windows",
        echo   "capabilities": {
        echo     "android": true,
        echo     "ios": false
        echo   },
        echo   "auto_start": false,
        echo   "log_level": "INFO"
        echo }
    ) > "%CONFIG_FILE%"
    echo %GREEN%[SUCCESS]%NC% Configuration created at %CONFIG_FILE%
) else (
    echo %BLUE%[INFO]%NC% Configuration file already exists
)

REM Copy agent files (if available)
echo %GREEN%[STEP]%NC% Setting up agent files...
if exist "local_device_agent.py" (
    copy "local_device_agent.py" "%AGENT_DIR%\local_device_agent.py" >nul
    echo %GREEN%[SUCCESS]%NC% Agent files copied
) else (
    echo %YELLOW%[WARNING]%NC% Agent source files not found in current directory
    echo %BLUE%[INFO]%NC% In production, agent files would be downloaded from the SaaS platform
)

REM Create startup script
echo %GREEN%[STEP]%NC% Creating startup script...
set STARTUP_SCRIPT=%AGENT_DIR%\start_agent.bat
(
    echo @echo off
    echo cd /d "%AGENT_DIR%"
    echo call venv\Scripts\activate.bat
    echo python local_device_agent.py
    echo pause
) > "%STARTUP_SCRIPT%"
echo %GREEN%[SUCCESS]%NC% Startup script created: %STARTUP_SCRIPT%

REM Create desktop shortcut
echo %GREEN%[STEP]%NC% Creating desktop shortcut...
set SHORTCUT_SCRIPT=%TEMP%\create_shortcut.vbs
(
    echo Set oWS = WScript.CreateObject^("WScript.Shell"^)
    echo sLinkFile = "%USERPROFILE%\Desktop\Mobile Testing Agent.lnk"
    echo Set oLink = oWS.CreateShortcut^(sLinkFile^)
    echo oLink.TargetPath = "%STARTUP_SCRIPT%"
    echo oLink.WorkingDirectory = "%AGENT_DIR%"
    echo oLink.Description = "Mobile Testing SaaS Local Agent"
    echo oLink.Save
) > "%SHORTCUT_SCRIPT%"
cscript //nologo "%SHORTCUT_SCRIPT%"
del "%SHORTCUT_SCRIPT%"
echo %GREEN%[SUCCESS]%NC% Desktop shortcut created

REM Run diagnostics
echo %GREEN%[STEP]%NC% Running system diagnostics...
echo %BLUE%[INFO]%NC% Checking Appium Doctor...
appium-doctor >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% Appium Doctor reported issues. Run 'appium-doctor' for details.
) else (
    echo %GREEN%[SUCCESS]%NC% Appium Doctor checks passed
)

echo %BLUE%[INFO]%NC% Checking connected Android devices...
adb devices

REM Print completion message
echo.
echo %GREEN%============================================================%NC%
echo %GREEN%  Installation Completed Successfully!%NC%
echo %GREEN%============================================================%NC%
echo.
echo %CYAN%Agent Directory:%NC% %AGENT_DIR%
echo %CYAN%Configuration:%NC% %CONFIG_FILE%
echo %CYAN%Startup Script:%NC% %STARTUP_SCRIPT%
echo.
echo %YELLOW%Next Steps:%NC%
echo 1. Update the configuration file with your SaaS platform details
echo 2. Run the agent using the desktop shortcut or startup script
echo 3. Register the agent with your SaaS platform
echo.
echo %CYAN%Start Agent:%NC% Double-click the "Mobile Testing Agent" shortcut on your desktop
echo %CYAN%Or run:%NC% %STARTUP_SCRIPT%
echo.
echo %GREEN%Installation completed successfully!%NC%
echo.
pause