#!/bin/bash

# Mobile Testing SaaS - Local Device Agent Installer
# This script installs and configures the local device agent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AGENT_VERSION="1.0.0"
AGENT_DIR="$HOME/.mobile-testing-agent"
PYTHON_MIN_VERSION="3.8"
NODE_MIN_VERSION="14"

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  Mobile Testing SaaS - Local Agent Installer  ${NC}"
    echo -e "${BLUE}  Version: $AGENT_VERSION                       ${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_os() {
    print_step "Detecting operating system..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_info "Detected: macOS"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_info "Detected: Linux"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        print_info "Detected: Windows"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

check_python() {
    print_step "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_info "Found Python $PYTHON_VERSION"
        
        # Check if version is sufficient
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
            PYTHON_CMD="python3"
        else
            print_error "Python 3.8+ is required. Found: $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 is not installed. Please install Python 3.8+ first."
        exit 1
    fi
}

check_node() {
    print_step "Checking Node.js installation..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        print_info "Found Node.js $NODE_VERSION"
    else
        print_warning "Node.js not found. Installing Node.js..."
        install_node
    fi
}

install_node() {
    if [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew install node
        else
            print_error "Homebrew not found. Please install Node.js manually from https://nodejs.org"
            exit 1
        fi
    elif [[ "$OS" == "linux" ]]; then
        # Install Node.js using NodeSource repository
        curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
        sudo apt-get install -y nodejs
    else
        print_error "Please install Node.js manually from https://nodejs.org"
        exit 1
    fi
}

install_appium() {
    print_step "Installing Appium..."
    
    if command -v appium &> /dev/null; then
        print_info "Appium already installed"
    else
        npm install -g appium
        npm install -g appium-doctor
    fi
    
    # Install Appium drivers
    print_info "Installing Appium drivers..."
    appium driver install uiautomator2
    
    if [[ "$OS" == "macos" ]]; then
        appium driver install xcuitest
    fi
}

install_android_tools() {
    print_step "Setting up Android development tools..."
    
    if command -v adb &> /dev/null; then
        print_info "ADB already available"
    else
        if [[ "$OS" == "macos" ]]; then
            if command -v brew &> /dev/null; then
                brew install android-platform-tools
            else
                print_warning "Please install Android SDK manually"
            fi
        elif [[ "$OS" == "linux" ]]; then
            sudo apt-get update
            sudo apt-get install -y android-tools-adb android-tools-fastboot
        else
            print_warning "Please install Android SDK manually"
        fi
    fi
}

install_ios_tools() {
    if [[ "$OS" == "macos" ]]; then
        print_step "Setting up iOS development tools..."
        
        # Check if Xcode is installed
        if xcode-select -p &> /dev/null; then
            print_info "Xcode Command Line Tools already installed"
        else
            print_info "Installing Xcode Command Line Tools..."
            xcode-select --install
        fi
        
        # Install libimobiledevice
        if command -v brew &> /dev/null; then
            brew install libimobiledevice
            brew install ios-deploy
        else
            print_warning "Homebrew not found. Please install libimobiledevice manually"
        fi
    fi
}

install_homebrew() {
    if [[ "$OS" == "macos" ]] && ! command -v brew &> /dev/null; then
        print_step "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
}

create_agent_directory() {
    print_step "Creating agent directory..."
    
    mkdir -p "$AGENT_DIR"
    mkdir -p "$AGENT_DIR/config"
    mkdir -p "$AGENT_DIR/logs"
    mkdir -p "$AGENT_DIR/temp"
    
    print_info "Agent directory created at: $AGENT_DIR"
}

install_python_dependencies() {
    print_step "Installing Python dependencies..."
    
    # Create virtual environment
    $PYTHON_CMD -m venv "$AGENT_DIR/venv"
    source "$AGENT_DIR/venv/bin/activate"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    cat > "$AGENT_DIR/requirements.txt" << EOF
aiohttp>=3.8.0
python-socketio>=5.7.0
appium-python-client>=2.9.0
cryptography>=3.4.8
requests>=2.28.0
psutil>=5.9.0
EOF
    
    pip install -r "$AGENT_DIR/requirements.txt"
    
    print_info "Python dependencies installed"
}

download_agent() {
    print_step "Downloading agent files..."
    
    # In a real deployment, this would download from your SaaS platform
    # For now, we'll copy the local files
    
    if [[ -f "local_device_agent.py" ]]; then
        cp "local_device_agent.py" "$AGENT_DIR/"
        print_info "Agent script copied"
    else
        print_error "Agent script not found. Please download from the SaaS platform."
        exit 1
    fi
    
    if [[ -f "config/agent.conf" ]]; then
        cp "config/agent.conf" "$AGENT_DIR/config/"
        print_info "Configuration template copied"
    fi
}

configure_agent() {
    print_step "Configuring agent..."
    
    echo
    echo -e "${YELLOW}Please provide the following information:${NC}"
    
    read -p "SaaS Platform URL: " PLATFORM_URL
    read -p "Agent ID: " AGENT_ID
    read -p "API Key: " API_KEY
    
    # Update configuration
    cat > "$AGENT_DIR/config/agent.conf" << EOF
{
  "agent_id": "$AGENT_ID",
  "api_key": "$API_KEY",
  "platform_url": "$PLATFORM_URL",
  "appium_server": {
    "host": "localhost",
    "port": 4723,
    "auto_start": true
  },
  "device_discovery": {
    "android": {
      "enabled": true,
      "adb_path": "adb"
    },
    "ios": {
      "enabled": $([ "$OS" == "macos" ] && echo "true" || echo "false")
    }
  },
  "logging": {
    "level": "INFO",
    "file": "$AGENT_DIR/logs/agent.log",
    "max_size_mb": 10,
    "backup_count": 5
  },
  "heartbeat_interval": 30,
  "max_concurrent_tests": 3
}
EOF
    
    print_info "Agent configured successfully"
}

create_startup_script() {
    print_step "Creating startup script..."
    
    cat > "$AGENT_DIR/start_agent.sh" << EOF
#!/bin/bash

# Mobile Testing Agent Startup Script

AGENT_DIR="$AGENT_DIR"
cd "\$AGENT_DIR"

# Activate virtual environment
source "\$AGENT_DIR/venv/bin/activate"

# Start Appium server in background
echo "Starting Appium server..."
appium --log-level info --log "\$AGENT_DIR/logs/appium.log" &
APPIUM_PID=\$!

# Wait for Appium to start
sleep 5

# Start the agent
echo "Starting Mobile Testing Agent..."
python local_device_agent.py --config config/agent.conf

# Cleanup
echo "Stopping Appium server..."
kill \$APPIUM_PID 2>/dev/null || true
EOF
    
    chmod +x "$AGENT_DIR/start_agent.sh"
    
    print_info "Startup script created at: $AGENT_DIR/start_agent.sh"
}

run_diagnostics() {
    print_step "Running diagnostics..."
    
    echo
    print_info "System Information:"
    echo "  OS: $OS"
    echo "  Python: $(python3 --version)"
    echo "  Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
    echo "  Appium: $(appium --version 2>/dev/null || echo 'Not installed')"
    echo "  ADB: $(adb version 2>/dev/null | head -1 || echo 'Not installed')"
    
    if [[ "$OS" == "macos" ]]; then
        echo "  Xcode: $(xcode-select -p 2>/dev/null || echo 'Not installed')"
        echo "  libimobiledevice: $(idevice_id --version 2>/dev/null || echo 'Not installed')"
    fi
    
    echo
    print_info "Connected Devices:"
    
    # Check Android devices
    if command -v adb &> /dev/null; then
        echo "  Android devices:"
        adb devices | tail -n +2 | while read line; do
            if [[ -n "$line" ]]; then
                echo "    $line"
            fi
        done
    fi
    
    # Check iOS devices (macOS only)
    if [[ "$OS" == "macos" ]] && command -v idevice_id &> /dev/null; then
        echo "  iOS devices:"
        idevice_id -l | while read device_id; do
            if [[ -n "$device_id" ]]; then
                device_name=$(ideviceinfo -u "$device_id" -k DeviceName 2>/dev/null || echo "Unknown")
                echo "    $device_id ($device_name)"
            fi
        done
    fi
}

print_completion() {
    echo
    echo -e "${GREEN}================================================${NC}"
    echo -e "${GREEN}  Installation completed successfully!          ${NC}"
    echo -e "${GREEN}================================================${NC}"
    echo
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Connect your mobile devices via USB"
    echo "2. Start the agent: $AGENT_DIR/start_agent.sh"
    echo "3. Check the SaaS platform for device registration"
    echo
    echo -e "${BLUE}Useful commands:${NC}"
    echo "  Start agent: $AGENT_DIR/start_agent.sh"
    echo "  View logs: tail -f $AGENT_DIR/logs/agent.log"
    echo "  Check devices: adb devices (Android) | idevice_id -l (iOS)"
    echo
    echo -e "${YELLOW}Support: Visit your SaaS platform documentation${NC}"
    echo
}

# Main installation flow
main() {
    print_header
    
    check_os
    check_python
    
    if [[ "$OS" == "macos" ]]; then
        install_homebrew
    fi
    
    check_node
    install_appium
    install_android_tools
    
    if [[ "$OS" == "macos" ]]; then
        install_ios_tools
    fi
    
    create_agent_directory
    install_python_dependencies
    download_agent
    configure_agent
    create_startup_script
    
    run_diagnostics
    print_completion
}

# Run main function
main "$@"