import os
from pathlib import Path

from app_android.utils.directory_paths_db import DirectoryPathsDB

BASE_DIR = Path(__file__).resolve().parent
WORKSPACE_ROOT = Path(os.environ.get('AUTOMATION_WORKSPACE', Path.home() / 'MobileAutomationWorkspace'))
ANDROID_WORKSPACE = WORKSPACE_ROOT / 'android'
SHARED_WORKSPACE = WORKSPACE_ROOT / 'shared'

directory_paths_db = DirectoryPathsDB()


def _ensure_directory(path: Path) -> Path:
    path.mkdir(parents=True, exist_ok=True)
    return path


def _resolve_directory(key: str, default_subpath: str) -> Path:
    override = directory_paths_db.get_path(key)
    if override:
        override_path = Path(override)
        if not override_path.is_absolute():
            override_path = WORKSPACE_ROOT / override_path
        return _ensure_directory(override_path)
    return _ensure_directory(ANDROID_WORKSPACE / default_subpath)


DIRECTORIES = {
    'TEST_CASES': _resolve_directory('TEST_CASES', 'test_cases'),
    'REPORTS': _resolve_directory('REPORTS', 'reports'),
    'SCREENSHOTS': _resolve_directory('SCREENSHOTS', 'screenshots'),
    'REFERENCE_IMAGES': _resolve_directory('REFERENCE_IMAGES', 'reference_images'),
    'TEST_SUITES': _resolve_directory('TEST_SUITES', 'test_suites'),
    'RESULTS': _resolve_directory('RESULTS', 'reports/suites'),
    'RECORDINGS': _resolve_directory('RECORDINGS', 'recordings'),
    'TEMP_FILES': _resolve_directory('TEMP_FILES', 'temp'),
}

FILES_TO_PUSH_DIR = _resolve_directory('FILES_TO_PUSH', '../shared/files_to_push')

GLOBAL_VALUES = {
    'Auto Rerun Failed': True,
    'Test Run Retry': 3,
    'Connection Retry Attempts': 3,
    'Connection Retry Delay': 2,
    'Max Step Execution Time': 300,
    'Test Case Delay': 15,
    'default_element_timeout': 60,
}

DEFAULT_FLASK_PORT = 8081
DEFAULT_APPIUM_PORT = 4724
DEFAULT_WDA_PORT = 8300

FLASK_PORT = int(os.environ.get('FLASK_PORT', DEFAULT_FLASK_PORT))
APPIUM_PORT = int(os.environ.get('APPIUM_PORT', DEFAULT_APPIUM_PORT))
WDA_PORT = int(os.environ.get('WDA_PORT', DEFAULT_WDA_PORT))

APPIUM_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': APPIUM_PORT,
    'BASE_PATH': '/wd/hub'
}

ADB_CONFIG = {
    'TIMEOUT': 10,
    'MAX_RETRIES': 3
}


class FlaskConfig:
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'mobile-automation-android-secret-key')
    SESSION_COOKIE_NAME = 'android_session'
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_SECURE = False
    TESTING = False
    DEBUG = False
    PORT = FLASK_PORT
    HOST = '0.0.0.0'
    TEST_CASES_DIR = str(DIRECTORIES['TEST_CASES'])
    REPORTS_DIR = str(DIRECTORIES['REPORTS'])
    SCREENSHOTS_DIR = str(DIRECTORIES['SCREENSHOTS'])
    TEMP_DIR = str(DIRECTORIES['TEMP_FILES'])
