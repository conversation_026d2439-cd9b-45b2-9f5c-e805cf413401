#!/bin/bash

# Dedicated Android Appium Server Startup Script
# Starts Appium server on port 4724 specifically for Android automation

set -e

# Configuration
ANDROID_APPIUM_PORT=4724
LOG_DIR="logs"
APPIUM_LOG_FILE="$LOG_DIR/android_appium.log"
PID_FILE="$LOG_DIR/android_appium.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[Android Appium]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[Android Appium]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[Android Appium]${NC} $1"
}

print_error() {
    echo -e "${RED}[Android Appium]${NC} $1"
}

# <PERSON>reate logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    print_warning "Killing process on port $port..."
    lsof -ti :$port | xargs kill -9 2>/dev/null || true
    sleep 2
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to stop existing Android Appium server
stop_android_appium() {
    print_status "Stopping existing Android Appium server..."
    
    # Kill by PID file if it exists
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            print_warning "Stopping Android Appium server (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            sleep 2
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Force killing Android Appium server..."
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "$PID_FILE"
    fi
    
    # Kill any process on the Android Appium port
    if check_port $ANDROID_APPIUM_PORT; then
        kill_port $ANDROID_APPIUM_PORT
    fi
    
    # Kill any remaining Appium processes on port 4724
    pkill -f "appium.*4724" 2>/dev/null || true
    sleep 1
}

# Function to start Android Appium server
start_android_appium() {
    print_status "Starting Android Appium server on port $ANDROID_APPIUM_PORT..."
    
    # Check if port is still in use
    if check_port $ANDROID_APPIUM_PORT; then
        print_error "Port $ANDROID_APPIUM_PORT is still in use after cleanup"
        return 1
    fi
    
    # Use local Appium if available, otherwise use global
    local appium_cmd
    if [ -f "./node_modules/.bin/appium" ]; then
        appium_cmd="./node_modules/.bin/appium"
        print_status "Using local Appium installation"
    else
        appium_cmd="appium"
        print_status "Using global Appium installation"
    fi
    
    # Start Android Appium server
    print_status "Starting Appium server with UiAutomator2 driver..."
    $appium_cmd server \
        --port $ANDROID_APPIUM_PORT \
        --address 127.0.0.1 \
        --base-path /wd/hub \
        --log "$APPIUM_LOG_FILE" \
        --log-level info \
        --use-drivers uiautomator2 \
        --relaxed-security \
        --allow-insecure chromedriver_autodownload \
        --allow-cors \
        > "$LOG_DIR/android_appium_startup.log" 2>&1 &
    
    local appium_pid=$!
    echo $appium_pid > "$PID_FILE"
    
    print_status "Android Appium server started with PID: $appium_pid"
    
    # Wait for server to be ready
    if wait_for_service "http://localhost:$ANDROID_APPIUM_PORT/status" "Android Appium Server"; then
        print_success "Android Appium server is ready on port $ANDROID_APPIUM_PORT"
        print_status "Log file: $APPIUM_LOG_FILE"
        print_status "PID file: $PID_FILE"
        return 0
    else
        print_error "Android Appium server failed to start"
        # Clean up PID file
        rm -f "$PID_FILE"
        return 1
    fi
}

# Main execution
case "${1:-start}" in
    start)
        print_status "Starting Android Appium Server..."
        stop_android_appium
        if start_android_appium; then
            print_success "Android Appium server started successfully!"
            print_status "Server URL: http://127.0.0.1:$ANDROID_APPIUM_PORT/wd/hub"
            print_status "To stop: $0 stop"
            print_status "To restart: $0 restart"
            print_status "To view logs: tail -f $APPIUM_LOG_FILE"
        else
            print_error "Failed to start Android Appium server"
            exit 1
        fi
        ;;
    stop)
        stop_android_appium
        print_success "Android Appium server stopped"
        ;;
    restart)
        print_status "Restarting Android Appium server..."
        stop_android_appium
        if start_android_appium; then
            print_success "Android Appium server restarted successfully!"
        else
            print_error "Failed to restart Android Appium server"
            exit 1
        fi
        ;;
    status)
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                print_success "Android Appium server is running (PID: $pid)"
                if check_port $ANDROID_APPIUM_PORT; then
                    print_success "Port $ANDROID_APPIUM_PORT is active"
                    # Test server status
                    if curl -s "http://localhost:$ANDROID_APPIUM_PORT/status" > /dev/null 2>&1; then
                        print_success "Server is responding to status requests"
                    else
                        print_warning "Server is not responding to status requests"
                    fi
                else
                    print_warning "Port $ANDROID_APPIUM_PORT is not active"
                fi
            else
                print_warning "PID file exists but process is not running"
                rm -f "$PID_FILE"
            fi
        else
            print_warning "Android Appium server is not running"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        echo ""
        echo "Commands:"
        echo "  start   - Start Android Appium server on port $ANDROID_APPIUM_PORT"
        echo "  stop    - Stop Android Appium server"
        echo "  restart - Restart Android Appium server"
        echo "  status  - Check Android Appium server status"
        exit 1
        ;;
esac
