#!/usr/bin/env python3
"""
Quick Dependency Installer

Installs all required dependencies for the secure distribution app.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Install dependencies"""
    try:
        print("📦 Installing dependencies for Mobile App Automation...")

        # Find requirements file
        app_dir = Path(__file__).parent / 'secure_distribution_app'
        requirements_file = app_dir / 'requirements.txt'

        if not requirements_file.exists():
            print(f"❌ Requirements file not found: {requirements_file}")
            sys.exit(1)

        print(f"📄 Using requirements file: {requirements_file}")

        # Try different installation methods
        install_commands = [
            # Try with --user flag first
            [sys.executable, '-m', 'pip', 'install', '--user', '-r', str(requirements_file)],
            # Try with --break-system-packages if user flag fails
            [sys.executable, '-m', 'pip', 'install', '--break-system-packages', '-r', str(requirements_file)],
            # Try with pip3 directly
            ['pip3', 'install', '--user', '-r', str(requirements_file)]
        ]

        for i, cmd in enumerate(install_commands):
            try:
                print(f"⏳ Attempting installation method {i+1}...")
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    print("✅ Dependencies installed successfully!")
                    print("\n📋 You can now run:")
                    print("cd secure_distribution_app")
                    print("python3 build_secure_executable.py")
                    return
                else:
                    print(f"⚠️ Method {i+1} failed, trying next method...")
                    if i == len(install_commands) - 1:  # Last attempt
                        print("❌ All installation methods failed")
                        print("\n💡 Please try one of these solutions:")
                        print("1. Use the setup script: ./setup_and_build.sh")
                        print("2. Create a virtual environment manually:")
                        print("   python3 -m venv venv")
                        print("   source venv/bin/activate")
                        print("   pip install -r secure_distribution_app/requirements.txt")
                        print("3. Use pipx: brew install pipx && pipx install -r secure_distribution_app/requirements.txt")
                        sys.exit(1)

            except FileNotFoundError:
                continue  # Try next method

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
