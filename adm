#!/usr/bin/env python3
"""
Appium Device Manager (ADM) - Quick CLI Wrapper

A simple wrapper script for the Appium Device Manager CLI.
This provides a shorter command name for easier usage.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from appium_device_manager import main

if __name__ == "__main__":
    sys.exit(main())