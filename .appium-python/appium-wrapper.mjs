#!/usr/bin/env node
            import path from 'node:path';
            import {fileURLToPath, pathToFileURL} from 'node:url';

            const __dirname = path.dirname(fileURLToPath(import.meta.url));
            const projectRoot = path.resolve(__dirname, '..');

            const asyncboxPath = pathToFileURL(path.join(projectRoot, 'node_modules', 'asyncbox', 'lib', 'asyncbox.js'));
            const appiumMainPath = pathToFileURL(path.join(projectRoot, 'node_modules', 'appium', 'lib', 'main.js'));

            const asyncboxModule = await import(asyncboxPath.href);
            const {asyncify} = asyncboxModule;

            const appiumModule = await import(appiumMainPath.href);

            if (typeof asyncify !== 'function' || typeof appiumModule.main !== 'function') {
              console.error('Failed to load local Appium runtime.');
              process.exit(1);
            }

            asyncify(appiumModule.main);
