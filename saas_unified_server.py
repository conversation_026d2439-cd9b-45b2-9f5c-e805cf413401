#!/usr/bin/env python3
"""
SaaS Unified Server - Mobile Testing Platform with Secure Distribution

This is the main server application for the SaaS version of the mobile testing platform.
It provides multi-tenant support, user authentication, secure file distribution, and API
endpoints for managing mobile testing workflows through local device agents.

Enhanced for secure distribution of mobile app automation tools to subscribers.
"""

import os
import sys
import logging
import hashlib
import secrets
import zipfile
import tempfile
import shutil
from datetime import datetime, timedelta
from functools import wraps
from pathlib import Path

from flask import Flask, request, jsonify, g, render_template, session, redirect, url_for, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from flask_jwt_extended import (
    JWTManager, jwt_required, create_access_token,
    get_jwt_identity, create_refresh_token, get_jwt
)
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_socketio import SocketIO, emit, join_room, leave_room
from werkzeug.security import generate_password_hash, check_password_hash
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import redis
import structlog
import base64
import json
import uuid

# Import container orchestrator and session manager for isolated instances
from container_orchestrator import ContainerOrchestrator
from session_manager import SessionManager

# Configure structured logging
logging.basicConfig(level=logging.INFO)
logger = structlog.get_logger()

# Initialize Flask app
app = Flask(__name__)

# Configuration
class Config:
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 
        'sqlite:///saas_platform.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Security
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-change-in-production')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # Redis
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/1')
    
    # File Storage
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', '/app/uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config.from_object(Config)

# Initialize extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)
cors = CORS(app, 
    origins=["*"],
    allow_headers=["Content-Type", "Authorization"],
    methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
)  # Configure properly for production
jwt = JWTManager(app)

# Make session available in templates
@app.context_processor
def inject_session():
    return dict(session=session)

# Rate limiting
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"]
)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='gevent')

# Redis connection
try:
    redis_client = redis.from_url(app.config['REDIS_URL'])
    redis_client.ping()
    logger.info("Redis connection established")
except Exception as e:
    logger.error(f"Redis connection failed: {e}")
    redis_client = None

# Global variables for device management
connected_agents = {}
device_registry = {}
connected_devices = {}  # Store device information from agents

# Global variable for tracking user sessions
user_sessions = {}  # Maps user_id to session_id for active instances

# Initialize orchestrator and session manager
orchestrator = ContainerOrchestrator()
session_manager = SessionManager(orchestrator, user_sessions)

# Database Models
class User(db.Model):
    """User model with multi-tenant support"""
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100))
    tenant_id = db.Column(db.String(36), nullable=False, index=True)
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    test_suites = db.relationship('TestSuite', backref='user', lazy=True, cascade='all, delete-orphan')
    device_agents = db.relationship('DeviceAgent', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class TestSuite(db.Model):
    """Test suite model with tenant isolation"""
    __tablename__ = 'test_suites'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    platform = db.Column(db.String(20))  # 'ios' or 'android'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    test_cases = db.relationship('TestCase', backref='test_suite', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'platform': self.platform,
            'test_cases_count': len(self.test_cases),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class TestCase(db.Model):
    """Test case model with tenant isolation"""
    __tablename__ = 'test_cases'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    test_suite_id = db.Column(db.String(36), db.ForeignKey('test_suites.id'), nullable=False, index=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    steps = db.Column(db.JSON)  # Store test steps as JSON
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'steps': self.steps or [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class DeviceAgent(db.Model):
    """Device agent registration and management"""
    __tablename__ = 'device_agents'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    agent_name = db.Column(db.String(255), nullable=False)
    agent_token = db.Column(db.String(255), unique=True, nullable=False)
    platform = db.Column(db.String(20))  # 'ios', 'android', or 'both'
    status = db.Column(db.String(20), default='offline')  # 'online', 'offline', 'busy'
    last_seen = db.Column(db.DateTime)
    capabilities = db.Column(db.JSON)  # Store device capabilities
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'agent_name': self.agent_name,
            'platform': self.platform,
            'status': self.status,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None,
            'capabilities': self.capabilities or {},
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class DistributionApp(db.Model):
    """Model for managing distributed mobile apps"""
    __tablename__ = 'distribution_apps'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    platform = db.Column(db.String(20), nullable=False)  # 'ios', 'android', or 'both'
    version = db.Column(db.String(50), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)  # Path to encrypted app package
    file_hash = db.Column(db.String(64), nullable=False)  # SHA-256 hash for integrity
    encryption_key_id = db.Column(db.String(36), nullable=False)  # Reference to encryption key
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user_permissions = db.relationship('UserAppPermission', backref='app', lazy=True, cascade='all, delete-orphan')
    download_logs = db.relationship('DownloadLog', backref='app', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'platform': self.platform,
            'version': self.version,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class UserAppPermission(db.Model):
    """Model for managing user permissions to specific apps"""
    __tablename__ = 'user_app_permissions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    app_id = db.Column(db.String(36), db.ForeignKey('distribution_apps.id'), nullable=False, index=True)
    granted_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime)  # Optional expiration
    is_active = db.Column(db.Boolean, default=True)

    # Unique constraint to prevent duplicate permissions
    __table_args__ = (db.UniqueConstraint('user_id', 'app_id', name='unique_user_app_permission'),)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'app_id': self.app_id,
            'granted_at': self.granted_at.isoformat() if self.granted_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active
        }

class EncryptionKey(db.Model):
    """Model for managing encryption keys for distributed apps"""
    __tablename__ = 'encryption_keys'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    key_data = db.Column(db.Text, nullable=False)  # Base64 encoded encrypted key
    salt = db.Column(db.String(64), nullable=False)  # Salt for key derivation
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def to_dict(self):
        return {
            'id': self.id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_active': self.is_active
        }

class DownloadLog(db.Model):
    """Model for logging app downloads for security and analytics"""
    __tablename__ = 'download_logs'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    app_id = db.Column(db.String(36), db.ForeignKey('distribution_apps.id'), nullable=False, index=True)
    ip_address = db.Column(db.String(45))  # IPv6 compatible
    user_agent = db.Column(db.String(500))
    download_started_at = db.Column(db.DateTime, default=datetime.utcnow)
    download_completed_at = db.Column(db.DateTime)
    file_size = db.Column(db.BigInteger)
    success = db.Column(db.Boolean, default=False)
    error_message = db.Column(db.Text)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'app_id': self.app_id,
            'ip_address': self.ip_address,
            'download_started_at': self.download_started_at.isoformat() if self.download_started_at else None,
            'download_completed_at': self.download_completed_at.isoformat() if self.download_completed_at else None,
            'file_size': self.file_size,
            'success': self.success,
            'error_message': self.error_message
        }

class UserSession(db.Model):
    """Model for tracking user sessions for the GUI application"""
    __tablename__ = 'user_sessions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    session_token = db.Column(db.String(255), unique=True, nullable=False)
    client_fingerprint = db.Column(db.String(255))  # Device/client identification
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_active = db.Column(db.Boolean, default=True)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active
        }

# Security utility functions
def generate_encryption_key():
    """Generate a new encryption key for app distribution"""
    key = Fernet.generate_key()
    salt = secrets.token_hex(32)
    return key, salt

def derive_key_from_password(password: str, salt: bytes) -> bytes:
    """Derive encryption key from password and salt"""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    return base64.urlsafe_b64encode(kdf.derive(password.encode()))

def encrypt_file(file_path: str, key: bytes) -> str:
    """Encrypt a file and return the encrypted file path"""
    fernet = Fernet(key)

    with open(file_path, 'rb') as file:
        file_data = file.read()

    encrypted_data = fernet.encrypt(file_data)

    encrypted_file_path = file_path + '.encrypted'
    with open(encrypted_file_path, 'wb') as encrypted_file:
        encrypted_file.write(encrypted_data)

    return encrypted_file_path

def calculate_file_hash(file_path: str) -> str:
    """Calculate SHA-256 hash of a file"""
    sha256_hash = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

# Authentication decorators
def require_auth(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        if not current_user or not current_user.is_active:
            return jsonify({'error': 'User not found or inactive'}), 401
        g.current_user = current_user
        return f(*args, **kwargs)
    return decorated_function

def require_session_auth(f):
    """Decorator to require session-based authentication for web pages"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        current_user = User.query.get(session['user_id'])
        if not current_user or not current_user.is_active:
            session.clear()
            return redirect(url_for('login_page'))
        
        g.current_user = current_user
        return f(*args, **kwargs)
    return decorated_function

def require_admin(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    @require_auth
    def decorated_function(*args, **kwargs):
        if not g.current_user.is_admin:
            return jsonify({'error': 'Admin privileges required'}), 403
        return f(*args, **kwargs)
    return decorated_function

# Utility functions
def get_user_file_path(user_id, filename):
    """Generate user-specific file path for tenant isolation"""
    user_dir = os.path.join(app.config['UPLOAD_FOLDER'], user_id)
    os.makedirs(user_dir, exist_ok=True)
    return os.path.join(user_dir, filename)

# API Routes

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception as e:
        db_status = f'unhealthy: {str(e)}'
    
    try:
        # Check Redis connection
        if redis_client:
            redis_client.ping()
            redis_status = 'healthy'
        else:
            redis_status = 'not configured'
    except Exception as e:
        redis_status = f'unhealthy: {str(e)}'
    
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'database': db_status,
        'redis': redis_status
    })

@app.route('/api/auth/register', methods=['POST'])
@limiter.limit("5 per minute")
def register():
    """User registration endpoint"""
    data = request.get_json()
    
    # Validate input
    if not data or not data.get('email') or not data.get('password'):
        return jsonify({'error': 'Email and password required'}), 400
    
    # Check if user already exists
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': 'User already exists'}), 409
    
    # Create new user
    import uuid
    user = User(
        id=str(uuid.uuid4()),
        email=data['email'],
        first_name=data.get('first_name', ''),
        last_name=data.get('last_name', ''),
        tenant_id=str(uuid.uuid4())  # Each user gets their own tenant
    )
    user.set_password(data['password'])
    
    try:
        db.session.add(user)
        db.session.commit()
        
        logger.info(f"User registered: {user.email}")
        
        return jsonify({
            'message': 'User registered successfully',
            'user': user.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        logger.error(f"Registration failed: {e}")
        return jsonify({'error': 'Registration failed'}), 500

@app.route('/api/auth/login', methods=['POST'])
@limiter.limit("10 per minute")
def login():
    """User login endpoint"""
    data = request.get_json()
    
    if not data or not data.get('email') or not data.get('password'):
        return jsonify({'message': 'Email and password required'}), 400
    
    user = User.query.filter_by(email=data['email']).first()
    
    if not user or not user.check_password(data['password']):
        return jsonify({'message': 'Invalid credentials'}), 401
    
    if not user.is_active:
        return jsonify({'message': 'Account is disabled'}), 401
    
    # Update last login
    user.last_login = datetime.utcnow()
    db.session.commit()
    
    # Set session data for web interface
    session['user_id'] = user.id
    session['user_email'] = user.email
    session['is_admin'] = user.is_admin
    
    # Create tokens for API access
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)
    
    logger.info(f"User logged in: {user.email}")
    
    return jsonify({
        'access_token': access_token,
        'refresh_token': refresh_token,
        'user': user.to_dict()
    })

@app.route('/api/auth/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """Token refresh endpoint"""
    current_user_id = get_jwt_identity()
    new_token = create_access_token(identity=current_user_id)
    return jsonify({'access_token': new_token})

@app.route('/api/test-suites', methods=['GET'])
@require_auth
def get_test_suites():
    """Get user's test suites"""
    test_suites = TestSuite.query.filter_by(user_id=g.current_user.id).all()
    return jsonify({
        'test_suites': [suite.to_dict() for suite in test_suites]
    })

@app.route('/api/test-suites', methods=['POST'])
@require_auth
def create_test_suite():
    """Create new test suite"""
    data = request.get_json()
    
    if not data or not data.get('name'):
        return jsonify({'error': 'Test suite name required'}), 400
    
    import uuid
    test_suite = TestSuite(
        id=str(uuid.uuid4()),
        user_id=g.current_user.id,
        name=data['name'],
        description=data.get('description', ''),
        platform=data.get('platform', 'android')
    )
    
    try:
        db.session.add(test_suite)
        db.session.commit()
        
        logger.info(f"Test suite created: {test_suite.name} by {g.current_user.email}")
        
        return jsonify({
            'message': 'Test suite created successfully',
            'test_suite': test_suite.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        logger.error(f"Test suite creation failed: {e}")
        return jsonify({'error': 'Failed to create test suite'}), 500

@app.route('/api/device-agents', methods=['GET'])
@require_auth
def get_device_agents():
    """Get user's device agents"""
    agents = DeviceAgent.query.filter_by(user_id=g.current_user.id).all()
    return jsonify({
        'agents': [agent.to_dict() for agent in agents]
    })

# Secure Distribution API Endpoints

@app.route('/api/auth/validate', methods=['POST'])
@require_auth
def validate_session():
    """Validate user session for GUI application"""
    try:
        # Create or update user session
        session_token = secrets.token_urlsafe(32)
        client_fingerprint = request.json.get('client_fingerprint', '')

        # Check for existing active session
        existing_session = UserSession.query.filter_by(
            user_id=g.current_user.id,
            is_active=True
        ).first()

        if existing_session:
            # Update existing session
            existing_session.session_token = session_token
            existing_session.last_activity = datetime.utcnow()
            existing_session.expires_at = datetime.utcnow() + timedelta(hours=24)
            existing_session.client_fingerprint = client_fingerprint
        else:
            # Create new session
            user_session = UserSession(
                user_id=g.current_user.id,
                session_token=session_token,
                client_fingerprint=client_fingerprint,
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )
            db.session.add(user_session)

        db.session.commit()

        return jsonify({
            'valid': True,
            'session_token': session_token,
            'user': g.current_user.to_dict(),
            'expires_at': (datetime.utcnow() + timedelta(hours=24)).isoformat()
        })

    except Exception as e:
        logger.error(f"Session validation failed: {e}")
        return jsonify({'error': 'Session validation failed'}), 500

@app.route('/api/apps/list', methods=['GET'])
@require_auth
def list_available_apps():
    """List available apps for the authenticated user"""
    try:
        # Get apps the user has permission to access
        user_permissions = db.session.query(UserAppPermission, DistributionApp).join(
            DistributionApp, UserAppPermission.app_id == DistributionApp.id
        ).filter(
            UserAppPermission.user_id == g.current_user.id,
            UserAppPermission.is_active == True,
            DistributionApp.is_active == True
        ).all()

        apps = []
        for permission, app in user_permissions:
            app_data = app.to_dict()
            app_data['permission_granted_at'] = permission.granted_at.isoformat() if permission.granted_at else None
            app_data['permission_expires_at'] = permission.expires_at.isoformat() if permission.expires_at else None
            apps.append(app_data)

        return jsonify({
            'apps': apps,
            'total': len(apps)
        })

    except Exception as e:
        logger.error(f"Failed to list apps: {e}")
        return jsonify({'error': 'Failed to retrieve apps'}), 500

@app.route('/api/apps/<app_id>/download', methods=['POST'])
@require_auth
@limiter.limit("3 per minute")
def download_app(app_id):
    """Securely download an encrypted app package"""
    try:
        # Verify user has permission to download this app
        permission = UserAppPermission.query.filter_by(
            user_id=g.current_user.id,
            app_id=app_id,
            is_active=True
        ).first()

        if not permission:
            return jsonify({'error': 'Access denied'}), 403

        # Check if permission has expired
        if permission.expires_at and permission.expires_at < datetime.utcnow():
            return jsonify({'error': 'Permission expired'}), 403

        # Get the app
        app = DistributionApp.query.get(app_id)
        if not app or not app.is_active:
            return jsonify({'error': 'App not found'}), 404

        # Log download attempt
        download_log = DownloadLog(
            user_id=g.current_user.id,
            app_id=app_id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(download_log)
        db.session.commit()

        # Check if file exists
        if not os.path.exists(app.file_path):
            download_log.error_message = 'File not found on server'
            db.session.commit()
            return jsonify({'error': 'File not available'}), 404

        # Verify file integrity
        current_hash = calculate_file_hash(app.file_path)
        if current_hash != app.file_hash:
            download_log.error_message = 'File integrity check failed'
            db.session.commit()
            logger.error(f"File integrity check failed for app {app_id}")
            return jsonify({'error': 'File integrity check failed'}), 500

        # Update download log with file size
        download_log.file_size = os.path.getsize(app.file_path)

        try:
            # Send the encrypted file
            response = send_file(
                app.file_path,
                as_attachment=True,
                download_name=f"{app.name}_{app.version}.encrypted",
                mimetype='application/octet-stream'
            )

            # Mark download as successful
            download_log.download_completed_at = datetime.utcnow()
            download_log.success = True
            db.session.commit()

            logger.info(f"App downloaded: {app.name} by {g.current_user.email}")
            return response

        except Exception as e:
            download_log.error_message = str(e)
            db.session.commit()
            raise e

    except Exception as e:
        logger.error(f"Download failed: {e}")
        return jsonify({'error': 'Download failed'}), 500

@app.route('/api/apps/<app_id>/key', methods=['POST'])
@require_auth
def get_decryption_key(app_id):
    """Get decryption key for an app (requires additional authentication)"""
    try:
        # Verify user has permission
        permission = UserAppPermission.query.filter_by(
            user_id=g.current_user.id,
            app_id=app_id,
            is_active=True
        ).first()

        if not permission:
            return jsonify({'error': 'Access denied'}), 403

        # Get the app
        app = DistributionApp.query.get(app_id)
        if not app or not app.is_active:
            return jsonify({'error': 'App not found'}), 404

        # Get encryption key
        encryption_key = EncryptionKey.query.get(app.encryption_key_id)
        if not encryption_key or not encryption_key.is_active:
            return jsonify({'error': 'Decryption key not available'}), 404

        # Verify password for additional security
        password = request.json.get('password')
        if not password or not g.current_user.check_password(password):
            return jsonify({'error': 'Password verification required'}), 401

        return jsonify({
            'key_data': encryption_key.key_data,
            'salt': encryption_key.salt,
            'app_name': app.name,
            'app_version': app.version
        })

    except Exception as e:
        logger.error(f"Key retrieval failed: {e}")
        return jsonify({'error': 'Key retrieval failed'}), 500

# WebSocket Events for Real-time Communication

@socketio.on('connect')
def handle_connect(auth):
    """Handle WebSocket connection"""
    # Check if this is an agent connection (no JWT) or user connection (with JWT)
    if auth and 'token' in auth:
        # User connection with JWT
        try:
            from flask_jwt_extended import decode_token
            decoded_token = decode_token(auth['token'])
            current_user_id = decoded_token['sub']
            user = User.query.get(current_user_id)
            
            if not user or not user.is_active:
                return False  # Reject connection
            
            join_room(f"user_{user.id}")
            emit('connected', {'message': 'Connected to SaaS platform'})
            logger.info(f"WebSocket connected: {user.email}")
        except Exception as e:
            logger.error(f"JWT validation failed: {e}")
            return False
    else:
        # Agent connection (no JWT required)
        emit('connected', {'message': 'Connected to SaaS platform'})
        logger.info("Agent WebSocket connected")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle WebSocket disconnection"""
    logger.info("WebSocket disconnected")

@socketio.on('agent_register')
@jwt_required()
def handle_agent_register(data):
    """Handle device agent registration"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        emit('error', {'message': 'User not found'})
        return
    
    # TODO: Implement agent registration logic
    # This would validate the agent and store its capabilities
    
    emit('agent_registered', {
        'message': 'Agent registered successfully',
        'agent_id': data.get('agent_id')
    })

@socketio.on('register_agent')
def handle_register_agent(data):
    """Handle device agent registration from local agent"""
    agent_id = data.get('agent_id')
    api_key = data.get('api_key')
    
    logger.info(f"Agent registration attempt: {agent_id}")
    
    # For now, accept all agents - in production, validate api_key
    emit('agent_registered', {
        'status': 'success',
        'message': 'Agent registered successfully',
        'agent_id': agent_id
    })
    
    logger.info(f"Agent registered: {agent_id}")

@socketio.on('device_list')
def handle_device_list(data):
    """Handle device list updates from local agent"""
    agent_id = data.get('agent_id')
    devices = data.get('devices', [])
    
    logger.info(f"Device list update from agent {agent_id}: {len(devices)} devices")
    
    # Store device information globally
    connected_devices[agent_id] = devices
    
    # Store device information (in production, save to database)
    for device in devices:
        logger.info(f"Device: {device.get('name')} ({device.get('id')}) - {device.get('platform')} - {device.get('status')}")
    
    # Broadcast device list to connected clients
    emit('devices_updated', {
        'agent_id': agent_id,
        'devices': devices
    }, broadcast=True)

@socketio.on('execute_test')
def handle_execute_test(data):
    """Handle test execution requests"""
    test_id = data.get('test_id')
    device_id = data.get('device_id')
    test_steps = data.get('test_steps', [])
    
    logger.info(f"Test execution request: {test_id} on device {device_id}")
    
    # Echo back to agent for execution
    emit('execute_test', {
        'test_id': test_id,
        'device_id': device_id,
        'test_steps': test_steps
    })

@socketio.on('test_result')
def handle_test_result(data):
    """Handle test result updates from agent"""
    test_id = data.get('test_id')
    status = data.get('status')
    result = data.get('result')
    
    logger.info(f"Test result for {test_id}: {status}")
    
    # Broadcast result to connected clients
    emit('test_completed', {
        'test_id': test_id,
        'status': status,
        'result': result
    }, broadcast=True)

# Web Routes

@app.route('/')
def index():
    """Main entry point - secure authentication page"""
    return render_template('index.html')

@app.route('/dashboard')
@limiter.limit("500 per hour")
@require_session_auth
def dashboard():
    """Main dashboard page"""
    # Get user statistics for dashboard
    user_id = session.get('user_id')
    current_user = User.query.get(user_id) if user_id else None
    
    # Premium profile placeholders until subscription service integration
    subscription_seed = (user_id or 'PREMIUM')[:8].upper() if user_id else 'PREMIUM'
    subscription_id = session.get('premium_subscription_id') or f"PREM-{subscription_seed}"
    session.setdefault('premium_subscription_id', subscription_id)

    expiry_display = session.get('premium_subscription_expires_on')
    if not expiry_display:
        expiry_display = (datetime.utcnow() + timedelta(days=30)).strftime('%b %d, %Y')
        session['premium_subscription_expires_on'] = expiry_display

    last_login_at = 'Awaiting activity'
    if current_user and current_user.last_login:
        last_login_at = current_user.last_login.strftime('%b %d, %Y • %I:%M %p UTC')

    session_duration = session.get('premium_last_session_duration', 'Tracking in progress')
    session.setdefault('premium_last_session_duration', session_duration)

    premium_profile = {
        'subscription_id': subscription_id,
        'subscription_expires_on': expiry_display,
        'last_login_at': last_login_at,
        'last_session_duration': session_duration
    }

    stats = {
        'total_test_suites': TestSuite.query.filter_by(user_id=user_id).count(),
        'android_suites': TestSuite.query.filter_by(user_id=user_id, platform='android').count(),
        'ios_suites': TestSuite.query.filter_by(user_id=user_id, platform='ios').count(),
        'active_devices': 0,  # TODO: Implement device counting
        'tests_today': 0,     # TODO: Implement test counting
        'success_rate': 95,   # TODO: Implement success rate calculation
        'android_devices': 0, # TODO: Implement Android device counting
        'ios_devices': 0,     # TODO: Implement iOS device counting
        'android_last_run': 'Never',  # TODO: Implement last run tracking
        'ios_last_run': 'Never',      # TODO: Implement last run tracking
        'android_status': 'Ready',    # TODO: Implement status tracking
        'ios_status': 'Ready'         # TODO: Implement status tracking
    }
    return render_template('dashboard.html', stats=stats, premium_profile=premium_profile)

@app.route('/android-dashboard')
@require_session_auth
def android_dashboard():
    """Android-specific dashboard page"""
    user_id = session.get('user_id')
    android_stats = {
        'connected_devices': 0,  # TODO: Implement Android device counting
        'test_suites': TestSuite.query.filter_by(user_id=user_id, platform='android').count(),
        'running_tests': 0,      # TODO: Implement running test counting
        'success_rate': 95       # TODO: Implement success rate calculation
    }
    return render_template('android_dashboard.html', android_stats=android_stats)

@app.route('/ios-dashboard')
@require_session_auth
def ios_dashboard():
    """iOS-specific dashboard page"""
    user_id = session.get('user_id')
    ios_stats = {
        'connected_devices': 0,  # TODO: Implement iOS device counting
        'test_suites': TestSuite.query.filter_by(user_id=user_id, platform='ios').count(),
        'running_tests': 0,      # TODO: Implement running test counting
        'success_rate': 95       # TODO: Implement success rate calculation
    }
    return render_template('ios_dashboard.html', ios_stats=ios_stats)

@app.route('/test-history')
@require_session_auth
def test_history():
    """Test history page"""
    user_id = session.get('user_id')
    # TODO: Implement actual test history retrieval
    test_runs = []  # Placeholder for test run history
    return render_template('test_history.html', test_runs=test_runs)

@app.route('/devices')
@require_session_auth
def devices():
    """Device management page"""
    # TODO: Implement device listing
    return jsonify({"message": "Device management page - coming soon"})

@app.route('/add-device')
@require_session_auth
def add_device():
    """Add device page"""
    # TODO: Implement device addition
    return jsonify({"message": "Add device page - coming soon"})

@app.route('/create-test-suite')
@require_session_auth
def create_test_suite_page():
    """Create test suite page"""
    # TODO: Implement test suite creation
    return jsonify({"message": "Create test suite page - coming soon"})

@app.route('/download-agent')
@require_session_auth
def download_agent():
    """Download agent page"""
    # TODO: Implement agent download
    return jsonify({"message": "Download agent page - coming soon"})

@app.route('/documentation')
def documentation():
    """Documentation page"""
    # TODO: Implement documentation
    return jsonify({"message": "Documentation page - coming soon"})

@app.route('/support')
def support():
    """Support page"""
    # TODO: Implement support
    return jsonify({"message": "Support page - coming soon"})

@app.route('/tutorials')
def tutorials():
    """Tutorials page"""
    # TODO: Implement tutorials
    return jsonify({"message": "Tutorials page - coming soon"})

@app.route('/create-android-test-suite')
@require_session_auth
def create_android_test_suite():
    """Create Android test suite page"""
    # TODO: Implement Android test suite creation
    return jsonify({"message": "Create Android test suite page - coming soon"})

@app.route('/android-devices')
@require_session_auth
def android_devices():
    """Android devices page"""
    # TODO: Implement Android device management
    return jsonify({"message": "Android devices page - coming soon"})

@app.route('/android-test-history')
@require_session_auth
def android_test_history():
    """Android test history page"""
    # TODO: Implement Android test history
    return jsonify({"message": "Android test history page - coming soon"})

@app.route('/create-ios-test-suite')
@require_session_auth
def create_ios_test_suite():
    """Create iOS test suite page"""
    # TODO: Implement iOS test suite creation
    return jsonify({"message": "Create iOS test suite page - coming soon"})

@app.route('/ios-devices')
@require_session_auth
def ios_devices():
    """iOS devices page"""
    # TODO: Implement iOS device management
    return jsonify({"message": "iOS devices page - coming soon"})

@app.route('/ios-test-history')
@require_session_auth
def ios_test_history():
    """iOS test history page"""
    # TODO: Implement iOS test history
    return jsonify({"message": "iOS test history page - coming soon"})

@app.route('/app_android')
@require_session_auth
def app_android():
    """Android app interface - redirect to Android platform server with session (legacy route)"""
    user_id = session.get('user_id')
    
    # Check if user has an active Android instance
    session_id = user_sessions.get(f"{user_id}_android")
    if session_id:
        return redirect(url_for('app_android_session', session_id=session_id))
    
    # No active instance, redirect to dashboard
    return redirect(url_for('dashboard'))

@app.route('/app_android/<session_id>')
@require_session_auth
def app_android_session(session_id):
    """Access isolated Android testing platform instance"""
    user_id = session.get('user_id')
    current_user = User.query.get(user_id)
    
    # Verify user owns this session
    user_session_id = user_sessions.get(f"{user_id}_android")
    if user_session_id != session_id:
        return redirect(url_for('dashboard'))
    
    # Get instance details
    instance = orchestrator.get_instance(session_id)
    if not instance or instance.status != 'running':
        return redirect(url_for('dashboard'))
    
    # Create a temporary access token for the platform server
    access_token = create_access_token(identity=user_id)
    
    # Store session data in Redis for the platform server to access
    if redis_client:
        session_data = {
            'user_id': user_id,
            'email': current_user.email,
            'first_name': current_user.first_name,
            'last_name': current_user.last_name,
            'is_admin': current_user.is_admin,
            'session_id': session_id
        }
        # Store with a short TTL (5 minutes)
        redis_client.setex(f"session_transfer:{access_token}", 300, str(session_data))
    
    # Redirect to isolated Android platform with session token
    android_url = f"http://localhost:{instance.port}/?session_token={access_token}&return_url=http://localhost:8080/dashboard"
    return redirect(android_url)

@app.route('/app_ios')
@require_session_auth
def app_ios():
    """iOS app interface - redirect to iOS platform server with session (legacy route)"""
    user_id = session.get('user_id')
    
    # Check if user has an active iOS instance
    session_id = user_sessions.get(f"{user_id}_ios")
    if session_id:
        return redirect(url_for('app_ios_session', session_id=session_id))
    
    # No active instance, redirect to dashboard
    return redirect(url_for('dashboard'))

@app.route('/app_ios/<session_id>')
@require_session_auth
def app_ios_session(session_id):
    """Access isolated iOS testing platform instance"""
    user_id = session.get('user_id')
    current_user = User.query.get(user_id)
    
    # Verify user owns this session
    user_session_id = user_sessions.get(f"{user_id}_ios")
    if user_session_id != session_id:
        return redirect(url_for('dashboard'))
    
    # Get instance details
    instance = orchestrator.get_instance(session_id)
    if not instance or instance.status != 'running':
        return redirect(url_for('dashboard'))
    
    # Create a temporary access token for the platform server
    access_token = create_access_token(identity=user_id)
    
    # Store session data in Redis for the platform server to access
    if redis_client:
        session_data = {
            'user_id': user_id,
            'email': current_user.email,
            'first_name': current_user.first_name,
            'last_name': current_user.last_name,
            'is_admin': current_user.is_admin,
            'session_id': session_id
        }
        # Store with a short TTL (5 minutes)
        redis_client.setex(f"session_transfer:{access_token}", 300, str(session_data))
    
    # Redirect to isolated iOS platform with session token
    ios_url = f"http://localhost:{instance.port}/?session_token={access_token}&return_url=http://localhost:8080/dashboard"
    return redirect(ios_url)

@app.route('/login')
def login_page():
    """Login page"""
    return render_template('login.html')

@app.route('/register')
def register_page():
    """Registration page"""
    return render_template('register.html')

@app.route('/logout', methods=['POST'])
def logout():
    """Logout user"""
    session.clear()
    return jsonify({'success': True, 'message': 'Logged out successfully'})

# Error Handlers

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({'error': 'Rate limit exceeded', 'retry_after': str(e.retry_after)}), 429

# CLI Commands

@app.cli.command()
def create_admin():
    """Create admin user"""
    import uuid
    from getpass import getpass
    
    email = input("Admin email: ")
    password = getpass("Admin password: ")
    
    if User.query.filter_by(email=email).first():
        print("User already exists")
        return
    
    admin = User(
        id=str(uuid.uuid4()),
        email=email,
        first_name="Admin",
        last_name="User",
        tenant_id=str(uuid.uuid4()),
        is_admin=True
    )
    admin.set_password(password)
    
    db.session.add(admin)
    db.session.commit()
    
    print(f"Admin user created: {email}")

@app.cli.command()
def init_db():
    """Initialize database"""
    db.create_all()
    print("Database initialized")

# Platform launch endpoints
@app.route('/api/launch/ios', methods=['POST'])
@jwt_required()
def launch_ios():
    """Launch isolated iOS testing platform instance"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404
        
        # Check if user already has an active iOS instance
        user_session = session_manager.get_or_create_session(current_user_id)
        if user_session.ios_session_id:
            existing_instance = orchestrator.get_instance(user_session.ios_session_id)
            if existing_instance and existing_instance.status == 'running':
                user_session.update_activity()
                return jsonify({
                    'success': True,
                    'message': 'iOS platform already running',
                    'redirect_url': f'/app_ios/{existing_instance.session_id}',
                    'session_id': existing_instance.session_id,
                    'port': existing_instance.port
                })
        
        # Log the platform launch
        logger.info(f"User {user.email} launching isolated iOS platform")
        
        try:
            # Create new isolated container instance
            instance = orchestrator.create_instance(current_user_id, 'ios')
            
            if not instance:
                return jsonify({
                    'success': False,
                    'error': 'Failed to create iOS instance - no available resources'
                }), 503
            
            # Store user session mapping using session manager
            session_manager.update_session_instance(current_user_id, 'ios', instance.session_id)
            
            return jsonify({
                'success': True,
                'message': 'iOS platform launched successfully in isolated container',
                'redirect_url': f'/app_ios/{instance.session_id}',
                'session_id': instance.session_id,
                'port': instance.port,
                'appium_port': instance.appium_port
            })
            
        except Exception as e:
            logger.error(f"Failed to launch iOS platform: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'Failed to start iOS platform: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f"Error in launch_ios: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/session/create', methods=['POST'])
def create_session():
    """Create a new session for testing (simplified endpoint without auth)"""
    try:
        data = request.get_json()
        platform = data.get('platform', 'android')
        user_id = data.get('user_id', 'test_user')
        
        if platform not in ['android', 'ios']:
            return jsonify({'success': False, 'error': 'Invalid platform. Must be android or ios'}), 400
        
        # Check if user already has an active instance
        user_session = session_manager.get_or_create_session(user_id)
        
        if platform == 'android' and user_session.android_session_id:
            existing_instance = orchestrator.get_instance(user_session.android_session_id)
            if existing_instance and existing_instance.status == 'running':
                user_session.update_activity()
                return jsonify({
                    'success': True,
                    'message': f'{platform.title()} platform already running',
                    'session_id': existing_instance.session_id,
                    'port': existing_instance.port,
                    'url': f'http://localhost:{existing_instance.port}'
                })
        elif platform == 'ios' and user_session.ios_session_id:
            existing_instance = orchestrator.get_instance(user_session.ios_session_id)
            if existing_instance and existing_instance.status == 'running':
                user_session.update_activity()
                return jsonify({
                    'success': True,
                    'message': f'{platform.title()} platform already running',
                    'session_id': existing_instance.session_id,
                    'port': existing_instance.port,
                    'url': f'http://localhost:{existing_instance.port}'
                })
        
        # Create new isolated container instance
        instance = orchestrator.create_instance(user_id, platform)
        
        if not instance:
            return jsonify({
                'success': False,
                'error': f'Failed to create {platform} instance - no available resources'
            }), 503
        
        # Store user session mapping using session manager
        session_manager.update_session_instance(user_id, platform, instance.session_id)
        
        return jsonify({
            'success': True,
            'message': f'{platform.title()} platform launched successfully in isolated container',
            'session_id': instance.session_id,
            'port': instance.port,
            'url': f'http://localhost:{instance.port}',
            'appium_port': getattr(instance, 'appium_port', None)
        })
        
    except Exception as e:
        logger.error(f"Error in create_session: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/launch/android', methods=['POST'])
@jwt_required()
def launch_android():
    """Launch isolated Android testing platform instance"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404
        
        # Check if user already has an active Android instance
        user_session = session_manager.get_or_create_session(current_user_id)
        if user_session.android_session_id:
            existing_instance = orchestrator.get_instance(user_session.android_session_id)
            if existing_instance and existing_instance.status == 'running':
                user_session.update_activity()
                return jsonify({
                    'success': True,
                    'message': 'Android platform already running',
                    'redirect_url': f'/app_android/{existing_instance.session_id}',
                    'session_id': existing_instance.session_id,
                    'port': existing_instance.port
                })
        
        # Log the platform launch
        logger.info(f"User {user.email} launching isolated Android platform")
        
        try:
            # Create new isolated container instance
            instance = orchestrator.create_instance(current_user_id, 'android')
            
            if not instance:
                return jsonify({
                    'success': False,
                    'error': 'Failed to create Android instance - no available resources'
                }), 503
            
            # Store user session mapping using session manager
            session_manager.update_session_instance(current_user_id, 'android', instance.session_id)
            
            return jsonify({
                'success': True,
                'message': 'Android platform launched successfully in isolated container',
                'redirect_url': f'/app_android/{instance.session_id}',
                'session_id': instance.session_id,
                'port': instance.port,
                'appium_port': instance.appium_port
            })
            
        except Exception as e:
            logger.error(f"Failed to launch Android platform: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'Failed to start Android platform: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f"Error in launch_android: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

# Instance monitoring endpoints
@app.route('/api/instances/status', methods=['GET'])
@jwt_required()
def get_instances_status():
    """Get status of user's isolated instances"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404
        
        # Get user instances using session manager
        instances = session_manager.get_user_instances(current_user_id)
        
        # Update user activity
        user_session = session_manager.get_or_create_session(current_user_id)
        user_session.update_activity()
        
        return jsonify({
            'success': True,
            'instances': instances
        })
        
    except Exception as e:
        logger.error(f"Error getting instances status: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/instances/stop/<platform>', methods=['POST'])
@jwt_required()
def stop_instance(platform):
    """Stop user's isolated instance for specified platform"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404
        
        if platform not in ['ios', 'android']:
            return jsonify({'success': False, 'error': 'Invalid platform'}), 400
        
        # Get user's session for the platform
        user_session = session_manager.get_or_create_session(current_user_id)
        session_id = getattr(user_session, f'{platform}_session_id')
        
        if not session_id:
            return jsonify({'success': False, 'error': f'No active {platform} instance found'}), 404
        
        # Stop the instance
        success = orchestrator.stop_instance(session_id)
        
        if success:
            # Remove from user sessions using session manager
            session_manager.remove_session_instance(current_user_id, platform)
            
            logger.info(f"User {user.email} stopped {platform} instance {session_id}")
            
            return jsonify({
                'success': True,
                'message': f'{platform.capitalize()} instance stopped successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Failed to stop {platform} instance'
            }), 500
        
    except Exception as e:
        logger.error(f"Error stopping {platform} instance: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/dashboard/stats', methods=['GET'])
@require_session_auth
def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        user_id = session.get('user_id')
        
        # Get test suites count
        total_test_suites = TestSuite.query.filter_by(user_id=user_id).count()
        
        # Get active devices (mock data for now)
        active_devices = 0
        
        # Get tests today (mock data for now)
        tests_today = 0
        
        # Calculate success rate (mock data for now)
        success_rate = 95
        
        # Get session statistics
        session_stats = session_manager.get_session_stats()
        user_instances = session_manager.get_user_instances(user_id)
        
        # TODO: Implement actual test statistics
        stats = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'devices_connected': 0,
            'recent_activity': [],
            'session_stats': session_stats,
            'user_instances': len(user_instances),
            'active_instances': user_instances
        }
        
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/admin/session-stats', methods=['GET'])
@jwt_required()
def get_session_stats():
    """Get session management statistics (admin only)"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or not user.is_admin:
            return jsonify({'success': False, 'error': 'Admin access required'}), 403
        
        stats = session_manager.get_session_stats()
        
        return jsonify({
            'success': True,
            'session_stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting session stats: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/admin/cleanup-user/<user_id>', methods=['POST'])
@jwt_required()
def force_cleanup_user(user_id):
    """Force cleanup of a specific user's session (admin only)"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or not user.is_admin:
            return jsonify({'success': False, 'error': 'Admin access required'}), 403
        
        success = session_manager.force_cleanup_user(user_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Successfully cleaned up user {user_id}'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Failed to cleanup user {user_id}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error force cleaning user {user_id}: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/admin/start-cleanup-service', methods=['POST'])
@jwt_required()
def start_cleanup_service():
    """Start the session cleanup service (admin only)"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or not user.is_admin:
            return jsonify({'success': False, 'error': 'Admin access required'}), 403
        
        success = session_manager.start_cleanup_service()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Session cleanup service started successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to start cleanup service'
            }), 500
        
    except Exception as e:
        logger.error(f"Error starting cleanup service: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/api/android/devices/status', methods=['GET'])
@limiter.limit("100 per hour")
def get_android_devices_status():
    """Get Android devices status"""
    # This would normally proxy to the Android backend or check device status
    # For now, return a placeholder response
    return jsonify({
        'status': 'no_devices',
        'devices': [],
        'message': 'Android backend not configured. Please start the Android backend server.'
    })

@app.route('/api/ios/devices/status', methods=['GET'])
@limiter.limit("100 per hour")
def get_ios_devices_status():
    """Get iOS devices status"""
    try:
        # This would integrate with your local device agent
        return jsonify({
            'status': 'success',
            'devices': [],
            'message': 'iOS backend not configured. Please start the iOS backend server.'
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/android/devices', methods=['GET'])
def get_android_devices():
    """Get Android devices"""
    return jsonify({
        'devices': [],
        'message': 'Android backend not configured. Please start the Android backend server.'
    })

@app.route('/api/ios/devices', methods=['GET'])
def get_ios_devices():
    """Get iOS devices"""
    ios_devices = []
    
    # Collect iOS devices from all connected agents
    for agent_id, devices in connected_devices.items():
        for device in devices:
            if device.get('platform', '').lower() == 'ios':
                device_info = device.copy()
                device_info['agent_id'] = agent_id
                ios_devices.append(device_info)
    
    return jsonify({
        'devices': ios_devices,
        'count': len(ios_devices),
        'message': f'Found {len(ios_devices)} iOS devices from connected agents'
    })

if __name__ == '__main__':
    # Development server
    import uuid  # Import here to avoid circular imports
    
    # Create tables if they don't exist
    with app.app_context():
        db.create_all()
        
        # Create default admin user if it doesn't exist
        admin_user = User.query.filter_by(email='<EMAIL>').first()
        if not admin_user:
            admin_user = User(
                id=str(uuid.uuid4()),
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                tenant_id='default',
                is_admin=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: <EMAIL> / admin123")
    
    # Run the application
    socketio.run(
        app,
        host=os.environ.get('SERVER_HOST', '0.0.0.0'),
        port=int(os.environ.get('SERVER_PORT', 8080)),
        debug=os.environ.get('FLASK_ENV') == 'development'
    )
