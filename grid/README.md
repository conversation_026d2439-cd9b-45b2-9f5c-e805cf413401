# Appium Grid Setup

This directory contains the configuration files and scripts for running an Appium Grid setup that enables simultaneous iOS and Android testing with proper session isolation.

## Architecture

- **Grid Hub**: Central coordinator that manages test sessions and routes them to appropriate nodes
- **iOS Node**: Appium server configured for iOS devices (port 4723)
- **Android Node**: Appium server configured for Android devices (port 4724)
- **Grid Console**: Web interface for monitoring the grid status

## Components

### Configuration Files
- `hub-config.json`: Selenium Grid Hub configuration
- `ios-node-config.json`: iOS Appium node configuration
- `android-node-config.json`: Android Appium node configuration

### Scripts
- `start-grid.sh`: Start the complete grid (hub + nodes)
- `stop-grid.sh`: Stop all grid components
- `start-hub.sh`: Start only the hub
- `start-ios-node.sh`: Start only the iOS node
- `start-android-node.sh`: Start only the Android node

## Ports Used

- **Grid Hub**: 4444 (main), 4442 (event bus), 4443 (session map)
- **iOS Appium Node**: 4723
- **Android Appium Node**: 4724
- **iOS App**: 8080 (unchanged)
- **Android App**: 8081 (unchanged)
- **WebDriverAgent**: 8100 (unchanged)

## Benefits

1. **Session Isolation**: Each platform has its own Appium server instance
2. **Parallel Execution**: True simultaneous testing on both platforms
3. **Centralized Management**: Single point of control for all test sessions
4. **Scalability**: Easy to add more nodes for additional devices
5. **Monitoring**: Web console for real-time grid status

## Usage

1. Start the grid: `./start-grid.sh`
2. Start your iOS and Android apps normally
3. Connect devices through the apps - they will automatically route through the grid
4. Monitor grid status at: http://localhost:4444/ui
5. Stop the grid: `./stop-grid.sh`