{"configs": [{"browserName": "safari", "platformName": "iOS", "appium:platformVersion": "18.5", "appium:deviceName": "iPhone", "appium:automationName": "XCUITest", "appium:udid": "00008120-00186C801E13C01E", "maxInstances": 1}], "port": 5557, "host": "127.0.0.1", "hub": "http://127.0.0.1:4444", "session-timeout": 300, "override-max-sessions": true, "max-sessions": 1, "heartbeat-period": 60, "session-request-timeout": 300, "relax-checks": true}