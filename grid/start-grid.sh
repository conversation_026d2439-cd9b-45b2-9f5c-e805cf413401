#!/bin/bash

# Appium Grid Startup Script
# This script starts the Selenium Grid Hub and Appium nodes for iOS and Android

set -e

echo "🚀 Starting Appium Grid Setup..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  Port $port is already in use"
        return 1
    fi
    return 0
}

# Function to kill processes using specific ports
kill_port_processes() {
    local port=$1
    echo "🔪 Checking for processes on port $port..."
    
    # Find PIDs using the port
    local pids=$(lsof -ti :$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "   Found processes using port $port: $pids"
        echo "   Killing processes..."
        
        # Try graceful termination first
        for pid in $pids; do
            if kill -TERM $pid 2>/dev/null; then
                echo "   Sent TERM signal to PID $pid"
            fi
        done
        
        # Wait a moment for graceful shutdown
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti :$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            echo "   Force killing remaining processes..."
            for pid in $remaining_pids; do
                if kill -KILL $pid 2>/dev/null; then
                    echo "   Force killed PID $pid"
                fi
            done
        fi
        
        echo "✅ Port $port is now free"
    else
        echo "✅ Port $port is already free"
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting for $service_name..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Clean up required ports
echo "🧹 Cleaning up required ports..."
ports_to_clean=(4444 4723 4724)
for port in "${ports_to_clean[@]}"; do
    kill_port_processes $port
done

echo "✅ All required ports are now available"

# Check if Selenium Grid is installed
if ! command -v java >/dev/null 2>&1; then
    echo "❌ Java is required but not installed. Please install Java and try again."
    exit 1
fi

# Download Selenium Grid if not present
SELENIUM_JAR="$SCRIPT_DIR/selenium-server-4.15.0.jar"
if [ ! -f "$SELENIUM_JAR" ]; then
    echo "📥 Downloading Selenium Grid..."
    curl -L -o "$SELENIUM_JAR" "https://github.com/SeleniumHQ/selenium/releases/download/selenium-4.15.0/selenium-server-4.15.0.jar"
fi

# Create logs directory
mkdir -p "$SCRIPT_DIR/logs"

echo "🎯 Starting Selenium Grid Hub..."
# Start Selenium Grid Hub
java -jar "$SELENIUM_JAR" hub \
    --config "$SCRIPT_DIR/hub-config.json" \
    > "$SCRIPT_DIR/logs/hub.log" 2>&1 &
HUB_PID=$!
echo "   Hub PID: $HUB_PID"

# Wait for hub to be ready
if ! wait_for_service "http://localhost:4444/wd/hub/status" "Grid Hub"; then
    echo "❌ Failed to start Grid Hub"
    kill $HUB_PID 2>/dev/null || true
    exit 1
fi

echo "📱 Starting iOS Appium Node (port 4723)..."
# Start iOS Appium Node
appium server \
    --port 4723 \
    --log "$SCRIPT_DIR/logs/ios-appium.log" \
    --log-level info \
    --use-drivers xcuitest \
    --allow-insecure chromedriver_autodownload \
    > "$SCRIPT_DIR/logs/ios-node.log" 2>&1 &
iOS_NODE_PID=$!
echo "   iOS Node PID: $iOS_NODE_PID"

# Wait for iOS node to be ready
if ! wait_for_service "http://localhost:4723/status" "iOS Appium Node"; then
    echo "❌ Failed to start iOS Appium Node"
    kill $HUB_PID $iOS_NODE_PID 2>/dev/null || true
    exit 1
fi

echo "🤖 Starting Android Appium Node (port 4724)..."
# Start Android Appium Node
appium server \
    --port 4724 \
    --log "$SCRIPT_DIR/logs/android-appium.log" \
    --log-level info \
    --use-drivers uiautomator2 \
    --allow-insecure chromedriver_autodownload \
    > "$SCRIPT_DIR/logs/android-node.log" 2>&1 &
ANDROID_NODE_PID=$!
echo "   Android Node PID: $ANDROID_NODE_PID"

# Wait for Android node to be ready
if ! wait_for_service "http://localhost:4724/status" "Android Appium Node"; then
    echo "❌ Failed to start Android Appium Node"
    kill $HUB_PID $iOS_NODE_PID $ANDROID_NODE_PID 2>/dev/null || true
    exit 1
fi

# Detect and configure devices
echo "🔍 Detecting connected devices..."
python3 "$SCRIPT_DIR/device_detector.py" "$SCRIPT_DIR"
if [ $? -eq 0 ]; then
    echo "✅ Device detection completed successfully"
else
    echo "⚠️  No devices detected, using default configurations"
fi

# Register nodes with the hub
echo "🔗 Registering nodes with Grid Hub..."
sleep 3

# Register iOS node
echo "   Registering iOS node..."
java -jar "$SELENIUM_JAR" node \
    --config "$SCRIPT_DIR/ios-node-config.json" \
    --service-url "http://127.0.0.1:4723" \
    --port 5557 \
    --detect-drivers false \
    > "$SCRIPT_DIR/logs/ios-registration.log" 2>&1 &
iOS_REG_PID=$!

# Register Android node
echo "   Registering Android node..."
java -jar "$SELENIUM_JAR" node \
    --config "$SCRIPT_DIR/android-node-config.json" \
    --service-url "http://127.0.0.1:4724" \
    --port 5558 \
    --detect-drivers false \
    > "$SCRIPT_DIR/logs/android-registration.log" 2>&1 &
ANDROID_REG_PID=$!

# Save PIDs for cleanup
echo "$HUB_PID" > "$SCRIPT_DIR/hub.pid"
echo "$iOS_NODE_PID" > "$SCRIPT_DIR/ios-node.pid"
echo "$ANDROID_NODE_PID" > "$SCRIPT_DIR/android-node.pid"
echo "$iOS_REG_PID" > "$SCRIPT_DIR/ios-registration.pid"
echo "$ANDROID_REG_PID" > "$SCRIPT_DIR/android-registration.pid"

# Wait a moment for registration to complete
sleep 5

echo ""
echo "✅ Appium Grid is now running!"
echo ""
echo "📊 Grid Console: http://localhost:4444/ui"
echo "🔍 Grid Status: http://localhost:4444/wd/hub/status"
echo "📱 iOS Node: http://localhost:4723/status"
echo "🤖 Android Node: http://localhost:4724/status"
echo ""
echo "📝 Logs are available in: $SCRIPT_DIR/logs/"
echo "🛑 To stop the grid: ./stop-grid.sh"
echo ""
echo "🎉 You can now start your iOS and Android apps - they will automatically connect through the grid!"