#!/bin/bash

# Appium Grid Stop Script
# This script stops all Selenium Grid Hub and Appium node processes

set -e

echo "🛑 Stopping Appium Grid..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to stop process by PID file
stop_process() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "   Stopping $service_name (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            # Wait for graceful shutdown
            sleep 2
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo "   Force stopping $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
        else
            echo "   $service_name process (PID: $pid) not running"
        fi
        rm -f "$pid_file"
    else
        echo "   No PID file found for $service_name"
    fi
}

# Stop all grid components
echo "🔄 Stopping Grid components..."
stop_process "$SCRIPT_DIR/android-registration.pid" "Android Registration"
stop_process "$SCRIPT_DIR/ios-registration.pid" "iOS Registration"
stop_process "$SCRIPT_DIR/android-node.pid" "Android Appium Node"
stop_process "$SCRIPT_DIR/ios-node.pid" "iOS Appium Node"
stop_process "$SCRIPT_DIR/hub.pid" "Grid Hub"

# Additional cleanup - kill any remaining processes
echo "🧹 Performing additional cleanup..."

# Kill any remaining Selenium Grid processes
pkill -f "selenium-server" 2>/dev/null || true

# Kill any remaining Appium processes on our specific ports
for port in 4723 4724 4444; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "   Killing process on port $port..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
    fi
done

# Clean up any remaining Appium processes
pkill -f "appium" 2>/dev/null || true

# Wait a moment for cleanup to complete
sleep 2

# Verify ports are free
echo "🔍 Verifying ports are free..."
for port in 4444 4723 4724; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "   ⚠️  Port $port is still in use"
    else
        echo "   ✅ Port $port is free"
    fi
done

echo ""
echo "✅ Appium Grid has been stopped!"
echo "📝 Log files are preserved in: $SCRIPT_DIR/logs/"
echo "🗑️  To clean up logs: rm -rf $SCRIPT_DIR/logs/"
echo ""