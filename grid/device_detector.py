#!/usr/bin/env python3
"""
Device Detection Script for Appium Grid
This script detects connected iOS and Android devices and updates node configurations.
"""

import json
import subprocess
import sys
import os
from typing import List, Dict, Any

class DeviceDetector:
    def __init__(self, grid_dir: str):
        self.grid_dir = grid_dir
        self.ios_config_path = os.path.join(grid_dir, 'ios-node-config.json')
        self.android_config_path = os.path.join(grid_dir, 'android-node-config.json')
    
    def detect_ios_devices(self) -> List[Dict[str, str]]:
        """Detect connected iOS devices using xcrun simctl and idevice_id"""
        devices = []
        
        try:
            # Check for physical iOS devices
            result = subprocess.run(['idevice_id', '-l'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                for udid in result.stdout.strip().split('\n'):
                    if udid.strip():
                        # Get device info
                        info_result = subprocess.run(['ideviceinfo', '-u', udid, '-k', 'ProductVersion'], 
                                                   capture_output=True, text=True, timeout=10)
                        version = info_result.stdout.strip() if info_result.returncode == 0 else '17.0'
                        
                        name_result = subprocess.run(['ideviceinfo', '-u', udid, '-k', 'DeviceName'], 
                                                   capture_output=True, text=True, timeout=10)
                        name = name_result.stdout.strip() if name_result.returncode == 0 else 'iPhone'
                        
                        devices.append({
                            'udid': udid.strip(),
                            'name': name,
                            'version': version,
                            'type': 'physical'
                        })
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️  idevice tools not found or timeout. Skipping physical iOS devices.")
        
        try:
            # Check for iOS simulators
            result = subprocess.run(['xcrun', 'simctl', 'list', 'devices', 'available', '--json'], 
                                  capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                sim_data = json.loads(result.stdout)
                for runtime, device_list in sim_data.get('devices', {}).items():
                    if 'iOS' in runtime:
                        version = runtime.split('-')[-1].replace('-', '.')
                        for device in device_list:
                            if device.get('state') == 'Booted':
                                devices.append({
                                    'udid': device['udid'],
                                    'name': device['name'],
                                    'version': version,
                                    'type': 'simulator'
                                })
        except (subprocess.TimeoutExpired, FileNotFoundError, json.JSONDecodeError):
            print("⚠️  xcrun simctl not available or timeout. Skipping iOS simulators.")
        
        return devices
    
    def detect_android_devices(self) -> List[Dict[str, str]]:
        """Detect connected Android devices using adb"""
        devices = []
        
        try:
            # Check for Android devices
            result = subprocess.run(['adb', 'devices'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if '\tdevice' in line:
                        udid = line.split('\t')[0]
                        
                        # Get device info
                        version_result = subprocess.run(['adb', '-s', udid, 'shell', 'getprop', 'ro.build.version.release'], 
                                                       capture_output=True, text=True, timeout=10)
                        version = version_result.stdout.strip() if version_result.returncode == 0 else '13.0'
                        
                        model_result = subprocess.run(['adb', '-s', udid, 'shell', 'getprop', 'ro.product.model'], 
                                                     capture_output=True, text=True, timeout=10)
                        model = model_result.stdout.strip() if model_result.returncode == 0 else 'Android Device'
                        
                        devices.append({
                            'udid': udid,
                            'name': model,
                            'version': version,
                            'type': 'physical'
                        })
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️  adb not found or timeout. Skipping Android devices.")
        
        return devices
    
    def update_ios_config(self, devices: List[Dict[str, str]]) -> bool:
        """Update iOS node configuration with detected devices"""
        try:
            with open(self.ios_config_path, 'r') as f:
                config = json.load(f)
            
            if devices:
                # Create configs for each device
                config['configs'] = []
                for device in devices:
                    device_config = {
                        "browserName": "safari",
                        "platformName": "iOS",
                        "appium:platformVersion": device['version'],
                        "appium:deviceName": device['name'],
                        "appium:automationName": "XCUITest",
                        "appium:udid": device['udid'],
                        "maxInstances": 1
                    }
                    config['configs'].append(device_config)
                
                config['max-sessions'] = len(devices)
            else:
                # Keep default config if no devices found
                if not config.get('configs'):
                    config['configs'] = [{
                        "browserName": "safari",
                        "platformName": "iOS",
                        "appium:platformVersion": "17.0",
                        "appium:deviceName": "iPhone",
                        "appium:automationName": "XCUITest",
                        "appium:udid": "",
                        "maxInstances": 5
                    }]
            
            with open(self.ios_config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            return True
        except Exception as e:
            print(f"❌ Error updating iOS config: {e}")
            return False
    
    def update_android_config(self, devices: List[Dict[str, str]]) -> bool:
        """Update Android node configuration with detected devices"""
        try:
            with open(self.android_config_path, 'r') as f:
                config = json.load(f)
            
            if devices:
                # Create configs for each device
                config['configs'] = []
                for device in devices:
                    device_config = {
                        "browserName": "chrome",
                        "platformName": "Android",
                        "appium:platformVersion": device['version'],
                        "appium:deviceName": device['name'],
                        "appium:automationName": "UiAutomator2",
                        "appium:udid": device['udid'],
                        "maxInstances": 1
                    }
                    config['configs'].append(device_config)
                
                config['max-sessions'] = len(devices)
            else:
                # Keep default config if no devices found
                if not config.get('configs'):
                    config['configs'] = [{
                        "browserName": "chrome",
                        "platformName": "Android",
                        "appium:platformVersion": "13.0",
                        "appium:deviceName": "Android Device",
                        "appium:automationName": "UiAutomator2",
                        "appium:udid": "",
                        "maxInstances": 5
                    }]
            
            with open(self.android_config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            return True
        except Exception as e:
            print(f"❌ Error updating Android config: {e}")
            return False
    
    def detect_and_update(self) -> Dict[str, Any]:
        """Detect all devices and update configurations"""
        print("🔍 Detecting connected devices...")
        
        ios_devices = self.detect_ios_devices()
        android_devices = self.detect_android_devices()
        
        print(f"📱 Found {len(ios_devices)} iOS device(s)")
        for device in ios_devices:
            print(f"   - {device['name']} ({device['version']}) [{device['type']}] - {device['udid'][:8]}...")
        
        print(f"🤖 Found {len(android_devices)} Android device(s)")
        for device in android_devices:
            print(f"   - {device['name']} ({device['version']}) [{device['type']}] - {device['udid'][:8]}...")
        
        ios_updated = self.update_ios_config(ios_devices)
        android_updated = self.update_android_config(android_devices)
        
        return {
            'ios_devices': ios_devices,
            'android_devices': android_devices,
            'ios_config_updated': ios_updated,
            'android_config_updated': android_updated,
            'total_devices': len(ios_devices) + len(android_devices)
        }

def main():
    if len(sys.argv) > 1:
        grid_dir = sys.argv[1]
    else:
        grid_dir = os.path.dirname(os.path.abspath(__file__))
    
    detector = DeviceDetector(grid_dir)
    result = detector.detect_and_update()
    
    if result['total_devices'] > 0:
        print(f"\n✅ Device detection complete! Found {result['total_devices']} device(s)")
        print("📝 Node configurations updated successfully")
    else:
        print("\n⚠️  No devices detected. Using default configurations.")
        print("💡 Make sure devices are connected and authorized for debugging.")
    
    return 0 if result['total_devices'] > 0 else 1

if __name__ == '__main__':
    sys.exit(main())