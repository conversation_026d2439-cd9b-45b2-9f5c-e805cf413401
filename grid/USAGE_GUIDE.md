# Appium Grid Usage Guide

This guide explains how to use the Appium Grid setup for simultaneous iOS and Android testing with session isolation.

## Quick Start

### 1. Start the Appium Grid

```bash
# Navigate to the grid directory
cd grid

# Start the Grid (Hub + iOS Node + Android Node)
./start-grid.sh
```

The script will:
- Download Selenium Grid if not present
- Start the Grid Hub on port 4444
- Start iOS Appium Node on port 4723
- Start Android Appium Node on port 4724
- Register nodes with the Hub

### 2. Verify Grid Setup

```bash
# Run the test script to verify everything is working
python test_grid_setup.py
```

### 3. Start Your Apps

```bash
# Terminal 1: Start iOS app (will route through Grid)
python run.py

# Terminal 2: Start Android app (will route through Grid)
python run_android.py
```

### 4. Monitor the Grid

Open your browser and go to: http://localhost:4444

This shows:
- Active sessions
- Available nodes
- Node capabilities
- Session distribution

## Environment Configuration

### Enable/Disable Grid Mode

```bash
# Enable Grid mode (default when Grid is running)
export APPIUM_GRID_ENABLED=true

# Disable Grid mode (use direct connections)
export APPIUM_GRID_ENABLED=false
```

### Using GridConfig Module

```python
from grid_config import GridConfig

# Get connection URL (automatically chooses Grid or direct)
ios_url = GridConfig.get_connection_url('ios')
android_url = GridConfig.get_connection_url('android')

# Check if Grid is available
if GridConfig.is_grid_available():
    print("Using Appium Grid")
else:
    print("Using direct connections")

# Enable/disable Grid programmatically
GridConfig.enable_grid()
GridConfig.disable_grid()
```

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐
│   iOS App       │    │  Android App    │
│   (Port 8080)   │    │  (Port 8081)    │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
            ┌────────▼────────┐
            │   Grid Hub      │
            │  (Port 4444)    │
            └────────┬────────┘
                     │
        ┌────────────┼────────────┐
        │            │            │
┌───────▼──────┐ ┌───▼──────┐ ┌──▼──────────┐
│  iOS Node    │ │Android   │ │   Future    │
│ (Port 4723)  │ │Node      │ │   Nodes     │
│              │ │(Port     │ │             │
│ XCUITest     │ │4724)     │ │             │
│ Driver       │ │          │ │             │
│              │ │UiAuto2   │ │             │
└──────────────┘ │Driver    │ └─────────────┘
                 └──────────┘
```

## Port Configuration

| Service | Port | Purpose |
|---------|------|----------|
| Grid Hub | 4444 | Central coordination |
| iOS Node | 4723 | iOS device automation |
| Android Node | 4724 | Android device automation |
| iOS App | 8080 | iOS app web interface |
| Android App | 8081 | Android app web interface |

## Session Management

### How Sessions Work

1. **App starts**: Requests session from Grid Hub
2. **Hub routes**: Directs to appropriate node (iOS/Android)
3. **Node handles**: Creates Appium driver for specific device
4. **Isolation**: Each session is completely isolated
5. **Cleanup**: Sessions automatically cleaned up when app stops

### Session Capabilities

The Grid automatically handles platform-specific capabilities:

**iOS Sessions:**
```json
{
  "platformName": "iOS",
  "automationName": "XCUITest",
  "deviceName": "iPhone",
  "udid": "device-uuid"
}
```

**Android Sessions:**
```json
{
  "platformName": "Android",
  "automationName": "UiAutomator2",
  "deviceName": "Android Device",
  "udid": "device-id"
}
```

## Troubleshooting

### Grid Not Starting

```bash
# Check if Java is installed
java -version

# Check if ports are available
lsof -i :4444
lsof -i :4723
lsof -i :4724

# Stop any conflicting processes
./stop-grid.sh
```

### Apps Not Connecting to Grid

1. **Check Grid status:**
   ```bash
   curl http://localhost:4444/status
   ```

2. **Verify nodes are registered:**
   - Open http://localhost:4444
   - Check if iOS and Android nodes appear

3. **Check app logs:**
   - Look for "Using connection URL" messages
   - Verify Grid URL is being used

### Session Conflicts

1. **Check active sessions:**
   - Open Grid console: http://localhost:4444
   - Look for stuck sessions

2. **Clean up sessions:**
   ```bash
   # Restart the Grid
   ./stop-grid.sh
   ./start-grid.sh
   ```

### Performance Issues

1. **Increase node capacity:**
   - Edit `ios-node-config.json` and `android-node-config.json`
   - Increase `maxInstances` values

2. **Monitor resource usage:**
   ```bash
   # Check CPU and memory
   top -p $(pgrep -f selenium-server)
   ```

## Advanced Configuration

### Custom Node Configuration

Edit the node configuration files:

**ios-node-config.json:**
```json
{
  "capabilities": [
    {
      "stereotype": {
        "platformName": "iOS",
        "automationName": "XCUITest"
      },
      "maxInstances": 5
    }
  ]
}
```

**android-node-config.json:**
```json
{
  "capabilities": [
    {
      "stereotype": {
        "platformName": "Android",
        "automationName": "UiAutomator2"
      },
      "maxInstances": 5
    }
  ]
}
```

### Adding More Nodes

1. **Create new node config:**
   ```bash
   cp ios-node-config.json ios-node-2-config.json
   ```

2. **Update port and capabilities:**
   ```json
   {
     "port": 4725,
     "hub": "http://localhost:4444"
   }
   ```

3. **Start additional node:**
   ```bash
   appium --port 4725 --config-file ios-node-2-config.json
   ```

### Environment-Specific Settings

```bash
# Development environment
export APPIUM_GRID_ENABLED=true
export GRID_HUB_URL=http://localhost:4444

# CI/CD environment
export APPIUM_GRID_ENABLED=true
export GRID_HUB_URL=http://grid-hub:4444

# Local testing (no Grid)
export APPIUM_GRID_ENABLED=false
```

## Best Practices

### 1. Resource Management
- Monitor Grid resource usage
- Limit concurrent sessions based on hardware
- Clean up stuck sessions regularly

### 2. Session Isolation
- Each test should create its own session
- Avoid sharing sessions between tests
- Use unique device identifiers

### 3. Error Handling
- Implement retry logic for session creation
- Handle Grid unavailability gracefully
- Log session details for debugging

### 4. Monitoring
- Use Grid console for real-time monitoring
- Set up alerts for node failures
- Track session success rates

## Integration Examples

### Test Framework Integration

```python
import pytest
from grid_config import GridConfig
from appium import webdriver

@pytest.fixture
def ios_driver():
    options = AppiumOptions()
    options.set_capability('platformName', 'iOS')
    
    driver = webdriver.Remote(
        command_executor=GridConfig.get_connection_url('ios'),
        options=options
    )
    yield driver
    driver.quit()

@pytest.fixture
def android_driver():
    options = AppiumOptions()
    options.set_capability('platformName', 'Android')
    
    driver = webdriver.Remote(
        command_executor=GridConfig.get_connection_url('android'),
        options=options
    )
    yield driver
    driver.quit()
```

### Parallel Test Execution

```python
import concurrent.futures
from grid_config import GridConfig

def run_ios_test():
    # iOS test logic
    pass

def run_android_test():
    # Android test logic
    pass

# Run tests in parallel
with concurrent.futures.ThreadPoolExecutor() as executor:
    ios_future = executor.submit(run_ios_test)
    android_future = executor.submit(run_android_test)
    
    # Wait for both to complete
    ios_result = ios_future.result()
    android_result = android_future.result()
```

## Stopping the Grid

```bash
# Stop all Grid components
./stop-grid.sh

# Verify all processes are stopped
ps aux | grep selenium-server
ps aux | grep appium
```

The stop script will:
- Kill Grid Hub and Node processes
- Clean up PID files
- Verify ports are free
- Provide cleanup confirmation