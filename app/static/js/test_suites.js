class TestSuitesManager {
    constructor() {
        this.selectedTestCases = [];  // Changed from Set to Array to maintain order
        this.allTestCases = [];  // Store all test cases for the selector
        this.sortable = null;  // Reference to Sortable instance
        this.initializeEventListeners();
        this.loadTestCases();
        this.loadTestSuites();
    }

    /**
     * Auto-save functionality:
     * - Automatically saves test suite changes when test cases are added/removed/reordered
     * - Saves changes when name or description fields are modified (with 1-second debounce)
     * - Only works when editing an existing test suite (not during creation)
     * - Shows visual feedback for save status
     */

    initializeEventListeners() {
        // Save Test Suite button click handler
        document.getElementById('saveTestSuiteBtn').addEventListener('click', () => this.saveTestSuite());

        // Auto-save when name or description changes (with debouncing)
        this.setupAutoSaveOnFieldChange();

        // Test case checkbox change handler (keep for backward compatibility)
        document.getElementById('availableTestCases').addEventListener('change', async (e) => {
            if (e.target.type === 'checkbox') {
                const testCaseId = e.target.value;
                if (e.target.checked) {
                    if (!this.selectedTestCases.includes(testCaseId)) {
                        this.selectedTestCases.push(testCaseId);
                    }
                } else {
                    this.selectedTestCases = this.selectedTestCases.filter(id => id !== testCaseId);
                }
                this.updateSelectedTestCasesList();

                // Auto-save the changes if we're in edit mode
                await this.autoSaveTestSuiteChanges();
            }
        });

        // Add test case button click handler
        const addTestCaseBtn = document.getElementById('addTestCaseBtn');
        if (addTestCaseBtn) {
            addTestCaseBtn.addEventListener('click', () => {
                const selector = document.getElementById('testCaseSelector');
                const selectedValue = selector.value;

                if (selectedValue && !this.selectedTestCases.includes(selectedValue)) {
                    this.selectedTestCases.push(selectedValue);
                    this.updateSelectedTestCasesList();

                    // Reset the selector
                    selector.value = '';
                }
            });
        }
    }

    async loadTestCases() {
        try {
            const response = await fetch('/api/recording/list');
            const data = await response.json();

            if (data.status === 'success') {
                this.allTestCases = data.test_cases; // Store all test cases
                this.displayTestCases(data.test_cases);
                this.populateTestCaseSelector(data.test_cases);
            } else {
                console.error('Failed to load test cases:', data.error);
                this.showError('Failed to load test cases');
            }
        } catch (error) {
            console.error('Error loading test cases:', error);
            this.showError('Error loading test cases');
        }
    }

    displayTestCases(testCases) {
        const container = document.getElementById('availableTestCases');
        container.innerHTML = '';

        testCases.forEach(testCase => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

            // Create label badges HTML if labels exist
            const labelsHtml = testCase.labels && testCase.labels.length > 0 
                ? testCase.labels.map(label => 
                    `<span class="badge bg-secondary me-1">${label}</span>`
                ).join('') 
                : '';

            listItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="form-check">
                        <input class="form-check-input me-3" type="checkbox" value="${testCase.filename}" id="testCase_${testCase.filename}">
                        <label class="form-check-label" for="testCase_${testCase.filename}">
                            <span class="fw-bold">${testCase.name}</span>
                            <div class="mt-1">${labelsHtml}</div>
                        </label>
                    </div>
                </div>
                <span class="badge bg-light text-dark">Created: ${(() => {
                    try {
                        const date = new Date(testCase.created);
                        return isNaN(date.getTime()) ? 'Unknown' : date.toLocaleDateString();
                    } catch (error) {
                        return 'Unknown';
                    }
                })()}</span>
            `;

            container.appendChild(listItem);
        });
    }

    populateTestCaseSelector(testCases) {
        const selector = document.getElementById('testCaseSelector');
        if (!selector) return;

        // Clear existing options except the first placeholder
        while (selector.options.length > 1) {
            selector.remove(1);
        }

        // Sort test cases by name for easier selection
        const sortedTestCases = [...testCases].sort((a, b) =>
            (a.name || '').localeCompare(b.name || '')
        );

        // Add options for each test case
        sortedTestCases.forEach(testCase => {
            const option = document.createElement('option');
            option.value = testCase.filename;
            option.textContent = testCase.name;
            selector.appendChild(option);
        });
    }

    updateSelectedTestCasesList() {
        const container = document.getElementById('selectedTestCases');

        // Destroy existing Sortable instance if it exists
        if (this.sortable) {
            this.sortable.destroy();
            this.sortable = null;
        }

        container.innerHTML = '';

        this.selectedTestCases.forEach(testCaseId => {
            // Find test case info from our stored test cases
            const testCaseInfo = this.allTestCases.find(tc => tc.filename === testCaseId);

            const div = document.createElement('div');
            div.className = 'list-group-item d-flex justify-content-between align-items-center';
            div.dataset.testCaseId = testCaseId;

            // If we found the test case in our list, use its name
            if (testCaseInfo) {
                div.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grip-vertical drag-handle me-2" style="cursor: grab;"></i>
                        <span>${testCaseInfo.name || 'Unknown Test Case'}</span>
                        ${testCaseInfo.labels && testCaseInfo.labels.length > 0 ? 
                            `<div class="ms-2">
                                ${testCaseInfo.labels.map(label => 
                                    `<span class="badge bg-secondary me-1">${label}</span>`
                                ).join('')}
                            </div>` : ''}
                    </div>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="testSuitesManager.editTestCase('${testCaseId}')" title="Edit Test Case">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="testSuitesManager.removeTestCase('${testCaseId}')" title="Remove Test Case">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;
            } else {
                // Fallback for test cases not found in the list
                const checkbox = document.getElementById(`testCase_${testCaseId}`);
                let displayName;

                if (checkbox && checkbox.nextElementSibling) {
                    // Try to extract just the test case name, not the button text
                    const labelElement = checkbox.nextElementSibling;

                    // Look for the test case name in the h6 element or similar
                    const nameElement = labelElement.querySelector('h6, .test-case-name, .test-case-info h6');
                    if (nameElement) {
                        displayName = nameElement.textContent.trim();
                    } else {
                        // If no specific name element found, try to extract from the beginning of the text
                        const fullText = labelElement.textContent.trim();
                        // Remove button text patterns and action counts
                        displayName = fullText
                            .replace(/\s+(Retry|Stop|Remove)\s*/gi, ' ')
                            .replace(/\s+\d+\s+actions?\s*$/i, '')
                            .replace(/\s+/g, ' ')
                            .trim();
                    }
                } else {
                    // Extract a simple name from the filename if possible
                    displayName = testCaseId.split('_')[0] || testCaseId;
                }

                div.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grip-vertical drag-handle me-2" style="cursor: grab;"></i>
                        <span>${displayName} <small class="text-muted">(ID: ${testCaseId})</small></span>
                    </div>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="testSuitesManager.editTestCase('${testCaseId}')" title="Edit Test Case">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="testSuitesManager.removeTestCase('${testCaseId}')" title="Remove Test Case">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;
            }

            container.appendChild(div);
        });

        // Initialize Sortable on the container
        if (container.children.length > 0) {
            this.initSortable(container);
        }

        // Update checkboxes to match selected state
        document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = this.selectedTestCases.includes(checkbox.value);
        });
    }

    initSortable(container) {
        // Make sure Sortable.js is available
        if (typeof Sortable !== 'undefined') {
            this.sortable = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',

                // Update the array when items are reordered
                onEnd: async (evt) => {
                    // Get the new order of test cases
                    const items = Array.from(container.children);
                    this.selectedTestCases = items.map(item => item.dataset.testCaseId);

                    // Auto-save the changes if we're in edit mode
                    await this.autoSaveTestSuiteChanges();
                }
            });
        } else {
            console.error('Sortable.js is not loaded. Drag and drop functionality will not work.');
        }
    }

    async removeTestCase(testCaseId) {
        // Remove from array
        this.selectedTestCases = this.selectedTestCases.filter(id => id !== testCaseId);

        // Uncheck the checkbox if it exists
        const checkbox = document.getElementById(`testCase_${testCaseId}`);
        if (checkbox) {
            checkbox.checked = false;
        }

        // Update the UI
        this.updateSelectedTestCasesList();

        // Auto-save the changes if we're in edit mode
        await this.autoSaveTestSuiteChanges();
    }

    async editTestCase(testCaseId) {
        try {
            // Find test case info from our stored test cases
            const testCaseInfo = this.allTestCases.find(tc => tc.filename === testCaseId);
            const testCaseName = testCaseInfo ? testCaseInfo.name : testCaseId;
            
            // Show the edit modal
            this.showEditTestCaseModal(testCaseId, testCaseName);
        } catch (error) {
            console.error('Error opening test case editor:', error);
            this.showError('Failed to open test case editor: ' + error.message);
        }
    }

    showEditTestCaseModal(testCaseId, testCaseName) {
        // Create modal HTML if it doesn't exist
        let modal = document.getElementById('editTestCaseModal');
        if (!modal) {
            this.createEditTestCaseModal();
            modal = document.getElementById('editTestCaseModal');
        }

        // Set modal title
        const modalTitle = modal.querySelector('.modal-title');
        modalTitle.innerHTML = `<i class="bi bi-pencil"></i> Edit Test Case: ${testCaseName}`;

        // Store test case ID for later use
        modal.dataset.testCaseId = testCaseId;

        // Load test case data
        this.loadTestCaseForEditing(testCaseId);

        // Show the modal
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    createEditTestCaseModal() {
        const modalHTML = `
            <div class="modal fade" id="editTestCaseModal" tabindex="-1" aria-labelledby="editTestCaseModalLabel" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="editTestCaseModalLabel">
                                <i class="bi bi-pencil"></i> Edit Test Case
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="row g-0" style="height: 70vh;">
                                <!-- Action Builder Section -->
                                <div class="col-md-6 border-end">
                                    <div class="p-3">
                                        <h6 class="mb-3"><i class="bi bi-tools"></i> Action Builder</h6>
                                        <div id="editActionBuilder">
                                            <!-- Action Builder will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                                <!-- Action List Section -->
                                <div class="col-md-6">
                                    <div class="p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0"><i class="bi bi-list-ol"></i> Actions List</h6>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button id="editClearAllBtn" class="btn btn-outline-danger" title="Clear All Actions">
                                                    <i class="bi bi-trash"></i> Clear All
                                                </button>
                                            </div>
                                        </div>
                                        <div id="editActionsList" class="list-group" style="max-height: 60vh; overflow-y: auto;">
                                            <!-- Actions will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> Cancel
                            </button>
                            <button type="button" class="btn btn-primary" id="saveEditedTestCaseBtn">
                                <i class="bi bi-check-circle"></i> Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add event listeners
        this.setupEditModalEventListeners();
    }

    setupEditModalEventListeners() {
        const modal = document.getElementById('editTestCaseModal');
        const saveBtn = document.getElementById('saveEditedTestCaseBtn');
        const clearAllBtn = document.getElementById('editClearAllBtn');

        // Save button event listener
        saveBtn.addEventListener('click', () => {
            this.saveEditedTestCase();
        });

        // Clear all button event listener
        clearAllBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all actions? This cannot be undone.')) {
                this.clearEditActions();
            }
        });
    }

    async loadTestCaseForEditing(testCaseId) {
        try {
            // Show loading state
            const actionsList = document.getElementById('editActionsList');
            actionsList.innerHTML = '<div class="text-center p-3"><div class="spinner-border" role="status"></div><p class="mt-2">Loading test case...</p></div>';

            // Load test case data from server
            const response = await fetch(`/api/test_cases/load/${testCaseId}`);
            const data = await response.json();

            if (data.status === 'success') {
                // Store the loaded actions for editing
                this.editingActions = data.actions || [];
                
                // Populate the actions list
                this.populateEditActionsList();
                
                // Initialize the action builder for editing
                this.initializeEditActionBuilder();
                
                // Setup action builder event listeners
                this.setupEditActionBuilderListeners();
            } else {
                throw new Error(data.error || 'Failed to load test case');
            }
        } catch (error) {
            console.error('Error loading test case for editing:', error);
            const actionsList = document.getElementById('editActionsList');
            actionsList.innerHTML = `<div class="text-center p-3 text-danger"><i class="bi bi-exclamation-triangle"></i><p class="mt-2">Failed to load test case: ${error.message}</p></div>`;
        }
    }

    populateEditActionsList() {
        const actionsList = document.getElementById('editActionsList');
        
        if (!this.editingActions || this.editingActions.length === 0) {
            actionsList.innerHTML = '<div class="text-center p-3 text-muted"><i class="bi bi-info-circle"></i><p class="mt-2">No actions in this test case</p></div>';
            return;
        }

        actionsList.innerHTML = '';
        
        this.editingActions.forEach((action, index) => {
            const actionItem = this.createEditActionItem(action, index);
            actionsList.appendChild(actionItem);
        });
    }

    createEditActionItem(action, index) {
        const div = document.createElement('div');
        div.className = 'list-group-item d-flex justify-content-between align-items-center';
        div.dataset.actionIndex = index;

        const actionText = this.getActionDisplayText(action);
        
        div.innerHTML = `
            <div class="d-flex align-items-center flex-grow-1">
                <span class="badge bg-secondary me-2">${index + 1}</span>
                <span class="action-text">${actionText}</span>
            </div>
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="testSuitesManager.editAction(${index})" title="Edit Action">
                    <i class="bi bi-pencil"></i>
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="testSuitesManager.removeEditAction(${index})" title="Remove Action">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        return div;
    }

    getActionDisplayText(action) {
        switch (action.action) {
            case 'tap':
                if (action.x !== undefined && action.y !== undefined) {
                    return `Tap at (${action.x}, ${action.y})`;
                } else if (action.image_path) {
                    return `Tap on image: ${action.image_path}`;
                } else if (action.locator_value) {
                    return `Tap on ${action.locator_type}: ${action.locator_value}`;
                }
                return 'Tap action';
            case 'doubleTap':
                if (action.x !== undefined && action.y !== undefined) {
                    return `Double Tap at (${action.x}, ${action.y})`;
                } else if (action.image_path) {
                    return `Double Tap on image: ${action.image_path}`;
                } else if (action.locator_value) {
                    return `Double Tap on ${action.locator_type}: ${action.locator_value}`;
                }
                return 'Double Tap action';
            case 'swipe':
                return `Swipe ${action.direction}`;
            case 'text':
            case 'tapAndType':
                return `Type: "${action.text}"`;
            case 'sendKeys':
                return `Send Keys: "${action.text}" to ${action.locator_type}(${action.locator_value})`;
            case 'getValue':
                return `Get Value from ${action.locator_type}: ${action.locator_value} → ${action.variable_name}`;
            case 'compareValue':
                return `Compare Value: ${action.locator_type}(${action.locator_value}) == "${action.expected_value}"`;
            case 'ifExists':
                return `If Exists: ${action.locator_type}(${action.locator_value})`;
            case 'ifVisible':
                return `If Visible: ${action.locator_type}(${action.locator_value})`;
            case 'ifContainsText':
                return `If Contains Text: ${action.locator_type}(${action.locator_value}) contains "${action.expected_text}"`;
            case 'ifValueEquals':
                return `If Value Equals: ${action.locator_type}(${action.locator_value}) == "${action.expected_value}"`;
            case 'ifValueContains':
                return `If Value Contains: ${action.locator_type}(${action.locator_value}) contains "${action.expected_text}"`;
            case 'ifHasAttribute':
                return `If Has Attribute: ${action.locator_type}(${action.locator_value}) has "${action.attribute_name}"`;
            case 'launchApp':
                return `Launch App: ${action.package_name}`;
            case 'takeScreenshot':
                return `Take Screenshot${action.filename ? ': ' + action.filename : ''}`;
            case 'wait':
                return `Wait ${action.duration}s`;
            case 'info':
                return `Info: "${action.message}"`;
            case 'click_element':
                return `Click element: ${action.locator || action.image || 'Unknown'}`;
            default:
                return `${action.action} action`;
        }
    }

    initializeEditActionBuilder() {
        const actionBuilder = document.getElementById('editActionBuilder');
        actionBuilder.innerHTML = `
            <div class="mb-3">
                <label for="editActionType" class="form-label">Action Type</label>
                <select id="editActionType" class="form-select">
                    <option value="">Select an action</option>
                    <option value="tap">Tap</option>
                    <option value="doubleTap">Double Tap</option>
                    <option value="swipe">Swipe</option>
                    <option value="text">Input Text</option>
                    <option value="sendKeys">Send Keys</option>
                    <option value="tapAndType">Tap and Type (iOS)</option>
                    <option value="tapOnText">Tap on Text</option>
                    <option value="tapIfImageExists">Tap If Image Exists</option>
                    <option value="tapIfLocatorExists">Tap If Locator Exists</option>
                    <option value="tapIfTextExists">Tap If Text Exists</option>
                    <option value="getValue">Get Value</option>
                    <option value="compareValue">Compare Value</option>
                    <option value="ifExists">If Exists</option>
                    <option value="ifVisible">If Visible</option>
                    <option value="ifContainsText">If Contains Text</option>
                    <option value="ifValueEquals">If Value Equals</option>
                    <option value="ifValueContains">If Value Contains</option>
                    <option value="ifHasAttribute">If Has Attribute</option>
                    <option value="launchApp">Launch App</option>
                    <option value="takeScreenshot">Take Screenshot</option>
                    <option value="wait">Wait</option>
                    <option value="deviceBack">Device Back (Android Only)</option>
                    <option value="getParam">Get Parameter</option>
                    <option value="setParam">Set Parameter</option>
                    <option value="restartApp">Restart App</option>
                    <option value="terminateApp">Terminate App</option>
                    <option value="uninstallApp">Uninstall App</option>
                    <option value="swipeTillVisible">Swipe Till Visible</option>
                    <option value="waitTill">Wait Till Element</option>
                    <option value="exists">Check If Exists</option>
                    <option value="textClear">Clear & Input Text</option>
                    <option value="iosFunctions">iOS Functions</option>
                    <option value="ifThenSteps">If Then Steps</option>
                    <option value="multiStep">Multi Step</option>
                    <option value="repeatSteps">Repeat Steps</option>
                    <option value="hookAction">Hook Action</option>
                    <option value="cleanupSteps">Cleanup Steps</option>
                    <option value="info">INFO</option>
                </select>
            </div>

            <div id="editActionForms">
                <!-- Tap Action Form with Tabs -->
                <div id="editTapActionForm" class="action-form d-none">
                    <ul class="nav nav-tabs mb-3" id="editTapActionTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="edit-tap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#edit-tap-coordinates" type="button" role="tab" aria-controls="edit-tap-coordinates" aria-selected="true">Coordinates</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-tap-image-tab" data-bs-toggle="tab" data-bs-target="#edit-tap-image" type="button" role="tab" aria-controls="edit-tap-image" aria-selected="false">Image</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-tap-locator-tab" data-bs-toggle="tab" data-bs-target="#edit-tap-locator" type="button" role="tab" aria-controls="edit-tap-locator" aria-selected="false">Locator</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="editTapActionTabContent">
                        <!-- Coordinates Tab -->
                        <div class="tab-pane fade show active" id="edit-tap-coordinates" role="tabpanel" aria-labelledby="edit-tap-coordinates-tab">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>X Coordinate</label>
                                        <input type="number" id="editTapX" class="form-control" value="0">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>Y Coordinate</label>
                                        <input type="number" id="editTapY" class="form-control" value="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Image Tab -->
                        <div class="tab-pane fade" id="edit-tap-image" role="tabpanel" aria-labelledby="edit-tap-image-tab">
                            <div class="form-group">
                                <label class="form-label">Image:</label>
                                <div class="input-group">
                                    <select class="form-select" id="editTapImageFilename">
                                        <option value="">Select...</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" id="editRefreshTapImages">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Threshold:</label>
                                        <input type="number" class="form-control" id="editTapThreshold" value="0.7" min="0" max="1" step="0.05">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Timeout (s):</label>
                                        <input type="number" class="form-control" id="editTapTimeout" value="20" min="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Locator Tab -->
                        <div class="tab-pane fade" id="edit-tap-locator" role="tabpanel" aria-labelledby="edit-tap-locator-tab">
                            <div class="form-group">
                                <label>Type</label>
                                <select id="editTapLocatorType" class="form-control">
                                    <option value="id">ID</option>
                                    <option value="xpath">XPath</option>
                                    <option value="accessibility_id">Accessibility ID</option>
                                    <option value="uiselector">UI Selector</option>
                                    <option value="class">Class Name</option>
                                    <option value="name">Name</option>
                                    <option value="text">Text</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Value</label>
                                <input type="text" id="editTapLocatorValue" class="form-control" placeholder="Enter locator value">
                            </div>
                            <div class="form-group">
                                <label>Timeout (s)</label>
                                <input type="number" id="editTapLocatorTimeout" class="form-control" value="10">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Text Input Action Form -->
                <div id="editTextActionForm" class="action-form d-none">
                    <div class="form-group mb-3">
                        <label>Text to Input</label>
                        <input type="text" id="editTextInput" class="form-control" placeholder="Enter text">
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>X Coordinate</label>
                                <input type="number" id="editTextX" class="form-control" value="0">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Y Coordinate</label>
                                <input type="number" id="editTextY" class="form-control" value="0">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Send Keys Action Form -->
                <div id="editSendKeysActionForm" class="action-form d-none">
                    <div class="form-group mb-3">
                        <label>Text to Send</label>
                        <div class="input-group">
                            <input type="text" id="editSendKeysText" class="form-control" placeholder="Enter text to send">
                            <button class="btn btn-outline-secondary" type="button" id="editGenerateRandomText">
                                <i class="bi bi-shuffle"></i> Random
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editSendKeysLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="uiselector">UI Selector</option>
                            <option value="class">Class Name</option>
                            <option value="name">Name</option>
                            <option value="text">Text</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editSendKeysLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                    <div class="form-group">
                        <label>Timeout (s)</label>
                        <input type="number" id="editSendKeysTimeout" class="form-control" value="10">
                    </div>
                </div>

                <!-- Wait Action Form -->
                <div id="editWaitActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Wait Duration (seconds)</label>
                        <input type="number" id="editWaitDuration" class="form-control" value="1" min="0.1" step="0.1">
                    </div>
                </div>

                <!-- Double Tap Action Form with Tabs -->
                <div id="editDoubleTapActionForm" class="action-form d-none">
                    <ul class="nav nav-tabs mb-3" id="editDoubleTapActionTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="edit-doubletap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#edit-doubletap-coordinates" type="button" role="tab" aria-controls="edit-doubletap-coordinates" aria-selected="true">Coordinates</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-doubletap-image-tab" data-bs-toggle="tab" data-bs-target="#edit-doubletap-image" type="button" role="tab" aria-controls="edit-doubletap-image" aria-selected="false">Image</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-doubletap-locator-tab" data-bs-toggle="tab" data-bs-target="#edit-doubletap-locator" type="button" role="tab" aria-controls="edit-doubletap-locator" aria-selected="false">Locator</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="editDoubleTapActionTabContent">
                        <!-- Coordinates Tab -->
                        <div class="tab-pane fade show active" id="edit-doubletap-coordinates" role="tabpanel" aria-labelledby="edit-doubletap-coordinates-tab">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>X Coordinate</label>
                                        <input type="number" id="editDoubleTapX" class="form-control" value="0">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>Y Coordinate</label>
                                        <input type="number" id="editDoubleTapY" class="form-control" value="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Image Tab -->
                        <div class="tab-pane fade" id="edit-doubletap-image" role="tabpanel" aria-labelledby="edit-doubletap-image-tab">
                            <div class="form-group">
                                <label class="form-label">Image:</label>
                                <div class="input-group">
                                    <select class="form-select" id="editDoubleTapImageFilename">
                                        <option value="">Select...</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" id="editRefreshDoubleTapImages">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Threshold:</label>
                                        <input type="number" class="form-control" id="editDoubleTapThreshold" value="0.7" min="0" max="1" step="0.05">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Timeout (s):</label>
                                        <input type="number" class="form-control" id="editDoubleTapTimeout" value="20" min="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Locator Tab -->
                        <div class="tab-pane fade" id="edit-doubletap-locator" role="tabpanel" aria-labelledby="edit-doubletap-locator-tab">
                            <div class="form-group">
                                <label>Type</label>
                                <select id="editDoubleTapLocatorType" class="form-control">
                                    <option value="id">ID</option>
                                    <option value="xpath">XPath</option>
                                    <option value="accessibility_id">Accessibility ID</option>
                                    <option value="uiselector">UI Selector</option>
                                    <option value="class">Class Name</option>
                                    <option value="name">Name</option>
                                    <option value="text">Text</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Value</label>
                                <input type="text" id="editDoubleTapLocatorValue" class="form-control" placeholder="Enter locator value">
                            </div>
                            <div class="form-group">
                                <label>Timeout (s)</label>
                                <input type="number" id="editDoubleTapLocatorTimeout" class="form-control" value="10">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Swipe Action Form with Tabs -->
                <div id="editSwipeActionForm" class="action-form d-none">
                    <ul class="nav nav-tabs mb-3" id="editSwipeActionTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="edit-swipe-direction-tab" data-bs-toggle="tab" data-bs-target="#edit-swipe-direction" type="button" role="tab" aria-controls="edit-swipe-direction" aria-selected="true">Direction</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-swipe-custom-tab" data-bs-toggle="tab" data-bs-target="#edit-swipe-custom" type="button" role="tab" aria-controls="edit-swipe-custom" aria-selected="false">Custom</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="editSwipeActionTabContent">
                        <!-- Direction Tab -->
                        <div class="tab-pane fade show active" id="edit-swipe-direction" role="tabpanel" aria-labelledby="edit-swipe-direction-tab">
                            <div class="form-group">
                                <label>Swipe Direction</label>
                                <select id="editSwipeDirection" class="form-control">
                                    <option value="up">Up</option>
                                    <option value="down">Down</option>
                                    <option value="left">Left</option>
                                    <option value="right">Right</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Duration (ms)</label>
                                <input type="number" id="editSwipeDuration" class="form-control" value="1000" min="100">
                            </div>
                        </div>
                        <!-- Custom Tab -->
                        <div class="tab-pane fade" id="edit-swipe-custom" role="tabpanel" aria-labelledby="edit-swipe-custom-tab">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>Start X</label>
                                        <input type="number" id="editSwipeStartX" class="form-control" value="0">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>Start Y</label>
                                        <input type="number" id="editSwipeStartY" class="form-control" value="0">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>End X</label>
                                        <input type="number" id="editSwipeEndX" class="form-control" value="0">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label>End Y</label>
                                        <input type="number" id="editSwipeEndY" class="form-control" value="0">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Duration (ms)</label>
                                <input type="number" id="editSwipeCustomDuration" class="form-control" value="1000" min="100">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Value Action Form -->
                <div id="editGetValueActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Variable Name</label>
                        <input type="text" id="editGetValueVariable" class="form-control" placeholder="Enter variable name">
                    </div>
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editGetValueLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editGetValueLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                </div>

                <!-- Compare Value Action Form -->
                <div id="editCompareValueActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Variable Name</label>
                        <input type="text" id="editCompareValueVariable" class="form-control" placeholder="Enter variable name">
                    </div>
                    <div class="form-group">
                        <label>Expected Value</label>
                        <input type="text" id="editCompareValueExpected" class="form-control" placeholder="Enter expected value">
                    </div>
                    <div class="form-group">
                        <label>Comparison Type</label>
                        <select id="editCompareValueType" class="form-control">
                            <option value="equals">Equals</option>
                            <option value="contains">Contains</option>
                            <option value="not_equals">Not Equals</option>
                        </select>
                    </div>
                </div>

                <!-- If Exists Action Form -->
                <div id="editIfExistsActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editIfExistsLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editIfExistsLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                </div>

                <!-- If Visible Action Form -->
                <div id="editIfVisibleActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editIfVisibleLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editIfVisibleLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                </div>

                <!-- If Contains Text Action Form -->
                <div id="editIfContainsTextActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editIfContainsTextLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editIfContainsTextLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                    <div class="form-group">
                        <label>Expected Text</label>
                        <input type="text" id="editIfContainsTextExpected" class="form-control" placeholder="Enter expected text">
                    </div>
                </div>

                <!-- If Value Equals Action Form -->
                <div id="editIfValueEqualsActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editIfValueEqualsLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editIfValueEqualsLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                    <div class="form-group">
                        <label>Expected Value</label>
                        <input type="text" id="editIfValueEqualsExpected" class="form-control" placeholder="Enter expected value">
                    </div>
                </div>

                <!-- If Value Contains Action Form -->
                <div id="editIfValueContainsActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editIfValueContainsLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editIfValueContainsLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                    <div class="form-group">
                        <label>Expected Value</label>
                        <input type="text" id="editIfValueContainsExpected" class="form-control" placeholder="Enter expected value">
                    </div>
                </div>

                <!-- If Has Attribute Action Form -->
                <div id="editIfHasAttributeActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Locator Type</label>
                        <select id="editIfHasAttributeLocatorType" class="form-control">
                            <option value="id">ID</option>
                            <option value="xpath">XPath</option>
                            <option value="accessibility_id">Accessibility ID</option>
                            <option value="class">Class Name</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Locator Value</label>
                        <input type="text" id="editIfHasAttributeLocatorValue" class="form-control" placeholder="Enter locator value">
                    </div>
                    <div class="form-group">
                        <label>Attribute Name</label>
                        <input type="text" id="editIfHasAttributeName" class="form-control" placeholder="Enter attribute name">
                    </div>
                </div>

                <!-- Launch App Action Form -->
                <div id="editLaunchAppActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>App Bundle ID / Package Name</label>
                        <input type="text" id="editLaunchAppId" class="form-control" placeholder="Enter app identifier">
                    </div>
                </div>

                <!-- Take Screenshot Action Form -->
                <div id="editTakeScreenshotActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Screenshot Name (Optional)</label>
                        <input type="text" id="editTakeScreenshotName" class="form-control" placeholder="Enter screenshot name">
                    </div>
                </div>

                <!-- Info Action Form -->
                <div id="editInfoActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Info Message</label>
                        <input type="text" id="editInfoMessage" class="form-control" placeholder="Enter info message">
                    </div>
                </div>

                <!-- Add Action Button -->
                <div class="mt-3">
                    <button id="editAddAction" class="btn btn-success">
                        <i class="bi bi-plus"></i> Add Action
                    </button>
                </div>
            </div>
        `;

        this.setupEditActionBuilderListeners();
    }

    async saveEditedTestCase() {
        try {
            const modal = document.getElementById('editTestCaseModal');
            const testCaseId = modal.dataset.testCaseId;
            
            if (!testCaseId) {
                throw new Error('No test case ID found');
            }

            // Show loading state
            const saveBtn = document.getElementById('saveEditedTestCaseBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';
            saveBtn.disabled = true;

            // Prepare the data to save
            const saveData = {
                actions: this.editingActions || []
            };

            // Send save request to server
            const response = await fetch(`/api/test_cases/save_modified`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: testCaseId,
                    ...saveData
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Close the modal
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                bootstrapModal.hide();

                // Show success message
                this.showSuccess('Test case updated successfully!');

                // Refresh the test suite display if needed
                // This will ensure any changes are reflected in the UI
                await this.loadTestCases();
            } else {
                throw new Error(data.error || 'Failed to save test case');
            }
        } catch (error) {
            console.error('Error saving edited test case:', error);
            this.showError('Failed to save test case: ' + error.message);
        } finally {
            // Restore save button
            const saveBtn = document.getElementById('saveEditedTestCaseBtn');
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> Save Changes';
                saveBtn.disabled = false;
            }
        }
    }

    clearEditActions() {
        this.editingActions = [];
        this.populateEditActionsList();
    }

    removeEditAction(index) {
        if (this.editingActions && index >= 0 && index < this.editingActions.length) {
            this.editingActions.splice(index, 1);
            this.populateEditActionsList();
        }
    }

    setupEditActionBuilderListeners() {
        // Action type change listener
        document.getElementById('editActionType').addEventListener('change', () => {
            this.updateEditActionForm();
        });

        // Add action button listener
        document.getElementById('editAddAction').addEventListener('click', () => {
            this.addEditAction();
        });

        // Tab functionality for action forms
        document.addEventListener('click', (e) => {
            // Tap action tabs
            if (e.target.classList.contains('edit-tap-tab')) {
                document.querySelectorAll('.edit-tap-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                e.target.classList.add('active');
                document.querySelectorAll('.edit-tap-tab-content').forEach(content => {
                    content.classList.add('d-none');
                });
                const targetTab = e.target.dataset.tab;
                document.getElementById(`editTap${targetTab.charAt(0).toUpperCase() + targetTab.slice(1)}Tab`).classList.remove('d-none');
            }
            
            // Double Tap action tabs
            if (e.target.classList.contains('edit-doubletap-tab')) {
                document.querySelectorAll('.edit-doubletap-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                e.target.classList.add('active');
                document.querySelectorAll('.edit-doubletap-tab-content').forEach(content => {
                    content.classList.add('d-none');
                });
                const targetTab = e.target.dataset.tab;
                document.getElementById(`editDoubleTap${targetTab.charAt(0).toUpperCase() + targetTab.slice(1)}Tab`).classList.remove('d-none');
            }
            
            // Swipe action tabs
            if (e.target.classList.contains('edit-swipe-tab')) {
                document.querySelectorAll('.edit-swipe-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                e.target.classList.add('active');
                document.querySelectorAll('.edit-swipe-tab-content').forEach(content => {
                    content.classList.add('d-none');
                });
                const targetTab = e.target.dataset.tab;
                document.getElementById(`editSwipe${targetTab.charAt(0).toUpperCase() + targetTab.slice(1)}Tab`).classList.remove('d-none');
            }
        });
    }

    updateEditActionForm() {
        const actionType = document.getElementById('editActionType').value;
        const forms = document.querySelectorAll('#editActionForms .action-form');
        
        // Hide all forms
        forms.forEach(form => form.classList.add('d-none'));
        
        // Show relevant form
        switch(actionType) {
            case 'tap':
            case 'doubleTap':
                document.getElementById('editTapActionForm').classList.remove('d-none');
                break;
            case 'swipe':
                document.getElementById('editSwipeActionForm').classList.remove('d-none');
                break;
            case 'text':
            case 'tapAndType':
                document.getElementById('editTextActionForm').classList.remove('d-none');
                break;
            case 'sendKeys':
                document.getElementById('editSendKeysActionForm').classList.remove('d-none');
                break;
            case 'getValue':
                document.getElementById('editGetValueActionForm').classList.remove('d-none');
                break;
            case 'compareValue':
                document.getElementById('editCompareValueActionForm').classList.remove('d-none');
                break;
            case 'ifExists':
                document.getElementById('editIfExistsActionForm').classList.remove('d-none');
                break;
            case 'ifVisible':
                document.getElementById('editIfVisibleActionForm').classList.remove('d-none');
                break;
            case 'ifContainsText':
                document.getElementById('editIfContainsTextActionForm').classList.remove('d-none');
                break;
            case 'ifValueEquals':
                document.getElementById('editIfValueEqualsActionForm').classList.remove('d-none');
                break;
            case 'ifValueContains':
                document.getElementById('editIfValueContainsActionForm').classList.remove('d-none');
                break;
            case 'ifHasAttribute':
                document.getElementById('editIfHasAttributeActionForm').classList.remove('d-none');
                break;
            case 'launchApp':
                document.getElementById('editLaunchAppActionForm').classList.remove('d-none');
                break;
            case 'takeScreenshot':
                document.getElementById('editTakeScreenshotActionForm').classList.remove('d-none');
                break;
            case 'wait':
                document.getElementById('editWaitActionForm').classList.remove('d-none');
                break;
            case 'info':
                document.getElementById('editInfoActionForm').classList.remove('d-none');
                break;
        }
    }

    addEditAction() {
        const actionType = document.getElementById('editActionType').value;
        if (!actionType) {
            alert('Please select an action type');
            return;
        }

        let actionData = { type: actionType };

        // Collect form data based on action type
        switch(actionType) {
            case 'tap':
            case 'doubleTap':
                // Check which tab is active and collect appropriate data
                const activeTab = document.querySelector('.edit-tap-tab.active')?.dataset.tab || 'coordinates';
                
                if (activeTab === 'coordinates') {
                    actionData.x = parseInt(document.getElementById('editTapX').value) || 0;
                    actionData.y = parseInt(document.getElementById('editTapY').value) || 0;
                } else if (activeTab === 'image') {
                    actionData.image_path = document.getElementById('editTapImagePath').value;
                    actionData.confidence = parseFloat(document.getElementById('editTapConfidence').value) || 0.8;
                } else if (activeTab === 'locator') {
                    actionData.locator_type = document.getElementById('editTapLocatorType').value;
                    actionData.locator_value = document.getElementById('editTapLocatorValue').value;
                }
                actionData.action = actionType;
                break;
            case 'swipe':
                actionData.direction = document.getElementById('editSwipeDirection').value;
                actionData.action = 'swipe';
                break;
            case 'text':
            case 'tapAndType':
                actionData.text = document.getElementById('editTextInput').value;
                actionData.x = parseInt(document.getElementById('editTextX').value) || 0;
                actionData.y = parseInt(document.getElementById('editTextY').value) || 0;
                actionData.action = actionType;
                if (!actionData.text) {
                    alert('Please enter text to input');
                    return;
                }
                break;
            case 'sendKeys':
                actionData.text = document.getElementById('editSendKeysText').value;
                actionData.locator_type = document.getElementById('editSendKeysLocatorType').value;
                actionData.locator_value = document.getElementById('editSendKeysLocatorValue').value;
                actionData.timeout = parseInt(document.getElementById('editSendKeysTimeout').value) || 10;
                actionData.action = 'sendKeys';
                if (!actionData.text) {
                    alert('Please enter text to send');
                    return;
                }
                break;
            case 'getValue':
                actionData.locator_type = document.getElementById('editGetValueLocatorType').value;
                actionData.locator_value = document.getElementById('editGetValueLocatorValue').value;
                actionData.variable_name = document.getElementById('editGetValueVariableName').value;
                actionData.action = 'getValue';
                break;
            case 'compareValue':
                actionData.locator_type = document.getElementById('editCompareValueLocatorType').value;
                actionData.locator_value = document.getElementById('editCompareValueLocatorValue').value;
                actionData.expected_value = document.getElementById('editCompareValueExpected').value;
                actionData.action = 'compareValue';
                break;
            case 'ifExists':
                actionData.locator_type = document.getElementById('editIfExistsLocatorType').value;
                actionData.locator_value = document.getElementById('editIfExistsLocatorValue').value;
                actionData.action = 'ifExists';
                break;
            case 'ifVisible':
                actionData.locator_type = document.getElementById('editIfVisibleLocatorType').value;
                actionData.locator_value = document.getElementById('editIfVisibleLocatorValue').value;
                actionData.action = 'ifVisible';
                break;
            case 'ifContainsText':
                actionData.locator_type = document.getElementById('editIfContainsTextLocatorType').value;
                actionData.locator_value = document.getElementById('editIfContainsTextLocatorValue').value;
                actionData.expected_text = document.getElementById('editIfContainsTextExpected').value;
                actionData.action = 'ifContainsText';
                break;
            case 'ifValueEquals':
                actionData.locator_type = document.getElementById('editIfValueEqualsLocatorType').value;
                actionData.locator_value = document.getElementById('editIfValueEqualsLocatorValue').value;
                actionData.expected_value = document.getElementById('editIfValueEqualsExpected').value;
                actionData.action = 'ifValueEquals';
                break;
            case 'ifValueContains':
                actionData.locator_type = document.getElementById('editIfValueContainsLocatorType').value;
                actionData.locator_value = document.getElementById('editIfValueContainsLocatorValue').value;
                actionData.expected_text = document.getElementById('editIfValueContainsExpected').value;
                actionData.action = 'ifValueContains';
                break;
            case 'ifHasAttribute':
                actionData.locator_type = document.getElementById('editIfHasAttributeLocatorType').value;
                actionData.locator_value = document.getElementById('editIfHasAttributeLocatorValue').value;
                actionData.attribute_name = document.getElementById('editIfHasAttributeName').value;
                actionData.action = 'ifHasAttribute';
                break;
            case 'launchApp':
                actionData.package_name = document.getElementById('editLaunchAppPackage').value;
                actionData.action = 'launchApp';
                if (!actionData.package_name) {
                    alert('Please enter a package name');
                    return;
                }
                break;
            case 'takeScreenshot':
                actionData.filename = document.getElementById('editTakeScreenshotFilename').value;
                actionData.action = 'takeScreenshot';
                break;
            case 'wait':
                actionData.duration = parseFloat(document.getElementById('editWaitDuration').value) || 1;
                actionData.action = 'wait';
                break;
            case 'info':
                actionData.message = document.getElementById('editInfoMessage').value;
                actionData.action = 'info';
                if (!actionData.message) {
                    alert('Please enter an info message');
                    return;
                }
                break;
        }

        // Add to actions list
        if (!this.editingActions) {
            this.editingActions = [];
        }
        this.editingActions.push(actionData);
        
        // Update the actions list display
        this.populateEditActionsList();
        
        // Clear the form
        this.clearEditActionForm();
    }

    clearEditActionForm() {
        document.getElementById('editActionType').value = '';
        document.getElementById('editTapX').value = '0';
        document.getElementById('editTapY').value = '0';
        document.getElementById('editTextInput').value = '';
        document.getElementById('editTextX').value = '0';
        document.getElementById('editTextY').value = '0';
        document.getElementById('editWaitDuration').value = '1';
        document.getElementById('editInfoMessage').value = '';
        this.updateEditActionForm();
    }

    editAction(index) {
        const action = this.editingActions[index];
        if (!action) return;

        // Populate the form with action data
        document.getElementById('editActionType').value = action.type;
        this.updateEditActionForm();

        // Fill form fields based on action type
        switch(action.type) {
            case 'tap':
            case 'doubleTap':
                document.getElementById('editTapX').value = action.x || 0;
                document.getElementById('editTapY').value = action.y || 0;
                break;
            case 'text':
            case 'tapAndType':
                document.getElementById('editTextInput').value = action.text || '';
                document.getElementById('editTextX').value = action.x || 0;
                document.getElementById('editTextY').value = action.y || 0;
                break;
            case 'wait':
                document.getElementById('editWaitDuration').value = action.duration || 1;
                break;
            case 'info':
                document.getElementById('editInfoMessage').value = action.message || '';
                break;
        }

        // Remove the action from the list (it will be re-added when user clicks Add Action)
        this.editingActions.splice(index, 1);
        this.populateEditActionsList();
    }

    async autoSaveTestSuiteChanges() {
        // Check if we're currently editing a test suite
        const saveButton = document.getElementById('saveTestSuiteBtn');
        const isEditMode = saveButton && saveButton.dataset.mode === 'edit';

        if (!isEditMode) {
            // Not in edit mode, no need to auto-save
            return;
        }

        const suiteId = saveButton.dataset.suiteId;
        if (!suiteId) {
            console.warn('No suite ID found for auto-save');
            return;
        }

        try {
            // Show auto-save indicator
            this.showAutoSaveIndicator('Saving...');

            // Get current form values
            const name = document.getElementById('testSuiteName').value;
            const description = document.getElementById('testSuiteDescription').value;

            // Validate that we have required data
            if (!name) {
                // Don't auto-save if name is missing
                this.hideAutoSaveIndicator();
                return;
            }

            // Allow auto-save even with no test cases during editing
            // The validation for minimum test cases will happen during execution

            // Save the changes to the server
            const response = await fetch(`/api/test_suites/${suiteId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    test_cases: this.selectedTestCases
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                console.log('Test suite auto-saved successfully');
                // Show a subtle success indicator
                this.showAutoSaveIndicator('✓ Saved', 'success');
            } else {
                console.error('Auto-save failed:', data.error);
                this.showAutoSaveIndicator('✗ Save failed', 'error');
                this.showError('Failed to auto-save changes: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error during auto-save:', error);
            this.showAutoSaveIndicator('✗ Save failed', 'error');
            this.showError('Error auto-saving changes: ' + error.message);
        }
    }

    showAutoSaveIndicator(message, type = 'info') {
        // Show auto-save indicator next to the save button
        const saveButton = document.getElementById('saveTestSuiteBtn');
        if (!saveButton) return;

        // Remove any existing indicator
        this.hideAutoSaveIndicator();

        // Create indicator element
        const indicator = document.createElement('span');
        indicator.id = 'autoSaveIndicator';
        indicator.className = 'ms-2 small';
        indicator.textContent = message;

        // Style based on type
        switch (type) {
            case 'success':
                indicator.className += ' text-success';
                break;
            case 'error':
                indicator.className += ' text-danger';
                break;
            case 'info':
            default:
                indicator.className += ' text-muted';
                break;
        }

        // Insert after the save button
        saveButton.parentNode.insertBefore(indicator, saveButton.nextSibling);

        // Auto-hide success/error messages after 3 seconds
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                this.hideAutoSaveIndicator();
            }, 3000);
        }
    }

    hideAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.remove();
        }
    }

    setupAutoSaveOnFieldChange() {
        // Debounce timer for auto-save
        let autoSaveTimer = null;

        const debouncedAutoSave = () => {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(async () => {
                await this.autoSaveTestSuiteChanges();
            }, 1000); // Wait 1 second after user stops typing
        };

        // Add event listeners to name and description fields
        const nameField = document.getElementById('testSuiteName');
        const descriptionField = document.getElementById('testSuiteDescription');

        if (nameField) {
            nameField.addEventListener('input', debouncedAutoSave);
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', debouncedAutoSave);
        }
    }

    async loadTestSuites() {
        try {
            console.log('Loading test suites...');
            const response = await fetch('/api/test_suites/list');
            const data = await response.json();

            console.log('Test suites response:', data);

            if (data.status === 'success') {
                console.log('Test suites loaded successfully, count:', data.test_suites.length);
                this.displayTestSuites(data.test_suites);
            } else {
                console.error('Failed to load test suites:', data.error);
                console.error('Failed to load test suites:', data.error);
                this.showError('Failed to load test suites');
            }
        } catch (error) {
            console.error('Error loading test suites:', error);
            this.showError('Error loading test suites');
        }
    }

    displayTestSuites(testSuites) {
        console.log('displayTestSuites called with:', testSuites);
        const testSuitesList = document.getElementById('testSuitesList');
        console.log('testSuitesList element:', testSuitesList);
        testSuitesList.innerHTML = ''; // Clear existing list

        if (testSuites.length === 0) {
            testSuitesList.innerHTML = '<p class="text-muted">No test suites created yet.</p>';
            return;
        }

        testSuites.forEach(suite => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item'; // Use the new list item class

            const testCaseCount = suite.test_cases ? suite.test_cases.length : 0;
            let created;
            try {
                const date = new Date(suite.created);
                created = isNaN(date.getTime()) ? 'Unknown' : date.toLocaleDateString();
            } catch (error) {
                created = 'Unknown';
            }

            listItem.innerHTML = `
                <div class="test-suite-info">
                    <span class="test-suite-name">${suite.name}</span>
                    <div class="test-suite-meta">
                        <span><i class="bi bi-file-earmark-text"></i> ${testCaseCount} Test Case${testCaseCount !== 1 ? 's' : ''}</span>
                        <span class="ms-3"><i class="bi bi-calendar-event"></i> Created: ${created}</span>
                    </div>
                </div>
                <div class="test-suite-actions">
                    <button class="btn btn-sm btn-outline-primary edit-suite-btn" data-suite-id="${suite.id}" title="Edit Test Suite">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline-secondary rename-suite-btn" data-suite-id="${suite.id}" data-suite-name="${suite.name}" title="Rename Test Suite">
                        <i class="bi bi-pencil-square"></i> Rename
                    </button>
                    <button class="btn btn-sm btn-outline-secondary duplicate-suite-btn" data-suite-id="${suite.id}" title="Duplicate Test Suite">
                        <i class="bi bi-files"></i> Duplicate
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-suite-btn" data-suite-id="${suite.id}" title="Delete Test Suite">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </div>
            `;

            testSuitesList.appendChild(listItem);
        });

        // Debug: Check the actual HTML that was generated
        console.log('Generated test suites HTML:');
        console.log(testSuitesList.innerHTML);

        // Re-add event listeners for the new buttons
        this.addTestSuiteActionListeners();
    }

    addTestSuiteActionListeners() {
        const editButtons = document.querySelectorAll('.edit-suite-btn');
        const renameButtons = document.querySelectorAll('.rename-suite-btn');
        const duplicateButtons = document.querySelectorAll('.duplicate-suite-btn');
        const deleteButtons = document.querySelectorAll('.delete-suite-btn');

        console.log('Adding test suite action listeners:');
        console.log('Edit buttons found:', editButtons.length);
        console.log('Rename buttons found:', renameButtons.length);
        console.log('Duplicate buttons found:', duplicateButtons.length);
        console.log('Delete buttons found:', deleteButtons.length);

        editButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                this.editTestSuite(suiteId);
            });
        });

        renameButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                const currentName = event.currentTarget.dataset.suiteName;
                this.renameTestSuite(suiteId, currentName);
            });
        });

        duplicateButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                this.duplicateTestSuite(suiteId);
            });
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                if (confirm(`Are you sure you want to delete test suite ${suiteId}?`)) {
                    this.deleteTestSuite(suiteId);
                }
            });
        });
    }

    async saveTestSuite() {
        const name = document.getElementById('testSuiteName').value;
        const description = document.getElementById('testSuiteDescription').value;
        const saveButton = document.getElementById('saveTestSuiteBtn');
        const isEdit = saveButton.dataset.mode === 'edit';
        const suiteId = isEdit ? saveButton.dataset.suiteId : null;

        if (!name) {
            this.showError('Please enter a name for the test suite');
            return;
        }

        if (this.selectedTestCases.length === 0) {
            this.showError('Please select at least one test case');
            return;
        }

        try {
            const endpoint = isEdit
                ? `/api/test_suites/${suiteId}/update`
                : '/api/test_suites/create';

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    test_cases: this.selectedTestCases // Already an array, no need for Array.from
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Close modal and reset form
                const modal = bootstrap.Modal.getInstance(document.getElementById('createTestSuiteModal'));
                modal.hide();
                document.getElementById('createTestSuiteForm').reset();
                this.selectedTestCases = []; // Reset to empty array
                this.updateSelectedTestCasesList();

                // Refresh test suites list
                this.loadTestSuites();

                // Uncheck all checkboxes
                document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Reset button text and mode for next time
                saveButton.textContent = 'Save Test Suite';
                saveButton.dataset.mode = 'create';
                delete saveButton.dataset.suiteId;

                // Reset modal title
                document.getElementById('createTestSuiteModalLabel').textContent = 'Create New Test Suite';

                // Hide any auto-save indicators
                this.hideAutoSaveIndicator();

                this.showSuccess(isEdit ? 'Test suite updated successfully' : 'Test suite created successfully');
            } else {
                this.showError(data.error || 'Failed to save test suite');
            }
        } catch (error) {
            console.error('Error saving test suite:', error);
            this.showError('Error saving test suite');
        }
    }

    async renameTestSuite(suiteId, currentName) {
        // Prompt for new name
        const newName = prompt('Enter a new name for the test suite:', currentName);

        // Check if user cancelled or entered an empty name
        if (!newName || newName.trim() === '') {
            return;
        }

        try {
            // Use the new rename endpoint that handles filename changes
            const response = await fetch(`/api/test_suites/${suiteId}/rename`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    new_name: newName
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess(`Test suite renamed successfully. New ID: ${data.id}`);
            } else {
                this.showError(data.error || 'Failed to rename test suite');
            }
        } catch (error) {
            console.error('Error renaming test suite:', error);
            this.showError('Error renaming test suite');
        }
    }

    async duplicateTestSuite(suiteId) {
        try {
            const response = await fetch(`/api/test_suites/${suiteId}/duplicate`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite duplicated successfully');
            } else {
                this.showError(data.error || 'Failed to duplicate test suite');
            }
        } catch (error) {
            console.error('Error duplicating test suite:', error);
            this.showError('Error duplicating test suite');
        }
    }

    async getTestSuite(suiteId) {
        try {
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status === 'success') {
                return data.test_suite;
            } else {
                this.showError(data.error || 'Failed to fetch test suite');
                return null;
            }
        } catch (error) {
            console.error('Error fetching test suite:', error);
            this.showError('Error fetching test suite');
            return null;
        }
    }

    async deleteTestSuite(suiteId) {
        if (!confirm('Are you sure you want to delete this test suite?')) {
            return;
        }

        try {
            const response = await fetch(`/api/test_suites/${suiteId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite deleted successfully');
            } else {
                this.showError(data.error || 'Failed to delete test suite');
            }
        } catch (error) {
            console.error('Error deleting test suite:', error);
            this.showError('Error deleting test suite');
        }
    }

    async runTestSuite(suiteId) {
        try {
            console.log('runTestSuite called with suiteId:', suiteId);

            // First, fetch the test suite details to get the test cases
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            console.log('Test suite data received:', data);

            if (data.status !== 'success') {
                console.error('Failed to fetch test suite details:', data.error);
                this.showError(data.error || 'Failed to fetch test suite details');
                return;
            }

            console.log('About to show execution config modal');
            // Show the execution configuration modal
            await this.showExecutionConfigModal(suiteId, data.test_suite);

        } catch (error) {
            console.error('Error running test suite:', error);
            this.hideTestSuiteExecutionLoading();
            this.enableDeviceScreenshots();
            this.showError('Error running test suite');
        }
    }

    async showExecutionConfigModal(suiteId, testSuite) {
        return new Promise((resolve, reject) => {
            console.log('showExecutionConfigModal called with:', { suiteId, testSuite });

            // Populate modal with test suite information
            const infoElement = document.getElementById('testSuiteExecutionInfo');
            const countElement = document.getElementById('testSuiteExecutionTestCount');

            console.log('Modal elements found:', { infoElement, countElement });

            if (infoElement) {
                infoElement.textContent = `Test Suite: ${testSuite.name}`;
            }
            if (countElement) {
                countElement.textContent = `${testSuite.test_cases.length} test cases`;
            }

            // Load default values from database
            this.loadExecutionDefaults();

            // Show the modal
            const modalElement = document.getElementById('testSuiteExecutionModal');
            console.log('Modal element found:', modalElement);

            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                console.log('Bootstrap modal created, about to show');
                modal.show();
            } else {
                console.error('testSuiteExecutionModal element not found!');
                reject(new Error('Modal element not found'));
                return;
            }

            // Handle Save and Execute button
            const saveAndExecuteBtn = document.getElementById('saveAndExecuteBtn');
            const newHandler = async () => {
                saveAndExecuteBtn.removeEventListener('click', newHandler);

                const recordExecution = document.getElementById('recordExecutionSelect').value;
                const retryFailedTests = parseInt(document.getElementById('retryFailedTestsSelect').value);

                // Save settings to database
                await this.saveExecutionSettings(recordExecution, retryFailedTests);

                // Hide modal
                modal.hide();

                // Start execution with settings
                await this.executeTestSuiteWithSettings(suiteId, testSuite, recordExecution, retryFailedTests);
                resolve();
            };

            saveAndExecuteBtn.addEventListener('click', newHandler);

            // Handle modal close/cancel
            const closeHandler = () => {
                modalElement.removeEventListener('hidden.bs.modal', closeHandler);
                saveAndExecuteBtn.removeEventListener('click', newHandler);
                reject(new Error('User cancelled execution'));
            };
            modalElement.addEventListener('hidden.bs.modal', closeHandler);
        });
    }

    async loadExecutionDefaults() {
        try {
            // Load default values from database
            const response = await fetch('/api/execution_settings');
            if (response.ok) {
                const settings = await response.json();
                document.getElementById('recordExecutionSelect').value = settings.record_execution || 'NO';
                document.getElementById('retryFailedTestsSelect').value = settings.retry_failed_tests || '0';
                const capEl = document.getElementById('captureStepScreenshotsCheckbox');
                if (capEl) capEl.checked = (settings.capture_step_screenshots !== 'false' && settings.capture_step_screenshots !== false);
            }
        } catch (error) {
            console.error('Error loading execution defaults:', error);
            // Use hardcoded defaults if API fails
            document.getElementById('recordExecutionSelect').value = 'NO';
            document.getElementById('retryFailedTestsSelect').value = '0';
        }
    }

    async saveExecutionSettings(recordExecution, retryFailedTests) {
        try {
            await fetch('/api/execution_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    record_execution: recordExecution,
                    retry_failed_tests: retryFailedTests.toString(),
                    capture_step_screenshots: (document.getElementById('captureStepScreenshotsCheckbox')?.checked ? 'true' : 'false')
                })
            });
        } catch (error) {
            console.error('Error saving execution settings:', error);
        }
    }

    async executeTestSuiteWithSettings(suiteId, testSuite, recordExecution, retryFailedTests) {
        try {
            // Show minimal loading indicator for test suite execution
            this.showTestSuiteExecutionLoading('Running test suite...');

            // Disable device screenshot refreshing during execution
            this.disableDeviceScreenshots();

            // Call the API to run the test suite with execution settings
            const runResponse = await fetch(`/api/test_suites/${suiteId}/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    device_id: window.app ? window.app.deviceId : null,  // Include device ID for session isolation
                    record_execution: recordExecution,
                    retry_failed_tests: retryFailedTests
                })
            });

            // Hide loading indicator and re-enable device screenshots
            this.hideTestSuiteExecutionLoading();
            this.enableDeviceScreenshots();

            const runData = await runResponse.json();

            if (runData.status === 'success') {
                this.showSuccess('Test suite execution started');

                // If a report URL is returned, offer to open it
                if (runData.report_url) {
                    // Add a button to open the report
                    this.showReportButton(runData.report_url, testSuite.name);

                    // Refresh the Reports tab if it exists
                    if (window.reportsManager) {
                        window.reportsManager.loadReports();
                    }
                }

                // Optionally, switch to a results tab or show execution status
                const testCasesTab = document.getElementById('test-cases-tab-btn');
                if (testCasesTab) {
                    const tabInstance = new bootstrap.Tab(testCasesTab);
                    tabInstance.show();
                }
            } else {
                this.showError(runData.error || 'Failed to run test suite');
            }
        } catch (error) {
            console.error('Error running test suite:', error);
            this.showError('Error running test suite');
            this.hideLoading();
        }
    }

    async editTestSuite(suiteId) {
        try {
            // Fetch the test suite details
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status !== 'success') {
                this.showError(data.error || 'Failed to fetch test suite details');
                return;
            }

            const suite = data.test_suite;

            // Populate the edit form
            document.getElementById('testSuiteName').value = suite.name;
            document.getElementById('testSuiteDescription').value = suite.description || '';

            // Clear existing selections
            this.selectedTestCases = [];

            // Ensure all available checkboxes are unchecked first
            document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Process the test cases in this suite (maintain order)
            if (Array.isArray(suite.test_cases)) {
                suite.test_cases.forEach(testCaseData => {
                    let filename = null;

                    // Determine the filename based on data structure
                    if (typeof testCaseData === 'string') {
                        filename = testCaseData;
                    } else if (testCaseData && typeof testCaseData.filename === 'string') {
                        filename = testCaseData.filename;
                    }

                    // Process if we have a valid filename
                    if (filename) {
                        // Add to our array if not already present
                        if (!this.selectedTestCases.includes(filename)) {
                            this.selectedTestCases.push(filename);
                        }

                        // Check the checkbox if it exists
                        const checkbox = document.getElementById(`testCase_${filename}`);
                        if (checkbox) {
                            checkbox.checked = true;
                        } else {
                            console.warn(`Checkbox for test case filename ${filename} not found in the available list.`);
                        }
                    } else {
                        console.warn('Skipping invalid test case data during edit:', testCaseData);
                    }
                });
            }

            // Update the selected test cases list
            this.updateSelectedTestCasesList();

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('createTestSuiteModal'));
            modal.show();

            // Change the title to indicate editing
            document.getElementById('createTestSuiteModalLabel').textContent = 'Edit Test Suite';

            // Change the save button text
            const saveButton = document.getElementById('saveTestSuiteBtn');
            saveButton.textContent = 'Update Test Suite';
            saveButton.dataset.mode = 'edit';
            saveButton.dataset.suiteId = suiteId;
        } catch (error) {
            console.error('Error editing test suite:', error);
            this.showError('Error editing test suite');
        }
    }

    showError(message) {
        // Display error message using Bootstrap toast or alert
        alert(message);
    }

    showSuccess(message) {
        // Display success message using Bootstrap toast or alert
        alert(message);
    }

    showLoading(message) {
        // Implement loading indicator if needed
        console.log(`Loading: ${message}`);
    }

    hideLoading() {
        // Hide loading indicator if needed
        console.log('Loading complete');
    }

    /**
     * Show a button to open the test report
     *
     * @param {string} reportUrl - URL to the test report
     * @param {string} suiteName - Name of the test suite
     */
    showReportButton(reportUrl, suiteName) {
        // Remove any existing report notification
        const existingContainer = document.getElementById('report-notification-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Create notification container
        const container = document.createElement('div');
        container.id = 'report-notification-container';
        container.className = 'position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1050';

        // Create a Bootstrap toast
        container.innerHTML = `
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <i class="bi bi-file-earmark-check me-2"></i>
                    <strong class="me-auto">Test Report Ready</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    <div class="mb-2">Test suite "${suiteName}" execution report is ready.</div>
                    <div class="d-flex justify-content-between">
                        <a href="${reportUrl}" target="_blank" class="btn btn-primary btn-sm">
                            <i class="bi bi-eye me-1"></i> View Report
                        </a>
                    </div>
                </div>
            </div>
        `;

        // Add to document body
        document.body.appendChild(container);

        // Set up auto-dismiss after 30 seconds
        setTimeout(() => {
            const toast = container.querySelector('.toast');
            if (toast) {
                const bsToast = bootstrap.Toast.getInstance(toast);
                if (bsToast) {
                    bsToast.hide();
                } else {
                    container.remove();
                }
            }
        }, 30000);

        // Set up event listeners
        const closeBtn = container.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                container.remove();
            });
        }
    }

    showLoading(message = 'Loading...') {
        let loadingOverlay = document.getElementById('loading-overlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loading-overlay';
            loadingOverlay.style.position = 'fixed';
            loadingOverlay.style.top = '0';
            loadingOverlay.style.left = '0';
            loadingOverlay.style.width = '100%';
            loadingOverlay.style.height = '100%';
            loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loadingOverlay.style.zIndex = '9999';
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.justifyContent = 'center';
            loadingOverlay.style.alignItems = 'center';
            loadingOverlay.style.color = '#fff';
            loadingOverlay.style.fontSize = '1.5rem';

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border mr-3';
            spinner.setAttribute('role', 'status');

            const loadingText = document.createElement('span');
            loadingText.id = 'loading-message';
            loadingText.style.marginLeft = '10px';

            loadingOverlay.appendChild(spinner);
            loadingOverlay.appendChild(loadingText);

            document.body.appendChild(loadingOverlay);
        }

        document.getElementById('loading-message').textContent = message;
        loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    showTestSuiteExecutionLoading(message) {
        // Create a minimal loading overlay for test suite execution
        let loadingOverlay = document.getElementById('test-suite-execution-loading');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'test-suite-execution-loading';
            loadingOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.3);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            `;

            const loadingContent = document.createElement('div');
            loadingContent.style.cssText = `
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                text-align: center;
                min-width: 300px;
            `;

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border text-primary';
            spinner.style.cssText = 'width: 3rem; height: 3rem; margin-bottom: 15px;';
            spinner.setAttribute('role', 'status');

            const loadingText = document.createElement('div');
            loadingText.id = 'test-suite-execution-message';
            loadingText.style.cssText = 'font-size: 16px; color: #333; font-weight: 500;';
            loadingText.textContent = message;

            const subText = document.createElement('div');
            subText.style.cssText = 'font-size: 12px; color: #666; margin-top: 10px;';
            subText.textContent = 'Device screenshots and overlays are disabled for optimal performance';

            loadingContent.appendChild(spinner);
            loadingContent.appendChild(loadingText);
            loadingContent.appendChild(subText);
            loadingOverlay.appendChild(loadingContent);

            document.body.appendChild(loadingOverlay);
        } else {
            document.getElementById('test-suite-execution-message').textContent = message;
        }

        loadingOverlay.style.display = 'flex';
    }

    hideTestSuiteExecutionLoading() {
        const loadingOverlay = document.getElementById('test-suite-execution-loading');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    disableDeviceScreenshots() {
        // Disable automatic screenshot refreshing
        const refreshBtn = document.getElementById('refreshScreenBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.title = 'Screenshot refresh disabled during test suite execution';
        }

        // Hide execution overlays
        if (window.executionOverlay) {
            window.executionOverlay.hide();
        }

        // Hide all device screen overlays
        const deviceScreenOverlays = document.querySelectorAll('.device-screen-overlay, .overlay-canvas, .loading-overlay');
        deviceScreenOverlays.forEach(overlay => {
            if (overlay) {
                overlay.style.display = 'none';
            }
        });

        // Hide fixed device screen if it exists
        if (window.fixedDeviceScreen) {
            window.fixedDeviceScreen.hide();
        }

        // Disable any automatic screenshot intervals
        if (window.screenshotInterval) {
            clearInterval(window.screenshotInterval);
            window.screenshotInterval = null;
        }

        // Hide device screen container overlays
        const deviceContainer = document.querySelector('.device-screen-container');
        if (deviceContainer) {
            const overlays = deviceContainer.querySelectorAll('.overlay, .device-overlay, .execution-overlay');
            overlays.forEach(overlay => {
                overlay.style.display = 'none';
            });
        }

        console.log('Device screenshots and overlays disabled for test suite execution');
    }

    enableDeviceScreenshots() {
        // Re-enable screenshot refreshing
        const refreshBtn = document.getElementById('refreshScreenBtn');
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.title = 'Refresh device screenshot';
        }

        // Re-show device screen overlays
        const deviceScreenOverlays = document.querySelectorAll('.device-screen-overlay, .overlay-canvas');
        deviceScreenOverlays.forEach(overlay => {
            if (overlay) {
                overlay.style.display = '';
            }
        });

        // Re-show fixed device screen if it exists
        if (window.fixedDeviceScreen) {
            window.fixedDeviceScreen.show();
        }

        // Re-show device screen container overlays
        const deviceContainer = document.querySelector('.device-screen-container');
        if (deviceContainer) {
            const overlays = deviceContainer.querySelectorAll('.overlay, .device-overlay');
            overlays.forEach(overlay => {
                overlay.style.display = '';
            });
        }

        console.log('Device screenshots and overlays re-enabled after test suite execution');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, creating TestSuitesManager');
    window.testSuitesManager = new TestSuitesManager();
    console.log('TestSuitesManager created:', window.testSuitesManager);
});