/* Test Executions tab logic (shared patterns with existing UI) */
(function() {
  const state = {
    page: 1,
    pageSize: 10,
    total: 0,
    pages: 0,
    loading: false,
  };

  const els = {
    tableBody: null,
    summary: null,
    prevBtn: null,
    nextBtn: null,
    feedback: null,
    tabBtn: null,
    refreshBtn: null,
  };

  function byId(id) { return document.getElementById(id); }

  function setLoading(isLoading) {
    state.loading = isLoading;
    if (els.feedback) {
      els.feedback.textContent = isLoading ? 'Loading executions…' : '';
    }
    if (els.prevBtn) els.prevBtn.disabled = isLoading || state.page <= 1;
    if (els.nextBtn) els.nextBtn.disabled = isLoading || state.page >= state.pages;
  }

  function statusBadge(status) {
    const s = (status || '').toLowerCase();
    let cls = 'secondary';
    if (s === 'passed' || s === 'success' || s === 'completed') cls = 'success';
    else if (s === 'failed' || s === 'error') cls = 'danger';
    else if (s === 'running' || s === 'in_progress') cls = 'warning';
    return `<span class="badge bg-${cls}">${status || 'UNKNOWN'}</span>`;
  }

  function formatDate(iso) {
    if (!iso) return '-';
    try {
      const d = new Date(iso);
      if (!isNaN(d)) return d.toLocaleString();
    } catch(_){}
    return iso;
  }

  async function fetchExecutions() {
    setLoading(true);
    try {
      const resp = await fetch(`/api/executions?page=${state.page}&page_size=${state.pageSize}`);
      if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
      const data = await resp.json();
      if (!data.success) throw new Error(data.error || 'Failed to load');
      state.total = data.total || 0;
      state.pages = data.pages || 0;
      renderRows(data.items || []);
      renderSummary();
    } catch (err) {
      console.error('Error loading executions:', err);
      toast('Failed to load executions', 'danger');
    } finally {
      setLoading(false);
    }
  }

  function renderRows(items) {
    if (!els.tableBody) return;
    if (!items.length) {
      els.tableBody.innerHTML = `<tr><td colspan="4" class="text-center text-muted py-4">No executions found</td></tr>`;
      return;
    }
    const rows = items.map(it => {
      const startedAt = formatDate(it.created_at || it.started_at);
      const execId = it.execution_id || it.test_execution_id || '-';
      const status = it.status || '-';
      return `
        <tr data-exec-id="${execId}">
          <td class="text-nowrap">${startedAt}</td>
          <td><code>${execId}</code></td>
          <td>${statusBadge(status)}</td>
          <td>
            <div class="btn-group btn-group-sm" role="group">
              <button class="btn btn-outline-primary" data-action="export" title="Download ZIP"><i class="bi bi-download"></i></button>
              <button class="btn btn-outline-secondary" data-action="load" title="Load Execution"><i class="bi bi-folder2-open"></i></button>
              <button class="btn btn-outline-danger" data-action="delete" title="Delete"><i class="bi bi-trash"></i></button>
            </div>
          </td>
        </tr>`;
    }).join('');
    els.tableBody.innerHTML = rows;
  }

  function renderSummary() {
    if (!els.summary) return;
    if (state.pages <= 1) {
      els.summary.textContent = `${state.total} total`;
    } else {
      const start = (state.page - 1) * state.pageSize + 1;
      const end = Math.min(state.page * state.pageSize, state.total);
      els.summary.textContent = `${start}-${end} of ${state.total}`;
    }
  }

  function toast(message, variant = 'info') {
    // Minimal feedback; can integrate with existing toast system if present
    if (els.feedback) {
      els.feedback.innerHTML = `<span class="text-${variant}">${message}</span>`;
    }
  }

  async function handleRowAction(e) {
    const btn = e.target.closest('button[data-action]');
    if (!btn) return;
    const tr = btn.closest('tr[data-exec-id]');
    if (!tr) return;
    const executionId = tr.getAttribute('data-exec-id');
    const action = btn.getAttribute('data-action');

    if (action === 'export') {
      await exportExecution(executionId);
    } else if (action === 'delete') {
      await deleteExecution(executionId);
    } else if (action === 'load') {
      try {
        setLoading(true);
        // Prefer the built-in import + load flow if available
        if (window.app && typeof window.app.importExecutionAndLoadTestSuiteWithBadges === 'function') {
          // Switch to Device Control tab to reveal the Action List
          const deviceTab = document.getElementById('device-tab');
          if (deviceTab) deviceTab.click();
          await window.app.importExecutionAndLoadTestSuiteWithBadges(executionId, executionId);
          toast('Execution loaded into Action List', 'success');
        } else {
          // Fallback: just try the import endpoint so user sees any errors
          const resp = await fetch(`/api/execution/import/${encodeURIComponent(executionId)}`);
          if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
          const data = await resp.json();
          if (data.status !== 'success') throw new Error(data.message || 'Import failed');
          toast('Execution data imported. Please use Import UI to load.', 'info');
        }
      } catch (e) {
        console.error('Load error:', e);
        toast(`Failed to load execution: ${e.message}`, 'danger');
      } finally {
        setLoading(false);
      }
    }
  }

  async function exportExecution(executionId) {
    try {
      setLoading(true);
      const resp = await fetch(`/api/executions/${encodeURIComponent(executionId)}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      const data = await resp.json();
      if (!resp.ok || !data.success) throw new Error(data.error || 'Export failed');
      if (data.download_url) {
        window.open(data.download_url, '_blank');
        toast('Export prepared', 'success');
      } else {
        toast('No download URL returned', 'warning');
      }
    } catch (err) {
      console.error('Export error:', err);
      toast('Failed to export execution', 'danger');
    } finally {
      setLoading(false);
    }
  }

  async function deleteExecution(executionId) {
    try {
      if (!confirm('Delete this execution? This cannot be undone.')) return;
      setLoading(true);
      const resp = await fetch(`/api/executions/${encodeURIComponent(executionId)}`, { method: 'DELETE' });
      const data = await resp.json().catch(() => ({}));
      if (!resp.ok || data.success === false) throw new Error(data.error || `HTTP ${resp.status}`);
      toast('Execution deleted', 'success');
      // reload current page
      await fetchExecutions();
    } catch (err) {
      console.error('Delete error:', err);
      toast('Failed to delete execution', 'danger');
    } finally {
      setLoading(false);
    }
  }

  function wireEvents() {
    if (els.prevBtn) {
      els.prevBtn.addEventListener('click', () => {
        if (state.page > 1 && !state.loading) {
          state.page -= 1;
          fetchExecutions();
        }
      });
    }
    if (els.nextBtn) {
      els.nextBtn.addEventListener('click', () => {
        if (state.page < state.pages && !state.loading) {
          state.page += 1;
          fetchExecutions();
        }
      });
    }
    if (els.tableBody) {
      els.tableBody.addEventListener('click', handleRowAction);
    }
    if (els.refreshBtn) {
      els.refreshBtn.addEventListener('click', () => {
        if (!state.loading) fetchExecutions();
      });
    }

    // Lazy-load when tab is shown
    if (els.tabBtn) {
      els.tabBtn.addEventListener('click', () => {
        if (!state._loadedOnce) {
          state._loadedOnce = true;
          fetchExecutions();
        }
      });
    }

    // If the tab is already active (on hard refresh), load immediately
    const tabPane = document.getElementById('test-executions-tab');
    if (tabPane && tabPane.classList.contains('show') && tabPane.classList.contains('active')) {
      state._loadedOnce = true;
      fetchExecutions();
    }
  }

  document.addEventListener('DOMContentLoaded', () => {
    els.tableBody = byId('executions-table-body');
    els.summary = byId('executions-summary');
    els.prevBtn = byId('exec-prev');
    els.nextBtn = byId('exec-next');
    els.feedback = byId('exec-feedback');
    els.tabBtn = byId('test-executions-tab-btn');
    els.refreshBtn = byId('exec-refresh');
    wireEvents();
  });

  // Expose a tiny API for manual refresh from elsewhere if needed
  window.TestExecutions = {
    refresh: () => { if (!state.loading) fetchExecutions(); }
  };
})();

