// UI Utility functions for Mobile App Automation Tool

// Show a loading overlay with a custom message
async function showLoading(message = 'Loading...') {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingMessage = document.getElementById('loadingMessage');
    if (loadingOverlay && loadingMessage) {
        loadingMessage.textContent = message;
        loadingOverlay.classList.remove('d-none');
    }
}

// Hide the loading overlay
async function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('d-none');
    }
}

// Show a toast notification
function showToast(title, message, type = 'info', delay = 5000) {
    // Create a unique ID for the toast
    const toastId = 'toast-' + Date.now();

    // Determine the icon and color based on type
    let icon, bgClass;
    switch(type) {
        case 'success':
            icon = '<i class="bi bi-check-circle-fill me-2"></i>';
            bgClass = 'bg-success';
            break;
        case 'error':
            icon = '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
            bgClass = 'bg-danger';
            break;
        case 'warning':
            icon = '<i class="bi bi-exclamation-circle-fill me-2"></i>';
            bgClass = 'bg-warning';
            break;
        default:
            icon = '<i class="bi bi-info-circle-fill me-2"></i>';
            bgClass = 'bg-info';
    }

    // Create the toast HTML
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="${delay}">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${icon}${title}</strong>: ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    // Add the toast to the container
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    toastContainer.innerHTML += toastHtml;

    // Initialize and show the toast
    const toastElement = document.getElementById(toastId);
    if (toastElement) {
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    }
}

// Update UI for connected state
function updateUIForConnectedState(isConnected) {
    const connectBtn = document.getElementById('connectBtn');
    const deviceSelect = document.getElementById('deviceSelect');
    const refreshDevicesBtn = document.getElementById('refreshDevices');
    const elements = document.querySelectorAll('[data-requires-connection="true"]');
    const deviceScreenHeader = document.querySelector('.card-header:has(.device-screen-title)') ||
                              document.querySelector('.card-header.bg-primary') ||
                              document.querySelector('.card-header.bg-success');
    const deviceScreenTitle = document.querySelector('.device-screen-title');

    if (isConnected) {
        if (connectBtn) {
            connectBtn.textContent = 'Disconnect';
            connectBtn.classList.remove('btn-primary');
            connectBtn.classList.add('btn-danger');
        }
        if (deviceSelect) deviceSelect.disabled = true;
        if (refreshDevicesBtn) refreshDevicesBtn.disabled = true;
        elements.forEach(el => el.disabled = false);

        // Update Device Screen header to show connected state
        if (deviceScreenHeader) {
            deviceScreenHeader.classList.remove('bg-primary');
            deviceScreenHeader.classList.add('bg-success');
        }
        if (deviceScreenTitle) {
            deviceScreenTitle.style.color = 'white';
        }
    } else {
        if (connectBtn) {
            connectBtn.textContent = 'Connect';
            connectBtn.classList.remove('btn-danger');
            connectBtn.classList.add('btn-primary');
        }
        if (deviceSelect) deviceSelect.disabled = false;
        if (refreshDevicesBtn) refreshDevicesBtn.disabled = false;
        elements.forEach(el => el.disabled = true);

        // Update Device Screen header to show disconnected state
        if (deviceScreenHeader) {
            deviceScreenHeader.classList.remove('bg-success');
            deviceScreenHeader.classList.add('bg-primary');
        }
        if (deviceScreenTitle) {
            deviceScreenTitle.style.color = 'white';
        }
    }
}

// Disable interaction buttons
function disableInteractions() {
    const buttons = document.querySelectorAll('#executeActions, #stopExecution, #clearActions, #refreshScreenBtn, #webInspectorBtn');
    buttons.forEach(btn => {
        btn.disabled = true;
        btn.classList.add('disabled-interaction');
    });
}

// Enable interaction buttons
function enableInteractions() {
    const buttons = document.querySelectorAll('#executeActions, #stopExecution, #clearActions, #refreshScreenBtn, #webInspectorBtn');
    buttons.forEach(btn => {
        btn.disabled = false;
        btn.classList.remove('disabled-interaction');
    });
}

// Attach functions to the global window object for compatibility
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.showToast = showToast;
window.updateUIForConnectedState = updateUIForConnectedState;
window.disableInteractions = disableInteractions;
window.enableInteractions = enableInteractions;

// Optionally, still export for environments that support modules
// export { showLoading, hideLoading, showToast, updateUIForConnectedState, disableInteractions, enableInteractions }; 