/**
 * Bulk Import/Export Manager
 * 
 * Handles bulk export and import of all test cases and test suites
 */

class BulkImportExportManager {
    constructor() {
        this.init();
    }

    init() {
        console.log('Initializing Bulk Import/Export Manager');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Test Cases Export/Import
        const exportAllTestCasesBtn = document.getElementById('exportAllTestCasesBtn');
        if (exportAllTestCasesBtn) {
            exportAllTestCasesBtn.addEventListener('click', () => this.exportAllTestCases());
        }

        const importAllTestCasesBtn = document.getElementById('importAllTestCasesBtn');
        if (importAllTestCasesBtn) {
            importAllTestCasesBtn.addEventListener('click', () => this.showImportTestCasesModal());
        }

        // Test Suites Export/Import
        const exportAllTestSuitesBtn = document.getElementById('exportAllTestSuitesBtn');
        if (exportAllTestSuitesBtn) {
            exportAllTestSuitesBtn.addEventListener('click', () => this.exportAllTestSuites());
        }

        const importAllTestSuitesBtn = document.getElementById('importAllTestSuitesBtn');
        if (importAllTestSuitesBtn) {
            importAllTestSuitesBtn.addEventListener('click', () => this.showImportTestSuitesModal());
        }

        // Import form submissions
        const importTestCasesForm = document.getElementById('importTestCasesForm');
        if (importTestCasesForm) {
            importTestCasesForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.importAllTestCases();
            });
        }

        const importTestSuitesForm = document.getElementById('importTestSuitesForm');
        if (importTestSuitesForm) {
            importTestSuitesForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.importAllTestSuites();
            });
        }

        // Import buttons in modals
        const importTestCasesSubmitBtn = document.getElementById('importTestCasesSubmitBtn');
        if (importTestCasesSubmitBtn) {
            importTestCasesSubmitBtn.addEventListener('click', () => this.importAllTestCases());
        }

        const importTestSuitesSubmitBtn = document.getElementById('importTestSuitesSubmitBtn');
        if (importTestSuitesSubmitBtn) {
            importTestSuitesSubmitBtn.addEventListener('click', () => this.importAllTestSuites());
        }
    }

    async exportAllTestCases() {
        try {
            const btn = document.getElementById('exportAllTestCasesBtn');
            const originalHtml = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Exporting...';

            const response = await fetch('/api/test_cases/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            // Get the filename from the Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'test_cases_export.zip';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="?(.+)"?/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showToast('Export Successful', 'All test cases exported successfully', 'success');
            
            btn.disabled = false;
            btn.innerHTML = originalHtml;
        } catch (error) {
            console.error('Export error:', error);
            this.showToast('Export Failed', error.message, 'error');
            
            const btn = document.getElementById('exportAllTestCasesBtn');
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-download"></i> Export All';
        }
    }

    async exportAllTestSuites() {
        try {
            const btn = document.getElementById('exportAllTestSuitesBtn');
            const originalHtml = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Exporting...';

            const response = await fetch('/api/test_suites/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            // Get the filename from the Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'test_suites_export.zip';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="?(.+)"?/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showToast('Export Successful', 'All test suites exported successfully', 'success');
            
            btn.disabled = false;
            btn.innerHTML = originalHtml;
        } catch (error) {
            console.error('Export error:', error);
            this.showToast('Export Failed', error.message, 'error');
            
            const btn = document.getElementById('exportAllTestSuitesBtn');
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-download"></i> Export All';
        }
    }

    showImportTestCasesModal() {
        const modal = new bootstrap.Modal(document.getElementById('importTestCasesModal'));
        modal.show();
    }

    showImportTestSuitesModal() {
        const modal = new bootstrap.Modal(document.getElementById('importTestSuitesModal'));
        modal.show();
    }

    async importAllTestCases() {
        try {
            const fileInput = document.getElementById('testCasesFile');
            const conflictResolution = document.getElementById('testCasesConflictResolution').value;

            if (!fileInput.files || fileInput.files.length === 0) {
                this.showToast('No File Selected', 'Please select a ZIP file to import', 'warning');
                return;
            }

            const btn = document.getElementById('importTestCasesSubmitBtn');
            const originalHtml = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Importing...';

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('conflict_resolution', conflictResolution);

            const response = await fetch('/api/test_cases/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Import failed');
            }

            this.showToast('Import Successful', result.message || 'Test cases imported successfully', 'success');
            
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('importTestCasesModal'));
            modal.hide();

            // Reset the form
            document.getElementById('importTestCasesForm').reset();

            // Refresh the test cases list
            if (window.app && window.app.loadTestCases) {
                window.app.loadTestCases();
            }

            btn.disabled = false;
            btn.innerHTML = originalHtml;
        } catch (error) {
            console.error('Import error:', error);
            this.showToast('Import Failed', error.message, 'error');
            
            const btn = document.getElementById('importTestCasesSubmitBtn');
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-upload"></i> Import';
            }
        }
    }

    async importAllTestSuites() {
        try {
            const fileInput = document.getElementById('testSuitesFile');
            const conflictResolution = document.getElementById('testSuitesConflictResolution').value;

            if (!fileInput.files || fileInput.files.length === 0) {
                this.showToast('No File Selected', 'Please select a ZIP file to import', 'warning');
                return;
            }

            const btn = document.getElementById('importTestSuitesSubmitBtn');
            const originalHtml = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Importing...';

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('conflict_resolution', conflictResolution);

            const response = await fetch('/api/test_suites/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Import failed');
            }

            this.showToast('Import Successful', result.message || 'Test suites imported successfully', 'success');
            
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('importTestSuitesModal'));
            modal.hide();

            // Reset the form
            document.getElementById('importTestSuitesForm').reset();

            // Refresh the test suites list if available
            // Note: You may need to add a refresh function for test suites

            btn.disabled = false;
            btn.innerHTML = originalHtml;
        } catch (error) {
            console.error('Import error:', error);
            this.showToast('Import Failed', error.message, 'error');
            
            const btn = document.getElementById('importTestSuitesSubmitBtn');
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-upload"></i> Import';
            }
        }
    }

    showToast(title, message, type = 'info') {
        // Use the global showToast function if available
        if (window.showToast) {
            window.showToast(title, message, type);
        } else {
            // Fallback to alert
            alert(`${title}: ${message}`);
        }
    }
}

// Initialize the manager when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.bulkImportExportManager = new BulkImportExportManager();
});

