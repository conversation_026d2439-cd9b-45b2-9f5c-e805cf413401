/**
 * Detachable Device Screen Manager
 * Creates a completely separate window for device screen monitoring
 * with platform identification and independent operation
 */
class DetachableDeviceScreenManager {
    constructor(platform = 'iOS') {
        this.platform = platform;
        this.detachedWindow = null;
        this.deviceScreen = null;
        this.overlayCanvas = null;
        this.loadingOverlay = null;
        this.observer = null;
        this.syncInterval = null;
        this.isDetached = false;
        this.windowFeatures = 'width=400,height=700,scrollbars=no,resizable=yes,status=no,location=no,toolbar=no,menubar=no';
        
        // Find the original device screen elements
        this.findDeviceScreenElements();
        
        // Set up event listeners
        this.setupEventListeners();
    }

    /**
     * Find the original device screen elements in the main window
     */
    findDeviceScreenElements() {
        this.deviceScreen = document.getElementById('deviceScreen');
        this.overlayCanvas = document.getElementById('overlayCanvas');
        this.loadingOverlay = document.querySelector('.loading-overlay');
        
        if (!this.deviceScreen) {
            console.warn('Device screen element not found');
        }
    }

    /**
     * Set up event listeners for window communication
     */
    setupEventListeners() {
        // Listen for window close events
        window.addEventListener('beforeunload', () => {
            this.closeDetachedWindow();
        });
        
        // Listen for messages from the detached window
        window.addEventListener('message', (event) => {
            if (event.data.type === 'DETACHED_WINDOW_CLOSED') {
                this.handleDetachedWindowClosed();
            }
        });
    }

    /**
     * Create and open the detached device screen window
     */
    detachDeviceScreen() {
        if (this.isDetached || !this.deviceScreen) {
            console.warn('Device screen already detached or not found');
            return;
        }

        // Create the detached window
        this.detachedWindow = window.open('', 'DeviceScreen', this.windowFeatures);
        
        if (!this.detachedWindow) {
            alert('Popup blocked! Please allow popups for this site to use the detachable device screen.');
            return;
        }

        // Set up the detached window content
        this.setupDetachedWindowContent();
        
        // Set up synchronization
        this.setupDeviceScreenSync();
        
        this.isDetached = true;
        
        // Hide the original device screen container (optional)
        const originalContainer = document.querySelector('.device-screen-container');
        if (originalContainer) {
            originalContainer.style.opacity = '0.3';
            originalContainer.style.pointerEvents = 'none';
        }
        
        console.log(`${this.platform} device screen detached successfully`);
    }

    /**
     * Set up the content of the detached window
     */
    setupDetachedWindowContent() {
        const doc = this.detachedWindow.document;
        
        // Set up the HTML structure
        doc.open();
        doc.write(`
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${this.platform} Device Screen</title>
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        background: #1a1a1a;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        overflow: hidden;
                    }
                    
                    .header {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 10px 15px;
                        text-align: center;
                        font-weight: 600;
                        font-size: 14px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        position: relative;
                    }
                    
                    .platform-badge {
                        display: inline-block;
                        background: rgba(255,255,255,0.2);
                        padding: 4px 12px;
                        border-radius: 20px;
                        font-size: 12px;
                        margin-left: 10px;
                    }
                    
                    .close-btn {
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .close-btn:hover {
                        background: rgba(255,255,255,0.3);
                    }
                    
                    .device-container {
                        position: relative;
                        width: 100%;
                        height: calc(100vh - 44px);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: #2a2a2a;
                    }
                    
                    .device-screen {
                        max-width: 100%;
                        max-height: 100%;
                        border-radius: 8px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                        background: #000;
                    }
                    
                    .loading-overlay {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: #888;
                        font-size: 14px;
                        text-align: center;
                    }
                    
                    .status-indicator {
                        position: absolute;
                        top: 10px;
                        left: 10px;
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: #4CAF50;
                        animation: pulse 2s infinite;
                    }
                    
                    @keyframes pulse {
                        0% { opacity: 1; }
                        50% { opacity: 0.5; }
                        100% { opacity: 1; }
                    }
                    
                    .error-state {
                        background: #f44336 !important;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <span>${this.platform} Device Screen</span>
                    <span class="platform-badge">${this.platform}</span>
                    <button class="close-btn" onclick="closeWindow()" title="Close Window">×</button>
                </div>
                <div class="device-container">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <img id="detachedDeviceScreen" class="device-screen" alt="${this.platform} Device Screen" />
                    <div class="loading-overlay" id="loadingOverlay">
                        Connecting to ${this.platform} device...
                    </div>
                </div>
                
                <script>
                    function closeWindow() {
                        // Notify parent window
                        if (window.opener) {
                            window.opener.postMessage({type: 'DETACHED_WINDOW_CLOSED'}, '*');
                        }
                        window.close();
                    }
                    
                    // Handle window close event
                    window.addEventListener('beforeunload', function() {
                        if (window.opener) {
                            window.opener.postMessage({type: 'DETACHED_WINDOW_CLOSED'}, '*');
                        }
                    });
                    
                    // Update status indicator based on image load
                    const deviceScreen = document.getElementById('detachedDeviceScreen');
                    const statusIndicator = document.getElementById('statusIndicator');
                    const loadingOverlay = document.getElementById('loadingOverlay');
                    
                    deviceScreen.onload = function() {
                        statusIndicator.classList.remove('error-state');
                        loadingOverlay.style.display = 'none';
                    };
                    
                    deviceScreen.onerror = function() {
                        statusIndicator.classList.add('error-state');
                        loadingOverlay.style.display = 'block';
                        loadingOverlay.textContent = 'Connection lost - Reconnecting...';
                    };
                </script>
            </body>
            </html>
        `);
        doc.close();
        
        // Focus the new window
        this.detachedWindow.focus();
    }

    /**
     * Set up synchronization between original and detached device screens
     */
    setupDeviceScreenSync() {
        if (!this.detachedWindow || !this.deviceScreen) return;
        
        const detachedScreen = this.detachedWindow.document.getElementById('detachedDeviceScreen');
        
        // Initial sync
        if (this.deviceScreen.src) {
            detachedScreen.src = this.deviceScreen.src;
        }
        
        // Set up MutationObserver to watch for changes
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
                    if (this.detachedWindow && !this.detachedWindow.closed) {
                        const detachedScreen = this.detachedWindow.document.getElementById('detachedDeviceScreen');
                        if (detachedScreen && this.deviceScreen) {
                            detachedScreen.src = this.deviceScreen.src;
                        }
                    }
                }
            });
        });
        
        // Start observing
        this.observer.observe(this.deviceScreen, {
            attributes: true,
            attributeFilter: ['src']
        });
        
        // Set up periodic sync as fallback
        this.syncInterval = setInterval(() => {
            if (this.detachedWindow && !this.detachedWindow.closed) {
                const detachedScreen = this.detachedWindow.document.getElementById('detachedDeviceScreen');
                if (detachedScreen && this.deviceScreen && detachedScreen.src !== this.deviceScreen.src) {
                    detachedScreen.src = this.deviceScreen.src;
                }
            } else {
                // Window was closed, clean up
                this.handleDetachedWindowClosed();
            }
        }, 1000);
    }

    /**
     * Handle when the detached window is closed
     */
    handleDetachedWindowClosed() {
        this.isDetached = false;
        
        // Clean up observers and intervals
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
        
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        
        // Restore the original device screen container
        const originalContainer = document.querySelector('.device-screen-container');
        if (originalContainer) {
            originalContainer.style.opacity = '1';
            originalContainer.style.pointerEvents = 'auto';
        }
        
        this.detachedWindow = null;
        console.log(`${this.platform} device screen reattached`);
    }

    /**
     * Close the detached window programmatically
     */
    closeDetachedWindow() {
        if (this.detachedWindow && !this.detachedWindow.closed) {
            this.detachedWindow.close();
        }
        this.handleDetachedWindowClosed();
    }

    /**
     * Check if the device screen is currently detached
     */
    isDeviceScreenDetached() {
        return this.isDetached && this.detachedWindow && !this.detachedWindow.closed;
    }

    /**
     * Toggle between attached and detached states
     */
    toggleDetachment() {
        if (this.isDeviceScreenDetached()) {
            this.closeDetachedWindow();
        } else {
            this.detachDeviceScreen();
        }
    }

    /**
     * Open a dedicated capture window with snipping and save-to-DB workflow
     */
    openCaptureWindow(actionType = 'generic') {
        // Create popup
        const w = window.open('', 'CaptureFromScreen', this.windowFeatures);
        if (!w) {
            alert('Popup blocked! Please allow popups to capture from screen.');
            return;
        }
        const doc = w.document;
        const platform = this.platform;
        doc.open();
        doc.write(`
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${platform} Capture</title>
                <style>
                    body { margin:0; background:#111; color:#eee; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif; }
                    .header { display:flex; align-items:center; justify-content:space-between; padding:10px 12px; background:linear-gradient(135deg,#667eea,#764ba2); }
                    .title { font-weight:600; font-size:14px; }
                    .help { font-size:12px; opacity:.9; }
                    .wrap { position:relative; width:auto; height:auto; display:inline-block; margin:0; }
                    img#capScreen { display:block; max-width:none; max-height:none; background:#000; border-radius:0; box-shadow:none; z-index:0; }
                    canvas#capOverlay { position:absolute; left:0; top:0; cursor: crosshair; z-index:10; pointer-events:auto; }
                    .toolbar { display:flex; gap:8px; align-items:center; }
                    .modal { position:fixed; inset:0; background:rgba(0,0,0,.6); display:none; align-items:center; justify-content:center; z-index:10000; pointer-events:auto; }
                    .modal .panel { background:#1f1f1f; padding:16px; border-radius:8px; width:min(420px, 92vw); box-shadow:0 8px 30px rgba(0,0,0,.5); }
                    .row { margin-top:10px; display:flex; gap:8px; align-items:center; }
                    input[type=text]{ flex:1; padding:8px; border:1px solid #333; background:#111; color:#eee; border-radius:6px; }
                    button { padding:6px 10px; border:1px solid #3f51b5; background:#3f51b5; color:white; border-radius:6px; cursor:pointer; }
                    button.secondary{ background:#333; border-color:#444; }
                    .hint { font-size:11px; opacity:.8; margin-top:6px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <div>
                        <div class="title">${platform} - Capture from Screen</div>
                        <div class="help">Drag to select. ESC to cancel. Image updates every ~2s.</div>
                    </div>
                    <div class="toolbar">
                        <button id="refreshBtn" title="Refresh screenshot">Refresh</button>
                        <button id="closeBtn" class="secondary" title="Close">Close</button>
                    </div>
                </div>
                <div class="wrap">
                    <img id="capScreen" alt="Device Screen" />
                    <canvas id="capOverlay"></canvas>
                </div>
                <div id="previewModal" class="modal" aria-modal="true" role="dialog">
                    <div class="panel">
                        <div style="font-weight:600; margin-bottom:8px;">Preview & Save</div>
                        <canvas id="previewCanvas" style="max-width:100%; border-radius:6px; background:#000"></canvas>
                        <div class="row">
                            <input id="imageName" type="text" placeholder="Image name (e.g., login_button)" />
                        </div>
                        <div class="row" style="justify-content:flex-end;">
                            <button id="cancelBtn" class="secondary">Cancel</button>
                            <button id="confirmBtn">Confirm</button>
                        </div>
                        <div class="hint">Saved to database and reference_images folder; dropdowns will refresh automatically.</div>
                    </div>
                </div>
                <script>
                    (function(){
                        const openerWin = window.opener;
                        const img = document.getElementById('capScreen');
                        const overlay = document.getElementById('capOverlay');
                        const ctx = overlay.getContext('2d');
                        const modal = document.getElementById('previewModal');
                        const pcanvas = document.getElementById('previewCanvas');
                        const pctx = pcanvas.getContext('2d');
                        const nameInput = document.getElementById('imageName');
                        const refreshBtn = document.getElementById('refreshBtn');
                        const closeBtn = document.getElementById('closeBtn');
                        const cancelBtn = document.getElementById('cancelBtn');
                        const confirmBtn = document.getElementById('confirmBtn');

                        let startX=0, startY=0, selW=0, selH=0, dragging=false;
                        function setImgToLatest(){
                            try {
                                const openerWin = window.opener;
                                const origin = openerWin && openerWin.location ? openerWin.location.origin : (window.location && window.location.origin ? window.location.origin : '');
                                const deviceId = (openerWin && openerWin.app && openerWin.app.deviceId) || (openerWin && openerWin.sessionStorage && openerWin.sessionStorage.getItem('connectedDeviceId')) || '';
                                const clientSessionId = (openerWin && openerWin.app && openerWin.app.clientSessionId) || '';
                                const ts = Date.now();
                                let url = origin ? (origin + '/screenshot?t=' + ts) : ('/screenshot?t=' + ts);
                                if (deviceId && clientSessionId) {
                                    url = origin + '/screenshot?deviceId=' + encodeURIComponent(deviceId) + '&clientSessionId=' + encodeURIComponent(clientSessionId) + '&t=' + ts;
                                }
                                img.src = url;
                            } catch (e) {
                                img.src = '/screenshot?t=' + Date.now();
                            }
                        }
                        setImgToLatest();
                        const interval = setInterval(setImgToLatest, 2000);

                        function syncLayout(){
                            const w = img.naturalWidth; const h = img.naturalHeight;
                            const wrapEl = document.querySelector('.wrap');
                            if (wrapEl){ wrapEl.style.width = w + 'px'; wrapEl.style.height = h + 'px'; }
                            overlay.width = w; overlay.height = h;
                            overlay.style.width = w + 'px'; overlay.style.height = h + 'px';
                            overlay.style.zIndex = '10'; overlay.style.pointerEvents = 'auto';
                            img.style.width = w + 'px'; img.style.height = h + 'px';
                            try {
                                const headerH = document.querySelector('.header')?.offsetHeight || 0;
                                window.resizeTo(w + 16, h + headerH + 16);
                            } catch(e) {}
                        }
                        img.onload = syncLayout;
                        const resizeObs = new ResizeObserver(()=>syncLayout()); resizeObs.observe(document.body);
                        img.onload = syncLayout;

                        function clearOverlay(){ ctx.clearRect(0,0,overlay.width, overlay.height); }
                        function drawRect(){
                            clearOverlay();
                            ctx.save();
                            ctx.lineWidth = 2;
                            ctx.strokeStyle = '#667eea';
                            if (ctx.setLineDash) { ctx.setLineDash([6,4]); }
                            ctx.beginPath();
                            ctx.rect(startX, startY, selW, selH);
                            ctx.stroke();
                            ctx.restore();
                        }

                        overlay.addEventListener('mousedown', e=>{ e.preventDefault(); e.stopPropagation(); dragging=true; const r=overlay.getBoundingClientRect(); startX = Math.max(0, Math.min(e.clientX - r.left, r.width)); startY = Math.max(0, Math.min(e.clientY - r.top, r.height)); selW=0; selH=0; drawRect(); });
                        overlay.addEventListener('mousemove', e=>{ if(!dragging) return; e.preventDefault(); const r=overlay.getBoundingClientRect(); const cx = Math.max(0, Math.min(e.clientX - r.left, r.width)); const cy = Math.max(0, Math.min(e.clientY - r.top, r.height)); selW = cx - startX; selH = cy - startY; drawRect(); });
                        overlay.addEventListener('mouseup', e=>{ if(!dragging) return; e.preventDefault(); dragging=false; openPreview(); });
                        window.addEventListener('keydown', e=>{ if(e.key==='Escape'){ dragging=false; clearOverlay(); }});

                        function openPreview(){
                            const sx = selW<0 ? startX+selW : startX; const sy = selH<0 ? startY+selH : startY; const sw = Math.abs(selW); const sh = Math.abs(selH);
                            if (sw < 2 || sh < 2) return;
                            pcanvas.width = sw; pcanvas.height = sh;
                            const off = document.createElement('canvas'); off.width = overlay.width; off.height = overlay.height;
                            const offctx = off.getContext('2d'); offctx.drawImage(img, 0, 0, overlay.width, overlay.height);
                            const imgData = offctx.getImageData(sx, sy, sw, sh);
                            pctx.putImageData(imgData, 0, 0);
                            dragging = false;
                            overlay.style.pointerEvents = 'none';
                            overlay.style.cursor = 'default';
                            modal.style.display = 'flex';
                            nameInput.focus();
                        }

                        refreshBtn.onclick = setImgToLatest;
                        closeBtn.onclick = ()=>{ window.close(); };
                        cancelBtn.onclick = ()=>{
                            modal.style.display='none';
                            clearOverlay();
                            // Restore snipping mode
                            overlay.style.pointerEvents = 'auto';
                            overlay.style.cursor = 'crosshair';
                        };
                        confirmBtn.onclick = async ()=>{
                            const name = (nameInput.value||'').trim();
                            if(!name){ alert('Please enter an image name.'); return; }
                            try{
                                const dataUrl = pcanvas.toDataURL('image/png');
                                const resp = await fetch('/api/reference_images/save', {
                                    method:'POST', headers:{'Content-Type':'application/json'},
                                    body: JSON.stringify({ image_name: name, image_data: dataUrl, action_type: ${JSON.stringify(actionType)} })
                                });
                                const json = await resp.json();
                                if(!resp.ok || json.status!=='success'){
                                    alert(json.message || 'Failed to save image');
                                    return;
                                }
                                if(openerWin){ openerWin.dispatchEvent(new CustomEvent('referenceImagesUpdated', { detail: { filename: json.filename } })); }
                                // Keep popup open for multiple captures: hide modal and re-enable snipping
                                modal.style.display='none';
                                clearOverlay();
                                overlay.style.pointerEvents = 'auto';
                                overlay.style.cursor = 'crosshair';
                                // Optional toast
                                try { if (w && w.Toast) { w.Toast.success('Saved ' + (json && json.filename ? json.filename : name)); } } catch(_) {}
                            }catch(err){
                                alert('Error saving image: '+ err);
                            }
                        };

                        window.addEventListener('beforeunload', ()=>{ clearInterval(interval); });
                    })();
                <\/script>
            </body>
            </html>
        `);
        doc.close();
        w.focus();
    }
}

// Export for use in other modules
window.DetachableDeviceScreenManager = DetachableDeviceScreenManager;
