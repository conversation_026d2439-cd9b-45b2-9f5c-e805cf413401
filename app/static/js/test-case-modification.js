/**
 * JSONTestCaseEditor - A comprehensive JSON editor for test case modification
 * Provides search functionality, validation, and save/revert capabilities
 */
class JSONTestCaseEditor {
    constructor() {
        this.currentTestCase = null;
        this.currentFilename = null;
        this.searchMatches = [];
        this.currentMatchIndex = -1;
        this.originalSearchTerm = '';
        this.isModified = false;
        
        this.initializeModal();
        this.bindEvents();
    }

    /**
     * Initialize the JSON editor modal
     */
    initializeModal() {
        // Check if modal already exists
        if (document.getElementById('jsonEditorModal')) {
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="jsonEditorModal" tabindex="-1" aria-labelledby="jsonEditorModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-fullscreen-lg-down">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="jsonEditorModalLabel">
                                <i class="bi bi-code-square"></i> JSON Test Case Editor
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <!-- Search Bar -->
                            <div class="border-bottom p-3">
                                <div class="row g-2 align-items-center">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                                            <input type="text" class="form-control" id="jsonSearchInput" placeholder="Search in JSON...">
                                            <button class="btn btn-outline-secondary" type="button" id="jsonSearchPrev" title="Previous match">
                                                <i class="bi bi-chevron-up"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" type="button" id="jsonSearchNext" title="Next match">
                                                <i class="bi bi-chevron-down"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="jsonSearchCaseSensitive">
                                            <label class="form-check-label" for="jsonSearchCaseSensitive">
                                                Case sensitive
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted" id="jsonSearchStatus">Ready to search</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- JSON Editor Container -->
                            <div class="json-editor-container" style="height: 60vh;">
                                <div id="jsonEditorLineNumbers"></div>
                                <textarea id="jsonEditor" class="form-control" style="height: 100%; font-family: 'Courier New', Consolas, monospace;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="me-auto">
                                <small class="text-muted" id="jsonEditorStatus">Ready</small>
                            </div>
                            <button type="button" class="btn btn-secondary" id="jsonValidateBtn">
                                <i class="bi bi-check-circle"></i> Validate
                            </button>
                            <button type="button" class="btn btn-warning" id="jsonRevertBtn">
                                <i class="bi bi-arrow-counterclockwise"></i> Revert
                            </button>
                            <button type="button" class="btn btn-success" id="jsonSaveBtn">
                                <i class="bi bi-save"></i> Validate & Save
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Search functionality
        document.addEventListener('input', (e) => {
            if (e.target.id === 'jsonSearchInput') {
                this.performSearch();
            }
        });

        document.addEventListener('change', (e) => {
            if (e.target.id === 'jsonSearchCaseSensitive') {
                this.performSearch();
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.id === 'jsonSearchNext' || e.target.closest('#jsonSearchNext')) {
                this.nextMatch();
            } else if (e.target.id === 'jsonSearchPrev' || e.target.closest('#jsonSearchPrev')) {
                this.previousMatch();
            } else if (e.target.id === 'jsonValidateBtn') {
                this.validateJSON();
            } else if (e.target.id === 'jsonSaveBtn') {
                this.saveJSON();
            } else if (e.target.id === 'jsonRevertBtn') {
                this.revertChanges();
            }
        });

        // JSON editor events
        document.addEventListener('input', (e) => {
            if (e.target.id === 'jsonEditor') {
                this.updateLineNumbers();
                this.markAsModified();
            }
        });

        document.addEventListener('scroll', (e) => {
            if (e.target.id === 'jsonEditor') {
                this.syncLineNumbers();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.target.id === 'jsonEditor' || e.target.id === 'jsonSearchInput') {
                if (e.ctrlKey || e.metaKey) {
                    if (e.key === 's') {
                        e.preventDefault();
                        this.saveJSON();
                    } else if (e.key === 'f') {
                        e.preventDefault();
                        document.getElementById('jsonSearchInput').focus();
                    }
                }
                if (e.key === 'F3' || (e.key === 'g' && (e.ctrlKey || e.metaKey))) {
                    e.preventDefault();
                    if (e.shiftKey) {
                        this.previousMatch();
                    } else {
                        this.nextMatch();
                    }
                }
            }
        });
    }

    /**
     * Open the JSON editor with test case data
     */
    async openEditor(filename, searchTerm = '') {
        try {
            this.currentFilename = filename;
            this.originalSearchTerm = searchTerm;
            
            // Create backup before editing
            await this.createBackup(filename);
            
            // Load test case data
            const response = await fetch(`/api/test_cases/${encodeURIComponent(filename)}`);
            if (!response.ok) {
                throw new Error(`Failed to load test case: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.currentTestCase = data;
            
            // Format JSON for display
            const jsonText = JSON.stringify(data, null, 2);
            
            // Set editor content
            const editor = document.getElementById('jsonEditor');
            editor.value = jsonText;
            
            // Update line numbers
            this.updateLineNumbers();
            
            // Set modal title
            const modalTitle = document.getElementById('jsonEditorModalLabel');
            modalTitle.innerHTML = `<i class="bi bi-code-square"></i> JSON Editor: ${filename}`;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('jsonEditorModal'));
            modal.show();
            
            // Auto-search if search term provided
            if (searchTerm) {
                setTimeout(() => {
                    document.getElementById('jsonSearchInput').value = searchTerm;
                    this.performSearch();
                }, 500);
            }
            
            this.isModified = false;
            this.updateStatus('Ready');
            
        } catch (error) {
            console.error('Error opening JSON editor:', error);
            this.showError('Failed to open JSON editor: ' + error.message);
        }
    }

    /**
     * Create backup of test case before editing
     */
    async createBackup(filename) {
        try {
            const response = await fetch('/api/test_cases/json_backup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filename: filename,
                    json_data: this.currentTestCase
                })
            });
            
            if (!response.ok) {
                console.warn('Failed to create backup:', response.statusText);
            }
        } catch (error) {
            console.warn('Error creating backup:', error);
        }
    }

    /**
     * Perform search in JSON content
     */
    performSearch() {
        const searchInput = document.getElementById('jsonSearchInput');
        const editor = document.getElementById('jsonEditor');
        const caseSensitive = document.getElementById('jsonSearchCaseSensitive').checked;
        
        const searchTerm = searchInput.value;
        const content = editor.value;
        
        // Clear previous matches
        this.searchMatches = [];
        this.currentMatchIndex = -1;
        
        if (!searchTerm) {
            this.updateSearchStatus('Ready to search');
            this.clearHighlights();
            return;
        }
        
        // Find all matches
        const flags = caseSensitive ? 'g' : 'gi';
        const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), flags);
        let match;
        
        while ((match = regex.exec(content)) !== null) {
            this.searchMatches.push({
                start: match.index,
                end: match.index + match[0].length,
                text: match[0]
            });
        }
        
        if (this.searchMatches.length > 0) {
            this.currentMatchIndex = 0;
            this.highlightCurrentMatch();
            this.updateSearchStatus(`${this.searchMatches.length} matches found`);
        } else {
            this.updateSearchStatus('No matches found');
            this.clearHighlights();
        }
    }

    /**
     * Navigate to next search match
     */
    nextMatch() {
        if (this.searchMatches.length === 0) return;
        
        this.currentMatchIndex = (this.currentMatchIndex + 1) % this.searchMatches.length;
        this.highlightCurrentMatch();
    }

    /**
     * Navigate to previous search match
     */
    previousMatch() {
        if (this.searchMatches.length === 0) return;
        
        this.currentMatchIndex = this.currentMatchIndex <= 0 ? 
            this.searchMatches.length - 1 : this.currentMatchIndex - 1;
        this.highlightCurrentMatch();
    }

    /**
     * Highlight current search match
     */
    highlightCurrentMatch() {
        if (this.currentMatchIndex < 0 || this.currentMatchIndex >= this.searchMatches.length) return;
        
        const editor = document.getElementById('jsonEditor');
        const match = this.searchMatches[this.currentMatchIndex];
        
        // Set selection to current match
        editor.setSelectionRange(match.start, match.end);
        editor.focus();
        
        // Update status
        this.updateSearchStatus(`Match ${this.currentMatchIndex + 1} of ${this.searchMatches.length}`);
    }

    /**
     * Clear search highlights
     */
    clearHighlights() {
        // Implementation would depend on highlighting method used
        // For now, just clear selection
        const editor = document.getElementById('jsonEditor');
        editor.setSelectionRange(0, 0);
    }

    /**
     * Update search status display
     */
    updateSearchStatus(message) {
        const statusElement = document.getElementById('jsonSearchStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    /**
     * Update line numbers display
     */
    updateLineNumbers() {
        const editor = document.getElementById('jsonEditor');
        const lineNumbers = document.getElementById('jsonEditorLineNumbers');
        
        if (!editor || !lineNumbers) return;
        
        const lines = editor.value.split('\n');
        const lineNumbersHTML = lines.map((_, index) => 
            `<div>${index + 1}</div>`
        ).join('');
        
        lineNumbers.innerHTML = lineNumbersHTML;
    }

    /**
     * Sync line numbers with editor scroll
     */
    syncLineNumbers() {
        const editor = document.getElementById('jsonEditor');
        const lineNumbers = document.getElementById('jsonEditorLineNumbers');
        
        if (!editor || !lineNumbers) return;
        
        lineNumbers.scrollTop = editor.scrollTop;
    }

    /**
     * Mark editor as modified
     */
    markAsModified() {
        this.isModified = true;
        this.updateStatus('Modified');
    }

    /**
     * Validate JSON content
     */
    async validateJSON() {
        try {
            const editor = document.getElementById('jsonEditor');
            const jsonText = editor.value;
            
            // Parse JSON to check syntax
            let testCaseData;
            try {
                testCaseData = JSON.parse(jsonText);
            } catch (parseError) {
                this.showError('Invalid JSON syntax: ' + parseError.message);
                return false;
            }
            
            // Validate with backend
            const response = await fetch('/api/test_cases/validate_json', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test_case: testCaseData,
                    filename: this.currentFilename
                })
            });
            
            const result = await response.json();
            
            if (response.ok && result.status === 'success') {
                this.showSuccess('JSON validation passed');
                return true;
            } else {
                const errors = result.validation_errors || [result.error || 'Validation failed'];
                this.showError('Validation errors:\n' + errors.join('\n'));
                return false;
            }
            
        } catch (error) {
            console.error('Error validating JSON:', error);
            this.showError('Error during validation: ' + error.message);
            return false;
        }
    }

    /**
     * Save JSON content
     */
    async saveJSON() {
        try {
            // Validate first
            const isValid = await this.validateJSON();
            if (!isValid) {
                return;
            }
            
            const editor = document.getElementById('jsonEditor');
            const jsonText = editor.value;
            const testCaseData = JSON.parse(jsonText);
            
            // Save with backend
            const response = await fetch('/api/test_cases/save_json', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test_case: testCaseData,
                    filename: this.currentFilename
                })
            });
            
            const result = await response.json();
            
            if (response.ok && result.status === 'success') {
                this.showSuccess('Test case saved successfully');
                this.isModified = false;
                this.updateStatus('Saved');
                
                // Refresh search results if we came from search
                if (window.testCaseManager && typeof window.testCaseManager.refreshSearchResults === 'function') {
                    window.testCaseManager.refreshSearchResults();
                }
                
                // Close modal after short delay
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('jsonEditorModal'));
                    if (modal) {
                        modal.hide();
                    }
                }, 1500);
                
            } else {
                const errors = result.validation_errors || [result.error || 'Save failed'];
                this.showError('Save failed:\n' + errors.join('\n'));
            }
            
        } catch (error) {
            console.error('Error saving JSON:', error);
            this.showError('Error during save: ' + error.message);
        }
    }

    /**
     * Revert changes to original content
     */
    revertChanges() {
        if (!this.currentTestCase) return;
        
        const editor = document.getElementById('jsonEditor');
        const jsonText = JSON.stringify(this.currentTestCase, null, 2);
        editor.value = jsonText;
        
        this.updateLineNumbers();
        this.isModified = false;
        this.updateStatus('Reverted');
        
        this.showSuccess('Changes reverted to original');
    }

    /**
     * Update status display
     */
    updateStatus(message) {
        const statusElement = document.getElementById('jsonEditorStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        // Create toast if toast container exists
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            alert(message); // Fallback to alert
            return;
        }
        
        const toastId = 'toast_' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
        
        const toastHTML = `
            <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${bgClass} text-white border-0">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <strong class="me-auto">JSON Editor</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message.replace(/\n/g, '<br>')}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: type !== 'error',
            delay: type === 'error' ? 10000 : 3000
        });
        
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// Initialize JSON editor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.jsonTestCaseEditor = new JSONTestCaseEditor();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JSONTestCaseEditor;
}