/* Styles for the Test Cases Tab */

/* Header styling - ensure white text */
.card-header.bg-primary h5,
.card-header.bg-primary h5 i {
    color: white !important;
}

/* List View Styling */
#testCasesList {
    list-style: none;
    padding: 0;
}

.test-case-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6; /* Add border */
    margin-bottom: 0.5rem;
    background-color: #fff;
    border-radius: 0.375rem; /* Use Bootstrap's default radius */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease-in-out;
}

.test-case-list-item:hover {
    background-color: #f8f9fa;
}

.test-case-info {
    flex-grow: 1;
    margin-right: 1rem;
}

.test-case-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #343a40;
    margin-bottom: 0.25rem;
    display: block; /* Ensure it takes block space */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.test-case-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.test-case-actions .btn {
    margin-left: 0.5rem;
    min-width: 80px; /* Ensure buttons have some minimum width */
}

/* Refresh Button Background */
#refreshTestCasesBtn {
    background-color: #e9ecef; /* Light gray background */
    border-color: #ced4da;
    color: #495057;
}

#refreshTestCasesBtn:hover {
    background-color: #dee2e6;
    border-color: #adb5bd;
}

/* Test Case Search Input Border */
#testCaseSearch {
    border: 1px solid #ced4da; /* Standard Bootstrap border */
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075); /* Subtle inner shadow */
}

#testCaseSearch:focus {
    border-color: #86b7fe; /* Bootstrap focus color */
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); /* Bootstrap focus shadow */
}

/* Ensure action buttons have icons */
.test-case-actions .edit-test-case i,
.test-case-actions .duplicate-test-case i,
.test-case-actions .delete-test-case i {
    margin-right: 0.3rem;
}

/* Dropdown menu styling for test cases */
.test-case-actions .dropdown-menu {
    min-width: 150px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.test-case-actions .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.test-case-actions .dropdown-item i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

.test-case-actions .dropdown-item:hover {
    background-color: #f8f9fa;
}

.test-case-actions .dropdown-item.text-danger:hover {
    background-color: #fcf2f2;
}

/* Adjust button spacing */
.test-case-actions .btn-group > .btn {
    margin-left: 0; /* Remove existing margin */
}

.test-case-actions .btn {
    min-width: 90px;
}

/* Ensure ellipsis button is not too wide */
.test-case-actions .dropdown-toggle {
    min-width: 40px !important;
    width: 40px;
}

/* Style for the test case info modal */
#testCaseInfoModal .form-control-static {
    padding: 0.375rem 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    min-height: 38px;
}

/* Styles for test case labels */
.badge.bg-secondary {
    font-size: 0.75rem;
    font-weight: normal;
    padding: 0.25rem 0.5rem;
    margin-right: 0.25rem;
    background-color: #6c757d;
}

.test-case-container .badge.bg-secondary {
    margin-bottom: 0.25rem;
}

.test-case-name + div .badge.bg-secondary {
    display: inline-block;
    margin-top: 0.25rem;
} 