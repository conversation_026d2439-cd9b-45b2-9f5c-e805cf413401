import os
import traceback
import time
import math
import base64
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates

# Import TargetNotFoundError at module level to ensure it's available in exception handling
from airtest.core.error import TargetNotFoundError


class TapIfImageExistsAction(BaseAction):
    def scale_ios_coordinates(self, coordinates):
        """
        Scale coordinates for iOS devices if needed (EXACT same as TapAction)

        Args:
            coordinates: Tuple of (x, y) coordinates

        Returns:
            tuple: Scaled coordinates
        """
        try:
            x, y = coordinates

            # Get device dimensions
            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                device_width = self.controller.device_dimensions.get('width')
                device_height = self.controller.device_dimensions.get('height')

                if device_width and device_height:
                    # Check if we need to scale (if image dimensions don't match device dimensions)
                    if hasattr(self.controller, 'airtest_device') and self.controller.airtest_device:
                        try:
                            # Get the screen resolution from Airtest device
                            airtest_resolution = self.controller.airtest_device.get_current_resolution()
                            if airtest_resolution and len(airtest_resolution) == 2:
                                airtest_width, airtest_height = airtest_resolution

                                # Only scale if dimensions are different
                                if airtest_width != device_width or airtest_height != device_height:
                                    scale_x = device_width / airtest_width
                                    scale_y = device_height / airtest_height

                                    self.logger.info(f"Scaling coordinates by factors: x={scale_x}, y={scale_y}")
                                    return (int(x * scale_x), int(y * scale_y))
                        except Exception as e:
                            self.logger.warning(f"Error getting Airtest resolution: {e}")

            # Return original coordinates if no scaling needed or if scaling failed
            return coordinates
        except Exception as e:
            self.logger.warning(f"Error in scale_ios_coordinates: {e}")
            return coordinates

    def execute(self, params):
        """
        Execute tap if image exists action - EXACT copy of TapAction image method logic
        The only difference: returns success when image is not found instead of error

        Args:
            params: Dictionary containing:
                - image_filename: Reference image to tap on
                - threshold: (Optional) Matching threshold (default: 0.7)
                - timeout: (Optional) Maximum time to wait in seconds (default: 5)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "No device controller available - step passed (if exists behavior)"}

        # Extract parameters
        image_filename = params.get('image_filename')
        threshold = float(params.get('threshold', 0.7))
        timeout = int(params.get('timeout', 5))

        if not image_filename:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "Image filename is required - step passed (if exists behavior)"}

        self.logger.info(f"Executing tap if image exists action for image: {image_filename} with threshold: {threshold} and timeout: {timeout}s")

        # Get the absolute path to the reference image (EXACT same as TapAction)
        # Resolve the image path properly
        abs_path = image_filename
        if not os.path.exists(image_filename):
            # Try to resolve from reference_images directory
            try:
                from config import DIRECTORIES
                reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                if reference_dir:
                    full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                    if os.path.exists(full_path):
                        abs_path = full_path
                        self.logger.info(f"Resolved image path to: {abs_path}")
                    else:
                        # Try directly in reference_images folder
                        ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                        if os.path.exists(ref_path):
                            abs_path = ref_path
                            self.logger.info(f"Resolved image path to: {abs_path}")
            except (ImportError, Exception) as e:
                self.logger.warning(f"Could not resolve image path from config: {e}")
                # Fallback to reference_images folder
                ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                if os.path.exists(ref_path):
                    abs_path = ref_path
                    self.logger.info(f"Fallback resolved image path to: {abs_path}")
        else:
            abs_path = image_filename

        if not os.path.exists(abs_path):
            self.logger.error(f"Reference image not found: {abs_path}")
            # Always return success for "if exists" behavior
            return {"status": "success", "message": f"Reference image not found - step passed (if exists behavior): {image_filename}"}

        # EXACT COPY of TapAction image method logic starts here
        try:
            from airtest.core.api import connect_device, wait, touch, Template, exists
            from airtest.core.error import TargetNotFoundError
            from airtest.core.helper import G

            # Ensure Airtest device is initialized (EXACT same as TapAction)
            if hasattr(self.controller, '_ensure_airtest_connected'):
                self.logger.info("Ensuring Airtest device is connected...")
                airtest_connected = self.controller._ensure_airtest_connected()
                if not airtest_connected:
                    self.logger.error("Failed to connect Airtest device, trying fallback methods")
                    # Don't return error here, continue to fallback methods
            else:
                self.logger.warning("Controller doesn't have _ensure_airtest_connected method")

            # Try to initialize Airtest device if not already initialized (EXACT same as TapAction)
            if hasattr(self.controller, '_init_airtest'):
                self.logger.info("Initializing Airtest device...")
                airtest_initialized = self.controller._init_airtest()
                if not airtest_initialized:
                    self.logger.error("Failed to initialize Airtest device, trying fallback methods")
                    # Don't return error here, continue to fallback methods

            # Log the device info to help with debugging (EXACT same as TapAction)
            if hasattr(G, 'DEVICE') and G.DEVICE:
                self.logger.info(f"Current Airtest device: {G.DEVICE}")
            else:
                self.logger.warning("No Airtest device available in G.DEVICE")

            # Use Airtest ONLY for finding the image coordinates (EXACT same as TapAction)
            self.logger.info(f"Using Airtest only for image recognition, not for tapping")

            # Create template and find its position with timeout compliance (EXACT same as TapAction)
            template_image = Template(abs_path, threshold=threshold)

            def wait_operation():
                return wait(template_image, timeout=timeout)

            # Use timeout-aware retry mechanism
            wait_result = self.execute_with_retry(
                wait_operation,
                max_retries=1,  # Image finding typically doesn't need many retries
                retry_delay=1,
                operation_name=f"wait_for_image({os.path.basename(image_filename)})",
                action_timeout=timeout
            )

            # Handle timeout compliance errors
            if isinstance(wait_result, dict) and wait_result.get('status') == 'error':
                if 'timeout' in wait_result.get('message', '').lower():
                    self.logger.info(f"Image '{os.path.basename(abs_path)}' not found within {timeout}s timeout - step passed (if exists behavior)")
                    return {"status": "success", "message": f"Image '{image_filename}' not found within {timeout}s timeout - step passed (if exists behavior)"}
                else:
                    # Other errors during wait operation
                    return {"status": "success", "message": f"Image search failed - step passed (if exists behavior): {wait_result.get('message')}"}

            match_pos = wait_result
            self.logger.info(f"Found image at position: {match_pos}, now using Appium to tap")

            # Skip all Airtest touch methods and go directly to Appium (EXACT same as TapAction)
            if hasattr(self.controller, 'driver') and self.controller.driver:
                # For iOS, apply scaling if needed (EXACT same as TapAction)
                if hasattr(self.controller, 'platform_name') and self.controller.platform_name == 'iOS':
                    original_pos = match_pos
                    match_pos = self.scale_ios_coordinates(match_pos)
                    self.logger.info(f"Applied iOS scaling: {original_pos} -> {match_pos}")

                # Extract coordinates (EXACT same as TapAction)
                x, y = match_pos

                # Try using mobile: tap command first (EXACT same as TapAction)
                try:
                    self.logger.info(f"Trying mobile: tap command at ({x}, {y})")
                    self.controller.driver.execute_script("mobile: tap", {"x": int(x), "y": int(y)})
                    return {"status": "success", "message": f"Tapped at {match_pos} using mobile: tap after finding image with Airtest"}
                except Exception as mobile_tap_err:
                    self.logger.warning(f"mobile: tap failed: {mobile_tap_err}")

                    # Try using Appium's TouchAction (EXACT same as TapAction)
                    try:
                        from appium.webdriver.common.touch_action import TouchAction
                        actions = TouchAction(self.controller.driver)
                        actions.tap(x=int(x), y=int(y)).perform()
                        return {"status": "success", "message": f"Tapped at {match_pos} using Appium TouchAction after finding image with Airtest"}
                    except Exception as touch_action_err:
                        self.logger.warning(f"Appium TouchAction failed: {touch_action_err}")

                        # One last attempt with W3C actions if available (EXACT same as TapAction)
                        try:
                            self.logger.info("Trying W3C Actions API")
                            from selenium.webdriver.common.action_chains import ActionChains
                            from selenium.webdriver.common.actions.pointer_input import PointerInput
                            from selenium.webdriver.common.actions.action_builder import ActionBuilder

                            # Create pointer input
                            pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                            # Create action chains
                            actions = ActionBuilder(self.controller.driver, mouse=pointer)
                            # Add pointer move and pointer down (tap)
                            actions.pointer_action.move_to_location(int(x), int(y))
                            actions.pointer_action.click()
                            # Perform the action
                            actions.perform()
                            return {"status": "success", "message": f"Tapped at {match_pos} using W3C Actions after finding image with Airtest"}
                        except Exception as w3c_err:
                            self.logger.error(f"All Appium tap methods failed: {w3c_err}")

                            # Only now try the Airtest touch as a last resort (EXACT same as TapAction)
                            self.logger.warning("All Appium methods failed, falling back to Airtest touch as last resort")
                            from airtest.core.api import touch
                            touch(template_image)
                            return {"status": "success", "message": f"Tapped on image using Airtest touch as fallback"}
            else:
                self.logger.warning("No Appium driver available, using Airtest touch")
                from airtest.core.api import touch
                touch(template_image)
                return {"status": "success", "message": f"Tapped on image using Airtest touch (no Appium driver)"}

        except Exception as e:
            self.logger.error(f"Failed to initialize Airtest device, trying fallback methods")
            
            # Check if it's a TargetNotFoundError specifically
            is_target_not_found = False
            try:
                # Try to import and check the exception type
                from airtest.core.error import TargetNotFoundError as TNF
                is_target_not_found = isinstance(e, TNF)
            except ImportError:
                # Fallback to string checking if import fails
                is_target_not_found = 'TargetNotFoundError' in str(type(e)) or 'TargetNotFoundError' in str(e.__class__.__name__)

            # For TargetNotFoundError, try exists() first
            if is_target_not_found:
                self.logger.info(f"Image '{os.path.basename(abs_path)}' not found within {timeout} seconds with threshold {threshold}")
                # Try exists() to see if we can find it with a lower threshold (EXACT same as TapAction)
                try:
                    from airtest.core.api import exists, touch
                    match_result = exists(template_image)
                    if match_result:
                        self.logger.info(f"Found with exists() but below threshold: {match_result}")
                        # If we found it with exists but not wait, try to tap on it anyway
                        try:
                            self.logger.info(f"Attempting to tap on image found with exists(): {match_result}")
                            touch(match_result)
                            return {"status": "success", "message": f"Tapped on image found with exists() at {match_result}"}
                        except Exception as touch_err:
                            self.logger.warning(f"Failed to tap on image found with exists(): {touch_err}")
                    else:
                        self.logger.info(f"Image not found even with exists() method")
                except Exception as exists_err:
                    self.logger.warning(f"exists() method also failed: {exists_err}")

            # For ALL errors (including Airtest initialization failures), try OpenCV fallback method
            try:
                self.logger.info(f"Trying OpenCV approach for image recognition: {abs_path}")
                import cv2
                import numpy as np
                from PIL import Image
                import io
                import base64

                # Take a screenshot using Appium
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # Get device dimensions first
                    device_width = None
                    device_height = None
                    if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                        device_width = self.controller.device_dimensions.get('width')
                        device_height = self.controller.device_dimensions.get('height')
                        self.logger.info(f"Device dimensions: {device_width}x{device_height}")

                    # Get screenshot as base64
                    screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                    screenshot_data = base64.b64decode(screenshot_base64)

                    # Convert to PIL Image first
                    screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                    original_size = screenshot_pil.size
                    self.logger.info(f"Screenshot dimensions: {original_size[0]}x{original_size[1]}")

                    # Convert PIL to OpenCV format
                    screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                    # Load template image
                    template = cv2.imread(abs_path)
                    if template is None:
                        self.logger.error(f"Could not load template image: {abs_path}")
                        raise Exception(f"Could not load template image: {abs_path}")

                    h, w = template.shape[:2]
                    self.logger.info(f"Template dimensions: {w}x{h}")

                    # Try multiple template matching methods
                    methods = [
                        (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
                        (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED"),
                        (cv2.TM_SQDIFF_NORMED, "TM_SQDIFF_NORMED")
                    ]

                    best_val = 0
                    best_loc = None
                    best_method = None

                    # Use a lower threshold for OpenCV matching
                    opencv_threshold = max(0.5, threshold - 0.2)  # Lower threshold by 0.2 but not below 0.5
                    self.logger.info(f"Using OpenCV threshold: {opencv_threshold} (original: {threshold})")

                    for method, method_name in methods:
                        # Perform template matching
                        result = cv2.matchTemplate(screenshot_cv, template, method)

                        # Different handling for SQDIFF (lower is better) vs others (higher is better)
                        if method == cv2.TM_SQDIFF_NORMED:
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                            curr_val = 1.0 - min_val  # Convert to same scale as other methods
                            curr_loc = min_loc
                        else:
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                            curr_val = max_val
                            curr_loc = max_loc

                        self.logger.info(f"Template matching with {method_name}: {curr_val}")

                        if curr_val > best_val:
                            best_val = curr_val
                            best_loc = curr_loc
                            best_method = method_name

                    self.logger.info(f"Best template matching result: {best_val} with method {best_method} (threshold: {opencv_threshold})")

                    if best_val >= opencv_threshold:
                        # Match found, calculate center coordinates
                        x = best_loc[0] + w // 2
                        y = best_loc[1] + h // 2

                        self.logger.info(f"Found image at ({x}, {y}) in original screenshot")

                        # Validate coordinates against device dimensions
                        if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                            device_width = self.controller.device_dimensions.get('width')
                            device_height = self.controller.device_dimensions.get('height')

                            # Check if coordinates are within device bounds
                            if device_width and device_height:
                                if x >= original_size[0] or y >= original_size[1]:
                                    self.logger.warning(f"Coordinates ({x}, {y}) are outside screenshot bounds {original_size[0]}x{original_size[1]}")

                                    # Clamp coordinates to screenshot bounds
                                    x = min(x, original_size[0] - 1)
                                    y = min(y, original_size[1] - 1)
                                    self.logger.info(f"Clamped coordinates to ({x}, {y})")

                                # Only scale if dimensions are different and scaling is needed
                                if device_width != original_size[0] or device_height != original_size[1]:
                                    # Calculate scaling factors
                                    scale_x = device_width / original_size[0]
                                    scale_y = device_height / original_size[1]

                                    # Apply scaling
                                    original_x, original_y = x, y
                                    x = int(x * scale_x)
                                    y = int(y * scale_y)

                                    # Ensure coordinates are within device bounds after scaling
                                    x = min(x, device_width - 1)
                                    y = min(y, device_height - 1)

                                    self.logger.info(f"Scaled coordinates from ({original_x}, {original_y}) to ({x}, {y}) for device dimensions {device_width}x{device_height}")

                        # Create a debug image showing the match
                        debug_match_path = os.path.join(os.path.dirname(abs_path), 'debug_match.png')
                        debug_img = screenshot_cv.copy()
                        cv2.rectangle(debug_img, best_loc, (best_loc[0] + w, best_loc[1] + h), (0, 255, 0), 2)
                        cv2.circle(debug_img, (x, y), 5, (0, 0, 255), -1)
                        cv2.imwrite(debug_match_path, debug_img)
                        self.logger.info(f"Saved debug match image to {debug_match_path}")

                        self.logger.info(f"Tapping at ({x}, {y}) using OpenCV image recognition")

                        # Tap at the center using Appium
                        self.controller.driver.tap([(int(x), int(y))])
                        return {"status": "success", "message": f"Tapped at ({x}, {y}) using OpenCV image recognition"}
                    else:
                        self.logger.warning(f"Image not found with OpenCV (best_val: {best_val}, threshold: {opencv_threshold})")
            except ImportError as opencv_e:
                self.logger.warning(f"OpenCV not available: {opencv_e}")
            except Exception as opencv_e:
                self.logger.error(f"Error using OpenCV for image recognition: {opencv_e}")

            # KEY DIFFERENCE: Return success instead of error for "if exists" behavior
            self.logger.info(f"Image '{image_filename}' not found - marking as passed (if exists behavior)")
            return {"status": "success", "message": f"Image '{image_filename}' not found - step passed (if exists behavior)"}




