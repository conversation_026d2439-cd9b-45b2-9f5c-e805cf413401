from .base_action import BaseAction
import logging
import os
import traceback
import time
from pathlib import Path

class MultiStepAction(BaseAction):
    """Handler for executing a test case as a multi-step action"""

    def execute(self, params):
        """
        Execute a test case as a multi-step action

        Args:
            params: Dictionary containing:
                - test_case_id: ID of the test case to execute
                - test_case_steps: Steps of the test case to execute

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the test case ID from params
        test_case_id = params.get('test_case_id')
        test_case_steps = params.get('test_case_steps', [])

        if not test_case_id:
            return {"status": "error", "message": "No test case ID provided"}

        # If no steps are provided directly, try to load them from the test case file
        if not test_case_steps:
            self.logger.info(f"No test case steps provided directly, attempting to load from file: {test_case_id}")
            try:
                # Import test_case_manager utilities with fallback
                try:
                    from ..utils.test_case_manager import TestCaseManager
                except ImportError:
                    try:
                        from utils.test_case_manager import TestCaseManager
                    except ImportError:
                        try:
                            from app.utils.test_case_manager import TestCaseManager
                        except ImportError:
                            # Fallback function if import fails
                            def TestCaseManager(*args, **kwargs):
                                return args[0] if args else None

                # Get the test cases directory
                try:
                    from config import DIRECTORIES
                    test_cases_dir = DIRECTORIES['TEST_CASES']
                except Exception:
                    workspace_root = Path(os.environ.get('AUTOMATION_WORKSPACE', Path.home() / 'MobileAutomationWorkspace'))
                    test_cases_dir = workspace_root / 'ios' / 'test_cases'

                # Create a test case manager instance
                test_case_manager = TestCaseManager(test_cases_dir)

                # Load the test case
                test_case = test_case_manager.load_test_case(test_case_id)

                if test_case and 'actions' in test_case:
                    test_case_steps = test_case.get('actions', [])
                    self.logger.info(f"Loaded {len(test_case_steps)} steps from test case file: {test_case_id}")
                else:
                    self.logger.error(f"Failed to load test case or no actions found: {test_case_id}")
                    return {"status": "error", "message": f"Failed to load test case or no actions found: {test_case_id}"}
            except Exception as e:
                self.logger.error(f"Error loading test case: {e}")
                return {"status": "error", "message": f"Error loading test case: {str(e)}"}

        if not test_case_steps:
            return {"status": "error", "message": "No test case steps provided or loaded"}

        self.logger.info(f"Executing multi-step action for test case: {test_case_id}")

        # Import the action factory
        try:
            from .action_factory import ActionFactory
        except ImportError:
            try:
                from action_factory import ActionFactory
            except ImportError:
                try:
                    from app.actions.action_factory import ActionFactory
                except ImportError:
                    # Last resort - try importing from actions module
                    from actions.action_factory import ActionFactory

        # Create a new action factory with the same controller
        action_factory = ActionFactory(self.controller)

        # Execute each step in the test case
        results = []
        success_count = 0

        # Find all hook actions in the test case steps
        # Look for both 'action_type' and 'type' being 'hookAction'
        hook_actions = [step for step in test_case_steps if step.get('action_type') == 'hookAction' or step.get('type') == 'hookAction']
        if hook_actions:
            self.logger.info(f"Found {len(hook_actions)} hook actions within multi-step")
            # Log the hook actions for debugging
            for i, hook in enumerate(hook_actions):
                self.logger.info(f"Hook action {i+1}: {hook}")

        step_index = 0
        step_retry_count = {}  # Track retry count for each step to prevent infinite loops
        max_retries_per_step = 3  # Maximum retries per step

        while step_index < len(test_case_steps):
            step = test_case_steps[step_index]

            # Initialize retry count for this step if not already tracked
            if step_index not in step_retry_count:
                step_retry_count[step_index] = 0

            try:
                # Get action type - try both 'type' and 'action_type' fields
                action_type = step.get('type') or step.get('action_type')

                # Log the step being executed
                self.logger.info(f"Executing step {step_index + 1}/{len(test_case_steps)}: {action_type or 'unknown'}")

                # Execute the step using the action factory
                if not action_type:
                    self.logger.warning(f"Step {step_index + 1} has no action_type, skipping")
                    results.append({
                        "status": "warning",
                        "message": f"Step {step_index + 1} has no action_type, skipping",
                        "step_index": step_index
                    })
                    step_index += 1
                    continue

                # Skip hook actions during normal execution
                if action_type == 'hookAction' or step.get('type') == 'hookAction':
                    # Skip hook actions during normal execution
                    self.logger.info(f"Skipping hook action during normal execution (step {step_index + 1})")
                    results.append({
                        "status": "success",
                        "message": "Hook Action skipped (will only be executed when a step fails)",
                        "step_index": step_index
                    })
                    success_count += 1
                    step_index += 1
                    continue

                # Execute the action
                result = action_factory.execute_action(action_type, step)

                # Add the step index to the result
                if isinstance(result, dict):
                    result['step_index'] = step_index
                    results.append(result)

                    # Check if the step failed
                    if result.get('status') != 'success':
                        self.logger.error(f"Step {step_index + 1} failed: {result.get('message')}")

                        # If we have hook actions in the multi-step, execute them
                        if hook_actions:
                            self.logger.info(f"Executing {len(hook_actions)} hook actions within multi-step due to step failure")

                            # Execute each hook action
                            hook_success = False
                            for hook_index, hook_action in enumerate(hook_actions):
                                self.logger.info(f"Executing hook action {hook_index + 1}/{len(hook_actions)}")

                                # Get the hook type and data
                                hook_type = hook_action.get('hook_type')
                                hook_data = hook_action.get('hook_data', {})

                                if not hook_type:
                                    self.logger.warning(f"Hook action {hook_index + 1} has no hook_type, skipping")
                                    continue

                                # Create a new action with the hook type and data
                                recovery_action = {'type': hook_type}

                                # Copy all hook_data to the recovery action
                                for key, value in hook_data.items():
                                    recovery_action[key] = value

                                self.logger.info(f"Executing hook action: {hook_type}")

                                # Execute the hook action
                                hook_result = action_factory.execute_action(hook_type, recovery_action)

                                # Check if the hook action succeeded
                                if isinstance(hook_result, dict) and hook_result.get('status') == 'success':
                                    self.logger.info(f"Hook action {hook_index + 1} succeeded")
                                    hook_success = True
                                else:
                                    self.logger.error(f"Hook action {hook_index + 1} failed: {hook_result}")

                            # If any hook action succeeded, retry the failed step (with retry limit)
                            if hook_success:
                                if step_retry_count[step_index] < max_retries_per_step:
                                    step_retry_count[step_index] += 1
                                    self.logger.info(f"Retrying failed step {step_index + 1} after hook actions (attempt {step_retry_count[step_index]}/{max_retries_per_step})")
                                    continue  # Retry the same step
                                else:
                                    self.logger.warning(f"Step {step_index + 1} has reached maximum retry limit ({max_retries_per_step}), moving to next step")

                        # If no hook actions or all hook actions failed, or max retries reached, move to the next step
                        step_index += 1
                    else:
                        # Step succeeded, count it and move to the next step
                        success_count += 1
                        # Reset retry count for this step since it succeeded
                        step_retry_count[step_index] = 0
                        step_index += 1
                else:
                    self.logger.warning(f"Step {step_index + 1} returned non-dict result: {result}")
                    results.append({
                        "status": "warning",
                        "message": f"Step {step_index + 1} returned non-dict result: {result}",
                        "step_index": step_index
                    })
                    step_index += 1

                # Removed delay between steps to improve performance
                # This will break some tests that rely on the delay, but will improve execution time
                pass

            except Exception as e:
                self.logger.error(f"Error executing step {step_index + 1}: {e}")
                self.logger.error(traceback.format_exc())
                results.append({
                    "status": "error",
                    "message": f"Error executing step {step_index + 1}: {str(e)}",
                    "step_index": step_index
                })

                # If we have hook actions in the multi-step, execute them
                if hook_actions:
                    self.logger.info(f"Executing {len(hook_actions)} hook actions within multi-step due to step exception")

                    # Execute each hook action
                    hook_success = False
                    for hook_index, hook_action in enumerate(hook_actions):
                        self.logger.info(f"Executing hook action {hook_index + 1}/{len(hook_actions)}")

                        # Get the hook type and data
                        hook_type = hook_action.get('hook_type')
                        hook_data = hook_action.get('hook_data', {})

                        if not hook_type:
                            self.logger.warning(f"Hook action {hook_index + 1} has no hook_type, skipping")
                            continue

                        # Create a new action with the hook type and data
                        recovery_action = {'type': hook_type}

                        # Copy all hook_data to the recovery action
                        for key, value in hook_data.items():
                            recovery_action[key] = value

                        self.logger.info(f"Executing hook action: {hook_type}")

                        # Execute the hook action
                        try:
                            hook_result = action_factory.execute_action(hook_type, recovery_action)

                            # Check if the hook action succeeded
                            if isinstance(hook_result, dict) and hook_result.get('status') == 'success':
                                self.logger.info(f"Hook action {hook_index + 1} succeeded")
                                hook_success = True
                            else:
                                self.logger.error(f"Hook action {hook_index + 1} failed: {hook_result}")
                        except Exception as hook_error:
                            self.logger.error(f"Error executing hook action {hook_index + 1}: {hook_error}")

                    # If any hook action succeeded, retry the failed step (with retry limit)
                    if hook_success:
                        if step_retry_count[step_index] < max_retries_per_step:
                            step_retry_count[step_index] += 1
                            self.logger.info(f"Retrying failed step {step_index + 1} after hook actions due to exception (attempt {step_retry_count[step_index]}/{max_retries_per_step})")
                            continue  # Retry the same step
                        else:
                            self.logger.warning(f"Step {step_index + 1} has reached maximum retry limit ({max_retries_per_step}) after exception, moving to next step")

                # If no hook actions or all hook actions failed, or max retries reached, move to the next step
                step_index += 1

        # Determine overall status
        if success_count == len(test_case_steps):
            overall_status = "success"
            message = f"All {success_count} steps executed successfully"
        elif success_count > 0:
            overall_status = "partial"
            message = f"{success_count}/{len(test_case_steps)} steps executed successfully"
        else:
            overall_status = "error"
            message = "All steps failed to execute"

        return {
            "status": overall_status,
            "message": message,
            "results": results,
            "success_count": success_count,
            "total_steps": len(test_case_steps)
        }
