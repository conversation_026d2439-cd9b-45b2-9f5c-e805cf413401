from .base_action import BaseAction
import logging
import traceback
import json
import os
from airtest.core.api import Template, wait, exists
from airtest.core.error import TargetNotFoundError
from airtest.core.helper import G

class IfThenStepsAction(BaseAction):
    """Handler for if-then conditional actions - COMPLETELY REWRITTEN to use exact TAP action logic"""

    def execute(self, params):
        """
        Execute a conditional action - if condition is true, execute then action

        Args:
            params: Dictionary containing:
                - condition_type: Type of condition (exists, not_exists, visible, contains_text, value_equals, has_attribute, etc.)
                - condition: Condition parameters
                - then_action: Action to execute if condition is true

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "success", "message": "No device controller available - step passed (if-then behavior)"}

        condition_type = params.get('condition_type')
        condition = params.get('condition', {})
        then_action = params.get('then_action', {})

        # Log the conditional action
        self.logger.info(f"Executing If-Then Steps with condition type: {condition_type}")
        self.logger.info(f"Condition parameters: {condition}")
        self.logger.info(f"Then action: {then_action}")

        # Check condition based on type
        condition_met = False
        found_element_coordinates = None  # Store coordinates of found condition element

        try:
            if condition_type in ['exists', 'not_exists']:
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value:
                    return {"status": "success", "message": "Missing locator parameters for condition - step passed (if-then behavior)"}

                self.logger.info(f"Checking if element exists with {locator_type}: {locator_value}, timeout={timeout}s")

                # Use timeout-aware retry mechanism for condition checking
                def condition_check_operation():
                    # Use EXACT same logic as working TAP actions
                    if locator_type == 'image':
                        # Use EXACT same image finding logic as TAP Image action
                        threshold = float(condition.get('threshold', 0.7))
                        element_found, coordinates = self._find_image_exact_tap_logic(locator_value, timeout, threshold)
                        if element_found:
                            found_element_coordinates = coordinates
                            self.logger.info(f"Found image at coordinates: {found_element_coordinates}")
                            return element_found, coordinates

                    elif locator_type == 'text':
                        # Use EXACT same text finding logic as TAP Text action
                        element_found, coordinates = self._find_text_exact_tap_logic(locator_value, timeout)
                        if element_found:
                            found_element_coordinates = coordinates
                            self.logger.info(f"Found text at coordinates: {found_element_coordinates}")
                            return element_found, coordinates

                    else:
                        # Use EXACT same locator finding logic as TAP Element action
                        element_found, coordinates = self._find_element_exact_tap_logic(locator_type, locator_value, timeout)
                        if element_found:
                            found_element_coordinates = coordinates
                            self.logger.info(f"Found element at coordinates: {found_element_coordinates}")
                            return element_found, coordinates

                    return None, None

                # Execute condition check with timeout compliance
                check_result = self.execute_with_retry(
                    condition_check_operation,
                    max_retries=2,
                    retry_delay=1,
                    operation_name=f"check_condition({locator_type}={locator_value})",
                    action_timeout=timeout
                )

                # Handle timeout compliance errors
                if isinstance(check_result, dict) and check_result.get('status') == 'error':
                    if 'timeout' in check_result.get('message', '').lower():
                        self.logger.info(f"Condition check timed out after {timeout}s - treating as element not found")
                        element_found, coordinates = None, None
                    else:
                        # Other errors during condition check
                        self.logger.warning(f"Condition check failed: {check_result.get('message')} - treating as element not found")
                        element_found, coordinates = None, None
                else:
                    element_found, coordinates = check_result
                    if element_found:
                        found_element_coordinates = coordinates

                # For 'exists', condition is met if element is found
                # For 'not_exists', condition is met if element is NOT found
                if condition_type == 'exists':
                    condition_met = element_found is not None
                    self.logger.info(f"Condition check result: Element exists = {condition_met}")
                else:  # not_exists
                    condition_met = element_found is None
                    found_element_coordinates = None  # No coordinates for not_exists
                    self.logger.info(f"Condition check result: Element does not exist = {condition_met}")

            else:
                # Handle other condition types (visible, contains_text, etc.)
                return {"status": "success", "message": f"Condition type '{condition_type}' not yet implemented - step passed (if-then behavior)"}

        except Exception as e:
            self.logger.error(f"Error checking condition: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error checking condition but step passed (if-then behavior): {str(e)}"}

        # Execute then action if condition is met
        if condition_met:
            return self._execute_then_action(then_action, found_element_coordinates)
        else:
            self.logger.info("Condition not met, no action taken")
            return {"status": "success", "message": "Condition not met, no action taken"}

    def _execute_then_action(self, then_action, found_coordinates):
        """Execute the then action - directly use the TAP action for tap actions"""
        if not then_action or not then_action.get('type'):
            self.logger.info("Condition met, but no Then action was provided")
            return {"status": "success", "message": "Condition met, no action taken (No Action selected)"}

        self.logger.info(f"Condition met, executing then action: {then_action.get('type')}")

        # Enhanced debugging
        self.logger.info(f"DEBUG: found_coordinates = {found_coordinates}")
        self.logger.info(f"DEBUG: found_coordinates type = {type(found_coordinates)}")
        self.logger.info(f"DEBUG: then_action type = {then_action.get('type')}")
        self.logger.info(f"DEBUG: original then_action = {then_action}")

        # For TAP actions, use the TAP action directly instead of replicating logic
        if then_action.get('type') == 'tap':
            return self._execute_tap_action_directly(then_action, found_coordinates)

        # For TAP ON TEXT actions, use the Tap on Text action directly
        if then_action.get('type') == 'tapOnText':
            return self._execute_tap_on_text_action_directly(then_action)

        # For other action types, use the action factory
        try:
            from .action_factory import ActionFactory
            action_factory = ActionFactory(self.controller)
            self.logger.info(f"DEBUG: Executing non-tap action with parameters: {then_action}")
            result = action_factory.execute_action(then_action.get('type'), then_action)
            self.logger.info(f"Then action result: {result}")
            return result
        except Exception as e:
            self.logger.error(f"Error executing then action: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error executing then action but step passed (if-then behavior): {str(e)}"}

    def _execute_tap_action_directly(self, then_action, found_coordinates):
        """Execute TAP action directly using the TAP action class"""
        try:
            # Import and create the TAP action directly
            from .tap_action import TapAction
            tap_action = TapAction(self.controller)

            # Prepare the tap action parameters
            tap_params = then_action.copy()

            self.logger.info(f"DEBUG: TAP action method = {tap_params.get('method', 'not specified')}")

            # Handle different TAP action methods
            method = tap_params.get('method', 'coordinates')

            if method == 'coordinates':
                # For coordinate-based tapping, use the provided coordinates or found coordinates
                if 'x' in tap_params and 'y' in tap_params:
                    # Use coordinates from the form
                    self.logger.info(f"Using coordinates from form: ({tap_params['x']}, {tap_params['y']})")
                elif found_coordinates and isinstance(found_coordinates, (tuple, list)) and len(found_coordinates) >= 2:
                    # Use coordinates from condition detection
                    try:
                        x = int(found_coordinates[0])
                        y = int(found_coordinates[1])
                        if x > 0 and y > 0:
                            tap_params['x'] = x
                            tap_params['y'] = y
                            self.logger.info(f"Using coordinates from condition detection: ({x}, {y})")
                        else:
                            self.logger.warning(f"Invalid coordinates from condition: ({x}, {y})")
                            return {"status": "success", "message": f"Invalid coordinates from condition but step passed (if-then behavior): ({x}, {y})"}
                    except (ValueError, TypeError) as coord_error:
                        self.logger.error(f"Error processing coordinates: {coord_error}")
                        return {"status": "success", "message": f"Error processing coordinates but step passed (if-then behavior): {coord_error}"}
                else:
                    self.logger.error("No valid coordinates available for tap action")
                    return {"status": "success", "message": "No valid coordinates available for tap action but step passed (if-then behavior)"}

            elif method == 'image':
                # For image-based tapping, ensure required parameters are present
                if not tap_params.get('image_filename'):
                    self.logger.error("Image filename is required for image-based tap")
                    return {"status": "success", "message": "Image filename is required for image-based tap but step passed (if-then behavior)"}
                self.logger.info(f"Using image-based tap with image: {tap_params['image_filename']}")

            elif method == 'locator':
                # For locator-based tapping, ensure required parameters are present
                if not tap_params.get('locator_type') or not tap_params.get('locator_value'):
                    self.logger.error("Locator type and value are required for locator-based tap")
                    return {"status": "success", "message": "Locator type and value are required for locator-based tap but step passed (if-then behavior)"}
                self.logger.info(f"Using locator-based tap with {tap_params['locator_type']}='{tap_params['locator_value']}'")

            elif method == 'text':
                # For text-based tapping, ensure required parameters are present
                if not tap_params.get('text_to_find'):
                    self.logger.error("Text to find is required for text-based tap")
                    return {"status": "success", "message": "Text to find is required for text-based tap but step passed (if-then behavior)"}
                self.logger.info(f"Using text-based tap with text: '{tap_params['text_to_find']}'")

            # Execute the TAP action directly
            self.logger.info(f"DEBUG: Executing TAP action directly with parameters: {tap_params}")
            result = tap_action.execute(tap_params)
            self.logger.info(f"TAP action result: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Error executing TAP action directly: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error executing TAP action directly but step passed (if-then behavior): {str(e)}"}

    def _execute_tap_on_text_action_directly(self, then_action):
        """Execute Tap on Text action directly using the Tap on Text action class"""
        try:
            # Import and create the Tap on Text action directly
            from .tap_on_text_action import TapOnTextAction
            tap_on_text_action = TapOnTextAction(self.controller)

            # Prepare the tap on text action parameters
            tap_on_text_params = then_action.copy()

            self.logger.info(f"DEBUG: Tap on Text action parameters = {tap_on_text_params}")

            # Validate required parameters
            if not tap_on_text_params.get('text_to_find'):
                self.logger.error("Text to find is required for Tap on Text action")
                return {"status": "success", "message": "Text to find is required for Tap on Text action but step passed (if-then behavior)"}

            # Set default values if not provided
            if 'timeout' not in tap_on_text_params:
                tap_on_text_params['timeout'] = 30
            if 'double_tap' not in tap_on_text_params:
                tap_on_text_params['double_tap'] = False

            self.logger.info(f"Executing Tap on Text action with text: '{tap_on_text_params['text_to_find']}'")

            # Execute the Tap on Text action directly
            result = tap_on_text_action.execute(tap_on_text_params)
            self.logger.info(f"Tap on Text action result: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Error executing Tap on Text action directly: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error executing Tap on Text action directly but step passed (if-then behavior): {str(e)}"}

    def _find_image_exact_tap_logic(self, image_filename, timeout, threshold):
        """
        Find image using EXACT same logic as TAP Image action
        Returns: (element_found, coordinates)
        """
        try:
            # Get absolute path for the image
            if hasattr(self.controller, 'get_screenshot_folder'):
                screenshot_folder = self.controller.get_screenshot_folder()
                abs_path = os.path.join(screenshot_folder, image_filename)
            else:
                abs_path = os.path.abspath(image_filename)

            if not os.path.exists(abs_path):
                self.logger.error(f"Image file not found: {abs_path}")
                return False, None

            self.logger.info(f"Looking for image: {abs_path} with threshold: {threshold}")

            # EXACT COPY of TAP Image action logic
            # Ensure Airtest device connection
            if hasattr(self.controller, '_ensure_airtest_connected'):
                self.logger.info("Ensuring Airtest device is connected...")
                airtest_connected = self.controller._ensure_airtest_connected()
                if not airtest_connected:
                    self.logger.error("Failed to connect Airtest device")
                    return False, None

            # Initialize Airtest device if needed
            if hasattr(self.controller, '_init_airtest'):
                self.logger.info("Initializing Airtest device...")
                airtest_initialized = self.controller._init_airtest()
                if not airtest_initialized:
                    self.logger.error("Failed to initialize Airtest device")
                    return False, None

            # Log device info
            if hasattr(G, 'DEVICE') and G.DEVICE:
                self.logger.info(f"Current Airtest device: {G.DEVICE}")
            else:
                self.logger.warning("No Airtest device available in G.DEVICE")

            # Use Airtest for image recognition (EXACT same as TAP Image)
            self.logger.info(f"Using Airtest for image recognition")
            template_image = Template(abs_path, threshold=threshold)
            match_pos = wait(template_image, timeout=timeout)
            self.logger.info(f"Found image at position: {match_pos}")

            # Apply iOS scaling if needed (EXACT same as TAP Image)
            if hasattr(self.controller, 'platform_name') and self.controller.platform_name == 'iOS':
                original_pos = match_pos
                match_pos = self.scale_ios_coordinates(match_pos)
                self.logger.info(f"Applied iOS scaling: {original_pos} -> {match_pos}")

            return True, match_pos

        except TargetNotFoundError:
            self.logger.info(f"Image not found: {image_filename}")
            return False, None
        except Exception as e:
            self.logger.error(f"Error finding image: {e}")
            return False, None

    def _find_text_exact_tap_logic(self, text_to_find, timeout):
        """
        Find text using EXACT same logic as TAP Text action
        Returns: (element_found, coordinates)
        """
        try:
            # Use the exact same text detection logic as TAP Text action
            from ..utils.text_detection import detect_text_in_screenshot

            # Take screenshot for text detection
            screenshot_path = None
            if hasattr(self.controller, 'take_screenshot'):
                screenshot_path = self.controller.take_screenshot()

            if not screenshot_path or not os.path.exists(screenshot_path):
                self.logger.error("Failed to take screenshot for text detection")
                return False, None

            self.logger.info(f"Looking for text: '{text_to_find}' in screenshot: {screenshot_path}")

            # Detect text in screenshot
            text_regions = detect_text_in_screenshot(screenshot_path, text_to_find)

            if text_regions:
                # Use the first match (same as TAP Text action)
                text_region = text_regions[0]
                coordinates = text_region.get('coordinates', {})
                center_x = coordinates.get('center_x')
                center_y = coordinates.get('center_y')

                if center_x is not None and center_y is not None:
                    self.logger.info(f"Found text '{text_to_find}' at coordinates: ({center_x}, {center_y})")
                    return True, (center_x, center_y)

            self.logger.info(f"Text not found: '{text_to_find}'")
            return False, None

        except Exception as e:
            self.logger.error(f"Error finding text: {e}")
            return False, None

    def _find_element_exact_tap_logic(self, locator_type, locator_value, timeout):
        """
        Find element using EXACT same logic as TAP Element action (iOS version)
        Returns: (element_found, coordinates)
        """
        try:
            self.logger.info(f"Finding element with {locator_type}='{locator_value}', timeout={timeout}s")

            # Enhanced element finding with multiple approaches for better reliability
            element = None

            # Method 1: Try controller's find_element method first
            if hasattr(self.controller, 'find_element'):
                try:
                    self.logger.info("Trying controller's find_element method...")
                    element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                    if element:
                        self.logger.info(f"Controller find_element method found element: {element is not None}")
                except Exception as e:
                    self.logger.warning(f"Controller find_element method failed: {e}")

            # Method 2: Try direct Appium driver method if controller method failed
            if not element and hasattr(self.controller, 'driver') and self.controller.driver:
                try:
                    self.logger.info("Trying direct Appium driver method...")
                    from appium.webdriver.common.appiumby import AppiumBy
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC

                    # Map locator types to AppiumBy constants
                    locator_map = {
                        'id': AppiumBy.ID,
                        'xpath': AppiumBy.XPATH,
                        'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                        'class': AppiumBy.CLASS_NAME,
                        'class_name': AppiumBy.CLASS_NAME,
                        'name': AppiumBy.NAME,
                        'ios_predicate': AppiumBy.IOS_PREDICATE,
                        'ios_class_chain': AppiumBy.IOS_CLASS_CHAIN
                    }

                    by_type = locator_map.get(locator_type.lower())
                    if by_type:
                        wait = WebDriverWait(self.controller.driver, timeout)
                        element = wait.until(EC.presence_of_element_located((by_type, locator_value)))
                        if element:
                            self.logger.info(f"Direct Appium driver method found element: {element is not None}")
                    else:
                        self.logger.warning(f"Unsupported locator type for direct Appium method: {locator_type}")

                except Exception as e:
                    self.logger.warning(f"Direct Appium driver method failed: {e}")

            if element:
                self.logger.info(f"Element found with {locator_type}='{locator_value}', extracting coordinates...")

                # Enhanced coordinate extraction with multiple approaches
                coordinates = None

                # Method 1: Try location and size attributes
                if hasattr(element, 'location') and hasattr(element, 'size'):
                    try:
                        location = element.location
                        size = element.size

                        self.logger.info(f"Element location: {location}, size: {size}")

                        # Validate location and size data
                        if (location and isinstance(location, dict) and 'x' in location and 'y' in location and
                            size and isinstance(size, dict) and 'width' in size and 'height' in size):

                            # Calculate center coordinates (same as TAP Element)
                            center_x = int(location['x'] + size['width'] // 2)
                            center_y = int(location['y'] + size['height'] // 2)
                            coordinates = (center_x, center_y)

                            self.logger.info(f"Successfully extracted coordinates using location/size: ({center_x}, {center_y})")
                        else:
                            self.logger.warning(f"Invalid location or size data: location={location}, size={size}")
                    except Exception as coord_error:
                        self.logger.warning(f"Error extracting coordinates from location/size: {coord_error}")

                # Method 2: Try rect attribute as fallback
                if not coordinates and hasattr(element, 'rect'):
                    try:
                        rect = element.rect
                        self.logger.info(f"Element rect: {rect}")

                        if rect and isinstance(rect, dict) and all(key in rect for key in ['x', 'y', 'width', 'height']):
                            center_x = int(rect['x'] + rect['width'] // 2)
                            center_y = int(rect['y'] + rect['height'] // 2)
                            coordinates = (center_x, center_y)

                            self.logger.info(f"Successfully extracted coordinates using rect: ({center_x}, {center_y})")
                        else:
                            self.logger.warning(f"Invalid rect data: {rect}")
                    except Exception as rect_error:
                        self.logger.warning(f"Error extracting coordinates from rect: {rect_error}")

                if coordinates:
                    self.logger.info(f"Found element with {locator_type}='{locator_value}' at coordinates: {coordinates}")
                    return True, coordinates
                else:
                    self.logger.warning(f"Element found but couldn't extract valid coordinates")
                    self.logger.warning(f"Element attributes: {[attr for attr in dir(element) if not attr.startswith('_')]}")
                    return True, None
            else:
                self.logger.info(f"Element not found with {locator_type}='{locator_value}'")
                return False, None

        except Exception as e:
            self.logger.error(f"Error finding element: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False, None

    def scale_ios_coordinates(self, coordinates):
        """Scale coordinates for iOS devices if needed - EXACT copy from TAP action"""
        try:
            x, y = coordinates

            # Get device dimensions
            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                device_width = self.controller.device_dimensions.get('width')
                device_height = self.controller.device_dimensions.get('height')

                if device_width and device_height:
                    try:
                        # Get Airtest resolution
                        if hasattr(G, 'DEVICE') and G.DEVICE:
                            airtest_width, airtest_height = G.DEVICE.get_current_resolution()

                            # Only scale if dimensions are different
                            if airtest_width != device_width or airtest_height != device_height:
                                scale_x = device_width / airtest_width
                                scale_y = device_height / airtest_height

                                self.logger.info(f"Scaling coordinates by factors: x={scale_x}, y={scale_y}")
                                return (int(x * scale_x), int(y * scale_y))
                    except Exception as e:
                        self.logger.warning(f"Error getting Airtest resolution: {e}")

            # Return original coordinates if no scaling needed or if scaling failed
            return coordinates
        except Exception as e:
            self.logger.error(f"Error scaling iOS coordinates: {e}")
            return coordinates
