import os
import traceback
from .check_if_exists_action import CheckIfExistsAction


class CheckIfNotExistsAction(CheckIfExistsAction):
    """
    Check if Not Exists action - verifies that an element/text/image is NOT present on screen.
    
    This action inherits from CheckIfExistsAction and inverts the logic:
    - PASS when element is NOT found (found=False)
    - FAIL when element IS found (found=True)
    
    Supports all the same locator types as CheckIfExistsAction:
    - Image (using Airtest Template, OpenCV)
    - Text (using OCR and XPath)
    - XPath
    - Accessibility ID
    - ID
    - Class
    - Name
    """
    
    def execute(self, params):
        """
        Execute the Check if Not Exists action.
        This action checks if an element does NOT exist on the screen using various locator types.
        
        Args:
            params (dict): Parameters for the action
                - locator_type (str): Type of locator (id, xpath, accessibility_id, class, name, text, image)
                - locator_value (str): Value of the locator
                - threshold (float, optional): Similarity threshold for image matching (0.0-1.0). Default is 0.7
                - timeout (int, optional): Maximum time to wait for confirming non-existence in seconds. Default is 10
                - use_env_var (bool, optional): Whether locator_value is an environment variable name
                - env_var_name (str, optional): Environment variable name if use_env_var is True
        
        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
                - found (bool): Whether the element was found (for consistency)
        """
        try:
            # Get parameters for logging
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            timeout = int(params.get('timeout', 10))
            
            self.logger.info(f"Checking if element does NOT exist with {locator_type}: {locator_value}, timeout={timeout}s")
            
            # Call the parent class's execute method to check if element exists
            # This will use all the same helper methods (_check_image_exists, _check_text_exists, etc.)
            exists_result = super().execute(params)
            
            # Handle error cases - propagate errors as-is
            if exists_result.get('status') == 'error':
                return exists_result
            
            # Invert the logic based on the 'found' field
            element_found = exists_result.get('found', False)
            
            if element_found:
                # Element WAS found - this is a FAILURE for "Check if Not Exists"
                self.logger.info(f"Check if Not Exists FAILED: Element was found when it should not exist")
                return {
                    "status": "error",
                    "message": f"Element exists but should not: {exists_result.get('message', '')}",
                    "found": True,
                    "original_result": exists_result
                }
            else:
                # Element was NOT found - this is a SUCCESS for "Check if Not Exists"
                self.logger.info(f"Check if Not Exists PASSED: Element not found as expected")
                return {
                    "status": "success",
                    "message": f"Element does not exist as expected: {locator_type}='{locator_value}'",
                    "found": False,
                    "original_result": exists_result
                }
                
        except Exception as e:
            self.logger.error(f"Error executing Check if Not Exists action: {e}")
            traceback.print_exc()
            return {"status": "error", "message": f"Check if Not Exists action failed: {str(e)}"}

