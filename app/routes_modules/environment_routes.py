"""
Environment Routes

This module contains all environment routes for the application.
"""

import os
import json
import base64
import time
import logging
import glob
import sys
import traceback
import shutil
import threading
import signal
import uuid
from pathlib import Path
from datetime import datetime
from functools import wraps

# Flask imports
from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session, Response
from werkzeug.utils import secure_filename

# PIL imports
import PIL.Image
import PIL.ImageDraw
import io

# Other imports
import xml.etree.ElementTree as ET
import requests
import re

# Import app instance from core
from app.core import app

# Import state variables
from app.core.state_manager import (
    device_controllers, players, action_factories,
    current_device, current_device_id, recording_actions,
    test_case_manager, test_suites_manager,
    get_session_device_id, get_session_id, get_client_session_id,
    get_current_environment_id_from_session, ensure_default_environment,
    socketio,
)

# Import utilities
from app.utils.appium_device_controller import AppiumDeviceController
from app.utils.recorder import Recorder
from app.utils.player import Player
from app.actions.action_factory import ActionFactory
from app.utils.reportGenerator import generateReport
from app.utils.custom_report_generator import generate_custom_report
from app.utils.screenshot_manager import screenshot_manager
from app.utils.directory_paths_db import directory_paths_db
from app.utils.global_values_db import global_values_db
from app.utils.environment_resolver import resolve_text_with_env_variables, get_resolved_variable_value
import config

# Import helper functions
from app.helpers.config_helpers import load_config, save_config, get_configured_directory, initialize_report_directory
from utils.directory_utils import get_reports_directory

# Set up logger
logger = logging.getLogger(__name__)

# ============================================================================
# ROUTE HANDLERS
# ============================================================================

@app.route('/api/settings', methods=['GET'])
def get_settings():
    """Get current settings from config.json, config.py, and the global values database"""
    try:
        # Load settings from config.json
        settings = load_config()

        # Get directory paths from database
        paths = directory_paths_db.get_all_paths()

        # Add directory paths to settings
        settings['test_cases_dir'] = paths.get('TEST_CASES', 'test_cases')
        # Get reports directory from database
        try:
            from utils.directory_paths_db import DirectoryPathsDB
            db = DirectoryPathsDB()
            reports_dir_path = db.get_path('REPORTS')
            if not reports_dir_path:
                reports_dir_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
            settings['reports_dir'] = paths.get('REPORTS', os.path.basename(reports_dir_path))
        except Exception:
            settings['reports_dir'] = paths.get('REPORTS', 'reports_ios')
        settings['reference_images_dir'] = paths.get('REFERENCE_IMAGES', 'reference_images')
        settings['test_suites_dir'] = paths.get('TEST_SUITES', 'test_suites')
        settings['files_to_push_dir'] = paths.get('FILES_TO_PUSH', 'files_to_push')

        # Get global values from the database
        db_values = global_values_db.get_all_values()

        # If database has values, use them
        if db_values:
            settings['global_values'] = db_values
            logger.info(f"Loaded global values from database: {len(db_values)} values")
        else:
            # Fallback to config.py for backward compatibility
            if hasattr(config, 'GLOBAL_VALUES') and isinstance(config.GLOBAL_VALUES, dict):
                settings['global_values'] = config.GLOBAL_VALUES
                logger.info(f"Loaded global values from config.py: {len(config.GLOBAL_VALUES)} values")

                # Initialize the database with these values
                global_values_db.save_values(config.GLOBAL_VALUES)
                logger.info("Initialized database with global values from config.py")
            else:
                settings['global_values'] = {}
                logger.info("No global values found in database or config.py")

        return jsonify(settings)
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500



@app.route('/api/settings', methods=['POST'])
def update_settings():
    """Update settings in config.json, config.py, and the global values database"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        logger.info(f"Received settings update: {data}")

        # Handle global values separately
        global_values = None
        if 'global_values' in data:
            global_values = data.pop('global_values')  # Remove from data to handle separately
            logger.info(f"Extracted global values: {len(global_values)} values")

        # If there are other settings besides global values, update them
        if data:
            logger.info(f"Updating directory settings: {data}")

            # Save to config.json
            save_config(data)

            # Update config.py with new directory paths
            config_path = Path(__file__).resolve().parent.parent / 'config.py'
            if config_path.exists():
                with open(config_path, 'r') as f:
                    content = f.read()

                # Helper function to update directory path in config.py
                def update_directory_path(content, dir_key, dir_path):
                    # Try to find the directory key in the DIRECTORIES dictionary
                    pattern = fr"'({dir_key})':\s*BASE_DIR\s*/\s*['\"]([^'\"]+)['\"]"
                    replacement = fr"'\1': BASE_DIR / '{dir_path}'"

                    match = re.search(pattern, content)
                    if match:
                        # Update existing entry
                        return re.sub(pattern, replacement, content)
                    else:
                        # Add new entry to DIRECTORIES dictionary
                        pattern = r"(DIRECTORIES\s*=\s*\{[^\}]+)(\})"
                        replacement = fr"\1    '{dir_key}': BASE_DIR / '{dir_path}',\n\2"
                        return re.sub(pattern, replacement, content)

                # Update reference images directory if provided
                if 'reference_images_dir' in data:
                    content = update_directory_path(content, 'REFERENCE_IMAGES', data['reference_images_dir'])

                # Update files to push directory if provided
                if 'files_to_push_dir' in data:
                    # Try to update FILES_TO_PUSH_DIR variable
                    files_pattern = r"FILES_TO_PUSH_DIR\s*=\s*BASE_DIR\s*/\s*['\"]([^'\"]+)['\"]"
                    files_replacement = f"FILES_TO_PUSH_DIR = BASE_DIR / '{data['files_to_push_dir']}'"

                    if re.search(files_pattern, content):
                        # Update existing entry
                        content = re.sub(files_pattern, files_replacement, content)
                    else:
                        # Add new entry after DIRECTORIES section
                        pattern = r"(# Ensure all directories exist)"
                        replacement = f"# Files to push directory (for media uploads)\n{files_replacement}\n\n\\1"
                        content = re.sub(pattern, replacement, content)

                # Write updated content back to config.py
                with open(config_path, 'w') as f:
                    f.write(content)
            else:
                logger.warning("config.py not found, skipping directory updates")

        # Handle global values if provided
        if global_values is not None:
            logger.info(f"Saving global values to database: {global_values}")

            # Save to the database
            if global_values_db.save_values(global_values):
                logger.info("Global values saved successfully to database")
            else:
                logger.error("Failed to save global values to database")
                return jsonify({"error": "Failed to save global values to database"}), 500

            # For backward compatibility, also update config.py
            try:
                config_path = Path(__file__).resolve().parent.parent / 'config.py'
                if config_path.exists():
                    with open(config_path, 'r') as f:
                        content = f.read()

                    # Update global values in config.py
                    if 'GLOBAL_VALUES' in content:
                        # Update existing GLOBAL_VALUES dictionary
                        global_values_pattern = r"GLOBAL_VALUES\s*=\s*\{[^\}]*\}"
                        global_values_str = "GLOBAL_VALUES = {\n"
                        for key, value in global_values.items():
                            # Format value based on type
                            if isinstance(value, str):
                                global_values_str += f"    '{key}': '{value}',\n"
                            else:
                                global_values_str += f"    '{key}': {value},\n"
                        global_values_str += "}"

                        content = re.sub(global_values_pattern, global_values_str, content)
                    else:
                        # Add new GLOBAL_VALUES section before APPIUM_CONFIG
                        pattern = r"(# Appium configuration)"
                        global_values_str = "# Global values for test parameterization\nGLOBAL_VALUES = {\n"
                        for key, value in global_values.items():
                            # Format value based on type
                            if isinstance(value, str):
                                global_values_str += f"    '{key}': '{value}',\n"
                            else:
                                global_values_str += f"    '{key}': {value},\n"
                        global_values_str += "}\n\n\\1"

                        content = re.sub(pattern, global_values_str, content)

                    # Write updated content back to config.py
                    with open(config_path, 'w') as f:
                        f.write(content)

                    logger.info("Global values also updated in config.py for backward compatibility")
            except Exception as e:
                logger.warning(f"Error updating global values in config.py (non-critical): {str(e)}")
                # Continue even if config.py update fails, as the database is the primary storage now

        return jsonify({"status": "success", "message": "Settings updated successfully"})
    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500



@app.route('/api/directory_paths', methods=['GET'])
def get_directory_paths():
    """Get directory paths from the database"""
    try:
        # Get directory paths from database
        paths = directory_paths_db.get_all_paths()

        # Format the response to match what the frontend expects
        response = {
            'test_cases_dir': paths.get('TEST_CASES', 'test_cases'),
            'reports_dir': paths.get('REPORTS', 'reports_ios'),
            'reference_images_dir': paths.get('REFERENCE_IMAGES', 'reference_images'),
            'files_to_push_dir': paths.get('FILES_TO_PUSH', 'files_to_push'),
            'test_suites_dir': paths.get('TEST_SUITES', 'test_suites'),
            'temp_files_dir': paths.get('TEMP_FILES', 'temp_files')
        }

        return jsonify(response)
    except Exception as e:
        logger.error(f"Error loading directory paths: {str(e)}")
        return jsonify({"error": str(e)}), 500


# Environment Management API endpoints


@app.route('/api/environments', methods=['GET'])
def get_environments():
    """Get all environments from the database"""
    try:
        environments = directory_paths_db.get_all_environments()
        # Format environments for frontend compatibility
        formatted_environments = []
        for env in environments:
            formatted_environments.append({
                'id': env.get('environment_id') or env.get('id'),
                'name': env.get('name'),
                'description': env.get('description', ''),
                'is_active': env.get('is_active', False),
                'created_at': env.get('created_at'),
                'updated_at': env.get('updated_at')
            })

        return jsonify({
            'success': True,
            'data': formatted_environments
        })
    except Exception as e:
        logger.error(f"Error getting environments: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/environments/current', methods=['GET'])
def get_current_environment():
    """Get the currently active environment"""
    try:
        current_env_id = get_current_environment_id_from_session()
        if not current_env_id:
            # Try to ensure a default environment exists and auto-select it
            current_env_id = ensure_default_environment()

        if current_env_id:
            env_details = directory_paths_db.get_environment_by_id(current_env_id)
            if env_details:
                return jsonify({
                    'success': True,
                    'data': {
                        'id': env_details.get('environment_id') or env_details.get('id'),
                        'name': env_details.get('name'),
                        'description': env_details.get('description', ''),
                        'is_active': True
                    }
                })
            else:
                # Environment ID in session doesn't exist, clear it and try to auto-select another
                session.pop('current_environment_id', None)
                logger.warning(f"Previously selected environment ID {current_env_id} no longer exists. Trying to auto-select another.")

                # Try to auto-select the first available environment
                environments = directory_paths_db.get_all_environments()
                if environments:
                    first_env = environments[0]
                    new_env_id = first_env.get('environment_id') or first_env.get('id')
                    session['current_environment_id'] = new_env_id
                    logger.info(f"Auto-selected environment: {first_env.get('name')} (ID: {new_env_id})")
                    return jsonify({
                        'success': True,
                        'data': {
                            'id': new_env_id,
                            'name': first_env.get('name'),
                            'description': first_env.get('description', ''),
                            'is_active': True
                        }
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': 'No environments available',
                        'environment_id': None,
                        'name': None
                    }), 404
        else:
            # No environment selected, try to auto-select the first available
            environments = directory_paths_db.get_all_environments()
            if environments:
                first_env = environments[0]
                new_env_id = first_env.get('environment_id') or first_env.get('id')
                session['current_environment_id'] = new_env_id
                logger.info(f"Auto-selected first available environment: {first_env.get('name')} (ID: {new_env_id})")
                return jsonify({
                    'success': True,
                    'data': {
                        'id': new_env_id,
                        'name': first_env.get('name'),
                        'description': first_env.get('description', ''),
                        'is_active': True
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'No environments available',
                    'environment_id': None,
                    'name': None
                }), 404
    except Exception as e:
        logger.error(f"Error getting current environment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/environments/current', methods=['POST'])
def set_current_environment():
    """Set the currently active environment"""
    try:
        data = request.get_json() or {}
        environment_id = data.get('environment_id')

        if not environment_id:
            return jsonify({'success': False, 'error': 'environment_id is required'}), 400

        # Verify the environment exists
        env_details = directory_paths_db.get_environment_by_id(environment_id)
        if not env_details:
            return jsonify({'success': False, 'error': f'Environment {environment_id} not found'}), 404

        # Set in session
        session['current_environment_id'] = environment_id

        return jsonify({
            'success': True,
            'data': {
                'id': environment_id,
                'name': env_details.get('name'),
                'message': f'Environment "{env_details.get("name")}" is now active'
            }
        })
    except Exception as e:
        logger.error(f"Error setting current environment: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/environments/<environment_id>/variables', methods=['GET'])
def get_environment_variables_by_id(environment_id):
    """Get variables for a specific environment"""
    try:
        variables = directory_paths_db.get_variables_for_environment(str(environment_id))
        if variables is None:
            return jsonify({'success': False, 'error': f'Environment {environment_id} not found'}), 404

        # Format variables for frontend
        formatted_variables = []
        for var in variables:
            formatted_variables.append({
                'id': var.get('id'),
                'name': var.get('name'),
                'initial_value': var.get('initial_value', ''),
                'current_value': var.get('current_value', ''),
                'type': var.get('type', 'default'),
                'description': var.get('description', ''),
                'created_at': var.get('created_at'),
                'updated_at': var.get('updated_at')
            })

        return jsonify({
            'success': True,
            'data': formatted_variables
        })
    except Exception as e:
        logger.error(f"Error getting environment variables for {environment_id}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Environment Variables API endpoints (Legacy - moved to Environments tab)


@app.route('/api/environment_variables', methods=['GET'])
def get_environment_variables():
    """Legacy endpoint: return variables for the current environment from centralized DB."""
    try:
        current_env = get_current_environment_id_from_session() or ensure_default_environment()
        if not current_env:
            return jsonify({'status': 'success', 'variables': []})
        variables = directory_paths_db.get_variables_for_environment(str(current_env))
        legacy = [{'name': v['name'], 'value': v.get('current_value', ''), 'description': v.get('description')}
                  for v in (variables or [])]
        return jsonify({'status': 'success', 'variables': legacy})
    except Exception as e:
        logger.error(f"Error getting environment variables: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500




@app.route('/api/environment_variables', methods=['POST'])
def save_environment_variable():
    """Legacy endpoint: upsert variable into centralized environment_variables for current env."""
    try:
        data = request.get_json() or {}
        name = data.get('name')
        value = data.get('value')
        description = data.get('description')
        if not name or value is None:
            return jsonify({'status': 'error', 'message': "'name' and 'value' are required"}), 400

        env_key = get_current_environment_id_from_session() or ensure_default_environment()
        if not env_key:
            return jsonify({'status': 'error', 'message': 'No current environment selected'}), 400

        import sqlite3
        from utils.database import get_db_path
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute('DELETE FROM environment_variables WHERE environment_id = ? AND name = ?', (str(env_key), name))
        cur.execute(
            """
            INSERT INTO environment_variables (environment_id, name, initial_value, current_value, type, created_at, updated_at)
            VALUES (?, ?, ?, ?, 'default', datetime('now'), datetime('now'))
            """,
            (str(env_key), name, value, value)
        )
        conn.commit()
        conn.close()
        return jsonify({'status': 'success', 'message': f'Environment variable "{name}" saved'} )
    except Exception as e:
        logger.error(f"Error saving environment variable: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500




@app.route('/api/environment_variables/<name>', methods=['DELETE'])
def delete_environment_variable(name):
    """Legacy endpoint: delete variable by name in current environment from centralized DB."""
    try:
        env_key = get_current_environment_id_from_session() or ensure_default_environment()
        if not env_key:
            return jsonify({'status': 'error', 'message': 'No current environment selected'}), 400
        import sqlite3
        from utils.database import get_db_path
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute('DELETE FROM environment_variables WHERE environment_id = ? AND name = ?', (str(env_key), name))
        conn.commit()
        conn.close()
        return jsonify({'status': 'success', 'message': f'Environment variable "{name}" deleted'})

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Environment variable "{name}" deleted successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to delete environment variable'
            }), 500

    except Exception as e:
        logger.error(f"Error deleting environment variable: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500




@app.route('/api/environments', methods=['POST'])
def create_environment_route():
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"error": "Environment name is required"}), 400
    name = data['name'].strip()
    env_id = directory_paths_db.create_environment(name)
    if env_id:
        environment = directory_paths_db.get_environment_by_id(env_id)
        return jsonify({"message": f"Environment '{name}' created successfully", "environment": environment}), 201
    return jsonify({"error": f"Failed to create environment '{name}'. It might already exist."}), 409



@app.route('/api/environments', methods=['GET'])
def get_all_environments_route():
    environments = directory_paths_db.get_all_environments()
    return jsonify(environments), 200



@app.route('/api/environments/<int:env_id>', methods=['PUT'])
def update_environment_route(env_id):
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"error": "New environment name is required"}), 400
    new_name = data['name'].strip()
    success = directory_paths_db.update_environment_name(env_id, new_name)
    if success:
        updated_env = directory_paths_db.get_environment_by_id(env_id)
        return jsonify({"message": f"Environment ID {env_id} updated to '{new_name}'", "environment": updated_env}), 200
    # Check if it failed due to name conflict vs not found
    existing_with_name = directory_paths_db.get_environment_by_name(new_name)
    if existing_with_name and existing_with_name['id'] != env_id:
        return jsonify({"error": f"Failed to update environment. Name '{new_name}' already exists."}), 409
    return jsonify({"error": f"Failed to update environment ID {env_id}. Environment not found or name conflict."}), 404



@app.route('/api/environments/<int:env_id>', methods=['DELETE'])
def delete_environment_route(env_id):
    success = directory_paths_db.delete_environment(env_id)
    if success:
        return jsonify({"message": f"Environment ID {env_id} deleted successfully"}), 200
    return jsonify({"error": f"Failed to delete environment ID {env_id}. Not found."}), 404



@app.route('/api/environments/<int:env_id>/variables', methods=['POST'])
def add_variable_route(env_id):
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"error": "Variable name is required"}), 400

    name = data['name'].strip()
    var_type = data.get('type', 'default')
    initial_value = data.get('initial_value', '')
    current_value = data.get('current_value', '')

    # Check if env exists
    if not directory_paths_db.get_environment_by_id(env_id):
        return jsonify({"error": f"Environment ID {env_id} not found."}), 404

    var_id = directory_paths_db.add_environment_variable(env_id, name, var_type, initial_value, current_value)
    if var_id:
        # Fetch the newly created variable to return it
        # This requires a new DB method or constructing the dict here
        # For now, let's assume we can construct it if needed or just return ID
        return jsonify({"message": "Variable added successfully", "variable_id": var_id, "environment_id": env_id, "name": name, "type": var_type, "initial_value": initial_value, "current_value": current_value }), 201
    return jsonify({"error": f"Failed to add variable '{name}'. It might already exist in this environment."}), 409



@app.route('/api/environments/<env_id>/variables', methods=['GET'])
def get_variables_route(env_id):
    # Support 'current', string IDs, and integer IDs
    if env_id == 'current':
        env_id_to_use = get_current_environment_id_from_session()
    else:
        # Try to use the env_id as-is (could be string or int)
        env_id_to_use = env_id
        # If it's a numeric string, convert to int
        try:
            if env_id.isdigit():
                env_id_to_use = int(env_id)
        except (AttributeError, ValueError):
            pass  # Keep as string if conversion fails

    env_details = directory_paths_db.get_environment_by_id(env_id_to_use)
    if not env_details:
        return jsonify({"error": f"Environment ID {env_id_to_use} not found."}), 404

    variables = directory_paths_db.get_variables_for_environment(env_id_to_use)
    return jsonify(variables), 200



@app.route('/api/environments/<int:env_id>/variables/<int:var_id>', methods=['PATCH'])
def update_environment_variable_route(env_id, var_id):
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Check if env exists
    if not directory_paths_db.get_environment_by_id(env_id):
        return jsonify({"error": f"Environment ID {env_id} not found."}), 404

    # Get current variable to check if it belongs to this environment
    variables = directory_paths_db.get_variables_for_environment(env_id)
    variable = next((v for v in variables if v['id'] == var_id), None)
    if not variable:
        return jsonify({"error": f"Variable ID {var_id} not found in environment {env_id}."}), 404

    # Update only the provided fields
    name = data.get('name', variable['name'])
    var_type = data.get('type', variable['type'])
    initial_value = data.get('initial_value', variable['initial_value'])
    current_value = data.get('current_value', variable['current_value'])

    success = directory_paths_db.update_environment_variable(var_id, name, var_type, initial_value, current_value)
    if success:
        return jsonify({"message": f"Variable ID {var_id} updated successfully"}), 200
    return jsonify({"error": f"Failed to update variable ID {var_id}. Variable not found or name conflict within its environment."}), 404



@app.route('/api/environment_variables/<int:var_id>', methods=['PUT'])
def update_variable_route(var_id):
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip(): # Name is mandatory
        return jsonify({"error": "Variable name is required"}), 400

    name = data['name'].strip()
    var_type = data.get('type', 'default') # Default if not provided
    initial_value = data.get('initial_value', '')
    current_value = data.get('current_value', '')

    success = directory_paths_db.update_environment_variable(var_id, name, var_type, initial_value, current_value)
    if success:
        # Fetch the updated variable to return it
        # Requires a get_variable_by_id method or constructing the dict here
        return jsonify({"message": "Variable updated successfully", "variable": {"id": var_id, "name": name, "type": var_type, "initial_value": initial_value, "current_value": current_value}}), 200
    # Distinguish between not found and name conflict
    # This logic is a bit complex here, the DB method update_environment_variable already logs warnings.
    # For the API, a generic failure might be okay, or more specific checks could be added.
    return jsonify({"error": f"Failed to update variable ID {var_id}. Variable not found or name conflict within its environment."}), 404 # Or 409 for conflict



@app.route('/api/environment_variables/<int:var_id>', methods=['DELETE'])
def delete_variable_route(var_id):
    success = directory_paths_db.delete_environment_variable(var_id)
    if success:
        return jsonify({"message": f"Variable ID {var_id} deleted successfully"}), 200
    return jsonify({"error": f"Failed to delete variable ID {var_id}. Not found."}), 404



@app.route('/api/environments/<int:env_id>/export', methods=['GET'])
def export_environment_route(env_id):
    """Export an environment with all its variables"""
    try:
        # Get environment details
        environment = directory_paths_db.get_environment_by_id(env_id)
        if not environment:
            return jsonify({"error": f"Environment ID {env_id} not found."}), 404

        # Get all variables for this environment
        variables = directory_paths_db.get_variables_for_environment(env_id)

        # Convert variables to import-compatible format (only essential fields)
        simplified_variables = []
        for var in variables:
            simplified_variables.append({
                "name": var['name'],
                "initial_value": var.get('initial_value', ''),
                "current_value": var.get('current_value', ''),
                "type": var.get('type', 'default')
            })

        # Create export data structure that matches import expectations
        export_data = {
            "name": environment['name'],
            "variables": simplified_variables,
            "exported_at": datetime.now().isoformat(),
            "version": "1.0"
        }

        return jsonify({"success": True, "data": export_data}), 200

    except Exception as e:
        logger.error(f"Error exporting environment {env_id}: {str(e)}")
        return jsonify({"error": f"Failed to export environment: {str(e)}"}), 500



@app.route('/api/environments/import', methods=['POST'])
def import_environment_route():
    """Import an environment from exported data"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        if 'name' not in data or not data['name'].strip():
            return jsonify({"error": "Environment name is required"}), 400

        name = data['name'].strip()
        variables = data.get('variables', [])

        # Check if environment with this name already exists
        existing_env = directory_paths_db.get_environment_by_name(name)
        if existing_env:
            # Generate a unique name
            counter = 1
            original_name = name
            while existing_env:
                name = f"{original_name} (Copy {counter})"
                existing_env = directory_paths_db.get_environment_by_name(name)
                counter += 1

        # Create the environment
        env_id = directory_paths_db.create_environment(name)
        if not env_id:
            return jsonify({"error": f"Failed to create environment '{name}'"}), 500

        # Import variables
        imported_variables = []
        for var_data in variables:
            if 'name' not in var_data or not var_data['name'].strip():
                continue  # Skip invalid variables

            var_name = var_data['name'].strip()
            var_type = var_data.get('type', 'default')
            initial_value = var_data.get('initial_value', '')
            current_value = var_data.get('current_value', '')

            var_id = directory_paths_db.add_environment_variable(env_id, var_name, var_type, initial_value, current_value)
            if var_id:
                imported_variables.append({
                    "id": var_id,
                    "name": var_name,
                    "type": var_type,
                    "initial_value": initial_value,
                    "current_value": current_value
                })

        # Get the created environment
        environment = directory_paths_db.get_environment_by_id(env_id)

        return jsonify({
            "success": True,
            "data": {
                "environment": environment,
                "imported_variables_count": len(imported_variables),
                "total_variables_in_import": len(variables)
            }
        }), 201

    except Exception as e:
        logger.error(f"Error importing environment: {str(e)}")
        return jsonify({"error": f"Failed to import environment: {str(e)}"}), 500

# --- New Endpoints for Current Environment and Hover Resolution ---



@app.route('/api/environments/resolve_hover_variable', methods=['POST'])
def resolve_hover_variable():
    data = request.get_json()
    raw_placeholder = data.get('placeholder') # e.g., "env[variable_name]"
    environment_id_override = data.get('environment_id') # Optional: for specific context if needed

    if not raw_placeholder or not raw_placeholder.startswith("env[") or not raw_placeholder.endswith("]"):
        return jsonify({"error": "Invalid placeholder format. Expected env[variable_name]"}), 400

    variable_name = raw_placeholder[4:-1] # Extract 'variable_name'

    current_env_id = environment_id_override if environment_id_override is not None else get_current_environment_id_from_session()

    if current_env_id is None:
        return jsonify({"error": "No environment selected to resolve variable from.", "variable_name": variable_name, "resolved_value": raw_placeholder}), 400 # Or 200 with info

    try:
        current_env_id = int(current_env_id)
    except ValueError:
        return jsonify({"error": "Invalid environment ID format for resolving."}), 400

    resolved_value = get_resolved_variable_value(variable_name, current_env_id)

    if resolved_value is not None:
        return jsonify({"variable_name": variable_name, "environment_id": current_env_id, "resolved_value": resolved_value, "status": "resolved"}), 200
    else:
        # Check if env exists at all
        env_exists = directory_paths_db.get_environment_by_id(current_env_id)
        if not env_exists:
            return jsonify({"variable_name": variable_name, "environment_id": current_env_id, "resolved_value": raw_placeholder, "status": "error", "message": f"Environment ID {current_env_id} not found."}), 404

        # Env exists, but variable doesn't
        return jsonify({"variable_name": variable_name, "environment_id": current_env_id, "resolved_value": raw_placeholder, "status": "not_found", "message": f"Variable '{variable_name}' not found in environment ID {current_env_id}."}), 200



@app.route('/api/environments/export', methods=['GET'])
def export_environments():
    """Export all environments and their variables as JSON"""
    try:
        # Get all environments
        environments = directory_paths_db.get_all_environments()

        # Get current environment ID
        current_env_id = get_current_environment_id_from_session()

        # Build export data structure
        export_data = {
            'environments': [],
            'exported_at': datetime.now().isoformat(),
            'version': '1.0'
        }

        # For each environment, get its variables
        for env in environments:
            variables = directory_paths_db.get_variables_for_environment(env['id'])

            # Convert variables to simpler format for export
            simplified_vars = []
            for var in variables:
                simplified_vars.append({
                    'name': var['name'],
                    'initial_value': var['initial_value'],
                    'current_value': var['current_value']
                })

            # Add environment with its variables to export data
            export_data['environments'].append({
                'name': env['name'],
                'variables': simplified_vars,
                'is_active': env['id'] == current_env_id
            })

        return jsonify(export_data), 200
    except Exception as e:
        logger.error(f"Error exporting environments: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': f"Error exporting environments: {str(e)}"
        }), 500



@app.route('/api/environments/import', methods=['POST'])
def import_environments():
    """Import environments and their variables from JSON"""
    try:
        # Get import data from request
        import_data = request.get_json()

        if not import_data or 'environments' not in import_data or not isinstance(import_data['environments'], list):
            return jsonify({
                'error': 'Invalid import data format'
            }), 400

        results = {
            'environments_created': 0,
            'environments_updated': 0,
            'variables_created': 0,
            'variables_updated': 0,
            'errors': []
        }

        # Get all existing environments for comparison
        existing_envs = directory_paths_db.get_all_environments()

        # Process each environment in the import data
        for env_data in import_data['environments']:
            if not env_data.get('name') or not isinstance(env_data.get('variables', []), list):
                results['errors'].append(f"Skipping environment with missing name or invalid variables")
                continue

            # Check if environment already exists
            existing_env = next((e for e in existing_envs if e['name'] == env_data['name']), None)

            env_id = None
            if existing_env:
                # Environment exists, use its ID
                env_id = existing_env['id']
                results['environments_updated'] += 1
            else:
                # Create new environment
                env_id = directory_paths_db.create_environment(env_data['name'])
                if not env_id:
                    results['errors'].append(f"Failed to create environment '{env_data['name']}'")
                    continue
                results['environments_created'] += 1

            # Get existing variables for this environment
            existing_vars = directory_paths_db.get_variables_for_environment(env_id)

            # Process each variable
            for var_data in env_data['variables']:
                if not var_data.get('name'):
                    results['errors'].append(f"Skipping variable with missing name in environment '{env_data['name']}'")
                    continue

                # Check if variable already exists
                existing_var = next((v for v in existing_vars if v['name'] == var_data['name']), None)

                if existing_var:
                    # Update existing variable
                    success = directory_paths_db.update_environment_variable(
                        existing_var['id'],
                        var_data['name'],
                        'string',  # Always string as per requirement
                        var_data.get('initial_value', ''),
                        var_data.get('current_value', '')
                    )
                    if success:
                        results['variables_updated'] += 1
                    else:
                        results['errors'].append(f"Failed to update variable '{var_data['name']}' in environment '{env_data['name']}'")
                else:
                    # Create new variable
                    var_id = directory_paths_db.add_environment_variable(
                        env_id,
                        var_data['name'],
                        'string',  # Always string as per requirement
                        var_data.get('initial_value', ''),
                        var_data.get('current_value', '')
                    )
                    if var_id:
                        results['variables_created'] += 1
                    else:
                        results['errors'].append(f"Failed to create variable '{var_data['name']}' in environment '{env_data['name']}'")

            # If this was the active environment in the export, make it active now
            if env_data.get('is_active', False):
                session['current_environment_id'] = env_id

        return jsonify({
            'message': 'Import completed successfully',
            'results': results
        }), 200
    except Exception as e:
        logger.error(f"Error importing environments: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': f"Error importing environments: {str(e)}"
        }), 500




