"""
Report Routes

This module contains all report routes for the application.
"""

import os
import json
import base64
import time
import logging
import glob
import sys
import traceback
import shutil
import threading
import signal
import uuid
from pathlib import Path
from datetime import datetime
from functools import wraps

# Flask imports
from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session, Response
from werkzeug.utils import secure_filename

# PIL imports
import PIL.Image
import PIL.ImageDraw
import io

# Other imports
import xml.etree.ElementTree as ET
import requests
import re

# Import app instance from core
from app.core import app

# Import configuration
try:
    from config import DIRECTORIES
except ImportError:
    # Fallback if config.py is not available
    DIRECTORIES = {
        'REPORTS': 'reports_ios',
        'TEST_CASES': 'test_cases',
        'SCREENSHOTS': 'screenshots',
        'REFERENCE_IMAGES': 'reference_images',
        'TEST_SUITES': 'test_suites',
        'RESULTS': 'reports_ios/suites',
        'RECORDINGS': 'recordings',
        'TEMP_FILES': 'temp',
    }

# Import state variables
from app.core.state_manager import (
    device_controllers, players, action_factories,
    current_device, current_device_id, recording_actions,
    test_case_manager, test_suites_manager,
    get_session_device_id, get_session_id, get_client_session_id,
    socketio,
)

# Import utilities
from app.utils.appium_device_controller import AppiumDeviceController
from app.utils.report_generator_db import ReportGeneratorDB
from app.utils.screenshot_manager_db import ScreenshotManagerDB
import zipfile
from io import BytesIO

# Database-first helpers for report generation
_report_generator = ReportGeneratorDB()
_screenshot_manager = ScreenshotManagerDB()
from app.utils.recorder import Recorder
from app.utils.player import Player
from app.actions.action_factory import ActionFactory
from app.utils.reportGenerator import generateReport
from app.utils.custom_report_generator import generate_custom_report
from app.utils.screenshot_manager import screenshot_manager
from app.utils.directory_paths_db import directory_paths_db
from app.utils.environment_resolver import resolve_text_with_env_variables, get_resolved_variable_value
from app.utils.database_report_generator import save_report_data_to_execution_reports

# Set up logger
logger = logging.getLogger(__name__)

# ============================================================================
# ROUTE HANDLERS
# ============================================================================

@app.route('/api/report/initialize', methods=['POST'])
def initialize_report_directory():
    """Initialize the report directory and screenshots folder for the current test execution"""
    global current_report_dir, current_screenshots_dir, current_report_timestamp

    try:
        current_report_dir = None
        current_screenshots_dir = None
        current_report_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        logger.info("DATABASE-ONLY: Report initialization routed to database; no filesystem directories created")

        return jsonify({
            'status': 'success',
            'message': 'Report initialization completed (database-only mode)',
            'report_dir': None,
            'screenshots_dir': None,
            'timestamp': current_report_timestamp
        })
    except Exception as e:
        logger.error(f"Error initializing report directory: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error initializing report directory: {str(e)}"
        }), 500

# Endpoint to delete screenshots when "Execute All" is clicked


@app.route('/api/reports/delete/<path:filename>', methods=['DELETE'])
def delete_report(filename):
    try:
        # Get the reports directory path
        config_data = load_config()
        reports_dir = config_data.get('reports_dir')

        if not reports_dir:
            # Get reports directory from database
            try:
                from utils.directory_paths_db import DirectoryPathsDB
                db = DirectoryPathsDB()
                reports_dir = db.get_path('REPORTS')
                print(f"=== DATABASE REPORTS PATH: {reports_dir} ===")
                if not reports_dir:
                    reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
                    print(f"=== USING FALLBACK PATH: {reports_dir} ===")
                else:
                    print(f"=== USING DATABASE PATH: {reports_dir} ===")
            except Exception as e:
                print(f"=== DATABASE ERROR: {str(e)} ===")
                reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
                print(f"=== USING EXCEPTION FALLBACK PATH: {reports_dir} ===")

        logger.info(f"Delete report called with filename: {filename}")
        logger.info(f"Reports directory: {reports_dir}")

        # Handle special case for "undefined" reports
        if filename == "undefined" or filename == "undefined/mainreport.html":
            logger.info("Detected 'undefined' report - will delete from database only")

            # Delete from database by name
            from utils.database import delete_report_by_name
            deleted = delete_report_by_name("undefined")

            if deleted:
                return jsonify({
                    'status': 'success',
                    'message': 'Undefined report deleted from database successfully'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to delete undefined report from database'
                }), 500

        # Check if this is a new-style report (directory/mainreport.html)
        if '/' in filename and filename.endswith('/mainreport.html'):
            # Extract the directory name
            dir_name = filename.split('/')[0]
            report_dir = os.path.join(reports_dir, dir_name)

            logger.info(f"Detected directory-style report: {dir_name}, path: {report_dir}")

            # Check if the directory exists
            if os.path.exists(report_dir) and os.path.isdir(report_dir):
                # Delete the entire directory
                shutil.rmtree(report_dir)
                logger.info(f"Deleted report directory: {report_dir}")

                # Also delete the ZIP file if it exists
                zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
                if os.path.exists(zip_path):
                    os.remove(zip_path)
                    logger.info(f"Deleted report ZIP file: {zip_path}")

                # Also delete from database
                from utils.database import delete_report_by_dir
                delete_report_by_dir(dir_name)

                return jsonify({
                    'status': 'success',
                    'message': f'Report directory {dir_name} deleted successfully'
                })
            else:
                logger.warning(f"Report directory not found: {report_dir}")

                # Try to delete from database anyway
                from utils.database import delete_report_by_dir
                deleted = delete_report_by_dir(dir_name)

                if deleted:
                    return jsonify({
                        'status': 'success',
                        'message': f'Report {dir_name} deleted from database (directory not found)'
                    })
                else:
                    return jsonify({
                        'status': 'error',
                        'message': f'Report directory not found: {dir_name}'
                    }), 404
        # Check if this is just a directory name (testsuite_execution_YYYYMMDD_HHMMSS)
        elif filename.startswith('testsuite_execution_') or filename.startswith('suite_execution_') or re.match(r'^\d{8}_\d{6}$', filename):
            # This is just a directory name
            dir_name = filename
            report_dir = os.path.join(reports_dir, dir_name)

            logger.info(f"Detected directory name: {dir_name}, path: {report_dir}")

            # Check if the directory exists
            if os.path.exists(report_dir) and os.path.isdir(report_dir):
                # Delete the entire directory
                shutil.rmtree(report_dir)
                logger.info(f"Deleted report directory: {report_dir}")

                # Also delete the ZIP file if it exists
                zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
                if os.path.exists(zip_path):
                    os.remove(zip_path)
                    logger.info(f"Deleted report ZIP file: {zip_path}")

                return jsonify({
                    'status': 'success',
                    'message': f'Report directory {dir_name} deleted successfully'
                })
            else:
                logger.warning(f"Report directory not found: {report_dir}")
                return jsonify({
                    'status': 'error',
                    'message': 'Report directory not found'
                }), 404
        else:
            # Legacy report file
            # Secure the filename to prevent path traversal
            safe_filename = secure_filename(os.path.basename(filename))
            file_path = os.path.join(reports_dir, safe_filename)

            logger.info(f"Detected legacy report file: {safe_filename}, path: {file_path}")

            # Check if file exists
            if not os.path.exists(file_path):
                logger.warning(f"Report file not found: {file_path}")
                return jsonify({
                    'status': 'error',
                    'message': 'Report file not found'
                }), 404

            # Delete the file
            os.remove(file_path)
            logger.info(f"Deleted report file: {file_path}")

            return jsonify({
                'status': 'success',
                'message': f'Report {safe_filename} deleted successfully'
            })
    except Exception as e:
        logger.error(f"Error deleting report: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



@app.route('/api/reports/get_test_data/<suite_id>', methods=['GET'])
def get_test_data(suite_id):
    """Get test data from the database"""
    try:
        from utils.database import get_test_suite
        suite_data = get_test_suite(suite_id)

        if not suite_data:
            return jsonify({
                'status': 'error',
                'message': f"Test suite not found: {suite_id}"
            }), 404

        return jsonify({
            'status': 'success',
            'data': suite_data
        })
    except Exception as e:
        logger.error(f"Error getting test data: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



@app.route('/api/reports/generate/<suite_id>', methods=['GET'])
def generate_report_from_suite_id(suite_id):
    """Generate a report from a test suite ID"""
    try:
        from utils.database import get_latest_execution_id_for_suite

        execution_id = get_latest_execution_id_for_suite(suite_id)

        if not execution_id:
            return jsonify({
                'status': 'error',
                'message': f"No executions found for suite: {suite_id}"
            }), 404

        html_content = _generate_report_html(execution_id)
        if not html_content:
            return jsonify({
                'status': 'error',
                'message': f"Unable to generate report HTML for execution {execution_id}"
            }), 500

        report_url = f"/api/executions/{execution_id}/report/html"
        zip_url = f"/api/executions/{execution_id}/report/zip"

        return jsonify({
            'status': 'success',
            'report_url': report_url,
            'zip_url': zip_url,
            'execution_id': execution_id,
            'message': 'Report ready for download'
        })

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    """Generate a report from test data provided by the client"""
    try:
        payload = request.get_json() or {}
        test_data = payload.get('test_data') or payload

        if not isinstance(test_data, dict):
            return jsonify({
                'status': 'error',
                'message': 'No test data provided'
            }), 400

        execution_id = test_data.get('execution_id') or f"adhoc_{uuid.uuid4().hex}"
        suite_id = test_data.get('suite_id') or f"adhoc_suite_{uuid.uuid4().hex}"

        summary = test_data.get('summary', {})
        status = summary.get('status') or summary.get('overall_status') or 'completed'
        start_time = test_data.get('start_time') or datetime.utcnow().isoformat()
        end_time = test_data.get('end_time') or start_time
        duration = summary.get('duration') or summary.get('total_duration') or 0

        save_report_data_to_execution_reports(
            execution_id=execution_id,
            suite_id=suite_id,
            test_case_id=None,
            platform='iOS',
            status=status,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            report_data_dict=test_data,
        )

        report_url = f"/api/executions/{execution_id}/report/html"
        zip_url = f"/api/executions/{execution_id}/report/zip"

        return jsonify({
            'status': 'success',
            'execution_id': execution_id,
            'suite_id': suite_id,
            'report_url': report_url,
            'zip_url': zip_url,
            'message': 'Report generated successfully'
        })

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Helper functions for report generation

def _generate_report_html(execution_id: str):
    """Return HTML report content for the given execution ID."""
    if not execution_id:
        return None

    try:
        return _report_generator.generate_html_from_database(execution_id)
    except Exception as err:
        logger.error(f"Error generating HTML report for execution {execution_id}: {err}")
        return None


def _fetch_report_data(execution_id: str):
    try:
        return _report_generator.get_report_data_from_db(execution_id)
    except Exception as err:
        logger.error(f"Error fetching report data for execution {execution_id}: {err}")
        return None


def _build_report_zip_bytes(execution_id: str):
    report_data = _fetch_report_data(execution_id)
    html_content = _generate_report_html(execution_id)

    if not report_data or html_content is None:
        return None

    buffer = BytesIO()
    try:
        with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.writestr('report_data.json', json.dumps(report_data, indent=2))
            zipf.writestr('test_execution_report.html', html_content)

            added_files = set()
            for test_case in report_data.get('test_cases', []):
                for step in test_case.get('steps', []):
                    screenshot_filename = step.get('screenshot_filename')
                    if not screenshot_filename or screenshot_filename in added_files:
                        continue

                    blob_info = _screenshot_manager.get_screenshot_blob(screenshot_filename)
                    if blob_info and blob_info[0]:
                        zipf.writestr(
                            f'screenshots/{screenshot_filename}',
                            blob_info[0],
                        )
                        added_files.add(screenshot_filename)
        buffer.seek(0)
        return buffer.read()
    except Exception as err:
        logger.error(f"Error building ZIP for execution {execution_id}: {err}")
        return None


def _serve_execution_html(execution_id: str):
    html_content = _generate_report_html(execution_id)
    if not html_content:
        return jsonify({
            'status': 'error',
            'message': f'No report available for execution {execution_id}'
        }), 404

    response = Response(html_content, mimetype='text/html')
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response


def _serve_execution_zip(execution_id: str):
    zip_bytes = _build_report_zip_bytes(execution_id)
    if not zip_bytes:
        return jsonify({
            'status': 'error',
            'message': f'No report bundle available for execution {execution_id}'
        }), 404

    return send_file(
        BytesIO(zip_bytes),
        as_attachment=True,
        download_name=f'{execution_id}.zip',
        mimetype='application/zip'
    )


def _serve_execution_screenshot(filename: str):
    blob_info = _screenshot_manager.get_screenshot_blob(filename)
    if not blob_info or not blob_info[0]:
        return jsonify({
            'status': 'error',
            'message': f'Screenshot not found: {filename}'
        }), 404

    blob_bytes, _thumb_bytes, mime = blob_info
    response = Response(blob_bytes, mimetype=mime or 'image/png')
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response


# Execution Settings API endpoints (iOS)


@app.route('/api/executions/<execution_id>/report/html', methods=['GET'])
@app.route('/reports/executions/<execution_id>/test_execution_report.html', methods=['GET'])
def execution_report_html(execution_id):
    return _serve_execution_html(execution_id)



@app.route('/api/executions/<execution_id>/report/zip', methods=['GET'])
def execution_report_zip(execution_id):
    return _serve_execution_zip(execution_id)



@app.route('/api/executions/<execution_id>/screenshots/<filename>', methods=['GET'])
def execution_report_screenshot(execution_id, filename):
    return _serve_execution_screenshot(filename)



@app.route('/reports/<path:filename>')
def serve_report(filename):
    """Serve report assets via database-backed storage."""
    parts = filename.split('/')

    if len(parts) >= 2 and parts[0] == 'executions':
        execution_id = parts[1]
        if parts[-1].endswith('.html'):
            return _serve_execution_html(execution_id)
        if len(parts) >= 3 and parts[2] == 'screenshots' and len(parts) >= 4:
            return _serve_execution_screenshot(parts[3])

    logger.warning(f"Legacy report path requested but not supported: {filename}")
    return jsonify({
        'status': 'error',
        'message': 'Legacy report paths are no longer supported. Use /api/executions/<execution_id>/report/html.'
    }), 404


@app.route('/api/reports/latest', methods=['GET'])
def get_latest_report():
    """Get the URL for the most recent execution report stored in the database."""
    try:
        from utils.database import get_db_path
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            '''
            SELECT er.execution_id, er.suite_id, er.status, er.end_time, er.updated_at, er.created_at, ts.name AS suite_name
              FROM execution_reports er
              LEFT JOIN test_suites ts ON er.suite_id = ts.suite_id
             ORDER BY COALESCE(er.end_time, er.updated_at, er.created_at) DESC
             LIMIT 1
            '''
        )
        row = cursor.fetchone()
        conn.close()

        if not row:
            return jsonify({
                'status': 'success',
                'message': 'No reports found',
                'report_url': None
            })

        execution_id = row['execution_id']
        suite_id = row['suite_id']
        suite_name = row['suite_name'] or suite_id or 'Unknown Suite'
        status = row['status'] or 'unknown'

        report_url = f"/api/executions/{execution_id}/report/html"
        zip_url = f"/api/executions/{execution_id}/report/zip"

        return jsonify({
            'status': 'success',
            'report_url': report_url,
            'zip_url': zip_url,
            'execution_id': execution_id,
            'suite_id': suite_id,
            'suite_name': suite_name,
            'status': status
        })
    except Exception as err:
        logger.error(f"Error getting latest report: {err}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(err)
        }), 500



@app.route('/api/reports/list', methods=['GET'])
def list_reports():
    """Get a list of available reports from the database-only store."""
    try:
        from utils.database import get_db_path
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            '''
            SELECT
                er.execution_id,
                er.suite_id,
                er.status,
                er.start_time,
                er.end_time,
                er.duration,
                er.created_at,
                er.updated_at,
                er.platform,
                ts.name AS suite_name
            FROM execution_reports er
            LEFT JOIN test_suites ts ON er.suite_id = ts.suite_id
            ORDER BY COALESCE(er.end_time, er.updated_at, er.created_at) DESC
            '''
        )
        reports = []
        rows = cursor.fetchall()
        conn.close()

        for row in rows:
            row_dict = dict(row)
            execution_id = row_dict.get('execution_id') or row_dict.get('test_execution_id')
            if not execution_id:
                continue

            suite_id = row_dict.get('suite_id')
            suite_name = row_dict.get('suite_name') or suite_id or 'Unknown Suite'
            created_ts = row_dict.get('created_at') or row_dict.get('start_time')
            end_ts = row_dict.get('end_time') or row_dict.get('updated_at')
            duration = row_dict.get('duration') or 0
            status = row_dict.get('status') or 'unknown'
            platform = row_dict.get('platform') or 'iOS'

            reports.append({
                'execution_id': execution_id,
                'suite_id': suite_id,
                'suite_name': suite_name,
                'status': status,
                'platform': platform,
                'created_at': created_ts,
                'completed_at': end_ts,
                'duration': duration,
                'report_url': f"/api/executions/{execution_id}/report/html",
                'zip_url': f"/api/executions/{execution_id}/report/zip",
            })

        return jsonify({
            'status': 'success',
            'reports': reports
        })
    except Exception as err:
        logger.error(f"Error listing reports: {err}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(err)
        }), 500



@app.route('/api/execution/update_retry_results', methods=['POST'])
def update_retry_results():
    """Update original data.json file with retry results"""
    import datetime

    request_start_time = datetime.datetime.now().isoformat()
    logger.info(f"🔄 [RETRY UPDATE START] {request_start_time} - Processing retry results update request")

    try:
        data = request.get_json()
        logger.info(f"📥 [RETRY UPDATE REQUEST] Received payload keys: {list(data.keys()) if data else 'None'}")

        original_execution_id = data.get('original_execution_id')
        test_case_name = data.get('test_case_name')
        test_case_id = data.get('test_case_id')
        retry_results = data.get('retry_results', {})
        execution_status = data.get('execution_status')
        updated_at = data.get('updated_at')

        logger.info(f"📋 [RETRY UPDATE PARAMS] Execution ID: {original_execution_id}, Test Case: {test_case_name} ({test_case_id})")
        logger.info(f"📊 [RETRY UPDATE STATUS] Execution Status: {execution_status}, Has Retry Results: {bool(retry_results)}")

        if not original_execution_id or not test_case_name:
            error_msg = "Missing required parameters: original_execution_id, test_case_name"
            logger.error(f"❌ [RETRY UPDATE ERROR] {error_msg}")
            return jsonify({
                "status": "error",
                "error": error_msg
            }), 400

        # Log the request details for debugging
        logger.info(f"📋 [RETRY UPDATE PARAMS] Execution ID: {original_execution_id}")
        logger.info(f"📋 [RETRY UPDATE PARAMS] Test Case: {test_case_name} (ID: {test_case_id})")
        logger.info(f"📋 [RETRY UPDATE PARAMS] Status: {execution_status}")
        logger.info(f"📋 [RETRY UPDATE PARAMS] Has retry results: {bool(retry_results)}")

        # Step 1: Find the original execution directory
        logger.info(f"📂 [RETRY UPDATE STEP 1] Finding original execution directory")
        reports_dir = get_configured_directory('REPORTS', 'reports_ios')
        logger.info(f"📁 [RETRY UPDATE] Reports directory: {reports_dir}")

        execution_dir = None
        available_dirs = []

        # Look for the execution directory - prioritize exact matches
        execution_dir = None
        try:
            for item in os.listdir(reports_dir):
                available_dirs.append(item)

            # First, look for exact match
            if original_execution_id in available_dirs:
                execution_dir = os.path.join(reports_dir, original_execution_id)
                logger.info(f"✅ [RETRY UPDATE STEP 1] Found exact execution directory: {execution_dir}")
            else:
                # Then look for partial matches, but exclude export_ prefixed directories
                for item in available_dirs:
                    if original_execution_id in item and not item.startswith('export_'):
                        execution_dir = os.path.join(reports_dir, item)
                        logger.info(f"✅ [RETRY UPDATE STEP 1] Found matching execution directory: {execution_dir}")
                        break

                # If still not found, try any match (including export_ directories)
                if not execution_dir:
                    for item in available_dirs:
                        if original_execution_id in item:
                            execution_dir = os.path.join(reports_dir, item)
                            logger.info(f"✅ [RETRY UPDATE STEP 1] Found fallback execution directory: {execution_dir}")
                            break

        except Exception as e:
            logger.error(f"❌ [RETRY UPDATE ERROR] Failed to list reports directory: {e}")
            return jsonify({
                "status": "error",
                "error": f"Failed to access reports directory: {str(e)}"
            }), 500

        if not execution_dir or not os.path.exists(execution_dir):
            logger.error(f"❌ [RETRY UPDATE ERROR] Execution directory not found: {original_execution_id}")
            logger.info(f"📋 [RETRY UPDATE DEBUG] Available directories: {available_dirs[:10]}...")  # Show first 10
            return jsonify({
                "status": "error",
                "error": f"Original execution directory not found: {original_execution_id}",
                "available_directories": available_dirs[:5]  # Return first 5 for debugging
            }), 404

        # Step 2: Load the original data.json file
        logger.info(f"📄 [RETRY UPDATE STEP 2] Loading original data.json file")
        data_json_path = os.path.join(execution_dir, 'data.json')
        logger.info(f"📍 [RETRY UPDATE] Data.json path: {data_json_path}")

        if not os.path.exists(data_json_path):
            logger.error(f"❌ [RETRY UPDATE ERROR] Data.json file not found: {data_json_path}")
            # List files in the directory for debugging
            try:
                dir_contents = os.listdir(execution_dir)
                logger.info(f"📋 [RETRY UPDATE DEBUG] Directory contents: {dir_contents}")
            except Exception as e:
                logger.error(f"❌ [RETRY UPDATE ERROR] Failed to list directory contents: {e}")

            return jsonify({
                "status": "error",
                "error": f"Original data.json file not found: {data_json_path}"
            }), 404

        # Step 3: Read the original data
        logger.info(f"📖 [RETRY UPDATE STEP 3] Reading original data.json file")
        try:
            with open(data_json_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)

            logger.info(f"✅ [RETRY UPDATE STEP 3] Data.json loaded successfully")
            logger.info(f"📊 [RETRY UPDATE DATA] Original data keys: {list(original_data.keys())}")

            test_cases = original_data.get('testCases', [])
            logger.info(f"📋 [RETRY UPDATE DATA] Found {len(test_cases)} test cases in original data")

        except json.JSONDecodeError as e:
            logger.error(f"❌ [RETRY UPDATE ERROR] Invalid JSON in data.json: {e}")
            return jsonify({
                "status": "error",
                "error": f"Invalid JSON in data.json file: {str(e)}"
            }), 500
        except Exception as e:
            logger.error(f"❌ [RETRY UPDATE ERROR] Failed to read data.json: {e}")
            return jsonify({
                "status": "error",
                "error": f"Failed to read data.json file: {str(e)}"
            }), 500

        # Step 4: Find and update the test case
        logger.info(f"🔍 [RETRY UPDATE STEP 4] Finding test case to update: {test_case_name} ({test_case_id})")
        test_case_updated = False
        original_status = None
        test_case_index = -1

        for idx, test_case in enumerate(test_cases):
            tc_name = test_case.get('name')
            tc_id = test_case.get('test_case_id')

            # Match by name first, then by ID if provided
            name_match = tc_name == test_case_name
            id_match = test_case_id and tc_id == test_case_id

            if name_match or id_match:
                logger.info(f"🎯 [RETRY UPDATE FOUND] Found matching test case at index {idx}: {tc_name} ({tc_id})")
                logger.info(f"🔍 [RETRY UPDATE MATCH] Match type: {'name' if name_match else 'id'}")

                original_status = test_case.get('status', 'unknown')
                test_case_index = idx

                # Determine the new status
                new_status = execution_status or (retry_results.get('execution_status') if retry_results else 'passed')

                logger.info(f"📝 [RETRY UPDATE] Updating test case status: {original_status} → {new_status}")

                # Update the test case with retry results
                test_case['original_status'] = original_status  # Save original status first
                test_case['status'] = new_status
                test_case['retry_updated'] = True
                test_case['retry_timestamp'] = updated_at
                test_case['retry_execution_status'] = execution_status

                # Update step details if available
                if retry_results and retry_results.get('steps'):
                    logger.info(f"📋 [RETRY UPDATE] Updating {len(retry_results['steps'])} step details")
                    test_case['steps'] = retry_results['steps']

                # Add retry metadata
                if retry_results:
                    test_case['retry_execution_id'] = retry_results.get('execution_id')
                    test_case['retry_report_path'] = retry_results.get('report_path')

                test_case_updated = True
                logger.info(f"✅ [RETRY UPDATE STEP 4] Updated test case {test_case_name} status to {new_status}")
                break

        if not test_case_updated:
            logger.error(f"❌ [RETRY UPDATE ERROR] Test case not found in data.json: {test_case_name}")
            # Log available test cases for debugging
            available_test_cases = [f"{tc.get('name', 'NO_NAME')} ({tc.get('test_case_id', 'NO_ID')})" for tc in test_cases[:5]]
            logger.info(f"📋 [RETRY UPDATE DEBUG] Available test cases (first 5): {available_test_cases}")

            return jsonify({
                "status": "error",
                "error": f"Test case not found in original data.json: {test_case_name}",
                "available_test_cases": available_test_cases
            }), 404

        # Step 5: Recalculate summary counts
        logger.info(f"📊 [RETRY UPDATE STEP 5] Recalculating summary counts")

        passed_count = len([tc for tc in test_cases if tc.get('status') == 'passed'])
        failed_count = len([tc for tc in test_cases if tc.get('status') == 'failed'])
        total_count = len(test_cases)

        logger.info(f"📈 [RETRY UPDATE SUMMARY] Counts - Passed: {passed_count}, Failed: {failed_count}, Total: {total_count}")

        # Update summary in original data
        if 'summary' not in original_data:
            original_data['summary'] = {}
            logger.info(f"📋 [RETRY UPDATE] Created new summary section")

        # Store previous summary for comparison
        prev_summary = original_data['summary'].copy() if original_data['summary'] else {}

        original_data['summary']['passed'] = passed_count
        original_data['summary']['failed'] = failed_count
        original_data['summary']['total_tests'] = total_count
        original_data['summary']['last_retry_update'] = updated_at
        original_data['summary']['retry_updated_test_case'] = test_case_name
        original_data['summary']['retry_updated_from_status'] = original_status
        original_data['summary']['retry_updated_to_status'] = execution_status or 'passed'

        # Update overall status if all tests are now passing
        prev_overall_status = original_data.get('status', 'unknown')
        if failed_count == 0:
            original_data['status'] = 'passed'
            logger.info(f"🎉 [RETRY UPDATE] All tests now passing - updated overall status to 'passed'")
        elif failed_count > 0:
            original_data['status'] = 'failed'
            logger.info(f"⚠️ [RETRY UPDATE] Some tests still failing - overall status remains 'failed'")

        logger.info(f"📊 [RETRY UPDATE CHANGES] Summary changes: {prev_summary} → {original_data['summary']}")
        logger.info(f"📊 [RETRY UPDATE CHANGES] Overall status: {prev_overall_status} → {original_data.get('status')}")

        # Step 6: Write the updated data back to the file
        logger.info(f"💾 [RETRY UPDATE STEP 6] Writing updated data back to file")
        try:
            # Create backup of original file
            backup_path = data_json_path + '.backup'
            import shutil
            shutil.copy2(data_json_path, backup_path)
            logger.info(f"📋 [RETRY UPDATE] Created backup: {backup_path}")

            # Write updated data
            with open(data_json_path, 'w', encoding='utf-8') as f:
                json.dump(original_data, f, indent=2, ensure_ascii=False)

            logger.info(f"✅ [RETRY UPDATE STEP 6] Successfully wrote updated data to file")

            # Verify the file was written correctly
            with open(data_json_path, 'r', encoding='utf-8') as f:
                verification_data = json.load(f)

            verification_test_case = next((tc for tc in verification_data.get('testCases', [])
                                         if tc.get('name') == test_case_name or tc.get('test_case_id') == test_case_id), None)

            if verification_test_case and verification_test_case.get('retry_updated'):
                logger.info(f"✅ [RETRY UPDATE VERIFICATION] File write verified successfully")
            else:
                logger.warning(f"⚠️ [RETRY UPDATE VERIFICATION] File write verification failed")

        except Exception as e:
            logger.error(f"❌ [RETRY UPDATE ERROR] Failed to write updated data: {e}")
            return jsonify({
                "status": "error",
                "error": f"Failed to write updated data to file: {str(e)}"
            }), 500

        request_end_time = datetime.datetime.now().isoformat()
        logger.info(f"🎉 [RETRY UPDATE SUCCESS] {request_end_time} - Successfully updated original data.json for execution {original_execution_id}")

        return jsonify({
            "status": "success",
            "message": f"Original data.json updated for test case: {test_case_name}",
            "updated_summary": {
                "passed": passed_count,
                "failed": failed_count,
                "total": total_count
            },
            "test_case_changes": {
                "name": test_case_name,
                "test_case_id": test_case_id,
                "original_status": original_status,
                "new_status": execution_status or 'passed',
                "retry_timestamp": updated_at
            },
            "overall_status": original_data.get('status'),
            "backup_created": backup_path
        })

    except Exception as e:
        request_error_time = datetime.datetime.now().isoformat()
        logger.error(f"💥 [RETRY UPDATE FAILED] {request_error_time} - Error updating retry results: {str(e)}")
        logger.error(f"🔍 [RETRY UPDATE ERROR DETAILS] Exception type: {type(e).__name__}")
        logger.error(f"🔍 [RETRY UPDATE ERROR DETAILS] Stack trace:", exc_info=True)

        return jsonify({
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__,
            "timestamp": request_error_time
        }), 500



@app.route('/api/execution/import/<execution_id>', methods=['GET'])
def import_execution_data(execution_id):
    """Import execution data from database first, fallback to data.json file"""
    try:
        # Using database for directory paths
        from utils.database import get_execution_tracking_for_suite, get_test_steps_for_suite

        logger.info(f"Importing execution data for: {execution_id}")

        # PRIORITY 1: Try data.json file first (contains correct test case names)
        execution_data = None
        # Get reports directory from database
        try:
            from utils.directory_paths_db import DirectoryPathsDB
            db = DirectoryPathsDB()
            reports_dir = db.get_path('REPORTS')
            if not reports_dir:
                reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        except Exception:
            reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        # Support exec_* identifiers that are stored under temp_exec_*
        candidate_dirs = [os.path.join(reports_dir, execution_id)]
        if execution_id.startswith('exec_'):
            candidate_dirs.insert(0, os.path.join(reports_dir, f"temp_{execution_id}"))
        # Also scan for a directory that contains this execution id
        selected_dir = None
        for d in candidate_dirs:
            if os.path.isdir(d):
                selected_dir = d
                break
        if not selected_dir:
            try:
                for entry in os.listdir(reports_dir):
                    entry_path = os.path.join(reports_dir, entry)
                    if os.path.isdir(entry_path) and execution_id in entry:
                        selected_dir = entry_path
                        break
            except Exception:
                pass
        execution_dir = selected_dir or os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        logger.info(f"Checking for data.json file: {data_json_path}")

        if os.path.exists(data_json_path):
            logger.info(f"Found data.json file, using it as primary source")

            # Read the execution data from file
            with open(data_json_path, 'r') as f:
                execution_data = json.load(f)

            # IMPORTANT: Do NOT clean the execution data when importing from data.json
            # The data.json file contains the correct test case names and should be preserved as-is
            logger.info(f"Successfully loaded execution data from data.json file for: {execution_data.get('name', 'Unknown')}")
            logger.info(f"Test cases in data.json: {[tc.get('name') for tc in execution_data.get('testCases', [])]}")
        else:
            logger.info(f"No data.json file found, trying database reconstruction")

            # FALLBACK: Try to reconstruct from database
            try:
                execution_tracking = get_execution_tracking_for_suite(execution_id)
                if execution_tracking:
                    logger.info(f"Found {len(execution_tracking)} execution tracking records in database")
                    execution_data = _reconstruct_execution_data_from_database(execution_id, execution_tracking)
                    if execution_data:
                        # Only clean reconstructed data from database, not data.json files
                        execution_data = _clean_execution_data_for_import(execution_data)
                        logger.info(f"Successfully reconstructed and cleaned execution data from database for: {execution_data.get('name', 'Unknown')}")
                    else:
                        logger.warning(f"Database reconstruction failed for execution: {execution_id}")
                else:
                    logger.warning(f"No execution tracking records found in database for: {execution_id}")
            except Exception as db_error:
                logger.warning(f"Could not reconstruct from database: {db_error}")

            if not execution_data:
                return jsonify({
                    'status': 'error',
                    'message': f'No execution data found in data.json file or database for execution: {execution_id}'
                }), 404

        # ENHANCEMENT: Check if there are any retry results in the database for this execution
        # and update the data.json with the latest retry status
        try:
            from utils.database import update_data_json_with_retry_results
            logger.info(f"Checking for retry results in database for execution: {execution_id}")

            # Try to update data.json with any retry results from database
            retry_update_success = update_data_json_with_retry_results(execution_id, execution_id)
            if retry_update_success:
                logger.info(f"Updated data.json with retry results for execution: {execution_id}")

                # Reload the updated data.json
                with open(data_json_path, 'r') as f:
                    execution_data = json.load(f)
                logger.info(f"Reloaded updated execution data from file")
            else:
                logger.info(f"No retry updates needed for execution: {execution_id}")
        except Exception as retry_error:
            logger.warning(f"Could not update data.json with retry results: {retry_error}")

        return jsonify({
            'status': 'success',
            'execution_data': execution_data,
            'execution_id': execution_id,
            'source': 'file_with_retry_updates'
        })

    except Exception as e:
        logger.error(f"Error importing execution data: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



@app.route('/api/execution/import-to-database/<execution_id>', methods=['POST'])
def import_execution_data_to_database(execution_id):
    """Import execution data from a data.json file into the database"""
    try:
        # Using database for directory paths
        from utils.database import track_test_execution

        # Get reports directory from database
        try:
            from utils.directory_paths_db import DirectoryPathsDB
            db = DirectoryPathsDB()
            reports_dir = db.get_path('REPORTS')
            if not reports_dir:
                reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        except Exception:
            reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        execution_dir = os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        logger.info(f"Importing execution data to database from: {data_json_path}")

        if not os.path.exists(data_json_path):
            return jsonify({
                'status': 'error',
                'message': f'Execution data file not found: {data_json_path}'
            }), 404

        # Read the execution data
        with open(data_json_path, 'r') as f:
            execution_data = json.load(f)

        # Extract execution metadata
        test_execution_id = execution_data.get('test_execution_id', execution_id)
        test_suite_id = execution_data.get('test_suite_id', execution_id)
        suite_name = execution_data.get('name', 'Unknown Suite')

        logger.info(f"Importing execution data: {suite_name} (execution_id: {test_execution_id})")

        # Import each test case and its steps with consistent ID handling
        imported_count = 0
        for test_case_idx, test_case in enumerate(execution_data.get('testCases', [])):
            # Use test_case_id as primary identifier, ensure it exists
            test_case_id = test_case.get('test_case_id')
            test_case_name = test_case.get('name', f'Test Case {test_case_idx + 1}')
            test_case_filename = test_case.get('filename')

            # If no filename, generate from name
            if not test_case_filename:
                test_case_filename = f'{test_case_name}.json'

            # Ensure test_case_id exists for consistent tracking
            if not test_case_id:
                # Generate test_case_id from filename or name
                import uuid
                test_case_id = str(uuid.uuid4())[:8].upper()
                logger.warning(f"Generated test_case_id {test_case_id} for test case {test_case_name}")

            test_case_status = test_case.get('status', 'unknown')

            logger.info(f"Importing test case: {test_case_name} (ID: {test_case_id}, filename: {test_case_filename})")

            # Import each step/action with proper status mapping
            for step_idx, step in enumerate(test_case.get('steps', [])):
                action_id = step.get('action_id')
                action_type = step.get('action_type', 'unknown')
                raw_step_status = step.get('status', 'unknown')
                step_status = _map_database_status_to_report_status(raw_step_status)
                retry_count = step.get('retry_count', 0)

                # Import this step into the database
                track_test_execution(
                    suite_id=test_suite_id,
                    test_idx=test_case_idx,
                    filename=test_case_filename,
                    status=step_status,
                    retry_count=retry_count,
                    max_retries=0,
                    error=step.get('error'),
                    in_progress=False,
                    step_idx=step_idx,
                    action_type=action_type,
                    action_params={'description': step.get('description', '')},
                    action_id=action_id,
                    test_case_id=test_case_id,
                    test_execution_id=test_execution_id
                )
                imported_count += 1

        logger.info(f"Successfully imported {imported_count} execution steps to database")

        return jsonify({
            'status': 'success',
            'message': f'Successfully imported {imported_count} execution steps to database',
            'imported_count': imported_count
        })

    except Exception as e:
        logger.error(f"Error importing execution data to database: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



@app.route('/api/get_execution_context', methods=['GET'])
def get_execution_context():
    """Get the current execution context (suite_id, execution_id, etc.)"""
    try:
        context = {
            'suite_id': getattr(app, 'current_suite_id', None),
            'execution_id': getattr(app, 'current_execution_id', None),
            'test_idx': getattr(app, 'current_test_idx', None),
            'test_case_name': getattr(app, 'current_test_case_name', None),
            'is_test_suite_execution': globals().get('is_test_suite_execution', False)
        }

        logger.info(f"Returning execution context: {context}")
        return jsonify({
            'status': 'success',
            'context': context
        })
    except Exception as e:
        logger.error(f"Error getting execution context: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500



@app.route('/api/get_test_execution_status', methods=['POST'])
def get_test_execution_status():
    """Get the most recent execution status for a specific test case"""
    try:
        data = request.get_json()
        suite_id = data.get('suite_id')
        filename = data.get('filename')
        test_case_name = data.get('test_case_name')

        if not suite_id:
            return jsonify({'error': 'suite_id is required'}), 400

        from utils.database import get_final_test_case_status

        # Get the final status considering all retries
        status_data = get_final_test_case_status(
            suite_id=suite_id,
            test_case_id=data.get('test_case_id'),
            filename=filename,
            test_idx=data.get('test_idx')
        )

        if status_data and status_data.get('status') != 'unknown':
            return jsonify({
                'status': status_data.get('status', 'unknown'),
                'total_actions': status_data.get('total_actions', 0),
                'actions': status_data.get('actions', {})
            })
        else:
            return jsonify({
                'status': 'unknown',
                'message': 'No execution data found'
            })

    except Exception as e:
        logger.error(f"Error getting test execution status: {str(e)}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/execution/get-suite-status-from-db', methods=['POST'])
def get_suite_status_from_db():
    """Get the current status of all test cases in a suite directly from database"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')

        if not execution_id:
            return jsonify({'error': 'execution_id is required'}), 400

        from utils.database import get_final_test_case_status, get_test_execution_data

        # Get all test cases for this execution
        execution_data = get_test_execution_data(execution_id)
        if not execution_data:
            return jsonify({'error': 'No execution data found'}), 404

        # Group by test case to get unique test cases
        test_cases = {}
        for entry in execution_data:
            filename = entry.get('filename')
            test_idx = entry.get('test_idx')
            if filename and filename not in test_cases:
                test_cases[filename] = {
                    'filename': filename,
                    'test_idx': test_idx,
                    'test_case_id': entry.get('test_case_id')
                }

        # Get current status for each test case from database using UUID-based system
        test_case_statuses = {}
        for filename, test_case_info in test_cases.items():
            # Use the resolved suite_id from the execution data
            resolved_suite_id = execution_data[0].get('suite_id', execution_id) if execution_data else execution_id

            # PREFERRED: Try UUID-based lookup first
            test_case_id = test_case_info.get('test_case_id')
            status_data = None

            if test_case_id:
                status_data = get_final_test_case_status(
                    suite_id=resolved_suite_id,
                    test_case_id=test_case_id
                )

            # FALLBACK: Use filename-based lookup if UUID fails
            if not status_data or status_data.get('status') == 'unknown':
                status_data = get_final_test_case_status(
                    suite_id=resolved_suite_id,
                    filename=filename,
                    test_idx=test_case_info.get('test_idx')
                )

            test_case_statuses[filename] = {
                'status': status_data.get('status', 'unknown'),
                'actions': status_data.get('actions', {}),
                'total_actions': status_data.get('total_actions', 0)
            }

        return jsonify({
            'status': 'success',
            'execution_id': execution_id,
            'test_cases': test_case_statuses
        })

    except Exception as e:
        logger.error(f"Error getting suite status from database: {str(e)}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/reports/generate-from-db', methods=['POST'])
def generate_report_from_database():
    """Generate HTML report directly from database instead of data.json"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')

        if not execution_id:
            return jsonify({'error': 'execution_id is required'}), 400

        from utils.database import get_test_execution_data, get_final_test_case_status
        from utils.reportGenerator import generate_report_from_database_data
        # Using database for directory paths

        # Get execution data from database
        execution_data = get_test_execution_data(execution_id)
        if not execution_data:
            return jsonify({'error': 'No execution data found'}), 404

        # Group by test case to get unique test cases
        test_cases = {}
        for entry in execution_data:
            filename = entry.get('filename')
            test_idx = entry.get('test_idx')
            if filename and filename not in test_cases:
                test_cases[filename] = {
                    'filename': filename,
                    'test_idx': test_idx,
                    'test_case_id': entry.get('test_case_id'),
                    'name': filename.replace('.json', ''),
                    'steps': []
                }

        # Get current status and steps for each test case from database
        for filename, test_case_info in test_cases.items():
            status_data = get_final_test_case_status(
                suite_id=execution_id,
                test_case_id=test_case_info.get('test_case_id'),
                filename=filename,
                test_idx=test_case_info.get('test_idx')
            )

            test_case_info['status'] = status_data.get('status', 'unknown')
            test_case_info['actions'] = status_data.get('actions', {})
            test_case_info['total_actions'] = status_data.get('total_actions', 0)

        # Generate report using database data
        # Get reports directory from database
        try:
            from utils.directory_paths_db import DirectoryPathsDB
            db = DirectoryPathsDB()
            reports_dir = db.get_path('REPORTS')
            if not reports_dir:
                reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        except Exception:
            reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        report_path = generate_report_from_database_data(execution_id, test_cases, reports_dir)

        # Return the test cases data for frontend consumption
        return jsonify({
            'status': 'success',
            'test_cases': list(test_cases.values()),
            'execution_id': execution_id,
            'message': 'Suite status retrieved successfully from database'
        })

    except Exception as e:
        logger.error(f"Error getting suite status from database: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'message': 'Failed to get suite status from database'
        }), 500



@app.route('/api/execution/get-data-json', methods=['POST'])
def get_execution_data_json():
    """Get the data.json file for a specific execution"""
    try:
        data = request.get_json()
        execution_id = data.get('execution_id')

        if not execution_id:
            return jsonify({'error': 'execution_id is required'}), 400

        # Get reports directory from database
        try:
            from utils.directory_paths_db import DirectoryPathsDB
            db = DirectoryPathsDB()
            reports_dir = db.get_path('REPORTS')
            if not reports_dir:
                reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        except Exception:
            reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')

        # Find the execution report directory
        execution_dir = os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        # If the original execution folder doesn't exist, check for export folders
        if not os.path.exists(data_json_path):
            # Look for export folders that contain this execution ID
            for folder_name in os.listdir(reports_dir):
                if folder_name.startswith(f'export_{execution_id}') and os.path.isdir(os.path.join(reports_dir, folder_name)):
                    export_data_json_path = os.path.join(reports_dir, folder_name, 'data.json')
                    if os.path.exists(export_data_json_path):
                        data_json_path = export_data_json_path
                        break

        if os.path.exists(data_json_path):
            with open(data_json_path, 'r') as f:
                data_json = json.load(f)

            return jsonify({
                'status': 'success',
                'data': data_json
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'data.json not found'
            }), 404

    except Exception as e:
        logger.error(f"Error getting data.json: {str(e)}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/reports/download/<path:filename>', methods=['GET'])
def download_report(filename):
    """Download a report file"""
    try:
        # Define directories to check
        directories_to_check = []

        # Get the reports directory from database
        try:
            reports_dir = directory_paths_db.get_path('REPORTS')
            if not reports_dir:
                reports_dir = 'reports_ios'  # fallback
        except Exception:
            reports_dir = 'reports_ios'  # fallback
        logger.info(f"Download report called with filename: {filename}")
        directories_to_check.append(reports_dir)

        # Also check the alternate reports directory
        try:
            alt_reports_dir = str(DIRECTORIES['REPORTS'])
            if os.path.exists(alt_reports_dir) and os.path.isdir(alt_reports_dir):
                logger.info(f"Also checking alternate reports directory: {alt_reports_dir}")
                directories_to_check.append(alt_reports_dir)
        except (NameError, KeyError) as e:
            logger.debug(f"DIRECTORIES not available or REPORTS key missing: {e}")

        # Secure the filename to prevent path traversal
        safe_filename = secure_filename(os.path.basename(filename))

        # Try to find the file in any of the directories
        file_path = None
        for current_dir in directories_to_check:
            temp_path = os.path.join(current_dir, safe_filename)
            if os.path.exists(temp_path):
                file_path = temp_path
                logger.info(f"Found report file: {file_path}")
                break

        # If not found, try looking in subdirectories
        if not file_path:
            logger.warning(f"Report file not found in main directories, checking subdirectories")
            for current_dir in directories_to_check:
                for root, dirs, files in os.walk(current_dir):
                    if safe_filename in files:
                        file_path = os.path.join(root, safe_filename)
                        logger.info(f"Found report file in subdirectory: {file_path}")
                        break
                if file_path:
                    break

        # If still not found, return 404
        if not file_path:
            logger.error(f"Report file not found: {safe_filename}")
            return jsonify({
                'status': 'error',
                'message': 'Report file not found'
            }), 404

        # Return the file as an attachment
        return send_file(file_path, as_attachment=True, download_name=safe_filename)
    except Exception as e:
        logger.error(f"Error downloading report: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



@app.route('/api/reports/download_zip/<path:filename>', methods=['GET'])
def download_zip_report(filename):
    """Compatibility wrapper for legacy zip download endpoint."""
    execution_id = os.path.splitext(os.path.basename(filename))[0]
    return _serve_execution_zip(execution_id)


@app.route('/api/reports/export/<report_id>', methods=['POST'])
def export_custom_report(report_id):
    """
    Generate a custom report for the given report ID and return a download URL

    This endpoint creates a custom HTML report and packages it as a ZIP file
    with screenshots and action logs. The format follows the Action List view
    but without control buttons and with screenshot links.

    Args:
        report_id (str): The ID of the report to export

    Returns:
        JSON response with success status, message, and download URL
    """
    logger.info(f"=== EXPORT REQUEST START === Report ID: {report_id}")

    try:
        # Validate report_id
        if not report_id or report_id.strip() == '':
            logger.error(f"Invalid report_id provided: '{report_id}'")
            return jsonify({
                'success': False,
                'message': 'Invalid report ID'
            })

        # Log retry data validation before export
        try:
            from utils.database import resolve_execution_id_to_suite_id, get_execution_tracking_for_suite
            actual_suite_id = resolve_execution_id_to_suite_id(report_id)
            execution_data = get_execution_tracking_for_suite(actual_suite_id)

            if execution_data:
                retry_entries = [entry for entry in execution_data if entry.get('action_type') == 'retry_update']
                logger.info(f"Pre-export validation: Found {len(execution_data)} total execution entries, {len(retry_entries)} retry entries for suite {actual_suite_id}")

                # Log retry status summary
                for entry in retry_entries:
                    logger.info(f"Retry entry: action_id={entry.get('action_id')}, status={entry.get('status')}, retry_count={entry.get('retry_count')}")
            else:
                logger.warning(f"Pre-export validation: No execution data found for report_id {report_id} (resolved to suite_id: {actual_suite_id})")
        except Exception as validation_error:
            logger.warning(f"Pre-export validation failed: {validation_error}")

        # Generate the custom report
        logger.info(f"Starting custom report generation for report_id: {report_id}")
        success, message, filename = generate_custom_report(report_id, app.root_path)

        if success and filename:
            # Create download URL
            download_url = f'/api/reports/download_export/{filename}'
            logger.info(f"=== EXPORT SUCCESS === Report ID: {report_id}, Filename: {filename}")

            return jsonify({
                'success': True,
                'message': message,
                'download_url': download_url,
                'filename': filename
            })
        else:
            logger.error(f"=== EXPORT FAILED === Report ID: {report_id}, Message: {message}")
            return jsonify({
                'success': False,
                'message': message or 'Failed to generate custom report'
            })
    except Exception as e:
        logger.error(f"=== EXPORT ERROR === Report ID: {report_id}, Error: {str(e)}")
        logger.exception(f"Full exception details for export_custom_report: {e}")
        return jsonify({
            'success': False,
            'message': f'Error generating custom report: {str(e)}'
        })




@app.route('/api/reports/download_export/<path:filename>', methods=['GET'])
def download_export_report(filename):
    """
    Download a custom report export

    Args:
        filename (str): The filename of the export to download

    Returns:
        The file for download or an error response
    """
    try:
        # Try multiple possible directories for the export file
        possible_directories = []

        # 1) Standard reports directory from centralized DB (absolute preferred)
        try:
            reports_dir = directory_paths_db.get_path('REPORTS')
            if reports_dir:
                possible_directories.append(os.path.abspath(reports_dir))
                logger.info(f"Checking main reports directory (DB): {reports_dir}")
        except Exception as e:
            logger.warning(f"Error getting reports directory from database: {e}")

        # 2) Project-root reports_ios fallback (always absolute)
        project_root = os.path.abspath(os.path.join(app.root_path, os.pardir))
        proj_reports = os.path.join(project_root, 'reports_ios')
        possible_directories.append(proj_reports)
        logger.info(f"Checking project reports directory: {proj_reports}")

        # 3) Classic relative fallback resolved to absolute
        fallback_reports_dir = 'reports_ios'
        possible_directories.append(os.path.abspath(fallback_reports_dir))
        logger.info(f"Checking fallback reports directory: {os.path.abspath(fallback_reports_dir)}")

        # 4) Also check alternate legacy reports directory
        try:
            alt_reports_dir = str(DIRECTORIES['REPORTS'])
            if os.path.exists(alt_reports_dir):
                possible_directories.append(alt_reports_dir)
                logger.info(f"Checking alternate reports directory: {alt_reports_dir}")
        except (NameError, KeyError) as e:
            logger.debug(f"DIRECTORIES not available or REPORTS key missing: {e}")

        # Check each possible directory for the file
        file_found = False
        file_path = None

        for dir_path in possible_directories:
            if not os.path.exists(dir_path):
                logger.warning(f"Directory does not exist: {dir_path}")
                continue

            temp_path = os.path.join(dir_path, filename)
            logger.info(f"Checking for file at: {temp_path}")

            if os.path.exists(temp_path) and os.path.isfile(temp_path):
                file_path = temp_path
                file_found = True
                logger.info(f"Found export file at: {file_path}")
                break

        if not file_found or not file_path:
            # If not found, do one more thorough search
            logger.warning(f"Export file not found in standard locations, performing deeper search")
            for dir_path in possible_directories:
                if not os.path.exists(dir_path):
                    continue

                # Search for any file with similar name (if filename contains timestamp)
                export_prefix = None
                if '_20' in filename:  # Check if it has a timestamp part
                    export_prefix = filename.split('_20')[0]

                if export_prefix:
                    for entry in os.listdir(dir_path):
                        if entry.startswith(export_prefix) and entry.endswith('.zip'):
                            file_path = os.path.join(dir_path, entry)
                            file_found = True
                            logger.info(f"Found similar export file: {file_path}")
                            break

                if file_found:
                    break

        if not file_found or not file_path:
            logger.error(f"Export file not found after searching all directories: {filename}")

            # List all available zip files in reports directories for debugging
            all_zip_files = []
            for dir_path in possible_directories:
                if os.path.exists(dir_path):
                    for entry in os.listdir(dir_path):
                        if entry.endswith('.zip'):
                            all_zip_files.append(os.path.join(dir_path, entry))

            if all_zip_files:
                logger.error(f"Available ZIP files in report directories:")
                for zip_file in all_zip_files[:10]:  # Show first 10 files
                    logger.error(f"  - {zip_file}")
            else:
                logger.error("No ZIP files found in any reports directory")

            return jsonify({
                'success': False,
                'message': 'Export file not found. Please try exporting again.'
            }), 404

        # Ensure file_path is absolute before sending
        if not os.path.isabs(file_path):
            file_path = os.path.abspath(file_path)

        logger.info(f"Sending export file for download: {file_path}")
        logger.info(f"File exists check: {os.path.exists(file_path)}")
        logger.info(f"File is absolute: {os.path.isabs(file_path)}")

        # Return the file for download with appropriate content type
        return send_file(
            file_path,
            mimetype='application/zip',
            as_attachment=True,
            download_name=os.path.basename(file_path)
        )
    except Exception as e:
        logger.exception(f"Error in download_export_report: {e}")
        return jsonify({'success': False, 'message': f'Error downloading export: {str(e)}'}), 500

# ===== Test Executions APIs =====


@app.route('/api/executions', methods=['GET'])
def api_list_executions():
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        from utils.database import list_executions, count_executions
        items = list_executions(page, page_size)
        total = count_executions()
        pages = (total + page_size - 1) // page_size
        return jsonify({
            'success': True,
            'items': items,
            'page': page,
            'page_size': page_size,
            'total': total,
            'pages': pages
        })
    except Exception as e:
        logger.exception(f"Error listing executions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500




@app.route('/api/executions/<execution_id>/export', methods=['POST'])
def api_export_execution(execution_id):
    """
    Export an execution report as a downloadable ZIP file (iOS) using the consolidated BLOB-based generator.
    Always generates a fresh ZIP that contains ONLY test_execution_report.html and action_log.txt.
    """
    try:
        logger.info(f"[Consolidated Export] Generating custom export for execution (iOS): {execution_id}")
        success, message, filename = generate_custom_report(execution_id, app.root_path, in_place=True)
        logger.info(f"generate_custom_report returned: success={success}, message='{message}', filename='{filename}'")
        if success and filename:
            download_url = f"/api/reports/download_export/{filename}"
            return jsonify({'success': True, 'download_url': download_url})
        return jsonify({'success': False, 'error': message or 'Failed to generate export'}), 500
    except Exception as e:
        logger.exception(f"Error exporting execution (iOS): {e}")
        return jsonify({'success': False, 'error': str(e)}), 500




@app.route('/api/executions/<execution_id>', methods=['DELETE'])
def api_delete_execution(execution_id):
    try:
        # Delete DB records from all relevant tables
        import sqlite3
        from utils.database import get_db_path, resolve_execution_id_to_suite_id

        logger.info(f"Deleting execution: {execution_id}")

        # Get the database connection
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Delete from execution_tracking table
        cursor.execute('DELETE FROM execution_tracking WHERE test_execution_id = ?', (execution_id,))
        tracking_deleted = cursor.rowcount
        logger.info(f"Deleted {tracking_deleted} records from execution_tracking")

        # Delete from screenshots table
        cursor.execute('DELETE FROM screenshots WHERE test_execution_id = ?', (execution_id,))
        screenshots_deleted = cursor.rowcount
        logger.info(f"Deleted {screenshots_deleted} records from screenshots")

        # Delete from execution_reports table
        cursor.execute('DELETE FROM execution_reports WHERE test_execution_id = ?', (execution_id,))
        reports_deleted = cursor.rowcount
        logger.info(f"Deleted {reports_deleted} records from execution_reports")

        # Commit the database changes BEFORE any other operations
        conn.commit()
        conn.close()

        logger.info(f"Successfully deleted execution {execution_id} from database")

        # Delete report directory if exists (this is optional and shouldn't affect the database operation)
        try:
            suite_id = resolve_execution_id_to_suite_id(execution_id)
        except Exception as e:
            logger.warning(f"Could not resolve suite_id for {execution_id}: {e}")
            suite_id = execution_id
        try:
            from utils.directory_paths_db import DirectoryPathsDB
            db = DirectoryPathsDB()
            reports_dir = db.get_path('REPORTS') or os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        except Exception:
            reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports_ios')
        exec_dir = os.path.join(reports_dir, suite_id if suite_id else execution_id)
        if os.path.isdir(exec_dir):
            import shutil
            shutil.rmtree(exec_dir, ignore_errors=True)
            logger.info(f"Deleted directory: {exec_dir}")

        logger.info(f"Successfully deleted execution {execution_id}")
        return jsonify({'success': True, 'message': f'Deleted execution {execution_id}'})
    except Exception as e:
        logger.exception(f"Error deleting execution: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500



