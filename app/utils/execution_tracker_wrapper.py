"""
Execution Tracker Wrapper - Database-only Integration Layer.

This module provides a simple wrapper that adds database-only tracking so the
primary application no longer depends on any filesystem-based execution
artifacts.

Usage in player.py:
    from .execution_tracker_wrapper import ExecutionTrackerWrapper
    
    # In __init__:
    self.execution_wrapper = ExecutionTrackerWrapper()
    
    # When starting test suite:
    execution_id = self.execution_wrapper.start_execution(suite_id, suite_name)
    
    # When capturing screenshot:
    screenshot_filename = self.execution_wrapper.capture_screenshot(
        device_controller, execution_id, step_idx, action_id, test_case_id, suite_id
    )
    
    # When tracking step:
    self.execution_wrapper.track_step(
        execution_id, test_case_id, step_idx, action_type, status,
        screenshot_filename, action_id, suite_id, filename
    )
    
    # When completing execution:
    report_data = self.execution_wrapper.complete_execution(execution_id, status)
"""

import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class ExecutionTrackerWrapper:
    """
    Wrapper class that integrates database-only execution tracking
    with minimal changes to existing code.
    """
    
    def __init__(self):
        """Initialize database-only trackers"""
        try:
            from .database_execution_tracker import DatabaseExecutionTracker
            from .screenshot_manager_db import ScreenshotManagerDB
            from .report_generator_db import ReportGeneratorDB
            
            self.db_tracker = DatabaseExecutionTracker()
            self.screenshot_manager = ScreenshotManagerDB()
            self.report_generator = ReportGeneratorDB()
            
            self.enabled = True
            logger.info("✅ ExecutionTrackerWrapper initialized - database-only tracking ENABLED")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize database-only tracking: {e}")
            self.enabled = False
            self.db_tracker = None
            self.screenshot_manager = None
            self.report_generator = None
    
    def start_execution(self, suite_id: str, suite_name: str, platform: str = 'Android', test_case_id: Optional[str] = None) -> Optional[str]:
        """
        Start execution tracking in database
        
        Args:
            suite_id: Test suite ID
            suite_name: Test suite name
            platform: Platform (Android/iOS)
            test_case_id: Optional test case ID
            
        Returns:
            execution_id if successful, None otherwise
        """
        if not self.enabled or not self.db_tracker:
            logger.warning("Database tracking not enabled")
            return None
        
        try:
            execution_id = self.db_tracker.start_execution(
                suite_id=suite_id,
                suite_name=suite_name,
                platform=platform,
                test_case_id=test_case_id
            )
            
            logger.info(f"✅ DATABASE-ONLY: Started execution tracking: {execution_id}")
            logger.info(f"   Suite: {suite_name} ({suite_id})")
            logger.info(f"   Platform: {platform}")
            
            return execution_id
            
        except Exception as e:
            logger.error(f"❌ DATABASE-ONLY: Failed to start execution: {e}")
            return None
    
    def capture_screenshot(
        self,
        device_controller,
        execution_id: str,
        step_idx: int,
        action_id: str,
        test_case_id: Optional[str] = None,
        suite_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Capture screenshot and save to database as BLOB
        
        Args:
            device_controller: Device controller instance
            execution_id: Execution ID
            step_idx: Step index
            action_id: Action ID
            test_case_id: Optional test case ID
            suite_id: Optional suite ID
            
        Returns:
            screenshot_filename if successful, None otherwise
        """
        if not self.enabled or not self.screenshot_manager:
            return None
        
        try:
            screenshot_filename = self.screenshot_manager.capture_and_save_screenshot(
                device_controller=device_controller,
                execution_id=execution_id,
                step_idx=step_idx,
                action_id=action_id,
                test_case_id=test_case_id,
                suite_id=suite_id
            )
            
            if screenshot_filename:
                logger.info(f"✅ DATABASE-ONLY: Screenshot saved: {screenshot_filename}")
            else:
                logger.warning(f"⚠️  DATABASE-ONLY: Screenshot capture failed")
            
            return screenshot_filename
            
        except Exception as e:
            logger.error(f"❌ DATABASE-ONLY: Screenshot capture error: {e}")
            return None
    
    def track_step(
        self,
        execution_id: str,
        test_case_id: str,
        step_idx: int,
        action_type: str,
        status: str,
        screenshot_filename: Optional[str] = None,
        action_id: Optional[str] = None,
        suite_id: Optional[str] = None,
        test_idx: Optional[int] = None,
        filename: Optional[str] = None,
        error_message: Optional[str] = None,
        action_params: Optional[Dict[str, Any]] = None
    ) -> Optional[int]:
        """
        Track step execution in database
        
        Args:
            execution_id: Execution ID
            test_case_id: Test case ID
            step_idx: Step index
            action_type: Action type (tap, input, etc.)
            status: Status (passed/failed)
            screenshot_filename: Optional screenshot filename
            action_id: Optional action ID
            suite_id: Optional suite ID
            filename: Optional test case filename
            error_message: Optional error message
            
        Returns:
            step_id if successful, None otherwise
        """
        if not self.enabled or not self.db_tracker:
            return None
        
        try:
            step_id = self.db_tracker.track_step(
                execution_id=execution_id,
                test_case_id=test_case_id,
                step_idx=step_idx,
                action_type=action_type,
                status=status,
                error_message=error_message,
                screenshot_filename=screenshot_filename,
                action_id=action_id,
                action_params=action_params,
                suite_id=suite_id,
                test_idx=test_idx or 0,
                filename=filename
            )
            # After tracking, attach screenshot BLOB to execution_tracking for convenience
            try:
                if screenshot_filename:
                    self.db_tracker.attach_screenshot_to_tracking(
                        execution_id=execution_id,
                        step_idx=step_idx,
                        screenshot_filename=screenshot_filename
                    )
            except Exception as attach_err:
                logger.debug(f"Attach screenshot to tracking skipped: {attach_err}")

            logger.debug(f"✅ DATABASE-ONLY: Step tracked: {step_id} (step {step_idx}, status: {status})")
            
            return step_id
            
        except Exception as e:
            logger.error(f"❌ DATABASE-ONLY: Step tracking error: {e}")
            return None
    
    def complete_execution(self, execution_id: str, status: str = 'completed', error_message: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Complete execution tracking and generate report data
        
        Args:
            execution_id: Execution ID
            status: Final status (completed/failed)
            error_message: Optional error message
            
        Returns:
            report_data dict if successful, None otherwise
        """
        if not self.enabled or not self.db_tracker:
            return None
        
        try:
            report_data = self.db_tracker.complete_execution(
                execution_id=execution_id,
                status=status,
                error_message=error_message
            )
            
            if report_data:
                summary = report_data.get('summary', {})
                logger.info(f"✅ DATABASE-ONLY: Execution completed: {execution_id}")
                logger.info(f"   Status: {status}")
                logger.info(f"   Total test cases: {summary.get('total_tests', 0)}")
                logger.info(f"   Total steps: {summary.get('total_steps', 0)}")
                logger.info(f"   Passed: {summary.get('passed', 0)}")
                logger.info(f"   Failed: {summary.get('failed', 0)}")
            else:
                logger.warning(f"⚠️  DATABASE-ONLY: Execution completion returned no data")
            
            return report_data
            
        except Exception as e:
            logger.error(f"❌ DATABASE-ONLY: Execution completion error: {e}")
            return None
    
    def generate_html_report(self, execution_id: str) -> Optional[str]:
        """
        Generate HTML report from database
        
        Args:
            execution_id: Execution ID
            
        Returns:
            HTML content if successful, None otherwise
        """
        if not self.enabled or not self.report_generator:
            return None
        
        try:
            html_content = self.report_generator.generate_html_from_database(execution_id)
            
            if html_content:
                logger.info(f"✅ DATABASE-ONLY: HTML report generated: {len(html_content)} characters")
            else:
                logger.warning(f"⚠️  DATABASE-ONLY: HTML report generation failed")
            
            return html_content
            
        except Exception as e:
            logger.error(f"❌ DATABASE-ONLY: HTML report generation error: {e}")
            return None
    
    def get_execution_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get execution data from database
        
        Args:
            execution_id: Execution ID
            
        Returns:
            execution_data dict if successful, None otherwise
        """
        if not self.enabled or not self.db_tracker:
            return None
        
        try:
            execution_data = self.db_tracker.get_execution_data(execution_id)
            
            if execution_data:
                logger.debug(f"✅ DATABASE-ONLY: Execution data retrieved: {execution_id}")
            else:
                logger.warning(f"⚠️  DATABASE-ONLY: Execution not found: {execution_id}")
            
            return execution_data
            
        except Exception as e:
            logger.error(f"❌ DATABASE-ONLY: Get execution data error: {e}")
            return None
