import cv2
import numpy as np
import pytesseract
import logging
import os
import re
import sys
from difflib import SequenceMatcher
from typing import List, Tuple, Optional, Dict, Any
import time
from concurrent.futures import ThreadPoolExecutor
import threading

# Set up logging
logger = logging.getLogger(__name__)

class OptimizedTextDetector:
    """
    Optimized text detector with improved Tesseract configuration,
    enhanced preprocessing, and performance optimizations
    """
    
    def __init__(self, cache_size: int = 100):
        """Initialize the optimized text detector"""
        self.logger = logging.getLogger("OptimizedTextDetector")
        
        # Cache for OCR results to avoid reprocessing same images
        self._cache = {}
        self._cache_size = cache_size
        self._cache_lock = threading.Lock()
        
        # Optimized Tesseract configurations for different scenarios
        self.configs = {
            'fast': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
            'accurate': '--oem 1 --psm 3',
            'sparse': '--oem 3 --psm 11',
            'single_word': '--oem 3 --psm 8',
            'single_char': '--oem 3 --psm 10',
            'digits_only': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',
            'alpha_only': '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
        }
        
        # Default configuration
        self.default_config = 'fast'
        
        # Preprocessing parameters
        self.preprocessing_params = {
            'gaussian_blur_kernel': (3, 3),
            'morph_kernel_size': (3, 3),
            'dilation_iterations': 1,
            'erosion_iterations': 1
        }
    
    def find_text(self, image_path: str, text_to_find: str, 
                  similarity_threshold: float = 0.7,
                  config_type: str = None,
                  use_cache: bool = True,
                  preprocessing_level: str = 'medium') -> List[Tuple[int, int, int, int]]:
        """
        Find text in an image with optimized processing
        
        Args:
            image_path: Path to the image file
            text_to_find: Text to search for in the image
            similarity_threshold: Minimum similarity score (0.0-1.0)
            config_type: Tesseract configuration type ('fast', 'accurate', 'sparse', etc.)
            use_cache: Whether to use cached results
            preprocessing_level: Level of preprocessing ('light', 'medium', 'heavy')
            
        Returns:
            List of bounding boxes (x1, y1, x2, y2) for matching text regions
        """
        if not os.path.exists(image_path):
            self.logger.error(f"Image file not found: {image_path}")
            return []
        
        # Generate cache key
        cache_key = self._generate_cache_key(image_path, text_to_find, 
                                           similarity_threshold, config_type, 
                                           preprocessing_level)
        
        # Check cache first
        if use_cache and cache_key in self._cache:
            self.logger.debug(f"Using cached result for {image_path}")
            return self._cache[cache_key]
        
        try:
            start_time = time.time()
            
            # Read and validate image
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"Failed to read image: {image_path}")
                return []
            
            # Auto-detect best configuration if not specified
            if config_type is None:
                config_type = self._auto_detect_config(text_to_find)
            
            # Apply optimized preprocessing
            processed_image = self._preprocess_image(image, preprocessing_level)
            
            # Perform OCR with optimized configuration
            config = self.configs.get(config_type, self.configs[self.default_config])
            data = pytesseract.image_to_data(processed_image, config=config, 
                                           output_type=pytesseract.Output.DICT)
            
            # Find matches with improved algorithm
            matches = self._find_matches_optimized(data, text_to_find, similarity_threshold)
            
            # Cache the result
            if use_cache:
                self._update_cache(cache_key, matches)
            
            processing_time = time.time() - start_time
            self.logger.info(f"Text detection completed in {processing_time:.3f}s, found {len(matches)} matches")
            
            return matches
            
        except Exception as e:
            self.logger.error(f"Error finding text: {e}")
            return []
    
    def _preprocess_image(self, image: np.ndarray, level: str = 'medium') -> np.ndarray:
        """
        Apply optimized preprocessing based on the specified level
        
        Args:
            image: Input image
            level: Preprocessing level ('light', 'medium', 'heavy')
            
        Returns:
            Preprocessed image
        """
        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        if level == 'light':
            # Basic preprocessing
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            return binary
            
        elif level == 'medium':
            # Enhanced preprocessing
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, self.preprocessing_params['gaussian_blur_kernel'], 0)
            
            # Apply adaptive threshold for better text extraction
            binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)
            
            # Apply morphological operations to clean up the image
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, 
                                             self.preprocessing_params['morph_kernel_size'])
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return binary
            
        elif level == 'heavy':
            # Aggressive preprocessing for difficult images
            # Apply bilateral filter to reduce noise while preserving edges
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(filtered)
            
            # Apply adaptive threshold
            binary = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)
            
            # Apply morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            binary = cv2.dilate(binary, kernel, iterations=self.preprocessing_params['dilation_iterations'])
            binary = cv2.erode(binary, kernel, iterations=self.preprocessing_params['erosion_iterations'])
            
            return binary
        
        else:
            # Default to medium level
            return self._preprocess_image(image, 'medium')
    
    def _auto_detect_config(self, text_to_find: str) -> str:
        """
        Auto-detect the best Tesseract configuration based on text characteristics
        
        Args:
            text_to_find: Text to search for
            
        Returns:
            Best configuration type
        """
        text_lower = text_to_find.lower().strip()
        
        # Check if text contains only digits
        if text_lower.isdigit():
            return 'digits_only'
        
        # Check if text contains only alphabetic characters
        if text_lower.isalpha():
            return 'alpha_only'
        
        # Check if it's a single word
        if ' ' not in text_lower and len(text_lower) <= 15:
            return 'single_word'
        
        # Check if it's a single character
        if len(text_lower) == 1:
            return 'single_char'
        
        # For short text, use sparse configuration
        if len(text_lower.split()) <= 3:
            return 'sparse'
        
        # Default to fast configuration for longer text
        return 'fast'
    
    def _find_matches_optimized(self, data: Dict[str, Any], text_to_find: str, 
                               similarity_threshold: float) -> List[Tuple[int, int, int, int]]:
        """
        Optimized text matching algorithm with improved performance
        
        Args:
            data: OCR data dictionary
            text_to_find: Text to search for
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of bounding boxes for matching regions
        """
        matches = []
        text_to_find_lower = text_to_find.lower().strip()
        n_boxes = len(data['text'])
        
        # Filter out low-confidence detections early
        valid_indices = [i for i in range(n_boxes) 
                        if int(data['conf'][i]) > 30 and data['text'][i].strip()]
        
        # Direct matches first (fastest)
        for i in valid_indices:
            detected_text = data['text'][i].strip().lower()
            similarity = self._calculate_similarity_fast(detected_text, text_to_find_lower)
            
            if similarity >= similarity_threshold:
                self.logger.info(f"Found direct match: '{data['text'][i]}' (similarity: {similarity:.2f})")
                x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                matches.append((x, y, x + w, y + h))
        
        # If no direct matches, try partial matches
        if not matches:
            matches = self._find_partial_matches_optimized(data, text_to_find_lower, 
                                                         similarity_threshold, valid_indices)
        
        # Remove duplicate matches
        matches = self._remove_duplicate_matches(matches)
        
        return matches
    
    def _calculate_similarity_fast(self, text1: str, text2: str) -> float:
        """
        Fast similarity calculation with early termination
        
        Args:
            text1: First string
            text2: Second string
            
        Returns:
            Similarity score between 0.0 and 1.0
        """
        # Quick exact match check
        if text1 == text2:
            return 1.0
        
        # Quick length difference check
        len_diff = abs(len(text1) - len(text2))
        max_len = max(len(text1), len(text2))
        if max_len > 0 and len_diff / max_len > 0.5:
            return 0.0
        
        # Use SequenceMatcher for fuzzy matching
        return SequenceMatcher(None, text1, text2).ratio()
    
    def _find_partial_matches_optimized(self, data: Dict[str, Any], text_to_find: str, 
                                       similarity_threshold: float, 
                                       valid_indices: List[int]) -> List[Tuple[int, int, int, int]]:
        """
        Optimized partial matching with reduced search space
        
        Args:
            data: OCR data dictionary
            text_to_find: Text to search for
            similarity_threshold: Minimum similarity score
            valid_indices: Pre-filtered valid text indices
            
        Returns:
            List of bounding boxes for matching regions
        """
        matches = []
        max_words = min(5, len(text_to_find.split()) + 2)  # Adaptive window size
        
        for i, idx in enumerate(valid_indices):
            if i >= len(valid_indices) - 1:
                break
                
            combined_text = data['text'][idx].lower()
            combined_boxes = [(idx, data['left'][idx], data['top'][idx],
                              data['width'][idx], data['height'][idx])]
            
            # Look for consecutive words within reasonable distance
            for j in range(i + 1, min(i + max_words, len(valid_indices))):
                next_idx = valid_indices[j]
                
                # Check if words are reasonably close (same line or nearby)
                y_diff = abs(data['top'][idx] - data['top'][next_idx])
                if y_diff > data['height'][idx] * 1.5:  # Skip if too far vertically
                    continue
                
                combined_text += " " + data['text'][next_idx].lower()
                combined_boxes.append((next_idx, data['left'][next_idx], data['top'][next_idx],
                                      data['width'][next_idx], data['height'][next_idx]))
                
                similarity = self._calculate_similarity_fast(combined_text, text_to_find)
                if similarity >= similarity_threshold:
                    self.logger.info(f"Found partial match: '{combined_text}' (similarity: {similarity:.2f})")
                    
                    # Calculate encompassing bounding box
                    min_x = min(box[1] for box in combined_boxes)
                    min_y = min(box[2] for box in combined_boxes)
                    max_x = max(box[1] + box[3] for box in combined_boxes)
                    max_y = max(box[2] + box[4] for box in combined_boxes)
                    
                    matches.append((min_x, min_y, max_x, max_y))
                    break
        
        return matches
    
    def _remove_duplicate_matches(self, matches: List[Tuple[int, int, int, int]], 
                                 overlap_threshold: float = 0.5) -> List[Tuple[int, int, int, int]]:
        """
        Remove duplicate or heavily overlapping matches
        
        Args:
            matches: List of bounding boxes
            overlap_threshold: Minimum overlap ratio to consider duplicates
            
        Returns:
            Filtered list of matches
        """
        if len(matches) <= 1:
            return matches
        
        filtered_matches = []
        
        for match in matches:
            is_duplicate = False
            x1, y1, x2, y2 = match
            area1 = (x2 - x1) * (y2 - y1)
            
            for existing in filtered_matches:
                ex1, ey1, ex2, ey2 = existing
                area2 = (ex2 - ex1) * (ey2 - ey1)
                
                # Calculate intersection
                ix1, iy1 = max(x1, ex1), max(y1, ey1)
                ix2, iy2 = min(x2, ex2), min(y2, ey2)
                
                if ix1 < ix2 and iy1 < iy2:
                    intersection = (ix2 - ix1) * (iy2 - iy1)
                    union = area1 + area2 - intersection
                    overlap = intersection / union if union > 0 else 0
                    
                    if overlap > overlap_threshold:
                        is_duplicate = True
                        break
            
            if not is_duplicate:
                filtered_matches.append(match)
        
        return filtered_matches
    
    def _generate_cache_key(self, image_path: str, text_to_find: str, 
                           similarity_threshold: float, config_type: str, 
                           preprocessing_level: str) -> str:
        """
        Generate a cache key for the given parameters
        
        Args:
            image_path: Path to image
            text_to_find: Text to search for
            similarity_threshold: Similarity threshold
            config_type: Tesseract configuration type
            preprocessing_level: Preprocessing level
            
        Returns:
            Cache key string
        """
        # Include file modification time to invalidate cache if image changes
        try:
            mtime = os.path.getmtime(image_path)
        except OSError:
            mtime = 0
        
        return f"{image_path}:{text_to_find}:{similarity_threshold}:{config_type}:{preprocessing_level}:{mtime}"
    
    def _update_cache(self, key: str, value: List[Tuple[int, int, int, int]]) -> None:
        """
        Update cache with new result, maintaining size limit
        
        Args:
            key: Cache key
            value: Result to cache
        """
        with self._cache_lock:
            # Remove oldest entries if cache is full
            if len(self._cache) >= self._cache_size:
                # Remove 20% of oldest entries
                keys_to_remove = list(self._cache.keys())[:self._cache_size // 5]
                for k in keys_to_remove:
                    del self._cache[k]
            
            self._cache[key] = value
    
    def clear_cache(self) -> None:
        """
        Clear the OCR results cache
        """
        with self._cache_lock:
            self._cache.clear()
            self.logger.info("OCR cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics
        
        Returns:
            Dictionary with cache statistics
        """
        with self._cache_lock:
            return {
                'size': len(self._cache),
                'max_size': self._cache_size
            }
    
    def batch_find_text(self, image_paths: List[str], texts_to_find: List[str], 
                       similarity_threshold: float = 0.7,
                       max_workers: int = 4) -> Dict[str, List[Tuple[int, int, int, int]]]:
        """
        Process multiple images in parallel for better performance
        
        Args:
            image_paths: List of image file paths
            texts_to_find: List of texts to search for (one per image)
            similarity_threshold: Minimum similarity score
            max_workers: Maximum number of worker threads
            
        Returns:
            Dictionary mapping image paths to their results
        """
        if len(image_paths) != len(texts_to_find):
            raise ValueError("Number of images and texts must match")
        
        results = {}
        
        def process_image(args):
            image_path, text_to_find = args
            return image_path, self.find_text(image_path, text_to_find, similarity_threshold)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_image, (img, txt)) 
                      for img, txt in zip(image_paths, texts_to_find)]
            
            for future in futures:
                try:
                    image_path, matches = future.result()
                    results[image_path] = matches
                except Exception as e:
                    self.logger.error(f"Error processing image in batch: {e}")
        
        return results


# Backward compatibility function
def detect_text_with_tesseract_optimized(image_path: str, text_to_find: str, 
                                        output_dir: Optional[str] = None, 
                                        similarity_threshold: float = 0.6) -> Optional[Tuple[int, int, Dict]]:
    """
    Optimized version of the original detect_text_with_tesseract function
    
    Args:
        image_path: Path to the screenshot image
        text_to_find: Text to search for in the image
        output_dir: Directory to save debug images (not used in optimized version)
        similarity_threshold: Minimum similarity score (0.0-1.0)
        
    Returns:
        Tuple of (center_x, center_y, match_info) or None if not found
    """
    detector = OptimizedTextDetector()
    matches = detector.find_text(image_path, text_to_find, similarity_threshold)
    
    if matches:
        # Return the first match in the original format
        x1, y1, x2, y2 = matches[0]
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        match_info = {
            'text': text_to_find,
            'coordinates': {
                'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                'center_x': center_x, 'center_y': center_y,
                'width': x2 - x1, 'height': y2 - y1
            },
            'confidence': 0.9  # Placeholder confidence
        }
        
        return center_x, center_y, match_info
    
    return None