"""
Database-First Report Generation Module

This module generates HTML/PDF reports directly from database records
without any file system dependencies (no data.json files).

DATABASE-FIRST ARCHITECTURE
"""

import sqlite3
import logging
import base64
from datetime import datetime
from utils.database import get_db_path, get_execution_tracking_by_id, get_screenshots_by_execution_id, get_execution_summary

logger = logging.getLogger(__name__)

def transform_db_records_to_report_data(execution_id):
    """
    Transform database records into report template format
    
    Args:
        execution_id (str): Unique execution identifier
        
    Returns:
        dict: Report data structure compatible with existing templates
    """
    try:
        logger.info(f"=" * 80)
        logger.info(f"TRANSFORMING DATABASE RECORDS TO REPORT DATA")
        logger.info(f"Execution ID: {execution_id}")
        logger.info(f"=" * 80)
        
        # Get execution tracking records
        tracking_records = get_execution_tracking_by_id(execution_id)
        if not tracking_records:
            logger.error(f"No execution tracking records found for {execution_id}")
            return None
        
        # Get screenshots
        screenshot_records = get_screenshots_by_execution_id(execution_id)
        
        # Get execution summary
        summary = get_execution_summary(execution_id)
        
        # Create screenshot lookup by action_id
        screenshot_lookup = {}
        for screenshot in screenshot_records:
            action_id = screenshot.get('action_id')
            if action_id:
                # Convert BLOB to base64 for HTML embedding
                if screenshot.get('screenshot_blob'):
                    screenshot_base64 = base64.b64encode(screenshot['screenshot_blob']).decode('utf-8')
                    screenshot['screenshot_data_url'] = f"data:{screenshot.get('screenshot_mime', 'image/png')};base64,{screenshot_base64}"
                screenshot_lookup[action_id] = screenshot
        
        # Group tracking records by test case
        test_cases = {}
        for record in tracking_records:
            test_idx = record.get('test_idx', 0)
            if test_idx not in test_cases:
                test_cases[test_idx] = {
                    'test_idx': test_idx,
                    'test_case_id': record.get('test_case_id'),
                    'filename': record.get('filename'),
                    'steps': [],
                    'status': 'passed',
                    'start_time': record.get('start_time'),
                    'end_time': record.get('end_time')
                }
            
            # Add step
            step = {
                'step_idx': record.get('step_idx'),
                'action_type': record.get('action_type'),
                'action_params': record.get('action_params'),
                'action_id': record.get('action_id'),
                'status': record.get('status'),
                'retry_count': record.get('retry_count', 0),
                'max_retries': record.get('max_retries', 3),
                'last_error': record.get('last_error'),
                'start_time': record.get('start_time'),
                'end_time': record.get('end_time'),
                'execution_result': record.get('execution_result')
            }
            
            # Add screenshot if available
            action_id = record.get('action_id')
            if action_id and action_id in screenshot_lookup:
                step['screenshot'] = screenshot_lookup[action_id]
            
            test_cases[test_idx]['steps'].append(step)
            
            # Update test case status
            if record.get('status') == 'failed':
                test_cases[test_idx]['status'] = 'failed'
        
        # Convert to list
        test_cases_list = list(test_cases.values())
        
        # Build report data structure
        report_data = {
            'execution_id': execution_id,
            'suite_id': summary.get('suite_id', ''),
            'execution_start_time': summary.get('execution_start_time'),
            'execution_end_time': summary.get('execution_end_time'),
            'total_steps': summary.get('total_steps', 0),
            'passed_steps': summary.get('passed_steps', 0),
            'failed_steps': summary.get('failed_steps', 0),
            'overall_status': summary.get('overall_status', 'unknown'),
            'test_cases': test_cases_list,
            'total_test_cases': len(test_cases_list),
            'platform': 'ios'
        }
        
        logger.info(f"✅ Transformed {len(tracking_records)} tracking records into report data")
        logger.info(f"   Test cases: {len(test_cases_list)}")
        logger.info(f"   Screenshots: {len(screenshot_records)}")
        logger.info(f"=" * 80)
        
        return report_data
        
    except Exception as e:
        logger.error(f"Error transforming database records: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_html_report_from_database(execution_id, html_report_path):
    """
    Generate HTML report directly from database (DATABASE-FIRST)
    
    Args:
        execution_id (str): Unique execution identifier
        html_report_path (str): Path to save HTML report
        
    Returns:
        bool: Success status
    """
    try:
        logger.info(f"=" * 80)
        logger.info(f"GENERATING HTML REPORT FROM DATABASE")
        logger.info(f"Execution ID: {execution_id}")
        logger.info(f"Output path: {html_report_path}")
        logger.info(f"=" * 80)
        
        # Transform database records to report data
        report_data = transform_db_records_to_report_data(execution_id)
        
        if not report_data:
            logger.error("Failed to transform database records to report data")
            return False
        
        # Import the existing report generator
        from utils.reportGenerator import generate_html_report
        
        # Generate HTML report using existing template
        # The existing function expects test_suite_data dict
        success = generate_html_report(report_data, html_report_path)
        
        if success:
            logger.info(f"✅ Successfully generated HTML report from database")
            logger.info(f"   Report path: {html_report_path}")
        else:
            logger.error(f"❌ Failed to generate HTML report")
        
        logger.info(f"=" * 80)
        return success
        
    except Exception as e:
        logger.error(f"Error generating HTML report from database: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_report_data_to_execution_reports(execution_id, suite_id, test_case_id, platform, status, start_time, end_time, duration, report_data_dict):
    """
    Save report data to execution_reports table (DATABASE-FIRST)
    
    Args:
        execution_id (str): Unique execution identifier
        suite_id (str): Test suite ID (None for individual test cases)
        test_case_id (str): Test case ID (None for test suites)
        platform (str): Platform (android/ios)
        status (str): Execution status (passed/failed)
        start_time (str): Start timestamp
        end_time (str): End timestamp
        duration (int): Duration in seconds
        report_data_dict (dict): Complete report data as dictionary
        
    Returns:
        bool: Success status
    """
    try:
        import json
        
        logger.info(f"=" * 80)
        logger.info(f"SAVING REPORT DATA TO execution_reports TABLE")
        logger.info(f"Execution ID: {execution_id}")
        logger.info(f"Suite ID: {suite_id}")
        logger.info(f"Test Case ID: {test_case_id}")
        logger.info(f"=" * 80)
        
        # Convert report data to JSON
        report_data_json = json.dumps(report_data_dict, default=str)
        
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        
        # Generate report_id
        report_id = f"{execution_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Insert or replace
        cursor.execute('''
            INSERT OR REPLACE INTO execution_reports
            (report_id, test_execution_id, suite_id, test_case_id, platform, status,
             start_time, end_time, duration, report_data, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            report_id, execution_id, suite_id, test_case_id, platform, status,
            start_time, end_time, duration, report_data_json, datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Saved report data to execution_reports table")
        logger.info(f"   Report ID: {report_id}")
        logger.info(f"   Data size: {len(report_data_json)} bytes")
        logger.info(f"=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"Error saving report data to execution_reports: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

