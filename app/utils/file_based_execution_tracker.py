#!/usr/bin/env python3
"""
File-Based Execution Tracker
============================

A simple, reliable execution tracking system that stores all data directly in JSON files
within execution report folders, eliminating database dependencies and complexity.

Key Features:
- Direct storage in data.json files
- No database dependencies
- Proper test case identification from source files
- Simple Import Execution process
- Reliable filename and test case name handling
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class FileBasedExecutionTracker:
    def __init__(self, reports_dir=None):
        """Initialize the file-based execution tracker"""
        self.reports_dir = reports_dir or self._get_reports_directory()
        self.current_execution_dir = None
        self.current_data_file = None
        self.execution_data = {
            'execution_id': None,
            'suite_id': None,
            'suite_name': None,
            'start_time': None,
            'end_time': None,
            'status': 'running',
            'test_cases': [],
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'skipped': 0
            }
        }
        
    def _get_reports_directory(self):
        """Get the reports directory from config"""
        try:
            from config import get_configured_directory
            return get_configured_directory('REPORTS', 'reports_ios')
        except Exception:
            return str(Path.home() / 'MobileAutomationWorkspace' / 'ios' / 'reports')
    
    def start_execution(self, suite_id=None, suite_name=None, execution_id=None):
        """Start a new execution session with coordination"""
        # Import execution state manager
        try:
            from utils.execution_state_manager import get_execution_state_manager
            state_manager = get_execution_state_manager()

            # Check if there's already a running execution
            current_execution = state_manager.get_current_execution()
            if current_execution and not execution_id:
                # Reuse existing execution
                existing_execution_id = current_execution['execution_id']
                self.current_execution_dir = os.path.join(self.reports_dir, existing_execution_id)

                # Load existing data if available
                existing_data_file = os.path.join(self.current_execution_dir, 'data.json')
                if os.path.exists(existing_data_file):
                    try:
                        with open(existing_data_file, 'r', encoding='utf-8') as f:
                            self.execution_data = json.load(f)
                        self.current_data_file = existing_data_file
                        logger.info(f"Reusing existing execution: {existing_execution_id}")
                        return existing_execution_id
                    except Exception as e:
                        logger.warning(f"Could not load existing execution data: {e}")

            # Start new coordinated execution
            execution_info = state_manager.start_execution(
                execution_type="test_suite" if suite_id != "individual" else "individual_test",
                suite_id=suite_id,
                force_new=execution_id is not None
            )
            execution_id = execution_info['execution_id']

        except Exception as e:
            logger.warning(f"Could not coordinate with execution state manager: {e}")
            # Fallback to original behavior
            if not execution_id:
                execution_id = f"exec_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Create execution directory using coordinated execution ID
        if not hasattr(self, 'current_execution_dir') or not self.current_execution_dir:
            if execution_id.startswith('testsuite_execution_'):
                dir_name = execution_id
            else:
                dir_name = f"testsuite_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            self.current_execution_dir = os.path.join(self.reports_dir, dir_name)
            os.makedirs(self.current_execution_dir, exist_ok=True)
        
        # Initialize execution data
        self.execution_data = {
            'execution_id': execution_id,
            'id': suite_id,  # For compatibility with existing import functionality
            'suite_id': suite_id,  # ENHANCEMENT: Explicit suite_id for test suite identification
            'suite_name': suite_name or 'Individual Test Execution',
            'name': suite_name or 'Individual Test Execution',  # For compatibility
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'status': 'running',
            'testCases': [],  # Changed from 'test_cases' to 'testCases' for consistency
            'test_cases': [],  # Keep both for backward compatibility
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'skipped': 0
            }
        }
        
        # Create data.json file
        self.current_data_file = os.path.join(self.current_execution_dir, 'data.json')
        self._save_data()
        
        logger.info(f"Started execution tracking: {execution_id} in {self.current_execution_dir}")
        return execution_id
    
    def add_test_case(self, test_case_filename, test_case_data=None):
        """Add a test case to the current execution"""
        if not self.current_execution_dir:
            # Auto-start execution if not already started
            self.start_execution()
        
        # Load test case data if not provided
        if not test_case_data:
            test_case_data = self._load_test_case_data(test_case_filename)
        
        # Extract test case information
        test_case_info = {
            'test_case_id': test_case_data.get('test_case_id', f"tc_{len(self.execution_data['test_cases']) + 1}"),
            'filename': test_case_filename,
            'name': test_case_data.get('name', test_case_filename.replace('.json', '')),
            'description': test_case_data.get('description', ''),
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'status': 'running',
            'steps': [],
            'total_steps': len(test_case_data.get('actions', [])),
            'passed_steps': 0,
            'failed_steps': 0,
            'error_message': None
        }
        
        self.execution_data['test_cases'].append(test_case_info)
        self.execution_data['summary']['total_tests'] += 1
        self._save_data()
        
        logger.info(f"Added test case: {test_case_filename} ({test_case_info['name']})")
        return len(self.execution_data['test_cases']) - 1  # Return test case index
    
    def track_step(self, test_case_index, step_index, action_type, status='running', error_message=None, action_data=None):
        """Track a test step execution"""
        if not self.current_execution_dir or test_case_index >= len(self.execution_data['test_cases']):
            logger.warning(f"Invalid test case index: {test_case_index}")
            return
        
        test_case = self.execution_data['test_cases'][test_case_index]
        
        # Find or create step entry
        step_entry = None
        for step in test_case['steps']:
            if step['step_index'] == step_index:
                step_entry = step
                break
        
        if not step_entry:
            step_entry = {
                'step_index': step_index,
                'action_type': action_type,
                'start_time': datetime.now().isoformat(),
                'end_time': None,
                'status': 'running',
                'error_message': None,
                'action_data': action_data or {}
            }
            test_case['steps'].append(step_entry)
        
        # Update step status
        step_entry['status'] = status
        step_entry['end_time'] = datetime.now().isoformat()
        if error_message:
            step_entry['error_message'] = error_message
        
        # Update test case step counters
        if status == 'passed':
            test_case['passed_steps'] += 1
        elif status == 'failed':
            test_case['failed_steps'] += 1
            test_case['error_message'] = error_message
        
        self._save_data()
        logger.info(f"Tracked step {step_index} for test case {test_case_index}: {action_type} -> {status}")
    
    def complete_test_case(self, test_case_index, status='passed', error_message=None):
        """Complete a test case execution"""
        if not self.current_execution_dir or test_case_index >= len(self.execution_data['test_cases']):
            logger.warning(f"Invalid test case index: {test_case_index}")
            return
        
        test_case = self.execution_data['test_cases'][test_case_index]
        test_case['end_time'] = datetime.now().isoformat()
        test_case['status'] = status
        if error_message:
            test_case['error_message'] = error_message
        
        # Update summary
        if status == 'passed':
            self.execution_data['summary']['passed'] += 1
        elif status == 'failed':
            self.execution_data['summary']['failed'] += 1
        else:
            self.execution_data['summary']['skipped'] += 1
        
        self._save_data()
        logger.info(f"Completed test case {test_case_index}: {test_case['name']} -> {status}")
    
    def complete_execution(self, status='completed'):
        """Complete the execution session with coordination"""
        if not self.current_execution_dir:
            return

        self.execution_data['end_time'] = datetime.now().isoformat()
        self.execution_data['status'] = status
        self._save_data()

        logger.info(f"Completed execution: {self.execution_data['execution_id']} -> {status}")

        # Coordinate with execution state manager
        try:
            from utils.execution_state_manager import get_execution_state_manager
            state_manager = get_execution_state_manager()
            state_manager.complete_execution(self.execution_data['execution_id'], status)
        except Exception as e:
            logger.warning(f"Could not coordinate execution completion: {e}")

        # Store the completed execution directory for reference
        completed_execution_dir = self.current_execution_dir

        # Reset for next execution
        self.current_execution_dir = None
        self.current_data_file = None

        return completed_execution_dir
    
    def _load_test_case_data(self, filename):
        """Load test case data from file"""
        try:
            from config import get_configured_directory
            test_cases_dir = get_configured_directory('TEST_CASES', 'test_cases')
            file_path = os.path.join(test_cases_dir, filename)
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"Test case file not found: {file_path}")
                return {'name': filename.replace('.json', ''), 'actions': []}
        except Exception as e:
            logger.error(f"Error loading test case {filename}: {e}")
            return {'name': filename.replace('.json', ''), 'actions': []}
    
    def _save_data(self):
        """Save execution data to JSON file"""
        if not self.current_data_file:
            return
        
        try:
            with open(self.current_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.execution_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving execution data: {e}")
    
    def get_current_execution_data(self):
        """Get current execution data"""
        return self.execution_data.copy()
    
    def get_execution_directory(self):
        """Get current execution directory"""
        return self.current_execution_dir

# Global instance
execution_tracker = FileBasedExecutionTracker()

def start_execution_tracking(suite_id=None, suite_name=None, execution_id=None):
    """Start execution tracking"""
    return execution_tracker.start_execution(suite_id, suite_name, execution_id)

def add_test_case_to_tracking(filename, test_case_data=None):
    """Add test case to tracking"""
    return execution_tracker.add_test_case(filename, test_case_data)

def track_test_step(test_case_index, step_index, action_type, status='running', error_message=None, action_data=None):
    """Track a test step"""
    execution_tracker.track_step(test_case_index, step_index, action_type, status, error_message, action_data)

def complete_test_case_tracking(test_case_index, status='passed', error_message=None):
    """Complete test case tracking"""
    execution_tracker.complete_test_case(test_case_index, status, error_message)

def complete_execution_tracking(status='completed'):
    """Complete execution tracking"""
    return execution_tracker.complete_execution(status)

def get_current_execution_data():
    """Get current execution data"""
    return execution_tracker.get_current_execution_data()

def get_execution_directory():
    """Get current execution directory"""
    return execution_tracker.get_execution_directory()
