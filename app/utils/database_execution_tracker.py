"""Database-only execution tracker for the primary automation app.

This implementation mirrors the Android database-first tracker but targets the
iOS/consolidated application stack. It persists every execution artifact in the
central SQLite database so no filesystem-based reporting paths are required.
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class DatabaseExecutionTracker:
    """
    Pure database-based execution tracker.
    NO filesystem operations for execution data.
    """
    
    def __init__(self, db_path: str = None):
        """Initialize tracker with database connection"""
        if db_path is None:
            try:
                from .database import get_db_path
                db_path = get_db_path()
            except Exception:
                # Fallback to hardcoded path if helper import fails
                import os
                db_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                    'db-data',
                    'ios.db',
                )
        self.db_path = db_path
        logger.info(f"DatabaseExecutionTracker initialized with DB: {self.db_path}")
    
    def start_execution(self, suite_id: str, suite_name: str = None,
                       execution_id: str = None, platform: str = 'iOS',
                       test_case_id: str = None) -> str:
        """
        Start new execution tracking in database only.
        
        Args:
            suite_id: Test suite identifier
            suite_name: Human-readable suite name
            execution_id: Optional custom execution ID
            platform: Platform (iOS/Android)
            test_case_id: Optional test case ID
            
        Returns:
            execution_id: Unique execution identifier
        """
        try:
            # Generate execution ID if not provided (use testsuite_execution_YYYYMMDD_HHMMSS for consistency with UI)
            if not execution_id:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                execution_id = f"testsuite_execution_{timestamp}"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check execution_reports schema and insert a running row using available columns
            cursor.execute("PRAGMA table_info(execution_reports)")
            columns = [col[1] for col in cursor.fetchall()]

            now_iso = datetime.now().isoformat()

            if 'test_execution_id' in columns:
                # Prefer rich schema with explicit execution identifier
                report_id = f"{execution_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}" if 'report_id' in columns else None
                if 'report_id' in columns and 'platform' in columns and 'start_time' in columns:
                    cursor.execute('''
                        INSERT INTO execution_reports
                        (report_id, test_execution_id, suite_id, test_case_id, platform, status, start_time, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        report_id,
                        execution_id,
                        suite_id,
                        test_case_id,
                        platform,
                        'running',
                        now_iso,
                        now_iso
                    ))
                else:
                    # Minimal row with required identifiers
                    cursor.execute('''
                        INSERT INTO execution_reports
                        (test_execution_id, suite_id, status, created_at)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        execution_id,
                        suite_id,
                        'running',
                        now_iso
                    ))
            elif 'execution_id' in columns:
                # Consolidated schema that uses execution_id and data_json BLOB
                # Use upsert-like semantics to avoid duplicates
                try:
                    cursor.execute(
                        """
                        INSERT INTO execution_reports (execution_id, suite_id, data_json, status, created_at, updated_at)
                        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        ON CONFLICT(execution_id) DO UPDATE SET
                            suite_id = excluded.suite_id,
                            status = COALESCE(execution_reports.status, excluded.status),
                            updated_at = CURRENT_TIMESTAMP
                        """,
                        (execution_id, suite_id, json.dumps({'execution_id': execution_id}).encode('utf-8'), 'running')
                    )
                except Exception:
                    # Fallback insert without ON CONFLICT for older SQLite versions
                    cursor.execute(
                        "INSERT OR REPLACE INTO execution_reports (execution_id, suite_id, data_json, status, created_at, updated_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)",
                        (execution_id, suite_id, json.dumps({'execution_id': execution_id}).encode('utf-8'), 'running')
                    )
            else:
                logger.warning("execution_reports table exists but without expected identifier columns; skipping start row insert")
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Started execution tracking: {execution_id}")
            return execution_id
            
        except Exception as e:
            logger.error(f"Error starting execution: {e}")
            raise
    
    def track_step(self, execution_id: str, test_case_id: str, step_idx: int,
                   action_type: str, status: str, error_message: str = None,
                   screenshot_filename: str = None, action_id: str = None,
                   action_params: Dict = None, suite_id: str = None,
                   test_idx: int = 0, filename: str = None, retry_count: int = 0,
                   max_retries: int = 0) -> int:
        """
        Track individual test step execution.
        
        Args:
            execution_id: Execution identifier
            test_case_id: Test case identifier
            step_idx: Step index (0-based)
            action_type: Action type (click, input, etc.)
            status: Step status (passed/failed/skipped)
            error_message: Error details if failed
            screenshot_filename: Screenshot identifier
            action_id: Action identifier
            action_params: Action parameters
            suite_id: Suite identifier
            filename: Test case filename
            retry_count: Current retry count
            max_retries: Maximum retries allowed
            
        Returns:
            step_id: Database record ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Ensure screenshot filename/name columns exist
            cursor.execute("PRAGMA table_info(execution_tracking)")
            columns = [col[1] for col in cursor.fetchall()]
            if 'screenshot_filename' not in columns:
                try:
                    cursor.execute('ALTER TABLE execution_tracking ADD COLUMN screenshot_filename TEXT')
                    columns.append('screenshot_filename')
                except Exception:
                    pass
            if 'screenshot_name' not in columns:
                try:
                    cursor.execute('ALTER TABLE execution_tracking ADD COLUMN screenshot_name TEXT')
                    columns.append('screenshot_name')
                except Exception:
                    pass
            
            # Prepare common values
            now = datetime.now().isoformat()
            params_json = json.dumps(action_params) if action_params else None
            
            # Derive a human-friendly screenshot name based on action_id
            screenshot_name = None
            if action_id:
                screenshot_name = f"{action_id}.png"

            # Insert into execution_tracking table
            if 'screenshot_filename' in columns:
                # New schema with screenshot_filename
                if 'screenshot_name' in columns:
                    cursor.execute('''
                        INSERT INTO execution_tracking
                        (test_execution_id, test_case_id, suite_id, test_idx, step_idx, filename,
                         action_type, action_id, action_params, status, last_error, 
                         start_time, end_time, screenshot_filename, screenshot_name, in_progress,
                         retry_count, max_retries)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        execution_id,
                        test_case_id,
                        suite_id,
                        int(test_idx or 0),
                        step_idx,
                        filename,
                        action_type,
                        action_id,
                        params_json,
                        status,
                        error_message,
                        now,
                        now,
                        screenshot_filename,
                        screenshot_name,
                        0,  # Not in progress (completed)
                        retry_count,
                        max_retries
                    ))
                else:
                    cursor.execute('''
                    INSERT INTO execution_tracking
                    (test_execution_id, test_case_id, suite_id, test_idx, step_idx, filename,
                     action_type, action_id, action_params, status, last_error, 
                     start_time, end_time, screenshot_filename, in_progress,
                     retry_count, max_retries)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    execution_id,
                    test_case_id,
                    suite_id,
                    int(test_idx or 0),
                    step_idx,
                    filename,
                    action_type,
                    action_id,
                    params_json,
                    status,
                    error_message,
                    now,
                    now,
                    screenshot_filename,
                    0,  # Not in progress (completed)
                    retry_count,
                    max_retries
                ))
            else:
                # Old schema without screenshot_filename
                cursor.execute('''
                    INSERT INTO execution_tracking
                    (test_execution_id, test_case_id, suite_id, test_idx, step_idx, filename,
                     action_type, action_id, action_params, status, last_error, 
                     start_time, end_time, in_progress, retry_count, max_retries)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    execution_id,
                    test_case_id,
                    suite_id,
                    int(test_idx or 0),
                    step_idx,
                    filename,
                    action_type,
                    action_id,
                    params_json,
                    status,
                    error_message,
                    now,
                    now,
                    0,  # Not in progress (completed)
                    retry_count,
                    max_retries
                ))
            
            step_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            logger.debug(f"✅ Tracked step {step_idx}: {action_type} -> {status}")
            return step_id
            
        except Exception as e:
            logger.error(f"Error tracking step: {e}")
            raise
    
    def complete_execution(self, execution_id: str, status: str = 'completed',
                          error_message: str = None) -> Dict:
        """
        Complete execution and generate report data.
        
        Args:
            execution_id: Execution identifier
            status: Final status (completed/failed/cancelled)
            error_message: Error message if failed
            
        Returns:
            report_data: Complete report data structure (dict)
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get all execution tracking records
            cursor.execute('''
                SELECT * FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY step_idx ASC
            ''', (execution_id,))
            
            steps = [dict(row) for row in cursor.fetchall()]
            
            # Build report_data structure
            report_data = self._build_report_data(execution_id, steps)
            
            # Determine start_time/end_time
            start_time = None
            # Try to read from execution_reports if a start_time column exists
            cursor.execute("PRAGMA table_info(execution_reports)")
            er_columns = [col[1] for col in cursor.fetchall()]
            if 'start_time' in er_columns:
                try:
                    id_col = 'test_execution_id' if 'test_execution_id' in er_columns else ('execution_id' if 'execution_id' in er_columns else None)
                    if id_col:
                        cursor.execute(f'SELECT start_time FROM execution_reports WHERE {id_col} = ?', (execution_id,))
                        row = cursor.fetchone()
                        start_time = row[0] if row else None
                except Exception:
                    start_time = None
            if not start_time:
                # Fallback: earliest start_time from execution_tracking
                cursor.execute('SELECT MIN(start_time) FROM execution_tracking WHERE test_execution_id = ?', (execution_id,))
                row = cursor.fetchone()
                start_time = row[0] if row and row[0] else datetime.now().isoformat()
            
            # Calculate duration
            end_time = datetime.now().isoformat()
            duration = self._calculate_duration(start_time, end_time)
            
            # Save report data to execution_reports table
            cursor.execute("PRAGMA table_info(execution_reports)")
            er_columns = [col[1] for col in cursor.fetchall()]

            # Generate report_id
            report_id = f"{execution_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Determine platform from execution_id or default
            platform = 'iOS' if 'ios' in execution_id.lower() else 'Android'

            # Get suite_id and test_case_id from steps if available
            suite_id = steps[0].get('suite_id', 'unknown') if steps else 'unknown'
            test_case_id = steps[0].get('test_case_id', 'unknown') if steps else 'unknown'

            if 'report_data' in er_columns:
                # New standardized schema
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO execution_reports
                        (report_id, test_execution_id, suite_id, test_case_id, platform, status,
                         start_time, end_time, duration, error_message, report_data, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        report_id,
                        execution_id,
                        suite_id,
                        test_case_id,
                        platform,
                        status,
                        start_time,
                        end_time,
                        duration,
                        error_message,
                        json.dumps(report_data),
                        datetime.now().isoformat()
                    ))
                    logger.info(f"✅ Saved report data to execution_reports (new schema): {report_id}")
                except Exception as e:
                    logger.error(f"Failed to save to execution_reports (new schema): {e}")
            else:
                # Legacy schema fallback
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO execution_reports
                        (execution_id, suite_id, data_json, status, created_at, updated_at)
                        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ''', (
                        execution_id,
                        suite_id,
                        json.dumps(report_data).encode('utf-8'),
                        status
                    ))
                    logger.info(f"✅ Saved report data to execution_reports (legacy schema): {execution_id}")
                except Exception as e:
                    logger.error(f"Failed to save to execution_reports (legacy schema): {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Completed execution: {execution_id} -> {status}")
            return report_data
            
        except Exception as e:
            logger.error(f"Error completing execution: {e}")
            raise

    def attach_screenshot_to_tracking(self, execution_id: str, step_idx: int, screenshot_filename: Optional[str] = None) -> bool:
        """Copy screenshot BLOB from screenshots table into execution_tracking for the given step.

        Args:
            execution_id: Execution identifier
            step_idx: Step index to update
            screenshot_filename: Optional filename to locate screenshot row

        Returns:
            bool: True if an update occurred
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Ensure screenshot columns exist on execution_tracking
            cursor.execute("PRAGMA table_info(execution_tracking)")
            et_columns = [c[1] for c in cursor.fetchall()]
            for name, coltype in (
                ('screenshot_blob', 'BLOB'),
                ('screenshot_thumb_blob', 'BLOB'),
                ('screenshot_mime', "TEXT DEFAULT 'image/png'"),
                ('screenshot_name', 'TEXT'),
            ):
                if name not in et_columns:
                    try:
                        cursor.execute(f"ALTER TABLE execution_tracking ADD COLUMN {name} {coltype}")
                    except Exception:
                        pass

            # Locate screenshot row
            if screenshot_filename:
                cursor.execute(
                    '''SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                         FROM screenshots WHERE filename = ? ORDER BY timestamp DESC LIMIT 1''',
                    (screenshot_filename,)
                )
            else:
                cursor.execute(
                    '''SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                         FROM screenshots WHERE test_execution_id = ? AND step_idx = ?
                      ORDER BY timestamp DESC LIMIT 1''',
                    (execution_id, step_idx)
                )
            row = cursor.fetchone()
            if not row or not row[0]:
                conn.close()
                return False

            blob, thumb, mime = row[0], (row[1] if len(row) > 1 else None), (row[2] if len(row) > 2 else 'image/png')

            # Derive screenshot_name from action_id for this step
            cursor.execute(
                'SELECT action_id FROM execution_tracking WHERE test_execution_id = ? AND step_idx = ? ORDER BY id DESC LIMIT 1',
                (execution_id, step_idx)
            )
            name_row = cursor.fetchone()
            derived_name = None
            if name_row and name_row[0]:
                derived_name = f"{name_row[0]}.png"

            # Update execution_tracking row
            if 'screenshot_name' in et_columns:
                cursor.execute(
                    '''UPDATE execution_tracking
                          SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?, screenshot_name = COALESCE(screenshot_name, ?)
                        WHERE test_execution_id = ? AND step_idx = ?''',
                    (blob, thumb, mime, derived_name, execution_id, step_idx)
                )
            else:
                cursor.execute(
                    '''UPDATE execution_tracking
                          SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                        WHERE test_execution_id = ? AND step_idx = ?''',
                    (blob, thumb, mime, execution_id, step_idx)
                )

            updated = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return updated
        except Exception as e:
            logger.error(f"attach_screenshot_to_tracking error: {e}")
            try:
                conn.close()
            except Exception:
                pass
            return False
    
    def get_execution_data(self, execution_id: str) -> Optional[Dict]:
        """
        Retrieve complete execution data from database.
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            dict: Complete execution data including all steps
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get execution report
            cursor.execute('''
                SELECT * FROM execution_reports
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            report_row = cursor.fetchone()
            if not report_row:
                logger.warning(f"Execution not found: {execution_id}")
                conn.close()
                return None
            
            report_data = dict(report_row)
            
            # Parse report_data JSON if exists
            if report_data.get('report_data'):
                try:
                    report_data['report_data'] = json.loads(report_data['report_data'])
                except:
                    pass
            elif report_data.get('data_json'):
                # Old schema - data_json is BLOB
                try:
                    data_json_bytes = report_data['data_json']
                    if isinstance(data_json_bytes, bytes):
                        report_data['report_data'] = json.loads(data_json_bytes.decode('utf-8'))
                    else:
                        report_data['report_data'] = json.loads(data_json_bytes)
                except:
                    pass
            
            conn.close()
            
            return report_data
            
        except Exception as e:
            logger.error(f"Error getting execution data: {e}")
            return None
    
    def _build_report_data(self, execution_id: str, steps: List[Dict]) -> Dict:
        """Build data.json structure from execution tracking records"""
        # Group steps by test case
        test_cases = {}
        for step in steps:
            test_case_id = step.get('test_case_id', 'unknown')
            if test_case_id not in test_cases:
                test_cases[test_case_id] = {
                    'test_case_id': test_case_id,
                    'name': step.get('filename', test_case_id),
                    'steps': [],
                    'status': 'passed',
                    'passed_steps': 0,
                    'failed_steps': 0,
                    'skipped_steps': 0
                }
            
            # Add step to test case
            # Compute screenshot naming and URL/linking info
            sc_filename = step.get('screenshot_filename')
            sc_name = step.get('screenshot_name') or (f"{step.get('action_id')}.png" if step.get('action_id') else None)
            sc_url = f"/api/executions/{execution_id}/screenshots/{sc_filename}" if sc_filename else None

            test_cases[test_case_id]['steps'].append({
                'step_index': step.get('step_idx'),
                'action_type': step.get('action_type'),
                'action_id': step.get('action_id'),
                'status': step.get('status'),
                'error_message': step.get('last_error'),
                'screenshot_filename': sc_filename,
                'screenshot_name': sc_name,
                'screenshot_url': sc_url,
                'images': ([{
                    'name': sc_name,
                    'filename': sc_filename,
                    'db_url': sc_url,
                }] if sc_filename else []),
                'timestamp': step.get('start_time'),
                'retry_count': step.get('retry_count', 0)
            })
            
            # Update counters
            if step.get('status') == 'passed':
                test_cases[test_case_id]['passed_steps'] += 1
            elif step.get('status') == 'failed':
                test_cases[test_case_id]['failed_steps'] += 1
                test_cases[test_case_id]['status'] = 'failed'
            elif step.get('status') == 'skipped':
                test_cases[test_case_id]['skipped_steps'] += 1
        
        # Build final report structure
        report_data = {
            'execution_id': execution_id,
            'test_execution_id': execution_id,
            'start_time': datetime.now().isoformat(),
            'test_cases': list(test_cases.values()),
            'summary': {
                'total_tests': len(test_cases),
                'passed': sum(1 for tc in test_cases.values() if tc['status'] == 'passed'),
                'failed': sum(1 for tc in test_cases.values() if tc['status'] == 'failed'),
                'total_steps': len(steps),
                'passed_steps': sum(tc['passed_steps'] for tc in test_cases.values()),
                'failed_steps': sum(tc['failed_steps'] for tc in test_cases.values()),
                'skipped_steps': sum(tc['skipped_steps'] for tc in test_cases.values())
            }
        }
        
        return report_data
    
    def _calculate_duration(self, start_time: str, end_time: str) -> int:
        """Calculate duration in seconds"""
        try:
            start = datetime.fromisoformat(start_time)
            end = datetime.fromisoformat(end_time)
            return int((end - start).total_seconds())
        except:
            return 0
