"""Database-only screenshot manager with optional compression.

Used by the primary app stack to persist screenshots directly inside the
database as BLOBs so no filesystem directories are required.
"""

import sqlite3
import base64
import logging
from io import BytesIO
from datetime import datetime
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

# Try to import PIL for image compression
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.warning("PIL/Pillow not available. Screenshot compression disabled.")


class ScreenshotManagerDB:
    """
    Database-only screenshot manager with compression.
    """
    
    def __init__(self, db_path: str = None):
        """Initialize screenshot manager"""
        if db_path is None:
            try:
                from .database import get_db_path
                db_path = get_db_path()
            except Exception:
                import os
                db_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                    'db-data',
                    'ios.db',
                )
        self.db_path = db_path
        logger.info(f"ScreenshotManagerDB initialized with DB: {self.db_path}")
    
    def capture_and_save_screenshot(self, device_controller, execution_id: str,
                                    step_idx: int, action_id: str,
                                    test_case_id: str = None, suite_id: str = None) -> Optional[str]:
        """
        Capture screenshot from device and save to database.
        
        Args:
            device_controller: Appium device controller instance
            execution_id: Execution identifier
            step_idx: Step index
            action_id: Action identifier
            test_case_id: Test case identifier
            suite_id: Suite identifier
            
        Returns:
            screenshot_filename: Unique screenshot identifier
        """
        try:
            # Capture screenshot from device
            if hasattr(device_controller, 'driver') and device_controller.driver:
                screenshot_data = device_controller.driver.get_screenshot_as_png()
            else:
                logger.warning("Device controller has no driver, cannot capture screenshot")
                return None
            
            # Generate unique filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
            screenshot_filename = f"exec_{execution_id}_step_{step_idx}_{timestamp}.png"
            
            # Compress screenshot if PIL is available
            if PIL_AVAILABLE:
                compressed_data, compression_ratio = self.compress_screenshot(screenshot_data)
                original_size = len(screenshot_data)
                compressed_size = len(compressed_data)
            else:
                compressed_data = screenshot_data
                compression_ratio = 0.0
                original_size = len(screenshot_data)
                compressed_size = len(screenshot_data)
            
            # Save to database
            success = self.save_screenshot_to_db(
                compressed_data,
                screenshot_filename,
                execution_id,
                test_case_id,
                action_id,
                original_size,
                compressed_size,
                suite_id
            )
            
            if success:
                logger.info(f"✅ Screenshot saved: {screenshot_filename} "
                          f"(compressed {compression_ratio:.1f}%)")
                return screenshot_filename
            else:
                logger.error(f"Failed to save screenshot: {screenshot_filename}")
                return None
                
        except Exception as e:
            logger.error(f"Error capturing screenshot: {e}")
            return None
    
    def compress_screenshot(self, image_data: bytes, quality: int = 85,
                          max_width: int = 1920) -> Tuple[bytes, float]:
        """
        Compress screenshot to reduce database size.
        
        Args:
            image_data: Raw image bytes
            quality: JPEG quality (1-100)
            max_width: Maximum width (resize if larger)
            
        Returns:
            (compressed_bytes, compression_ratio)
        """
        if not PIL_AVAILABLE:
            return image_data, 0.0
        
        try:
            # Load image
            image = Image.open(BytesIO(image_data))
            
            # Resize if too large
            if image.width > max_width:
                ratio = max_width / image.width
                new_height = int(image.height * ratio)
                image = image.resize((max_width, new_height), Image.LANCZOS)
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Compress and save to bytes
            output = BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            compressed_data = output.getvalue()
            
            # Calculate compression ratio
            original_size = len(image_data)
            compressed_size = len(compressed_data)
            compression_ratio = ((original_size - compressed_size) / original_size) * 100
            
            logger.debug(f"Screenshot compressed: {original_size} -> {compressed_size} bytes "
                        f"({compression_ratio:.1f}% reduction)")
            
            return compressed_data, compression_ratio
            
        except Exception as e:
            logger.warning(f"Screenshot compression failed, using original: {e}")
            return image_data, 0.0
    
    def save_screenshot_to_db(self, screenshot_data: bytes, filename: str,
                             execution_id: str, test_case_id: str, action_id: str,
                             original_size: int, compressed_size: int,
                             suite_id: str = None) -> bool:
        """
        Save screenshot BLOB to database.
        
        Args:
            screenshot_data: Compressed image bytes
            filename: Unique screenshot identifier
            execution_id: Execution identifier
            test_case_id: Test case identifier
            action_id: Action identifier
            original_size: Original image size
            compressed_size: Compressed image size
            suite_id: Suite identifier
            
        Returns:
            success: Boolean
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Ensure BLOB-capable schema exists for screenshots table
            cursor.execute("PRAGMA table_info(screenshots)")
            columns = [col[1] for col in cursor.fetchall()]

            def _ensure_column(col_name: str, col_type: str):
                if col_name not in columns:
                    try:
                        cursor.execute(f"ALTER TABLE screenshots ADD COLUMN {col_name} {col_type}")
                        logger.info(f"Added column to screenshots: {col_name} {col_type}")
                    except Exception as e:
                        logger.debug(f"Column add skipped ({col_name}): {e}")

            # Add required columns when missing (migration-in-place)
            _ensure_column('screenshot_blob', 'BLOB')
            _ensure_column('screenshot_thumb_blob', 'BLOB')
            _ensure_column('screenshot_mime', 'TEXT')
            _ensure_column('test_execution_id', 'TEXT')
            _ensure_column('test_case_id', 'TEXT')
            _ensure_column('original_size', 'INTEGER')
            _ensure_column('compressed_size', 'INTEGER')
            _ensure_column('suite_id', 'TEXT')
            _ensure_column('timestamp', 'TEXT')
            # Refresh columns list after potential migration
            cursor.execute("PRAGMA table_info(screenshots)")
            columns = [col[1] for col in cursor.fetchall()]
            
            # Insert into screenshots table
            if 'screenshot_blob' in columns and 'test_execution_id' in columns:
                # New schema with BLOB columns
                cursor.execute('''
                    INSERT INTO screenshots
                    (filename, screenshot_blob, screenshot_mime, test_execution_id,
                     test_case_id, action_id, original_size, compressed_size, 
                     suite_id, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    filename,
                    screenshot_data,
                    'image/jpeg' if PIL_AVAILABLE else 'image/png',
                    execution_id,
                    test_case_id,
                    action_id,
                    original_size,
                    compressed_size,
                    suite_id,
                    datetime.now().isoformat()
                ))
            else:
                # Old schema - just save metadata (no BLOB)
                # This is for backward compatibility
                cursor.execute('''
                    INSERT INTO screenshots
                    (filename, path, action_id, suite_id, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    filename,
                    f"database_blob://{filename}",  # Indicate it's in database
                    action_id,
                    suite_id,
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            
            logger.debug(f"✅ Screenshot BLOB saved to database: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving screenshot to database: {e}")
            return False
    
    def get_screenshot_blob(self, filename: str) -> Optional[Tuple[bytes, Optional[bytes], str]]:
        """Return screenshot bytes, optional thumbnail bytes, and MIME type."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                '''
                SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                  FROM screenshots
                 WHERE filename = ?
                 ORDER BY timestamp DESC
                 LIMIT 1
                ''',
                (filename,),
            )
            row = cursor.fetchone()
            conn.close()

            if row and row[0]:
                return row[0], row[1], row[2]

            logger.warning(f"Screenshot not found: {filename}")
            return None
        except Exception as err:
            logger.error(f"Error retrieving screenshot blob: {err}")
            return None

    def get_screenshot_from_db(self, screenshot_filename: str) -> Optional[bytes]:
        """Legacy helper to fetch screenshot bytes."""
        blob_info = self.get_screenshot_blob(screenshot_filename)
        if blob_info and blob_info[0]:
            return blob_info[0]
        return None

    def get_screenshot_base64(self, screenshot_filename: str) -> Optional[str]:
        """
        Get screenshot as base64-encoded string for HTML embedding.
        
        Args:
            screenshot_filename: Screenshot identifier
            
        Returns:
            base64_string: "data:image/jpeg;base64,..." format
        """
        try:
            blob_info = self.get_screenshot_blob(screenshot_filename)

            if not blob_info or not blob_info[0]:
                return None

            # Encode as base64
            screenshot_data, _thumb_data, mime_type = blob_info
            base64_data = base64.b64encode(screenshot_data).decode('utf-8')

            # Format as data URI
            data_uri = f"data:{mime_type};base64,{base64_data}"

            return data_uri
            
        except Exception as e:
            logger.error(f"Error encoding screenshot to base64: {e}")
            return None
    
    def delete_screenshots_for_execution(self, execution_id: str) -> int:
        """
        Delete all screenshots for a specific execution.
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            count: Number of screenshots deleted
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if test_execution_id column exists
            cursor.execute("PRAGMA table_info(screenshots)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'test_execution_id' in columns:
                cursor.execute('''
                    DELETE FROM screenshots
                    WHERE test_execution_id = ?
                ''', (execution_id,))
            else:
                # Old schema - cannot delete by execution_id
                logger.warning("test_execution_id column not found, cannot delete screenshots")
                conn.close()
                return 0
            
            count = cursor.rowcount
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Deleted {count} screenshots for execution: {execution_id}")
            return count
            
        except Exception as e:
            logger.error(f"Error deleting screenshots: {e}")
            return 0
