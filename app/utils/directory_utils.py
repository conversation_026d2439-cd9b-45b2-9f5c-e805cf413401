import os
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)

def get_reports_directory():
    """Get reports directory with platform isolation"""
    logger.info("=== get_reports_directory() called from ROOT utils ===")
    try:
        # Import from the correct location
        import sys
        app_utils_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'utils')
        if app_utils_path not in sys.path:
            sys.path.insert(0, app_utils_path)
        from directory_paths_db import DirectoryPathsDB
        
        db = DirectoryPathsDB()
        reports_dir = db.get_path('REPORTS')
        logger.info(f"Database returned reports_dir: {reports_dir}")
        if reports_dir:
            logger.info(f"Returning reports directory from database: {reports_dir}")
            return reports_dir
        else:
            logger.info("No reports directory found in database")
    except Exception as e:
        logger.error(f"Error getting reports directory from database: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        pass
    
    # Try config.json in multiple locations
    config_paths = ['config.json', 'app/config.json', os.path.join(os.path.dirname(__file__), '..', 'app', 'config.json')]
    for config_path in config_paths:
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                if 'reports_directory' in config:
                    logger.info(f"Found reports_directory in {config_path}: {config['reports_directory']}")
                    return config['reports_directory']
        except Exception:
            continue
    
    # Default with platform suffix
    logger.info("Falling back to default: reports_ios")
    return 'reports_ios'

def get_test_cases_directory():
    """Get test cases directory with platform isolation"""
    try:
        # Import from the correct location
        import sys
        app_utils_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'utils')
        if app_utils_path not in sys.path:
            sys.path.insert(0, app_utils_path)
        from directory_paths_db import DirectoryPathsDB
        
        db = DirectoryPathsDB()
        test_cases_dir = db.get_path('TEST_CASES')
        if test_cases_dir:
            return test_cases_dir
    except Exception as e:
        logger.error(f"Error getting test cases directory from database: {str(e)}")
        pass
    
    # Try config.json in multiple locations
    config_paths = ['config.json', 'app/config.json', os.path.join(os.path.dirname(__file__), '..', 'app', 'config.json')]
    for config_path in config_paths:
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                if 'test_cases_directory' in config:
                    logger.info(f"Found test_cases_directory in {config_path}: {config['test_cases_directory']}")
                    return config['test_cases_directory']
        except Exception:
            continue
    
    # Default with platform suffix
    return 'test_cases_ios'