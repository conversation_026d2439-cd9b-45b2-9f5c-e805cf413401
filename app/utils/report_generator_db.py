"""Database-only report generator for the primary automation app.

Builds HTML content straight from the database with embedded screenshots so no
filesystem staging is necessary.
"""

import base64
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, Optional

logger = logging.getLogger(__name__)


class ReportGeneratorDB:
    """
    Generate HTML reports from database data only.
    """
    
    def __init__(self, db_path: str = None):
        """Initialize report generator"""
        if db_path is None:
            try:
                from .database import get_db_path
                db_path = get_db_path()
            except Exception:
                import os
                db_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                    'db-data',
                    'ios.db',
                )
        self.db_path = db_path
        logger.info(f"ReportGeneratorDB initialized with DB: {self.db_path}")
    
    def generate_html_from_database(self, execution_id: str) -> Optional[str]:
        """
        Generate complete HTML report from database.
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            html_string: Complete HTML report
        """
        logger.info(f"🚀 generate_html_from_database called for execution_id: {execution_id}")
        try:
            # Get report data from database
            report_data = self.get_report_data_from_db(execution_id)
            
            if not report_data:
                logger.error(f"No report data found for execution: {execution_id}")
                return None
            
            # Embed screenshots as base64
            report_data_with_screenshots = self.embed_screenshots_base64(report_data, execution_id)
            
            # Generate HTML
            html_content = self._render_html_template(report_data_with_screenshots)
            
            logger.info(f"✅ Generated HTML report for execution: {execution_id}")
            return html_content
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return None
    
    def get_report_data_from_db(self, execution_id: str) -> Optional[Dict]:
        """
        Retrieve report data from database.

        Args:
            execution_id: Execution identifier

        Returns:
            dict: Report data structure (from report_data column or data_json)
        """
        logger.info(f"🔍 get_report_data_from_db called for execution_id: {execution_id}")
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get execution report
            cursor.execute('''
                SELECT * FROM execution_reports
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                logger.warning(f"Execution report not found: {execution_id}")
                # Fall back to building from execution_tracking
                logger.info("No report_data found, building from execution_tracking...")
                try:
                    from .database_execution_tracker import DatabaseExecutionTracker
                except Exception:
                    from app.utils.database_execution_tracker import DatabaseExecutionTracker
                tracker = DatabaseExecutionTracker(self.db_path)
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM execution_tracking
                    WHERE test_execution_id = ?
                    ORDER BY step_idx ASC
                ''', (execution_id,))
                steps = [dict(r) for r in cursor.fetchall()]
                conn.close()
                if steps:
                    return tracker._build_report_data(execution_id, steps)
                return None
            
            report_dict = dict(row)
            
            # Try to get report_data (new schema)
            if report_dict.get('report_data'):
                try:
                    report_data = json.loads(report_dict['report_data'])
                    logger.info(f"Found report_data for execution {execution_id}")
                    logger.info(f"Report data keys: {list(report_data.keys())}")

                    # Check if it's in old format and convert to new format
                    if 'testCases' in report_data and 'test_cases' not in report_data:
                        logger.info(f"Converting old format report_data to new format for execution {execution_id}")
                        # Convert old format to new format
                        # Try to extract timestamp from the name or use current time
                        start_time = 'Unknown'
                        if 'name' in report_data and 'Execution' in report_data['name']:
                            # Try to extract date from name like "UI Execution 08/10/2025, 20:54:51"
                            import re
                            from datetime import datetime
                            date_match = re.search(r'(\d{2}/\d{2}/\d{4}, \d{2}:\d{2}:\d{2})', report_data['name'])
                            if date_match:
                                try:
                                    dt = datetime.strptime(date_match.group(1), '%d/%m/%Y, %H:%M:%S')
                                    start_time = dt.isoformat()
                                except:
                                    start_time = datetime.now().isoformat()
                            else:
                                start_time = datetime.now().isoformat()
                        else:
                            start_time = datetime.now().isoformat()

                        converted_data = {
                            'execution_id': execution_id,
                            'test_execution_id': execution_id,
                            'start_time': start_time,
                            'test_cases': [],
                            'summary': {
                                'total_tests': len(report_data.get('testCases', [])),
                                'passed': report_data.get('passed', 0),
                                'failed': report_data.get('failed', 0),
                                'total_steps': 0,
                                'passed_steps': 0,
                                'failed_steps': 0,
                                'skipped_steps': 0
                            }
                        }

                        # Convert test cases
                        for old_tc in report_data.get('testCases', []):
                            new_tc = {
                                'test_case_id': old_tc.get('name', 'Unknown'),
                                'name': old_tc.get('name', 'Unknown'),
                                'status': old_tc.get('status', 'unknown'),
                                'steps': [],
                                'passed_steps': 0,
                                'failed_steps': 0,
                                'skipped_steps': 0
                            }

                            # Convert steps
                            for i, old_step in enumerate(old_tc.get('steps', [])):
                                new_step = {
                                    'step_index': i,
                                    'action_type': old_step.get('name', 'Unknown'),
                                    'action_id': f"action_{i}",
                                    'status': old_step.get('status', 'unknown'),
                                    'error_message': None,
                                    'screenshot_filename': None,
                                    'timestamp': 'Unknown',
                                    'retry_count': 0
                                }
                                new_tc['steps'].append(new_step)

                                # Update counters
                                converted_data['summary']['total_steps'] += 1
                                if old_step.get('status') == 'passed':
                                    new_tc['passed_steps'] += 1
                                    converted_data['summary']['passed_steps'] += 1
                                elif old_step.get('status') == 'failed':
                                    new_tc['failed_steps'] += 1
                                    converted_data['summary']['failed_steps'] += 1
                                else:
                                    new_tc['skipped_steps'] += 1
                                    converted_data['summary']['skipped_steps'] += 1

                            converted_data['test_cases'].append(new_tc)

                        logger.info(f"Conversion completed for execution {execution_id}. Converted data has execution_id: {converted_data.get('execution_id')}")
                        return converted_data
                    else:
                        return report_data
                except Exception as e:
                    logger.warning(f"Failed to parse report_data JSON: {e}")
            
            # Fallback to data_json (old schema - BLOB)
            if report_dict.get('data_json'):
                try:
                    data_json_bytes = report_dict['data_json']
                    if isinstance(data_json_bytes, bytes):
                        return json.loads(data_json_bytes.decode('utf-8'))
                    else:
                        return json.loads(data_json_bytes)
                except Exception as e:
                    logger.warning(f"Failed to parse data_json BLOB: {e}")
            
            # If no report data, build from execution_tracking
            logger.info("No report_data found, building from execution_tracking...")
            try:
                from .database_execution_tracker import DatabaseExecutionTracker
            except Exception:
                from app.utils.database_execution_tracker import DatabaseExecutionTracker
            tracker = DatabaseExecutionTracker(self.db_path)
            
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY step_idx ASC
            ''', (execution_id,))
            
            steps = [dict(row) for row in cursor.fetchall()]
            conn.close()
            
            if steps:
                return tracker._build_report_data(execution_id, steps)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting report data: {e}")
            return None
    
    def embed_screenshots_base64(self, report_data: Dict, execution_id: str) -> Dict:
        """
        Embed screenshots as base64 in report data.

        Args:
            report_data: Report data structure
            execution_id: Execution identifier
            
        Returns:
            report_data: Updated with base64 screenshots
        """
        try:
            from .screenshot_manager_db import ScreenshotManagerDB
            screenshot_manager = ScreenshotManagerDB(self.db_path)

            # Iterate through test cases and steps
            for test_case in report_data.get('test_cases', []):
                for step in test_case.get('steps', []):
                    screenshot_filename = step.get('screenshot_filename')

                    if screenshot_filename:
                        # Ensure a DB-backed URL for linking
                        if not step.get('screenshot_url'):
                            step['screenshot_url'] = f"/api/executions/{report_data.get('execution_id')}/screenshots/{screenshot_filename}"
                        # Get screenshot as base64
                        base64_data = screenshot_manager.get_screenshot_base64(screenshot_filename)

                        if base64_data:
                            step['screenshot_base64'] = base64_data
                        else:
                            fallback_data = self._get_execution_tracking_screenshot_base64(
                                screenshot_filename,
                                step.get('action_id'),
                            )
                            if fallback_data:
                                step['screenshot_base64'] = fallback_data
                            else:
                                logger.warning(f"Screenshot not found: {screenshot_filename}")
            
            return report_data

        except Exception as e:
            logger.error(f"Error embedding screenshots: {e}")
            return report_data

    def _get_execution_tracking_screenshot_base64(self, screenshot_filename: str, action_id: Optional[str] = None) -> Optional[str]:
        """Fallback to execution_tracking table for inline screenshot data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = (
                "SELECT screenshot_blob, screenshot_mime FROM execution_tracking "
                "WHERE screenshot_filename = ? AND screenshot_blob IS NOT NULL "
                "ORDER BY id DESC LIMIT 1"
            )
            params = [screenshot_filename]

            cursor.execute(query, params)
            row = cursor.fetchone()

            if not row and action_id:
                cursor.execute(
                    "SELECT screenshot_blob, screenshot_mime FROM execution_tracking "
                    "WHERE action_id = ? AND screenshot_blob IS NOT NULL "
                    "ORDER BY id DESC LIMIT 1",
                    (action_id,),
                )
                row = cursor.fetchone()

            conn.close()

            if row and row[0]:
                mime = row[1] or 'image/png'
                base64_data = base64.b64encode(row[0]).decode('utf-8')
                return f"data:{mime};base64,{base64_data}"

        except Exception as fallback_err:
            logger.debug(f"Fallback screenshot retrieval failed: {fallback_err}")

        return None
    
    def save_report_data_to_db(self, execution_id: str, report_data: Dict, 
                              status: str = 'completed') -> bool:
        """
        Save complete report data to database.
        
        Args:
            execution_id: Execution identifier
            report_data: Complete report data structure (dict)
            status: Execution status
            
        Returns:
            success: Boolean
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if report_data column exists
            cursor.execute("PRAGMA table_info(execution_reports)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'report_data' in columns:
                # New schema
                cursor.execute('''
                    UPDATE execution_reports
                    SET report_data = ?, status = ?
                    WHERE test_execution_id = ?
                ''', (
                    json.dumps(report_data),
                    status,
                    execution_id
                ))
            else:
                # Old schema - use data_json BLOB
                cursor.execute('''
                    UPDATE execution_reports
                    SET data_json = ?, status = ?
                    WHERE test_execution_id = ?
                ''', (
                    json.dumps(report_data).encode('utf-8'),
                    status,
                    execution_id
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Saved report data for execution: {execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving report data: {e}")
            return False
    
    def _render_html_template(self, report_data: Dict) -> str:
        """
        Render HTML template with report data.
        
        Args:
            report_data: Report data with embedded screenshots
            
        Returns:
            html_string: Complete HTML report
        """
        # Basic HTML template
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report - {report_data.get('execution_id', 'Unknown')}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background-color: #2196F3;
            color: white;
            padding: 20px;
            border-radius: 5px;
        }}
        .summary {{
            background-color: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .test-case {{
            background-color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .step {{
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #ddd;
            background-color: #fafafa;
        }}
        .step.passed {{
            border-left-color: #4CAF50;
            background-color: #f1f8f4;
        }}
        .step.failed {{
            border-left-color: #f44336;
            background-color: #ffebee;
        }}
        .screenshot {{
            max-width: 300px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-top: 10px;
        }}
        .screenshot:hover {{
            opacity: 0.8;
        }}
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }}
        .modal-content {{
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
            margin-top: 50px;
        }}
        .close {{
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Execution Report</h1>
        <p>Execution ID: {report_data.get('execution_id', 'Unknown')}</p>
        <p>Start Time: {report_data.get('start_time', 'Unknown')}</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: {report_data.get('summary', {}).get('total_tests', 0)}</p>
        <p>Passed: {report_data.get('summary', {}).get('passed', 0)}</p>
        <p>Failed: {report_data.get('summary', {}).get('failed', 0)}</p>
        <p>Total Steps: {report_data.get('summary', {}).get('total_steps', 0)}</p>
    </div>
"""
        
        # Add test cases
        for test_case in report_data.get('test_cases', []):
            html += f"""
    <div class="test-case">
        <h3>{test_case.get('name', 'Unknown Test')}</h3>
        <p>Status: <strong>{test_case.get('status', 'unknown').upper()}</strong></p>
        <p>Passed Steps: {test_case.get('passed_steps', 0)} | Failed Steps: {test_case.get('failed_steps', 0)}</p>
"""
            
            # Add steps
            for step in test_case.get('steps', []):
                status_class = step.get('status', 'unknown')
                html += f"""
        <div class="step {status_class}">
            <strong>Step {step.get('step_index', 0)}: {step.get('action_type', 'Unknown')}</strong>
            <p>Status: {step.get('status', 'unknown').upper()}</p>
"""
                
                if step.get('error_message'):
                    html += f"            <p style='color: red;'>Error: {step.get('error_message')}</p>\n"
                
                if step.get('screenshot_base64'):
                    html += f"""
            <img src="{step.get('screenshot_base64')}" class="screenshot" 
                 onclick="openModal(this.src)" alt="Screenshot">
"""
                    if step.get('screenshot_url'):
                        html += f"            <div><a href=\"{step.get('screenshot_url')}\" target=\"_blank\">Open full image</a></div>\n"
                elif step.get('screenshot_url'):
                    # Fallback: provide a link if we couldn't embed inline image
                    html += f"            <div><a href=\"{step.get('screenshot_url')}\" target=\"_blank\">Open screenshot</a></div>\n"
                
                html += "        </div>\n"
            
            html += "    </div>\n"
        
        # Add modal and JavaScript
        html += """
    <div id="imageModal" class="modal" onclick="closeModal()">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(src) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }
        
        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }
    </script>
</body>
</html>
"""
        
        return html
