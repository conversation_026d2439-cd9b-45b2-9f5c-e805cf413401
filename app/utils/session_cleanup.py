#!/usr/bin/env python3
"""
Session Cleanup Utility for iOS App
Cleans up stale Appium sessions that might be blocking new connections
"""

import requests
import logging
import time
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class SessionCleanup:
    """Utility class for cleaning up Appium sessions"""
    
    def __init__(self, appium_port: int = 4723):
        self.appium_port = appium_port
        self.appium_url = f"http://localhost:{appium_port}/wd/hub"
        
    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """Get list of active Appium sessions"""
        try:
            response = requests.get(f"{self.appium_url}/sessions", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('value', [])
            else:
                logger.warning(f"Failed to get sessions: HTTP {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"Error getting active sessions: {e}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a specific Appium session"""
        try:
            response = requests.delete(f"{self.appium_url}/session/{session_id}", timeout=10)
            if response.status_code in [200, 404]:  # 404 means session already gone
                logger.info(f"Successfully deleted session: {session_id}")
                return True
            else:
                logger.warning(f"Failed to delete session {session_id}: HTTP {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {e}")
            return False
    
    def cleanup_all_sessions(self) -> int:
        """Clean up all active Appium sessions"""
        sessions = self.get_active_sessions()
        if not sessions:
            logger.info("No active sessions to clean up")
            return 0
        
        logger.info(f"Found {len(sessions)} active sessions to clean up")
        cleaned_count = 0
        
        for session in sessions:
            session_id = session.get('id')
            if session_id:
                if self.delete_session(session_id):
                    cleaned_count += 1
                time.sleep(0.5)  # Small delay between deletions
        
        logger.info(f"Cleaned up {cleaned_count}/{len(sessions)} sessions")
        return cleaned_count
    
    def cleanup_device_sessions(self, device_id: str) -> int:
        """Clean up sessions for a specific device"""
        sessions = self.get_active_sessions()
        if not sessions:
            logger.info("No active sessions to clean up")
            return 0
        
        device_sessions = []
        for session in sessions:
            capabilities = session.get('capabilities', {})
            session_device_id = capabilities.get('udid') or capabilities.get('deviceName')
            if session_device_id == device_id:
                device_sessions.append(session)
        
        if not device_sessions:
            logger.info(f"No active sessions found for device: {device_id}")
            return 0
        
        logger.info(f"Found {len(device_sessions)} active sessions for device {device_id}")
        cleaned_count = 0
        
        for session in device_sessions:
            session_id = session.get('id')
            if session_id:
                if self.delete_session(session_id):
                    cleaned_count += 1
                time.sleep(0.5)  # Small delay between deletions
        
        logger.info(f"Cleaned up {cleaned_count}/{len(device_sessions)} sessions for device {device_id}")
        return cleaned_count
    
    def is_appium_responsive(self) -> bool:
        """Check if Appium server is responsive"""
        try:
            response = requests.get(f"{self.appium_url}/status", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Appium server not responsive: {e}")
            return False

def cleanup_sessions_for_device(device_id: str, appium_port: int = 4723) -> int:
    """Convenience function to clean up sessions for a specific device"""
    cleanup = SessionCleanup(appium_port)
    return cleanup.cleanup_device_sessions(device_id)

def cleanup_all_sessions(appium_port: int = 4723) -> int:
    """Convenience function to clean up all sessions"""
    cleanup = SessionCleanup(appium_port)
    return cleanup.cleanup_all_sessions()

if __name__ == "__main__":
    # Command line usage
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "all":
            count = cleanup_all_sessions()
            print(f"Cleaned up {count} sessions")
        else:
            device_id = sys.argv[1]
            count = cleanup_sessions_for_device(device_id)
            print(f"Cleaned up {count} sessions for device {device_id}")
    else:
        print("Usage: python session_cleanup.py [device_id|all]")
        print("Examples:")
        print("  python session_cleanup.py all")
        print("  python session_cleanup.py 00008120-00186C801E13C01E")
