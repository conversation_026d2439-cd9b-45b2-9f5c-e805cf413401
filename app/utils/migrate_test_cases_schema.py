#!/usr/bin/env python3
"""
Database Migration Script: Add test_case_id field to test_cases table

This script fixes Issue 1 by adding the missing test_case_id field to the test_cases table.
The TestCaseManager expects this field but the current schema doesn't include it,
causing sync failures and duplicate test case entries in the UI.
"""

import os
import sqlite3
import logging
import uuid
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_path(db_name='test_suites'):
    """Get database path using the same logic as the main application"""
    try:
        from .database import get_db_path as app_get_db_path
        return app_get_db_path(db_name)
    except ImportError:
        # Fallback for direct script execution
        base_dir = Path(__file__).parent.parent
        # Use platform-specific database names
        if db_name == 'test_suites':
            return str(base_dir / 'data' / 'ios_test_suites.db')
        return str(base_dir / 'data' / f'ios_{db_name}.db')

def check_schema_needs_migration(db_path):
    """Check if the test_cases table needs the test_case_id field added"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute("PRAGMA table_info(test_cases)")
        columns = cursor.fetchall()
        conn.close()
        
        # Check if test_case_id column exists
        column_names = [col[1] for col in columns]
        has_test_case_id = 'test_case_id' in column_names
        
        logger.info(f"Current test_cases table columns: {column_names}")
        logger.info(f"Has test_case_id field: {has_test_case_id}")
        
        return not has_test_case_id
        
    except Exception as e:
        logger.error(f"Error checking schema: {e}")
        return False

def migrate_test_cases_schema(db_path):
    """Add missing fields to test_cases table and populate test_case_id values"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("Starting test_cases table schema migration...")
        
        # Add missing columns to test_cases table
        missing_columns = [
            ('test_case_id', 'TEXT'),
            ('suite_id', 'TEXT'),  # Ensure this exists too
            ('platform', 'TEXT'),
            ('description', 'TEXT'),
            ('file_path', 'TEXT'),
            ('step_count', 'INTEGER'),
            ('json_payload', 'TEXT'),
            ('test_idx', 'INTEGER'),
            ('created_at', 'TEXT'),
            ('updated_at', 'TEXT')
        ]
        
        for column_name, column_type in missing_columns:
            try:
                cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {column_name} {column_type}")
                logger.info(f"Added column: {column_name} {column_type}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"Column {column_name} already exists, skipping")
                else:
                    logger.warning(f"Error adding column {column_name}: {e}")
        
        # Generate test_case_id for existing records that don't have one
        cursor.execute("SELECT id, name FROM test_cases WHERE test_case_id IS NULL OR test_case_id = ''")
        records_to_update = cursor.fetchall()
        
        logger.info(f"Found {len(records_to_update)} records without test_case_id")
        
        for record_id, name in records_to_update:
            # Generate a unique test_case_id
            test_case_id = str(uuid.uuid4())[:8].upper()
            
            cursor.execute(
                "UPDATE test_cases SET test_case_id = ?, updated_at = datetime('now') WHERE id = ?",
                (test_case_id, record_id)
            )
            logger.info(f"Updated record {record_id} ({name}) with test_case_id: {test_case_id}")
        
        # Create index for better performance
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_case_id ON test_cases(test_case_id)")
            logger.info("Created index on test_case_id")
        except sqlite3.OperationalError as e:
            logger.info(f"Index creation skipped: {e}")
        
        conn.commit()
        conn.close()
        
        logger.info("Schema migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """Main migration function"""
    logger.info("Starting test_cases table schema migration...")
    
    # Get database path
    db_path = get_db_path('test_suites')
    logger.info(f"Database path: {db_path}")
    
    # Check if database exists
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return False
    
    # Check if migration is needed
    if not check_schema_needs_migration(db_path):
        logger.info("Schema migration not needed - test_case_id field already exists")
        return True
    
    # Perform migration
    success = migrate_test_cases_schema(db_path)
    
    if success:
        logger.info("Migration completed successfully!")
        logger.info("TestCaseManager should now be able to sync properly without duplicates")
    else:
        logger.error("Migration failed!")
    
    return success

if __name__ == '__main__':
    main()