"""
PDF Report Generator for Android Mobile Automation
Generates professional PDF reports with embedded screenshots
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# PDF generation imports
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import (
        SimpleDocTemplate, Table, TableStyle, Paragraph, 
        Spacer, PageBreak, Image, KeepTogether
    )
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logging.warning("ReportLab not installed. PDF generation will not be available.")

logger = logging.getLogger(__name__)


class PDFReportGenerator:
    """Generates PDF reports for test execution results"""
    
    def __init__(self):
        """Initialize the PDF report generator"""
        self.styles = None
        if REPORTLAB_AVAILABLE:
            self._init_styles()
    
    def _init_styles(self):
        """Initialize custom paragraph styles"""
        self.styles = getSampleStyleSheet()
        
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            textColor=colors.HexColor('#2c3e50'),
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        
        # Subtitle style
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            textColor=colors.HexColor('#34495e'),
            spaceAfter=12,
            fontName='Helvetica-Bold'
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=14,
            textColor=colors.HexColor('#2980b9'),
            spaceAfter=10,
            spaceBefore=15,
            fontName='Helvetica-Bold'
        ))
        
        # Step header style
        self.styles.add(ParagraphStyle(
            name='StepHeader',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.HexColor('#2c3e50'),
            fontName='Helvetica-Bold',
            spaceAfter=6
        ))
        
        # Normal text style
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#34495e'),
            spaceAfter=6
        ))
        
        # Status styles
        self.styles.add(ParagraphStyle(
            name='PassStatus',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#27ae60'),
            fontName='Helvetica-Bold'
        ))
        
        self.styles.add(ParagraphStyle(
            name='FailStatus',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#e74c3c'),
            fontName='Helvetica-Bold'
        ))
        
        self.styles.add(ParagraphStyle(
            name='SkipStatus',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#95a5a6'),
            fontName='Helvetica-Bold'
        ))
    
    def generate_report(self, data_json_path: str, output_pdf_path: str,
                       report_dir: str) -> bool:
        """
        Generate a PDF report from execution data

        Args:
            data_json_path: Path to the data.json file
            output_pdf_path: Path where the PDF should be saved
            report_dir: Directory containing screenshots and other assets

        Returns:
            True if report generated successfully, False otherwise
        """
        if not REPORTLAB_AVAILABLE:
            logger.error("ReportLab is not installed. Cannot generate PDF report.")
            logger.error("Install with: pip install reportlab")
            return False

        try:
            # Load execution data
            with open(data_json_path, 'r') as f:
                data = json.load(f)

            logger.info(f"Generating PDF report: {output_pdf_path}")
            logger.info(f"Report directory: {report_dir}")

            # Copy screenshots to report directory before generating PDF
            self._copy_screenshots_to_report(data, report_dir)

            # Create PDF document
            doc = SimpleDocTemplate(
                output_pdf_path,
                pagesize=letter,
                rightMargin=0.75*inch,
                leftMargin=0.75*inch,
                topMargin=1*inch,
                bottomMargin=0.75*inch
            )

            # Build the story (content)
            story = []

            # Add title page
            self._add_title_page(story, data)

            # Add execution summary
            self._add_execution_summary(story, data)

            # Add test cases with steps
            self._add_test_cases(story, data, report_dir)

            # Build the PDF
            doc.build(story)

            logger.info(f"PDF report generated successfully: {output_pdf_path}")
            return True

        except Exception as e:
            logger.error(f"Error generating PDF report: {str(e)}", exc_info=True)
            return False

    def _copy_screenshots_to_report(self, data: Dict, report_dir: str):
        """
        Copy screenshots from temp/static folder to report directory

        Args:
            data: Execution data containing test cases and steps
            report_dir: Report directory where screenshots should be copied
        """
        import shutil

        # Create screenshots directory in report folder
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        os.makedirs(screenshots_dir, exist_ok=True)

        # Possible source directories for screenshots
        source_dirs = [
            os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'screenshots'),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'screenshots'),
        ]

        # Try to get temp folder from settings
        try:
            settings_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'app', 'data', 'settings.json'
            )
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                    temp_folder = settings.get('temp_folder', '')
                    if temp_folder and os.path.exists(temp_folder):
                        source_dirs.insert(0, temp_folder)
        except Exception as e:
            logger.debug(f"Could not load temp folder from settings: {e}")

        logger.info(f"Screenshot source directories: {source_dirs}")

        # Collect all screenshot filenames from data
        screenshot_files = set()
        test_cases = data.get('testCases', [])
        for test_case in test_cases:
            steps = test_case.get('steps', [])
            for step in steps:
                # Check various screenshot field names
                for field in ['screenshot_filename', 'report_screenshot', 'screenshot', 'screenshot_path']:
                    if field in step and step[field]:
                        filename = os.path.basename(step[field])
                        screenshot_files.add(filename)

        logger.info(f"Found {len(screenshot_files)} unique screenshots to copy")

        # Copy each screenshot
        copied_count = 0
        for screenshot_file in screenshot_files:
            copied = False
            for source_dir in source_dirs:
                source_path = os.path.join(source_dir, screenshot_file)
                if os.path.exists(source_path):
                    dest_path = os.path.join(screenshots_dir, screenshot_file)
                    try:
                        shutil.copy2(source_path, dest_path)
                        logger.info(f"Copied screenshot: {screenshot_file}")
                        copied = True
                        copied_count += 1
                        break
                    except Exception as e:
                        logger.error(f"Error copying screenshot {screenshot_file}: {e}")

            if not copied:
                logger.warning(f"Screenshot not found in any source directory: {screenshot_file}")

        logger.info(f"Successfully copied {copied_count}/{len(screenshot_files)} screenshots")
    
    def _add_title_page(self, story: List, data: Dict):
        """Add title page to the report"""
        # Title
        title = Paragraph("Test Execution Report", self.styles['CustomTitle'])
        story.append(title)
        story.append(Spacer(1, 0.3*inch))
        
        # Execution info
        execution_info = data.get('execution', {})
        suite_name = execution_info.get('suite_name', 'Unknown Suite')
        start_time = execution_info.get('start_time', 'N/A')
        end_time = execution_info.get('end_time', 'N/A')
        
        info_text = f"""
        <b>Test Suite:</b> {suite_name}<br/>
        <b>Start Time:</b> {start_time}<br/>
        <b>End Time:</b> {end_time}<br/>
        <b>Platform:</b> Android
        """
        
        info_para = Paragraph(info_text, self.styles['CustomNormal'])
        story.append(info_para)
        story.append(PageBreak())
    
    def _add_execution_summary(self, story: List, data: Dict):
        """Add execution summary section"""
        story.append(Paragraph("Execution Summary", self.styles['CustomSubtitle']))
        story.append(Spacer(1, 0.2*inch))

        # Calculate statistics
        test_cases = data.get('testCases', [])
        total_tests = len(test_cases)
        passed_tests = sum(1 for tc in test_cases if tc.get('status') == 'passed')
        failed_tests = sum(1 for tc in test_cases if tc.get('status') == 'failed')
        skipped_tests = sum(1 for tc in test_cases if tc.get('status') == 'skipped')

        # Use 'steps' instead of 'actions' to match data.json structure
        total_steps = sum(len(tc.get('steps', [])) for tc in test_cases)
        passed_steps = sum(
            sum(1 for step in tc.get('steps', []) if step.get('status') == 'passed')
            for tc in test_cases
        )
        failed_steps = sum(
            sum(1 for step in tc.get('steps', []) if step.get('status') == 'failed')
            for tc in test_cases
        )
        
        # Create summary table
        summary_data = [
            ['Metric', 'Count'],
            ['Total Test Cases', str(total_tests)],
            ['Passed Test Cases', str(passed_tests)],
            ['Failed Test Cases', str(failed_tests)],
            ['Skipped Test Cases', str(skipped_tests)],
            ['', ''],
            ['Total Steps', str(total_steps)],
            ['Passed Steps', str(passed_steps)],
            ['Failed Steps', str(failed_steps)]
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica-Bold'),
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 0.3*inch))
    
    def _add_test_cases(self, story: List, data: Dict, report_dir: str):
        """Add test cases with all steps"""
        test_cases = data.get('testCases', [])

        for test_idx, test_case in enumerate(test_cases, 1):
            # Test case header
            test_name = test_case.get('name', f'Test Case {test_idx}')
            test_status = test_case.get('status', 'unknown')

            story.append(PageBreak())
            story.append(Paragraph(
                f"Test Case {test_idx}: {test_name}",
                self.styles['CustomSubtitle']
            ))

            # Test case status
            status_style = self._get_status_style(test_status)
            story.append(Paragraph(f"Status: {test_status.upper()}", status_style))
            story.append(Spacer(1, 0.2*inch))

            # Add all steps (use 'steps' instead of 'actions' to match data.json structure)
            steps = test_case.get('steps', [])
            for step_idx, step in enumerate(steps, 1):
                self._add_step(story, step, step_idx, report_dir)
    
    def _add_step(self, story: List, step: Dict, step_number: int, report_dir: str):
        """Add a single step with screenshot"""
        # Create a list to hold step content (for KeepTogether)
        step_content = []

        # Step header - use 'name' field from data.json
        step_name = step.get('name', f'Step {step_number}')
        status = step.get('status', 'unknown')

        step_header = f"Step {step_number}: {step_name}"
        step_content.append(Paragraph(step_header, self.styles['StepHeader']))

        # Status
        status_style = self._get_status_style(status)
        step_content.append(Paragraph(f"Status: {status.upper()}", status_style))

        # Add metadata if available
        metadata_lines = []

        # Duration
        if step.get('duration'):
            metadata_lines.append(f"<b>Duration:</b> {step['duration']}")

        # Action ID
        if step.get('action_id'):
            metadata_lines.append(f"<b>Action ID:</b> {step['action_id']}")

        # Locator (if available)
        if step.get('locator'):
            metadata_lines.append(f"<b>Locator:</b> {step['locator']}")

        # Value (if available)
        if step.get('value'):
            metadata_lines.append(f"<b>Value:</b> {step['value']}")

        # Error (if available)
        if step.get('error'):
            metadata_lines.append(f"<b>Error:</b> <font color='red'>{step['error']}</font>")

        if metadata_lines:
            metadata_text = "<br/>".join(metadata_lines)
            step_content.append(Paragraph(metadata_text, self.styles['CustomNormal']))

        # Add screenshot if available
        screenshot_path = self._find_screenshot(step, report_dir)
        if screenshot_path and os.path.exists(screenshot_path):
            try:
                # Add screenshot with reasonable size
                img = Image(screenshot_path, width=4*inch, height=3*inch, kind='proportional')
                step_content.append(Spacer(1, 0.1*inch))
                step_content.append(img)
            except Exception as e:
                logger.warning(f"Could not add screenshot {screenshot_path}: {e}")
                step_content.append(Paragraph(
                    f"<i>Screenshot available but could not be embedded: {os.path.basename(screenshot_path)}</i>",
                    self.styles['CustomNormal']
                ))
        else:
            step_content.append(Paragraph(
                f"<i>No screenshot available</i>",
                self.styles['CustomNormal']
            ))

        step_content.append(Spacer(1, 0.15*inch))

        # Add all step content as a KeepTogether group
        story.append(KeepTogether(step_content))
    
    def _find_screenshot(self, step: Dict, report_dir: str) -> Optional[str]:
        """Find screenshot file for a step"""
        # Try different screenshot field names (matching data.json structure)
        screenshot_fields = [
            'screenshot_filename',  # Primary field in data.json
            'report_screenshot',    # Alternative field in data.json
            'screenshot',           # Generic field
            'screenshot_path',      # Path field
            'resolved_screenshot'   # Resolved path field
        ]

        for field in screenshot_fields:
            if field in step and step[field]:
                screenshot_name = step[field]

                # Remove 'screenshots/' prefix if present
                if screenshot_name.startswith('screenshots/'):
                    screenshot_name = screenshot_name.replace('screenshots/', '')

                # Try different paths
                possible_paths = [
                    os.path.join(report_dir, 'screenshots', screenshot_name),
                    os.path.join(report_dir, 'screenshots', os.path.basename(screenshot_name)),
                    os.path.join(report_dir, screenshot_name),
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        logger.debug(f"Found screenshot at: {path}")
                        return path

        # Try action_id based screenshot (fallback)
        action_id = step.get('action_id')
        if action_id:
            # Try with .png extension
            screenshot_name = f"{action_id}.png"
            screenshot_path = os.path.join(report_dir, 'screenshots', screenshot_name)
            if os.path.exists(screenshot_path):
                logger.debug(f"Found screenshot by action_id at: {screenshot_path}")
                return screenshot_path

            # Try with action_ prefix
            screenshot_name = f"action_{action_id}_screenshot.png"
            screenshot_path = os.path.join(report_dir, 'screenshots', screenshot_name)
            if os.path.exists(screenshot_path):
                logger.debug(f"Found screenshot by action_id (with prefix) at: {screenshot_path}")
                return screenshot_path

        logger.warning(f"Screenshot not found for step: {step.get('name', 'Unknown')}")
        return None
    
    def _get_status_style(self, status: str) -> ParagraphStyle:
        """Get the appropriate style for a status"""
        status_lower = status.lower()
        if status_lower in ['passed', 'pass', 'success']:
            return self.styles['PassStatus']
        elif status_lower in ['failed', 'fail', 'error']:
            return self.styles['FailStatus']
        else:
            return self.styles['SkipStatus']

