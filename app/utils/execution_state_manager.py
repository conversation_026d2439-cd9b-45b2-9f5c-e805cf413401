"""
Execution State Manager
======================

Centralized manager to coordinate execution state across different tracking systems
and prevent multiple execution folders from being created for a single test run.
"""

import logging
import threading
import time
from datetime import datetime
from typing import Optional, Dict, Any
import uuid

logger = logging.getLogger(__name__)

class ExecutionStateManager:
    """Centralized execution state manager to prevent duplicate executions"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._current_execution = None
        self._execution_history = {}
        self._last_execution_time = None
        
    def start_execution(self, execution_type: str = "test_suite", suite_id: str = None, 
                       test_case_name: str = None, force_new: bool = False) -> Dict[str, Any]:
        """
        Start a new execution or return existing execution if one is already running
        
        Args:
            execution_type: Type of execution ("test_suite", "individual_test", "retry")
            suite_id: Suite ID for test suite executions
            test_case_name: Test case name for individual test executions
            force_new: Force creation of new execution even if one exists
            
        Returns:
            Dict containing execution information
        """
        with self._lock:
            current_time = time.time()
            
            # Check if we have a recent execution (within 5 seconds) and not forcing new
            if (not force_new and 
                self._current_execution and 
                self._last_execution_time and 
                (current_time - self._last_execution_time) < 5):
                
                logger.info(f"Reusing existing execution: {self._current_execution['execution_id']}")
                return self._current_execution
            
            # Create new execution with UUID-based ID
            execution_id = str(uuid.uuid4())

            # Ensure unique execution ID in this process history (extremely unlikely to collide)
            while execution_id in self._execution_history:
                execution_id = str(uuid.uuid4())

            execution_info = {
                'execution_id': execution_id,
                'execution_type': execution_type,
                'suite_id': suite_id,
                'test_case_name': test_case_name,
                'start_time': datetime.now().isoformat(),
                'status': 'running',
                'created_by': 'execution_state_manager'
            }

            self._current_execution = execution_info
            self._last_execution_time = current_time
            self._execution_history[execution_id] = execution_info
            
            logger.info(f"Started new execution: {execution_id} (type: {execution_type})")
            return execution_info
    
    def get_current_execution(self) -> Optional[Dict[str, Any]]:
        """Get the current active execution"""
        with self._lock:
            return self._current_execution.copy() if self._current_execution else None
    
    def complete_execution(self, execution_id: str, status: str = "completed") -> bool:
        """
        Mark an execution as completed
        
        Args:
            execution_id: The execution ID to complete
            status: Final status ("completed", "failed", "cancelled")
            
        Returns:
            True if execution was found and completed, False otherwise
        """
        with self._lock:
            if (self._current_execution and 
                self._current_execution['execution_id'] == execution_id):
                
                self._current_execution['status'] = status
                self._current_execution['end_time'] = datetime.now().isoformat()
                
                # Move to history and clear current
                self._execution_history[execution_id] = self._current_execution.copy()
                self._current_execution = None
                self._last_execution_time = None
                
                logger.info(f"Completed execution: {execution_id} with status: {status}")
                return True
            
            return False
    
    def is_execution_running(self) -> bool:
        """Check if any execution is currently running"""
        with self._lock:
            return self._current_execution is not None
    
    def get_execution_info(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific execution"""
        with self._lock:
            if (self._current_execution and 
                self._current_execution['execution_id'] == execution_id):
                return self._current_execution.copy()
            
            return self._execution_history.get(execution_id, {}).copy() if execution_id in self._execution_history else None
    
    def cleanup_old_executions(self, max_age_hours: int = 24):
        """Clean up old execution history entries"""
        with self._lock:
            current_time = datetime.now()
            to_remove = []
            
            for execution_id, execution_info in self._execution_history.items():
                try:
                    start_time = datetime.fromisoformat(execution_info['start_time'])
                    age_hours = (current_time - start_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        to_remove.append(execution_id)
                except Exception as e:
                    logger.warning(f"Error checking age of execution {execution_id}: {e}")
                    to_remove.append(execution_id)
            
            for execution_id in to_remove:
                del self._execution_history[execution_id]
                logger.info(f"Cleaned up old execution: {execution_id}")
    
    def force_reset(self):
        """Force reset all execution state (emergency use only)"""
        with self._lock:
            logger.warning("Force resetting all execution state")
            self._current_execution = None
            self._last_execution_time = None
            # Keep history for debugging
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Get a summary of execution state for debugging"""
        with self._lock:
            return {
                'current_execution': self._current_execution,
                'last_execution_time': self._last_execution_time,
                'history_count': len(self._execution_history),
                'is_running': self._current_execution is not None
            }

# Global instance
execution_state_manager = ExecutionStateManager()

def get_execution_state_manager() -> ExecutionStateManager:
    """Get the global execution state manager instance"""
    return execution_state_manager

def start_coordinated_execution(execution_type: str = "test_suite", suite_id: str = None, 
                              test_case_name: str = None, force_new: bool = False) -> Dict[str, Any]:
    """Convenience function to start a coordinated execution"""
    return execution_state_manager.start_execution(execution_type, suite_id, test_case_name, force_new)

def get_current_execution_info() -> Optional[Dict[str, Any]]:
    """Convenience function to get current execution info"""
    return execution_state_manager.get_current_execution()

def complete_current_execution(status: str = "completed") -> bool:
    """Convenience function to complete current execution"""
    current = execution_state_manager.get_current_execution()
    if current:
        return execution_state_manager.complete_execution(current['execution_id'], status)
    return False
