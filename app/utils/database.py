import os
import sqlite3
import logging
import json
import re
import glob
from datetime import datetime
from typing import Optional

logger = logging.getLogger(__name__)

# Create a dedicated logger for execution tracking
execution_logger = logging.getLogger('execution_tracking')
execution_handler = logging.FileHandler('execution_tracking.log')
execution_formatter = logging.Formatter('%(asctime)s - EXECUTION_TRACKING - %(message)s')
execution_handler.setFormatter(execution_formatter)
execution_logger.addHandler(execution_handler)
execution_logger.setLevel(logging.INFO)

def log_execution_tracking_change(test_execution_id, test_suite_id, test_case_id, action_id, old_status, new_status, retry_count, timestamp, operation_type="UPDATE"):
    """
    Log execution tracking changes for debugging and audit purposes

    Args:
        test_execution_id (str): Test execution ID
        test_suite_id (str): Test suite ID
        test_case_id (str): Test case ID
        action_id (str): Action ID
        old_status (str): Previous status
        new_status (str): New status
        retry_count (int): Current retry count
        timestamp (str): Timestamp of change
        operation_type (str): Type of operation (UPDATE, INSERT)
    """
    log_message = f"OPERATION={operation_type} | test_execution_id={test_execution_id} | test_suite_id={test_suite_id} | test_case_id={test_case_id} | action_id={action_id} | old_status={old_status} | new_status={new_status} | retry_count={retry_count} | timestamp={timestamp}"

    # Log to both the main logger and dedicated execution logger
    logger.info(log_message)
    execution_logger.info(log_message)

def sync_execution_data_to_json(test_execution_id, reports_dir):
    """
    Synchronize database execution data to data.json file

    Args:
        test_execution_id (str): Test execution ID to sync
        reports_dir (str): Reports directory path

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        cursor = conn.cursor()

        # Get all execution data for this test execution ID
        cursor.execute('''
        SELECT suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
               status, retry_count, last_error, start_time, end_time, execution_result,
               test_case_id, test_execution_id
        FROM execution_tracking
        WHERE test_execution_id = ?
        ORDER BY test_idx, step_idx
        ''', (test_execution_id,))

        execution_data = cursor.fetchall()
        conn.close()

        if not execution_data:
            logger.warning(f"No execution data found for test_execution_id: {test_execution_id}")
            return False

        # Group data by test case
        test_cases = {}
        suite_id = None

        for row in execution_data:
            (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
             status, retry_count, last_error, start_time, end_time, execution_result,
             test_case_id, exec_id) = row

            if test_case_id not in test_cases:
                test_cases[test_case_id] = {
                    'name': filename.replace('.json', ''),
                    'test_case_id': test_case_id,
                    'filename': filename,
                    'status': 'unknown',
                    'steps': []
                }

            if step_idx is not None:
                # This is a step/action
                step_data = {
                    'action_id': action_id,
                    'action_type': action_type,
                    'status': status,
                    'retry_count': retry_count,
                    'description': action_params,
                    'timestamp': end_time,
                    'test_execution_id': exec_id
                }
                test_cases[test_case_id]['steps'].append(step_data)

        # Determine final status for each test case
        for test_case_id, test_case in test_cases.items():
            steps = test_case['steps']
            if not steps:
                test_case['status'] = 'unknown'
            else:
                # Group steps by action_id to get final status for each action
                action_statuses = {}
                for step in steps:
                    action_id = step['action_id']
                    if action_id not in action_statuses:
                        action_statuses[action_id] = []
                    action_statuses[action_id].append(step)

                # Get final status for each action (last retry)
                final_action_statuses = []
                for action_id, action_steps in action_statuses.items():
                    # Sort by retry_count to get the latest attempt
                    action_steps.sort(key=lambda x: x['retry_count'])
                    final_status = action_steps[-1]['status']
                    final_action_statuses.append(final_status)

                # Test case passes only if all actions pass
                if all(status == 'passed' for status in final_action_statuses):
                    test_case['status'] = 'passed'
                else:
                    test_case['status'] = 'failed'

        # Create the data.json structure
        json_data = {
            'name': f'Test Suite {suite_id}',
            'test_execution_id': test_execution_id,
            'test_suite_id': suite_id,
            'timestamp': datetime.now().isoformat(),
            'status': 'passed' if all(tc['status'] == 'passed' for tc in test_cases.values()) else 'failed',
            'testCases': list(test_cases.values())
        }

        # Find the execution directory
        execution_dirs = [d for d in os.listdir(reports_dir) if test_execution_id in d]
        if execution_dirs:
            execution_dir = os.path.join(reports_dir, execution_dirs[0])
            data_json_path = os.path.join(execution_dir, 'data.json')

            # Write the synchronized data
            with open(data_json_path, 'w') as f:
                json.dump(json_data, f, indent=2)

            logger.info(f"Successfully synchronized execution data to {data_json_path}")
            return True
        else:
            logger.warning(f"No execution directory found for test_execution_id: {test_execution_id}")
            return False

    except Exception as e:
        logger.error(f"Error synchronizing execution data to JSON: {str(e)}")
        return False

# Database file path - updated to use correct database files per migration guide
def get_db_path(db_type='test_suites'):
    """
    Centralized database path for iOS (ignores db_type).
    """
    from pathlib import Path
    override_root = os.environ.get('AUTOMATION_DB_ROOT')
    if override_root:
        base_dir = Path(override_root)
    else:
        base_dir = Path(__file__).resolve().parents[2]
    return str(base_dir / 'db-data' / 'ios.db')


# Use the function to get the path - default to test_suites database
DB_PATH = get_db_path('test_suites')

def ensure_db_directory(db_type='test_suites'):
    """Ensure the directory for the database exists"""
    # Check if we're in migration mode to prevent unauthorized database creation
    if os.environ.get('IOS_MIGRATION_MODE') == 'true':
        logger.info("iOS migration mode - skipping directory creation")
        return

    db_path = get_db_path(db_type)
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"Created directory for database: {db_dir}")

def init_db():
    """Initialize the database with required tables"""
    # Check if we're in migration mode to prevent unauthorized database creation
    if os.environ.get('IOS_MIGRATION_MODE') == 'true':
        logger.info("iOS migration mode - skipping database initialization")
        return

    ensure_db_directory()

    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create test_suites table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_suites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        name TEXT,
        status TEXT,
        passed INTEGER,
        failed INTEGER,
        skipped INTEGER,
        timestamp TEXT,
        report_dir TEXT,
        error TEXT
    )
    ''')

    # Create test_cases table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_cases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        name TEXT,
        status TEXT,
        duration TEXT,
        timestamp TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 0,
        error TEXT
    )
    ''')

    # Create test_steps table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_steps (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        name TEXT,
        action_type TEXT,
        action_id TEXT,
        status TEXT,
        duration TEXT,
        timestamp TEXT,
        screenshot_path TEXT,
        error TEXT,
        enabled INTEGER DEFAULT 1
    )
    ''')

    # Create test_case_json_backups table for JSON editor functionality
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_case_json_backups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        test_case_filename TEXT NOT NULL,
        test_case_id TEXT,
        json_data BLOB NOT NULL,
        backup_timestamp TEXT NOT NULL,
        session_id TEXT,
        created_by TEXT DEFAULT 'json_editor'
    )
    ''')

    # Create screenshots table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS screenshots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        path TEXT,
        timestamp TEXT,
        action_id TEXT,
        custom_screenshot_name TEXT,
        custom_screenshot_filename TEXT,
        custom_screenshot_path TEXT
    )
    ''')

    # Create environment_variables table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS environment_variables (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create execution_tracking table for detailed execution status
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_tracking (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        action_type TEXT,
        action_params TEXT,
        action_id TEXT,
        status TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 0,
        last_error TEXT,
        start_time TEXT,
        end_time TEXT,
        in_progress BOOLEAN DEFAULT 0,
        execution_result TEXT,
        test_case_id TEXT,
        test_execution_id TEXT
    )
    ''')

    # Add execution_result column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN execution_result TEXT')
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Add test_case_id column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN test_case_id TEXT')
        logger.info("Added test_case_id column to execution_tracking table")
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Add test_execution_id column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN test_execution_id TEXT')
        logger.info("Added test_execution_id column to execution_tracking table")
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Create execution_reports table with standardized schema
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_reports (
        report_id TEXT PRIMARY KEY,
        test_execution_id TEXT,
        suite_id TEXT,
        test_case_id TEXT,
        platform TEXT,
        status TEXT,
        start_time TIMESTAMP,
        end_time TIMESTAMP,
        duration INTEGER,
        error_message TEXT,
        report_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Add indexes for performance
    try:
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_suite_id ON execution_reports(suite_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_case_id ON execution_reports(test_case_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_status ON execution_reports(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_execution_id ON execution_reports(test_execution_id)')
    except sqlite3.OperationalError:
        # Indexes already exist
        pass

    # Create execution_settings table for test suite execution preferences
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Insert default execution settings if they don't exist
    cursor.execute('''
    INSERT OR IGNORE INTO execution_settings (setting_name, setting_value, description)
    VALUES
        ('record_execution_default', 'NO', 'Default setting for recording test suite execution'),
        ('retry_failed_tests_default', '0', 'Default number of retries for failed test cases'),
        ('capture_step_screenshots', 'true', 'Capture and store screenshots for every step in execution tracking (default true)')
    ''')

    # Create locators_repository table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS locators_repository (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        test_case_name TEXT NOT NULL,
        test_case_id TEXT,
        action_id TEXT NOT NULL,
        locator_type TEXT NOT NULL,
        locator_value TEXT NOT NULL,
        platform TEXT NOT NULL,
        created_date TEXT DEFAULT CURRENT_TIMESTAMP,
        last_used_date TEXT DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(test_case_name, action_id, locator_type, locator_value)
    )
    ''')

    conn.commit()
    conn.close()

    # Update schemas for new features
    update_test_steps_schema()
    update_screenshots_schema()
    update_execution_tracking_schema()
    update_execution_reports_schema()

    logger.info("Database initialized successfully")



def update_screenshots_schema():
    """
    Update the screenshots table schema to include custom screenshot fields

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING SCREENSHOTS TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if custom screenshot columns exist
        cursor.execute("PRAGMA table_info(screenshots)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add custom screenshot columns if they don't exist
        if 'custom_screenshot_name' not in columns:
            logger.info("Adding custom_screenshot_name column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_name TEXT")

        if 'custom_screenshot_filename' not in columns:
            logger.info("Adding custom_screenshot_filename column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_filename TEXT")

        if 'custom_screenshot_path' not in columns:
            logger.info("Adding custom_screenshot_path column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_path TEXT")

        # Add BLOB columns for database-only screenshot storage
        if 'screenshot_blob' not in columns:
            logger.info("Adding screenshot_blob column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_blob BLOB")

        if 'screenshot_thumb_blob' not in columns:
            logger.info("Adding screenshot_thumb_blob column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_thumb_blob BLOB")

        if 'screenshot_mime' not in columns:
            logger.info("Adding screenshot_mime column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")

        if 'test_execution_id' not in columns:
            logger.info("Adding test_execution_id column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN test_execution_id TEXT")

        if 'test_case_id' not in columns:
            logger.info("Adding test_case_id column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN test_case_id TEXT")

        if 'original_size' not in columns:
            logger.info("Adding original_size column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN original_size INTEGER")

        if 'compressed_size' not in columns:
            logger.info("Adding compressed_size column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN compressed_size INTEGER")

        conn.commit()
        conn.close()

        logger.info("Screenshots table schema updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating screenshots table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_test_steps_schema():
    """
    Update the test_steps table schema to include enabled column for step enable/disable functionality

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING TEST_STEPS TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if enabled column exists
        cursor.execute("PRAGMA table_info(test_steps)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add enabled column if it doesn't exist (default to 1 for enabled)
        if 'enabled' not in columns:
            logger.info("Adding enabled column to test_steps table")
            cursor.execute("ALTER TABLE test_steps ADD COLUMN enabled INTEGER DEFAULT 1")

        conn.commit()
        conn.close()

        logger.info("Test_steps table schema updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating test_steps table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_execution_tracking_schema():
    """
    Update the execution_tracking table schema to include step_idx, action_type, action_params, and action_id columns

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING EXECUTION_TRACKING TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if execution_tracking table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
        if cursor.fetchone() is None:
            logger.info("execution_tracking table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS execution_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                filename TEXT,
                action_type TEXT,
                action_params TEXT,
                action_id TEXT,
                status TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                last_error TEXT,
                start_time TEXT,
                end_time TEXT,
                in_progress BOOLEAN DEFAULT 0,
                execution_result TEXT
            )
            ''')

            # Add execution_result column if it doesn't exist (for existing databases)
            try:
                cursor.execute('ALTER TABLE execution_tracking ADD COLUMN execution_result TEXT')
            except sqlite3.OperationalError:
                # Column already exists
                pass

            logger.info("execution_tracking table created successfully")
        else:
            # Check if step_idx column exists
            try:
                cursor.execute("SELECT step_idx FROM execution_tracking LIMIT 1")
                logger.info("step_idx column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding step_idx column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN step_idx INTEGER")
                logger.info("step_idx column added successfully")

            # Check if action_type column exists
            try:
                cursor.execute("SELECT action_type FROM execution_tracking LIMIT 1")
                logger.info("action_type column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_type column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_type TEXT")
                logger.info("action_type column added successfully")

            # Check if action_params column exists
            try:
                cursor.execute("SELECT action_params FROM execution_tracking LIMIT 1")
                logger.info("action_params column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_params column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_params TEXT")
                logger.info("action_params column added successfully")

            # Check if action_id column exists
            try:
                cursor.execute("SELECT action_id FROM execution_tracking LIMIT 1")
                logger.info("action_id column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_id column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_id TEXT")
                logger.info("action_id column added successfully")

            # Add screenshot-related columns if they don't exist
            try:
                cursor.execute("SELECT screenshot_blob FROM execution_tracking LIMIT 1")
                logger.info("screenshot_blob column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_blob column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_blob BLOB")
                logger.info("screenshot_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_thumb_blob FROM execution_tracking LIMIT 1")
                logger.info("screenshot_thumb_blob column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_thumb_blob column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_thumb_blob BLOB")
                logger.info("screenshot_thumb_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_mime FROM execution_tracking LIMIT 1")
                logger.info("screenshot_mime column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_mime column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")
                logger.info("screenshot_mime column added successfully")

            try:
                cursor.execute("SELECT screenshot_filename FROM execution_tracking LIMIT 1")
                logger.info("screenshot_filename column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_filename column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_filename TEXT")
                logger.info("screenshot_filename column added successfully")

            # Ensure screenshots table has all required BLOB columns
            try:
                cursor.execute("SELECT screenshot_blob FROM screenshots LIMIT 1")
                logger.info("screenshot_blob column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_blob column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_blob BLOB")
                logger.info("screenshot_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_thumb_blob FROM screenshots LIMIT 1")
                logger.info("screenshot_thumb_blob column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_thumb_blob column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_thumb_blob BLOB")
                logger.info("screenshot_thumb_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_mime FROM screenshots LIMIT 1")
                logger.info("screenshot_mime column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_mime column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")
                logger.info("screenshot_mime column added successfully")

            try:
                cursor.execute("SELECT test_execution_id FROM screenshots LIMIT 1")
                logger.info("test_execution_id column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding test_execution_id column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN test_execution_id TEXT")
                logger.info("test_execution_id column added successfully")

            try:
                cursor.execute("SELECT test_case_id FROM screenshots LIMIT 1")
                logger.info("test_case_id column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding test_case_id column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN test_case_id TEXT")
                logger.info("test_case_id column added successfully")

            try:
                cursor.execute("SELECT original_size FROM screenshots LIMIT 1")
                logger.info("original_size column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding original_size column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN original_size INTEGER")
                logger.info("original_size column added successfully")

            try:
                cursor.execute("SELECT compressed_size FROM screenshots LIMIT 1")
                logger.info("compressed_size column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding compressed_size column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN compressed_size INTEGER")
                logger.info("compressed_size column added successfully")

        # Commit and close
        conn.commit()
        conn.close()

        logger.info("Successfully updated execution_tracking table schema")
        return True
    except Exception as e:
        logger.error(f"Error updating execution_tracking table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_execution_reports_schema():
    """Update execution_reports table schema to new standardized format"""
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if execution_reports table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_reports'")
        if cursor.fetchone() is None:
            logger.info("execution_reports table does not exist, will be created by init_db")
            conn.close()
            return

        # Check existing columns
        cursor.execute("PRAGMA table_info(execution_reports)")
        columns = [col[1] for col in cursor.fetchall()]

        # If we have the old schema (execution_id, data_json), migrate to new schema
        if 'execution_id' in columns and 'report_id' not in columns:
            logger.info("Migrating execution_reports table to new schema...")

            # Create new table with correct schema
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS execution_reports_new (
                report_id TEXT PRIMARY KEY,
                test_execution_id TEXT,
                suite_id TEXT,
                test_case_id TEXT,
                platform TEXT,
                status TEXT,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration INTEGER,
                error_message TEXT,
                report_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Migrate existing data
            cursor.execute('''
            INSERT INTO execution_reports_new
            (report_id, test_execution_id, suite_id, status, report_data, created_at)
            SELECT
                execution_id || '_' || datetime('now'),
                execution_id,
                suite_id,
                status,
                CASE
                    WHEN data_json IS NOT NULL THEN data_json
                    ELSE NULL
                END,
                created_at
            FROM execution_reports
            ''')

            # Drop old table and rename new one
            cursor.execute('DROP TABLE execution_reports')
            cursor.execute('ALTER TABLE execution_reports_new RENAME TO execution_reports')

            # Add indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_suite_id ON execution_reports(suite_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_case_id ON execution_reports(test_case_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_status ON execution_reports(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_execution_id ON execution_reports(test_execution_id)')

            logger.info("Successfully migrated execution_reports table to new schema")

        conn.commit()
        conn.close()
        logger.info("execution_reports schema updated successfully")

    except Exception as e:
        logger.error(f"Error updating execution_reports schema: {e}")
        import traceback
        traceback.print_exc()


def clear_test_tables():
    """
    Clear the test_suites and test_cases tables instead of dropping them

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING TEST TABLES (test_suites, test_cases) ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if tables exist before clearing
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_suites'")
        test_suites_exists = cursor.fetchone() is not None

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
        test_cases_exists = cursor.fetchone() is not None

        # Clear tables if they exist
        if test_suites_exists:
            logger.info("Clearing test_suites table...")
            cursor.execute("DELETE FROM test_suites")
            suites_cleared = cursor.rowcount
            logger.info(f"test_suites table cleared successfully, removed {suites_cleared} rows")
        else:
            logger.info("test_suites table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                name TEXT,
                status TEXT,
                passed INTEGER,
                failed INTEGER,
                skipped INTEGER,
                timestamp TEXT,
                report_dir TEXT,
                error TEXT
            )
            ''')
            logger.info("test_suites table created successfully")

        if test_cases_exists:
            logger.info("Clearing test_cases table...")
            cursor.execute("DELETE FROM test_cases")
            cases_cleared = cursor.rowcount
            logger.info(f"test_cases table cleared successfully, removed {cases_cleared} rows")
        else:
            logger.info("test_cases table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                name TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                error TEXT
            )
            ''')
            logger.info("test_cases table created successfully")

        # Commit and close
        conn.commit()
        conn.close()

        logger.info("Successfully cleared test tables")
        return True
    except Exception as e:
        logger.error(f"Error clearing test tables: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def track_test_execution(suite_id, test_idx, filename, status, retry_count=0, max_retries=0, error=None, in_progress=False, step_idx=None, action_type=None, action_params=None, action_id=None, execution_result=None, test_case_id=None, test_execution_id=None):
    """
    Track test execution status in the database

    NOTE: Only inserts records with 'passed' or 'failed' status to avoid cluttering
    the database with intermediate 'running' or 'in_progress' states.

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        filename (str): Test case filename
        status (str): Test execution status (started, running, passed, failed, retrying)
        retry_count (int): Current retry count
        max_retries (int): Maximum number of retries
        error (str): Error message if any
        in_progress (bool): Whether the test is currently in progress
        step_idx (int, optional): Step index within the test case
        action_type (str, optional): Type of action being executed
        action_params (dict, optional): Parameters for the action

    Returns:
        bool: Success status
    """
    try:
        # ENHANCED DEBUG: Log all calls to track_test_execution
        logger.info(f"========== TRACK_TEST_EXECUTION CALLED ==========")
        logger.info(f"Parameters received:")
        logger.info(f"  suite_id: {suite_id}")
        logger.info(f"  test_idx: {test_idx}")
        logger.info(f"  filename: {filename}")
        logger.info(f"  status: {status}")
        logger.info(f"  test_case_id: {test_case_id}")
        logger.info(f"  in_progress: {in_progress}")
        logger.info(f"  step_idx: {step_idx}")
        logger.info(f"  action_type: {action_type}")

        # Persist every status transition so the execution timeline is complete in the DB
        if not status:
            status = 'unknown'

        logger.info(f"========== TRACKING TEST EXECUTION ==========")
        logger.info(f"DEBUG: RECEIVED test_idx: {test_idx} (type: {type(test_idx)})")
        logger.info(f"DEBUG: suite_id: {suite_id}, test_idx: {test_idx}, step_idx: {step_idx}")
        logger.info(f"DEBUG: filename: {filename}, action_type: {action_type}")
        logger.info(f"DEBUG: status: {status}, retry: {retry_count}/{max_retries}, in_progress: {in_progress}")
        logger.info(f"DEBUG: STATUS FILTER PASSED - inserting record with status: {status}")

        # WRITE-TIME SAFEGUARDS: normalize and enforce IDs before any DB access
        # 1) Prevent accidentally using execution_id as suite_id and repair if possible
        try:
            if suite_id and isinstance(suite_id, str) and suite_id.startswith('exec_'):
                # If test_execution_id is missing, recover it from suite_id
                if not test_execution_id:
                    test_execution_id = suite_id
                    logger.warning(f"Recovered test_execution_id from suite_id value: {test_execution_id}")
                # Try to find the correct suite_id from filename
                fixed_suite_id = None
                try:
                    if filename:
                        fixed_suite_id = find_suite_id_for_test_case(filename)
                except Exception as _e:
                    logger.warning(f"Failed to resolve suite_id from filename '{filename}': {_e}")
                if fixed_suite_id and not fixed_suite_id.startswith('exec_'):
                    logger.warning(f"Correcting suite_id (was execution_id): {suite_id} -> {fixed_suite_id}")
                    suite_id = fixed_suite_id
        except Exception as guard_err:
            logger.warning(f"Suite/Test execution ID guard encountered an error: {guard_err}")

        # 2) Align/repair test_case_id using suite mapping when available
        try:
            if suite_id and filename:
                mapping = get_test_case_id_mapping_for_suite(suite_id)
                if mapping:
                    clean_fn = os.path.basename(filename)
                    if not clean_fn.endswith('.json'):
                        clean_fn += '.json'
                    base_no_ext = os.path.splitext(clean_fn)[0]
                    # strip trailing timestamp patterns like _YYYYMMDDHHMMSS or -YYYYMMDD
                    stripped = re.sub(r'([_-][0-9]{8,})+$', '', base_no_ext)
                    candidates = [
                        filename,
                        clean_fn,
                        stripped + '.json',
                        clean_fn.replace('_', ' '),
                        clean_fn.replace(' ', '_')
                    ]
                    mapped_id = None
                    for key in candidates:
                        if key in mapping:
                            mapped_id = mapping[key]
                            break
                    if mapped_id and (not test_case_id or test_case_id != mapped_id):
                        logger.warning(f"Correcting test_case_id based on suite mapping: {test_case_id} -> {mapped_id} for filename '{filename}'")
                        test_case_id = mapped_id
        except Exception as map_err:
            logger.warning(f"Failed to align test_case_id from mapping: {map_err}")

        # ENHANCED VALIDATION: Handle both suite and individual test case execution
        if not suite_id or suite_id.strip() == '':
            logger.warning(f"SKIPPING database insert - missing or empty suite_id: '{suite_id}'")
            return False

        # For individual test case execution, try to find test_case_id from filename
        if not test_case_id or test_case_id.strip() == '':
            logger.warning(f"test_case_id missing, attempting to find from filename: {filename}")

            if filename and filename != 'unknown':
                # Try to find test_case_id from database using filename
                try:
                    from utils.database import find_test_case_id_for_filename
                    found_test_case_id = find_test_case_id_for_filename(filename)
                    if found_test_case_id:
                        test_case_id = found_test_case_id
                        logger.info(f"Found test_case_id from filename lookup: {filename} -> {test_case_id}")
                    else:
                        logger.warning(f"Could not find test_case_id for filename: {filename}")
                except Exception as e:
                    logger.error(f"Error looking up test_case_id for filename {filename}: {e}")

            # If still no test_case_id, generate a fallback
            if not test_case_id or test_case_id.strip() == '':
                import uuid
                test_case_id = f"exec_{uuid.uuid4().hex[:8]}"
                logger.warning(f"Generated fallback test_case_id: {test_case_id}")

        logger.info(f"DEBUG: VALIDATION PASSED - suite_id: {suite_id}, test_case_id: {test_case_id}")

        # Get stack trace to see where this function is being called from
        import traceback
        stack_trace = traceback.format_stack()
        logger.info(f"DEBUG: Called from: {stack_trace[-2]}")

        # Log error if present
        if error:
            logger.info(f"DEBUG: error: {error[:100]}..." if len(str(error)) > 100 else f"DEBUG: error: {error}")

        # Validate parameters
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        # Convert test_idx to int if it's not already
        try:
            # Make sure test_idx is an integer and not None
            if test_idx is None:
                logger.warning("DEBUG: test_idx is None, using 0 instead")
                test_idx = 0
            else:
                # Force conversion to int to ensure we're using the correct type
                test_idx = int(test_idx)
                logger.info(f"DEBUG: Converted test_idx to int: {test_idx}")
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        # Convert step_idx to int if it's not None and not already an int
        if step_idx is not None:
            try:
                step_idx = int(step_idx)
            except (ValueError, TypeError):
                logger.warning(f"DEBUG: Invalid step_idx value: {step_idx}, using None instead")
                step_idx = None

        # Convert action_params to JSON string if it's a dict
        action_params_str = None
        if action_params:
            if isinstance(action_params, dict):
                action_params_str = json.dumps(action_params)
            else:
                action_params_str = str(action_params)
            logger.info(f"DEBUG: action_params: {action_params_str[:100]}..." if len(str(action_params_str)) > 100 else f"DEBUG: action_params: {action_params_str}")

        # Convert execution_result to JSON string if it's a dict
        execution_result_str = None
        if execution_result:
            if isinstance(execution_result, dict):
                execution_result_str = json.dumps(execution_result)
            else:
                execution_result_str = str(execution_result)
            logger.info(f"DEBUG: execution_result: {execution_result_str[:100]}..." if len(str(execution_result_str)) > 100 else f"DEBUG: execution_result: {execution_result_str}")

        conn = sqlite3.connect(get_db_path('execution_tracker'))
        cursor = conn.cursor()

        # Check if there's an existing entry - prioritize unique ID matching for retries
        existing = None
        existing_id = None

        # For retry scenarios, check by test_execution_id, test_case_id, and action_id first
        if test_execution_id and test_case_id and action_id:
            query = '''SELECT id, retry_count, status FROM execution_tracking
                      WHERE test_execution_id = ? AND test_case_id = ? AND action_id = ?'''
            logger.info(f"DEBUG: Checking for retry entry with unique IDs: test_execution_id={test_execution_id}, test_case_id={test_case_id}, action_id={action_id}")
            cursor.execute(query, (test_execution_id, test_case_id, action_id))
            existing = cursor.fetchone()
            if existing:
                existing_id, current_retry_count, current_status = existing
                logger.info(f"DEBUG: Found existing entry for retry - ID: {existing_id}, current retry: {current_retry_count}, current status: {current_status}")
                # Increment retry count for retries
                retry_count = current_retry_count + 1
                logger.info(f"DEBUG: Incremented retry count to: {retry_count}")

        # If no unique ID match found, fall back to traditional matching
        if not existing:
            if step_idx is not None:
                query = 'SELECT id, retry_count, status FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ?'
                logger.info(f"DEBUG: Checking for existing step entry with query: {query} and params: ({suite_id}, {test_idx}, {step_idx}, {filename})")
                cursor.execute(query, (suite_id, test_idx, step_idx, filename))
            else:
                query = 'SELECT id, retry_count, status FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND filename = ? AND step_idx IS NULL'
                logger.info(f"DEBUG: Checking for existing test case entry with query: {query} and params: ({suite_id}, {test_idx}, {filename})")
                cursor.execute(query, (suite_id, test_idx, filename))
            existing = cursor.fetchone()
            if existing:
                existing_id, current_retry_count, current_status = existing
                logger.info(f"DEBUG: Found existing entry via traditional matching - ID: {existing_id}")

        logger.info(f"DEBUG: Existing entry found: {existing is not None}")

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if existing:
            # Log the status change for comprehensive tracking
            old_status = current_status if 'current_status' in locals() else 'unknown'
            log_execution_tracking_change(
                test_execution_id=test_execution_id,
                test_suite_id=suite_id,
                test_case_id=test_case_id,
                action_id=action_id,
                old_status=old_status,
                new_status=status,
                retry_count=retry_count,
                timestamp=timestamp,
                operation_type="UPDATE"
            )

            # Update existing entry
            cursor.execute('''
            UPDATE execution_tracking
            SET status = ?, retry_count = ?, max_retries = ?, last_error = ?,
                end_time = ?, in_progress = ?, action_type = ?, action_params = ?, action_id = ?, execution_result = ?,
                test_case_id = ?, test_execution_id = ?
            WHERE id = ?
            ''', (
                status, retry_count, max_retries, error or '',
                timestamp, 1 if in_progress else 0, action_type, action_params_str, action_id, execution_result_str,
                test_case_id, test_execution_id, existing_id
            ))
            logger.info(f"Updated execution tracking for test {filename} (idx: {test_idx}, step: {step_idx}): status={status}, retry={retry_count}/{max_retries}")
        else:
            # Log the new entry creation
            log_execution_tracking_change(
                test_execution_id=test_execution_id,
                test_suite_id=suite_id,
                test_case_id=test_case_id,
                action_id=action_id,
                old_status="none",
                new_status=status,
                retry_count=retry_count,
                timestamp=timestamp,
                operation_type="INSERT"
            )

            # Insert new entry
            cursor.execute('''
            INSERT INTO execution_tracking
            (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id, status, retry_count, max_retries, last_error, start_time, end_time, in_progress, execution_result, test_case_id, test_execution_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id, test_idx, step_idx, filename, action_type, action_params_str, action_id, status, retry_count, max_retries, error or '',
                timestamp, timestamp, 1 if in_progress else 0, execution_result_str, test_case_id, test_execution_id
            ))
            logger.info(f"Created execution tracking for test {filename} (idx: {test_idx}, step: {step_idx}): status={status}, retry={retry_count}/{max_retries}")

        # Save step information to test_steps table if step_idx is provided
        if step_idx is not None and action_type is not None:
            try:
                # Extract step name from action_params if available
                step_name = f"Step {step_idx}: {action_type}"
                if action_params and isinstance(action_params, dict):
                    if 'description' in action_params:
                        step_name = action_params['description']
                    elif 'name' in action_params:
                        step_name = action_params['name']

                # Use provided action_id or extract from action_params if available
                if action_id is None:
                    if action_params and isinstance(action_params, dict) and 'action_id' in action_params:
                        action_id = action_params['action_id']
                        logger.info(f"Extracted action_id from action_params: {action_id}")

                # Check if step already exists
                cursor.execute(
                    'SELECT id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
                    (suite_id, test_idx, step_idx)
                )
                existing_step = cursor.fetchone()

                if existing_step:
                    # Update existing step
                    cursor.execute('''
                    UPDATE test_steps
                    SET name = ?, action_type = ?, status = ?, timestamp = ?, error = ?, action_id = ?
                    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                    ''', (
                        step_name, action_type, status, timestamp, error or '', action_id,
                        suite_id, test_idx, step_idx
                    ))
                    logger.info(f"Updated step information in test_steps table: {step_name}")
                else:
                    # Insert new step
                    cursor.execute('''
                    INSERT INTO test_steps
                    (suite_id, test_idx, step_idx, name, action_type, status, timestamp, error, action_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        suite_id, test_idx, step_idx, step_name, action_type, status, timestamp, error or '', action_id
                    ))
                    logger.info(f"Inserted step information into test_steps table: {step_name}")
            except Exception as step_error:
                logger.error(f"Error saving step information to test_steps table: {str(step_error)}")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error tracking test execution: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def find_suite_id_for_test_case(filename):
    """
    Find the suite_id for a given test case filename by looking up which test suite contains it

    Args:
        filename (str): Test case filename (e.g., 'health2.json')

    Returns:
        str: The suite_id if found, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        # Clean filename - remove path and ensure .json extension
        clean_filename = os.path.basename(filename)
        if not clean_filename.endswith('.json'):
            clean_filename += '.json'

        logger.info(f"Looking for suite_id for test case: {clean_filename}")

        # First try: Look for test case in test_cases table
        cursor.execute('''
            SELECT suite_id, test_case_id, name
            FROM test_cases
            WHERE file_path LIKE ? OR name = ?
            AND status = 'active'
            LIMIT 1
        ''', (f'%{clean_filename}', clean_filename.replace('.json', '')))

        result = cursor.fetchone()
        if result:
            suite_id, test_case_id, name = result
            logger.info(f"Found test case {clean_filename} in suite {suite_id} with test_case_id {test_case_id}")
            conn.close()
            return suite_id

        # Second try: Look in test_suites table for JSON data containing the filename
        cursor.execute('SELECT suite_id, name, json_payload FROM test_suites WHERE status = "active"')
        suites = cursor.fetchall()

        for suite_id, suite_name, json_payload in suites:
            if json_payload:
                try:
                    suite_data = json.loads(json_payload)
                    test_cases = suite_data.get('test_cases', [])
                    if clean_filename in test_cases:
                        logger.info(f"Found test case {clean_filename} in suite {suite_id} ({suite_name})")
                        conn.close()
                        return suite_id
                except json.JSONDecodeError:
                    continue

        conn.close()
        logger.warning(f"Could not find suite_id for test case: {clean_filename}")
        return None

    except Exception as e:
        logger.error(f"Error finding suite_id for test case {filename}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def find_test_case_id_for_filename(filename):
    """
    Find the correct test_case_id for a given filename from the database

    Args:
        filename (str): Test case filename (e.g., 'health2.json')

    Returns:
        str: The test_case_id if found, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        # Clean filename - remove path and ensure .json extension
        clean_filename = os.path.basename(filename)
        if not clean_filename.endswith('.json'):
            clean_filename += '.json'

        logger.info(f"Looking for test_case_id for filename: {clean_filename}")

        # Look for test case in test_cases table
        cursor.execute('''
            SELECT test_case_id, suite_id, name
            FROM test_cases
            WHERE (file_path LIKE ? OR name = ?)
            AND status = 'active'
            LIMIT 1
        ''', (f'%{clean_filename}', clean_filename.replace('.json', '')))

        result = cursor.fetchone()
        if result:
            test_case_id, suite_id, name = result
            logger.info(f"Found test_case_id {test_case_id} for filename {clean_filename} in suite {suite_id}")
            conn.close()
            return test_case_id

        conn.close()
        logger.warning(f"Could not find test_case_id for filename: {clean_filename}")
        return None

    except Exception as e:
        logger.error(f"Error finding test_case_id for filename {filename}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_case_id_mapping_for_suite(suite_id):
    """
    Get a mapping of test case filenames to test_case_ids for a given test suite.

    This function implements the database lookup process:
    1. Extract test case filenames from test suite json_payload
    2. Map filenames to test case IDs from test_cases table
    3. Return mapping for use in execution tracking

    Args:
        suite_id (str): The test suite ID (e.g., 'vuYvvC')

    Returns:
        dict: Mapping of filename -> test_case_id (e.g., {'health2.json': 'CSUVQZ'})
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        logger.info(f"Getting test case ID mapping for suite: {suite_id}")

        # Step 1: Extract test case filenames from test suite json_payload
        cursor.execute('''
            SELECT json_payload, name
            FROM test_suites
            WHERE suite_id = ? AND status = 'active'
            LIMIT 1
        ''', (suite_id,))

        result = cursor.fetchone()
        if not result:
            logger.warning(f"Test suite {suite_id} not found")
            conn.close()
            return {}

        json_payload, suite_name = result
        if not json_payload:
            logger.warning(f"No json_payload found for suite {suite_id}")
            conn.close()
            return {}

        # Parse the json_payload to extract test_cases array
        try:
            suite_data = json.loads(json_payload)
            test_case_filenames = suite_data.get('test_cases', [])
            logger.info(f"Found {len(test_case_filenames)} test cases in suite {suite_name}: {test_case_filenames}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse json_payload for suite {suite_id}: {e}")
            conn.close()
            return {}

        # Step 2: Map filenames to test case IDs
        filename_to_id_mapping = {}

        for filename in test_case_filenames:
            # Clean filename for consistent matching
            clean_filename = os.path.basename(filename)
            if not clean_filename.endswith('.json'):
                clean_filename += '.json'

            # Query test_cases table to find matching test_case_id
            cursor.execute('''
                SELECT test_case_id, name, file_path
                FROM test_cases
                WHERE (file_path LIKE ? OR name = ?) AND status = 'active'
                LIMIT 1
            ''', (f'%{clean_filename}', clean_filename.replace('.json', '')))

            test_case_result = cursor.fetchone()
            if test_case_result:
                test_case_id, test_case_name, file_path = test_case_result
                filename_to_id_mapping[filename] = test_case_id
                logger.info(f"Mapped {filename} -> {test_case_id} ({test_case_name})")
            else:
                logger.warning(f"No test_case_id found for filename: {filename}")
                # Use filename as fallback to avoid None values
                filename_to_id_mapping[filename] = filename.replace('.json', '')

        conn.close()
        logger.info(f"Created test case ID mapping for suite {suite_id}: {filename_to_id_mapping}")
        return filename_to_id_mapping

    except Exception as e:
        logger.error(f"Error getting test case ID mapping for suite {suite_id}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return {}

def clear_execution_tracking():
    """
    DEPRECATED: This function is no longer used in database-first architecture.
    Execution tracking records persist indefinitely (30-day retention policy applies).

    Returns:
        bool: True (no-op for backward compatibility)
    """
    logger.warning("clear_execution_tracking() called but is deprecated in database-first architecture")
    return True

def create_test_execution_status_table() -> bool:
    """Ensure test_execution_status table exists (iOS)."""
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_execution_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id TEXT UNIQUE NOT NULL,
            suite_id TEXT,
            status TEXT NOT NULL,
            total_tests INTEGER DEFAULT 0,
            passed_tests INTEGER DEFAULT 0,
            failed_tests INTEGER DEFAULT 0,
            start_time TEXT,
            end_time TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        conn.commit()
        conn.close()
        logger.info("Test execution status table created/verified (iOS)")
        return True
    except Exception as e:
        logger.error(f"Error creating test_execution_status table (iOS): {e}")
        return False

def update_test_execution_status(execution_id, status, total_tests=None, passed_tests=None, failed_tests=None):
    """Update or insert execution status (iOS)."""
    try:
        create_test_execution_status_table()
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('SELECT id FROM test_execution_status WHERE execution_id = ?', (execution_id,))
        existing = cursor.fetchone()
        if existing:
            update_fields = ['status = ?', 'updated_at = ?']
            update_values = [status, timestamp]
            if total_tests is not None:
                update_fields.append('total_tests = ?')
                update_values.append(total_tests)
            if passed_tests is not None:
                update_fields.append('passed_tests = ?')
                update_values.append(passed_tests)
            if failed_tests is not None:
                update_fields.append('failed_tests = ?')
                update_values.append(failed_tests)
            if status in ['completed', 'failed']:
                update_fields.append('end_time = ?')
                update_values.append(timestamp)
            update_values.append(execution_id)
            query = f'UPDATE test_execution_status SET {", ".join(update_fields)} WHERE execution_id = ?'
            cursor.execute(query, update_values)
        else:
            cursor.execute('''
            INSERT INTO test_execution_status
            (execution_id, status, total_tests, passed_tests, failed_tests, start_time, end_time, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                execution_id, status,
                total_tests or 0, passed_tests or 0, failed_tests or 0,
                timestamp if status == 'running' else None,
                timestamp if status in ['completed', 'failed'] else None,
                timestamp, timestamp
            ))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error updating test execution status (iOS): {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False

def get_execution_tracking_by_id(execution_id):
    """
    Get all execution tracking records for a specific execution (DATABASE-FIRST)

    Args:
        execution_id (str): Unique execution identifier (test_execution_id)

    Returns:
        list: List of execution tracking records as dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM execution_tracking
            WHERE test_execution_id = ?
            ORDER BY test_idx, step_idx
        ''', (execution_id,))

        records = [dict(row) for row in cursor.fetchall()]
        conn.close()

        logger.info(f"Retrieved {len(records)} execution tracking records for execution {execution_id}")
        return records

    except Exception as e:
        logger.error(f"Error getting execution tracking by ID {execution_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return []

def get_screenshots_by_execution_id(execution_id):
    """
    Get all screenshots for a specific execution (DATABASE-FIRST)

    Args:
        execution_id (str): Unique execution identifier (test_execution_id)

    Returns:
        list: List of screenshot records as dictionaries (includes BLOBs)
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM screenshots
            WHERE test_execution_id = ?
            ORDER BY test_idx, step_idx
        ''', (execution_id,))

        records = [dict(row) for row in cursor.fetchall()]
        conn.close()

        logger.info(f"Retrieved {len(records)} screenshot records for execution {execution_id}")
        return records

    except Exception as e:
        logger.error(f"Error getting screenshots by execution ID {execution_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return []

def get_execution_summary(execution_id):
    """
    Get summary information for a specific execution (DATABASE-FIRST)

    Args:
        execution_id (str): Unique execution identifier (test_execution_id)

    Returns:
        dict: Execution summary with metadata and statistics
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                suite_id,
                MIN(start_time) as execution_start_time,
                MAX(end_time) as execution_end_time,
                COUNT(*) as total_steps,
                SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed_steps,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_steps,
                SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_steps,
                COUNT(DISTINCT test_case_id) as total_test_cases,
                COUNT(DISTINCT filename) as total_files
            FROM execution_tracking
            WHERE test_execution_id = ?
        ''', (execution_id,))

        row = cursor.fetchone()
        summary = dict(row) if row else {}

        # Get overall status
        if summary:
            cursor.execute('''
                SELECT status FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY id DESC LIMIT 1
            ''', (execution_id,))
            last_status = cursor.fetchone()
            summary['overall_status'] = last_status[0] if last_status else 'unknown'

        conn.close()

        logger.info(f"Retrieved execution summary for {execution_id}: {summary}")
        return summary

    except Exception as e:
        logger.error(f"Error getting execution summary for {execution_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return {}

def get_screenshots_for_suite(suite_id):
    """
    Get all screenshots for a specific test suite

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of screenshot dictionaries with filename, path, test_idx, step_idx, and action_id
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all screenshots for this suite
        cursor.execute(
            'SELECT * FROM screenshots WHERE suite_id = ?',
            (suite_id,)
        )
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        screenshots = []
        for row in rows:
            screenshot = {
                'filename': row['filename'],
                'path': row['path'],
                'test_idx': row['test_idx'],
                'step_idx': row['step_idx'],
                'action_id': row['action_id'],
                'timestamp': row['timestamp']
            }
            screenshots.append(screenshot)

        logger.info(f"Found {len(screenshots)} screenshots for suite {suite_id}")
        conn.close()
        return screenshots
    except Exception as e:
        logger.error(f"Error getting screenshots for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_test_steps_for_suite(suite_id):
    """
    Get all test steps for a specific test suite from the database

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of step dictionaries with all step information
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # First, get all execution tracking entries for this suite
        # This will give us all the steps that were executed
        cursor.execute(
            '''
            SELECT * FROM execution_tracking
            WHERE suite_id = ? AND in_progress = 0
            ORDER BY test_idx, step_idx
            ''',
            (suite_id,)
        )
        execution_rows = cursor.fetchall()

        # Convert execution rows to dictionaries
        steps = []
        for row in execution_rows:
            # Convert row to dict
            step = dict(row)

            # Parse action_params if it's a JSON string
            if step['action_params'] and isinstance(step['action_params'], str):
                try:
                    action_params = json.loads(step['action_params'])
                    # Add relevant fields from action_params
                    if isinstance(action_params, dict):
                        # Add description or message as step name if available
                        if 'description' in action_params:
                            step['name'] = action_params['description']
                        elif 'message' in action_params:
                            step['name'] = action_params['message']
                        else:
                            step['name'] = f"Step {step['step_idx']}: {step['action_type']}"
                except:
                    # If JSON parsing fails, use a default name
                    step['name'] = f"Step {step['step_idx']}: {step['action_type']}"
            else:
                # Default name if no action_params
                step['name'] = f"Step {step['step_idx']}: {step['action_type']}"

            # Now get the screenshot for this step if available
            cursor.execute(
                '''
                SELECT * FROM screenshots
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''',
                (suite_id, step['test_idx'], step['step_idx'])
            )
            screenshot_row = cursor.fetchone()

            if screenshot_row:
                screenshot = dict(screenshot_row)
                # Add screenshot information
                step['screenshot'] = screenshot['path']
                step['screenshot_filename'] = screenshot['filename']
                step['report_screenshot'] = screenshot['filename']
                step['resolved_screenshot'] = f"screenshots/{screenshot['filename']}"

                # Use action_id from screenshot if available
                if screenshot['action_id']:
                    step['action_id'] = screenshot['action_id']

            # If we have an action_id in the step, use it for screenshot paths
            if step['action_id']:
                # Standardize screenshot paths based on action_id
                step['screenshot_filename'] = f"{step['action_id']}.png"
                step['report_screenshot'] = f"{step['action_id']}.png"
                step['resolved_screenshot'] = f"screenshots/{step['action_id']}.png"

            # Add duration field for report compatibility
            step['duration'] = '0ms'  # We don't track duration currently

            steps.append(step)

        logger.info(f"Found {len(steps)} steps for suite {suite_id}")

        # If we didn't find any steps in execution_tracking, try the test_steps table as fallback
        if not steps:
            logger.info(f"No steps found in execution_tracking, trying test_steps table")
            cursor.execute(
                '''
                SELECT ts.*, s.filename as screenshot_filename, s.path as screenshot_path, s.action_id as screenshot_action_id
                FROM test_steps ts
                LEFT JOIN screenshots s ON ts.suite_id = s.suite_id AND ts.test_idx = s.test_idx AND ts.step_idx = s.step_idx
                WHERE ts.suite_id = ?
                ORDER BY ts.test_idx, ts.step_idx
                ''',
                (suite_id,)
            )
            rows = cursor.fetchall()

            # Convert rows to dictionaries
            for row in rows:
                step = dict(row)

                # Add screenshot information if available
                if step.get('screenshot_filename'):
                    step['screenshot'] = step['screenshot_path']
                    step['report_screenshot'] = step['screenshot_filename']
                    step['resolved_screenshot'] = f"screenshots/{step['screenshot_filename']}"

                # Use action_id from step or screenshot
                if step.get('action_id'):
                    # Use step's action_id
                    pass
                elif step.get('screenshot_action_id'):
                    # Use screenshot's action_id
                    step['action_id'] = step['screenshot_action_id']

                # If we have an action_id, standardize screenshot paths
                if step.get('action_id'):
                    step['screenshot_filename'] = f"{step['action_id']}.png"
                    step['report_screenshot'] = f"{step['action_id']}.png"
                    step['resolved_screenshot'] = f"screenshots/{step['action_id']}.png"

                steps.append(step)

            logger.info(f"Found {len(steps)} steps in test_steps table for suite {suite_id}")

        conn.close()
        return steps
    except Exception as e:
        logger.error(f"Error getting steps for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def resolve_execution_id_to_suite_id(execution_id):
    """
    Resolve a timestamp-based execution ID to the actual test suite UUID.
    Enhanced to handle retry scenarios and imported test sessions.
    Prioritizes exact test_execution_id match over fuzzy fallbacks.

    Args:
        execution_id (str): Execution ID (could be timestamp-based like 'testsuite_execution_20250627_181306',
                           short exec ID like 'exec_595837249263', or actual suite UUID)

    Returns:
        str: The actual test suite UUID, or the original execution_id if no mapping found
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        cursor = conn.cursor()

        # Strategy 1: Direct test_execution_id match (highest priority - exact match)
        cursor.execute('''
            SELECT DISTINCT suite_id, test_execution_id, action_type
            FROM execution_tracking
            WHERE test_execution_id = ?
            ORDER BY CASE WHEN action_type = 'retry_update' THEN 0 ELSE 1 END, id DESC
            LIMIT 1
        ''', (execution_id,))

        result = cursor.fetchone()
        if result:
            actual_suite_id = result[0]
            logger.info(f"Resolved execution ID {execution_id} to suite ID {actual_suite_id} via exact test_execution_id match")
            conn.close()
            return actual_suite_id

        # Strategy 2: Check if this is already a valid suite_id in the database
        cursor.execute('SELECT DISTINCT suite_id FROM execution_tracking WHERE suite_id = ? LIMIT 1', (execution_id,))
        if cursor.fetchone():
            conn.close()
            logger.info(f"Execution ID {execution_id} is already a valid suite_id")
            return execution_id

        # Strategy 3: Pattern matching for timestamp-based IDs (only if exact match fails)
        if 'testsuite_execution_' in execution_id:
            # Extract timestamp from execution_id (e.g., '20250627_181306' from 'testsuite_execution_20250627_181306')
            timestamp_part = execution_id.replace('testsuite_execution_', '')

            # Look for execution tracking entries where test_execution_id contains this timestamp
            cursor.execute('''
                SELECT DISTINCT suite_id, test_execution_id, action_type
                FROM execution_tracking
                WHERE test_execution_id LIKE ? OR test_execution_id LIKE ?
                ORDER BY CASE WHEN action_type = 'retry_update' THEN 0 ELSE 1 END, id DESC
                LIMIT 1
            ''', (f'%{timestamp_part}%', f'%{execution_id}%'))

            result = cursor.fetchone()
            if result:
                actual_suite_id = result[0]
                logger.info(f"Resolved execution ID {execution_id} to suite ID {actual_suite_id} via timestamp pattern")
                conn.close()
                return actual_suite_id

        # Strategy 4: Handle short exec_* identifiers by fuzzy LIKE match (conservative fallback)
        if execution_id.startswith('exec_'):
            short = execution_id.replace('exec_', '')
            # Only use fuzzy match if the short ID is sufficiently long to avoid false positives
            if len(short) >= 10:
                cursor.execute('''
                    SELECT DISTINCT suite_id
                    FROM execution_tracking
                    WHERE test_execution_id LIKE ?
                    ORDER BY id DESC
                    LIMIT 1
                ''', (f'%{short}%',))
                result = cursor.fetchone()
                if result:
                    actual_suite_id = result[0]
                    logger.info(f"Resolved short exec ID {execution_id} to suite ID {actual_suite_id} via conservative LIKE match")
                    conn.close()
                    return actual_suite_id

        # Strategy 5: Check if execution_id is used as suite_id directly (for imported sessions)
        cursor.execute('''
            SELECT COUNT(*) as count, suite_id
            FROM execution_tracking
            WHERE suite_id = ?
            GROUP BY suite_id
        ''', (execution_id,))

        result = cursor.fetchone()
        if result and result[0] > 0:
            logger.info(f"Execution ID {execution_id} is being used as suite_id directly")
            conn.close()
            return execution_id

        # If no mapping found, return the original execution_id
        logger.warning(f"Could not resolve execution ID {execution_id} to a suite ID using any strategy")
        conn.close()
        return execution_id

    except Exception as e:
        logger.error(f"Error resolving execution ID to suite ID: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return execution_id

def get_execution_tracking_for_suite(suite_id):
    """
    Get all execution tracking entries for a specific test suite from the database.
    Automatically resolves timestamp-based execution IDs to actual suite UUIDs.

    Args:
        suite_id (str): Test suite ID (can be timestamp-based execution ID or actual UUID)

    Returns:
        list: List of execution tracking dictionaries with all execution information
    """
    try:
        # Resolve the execution ID to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(suite_id)
        logger.info(f"Querying execution tracking for resolved suite_id: {actual_suite_id}")

        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all execution tracking entries for this suite with custom screenshot information
        # Order by end_time DESC, then by id DESC to get the most recent execution status for each step
        # The id DESC ensures that if end_time is the same, we get the latest inserted record
        cursor.execute(
            '''
            SELECT et.*, s.custom_screenshot_name, s.custom_screenshot_filename, s.custom_screenshot_path
            FROM execution_tracking et
            LEFT JOIN screenshots s ON et.suite_id = s.suite_id
                AND et.test_idx = s.test_idx
                AND et.step_idx = s.step_idx
            WHERE et.suite_id = ?
            ORDER BY et.test_execution_id DESC, et.test_idx, et.step_idx, et.end_time DESC, et.id DESC
            ''',
            (actual_suite_id,)
        )
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        execution_entries = []
        for row in rows:
            entry = dict(row)

            # Convert action_params from JSON string to dict if it exists
            if entry.get('action_params'):
                try:
                    entry['action_params'] = json.loads(entry['action_params'])
                except:
                    # If JSON parsing fails, keep it as a string
                    pass

            # Log the action_id for debugging
            if entry.get('action_id'):
                logger.info(f"Found action_id {entry['action_id']} for step {entry.get('step_idx')} in test {entry.get('test_idx')}")
            else:
                logger.warning(f"No action_id found for step {entry.get('step_idx')} in test {entry.get('test_idx')}")

            # If action_params contains action_id, use it if entry doesn't already have one
            if not entry.get('action_id') and entry.get('action_params') and isinstance(entry['action_params'], dict) and entry['action_params'].get('action_id'):
                entry['action_id'] = entry['action_params']['action_id']
                logger.info(f"Using action_id {entry['action_id']} from action_params for step {entry.get('step_idx')} in test {entry.get('test_idx')}")

            # Add timestamp for sorting
            entry['timestamp'] = entry.get('start_time') or entry.get('end_time') or datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Ensure status is properly mapped for report generation
            db_status = entry.get('status', 'unknown')
            if db_status in ['success', 'passed']:
                entry['status'] = 'passed'
            elif db_status in ['error', 'failed']:
                entry['status'] = 'failed'
            elif db_status == 'unknown' and entry.get('last_error') and entry.get('last_error').strip():
                # If status is unknown but there's an error, mark as failed
                entry['status'] = 'failed'
            elif db_status == 'unknown' and not entry.get('last_error'):
                # If status is unknown and no error, mark as passed
                entry['status'] = 'passed'

            logger.debug(f"Mapped status '{db_status}' to '{entry['status']}' for action_id {entry.get('action_id', 'N/A')}")

            execution_entries.append(entry)

        logger.info(f"Found {len(execution_entries)} execution tracking entries for suite {suite_id}")
        conn.close()
        return execution_entries
    except Exception as e:
        logger.error(f"Error getting execution tracking for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_action_id_for_step(suite_id, test_idx, step_idx):
    """
    Get the action_id for a specific step in a test case

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index

    Returns:
        str: Action ID if found, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        cursor = conn.cursor()

        # First try to find the action_id in the test_steps table
        cursor.execute(
            'SELECT action_id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in test_steps table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If not found in test_steps, try the screenshots table
        cursor.execute(
            'SELECT action_id FROM screenshots WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in screenshots table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If not found in screenshots, try the execution_tracking table
        cursor.execute(
            'SELECT action_id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in execution_tracking table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If we still don't have an action_id, return None
        logger.warning(f"No action_id found for step {step_idx} in test {test_idx}")
        conn.close()
        return None
    except Exception as e:
        logger.error(f"Error getting action_id for step: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_execution_status(suite_id, test_idx=None, filename=None, test_case_id=None):
    """
    Get test execution status from the database

    Args:
        suite_id (str): Test suite ID
        test_idx (int, optional): Test case index
        filename (str, optional): Test case filename

    Returns:
        dict or list: Test execution status(es)
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if test_idx is not None and filename is not None:
            # Get specific test execution - prioritize test_case_id if available
            if test_case_id:
                cursor.execute(
                    'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND test_case_id = ? ORDER BY retry_count DESC LIMIT 1',
                    (suite_id, test_idx, test_case_id)
                )
            else:
                cursor.execute(
                    'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND filename = ? ORDER BY retry_count DESC LIMIT 1',
                    (suite_id, test_idx, filename)
                )
            row = cursor.fetchone()
            conn.close()

            if row:
                return dict(row)
            return None
        else:
            # Get all test executions for the suite
            cursor.execute(
                'SELECT * FROM execution_tracking WHERE suite_id = ? ORDER BY test_idx',
                (suite_id,)
            )
            rows = cursor.fetchall()
            conn.close()

            return [dict(row) for row in rows]
    except Exception as e:
        logger.error(f"Error getting test execution status: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_execution_data(execution_id):
    """
    Get test execution data for a specific execution ID

    Args:
        execution_id (str): Execution ID (can be timestamp-based or actual suite UUID)

    Returns:
        list: List of execution data dictionaries containing filename, test_idx, test_case_id, etc.
    """
    try:
        # Resolve the execution ID to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(execution_id)
        logger.info(f"Getting test execution data for resolved suite_id: {actual_suite_id}")

        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all execution tracking entries for this suite
        cursor.execute('''
            SELECT DISTINCT filename, test_idx, test_case_id, suite_id
            FROM execution_tracking
            WHERE suite_id = ?
            ORDER BY test_idx
        ''', (actual_suite_id,))

        rows = cursor.fetchall()
        conn.close()

        if not rows:
            logger.warning(f"No execution data found for execution_id: {execution_id} (resolved to: {actual_suite_id})")
            return []

        # Convert to list of dictionaries
        execution_data = []
        for row in rows:
            execution_data.append({
                'filename': row['filename'],
                'test_idx': row['test_idx'],
                'test_case_id': row['test_case_id'],
                'suite_id': row['suite_id']
            })

        logger.info(f"Found {len(execution_data)} test cases for execution_id: {execution_id}")
        return execution_data

    except Exception as e:
        logger.error(f"Error getting test execution data: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def list_executions(page: int = 1, page_size: int = 20):
    """
    List executions from database (execution_reports table) with pagination (iOS).

    This function queries the execution_reports table to build the execution list.
    """
    try:
        import json
        from datetime import datetime

        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Calculate offset for pagination
        offset = (page - 1) * page_size

        # Query execution_reports table
        cursor.execute('''
            SELECT * FROM execution_reports
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ''', (page_size, offset))

        rows = cursor.fetchall()
        conn.close()

        executions = []
        for row in rows:
            row_dict = dict(row)

            # Get basic info
            execution_id = row_dict.get('test_execution_id') or row_dict.get('execution_id')
            if not execution_id:
                continue

            # Parse report data if available
            report_data = None
            if row_dict.get('report_data'):
                try:
                    report_data = json.loads(row_dict['report_data'])
                except:
                    pass
            elif row_dict.get('data_json'):
                try:
                    data_json = row_dict['data_json']
                    if isinstance(data_json, bytes):
                        report_data = json.loads(data_json.decode('utf-8'))
                    else:
                        report_data = json.loads(data_json)
                except:
                    pass

            # Extract metadata
            if report_data:
                suite_name = report_data.get('name', 'Unknown Suite')
                test_cases = report_data.get('test_cases', []) or report_data.get('testCases', [])
                summary = report_data.get('summary', {})

                total_tests = summary.get('total_tests', len(test_cases))
                passed_tests = summary.get('passed', sum(1 for tc in test_cases if tc.get('status') == 'passed'))
                failed_tests = summary.get('failed', sum(1 for tc in test_cases if tc.get('status') == 'failed'))
            else:
                suite_name = row_dict.get('suite_id', 'Unknown Suite')
                total_tests = 0
                passed_tests = 0
                failed_tests = 0

            # Determine overall status
            status = row_dict.get('status', 'unknown')
            if status == 'unknown':
                status = 'failed' if failed_tests > 0 else 'passed'

            # Format timestamp
            created_at = row_dict.get('created_at') or row_dict.get('start_time')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    timestamp_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    timestamp_str = created_at
            else:
                timestamp_str = 'Unknown'

            # Add to executions list
            executions.append({
                'execution_id': execution_id,
                'suite_name': suite_name,
                'started_at': row_dict.get('start_time') or row_dict.get('created_at'),
                'finished_at': row_dict.get('end_time') or row_dict.get('created_at'),
                'status': status,
                'total_tests': total_tests,
                'passed_count': passed_tests,
                'failed_count': failed_tests,
                'suite_id': row_dict.get('suite_id', execution_id),
                'platform': row_dict.get('platform', 'iOS'),
                'duration': row_dict.get('duration', 0),
                'error_message': row_dict.get('error_message'),
                'created_at': timestamp_str
            })

        return executions

    except Exception as e:
        logger.error(f"Error listing executions (iOS): {e}")
        logger.exception(e)
        return []


def count_executions():
    """
    Count total executions from database (execution_reports table) (iOS).

    This function queries the execution_reports table to count total executions.
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Count execution_reports
        cursor.execute('SELECT COUNT(*) FROM execution_reports')
        count = cursor.fetchone()[0]
        conn.close()

        logger.info(f"Found {count} total executions in database")
        return count

    except Exception as e:
        logger.error(f"Error counting executions (iOS): {e}")
        logger.exception(e)
        return 0


def update_data_json_with_retry_results(execution_id, suite_id):
    """
    Update the data.json file with the latest retry results from the database

    Args:
        execution_id (str): Test execution ID
        suite_id (str): Test suite ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"=== UPDATING DATA.JSON === Execution ID: {execution_id}, Suite ID: {suite_id}")

        # Find the data.json file for this execution
        reports_base_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'reports_ios')
        execution_dir = os.path.join(reports_base_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        if not os.path.exists(data_json_path):
            logger.warning(f"data.json not found at {data_json_path}")
            return False

        # Load the current data.json
        with open(data_json_path, 'r') as f:
            data = json.load(f)

        logger.info(f"Loaded data.json with {len(data.get('testCases', []))} test cases")

        # Get the latest execution data from database
        execution_data = get_execution_tracking_for_suite(suite_id)
        if not execution_data:
            logger.warning(f"No execution data found in database for suite_id: {suite_id}")
            return False

        # Group execution data by test case and action
        test_case_updates = {}
        for entry in execution_data:
            test_case_id = entry.get('test_case_id')
            filename = entry.get('filename', '')
            action_id = entry.get('action_id')
            action_type = entry.get('action_type')
            status = entry.get('status')
            retry_count = entry.get('retry_count', 0)

            if not test_case_id and filename:
                # Use filename as fallback identifier
                test_case_id = filename.replace('.json', '')

            if test_case_id not in test_case_updates:
                test_case_updates[test_case_id] = {
                    'filename': filename,
                    'actions': {},
                    'final_status': 'unknown'
                }

            if action_id and action_id not in test_case_updates[test_case_id]['actions']:
                test_case_updates[test_case_id]['actions'][action_id] = []

            if action_id:
                test_case_updates[test_case_id]['actions'][action_id].append({
                    'status': status,
                    'retry_count': retry_count,
                    'action_type': action_type,
                    'timestamp': entry.get('end_time')
                })

        # Determine final status for each test case
        for test_case_id, updates in test_case_updates.items():
            action_final_statuses = []
            for action_id, action_entries in updates['actions'].items():
                # Sort by retry_count to get the latest attempt
                action_entries.sort(key=lambda x: (x['retry_count'], x['timestamp'] or ''))
                if action_entries:
                    latest_entry = action_entries[-1]
                    action_final_statuses.append(latest_entry['status'])

            # Test case passes only if all actions pass
            if action_final_statuses:
                if all(status == 'passed' for status in action_final_statuses):
                    updates['final_status'] = 'passed'
                else:
                    updates['final_status'] = 'failed'

        # Update the data.json structure
        updated = False
        for test_case in data.get('testCases', []):
            test_case_id = test_case.get('id') or test_case.get('test_case_id')
            test_case_name = test_case.get('name', '')

            # Try to match by ID first, then by name
            matching_update = None
            if test_case_id and test_case_id in test_case_updates:
                matching_update = test_case_updates[test_case_id]
            else:
                # Try to match by filename/name
                for tc_id, tc_update in test_case_updates.items():
                    if tc_update['filename'] and test_case_name in tc_update['filename']:
                        matching_update = tc_update
                        break

            if matching_update and matching_update['final_status'] != 'unknown':
                old_status = test_case.get('status', 'unknown')
                new_status = matching_update['final_status']

                if old_status != new_status:
                    test_case['status'] = new_status
                    updated = True
                    logger.info(f"Updated test case '{test_case_name}' status: {old_status} -> {new_status}")

                    # Also update individual step statuses if available
                    if 'steps' in test_case and matching_update['actions']:
                        for step in test_case['steps']:
                            step_action_id = step.get('action_id')
                            if step_action_id and step_action_id in matching_update['actions']:
                                action_entries = matching_update['actions'][step_action_id]
                                if action_entries:
                                    latest_action = action_entries[-1]
                                    old_step_status = step.get('status', 'unknown')
                                    new_step_status = latest_action['status']
                                    if old_step_status != new_step_status:
                                        step['status'] = new_step_status
                                        step['retry_count'] = latest_action['retry_count']
                                        logger.info(f"Updated step '{step_action_id}' status: {old_step_status} -> {new_step_status}")

        # Update overall suite status
        if updated:
            test_case_statuses = [tc.get('status', 'unknown') for tc in data.get('testCases', [])]
            if all(status == 'passed' for status in test_case_statuses):
                data['status'] = 'passed'
            elif any(status == 'failed' for status in test_case_statuses):
                data['status'] = 'failed'

            # Add retry metadata
            data['has_retries'] = True
            data['last_updated'] = datetime.now().isoformat()

            # Create backup of original file
            backup_path = data_json_path + '.backup'
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(data_json_path, backup_path)
                logger.info(f"Created backup: {backup_path}")

            # Save updated data.json
            with open(data_json_path, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Successfully updated data.json with retry results")
            return True
        else:
            logger.info(f"No updates needed for data.json")
            return True

    except Exception as e:
        logger.error(f"Error updating data.json with retry results: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def get_final_test_case_status(suite_id, test_case_id=None, filename=None, test_idx=None):
    """
    Get the final status for a test case considering all retries

    Args:
        suite_id (str): Test suite ID
        test_case_id (str, optional): Test case ID
        filename (str, optional): Test case filename
        test_idx (int, optional): Test case index

    Returns:
        dict: Final status information including overall test case status
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Build query based on available identifiers
        # PRIORITIZE UUID-based lookups for reliability
        where_conditions = ['suite_id = ?']
        params = [suite_id]

        if test_case_id:
            # PREFERRED: Use UUID-based lookup
            where_conditions.append('test_case_id = ?')
            params.append(test_case_id)
            logger.debug(f"Using UUID-based lookup: test_case_id={test_case_id}")
        elif filename:
            # FALLBACK: Use filename-based lookup (less reliable)
            where_conditions.append('filename = ?')
            params.append(filename)
            logger.debug(f"Using filename-based lookup: filename={filename}")
        elif test_idx is not None:
            # FALLBACK: Use test_idx-based lookup (legacy)
            where_conditions.append('test_idx = ?')
            params.append(test_idx)
            logger.debug(f"Using test_idx-based lookup: test_idx={test_idx}")
        else:
            logger.warning("No test case identifier provided")
            return {'status': 'unknown', 'actions': {}, 'total_actions': 0}

        where_clause = ' AND '.join(where_conditions)

        # Get all actions for this test case, prioritizing retry_update entries
        # Order by action_id, then prioritize retry_update entries, then by end_time DESC, id DESC
        cursor.execute(f'''
            SELECT action_id, status, retry_count, end_time, action_type, id
            FROM execution_tracking
            WHERE {where_clause}
            ORDER BY action_id,
                     CASE WHEN action_type = 'retry_update' THEN 0 ELSE 1 END,
                     end_time DESC, id DESC
        ''', params)

        rows = cursor.fetchall()
        conn.close()

        if not rows:
            return {'status': 'unknown', 'actions': {}, 'total_actions': 0}

        # Group by action_id and get the latest status for each action, prioritizing retry_update
        action_statuses = {}
        for row in rows:
            action_id = row['action_id']
            action_type = row['action_type']
            retry_count = row['retry_count'] or 0

            if action_id not in action_statuses:
                # First entry for this action_id (most recent due to ORDER BY with retry_update priority)
                # Map database status to standard status values
                db_status = row['status']
                if db_status in ['success', 'passed']:
                    mapped_status = 'passed'
                elif db_status in ['error', 'failed']:
                    mapped_status = 'failed'
                elif db_status == 'running':
                    mapped_status = 'running'
                else:
                    mapped_status = 'unknown'

                action_statuses[action_id] = {
                    'status': mapped_status,
                    'retry_count': retry_count,
                    'end_time': row['end_time'],
                    'action_type': action_type
                }

                # Log retry information for debugging
                if retry_count > 0:
                    logger.debug(f"Action {action_id}: Using {action_type} status '{mapped_status}' (retry_count: {retry_count})")
                else:
                    logger.debug(f"Action {action_id}: Using {action_type} status '{mapped_status}' (no retries)")
            elif action_type == 'retry_update' and action_statuses[action_id]['action_type'] != 'retry_update':
                # Override with retry_update if we previously had a non-retry entry
                # This shouldn't happen with our improved ORDER BY, but keeping as safety net
                db_status = row['status']
                if db_status in ['success', 'passed']:
                    mapped_status = 'passed'
                elif db_status in ['error', 'failed']:
                    mapped_status = 'failed'
                elif db_status == 'running':
                    mapped_status = 'running'
                else:
                    mapped_status = 'unknown'

                logger.info(f"Action {action_id}: Overriding with retry_update status '{mapped_status}' (was: {action_statuses[action_id]['status']})")
                action_statuses[action_id] = {
                    'status': mapped_status,
                    'retry_count': retry_count,
                    'end_time': row['end_time'],
                    'action_type': action_type
                }

        # Determine overall test case status based on final action statuses
        final_statuses = [action['status'] for action in action_statuses.values()]

        # If all actions passed, test case passed
        if final_statuses and all(status == 'passed' for status in final_statuses):
            overall_status = 'passed'
        # If any action failed, test case failed
        elif any(status == 'failed' for status in final_statuses):
            overall_status = 'failed'
        # If any action is running, test case is running
        elif any(status == 'running' for status in final_statuses):
            overall_status = 'running'
        else:
            overall_status = 'unknown'

        return {
            'status': overall_status,
            'actions': action_statuses,
            'total_actions': len(action_statuses)
        }

    except Exception as e:
        logger.error(f"Error getting final test case status: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return {'status': 'unknown', 'actions': {}, 'total_actions': 0}

def reset_test_case_execution_tracking(suite_id, test_idx):
    """
    No-op under database-first architecture. We preserve prior executions.

    Historical runs are differentiated by test_execution_id, so clearing by
    suite_id/test_idx would erase history. This function now returns True
    without deleting records.
    """
    logger.info("DATABASE-FIRST: reset_test_case_execution_tracking is a no-op (preserving history)")
    return True

def has_test_case_failures(suite_id, test_idx, force_return_true=False):
    """
    Check if there are any failures in the current test case.
    This is the SINGLE SOURCE OF TRUTH for determining if a test case has failures.
    All code should use this function instead of tracking failures in memory.

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        force_return_true (bool): If True, always return True regardless of actual failures.

    Returns:
        tuple: (has_failures, failure_details)
            - has_failures (bool): True if there are failures, False otherwise
            - failure_details (str): Error message if there are failures, None otherwise
    """
    try:
        # If force_return_true is set, return True immediately without checking the database
        if force_return_true:
            logger.info(f"DEBUG: force_return_true is set, returning True without checking database")
            return True, "Force return true flag is set"

        # Log detailed information for debugging
        logger.info(f"DEBUG: Checking for failures in test case {test_idx} for suite {suite_id}")

        # Validate parameters
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        if test_idx is None:
            logger.warning("DEBUG: test_idx is None, using 0 instead")
            test_idx = 0

        # Convert test_idx to int if it's not already
        try:
            test_idx = int(test_idx)
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        conn = sqlite3.connect(get_db_path('execution_tracker'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the current test run start time
        # First, check if there's a running entry for this test case
        query_start_time = '''
        SELECT MIN(created_at) as start_time
        FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND in_progress = 1
        '''
        cursor.execute(query_start_time, (suite_id, test_idx))
        start_time_row = cursor.fetchone()

        if start_time_row and start_time_row['start_time']:
            # We found a running entry, use its timestamp as the start time
            start_time = start_time_row['start_time']
            logger.info(f"DEBUG: Found running entry with start time: {start_time}")
        else:
            # No running entry found, check for the earliest entry for this test case in this run
            query_earliest = '''
            SELECT MIN(created_at) as start_time
            FROM execution_tracking
            WHERE suite_id = ? AND test_idx = ?
            '''
            cursor.execute(query_earliest, (suite_id, test_idx))
            earliest_row = cursor.fetchone()

            if earliest_row and earliest_row['start_time']:
                start_time = earliest_row['start_time']
                logger.info(f"DEBUG: Using earliest entry as start time: {start_time}")
            else:
                # No entries found at all, use a recent timestamp (last 10 minutes)
                from datetime import datetime, timedelta
                start_time = (datetime.now() - timedelta(minutes=10)).strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"DEBUG: No entries found, using default start time: {start_time}")

        # Check for any failed steps in the current test case, only considering entries after the start time
        query1 = '''
        SELECT * FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND status = "failed" AND created_at >= ?
        '''
        logger.info(f"DEBUG: Executing query: {query1} with params: ({suite_id}, {test_idx}, {start_time})")
        cursor.execute(query1, (suite_id, test_idx, start_time))
        failed_rows = cursor.fetchall()

        # Log the raw failed rows for debugging
        logger.info(f"DEBUG: Found {len(failed_rows)} failed rows in current run (after {start_time})")
        for i, row in enumerate(failed_rows[:3]):  # Only log the first 3 rows to avoid flooding the logs
            row_dict = dict(row)
            logger.info(f"DEBUG: Failed row {i+1}: {row_dict}")

        if len(failed_rows) > 3:
            logger.info(f"DEBUG: ... and {len(failed_rows) - 3} more failed rows")

        # Also check for steps with errors in the current run
        query2 = '''
        SELECT * FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND last_error IS NOT NULL AND last_error != "" AND created_at >= ?
        '''
        logger.info(f"DEBUG: Executing query: {query2} with params: ({suite_id}, {test_idx}, {start_time})")
        cursor.execute(query2, (suite_id, test_idx, start_time))
        error_rows = cursor.fetchall()

        # Log the raw error rows for debugging
        logger.info(f"DEBUG: Found {len(error_rows)} error rows in current run (after {start_time})")
        for i, row in enumerate(error_rows[:3]):  # Only log the first 3 rows to avoid flooding the logs
            row_dict = dict(row)
            logger.info(f"DEBUG: Error row {i+1}: {row_dict}")

        if len(error_rows) > 3:
            logger.info(f"DEBUG: ... and {len(error_rows) - 3} more error rows")

        conn.close()

        # Combine failed and error rows
        all_failures = failed_rows + error_rows

        if all_failures:
            # Get the error message from the first failure
            error_message = None
            for row in all_failures:
                row_dict = dict(row)
                if row_dict.get('last_error'):
                    error_message = row_dict['last_error']
                    break

            logger.info(f"DEBUG: Found {len(all_failures)} total failures in test case {test_idx} for suite {suite_id} in current run")
            if error_message:
                logger.info(f"DEBUG: Error message: {error_message[:100]}..." if len(str(error_message)) > 100 else f"DEBUG: Error message: {error_message}")

            return True, error_message

        logger.info(f"DEBUG: No failures found in test case {test_idx} for suite {suite_id} in current run")
        return False, None
    except Exception as e:
        logger.error(f"Error checking for test case failures: {str(e)}")
        logger.error(traceback.format_exc())  # Log the full traceback for better debugging
        if 'conn' in locals() and conn:
            conn.close()
        return False, None


def get_hook_execution_count(suite_id, test_idx, step_idx=None, action_type=None):
    """
    Get the number of times a hook action has been executed for a specific test case

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int, optional): Step index
        action_type (str, optional): Action type (e.g., 'hookAction')

    Returns:
        int: Number of times the hook action has been executed
    """
    try:
        conn = sqlite3.connect(get_db_path('execution_tracker'))
        cursor = conn.cursor()

        # Build the query based on the provided parameters
        query = 'SELECT COUNT(*) FROM execution_tracking WHERE suite_id = ? AND test_idx = ?'
        params = [suite_id, test_idx]

        # Add step_idx condition if provided
        if step_idx is not None:
            query += ' AND step_idx = ?'
            params.append(step_idx)

        # Add action_type condition if provided
        if action_type is not None:
            query += ' AND action_type = ?'
            params.append(action_type)

        # Execute the query
        cursor.execute(query, params)
        count = cursor.fetchone()[0]
        conn.close()

        logger.info(f"Hook execution count for suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}, action_type={action_type}: {count}")
        return count
    except Exception as e:
        logger.error(f"Error getting hook execution count: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return 0

def clear_database():
    """
    Clear all test data from the database but preserve the screenshots table
    This is a less aggressive alternative to reset_database() that doesn't drop tables

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING DATABASE (PRESERVING SCREENSHOTS) ===")

        # Check if database file exists
        if not os.path.exists(get_db_path()):
            logger.warning(f"Database file does not exist: {get_db_path()}")
            # Create the database file and initialize it
            init_db()
            logger.info("Database initialized - it was empty to begin with")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        try:
            # Begin transaction
            conn.execute('BEGIN TRANSACTION')

            # Count before clearing
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count_before = cursor.fetchone()[0]

            logger.info(f"Before clearing - Database contains: {suites_count_before} suites, {cases_count_before} cases, {steps_count_before} steps, {screenshots_count_before} screenshots")

            # Clear all tables EXCEPT screenshots
            cursor.execute('DELETE FROM test_suites')
            cursor.execute('DELETE FROM test_cases')
            cursor.execute('DELETE FROM test_steps')
            # DO NOT clear screenshots: cursor.execute('DELETE FROM screenshots')
            logger.info("Cleared all tables except screenshots")

            # Verify the clear operation succeeded
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count_after = cursor.fetchone()[0]

            logger.info(f"After clearing - Database contains: {suites_count_after} suites, {cases_count_after} cases, {steps_count_after} steps, {screenshots_count_after} screenshots")

            # Verify screenshots were preserved
            if screenshots_count_after == screenshots_count_before:
                logger.info(f"Successfully preserved {screenshots_count_after} screenshots")
            else:
                logger.warning(f"Screenshot count changed: {screenshots_count_before} before, {screenshots_count_after} after")

            # Commit the transaction
            conn.commit()
            logger.info("Database clearing transaction committed")

            # Verify success
            clear_successful = (suites_count_after == 0 and cases_count_after == 0 and steps_count_after == 0)
            logger.info(f"Database clear successful: {clear_successful}")

            return clear_successful

        except Exception as e:
            # Rollback on error
            conn.rollback()
            logger.error(f"Error during database clearing: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Close connection
            conn.close()
            logger.info("Database connection closed after clear operation")

    except Exception as e:
        logger.error(f"Error in clear_database: {str(e)}")
        logger.error(traceback.format_exc())

        # Try the reset_database function as a last resort
        logger.info("Attempting reset_database as fallback after clear_database failure")
        reset_success = reset_database()
        return reset_success

def save_test_suite(suite_data):
    """
    Save test suite data to the database

    Args:
        suite_data (dict): Test suite data

    Returns:
        str: Suite ID
    """
    conn = sqlite3.connect(get_db_path('test_suites'))
    cursor = conn.cursor()

    suite_id = suite_data.get('id')
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Insert test suite
    cursor.execute('''
    INSERT INTO test_suites (suite_id, name, status, passed, failed, skipped, timestamp, report_dir, error)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        suite_id,
        suite_data.get('name', 'Unknown Suite'),
        suite_data.get('status', 'unknown'),
        suite_data.get('passed', 0),
        suite_data.get('failed', 0),
        suite_data.get('skipped', 0),
        timestamp,
        suite_data.get('report_dir', ''),
        suite_data.get('error', '')
    ))

    # Insert test cases and steps
    for test_idx, test_case in enumerate(suite_data.get('testCases', [])):
        cursor.execute('''
        INSERT INTO test_cases (suite_id, test_idx, name, status, duration, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            suite_id,
            test_idx,
            test_case.get('name', f'Test {test_idx}'),
            test_case.get('status', 'unknown'),
            test_case.get('duration', '0ms'),
            timestamp
        ))

        for step_idx, step in enumerate(test_case.get('steps', [])):
            # Extract action type from step data
            action_type = None
            if 'action' in step and isinstance(step['action'], dict) and 'type' in step['action']:
                action_type = step['action']['type']
            elif 'type' in step:
                action_type = step['type']
            elif 'name' in step and ':' in step['name']:
                # Try to extract from name (e.g., "Tap: element" -> "Tap")
                action_type = step['name'].split(':', 1)[0].strip()

            cursor.execute('''
            INSERT INTO test_steps (suite_id, test_idx, step_idx, name, action_type, status, duration, timestamp, screenshot_path, error)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id,
                test_idx,
                step_idx,
                step.get('name', f'Step {step_idx}'),
                action_type,
                step.get('status', 'unknown'),
                step.get('duration', '0ms'),
                step.get('timestamp', timestamp),
                step.get('screenshot', ''),
                step.get('error', '')
            ))

            # Save screenshot info if available
            if 'screenshot_filename' in step and step['screenshot_filename']:
                cursor.execute('''
                INSERT INTO screenshots (suite_id, test_idx, step_idx, filename, path, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    suite_id,
                    test_idx,
                    step_idx,
                    step.get('screenshot_filename', ''),
                    step.get('screenshot', ''),
                    timestamp
                ))

    conn.commit()
    conn.close()

    logger.info(f"Saved test suite {suite_id} to database")
    return suite_id

def get_test_suite(suite_id):
    """
    Get test suite data from the database

    Args:
        suite_id (str): Test suite ID

    Returns:
        dict: Test suite data
    """
    conn = sqlite3.connect(get_db_path('test_suites'))
    conn.row_factory = sqlite3.Row  # This enables column access by name
    cursor = conn.cursor()

    # Get test suite
    cursor.execute('SELECT * FROM test_suites WHERE suite_id = ?', (suite_id,))
    suite_row = cursor.fetchone()

    if not suite_row:
        conn.close()
        return None

    # Convert to dict
    suite_data = dict(suite_row)

    # Initialize screenshots map
    screenshots_map = {}

    # Get test cases
    cursor.execute('SELECT * FROM test_cases WHERE suite_id = ? ORDER BY test_idx', (suite_id,))
    test_cases = []

    for test_case_row in cursor.fetchall():
        test_case = dict(test_case_row)
        test_idx = test_case['test_idx']

        # Get steps for this test case
        cursor.execute('''
        SELECT * FROM test_steps
        WHERE suite_id = ? AND test_idx = ?
        ORDER BY step_idx
        ''', (suite_id, test_idx))

        steps = []
        for step_row in cursor.fetchall():
            step = dict(step_row)
            step_idx = step['step_idx']

            # Get screenshot for this step - prioritize standardized screenshots
            cursor.execute('''
            SELECT * FROM screenshots
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ?
            ''', (suite_id, test_idx, step_idx, f"step_{test_idx}_{step_idx}.png"))

            standardized_screenshot = cursor.fetchone()

            if standardized_screenshot:
                # Use the standardized screenshot
                screenshot = dict(standardized_screenshot)
                step['screenshot'] = screenshot['path']
                step['screenshot_filename'] = screenshot['filename']
                step['report_screenshot'] = screenshot['filename']
                step['resolved_screenshot'] = f"screenshots/{screenshot['filename']}"

                logger.info(f"Found standardized screenshot for step {step_idx}: {screenshot['filename']}")
            else:
                # Fall back to any screenshot for this step
                cursor.execute('''
                SELECT * FROM screenshots
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (suite_id, test_idx, step_idx))

                screenshot_row = cursor.fetchone()
                if screenshot_row:
                    screenshot = dict(screenshot_row)
                    step['screenshot'] = screenshot['path']
                    step['screenshot_filename'] = screenshot['filename']

                    # Also add the standardized format for the report
                    standardized_name = f"step_{test_idx}_{step_idx}.png"
                    step['report_screenshot'] = standardized_name
                    step['resolved_screenshot'] = f"screenshots/{standardized_name}"

                    logger.info(f"Using non-standardized screenshot for step {step_idx}: {screenshot['filename']}")

            # If we found any screenshot, add it to the screenshots map
            if 'screenshot_filename' in step:
                # Add to screenshots map for easy access in the report
                test_case_name = test_case.get('name', f'test_{test_idx}')
                test_case_name = re.sub(r'[^a-zA-Z0-9]', '', test_case_name)
                screenshot_key = f"{test_case_name}_{step_idx}"

                screenshots_map[screenshot_key] = {
                    'filename': step['screenshot_filename'],
                    'path': step['screenshot'],
                    'action_type': step.get('name', '').split(':')[0].strip() if ':' in step.get('name', '') else step.get('name', '')
                }

            steps.append(step)

        test_case['steps'] = steps
        test_cases.append(test_case)

    suite_data['testCases'] = test_cases
    suite_data['screenshots_map'] = screenshots_map

    conn.close()
    return suite_data

def get_latest_test_suite():
    """
    Get the latest test suite from the database

    Returns:
        dict: Latest test suite data
    """
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()

    cursor.execute('SELECT suite_id FROM test_suites ORDER BY id DESC LIMIT 1')
    result = cursor.fetchone()

    conn.close()

    if result:
        return get_test_suite(result[0])
    return None


def update_test_suite(suite_id, test_suite_data):
    """
    Update test suite data in the database

    Args:
        suite_id (str): Test suite ID
        test_suite_data (dict): Test suite data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update test suite
        cursor.execute('''
        UPDATE test_suites
        SET name = ?, status = ?, timestamp = ?
        WHERE suite_id = ?
        ''', (
            test_suite_data.get('name', ''),
            test_suite_data.get('status', 'updated'),
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            suite_id
        ))

        # If report_dir is provided, update it
        if 'report_dir' in test_suite_data:
            cursor.execute('''
            UPDATE test_suites
            SET report_dir = ?
            WHERE suite_id = ?
            ''', (
                test_suite_data.get('report_dir', ''),
                suite_id
            ))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error updating test suite in database: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_all_test_suites():
    """
    Get all test suites from the database

    Returns:
        list: List of test suite summary data
    """
    conn = sqlite3.connect(get_db_path())
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('''
    SELECT id, suite_id, name, status, passed, failed, skipped, timestamp, report_dir
    FROM test_suites
    ORDER BY id DESC
    ''')

    suites = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return suites

def delete_report_by_name(report_name):
    """
    Delete a report from the database by name

    Args:
        report_name (str): Name of the report to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Find the suite_id for the report
        cursor.execute("SELECT suite_id FROM test_suites WHERE name = ?", (report_name,))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"No report found with name: {report_name}")
            conn.close()
            return False

        suite_id = result[0]
        logger.info(f"Found report with suite_id: {suite_id}")

        # Delete all related data
        cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
        screenshots_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
        steps_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
        cases_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
        suites_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Deleted report data: {screenshots_deleted} screenshots, {steps_deleted} steps, {cases_deleted} cases, {suites_deleted} suites")
        return True
    except Exception as e:
        logger.error(f"Error deleting report by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def delete_report_by_dir(dir_name):
    """
    Delete a report from the database by directory name

    Args:
        dir_name (str): Directory name of the report to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        # Find the suite_id for the report
        cursor.execute("SELECT suite_id FROM test_suites WHERE report_dir LIKE ?", (f"%{dir_name}%",))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"No report found with directory: {dir_name}")
            conn.close()
            return False

        suite_id = result[0]
        logger.info(f"Found report with suite_id: {suite_id}")

        # Delete all related data
        cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
        screenshots_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
        steps_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
        cases_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
        suites_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Deleted report data: {screenshots_deleted} screenshots, {steps_deleted} steps, {cases_deleted} cases, {suites_deleted} suites")
        return True
    except Exception as e:
        logger.error(f"Error deleting report by directory: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_all_reports():
    """
    Get all reports from the database

    Returns:
        list: List of report data
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Query to get all test suites with report data
        cursor.execute('''
        SELECT
            ts.suite_id,
            ts.name AS suite_name,
            ts.status,
            ts.passed,
            ts.failed,
            ts.skipped,
            ts.timestamp,
            ts.report_dir,
            COUNT(tc.id) AS test_case_count
        FROM
            test_suites ts
        LEFT JOIN
            test_cases tc ON ts.suite_id = tc.suite_id
        WHERE
            ts.report_dir IS NOT NULL AND ts.report_dir != ''
        GROUP BY
            ts.suite_id
        ORDER BY
            ts.timestamp DESC
        ''')

        reports = []
        for row in cursor.fetchall():
            row_dict = dict(row)

            # Skip reports with invalid or missing report directories
            if not row_dict.get('report_dir'):
                continue

            # Get the report directory name
            report_dir = row_dict['report_dir']
            dir_name = os.path.basename(report_dir)

            # Create the report URL
            report_url = f"/reports/{dir_name}/mainreport.html"

            # Check if there's a ZIP file
            # Get reports directory from database
            try:
                from utils.directory_paths_db import DirectoryPathsDB
                db = DirectoryPathsDB()
                reports_dir = db.get_path('REPORTS')
                if not reports_dir:
                    reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'reports_ios')
            except Exception:
                reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'reports_ios')
            zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
            zip_url = f"/api/reports/download_zip/{dir_name}.zip" if os.path.exists(zip_path) else None

            # Also check in the report directory itself
            if not os.path.exists(zip_path):
                report_parent_dir = os.path.dirname(report_dir)
                alt_zip_path = os.path.join(report_parent_dir, f"{dir_name}.zip")
                if os.path.exists(alt_zip_path):
                    zip_path = alt_zip_path
                    zip_url = f"/api/reports/download_zip/{dir_name}.zip"

            # Build the report data structure
            reports.append({
                'filename': dir_name,
                'suite_name': row_dict['suite_name'],
                'status': row_dict['status'] or 'Unknown',
                'passed': row_dict['passed'] or 0,
                'failed': row_dict['failed'] or 0,
                'skipped': row_dict['skipped'] or 0,
                'creation_time': row_dict['timestamp'],
                'report_id': dir_name,
                'url': report_url,
                'zip_url': zip_url,
                'test_case_count': row_dict['test_case_count'] or 0,
                'allure_report_url': None  # Currently not supported, could be added in the future
            })

        conn.close()
        return reports

    except Exception as e:
        logger.error(f"Error getting reports: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def reset_database():
    """
    Reset the database by dropping and recreating all tables
    Preserves the execution_tracking table for retry tracking
    Preserves the screenshots table to maintain history between retries

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== RESETTING DATABASE ===")

        # Check if database file exists
        if not os.path.exists(get_db_path()):
            logger.warning(f"Database file does not exist: {get_db_path()}")
            # Create the database file and initialize it
            init_db()
            logger.info("Database initialized - it was empty to begin with")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Begin transaction
        conn.execute('BEGIN TRANSACTION')

        try:
            # Save execution tracking data before dropping tables
            cursor.execute('SELECT * FROM execution_tracking')
            execution_tracking_data = cursor.fetchall()
            logger.info(f"Saved {len(execution_tracking_data)} execution tracking records before reset")

            # Save screenshots data before reset - this is critical for retry functionality
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count = cursor.fetchone()[0]
            logger.info(f"Preserving {screenshots_count} screenshots during database reset")

            # Drop all tables except execution_tracking and screenshots
            cursor.execute('DROP TABLE IF EXISTS test_suites')
            cursor.execute('DROP TABLE IF EXISTS test_cases')
            cursor.execute('DROP TABLE IF EXISTS test_steps')
            # Explicitly preserving screenshots table - DO NOT DROP
            # cursor.execute('DROP TABLE IF EXISTS screenshots')
            logger.info("Dropped all tables except screenshots and execution_tracking")

            # Recreate all tables
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                name TEXT,
                status TEXT,
                passed INTEGER,
                failed INTEGER,
                skipped INTEGER,
                timestamp TEXT,
                report_dir TEXT,
                error TEXT
            )
            ''')

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                name TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                error TEXT
            )
            ''')

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                name TEXT,
                action_type TEXT,
                action_id TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                screenshot_path TEXT,
                error TEXT
            )
            ''')

            # Create test_case_json_backups table for JSON editor functionality
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_case_json_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case_filename TEXT NOT NULL,
                test_case_id TEXT,
                json_data BLOB NOT NULL,
                backup_timestamp TEXT NOT NULL,
                session_id TEXT,
                created_by TEXT DEFAULT 'json_editor'
            )
            ''')

            # Ensure screenshots table exists but don't recreate it (to preserve data)
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='screenshots'")
            if not cursor.fetchone():
                logger.info("Screenshots table doesn't exist, creating it")
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS screenshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    test_idx INTEGER,
                    step_idx INTEGER,
                    filename TEXT,
                    path TEXT,
                    timestamp TEXT,
                    action_id TEXT
                )
                ''')
            else:
                logger.info("Screenshots table exists, preserving it for retry functionality")

            # Check if execution_tracking table exists, create it if not
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
            if not cursor.fetchone():
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    test_idx INTEGER,
                    step_idx INTEGER,
                    filename TEXT,
                    action_type TEXT,
                    action_params TEXT,
                    action_id TEXT,
                    status TEXT,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 0,
                    last_error TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    in_progress BOOLEAN DEFAULT 0
                )
                ''')
                logger.info("Created execution_tracking table")

            # Commit transaction
            conn.commit()
            logger.info("Database reset successful - tables reset while preserving screenshots")

            # Verify tables are empty except screenshots
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM execution_tracking')
            tracking_count = cursor.fetchone()[0]

            logger.info(f"After reset - Database contains: {suites_count} suites, {cases_count} cases, {steps_count} steps, {screenshots_count} screenshots, {tracking_count} tracking entries")
            logger.info(f"Screenshots preserved: {screenshots_count}")

            # Verify reset was successful
            reset_successful = (suites_count == 0 and cases_count == 0 and steps_count == 0)
            logger.info(f"Database reset successful: {reset_successful}")
            return reset_successful

        except Exception as e:
            # Rollback transaction if there's an error
            conn.rollback()
            logger.error(f"Error during database reset: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Close connection
            conn.close()
            logger.info("Database connection closed after reset operation")

    except Exception as e:
        logger.error(f"Error in reset_database: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_database_state():
    """
    Check the current state of the database and return a summary

    Returns:
        dict: Summary of database state
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Get counts from all tables
        cursor.execute('SELECT COUNT(*) FROM test_suites')
        suites_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM test_cases')
        cases_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM test_steps')
        steps_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM screenshots')
        screenshots_count = cursor.fetchone()[0]

        # Check if execution_tracking table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
        if cursor.fetchone():
            cursor.execute('SELECT COUNT(*) FROM execution_tracking')
            tracking_count = cursor.fetchone()[0]
        else:
            tracking_count = 0

        # Get sample data if available
        sample_suites = []
        if suites_count > 0:
            # Check if timestamp column exists, otherwise use created_at
            cursor.execute("PRAGMA table_info(test_suites)")
            columns = [column[1] for column in cursor.fetchall()]
            timestamp_column = 'timestamp' if 'timestamp' in columns else 'created_at'

            cursor.execute(f'SELECT suite_id, name, status, {timestamp_column} FROM test_suites LIMIT 3')
            sample_suites = cursor.fetchall()

        conn.close()

        state = {
            'suites_count': suites_count,
            'cases_count': cases_count,
            'steps_count': steps_count,
            'screenshots_count': screenshots_count,
            'tracking_count': tracking_count,
            'sample_suites': sample_suites
        }

        logger.info(f"Database state: {suites_count} suites, {cases_count} cases, {steps_count} steps, {screenshots_count} screenshots, {tracking_count} tracking entries")
        if sample_suites:
            logger.info(f"Sample suites: {sample_suites}")

        return state
    except Exception as e:
        logger.error(f"Error checking database state: {str(e)}")
        return {
            'error': str(e),
            'suites_count': -1,
            'cases_count': -1,
            'steps_count': -1,
            'screenshots_count': -1,
            'tracking_count': -1,
            'sample_suites': []
        }



# Initialize the database when the module is imported (disabled in DB-only mode)
import os as _os
if _os.getenv('ALLOW_LEGACY_DB_INIT', '').lower() == 'true':
    init_db()
else:
    logger.info('Skipping legacy init_db() (DB-only mode). Set ALLOW_LEGACY_DB_INIT=true to enable.')

def get_test_case_by_id(test_case_id):
    """
    Get a test case by its ID

    Args:
        test_case_id (str): Test case ID

    Returns:
        dict: Test case data including actions
    """
    try:
        # Check if the test_case_id contains a timestamp (format: Name_YYYYMMDD_HHMMSS.json)
        # If it does, extract the base name
        base_name = test_case_id
        if '_20' in test_case_id and test_case_id.endswith('.json'):
            # Extract the base name (everything before the timestamp)
            parts = test_case_id.split('_')
            timestamp_index = -1
            for i, part in enumerate(parts):
                if part.startswith('20') and len(part) >= 8:  # Looks like a timestamp starting with 20xx
                    timestamp_index = i
                    break

            if timestamp_index > 0:
                base_name = '_'.join(parts[:timestamp_index])
                logger.info(f"Extracted base name '{base_name}' from test case ID: {test_case_id}")

        # First, check if we have a test case file with the exact ID
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        test_cases_dir = os.path.join(app_dir, 'test_cases')
        test_case_file = os.path.join(test_cases_dir, test_case_id)

        # If the test case ID doesn't end with .json, add it
        if not test_case_file.endswith('.json'):
            test_case_file += '.json'

        logger.info(f"Looking for test case file: {test_case_file}")

        if os.path.exists(test_case_file):
            # Load the test case from the file
            with open(test_case_file, 'r') as f:
                test_case = json.load(f)
                logger.info(f"Loaded test case {test_case_id} from file")
                return test_case

        # If the exact file doesn't exist, try to find a file with the base name
        if base_name != test_case_id:
            base_file = os.path.join(test_cases_dir, f"{base_name}.json")
            logger.info(f"Looking for test case with base name: {base_file}")

            if os.path.exists(base_file):
                # Load the test case from the file
                with open(base_file, 'r') as f:
                    test_case = json.load(f)
                    logger.info(f"Loaded test case {base_name} from file (using base name)")
                    return test_case

        # If still not found, try to find any file that starts with the base name
        matching_files = [f for f in os.listdir(test_cases_dir) if f.startswith(f"{base_name}_") and f.endswith('.json')]
        if matching_files:
            # Sort by modification time (newest first)
            matching_files.sort(key=lambda f: os.path.getmtime(os.path.join(test_cases_dir, f)), reverse=True)
            latest_file = os.path.join(test_cases_dir, matching_files[0])
            logger.info(f"Found matching test case file: {latest_file}")

            # Load the test case from the file
            with open(latest_file, 'r') as f:
                test_case = json.load(f)
                logger.info(f"Loaded test case from matching file: {matching_files[0]}")
                return test_case

        # If no file exists, try to load from the database
        conn = sqlite3.connect(get_db_path('test_suites'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the test case from the database
        cursor.execute('''
        SELECT tc.*, ts.name as suite_name
        FROM test_cases tc
        JOIN test_suites ts ON tc.suite_id = ts.suite_id
        WHERE tc.id = ?
        ''', (test_case_id,))

        test_case_row = cursor.fetchone()

        if not test_case_row:
            # Try with the base name
            if base_name != test_case_id:
                cursor.execute('''
                SELECT tc.*, ts.name as suite_name
                FROM test_cases tc
                JOIN test_suites ts ON tc.suite_id = ts.suite_id
                WHERE tc.name LIKE ?
                ''', (f"{base_name}%",))

                test_case_row = cursor.fetchone()

            if not test_case_row:
                logger.warning(f"Test case not found: {test_case_id}")
                conn.close()
                return None

        # Convert to dict
        test_case = dict(test_case_row)

        # Get the actions for this test case
        # For now, we'll use a placeholder since we don't store actions in the database
        # In a real implementation, you would load the actions from a file or another table
        test_case['actions'] = []

        conn.close()
        logger.info(f"Loaded test case {test_case_id} from database")
        return test_case
    except Exception as e:
        logger.error(f"Error getting test case by ID: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# Check initial database state
logger.info("Checking initial database state...")
check_database_state()

def _save_screenshot_info_db(
    suite_id,
    test_idx,
    step_idx,
    filename,
    path,
    retry_number=None,
    action_id=None,
    custom_screenshot_name=None,
    custom_screenshot_filename=None,
    custom_screenshot_path=None,
):
    """Persist step screenshots directly into the database with no filesystem copies."""

    try:
        from .screenshot_manager_db import ScreenshotManagerDB
    except Exception as import_err:
        logger.error(f"Screenshot manager unavailable: {import_err}")
        return False

    # Normalize numeric inputs early
    try:
        test_idx_int = int(test_idx or 0)
    except (TypeError, ValueError):
        test_idx_int = 0

    try:
        step_idx_int = int(step_idx) if step_idx is not None else 0
    except (TypeError, ValueError):
        step_idx_int = 0

    # Prepare database handles for lookup/update work
    conn = sqlite3.connect(get_db_path())
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    execution_id = None
    test_case_id = None
    lookup_action_id = action_id

    # Try to reuse tracking metadata if the caller did not provide it
    try:
        cursor.execute(
            '''
            SELECT id, test_execution_id, test_case_id, action_id
              FROM execution_tracking
             WHERE suite_id = ? AND test_idx = ? AND (step_idx = ? OR (? IS NULL AND step_idx IS NULL))
             ORDER BY id DESC LIMIT 1
            ''',
            (suite_id, test_idx_int, step_idx_int, step_idx),
        )
        tracking_row = cursor.fetchone()
        if tracking_row:
            execution_id = tracking_row['test_execution_id'] or execution_id
            test_case_id = tracking_row['test_case_id'] or test_case_id
            lookup_action_id = lookup_action_id or tracking_row['action_id']
    except Exception as lookup_err:
        logger.debug(f"Execution_tracking lookup failed: {lookup_err}")

    if not lookup_action_id:
        try:
            cursor.execute(
                '''
                SELECT action_id FROM test_steps
                 WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                 ORDER BY id DESC LIMIT 1
                ''',
                (suite_id, test_idx_int, step_idx_int),
            )
            row = cursor.fetchone()
            if row and row['action_id']:
                lookup_action_id = row['action_id']
        except Exception as step_err:
            logger.debug(f"test_steps lookup failed: {step_err}")

    # Resolve source bytes from the preferred path hint
    candidate_paths = [
        custom_screenshot_path,
        custom_screenshot_filename,
        path,
    ]
    screenshot_bytes = None
    chosen_path = None

    for candidate in candidate_paths:
        if not candidate:
            continue
        try:
            resolved = candidate
            if not os.path.isabs(resolved):
                resolved = os.path.abspath(resolved)
            if os.path.exists(resolved):
                with open(resolved, 'rb') as fp:
                    screenshot_bytes = fp.read()
                chosen_path = resolved
                break
        except Exception as read_err:
            logger.debug(f"Unable to read screenshot bytes from {candidate}: {read_err}")

    if not screenshot_bytes:
        logger.warning(
            "save_screenshot_info skipped (no screenshot bytes)",
        )
        conn.close()
        return False

    # Determine final filename
    timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
    if custom_screenshot_filename:
        final_filename = os.path.basename(custom_screenshot_filename)
    elif lookup_action_id:
        final_filename = f"{lookup_action_id}.png"
    else:
        final_filename = f"step_{test_idx_int}_{step_idx_int}_{timestamp}.png"

    # Apply retry naming if requested
    if retry_number is not None and not final_filename.startswith(f"retry{retry_number}_"):
        final_filename = f"retry{retry_number}_{final_filename}"

    screenshot_manager = ScreenshotManagerDB()

    # Optionally compress image bytes via helper
    try:
        compressed_bytes, _ratio = screenshot_manager.compress_screenshot(screenshot_bytes)
    except Exception:
        compressed_bytes = screenshot_bytes

    execution_id = execution_id or ''
    suite_id_normalized = suite_id or ''
    test_case_id = test_case_id or ''
    lookup_action_id = lookup_action_id or f"step_{test_idx_int}_{step_idx_int}"

    try:
        screenshot_manager.save_screenshot_to_db(
            screenshot_data=compressed_bytes,
            filename=final_filename,
            execution_id=execution_id,
            test_case_id=test_case_id,
            action_id=lookup_action_id,
            original_size=len(screenshot_bytes),
            compressed_size=len(compressed_bytes),
            suite_id=suite_id_normalized,
        )
    except Exception as save_err:
        logger.error(f"Failed to persist screenshot blob: {save_err}")
        conn.close()
        return False

    # Store a copy on execution_tracking for quick retrieval (inline blob)
    try:
        set_execution_step_screenshot_blob(
            suite_id=suite_id_normalized,
            test_idx=test_idx_int,
            step_idx=step_idx_int,
            action_id=lookup_action_id,
            blob_bytes=compressed_bytes,
            thumb_bytes=None,
            mime='image/jpeg' if compressed_bytes != screenshot_bytes else 'image/png',
        )
    except Exception as track_blob_err:
        logger.debug(f"Inline screenshot blob update skipped: {track_blob_err}")

    # Ensure execution_tracking row references the stored filename
    try:
        cursor.execute(
            '''
            UPDATE execution_tracking
               SET screenshot_filename = ?, updated_at = CURRENT_TIMESTAMP
             WHERE suite_id = ? AND test_idx = ? AND (step_idx = ? OR (? IS NULL AND step_idx IS NULL))
               AND (action_id = ? OR ? IS NULL)
            ''',
            (
                final_filename,
                suite_id_normalized,
                test_idx_int,
                step_idx_int,
                step_idx,
                lookup_action_id,
                lookup_action_id,
            ),
        )
        conn.commit()
    except Exception as update_err:
        logger.debug(f"execution_tracking filename update skipped: {update_err}")
        conn.rollback()

    conn.close()
    logger.info(
        f"Saved screenshot to DB | execution_id={execution_id or 'unknown'} | action_id={lookup_action_id} | file={final_filename} | bytes={len(compressed_bytes)} | source={chosen_path}"
    )
    return True


def save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id=None, **kwargs):
    """Backward compatible shim for callers still using the legacy signature."""

    return _save_screenshot_info_db(
        suite_id=suite_id,
        test_idx=test_idx,
        step_idx=step_idx,
        filename=filename,
        path=path,
        action_id=action_id,
        retry_number=kwargs.get('retry_number'),
        custom_screenshot_name=kwargs.get('custom_screenshot_name'),
        custom_screenshot_filename=kwargs.get('custom_screenshot_filename'),
        custom_screenshot_path=kwargs.get('custom_screenshot_path'),
    )


def check_screenshot_exists(action_id):
    """
    Check if a screenshot with the given action_id already exists in the database

    Args:
        action_id (str): The action_id to check

    Returns:
        bool: True if a screenshot with this action_id exists, False otherwise
    """
    if not action_id:
        return False

    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM screenshots WHERE action_id = ?', (action_id,))
        exists = cursor.fetchone() is not None
        conn.close()
        return exists
    except Exception as err:
        logger.error(f"Error checking if screenshot exists: {err}")
        if conn:
            conn.close()
        return False

def delete_all_screenshots() -> int:
    """Remove every screenshot record and return the number of rows deleted."""
    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute('DELETE FROM screenshots')
        deleted = cursor.rowcount
        conn.commit()
        return deleted or 0
    except Exception as err:
        logger.error(f"Error deleting screenshots: {err}")
        if conn:
            conn.rollback()
        return 0
    finally:
        if conn:
            conn.close()


def get_latest_execution_id_for_suite(suite_id: str) -> Optional[str]:
    """Return the most recent execution ID associated with the given suite."""
    if not suite_id:
        return None

    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(
            '''
            SELECT test_execution_id
              FROM execution_reports
             WHERE suite_id = ?
             ORDER BY COALESCE(end_time, updated_at, created_at) DESC
             LIMIT 1
            ''',
            (suite_id,),
        )
        row = cursor.fetchone()
        return row[0] if row and row[0] else None
    except Exception as err:
        logger.error(f"Error resolving latest execution for suite {suite_id}: {err}")
        return None
    finally:
        if conn:
            conn.close()

            conn.close()
        return False

def update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id=None):
    """
    Update the action_type field in the test_steps table

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        action_type (str): Action type
        action_id (str, optional): Unique action ID for the action

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if the step exists
        cursor.execute(
            'SELECT id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        existing = cursor.fetchone()

        if existing:
            # Update existing step
            if action_id:
                cursor.execute('''
                UPDATE test_steps
                SET action_type = ?, action_id = ?
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (
                    action_type,
                    action_id,
                    suite_id,
                    test_idx,
                    step_idx
                ))
                logger.info(f"Updated action_type and action_id for step {test_idx}_{step_idx} to {action_type}, {action_id}")
            else:
                cursor.execute('''
                UPDATE test_steps
                SET action_type = ?
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (
                    action_type,
                    suite_id,
                    test_idx,
                    step_idx
                ))
                logger.info(f"Updated action_type for step {test_idx}_{step_idx} to {action_type}")
        else:
            # Step doesn't exist, log a warning
            logger.warning(f"Step {test_idx}_{step_idx} not found in test_steps table, cannot update action_type")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error updating action_type in test_steps table: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_action_id(suite_id, test_idx, step_idx, action_id):
    """
    Update the action_id field in the test_steps and screenshots tables

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        action_id (str): Unique action ID for the action

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update test_steps table
        cursor.execute('''
        UPDATE test_steps
        SET action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (
            action_id,
            suite_id,
            test_idx,
            step_idx
        ))
        steps_updated = cursor.rowcount
        logger.info(f"Updated action_id for {steps_updated} steps in test_steps table")

        # Update screenshots table
        cursor.execute('''
        UPDATE screenshots
        SET action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (
            action_id,
            suite_id,
            test_idx,
            step_idx
        ))
        screenshots_updated = cursor.rowcount
        logger.info(f"Updated action_id for {screenshots_updated} screenshots in screenshots table")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error updating action_id: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def clear_screenshots():
    """
    Clear screenshots table in the database

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        import sqlite3
        import traceback

        logger.info("Clearing screenshots from database...")

        # Connect to the database
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Delete all records from the screenshots table
        cursor.execute("DELETE FROM screenshots")
        screenshots_deleted = cursor.rowcount

        # Also clear any standardized_screenshots table if it exists
        try:
            cursor.execute("DELETE FROM standardized_screenshots")
            std_screenshots_deleted = cursor.rowcount
        except sqlite3.OperationalError:
            std_screenshots_deleted = 0
            logger.info("standardized_screenshots table does not exist")

        # Commit and close
        conn.commit()
        conn.close()

        # Vacuum the database to reclaim space
        conn = sqlite3.connect(get_db_path())
        conn.execute("PRAGMA foreign_keys = OFF")
        conn.execute("VACUUM")
        conn.execute("PRAGMA foreign_keys = ON")
        conn.close()

        logger.info(f"Successfully cleared {screenshots_deleted} screenshots and {std_screenshots_deleted} standardized screenshots")
        return True

    except Exception as e:
        logger.error(f"Error in clear_screenshots: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def save_environment_variable(name, value, description=None):
    """
    Save or update an environment variable in the database

    Args:
        name (str): Variable name
        value (str): Variable value
        description (str, optional): Variable description

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Use INSERT OR REPLACE to handle both new and existing variables
        cursor.execute("""
            INSERT OR REPLACE INTO environment_variables (name, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        """, (name, value, description))

        conn.commit()
        conn.close()

        logger.info(f"Environment variable '{name}' saved successfully")
        return True

    except Exception as e:
        logger.error(f"Error saving environment variable '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def get_environment_variable(name):
    """
    Get an environment variable from the database

    Args:
        name (str): Variable name

    Returns:
        dict: Variable data or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path('environments'))
        cursor = conn.cursor()

        cursor.execute("""
            SELECT name, value, description, created_at, updated_at
            FROM environment_variables
            WHERE name = ?
        """, (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                'name': row[0],
                'value': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            }
        return None

    except Exception as e:
        logger.error(f"Error getting environment variable '{name}': {str(e)}")
        return None


def get_all_environment_variables():
    """
    Get all environment variables from the database

    Returns:
        list: List of environment variable dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT name, value, description, created_at, updated_at
            FROM environment_variables
            ORDER BY name
        """)

        rows = cursor.fetchall()
        conn.close()

        variables = []
        for row in rows:
            variables.append({
                'name': row[0],
                'value': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        return variables

    except Exception as e:
        logger.error(f"Error getting all environment variables: {str(e)}")
        return []


def delete_environment_variable(name):
    """
    Delete an environment variable from the database

    Args:
        name (str): Variable name

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("DELETE FROM environment_variables WHERE name = ?", (name,))

        conn.commit()
        conn.close()

        logger.info(f"Environment variable '{name}' deleted successfully")
        return True

    except Exception as e:
        logger.error(f"Error deleting environment variable '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_action_enabled_state(action_id, enabled):
    """
    Update the enabled state of an action in the database

    Args:
        action_id (str): Action ID
        enabled (bool): Whether the action is enabled

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update the enabled state in the actions table
        cursor.execute("""
            UPDATE actions SET enabled = ? WHERE action_id = ?
        """, (enabled, action_id))

        # If no rows were affected, the action might not exist yet
        if cursor.rowcount == 0:
            # Try to insert a new record (this might be needed for legacy actions)
            cursor.execute("""
                INSERT OR IGNORE INTO actions (action_id, enabled) VALUES (?, ?)
            """, (action_id, enabled))

        conn.commit()
        conn.close()

        logger.info(f"Action '{action_id}' enabled state updated to {enabled}")
        return True

    except Exception as e:
        logger.error(f"Error updating action enabled state for '{action_id}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


# Environment Management Functions
def get_environments():
    """
    Get all environments from the database

    Returns:
        list: List of environment dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, is_active, created_at, updated_at
            FROM env_environments
            ORDER BY name
        """)

        environments = []
        for row in cursor.fetchall():
            environments.append({
                'id': row[0],
                'name': row[1],
                'is_active': bool(row[2]),
                'created_at': row[3],
                'updated_at': row[4]
            })

        conn.close()
        return environments

    except Exception as e:
        logger.error(f"Error getting environments: {str(e)}")
        return []


def get_variables(environment_id):
    """
    Get all variables for a specific environment

    Args:
        environment_id (int): Environment ID

    Returns:
        list: List of variable dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, initial_value, current_value, type, created_at, updated_at
            FROM environment_variables
            WHERE environment_id = ?
            ORDER BY name
        """, (environment_id,))

        variables = []
        for row in cursor.fetchall():
            variables.append({
                'id': row[0],
                'name': row[1],
                'initial_value': row[2] or '',
                'current_value': row[3] or '',
                'type': row[4] or 'default',
                'created_at': row[5],
                'updated_at': row[6]
            })

        conn.close()
        return variables

    except Exception as e:
        logger.error(f"Error getting variables for environment {environment_id}: {str(e)}")
        return []


def create_environment(name):
    """
    Create a new environment

    Args:
        name (str): Environment name

    Returns:
        int: Environment ID if successful, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO env_environments (name, is_active, created_at, updated_at)
            VALUES (?, 0, datetime('now'), datetime('now'))
        """, (name,))

        environment_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"Environment '{name}' created with ID {environment_id}")
        return environment_id

    except Exception as e:
        logger.error(f"Error creating environment '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return None


def create_variable(environment_id, name, initial_value, current_value=None, var_type='default'):
    """
    Create a new environment variable

    Args:
        environment_id (int): Environment ID
        name (str): Variable name
        initial_value (str): Initial value
        current_value (str): Current value (defaults to initial_value)
        var_type (str): Variable type

    Returns:
        int: Variable ID if successful, None otherwise
    """
    try:
        if current_value is None:
            current_value = initial_value

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if variable already exists for this environment
        cursor.execute("""
            SELECT id FROM environment_variables
            WHERE environment_id = ? AND name = ?
        """, (environment_id, name))
        existing = cursor.fetchone()

        if existing:
            # Update existing variable
            cursor.execute("""
                UPDATE environment_variables
                SET current_value = ?, updated_at = datetime('now')
                WHERE environment_id = ? AND name = ?
            """, (current_value, environment_id, name))
            variable_id = existing[0]
            logger.info(f"Variable '{name}' updated for environment {environment_id}")
        else:
            # Create new variable
            cursor.execute("""
                INSERT INTO environment_variables
                (environment_id, name, initial_value, current_value, type, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (environment_id, name, initial_value, current_value, var_type))

            variable_id = cursor.lastrowid
            logger.info(f"Variable '{name}' created for environment {environment_id}")

        conn.commit()
        conn.close()

        return variable_id

    except Exception as e:
        logger.error(f"Error creating variable '{name}' for environment {environment_id}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return None

# ============================================================================
# TEST CASE AND TEST SUITE METADATA FUNCTIONS
# ============================================================================

def get_test_case_metadata(test_case_id):
    """
    Get test case metadata by ID

    Args:
        test_case_id (str): Test case ID

    Returns:
        dict: Test case metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_case_metadata WHERE id = ?
        ''', (test_case_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test case metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_case_metadata_by_name(name):
    """
    Get test case metadata by name

    Args:
        name (str): Test case name

    Returns:
        dict: Test case metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_case_metadata WHERE name = ?
        ''', (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test case metadata by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_all_test_cases_metadata():
    """
    Get all test case metadata

    Returns:
        list: List of test case metadata dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_case_metadata ORDER BY updated_date DESC
        ''')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    except Exception as e:
        logger.error(f"Error getting all test cases metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def upsert_test_case_metadata(test_case_id, name, description='', file_path='', device_id='', action_count=0, labels=None):
    """
    Insert or update test case metadata

    Args:
        test_case_id (str): Test case ID
        name (str): Test case name
        description (str): Test case description
        file_path (str): File path
        device_id (str): Device ID
        action_count (int): Number of actions
        labels (list): List of labels

    Returns:
        bool: Success status
    """
    try:
        import json
        from datetime import datetime

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        labels_json = json.dumps(labels or [])
        current_time = datetime.now().isoformat()

        # Check if record exists
        cursor.execute('SELECT id FROM test_case_metadata WHERE id = ?', (test_case_id,))
        existing = cursor.fetchone()

        if existing:
            # Update existing record
            cursor.execute('''
                UPDATE test_case_metadata
                SET name = ?, description = ?, updated_date = ?, device_id = ?,
                    action_count = ?, labels = ?
                WHERE id = ?
            ''', (name, description, current_time, device_id, action_count, labels_json, test_case_id))
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO test_case_metadata
                (id, name, description, file_path, created_date, updated_date, device_id, action_count, labels)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (test_case_id, name, description, file_path, current_time, current_time, device_id, action_count, labels_json))

        conn.commit()
        conn.close()

        logger.info(f"Upserted test case metadata: {test_case_id}")
        return True

    except Exception as e:
        logger.error(f"Error upserting test case metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def delete_test_case_metadata(test_case_id):
    """
    Delete test case metadata

    Args:
        test_case_id (str): Test case ID

    Returns:
        bool: Success status
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute('DELETE FROM test_case_metadata WHERE id = ?', (test_case_id,))

        conn.commit()
        conn.close()

        logger.info(f"Deleted test case metadata: {test_case_id}")
        return True

    except Exception as e:
        logger.error(f"Error deleting test case metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

# Test Suite Metadata Functions

def get_test_suite_metadata(suite_id):
    """
    Get test suite metadata by ID

    Args:
        suite_id (str): Test suite ID

    Returns:
        dict: Test suite metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_suite_metadata WHERE id = ?
        ''', (suite_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test suite metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_suite_metadata_by_name(name):
    """
    Get test suite metadata by name

    Args:
        name (str): Test suite name

    Returns:
        dict: Test suite metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_suite_metadata WHERE name = ?
        ''', (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test suite metadata by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_all_test_suites_metadata():
    """
    Get all test suite metadata

    Returns:
        list: List of test suite metadata dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_suite_metadata ORDER BY updated_date DESC
        ''')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    except Exception as e:
        logger.error(f"Error getting all test suites metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def upsert_test_suite_metadata(suite_id, name, description='', file_path='', test_case_files=None):
    """
    Insert or update test suite metadata

    Args:
        suite_id (str): Test suite ID
        name (str): Test suite name
        description (str): Test suite description
        file_path (str): File path
        test_case_files (list): List of test case files

    Returns:
        bool: Success status
    """
    try:
        import json
        from datetime import datetime

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        test_case_files_json = json.dumps(test_case_files or [])
        test_case_count = len(test_case_files or [])
        current_time = datetime.now().isoformat()

        # Check if record exists
        cursor.execute('SELECT id FROM test_suite_metadata WHERE id = ?', (suite_id,))
        existing = cursor.fetchone()

        if existing:
            # Update existing record
            cursor.execute('''
                UPDATE test_suite_metadata
                SET name = ?, description = ?, updated_date = ?, test_case_count = ?, test_case_files = ?
                WHERE id = ?
            ''', (name, description, current_time, test_case_count, test_case_files_json, suite_id))
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO test_suite_metadata
                (id, name, description, file_path, created_date, updated_date, test_case_count, test_case_files)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (suite_id, name, description, file_path, current_time, current_time, test_case_count, test_case_files_json))

        conn.commit()
        conn.close()

        logger.info(f"Upserted test suite metadata: {suite_id}")
        return True

    except Exception as e:
        logger.error(f"Error upserting test suite metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def get_execution_setting(setting_key, default_value=None):
    """
    Get execution setting value by key

    Args:
        setting_key (str): Setting key to retrieve
        default_value (str): Default value if setting not found

    Returns:
        str: Setting value or default value
    """
    conn = None
    try:
        conn = sqlite3.connect(get_db_path('settings'))
        cursor = conn.cursor()

        cursor.execute('SELECT setting_value FROM execution_settings WHERE setting_name = ?', (setting_key,))
        result = cursor.fetchone()

        if result:
            return result[0]
        return default_value

    except Exception as e:
        logger.error(f"Error getting execution setting {setting_key}: {e}")
        return default_value
    finally:
        if conn:
            conn.close()

def set_execution_setting(setting_key, setting_value, description=None):
    """
    Set execution setting value

    Args:
        setting_key (str): Setting key
        setting_value (str): Setting value
        description (str): Optional description

    Returns:
        bool: True if successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Use INSERT OR REPLACE to update existing or create new
        cursor.execute('''
        INSERT OR REPLACE INTO execution_settings (setting_name, setting_value, description, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ''', (setting_key, setting_value, description))

        conn.commit()
        logger.info(f"Set execution setting {setting_key} = {setting_value}")
        return True

    except Exception as e:
        logger.error(f"Error setting execution setting {setting_key}: {e}")
        return False
    finally:
        if conn:
            conn.close()

# --- Screenshot BLOB helpers ---
from typing import Optional, Tuple

def set_execution_step_screenshot_blob(suite_id: str, test_idx: int, step_idx: Optional[int], action_id: Optional[str], blob_bytes: bytes, thumb_bytes: Optional[bytes] = None, mime: str = 'image/png') -> bool:
    """
    Store screenshot bytes (and optional thumbnail) for a specific execution step.
    Matches by (suite_id, test_idx, step_idx) if provided, otherwise by action_id.
    """
    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Ensure execution_tracking has screenshot columns
        cursor.execute("PRAGMA table_info(execution_tracking)")
        et_cols = [c[1] for c in cursor.fetchall()]
        for col, coltype in (
            ('screenshot_blob', 'BLOB'),
            ('screenshot_thumb_blob', 'BLOB'),
            ('screenshot_mime', 'TEXT'),
            ('screenshot_filename', 'TEXT'),
        ):
            if col not in et_cols:
                try:
                    cursor.execute(f'ALTER TABLE execution_tracking ADD COLUMN {col} {coltype}')
                except Exception:
                    pass

        # Prefer matching by action_id if provided and unique
        if action_id:
            cursor.execute(
                """
                UPDATE execution_tracking
                   SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                 WHERE action_id = ? AND (suite_id = ? OR ? IS NULL OR ? = '')
                """,
                (blob_bytes, thumb_bytes, mime, action_id, suite_id, suite_id, suite_id),
            )
            if cursor.rowcount == 0 and suite_id is not None:
                # Fallback without suite constraint if no rows updated
                cursor.execute(
                    """
                    UPDATE execution_tracking
                       SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                     WHERE action_id = ?
                    """,
                    (blob_bytes, thumb_bytes, mime, action_id),
                )
        else:
            # Match by composite key
            cursor.execute(
                """
                UPDATE execution_tracking
                   SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                 WHERE suite_id = ? AND test_idx = ? AND (step_idx = ? OR (? IS NULL AND step_idx IS NULL))
                """,
                (blob_bytes, thumb_bytes, mime, suite_id, int(test_idx or 0), step_idx, step_idx),
            )

        conn.commit()
        return True
    except Exception as e:
        logger.error(f"Error setting screenshot blob: {e}")
        return False
    finally:
        if conn:
            conn.close()

def get_execution_step_screenshot_blob(suite_id: str, test_idx: int, step_idx: Optional[int]) -> Optional[Tuple[bytes, Optional[bytes], str]]:
    """Return (full_bytes, thumb_bytes, mime) for a specific step, or None."""
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
              FROM execution_tracking
             WHERE suite_id = ? AND test_idx = ? AND (step_idx = ? OR (? IS NULL AND step_idx IS NULL))
             ORDER BY id DESC LIMIT 1
            """,
            (suite_id, int(test_idx or 0), step_idx, step_idx),
        )
        row = cursor.fetchone()
        conn.close()
        if row and row[0]:
            return row[0], row[1], row[2]
        return None
    except Exception as e:
        logger.error(f"Error getting screenshot blob: {e}")
        return None

def get_execution_step_screenshot_by_action(action_id: str, suite_id: Optional[str] = None) -> Optional[Tuple[bytes, Optional[bytes], str]]:
    """Return (full_bytes, thumb_bytes, mime) by action_id (optionally constrained by suite_id)."""
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        if suite_id:
            cursor.execute(
                """
                SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                  FROM execution_tracking
                 WHERE action_id = ? AND suite_id = ?
                 ORDER BY id DESC LIMIT 1
                """,
                (action_id, suite_id),
            )
        else:
            cursor.execute(
                """
                SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                  FROM execution_tracking
                 WHERE action_id = ?
                 ORDER BY id DESC LIMIT 1
                """,
                (action_id,),
            )
        row = cursor.fetchone()
        conn.close()
        if row and row[0]:
            return row[0], row[1], row[2]
        return None
    except Exception as e:
        logger.error(f"Error getting screenshot blob by action: {e}")
        return None

def delete_test_suite_metadata(suite_id):
    """
    Delete test suite metadata

    Args:
        suite_id (str): Test suite ID

    Returns:
        bool: Success status
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute('DELETE FROM test_suite_metadata WHERE id = ?', (suite_id,))

        conn.commit()
        conn.close()

        logger.info(f"Deleted test suite metadata: {suite_id}")
        return True

    except Exception as e:
        logger.error(f"Error deleting test suite metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def get_video_recording_settings():
    """
    Get video recording settings from database

    Returns:
        dict: Video recording settings or None if not found
    """
    try:
        conn = sqlite3.connect('test_automation.db')
        cursor = conn.cursor()

        # Create video_recording_settings table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_recording_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            video_quality TEXT,
            video_size TEXT,
            bit_rate INTEGER,
            video_fps INTEGER,
            time_limit INTEGER,
            video_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Get current platform (default to 'ios' if not specified)
        platform = 'ios'  # This could be made dynamic based on current session

        cursor.execute('''
        SELECT video_quality, video_size, bit_rate, video_fps, time_limit, video_type
        FROM video_recording_settings
        WHERE platform = ?
        ORDER BY updated_at DESC
        LIMIT 1
        ''', (platform,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                'video_quality': result[0],
                'video_size': result[1],
                'bit_rate': result[2],
                'video_fps': result[3],
                'time_limit': result[4],
                'video_type': result[5]
            }

        return None

    except Exception as e:
        logger.error(f"Error getting video recording settings: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def save_video_recording_settings(platform, settings):
    """
    Save video recording settings to database

    Args:
        platform (str): Platform (ios/android)
        settings (dict): Video recording settings

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect('test_automation.db')
        cursor = conn.cursor()

        # Create table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_recording_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            video_quality TEXT,
            video_size TEXT,
            bit_rate INTEGER,
            video_fps INTEGER,
            time_limit INTEGER,
            video_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Insert or update settings
        cursor.execute('''
        INSERT OR REPLACE INTO video_recording_settings
        (platform, video_quality, video_size, bit_rate, video_fps, time_limit, video_type, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            platform,
            settings.get('video_quality'),
            settings.get('video_size'),
            settings.get('bit_rate'),
            settings.get('video_fps'),
            settings.get('time_limit'),
            settings.get('video_type')
        ))

        conn.commit()
        conn.close()

        logger.info(f"Video recording settings saved for platform: {platform}")
        return True

    except Exception as e:
        logger.error(f"Error saving video recording settings: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def sync_test_cases_with_filesystem():
    """Synchronize test cases between file system and database (DISABLED in DB-only mode)."""
    import os
    import json
    from datetime import datetime

    # Enforce DB-only mode by default; allow enabling via env var
    if os.getenv('ALLOW_JSON_FS_SYNC', '').lower() != 'true':
        logger.info("Filesystem test case sync is disabled (DB-only mode). Set ALLOW_JSON_FS_SYNC=true to enable.")
        return {'status': 'skipped', 'reason': 'DB_ONLY'}

    logger.info("Starting test case synchronization...")

    # Get test cases directory
    test_cases_dir = get_configured_directory('TEST_CASES', 'test_cases')
    if not os.path.exists(test_cases_dir):
        logger.error(f"Test cases directory not found: {test_cases_dir}")
        return {'error': 'Test cases directory not found'}

    conn = get_db_connection()
    if not conn:
        return {'error': 'Database connection failed'}

    stats = {
        'files_processed': 0,
        'files_added_to_db': 0,
        'files_updated_in_db': 0,
        'db_entries_removed': 0,
        'files_updated_with_id': 0,
        'errors': 0
    }

    try:
        cursor = conn.cursor()

        # Get all JSON files from filesystem
        json_files = []
        for filename in os.listdir(test_cases_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(test_cases_dir, filename)
                json_files.append({'filename': filename, 'filepath': filepath})

        # Get all test cases from database
        cursor.execute("SELECT test_case_id, file_path, name, status FROM test_cases")
        db_entries = {}
        for row in cursor.fetchall():
            test_case_id, file_path, name, status = row
            # Extract filename from file_path
            filename = os.path.basename(file_path) if file_path else None
            if filename:
                db_entries[filename] = {'test_case_id': test_case_id, 'name': name, 'status': status}

        logger.info(f"Found {len(json_files)} JSON files and {len(db_entries)} database entries")

        # Process each JSON file
        for file_info in json_files:
            filename = file_info['filename']
            filepath = file_info['filepath']
            stats['files_processed'] += 1

            try:
                # Read JSON file
                with open(filepath, 'r', encoding='utf-8') as f:
                    test_case_data = json.load(f)

                # Check if file has test_case_id
                file_test_case_id = test_case_data.get('test_case_id')

                if filename in db_entries:
                    # File exists in database
                    db_entry = db_entries[filename]
                    db_test_case_id = db_entry['test_case_id']

                    if file_test_case_id != db_test_case_id:
                        # Update file with correct test_case_id
                        test_case_data['test_case_id'] = db_test_case_id
                        with open(filepath, 'w', encoding='utf-8') as f:
                            json.dump(test_case_data, f, indent=2, ensure_ascii=False)
                        stats['files_updated_with_id'] += 1
                        logger.info(f"Updated {filename} with test_case_id: {db_test_case_id}")

                    # Update database entry if needed
                    file_name = test_case_data.get('name', filename.replace('.json', ''))
                    if db_entry['name'] != file_name:
                        cursor.execute("""
                            UPDATE test_cases SET name = ?, updated_at = ?
                            WHERE test_case_id = ?
                        """, (file_name, datetime.now(), db_test_case_id))
                        stats['files_updated_in_db'] += 1
                        logger.info(f"Updated database name for {filename}: {file_name}")

                else:
                    # File doesn't exist in database - create entry
                    if not file_test_case_id:
                        # Generate new test_case_id
                        file_test_case_id = generate_test_case_id()
                        test_case_data['test_case_id'] = file_test_case_id
                        with open(filepath, 'w', encoding='utf-8') as f:
                            json.dump(test_case_data, f, indent=2, ensure_ascii=False)
                        stats['files_updated_with_id'] += 1

                    # Create database entry
                    name = test_case_data.get('name', filename.replace('.json', ''))
                    file_path = os.path.join(test_cases_dir, filename)
                    cursor.execute("""
                        INSERT INTO test_cases (test_case_id, suite_id, platform, name, file_path, step_count, json_payload, test_idx, status, created_at, updated_at)
                        VALUES (?, 'standalone', 'ios', ?, ?, ?, ?, 0, 'active', ?, ?)
                    """, (file_test_case_id, name, file_path, len(test_case_data.get('actions', [])),
                          json.dumps(test_case_data), datetime.now(), datetime.now()))
                    stats['files_added_to_db'] += 1
                    logger.info(f"Added {filename} to database with ID: {file_test_case_id}")

            except Exception as e:
                logger.error(f"Error processing {filename}: {e}")
                stats['errors'] += 1

        # Find orphaned database entries (files that no longer exist)
        existing_filenames = {f['filename'] for f in json_files}
        orphaned_entries = [filename for filename in db_entries.keys() if filename not in existing_filenames]

        for filename in orphaned_entries:
            db_entry = db_entries[filename]
            if db_entry['status'] == 'active':
                # Mark as inactive instead of deleting
                cursor.execute("""
                    UPDATE test_cases SET status = 'inactive', updated_at = ?
                    WHERE test_case_id = ?
                """, (datetime.now(), db_entry['test_case_id']))
                stats['db_entries_removed'] += 1
                logger.info(f"Marked {filename} as inactive (file not found)")

        conn.commit()
        logger.info(f"Test case synchronization completed: {stats}")
        return stats

    except Exception as e:
        logger.error(f"Error during test case synchronization: {e}")
        conn.rollback()
        return {'error': str(e)}
    finally:
        conn.close()

def sync_test_suites_with_filesystem():
    """Synchronize test suites between file system and database"""
    import os
    import json
    from datetime import datetime

    logger.info("Starting test suite synchronization...")

    # Get test suites directory
    test_suites_dir = get_configured_directory('TEST_SUITES', 'test_suites')
    if not os.path.exists(test_suites_dir):
        logger.error(f"Test suites directory not found: {test_suites_dir}")
        return {'error': 'Test suites directory not found'}

    conn = get_db_connection()
    if not conn:
        return {'error': 'Database connection failed'}

    stats = {
        'files_processed': 0,
        'files_added_to_db': 0,
        'files_updated_in_db': 0,
        'db_entries_removed': 0,
        'errors': 0
    }

    try:
        cursor = conn.cursor()

        # Get all JSON files from filesystem
        json_files = []
        for filename in os.listdir(test_suites_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(test_suites_dir, filename)
                json_files.append({'filename': filename, 'filepath': filepath})

        # Get all test suites from database
        cursor.execute("SELECT suite_id, name, status FROM test_suites")
        db_entries = {row[1]: {'suite_id': row[0], 'status': row[2]} for row in cursor.fetchall()}

        logger.info(f"Found {len(json_files)} JSON files and {len(db_entries)} database entries")

        # Process each JSON file
        for file_info in json_files:
            filename = file_info['filename']
            filepath = file_info['filepath']
            stats['files_processed'] += 1

            try:
                # Read JSON file
                with open(filepath, 'r', encoding='utf-8') as f:
                    suite_data = json.load(f)

                suite_name = suite_data.get('name', filename.replace('.json', ''))

                if suite_name in db_entries:
                    # Suite exists in database - update if needed
                    stats['files_updated_in_db'] += 1
                else:
                    # Suite doesn't exist in database - create entry
                    suite_id = generate_suite_id()
                    cursor.execute("""
                        INSERT INTO test_suites (suite_id, name, status, created_at, updated_at)
                        VALUES (?, ?, 'active', ?, ?)
                    """, (suite_id, suite_name, datetime.now(), datetime.now()))
                    stats['files_added_to_db'] += 1
                    logger.info(f"Added {suite_name} to database with ID: {suite_id}")

            except Exception as e:
                logger.error(f"Error processing {filename}: {e}")
                stats['errors'] += 1

        conn.commit()
        logger.info(f"Test suite synchronization completed: {stats}")
        return stats

    except Exception as e:
        logger.error(f"Error during test suite synchronization: {e}")
        conn.rollback()
        return {'error': str(e)}
    finally:
        conn.close()

def generate_test_case_id():
    """Generate a unique test case ID"""
    import string
    import random
    chars = string.ascii_uppercase + string.digits
    return ''.join(random.choices(chars, k=6))

def generate_suite_id():
    """Generate a unique suite ID"""
    import string
    import random
    chars = string.ascii_uppercase + string.digits
    return ''.join(random.choices(chars, k=6))


# --- Execution Reports helpers ---

def upsert_execution_report(execution_id: str, suite_id: str, data_json, status: str | None = None) -> bool:
    """Create or update an execution_reports row.

    - execution_id: UUID used in execution_tracking.test_execution_id
    - suite_id: the suite UUID used in execution_tracking.suite_id
    - data_json: dict/list/bytes/str representing data.json content
    - status: optional overall status (passed/failed/running/...)
    """
    try:
        import json as _json
        if isinstance(data_json, (dict, list)):
            data_bytes = _json.dumps(data_json).encode('utf-8')
        elif isinstance(data_json, (bytes, bytearray)):
            data_bytes = bytes(data_json)
        else:
            data_bytes = str(data_json).encode('utf-8')

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO execution_reports (execution_id, suite_id, data_json, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON CONFLICT(execution_id) DO UPDATE SET
                suite_id = excluded.suite_id,
                data_json = excluded.data_json,
                status = COALESCE(execution_reports.status, excluded.status),
                updated_at = CURRENT_TIMESTAMP
            """,
            (execution_id, suite_id, data_bytes, status)
        )
        conn.commit()
        conn.close()
        logger.info(f"upsert_execution_report: persisted execution_id={execution_id}, suite_id={suite_id}, status={status if status else 'n/a'}, bytes={len(data_bytes)}")
        return True
    except Exception as e:
        logger.error(f"upsert_execution_report error: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def save_execution_report_snapshot(execution_id: str, suite_id: str, status: str | None = None) -> bool:
    """Build a minimal data.json from DB and persist it into execution_reports.

    This is called when a suite finishes to ensure the Reports tab is populated.
    """
    try:
        # Resolve suite_id properly
        resolved_suite_id = resolve_execution_id_to_suite_id(suite_id or execution_id)
        from .build_data_json import build_data_json_from_execution_tracker

        tracking_rows = get_execution_tracking_for_suite(resolved_suite_id)
        # Derive a conservative overall status if not provided
        overall = status
        if overall is None:
            any_failed = any((r.get('status') or '').lower() == 'failed' for r in tracking_rows)
            overall = 'failed' if any_failed else 'passed'

        data = build_data_json_from_execution_tracker(resolved_suite_id, tracking_rows)
        if not data:
            # Fallback to a tiny shell so the row still exists
            data = {
                'execution_id': execution_id,
                'suite_id': resolved_suite_id,
                'status': overall,
                'generated_from': 'database',
            }
        else:
            # Ensure identifiers present
            data['execution_id'] = data.get('execution_id') or execution_id
            data['suite_id'] = data.get('suite_id') or resolved_suite_id
            data['status'] = data.get('status') or overall

        return upsert_execution_report(execution_id, resolved_suite_id, data, overall)
    except Exception as e:
        logger.error(f"save_execution_report_snapshot error: {e}")
        return False


# --- Reference Images (BLOB) helpers ---
def ensure_reference_images_table():
    """Create the reference_images table if it doesn't exist (iOS)."""
    try:
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute(
            """
            CREATE TABLE IF NOT EXISTS reference_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                image BLOB NOT NULL,
                created_at TEXT DEFAULT (datetime('now'))
            )
            """
        )
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Failed to ensure reference_images table: {e}")
        return False


def reference_image_exists(name: str) -> bool:
    """Return True if a reference image with this name exists (iOS, supports legacy schema)."""
    if not name:
        return False
    try:
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        # Determine schema columns
        cur.execute("PRAGMA table_info(reference_images)")
        cols = [row[1].lower() for row in cur.fetchall()]
        name_col = 'name' if 'name' in cols else ('image_name' if 'image_name' in cols else 'name')
        cur.execute(f"SELECT 1 FROM reference_images WHERE {name_col} = ? LIMIT 1", (name,))
        exists = cur.fetchone() is not None
        conn.close()
        return exists
    except Exception as e:
        logger.error(f"Failed checking reference image existence: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def save_reference_image(name: str, image_bytes: bytes) -> bool:
    """Save or replace a reference image BLOB by name (iOS, supports legacy schema)."""
    if not name or not image_bytes:
        return False
    try:
        ensure_reference_images_table()
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        # Determine schema columns and build insert dynamically for legacy and consolidated schemas
        cur.execute("PRAGMA table_info(reference_images)")
        cols = [row[1].lower() for row in cur.fetchall()]
        name_col = 'name' if 'name' in cols else ('image_name' if 'image_name' in cols else 'name')
        image_col = 'image' if 'image' in cols else ('image_data' if 'image_data' in cols else 'image')

        columns = [name_col, image_col]
        params = [name, sqlite3.Binary(image_bytes)]

        # If consolidated schema is present, provide required fields
        if 'image_format' in cols:
            columns.append('image_format')
            params.append('png')  # dataURL is PNG
        if 'file_size' in cols:
            columns.append('file_size')
            params.append(len(image_bytes))
        if 'checksum' in cols:
            import hashlib
            columns.append('checksum')
            params.append(hashlib.md5(image_bytes).hexdigest())
        if 'created_at' in cols:
            columns.append('created_at')
            # Use SQLite function for timestamp
            placeholders = ','.join(['?'] * (len(params))) + ", datetime('now')"
        else:
            placeholders = ','.join(['?'] * (len(params)))

        cols_sql = ', '.join(columns)
        sql = f"INSERT OR REPLACE INTO reference_images ({cols_sql}) VALUES ({placeholders})"
        cur.execute(sql, params)
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Failed to save reference image '{name}' to DB: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def list_reference_image_names_sorted():
    """Return list of reference image names sorted alphabetically (iOS)."""
    try:
        ensure_reference_images_table()
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("SELECT name FROM reference_images ORDER BY LOWER(name) ASC")
        rows = cur.fetchall()
        conn.close()
        return [r[0] for r in rows]
    except Exception as e:
        logger.error(f"Failed to list reference image names: {e}")
        return []


def save_test_run_data(data):
    """
    Save test run data to the database.
    This is a compatibility function for legacy code.

    Args:
        data: Test run data to save

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"save_test_run_data called with data: {type(data)}")
        # This is a legacy function that may not be needed in the new database-first architecture
        # For now, just log and return True to prevent errors
        return True
    except Exception as e:
        logger.error(f"Error in save_test_run_data: {e}")
        return False
