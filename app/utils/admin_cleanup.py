#!/usr/bin/env python3
"""
Admin cleanup utilities for iOS backend (app/)
- Clears test data while preserving system configuration (backend URLs, system preferences)
- Provides transactional safety, rollback on errors, and robust error handling
"""
import os
import sqlite3
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# Database paths for iOS backend
from .database import get_db_path

PRESERVE_TABLES = {
    # Settings DB contains global app settings and directory paths
    'settings': ['settings'],
    # Preserve environments/global variables tables
    'environments': ['environments'],
    'globals': ['global_values']
}

# Tables to clear fully (test data)
CLEAR_TABLES = {
    'test_suites': ['test_suites', 'test_cases', 'test_steps'],
    'execution_tracker': ['execution_tracking', 'screenshots']  # screenshots references
}

# Additionally purge directory path configurations (platform-specific)
PLATFORM_SETTINGS_CATEGORY = 'directory_path'


def reset_user_data() -> Dict[str, Any]:
    """Reset user data for a fresh installation on iOS backend.

    Returns a JSON-like dict with success flag and details.
    """
    results = {"success": True, "details": []}
    try:
        # 1) Clear test_suites related tables
        _clear_tables('test_suites', CLEAR_TABLES['test_suites'], results)
        # 2) Clear execution logs and session data
        _clear_tables('execution_tracker', CLEAR_TABLES['execution_tracker'], results)
        # 3) Remove platform-specific directory path configs from settings DB
        _clear_platform_paths('settings', results)
        results["message"] = "All user data cleared (iOS)."
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        results["success"] = False
        results["error"] = str(e)
    return results


def _clear_tables(db_type: str, tables: list, results: Dict[str, Any]):
    db_path = get_db_path(db_type)
    if not os.path.exists(db_path):
        results["details"].append({"db": db_type, "status": "skipped", "reason": "db not found"})
        return
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        conn.execute('PRAGMA foreign_keys = ON')
        cur = conn.cursor()
        conn.execute('BEGIN')
        for table in tables:
            try:
                cur.execute(f'DELETE FROM {table}')
                results["details"].append({"db": db_type, "table": table, "status": "cleared"})
            except sqlite3.OperationalError as oe:
                # Table might not exist; record and continue
                results["details"].append({"db": db_type, "table": table, "status": "missing", "error": str(oe)})
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error clearing {db_type}: {e}")
        raise
    finally:
        if conn:
            conn.close()


def _clear_platform_paths(db_type: str, results: Dict[str, Any]):
    """Remove directory_path category entries from settings DB only (preserve other settings)."""
    db_path = get_db_path('settings')
    if not os.path.exists(db_path):
        results["details"].append({"db": db_type, "status": "skipped", "reason": "settings db not found"})
        return
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cur = conn.cursor()
        conn.execute('BEGIN')
        try:
            cur.execute("DELETE FROM settings WHERE platform = 'ios' AND category = ?", (PLATFORM_SETTINGS_CATEGORY,))
            results["details"].append({"db": db_type, "table": "settings", "status": "directory_paths_cleared"})
        except sqlite3.OperationalError as oe:
            results["details"].append({"db": db_type, "table": "settings", "status": "missing", "error": str(oe)})
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error clearing platform paths in settings: {e}")
        raise
    finally:
        if conn:
            conn.close()

