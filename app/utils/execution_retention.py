"""
Execution Retention and Data Management Module

This module provides:
1. 30-day automatic retention policy with cleanup
2. Export/Import functionality for execution data
3. Database maintenance utilities

Database-First Architecture - All data stored in database, no file dependencies
"""

import sqlite3
import json
import base64
import os
import logging
from datetime import datetime, timedelta
import zipfile
import io

logger = logging.getLogger(__name__)

def get_db_path():
    """Get the database path"""
    from utils.database import get_db_path as get_ios_db_path
    return get_ios_db_path()

def cleanup_old_executions(retention_days=30, dry_run=False):
    """
    Delete execution records older than retention_days
    
    Args:
        retention_days (int): Number of days to retain executions (default: 30)
        dry_run (bool): If True, only report what would be deleted without actually deleting
        
    Returns:
        dict: Summary of deleted records
    """
    try:
        logger.info(f"=" * 80)
        logger.info(f"EXECUTION RETENTION CLEANUP (Retention: {retention_days} days)")
        logger.info(f"Mode: {'DRY RUN' if dry_run else 'LIVE DELETION'}")
        logger.info(f"=" * 80)
        
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        cutoff_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"Cutoff date: {cutoff_str}")
        logger.info(f"Deleting executions older than: {cutoff_str}")
        
        # Find old executions
        cursor.execute('''
            SELECT DISTINCT test_execution_id, MIN(start_time) as earliest_time
            FROM execution_tracking
            WHERE start_time < ?
            GROUP BY test_execution_id
        ''', (cutoff_str,))
        
        old_executions = cursor.fetchall()
        
        if not old_executions:
            logger.info("✅ No old executions found to delete")
            conn.close()
            return {
                'deleted_executions': 0,
                'deleted_tracking_records': 0,
                'deleted_screenshots': 0,
                'deleted_reports': 0
            }
        
        logger.info(f"Found {len(old_executions)} old executions to delete:")
        for exec_row in old_executions:
            logger.info(f"  - {exec_row['test_execution_id']} (earliest: {exec_row['earliest_time']})")
        
        if dry_run:
            logger.info("\n⚠️  DRY RUN MODE - No actual deletion performed")
            conn.close()
            return {
                'deleted_executions': len(old_executions),
                'deleted_tracking_records': 0,
                'deleted_screenshots': 0,
                'deleted_reports': 0,
                'dry_run': True
            }
        
        # Delete old executions
        deleted_tracking = 0
        deleted_screenshots = 0
        deleted_reports = 0
        
        for exec_row in old_executions:
            execution_id = exec_row['test_execution_id']
            
            # Delete from screenshots table
            cursor.execute('DELETE FROM screenshots WHERE test_execution_id = ?', (execution_id,))
            deleted_screenshots += cursor.rowcount
            
            # Delete from execution_reports table
            cursor.execute('DELETE FROM execution_reports WHERE test_execution_id = ?', (execution_id,))
            deleted_reports += cursor.rowcount
            
            # Delete from execution_tracking table
            cursor.execute('DELETE FROM execution_tracking WHERE test_execution_id = ?', (execution_id,))
            deleted_tracking += cursor.rowcount
            
            logger.info(f"✅ Deleted execution: {execution_id}")
        
        conn.commit()
        conn.close()
        
        summary = {
            'deleted_executions': len(old_executions),
            'deleted_tracking_records': deleted_tracking,
            'deleted_screenshots': deleted_screenshots,
            'deleted_reports': deleted_reports
        }
        
        logger.info(f"\n" + "=" * 80)
        logger.info(f"CLEANUP SUMMARY")
        logger.info(f"=" * 80)
        logger.info(f"Deleted executions:        {summary['deleted_executions']}")
        logger.info(f"Deleted tracking records:  {summary['deleted_tracking_records']}")
        logger.info(f"Deleted screenshots:       {summary['deleted_screenshots']}")
        logger.info(f"Deleted reports:           {summary['deleted_reports']}")
        logger.info(f"=" * 80)
        
        return summary
        
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return None

def export_executions(output_file=None, execution_ids=None):
    """
    Export execution data to a portable JSON format
    
    Args:
        output_file (str): Path to output file (default: auto-generated)
        execution_ids (list): List of execution IDs to export (default: all)
        
    Returns:
        str: Path to exported file
    """
    try:
        logger.info(f"=" * 80)
        logger.info(f"EXPORTING EXECUTION DATA")
        logger.info(f"=" * 80)
        
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Determine which executions to export
        if execution_ids:
            placeholders = ','.join('?' * len(execution_ids))
            where_clause = f"WHERE test_execution_id IN ({placeholders})"
            params = execution_ids
        else:
            where_clause = ""
            params = []
        
        # Export execution_tracking records
        cursor.execute(f'SELECT * FROM execution_tracking {where_clause}', params)
        tracking_records = [dict(row) for row in cursor.fetchall()]
        
        # Export screenshots (with BLOB data as base64)
        cursor.execute(f'SELECT * FROM screenshots {where_clause}', params)
        screenshot_records = []
        for row in cursor.fetchall():
            record = dict(row)
            # Convert BLOB to base64 for portability
            if record.get('screenshot_blob'):
                record['screenshot_blob'] = base64.b64encode(record['screenshot_blob']).decode('utf-8')
            screenshot_records.append(record)
        
        # Export execution_reports
        if execution_ids:
            cursor.execute(f'SELECT * FROM execution_reports WHERE test_execution_id IN ({placeholders})', execution_ids)
        else:
            cursor.execute('SELECT * FROM execution_reports')
        report_records = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        
        # Build export data structure
        export_data = {
            'export_metadata': {
                'export_date': datetime.now().isoformat(),
                'platform': 'android',
                'version': '1.0',
                'record_counts': {
                    'execution_tracking': len(tracking_records),
                    'screenshots': len(screenshot_records),
                    'execution_reports': len(report_records)
                }
            },
            'execution_tracking': tracking_records,
            'screenshots': screenshot_records,
            'execution_reports': report_records
        }
        
        # Generate output filename if not provided
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'execution_export_{timestamp}.json'
        
        # Write to file
        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        logger.info(f"✅ Exported {len(tracking_records)} execution tracking records")
        logger.info(f"✅ Exported {len(screenshot_records)} screenshot records")
        logger.info(f"✅ Exported {len(report_records)} execution report records")
        logger.info(f"✅ Export file: {output_file}")
        logger.info(f"=" * 80)
        
        return output_file
        
    except Exception as e:
        logger.error(f"Error exporting executions: {e}")
        import traceback
        traceback.print_exc()
        return None

def import_executions(import_file):
    """
    Import execution data from exported JSON file
    
    Args:
        import_file (str): Path to import file
        
    Returns:
        dict: Summary of imported records
    """
    try:
        logger.info(f"=" * 80)
        logger.info(f"IMPORTING EXECUTION DATA")
        logger.info(f"=" * 80)
        logger.info(f"Import file: {import_file}")
        
        if not os.path.exists(import_file):
            logger.error(f"Import file not found: {import_file}")
            return None
        
        # Load import data
        with open(import_file, 'r') as f:
            import_data = json.load(f)
        
        logger.info(f"Export metadata: {import_data.get('export_metadata', {})}")
        
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        
        imported_tracking = 0
        imported_screenshots = 0
        imported_reports = 0
        
        # Import execution_tracking records
        for record in import_data.get('execution_tracking', []):
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO execution_tracking
                    (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
                     status, retry_count, max_retries, last_error, start_time, end_time, in_progress,
                     execution_result, test_case_id, test_execution_id, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record.get('suite_id'), record.get('test_idx'), record.get('step_idx'),
                    record.get('filename'), record.get('action_type'), record.get('action_params'),
                    record.get('action_id'), record.get('status'), record.get('retry_count'),
                    record.get('max_retries'), record.get('last_error'), record.get('start_time'),
                    record.get('end_time'), record.get('in_progress'), record.get('execution_result'),
                    record.get('test_case_id'), record.get('test_execution_id'), record.get('timestamp')
                ))
                imported_tracking += 1
            except Exception as e:
                logger.warning(f"Could not import tracking record: {e}")
        
        # Import screenshots
        for record in import_data.get('screenshots', []):
            try:
                # Convert base64 back to BLOB
                screenshot_blob = None
                if record.get('screenshot_blob'):
                    screenshot_blob = base64.b64decode(record['screenshot_blob'])
                
                cursor.execute('''
                    INSERT OR REPLACE INTO screenshots
                    (suite_id, test_idx, step_idx, filename, path, timestamp, action_id,
                     test_execution_id, test_case_id, screenshot_blob, screenshot_mime)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record.get('suite_id'), record.get('test_idx'), record.get('step_idx'),
                    record.get('filename'), record.get('path'), record.get('timestamp'),
                    record.get('action_id'), record.get('test_execution_id'), record.get('test_case_id'),
                    screenshot_blob, record.get('screenshot_mime', 'image/png')
                ))
                imported_screenshots += 1
            except Exception as e:
                logger.warning(f"Could not import screenshot record: {e}")
        
        # Import execution_reports
        for record in import_data.get('execution_reports', []):
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO execution_reports
                    (report_id, test_execution_id, suite_id, test_case_id, platform, status,
                     start_time, end_time, duration, error_message, report_data, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record.get('report_id'), record.get('test_execution_id'), record.get('suite_id'),
                    record.get('test_case_id'), record.get('platform'), record.get('status'),
                    record.get('start_time'), record.get('end_time'), record.get('duration'),
                    record.get('error_message'), record.get('report_data'), record.get('created_at')
                ))
                imported_reports += 1
            except Exception as e:
                logger.warning(f"Could not import report record: {e}")
        
        conn.commit()
        conn.close()
        
        summary = {
            'imported_tracking_records': imported_tracking,
            'imported_screenshots': imported_screenshots,
            'imported_reports': imported_reports
        }
        
        logger.info(f"\n" + "=" * 80)
        logger.info(f"IMPORT SUMMARY")
        logger.info(f"=" * 80)
        logger.info(f"Imported tracking records:  {summary['imported_tracking_records']}")
        logger.info(f"Imported screenshots:       {summary['imported_screenshots']}")
        logger.info(f"Imported reports:           {summary['imported_reports']}")
        logger.info(f"=" * 80)
        
        return summary
        
    except Exception as e:
        logger.error(f"Error importing executions: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return None

