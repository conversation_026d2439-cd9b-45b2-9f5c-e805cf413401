"""
Standalone Report Generator Module

This module is completely independent and handles:
1. Adding screenshot_blob columns to execution_tracking table
2. Reading execution data from execution_tracking table
3. Generating data.json structure
4. Inserting report into execution_reports table

No dependencies on existing report generation methods.
"""

import sqlite3
import json
import logging
import base64
from datetime import datetime
from typing import Dict, List, Optional, Any
import os

logger = logging.getLogger(__name__)


class StandaloneReportGenerator:
    """
    Standalone report generator that works exclusively with execution_tracking table.
    """
    
    def __init__(self, db_path: str):
        """
        Initialize the standalone report generator.
        
        Args:
            db_path: Path to the SQLite database
        """
        self.db_path = db_path
        self._ensure_schema()
    
    def _ensure_schema(self):
        """
        Ensure execution_tracking table has screenshot_blob columns.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check existing columns
            cursor.execute("PRAGMA table_info(execution_tracking)")
            columns = {col[1] for col in cursor.fetchall()}
            
            # Add screenshot_blob if missing
            if 'screenshot_blob' not in columns:
                logger.info("Adding screenshot_blob column to execution_tracking")
                cursor.execute("""
                    ALTER TABLE execution_tracking 
                    ADD COLUMN screenshot_blob BLOB
                """)
            
            # Add screenshot_thumb_blob if missing
            if 'screenshot_thumb_blob' not in columns:
                logger.info("Adding screenshot_thumb_blob column to execution_tracking")
                cursor.execute("""
                    ALTER TABLE execution_tracking 
                    ADD COLUMN screenshot_thumb_blob BLOB
                """)
            
            # Add screenshot_mime if missing
            if 'screenshot_mime' not in columns:
                logger.info("Adding screenshot_mime column to execution_tracking")
                cursor.execute("""
                    ALTER TABLE execution_tracking 
                    ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'
                """)
            
            conn.commit()
            conn.close()
            logger.info("✅ Schema validation complete")
            
        except Exception as e:
            logger.error(f"Error ensuring schema: {e}")
            raise
    
    def insert_screenshot_blob(
        self, 
        execution_id: str, 
        step_idx: int, 
        screenshot_blob: bytes,
        screenshot_thumb_blob: Optional[bytes] = None,
        mime_type: str = 'image/png'
    ) -> bool:
        """
        Insert screenshot blob into execution_tracking table for a specific step.
        
        Args:
            execution_id: Test execution ID
            step_idx: Step index
            screenshot_blob: Screenshot image as bytes
            screenshot_thumb_blob: Optional thumbnail as bytes
            mime_type: MIME type of the image
            
        Returns:
            Success status
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Update the specific step with screenshot blob
            cursor.execute("""
                UPDATE execution_tracking
                SET screenshot_blob = ?,
                    screenshot_thumb_blob = ?,
                    screenshot_mime = ?
                WHERE test_execution_id = ? AND step_idx = ?
            """, (screenshot_blob, screenshot_thumb_blob, mime_type, execution_id, step_idx))
            
            rows_updated = cursor.rowcount
            conn.commit()
            conn.close()
            
            if rows_updated > 0:
                logger.debug(f"✅ Inserted screenshot blob for execution {execution_id}, step {step_idx}")
                return True
            else:
                logger.warning(f"⚠️ No rows updated for execution {execution_id}, step {step_idx}")
                return False
                
        except Exception as e:
            logger.error(f"Error inserting screenshot blob: {e}")
            return False
    
    def read_execution_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Read all execution data from execution_tracking table.
        
        Args:
            execution_id: Test execution ID
            
        Returns:
            Dictionary containing all execution tracking records
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get all records for this execution
            cursor.execute("""
                SELECT * FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY test_idx, step_idx
            """, (execution_id,))
            
            records = [dict(row) for row in cursor.fetchall()]
            conn.close()
            
            logger.info(f"✅ Read {len(records)} execution tracking records for {execution_id}")
            return {
                'execution_id': execution_id,
                'records': records,
                'total_steps': len(records)
            }
            
        except Exception as e:
            logger.error(f"Error reading execution data: {e}")
            return None
    
    def generate_data_json(self, execution_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Generate data.json structure from execution tracking records.
        
        Args:
            execution_data: Dictionary from read_execution_data()
            
        Returns:
            data.json structure as dictionary
        """
        try:
            records = execution_data.get('records', [])
            execution_id = execution_data.get('execution_id')
            
            if not records:
                logger.warning("No records to generate data.json")
                return None
            
            # Group records by test case
            test_cases_map = {}
            suite_id = None
            
            for record in records:
                test_idx = record.get('test_idx', 0)
                suite_id = record.get('suite_id') or suite_id
                
                if test_idx not in test_cases_map:
                    test_cases_map[test_idx] = {
                        'name': record.get('filename', f'Test Case {test_idx}'),
                        'test_case_id': record.get('test_case_id', ''),
                        'status': 'passed',
                        'steps': []
                    }
                
                # Create step data
                action_id = record.get('action_id', f"step_{record.get('step_idx', 0)}")
                step_status = record.get('status', 'unknown')
                
                # Update test case status if any step failed
                if step_status == 'failed':
                    test_cases_map[test_idx]['status'] = 'failed'
                
                step_data = {
                    'name': f"Step {record.get('step_idx', 0)}: {record.get('action_type', 'unknown')}",
                    'action_id': action_id,
                    'action_type': record.get('action_type', 'unknown'),
                    'type': record.get('action_type', 'unknown'),
                    'status': step_status,
                    'duration': '0ms',
                    'timestamp': record.get('start_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                    'screenshot_filename': f"{action_id}.png",
                    'error_message': record.get('last_error', '')
                }
                
                # Add screenshot as base64 data URL if blob exists
                if record.get('screenshot_blob'):
                    try:
                        screenshot_base64 = base64.b64encode(record['screenshot_blob']).decode('utf-8')
                        mime_type = record.get('screenshot_mime', 'image/png')
                        step_data['screenshot_data_url'] = f"data:{mime_type};base64,{screenshot_base64}"
                    except Exception as e:
                        logger.warning(f"Could not encode screenshot blob: {e}")
                
                test_cases_map[test_idx]['steps'].append(step_data)
            
            # Convert to list
            test_cases_list = [test_cases_map[idx] for idx in sorted(test_cases_map.keys())]
            
            # Calculate statistics
            passed_count = sum(1 for tc in test_cases_list if tc['status'] == 'passed')
            failed_count = sum(1 for tc in test_cases_list if tc['status'] == 'failed')
            overall_status = 'passed' if failed_count == 0 else 'failed'
            
            # Build data.json structure
            data_json = {
                'name': f'Execution {execution_id}',
                'execution_id': execution_id,
                'suite_id': suite_id,
                'testCases': test_cases_list,
                'status': overall_status,
                'passed': passed_count,
                'failed': failed_count,
                'skipped': 0,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'source': 'standalone_report_generator'
            }
            
            logger.info(f"✅ Generated data.json with {len(test_cases_list)} test cases")
            return data_json
            
        except Exception as e:
            logger.error(f"Error generating data.json: {e}")
            import traceback
            traceback.print_exc()
            return None

    def insert_execution_report(
        self,
        execution_id: str,
        data_json: Dict[str, Any],
        platform: str = 'ios',
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> bool:
        """
        Insert report data into execution_reports table.

        Args:
            execution_id: Test execution ID
            data_json: The data.json structure
            platform: Platform (ios/android)
            start_time: Optional start time
            end_time: Optional end time

        Returns:
            Success status
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Ensure execution_reports table exists
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS execution_reports (
                    report_id TEXT PRIMARY KEY,
                    test_execution_id TEXT,
                    suite_id TEXT,
                    test_case_id TEXT,
                    platform TEXT,
                    status TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    duration INTEGER,
                    error_message TEXT,
                    screenshot_data BLOB,
                    report_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Generate report_id
            report_id = f"{execution_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Extract metadata from data_json
            suite_id = data_json.get('suite_id')
            status = data_json.get('status', 'unknown')

            # Calculate duration if times provided
            duration = 0
            if start_time and end_time:
                try:
                    start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                    end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                    duration = int((end_dt - start_dt).total_seconds())
                except Exception as e:
                    logger.warning(f"Could not calculate duration: {e}")

            # Convert data_json to JSON string
            report_data_json = json.dumps(data_json, default=str)

            # Insert into execution_reports
            cursor.execute("""
                INSERT OR REPLACE INTO execution_reports
                (report_id, test_execution_id, suite_id, test_case_id, platform,
                 status, start_time, end_time, duration, report_data, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                report_id,
                execution_id,
                suite_id,
                None,  # test_case_id (for suite execution)
                platform,
                status,
                start_time or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                end_time or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                duration,
                report_data_json,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))

            conn.commit()
            conn.close()

            logger.info(f"✅ Inserted execution report: {report_id}")
            logger.info(f"   Execution ID: {execution_id}")
            logger.info(f"   Suite ID: {suite_id}")
            logger.info(f"   Status: {status}")
            logger.info(f"   Data size: {len(report_data_json)} bytes")

            return True

        except Exception as e:
            logger.error(f"Error inserting execution report: {e}")
            import traceback
            traceback.print_exc()
            return False

    def generate_complete_report(
        self,
        execution_id: str,
        platform: str = 'ios',
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Complete workflow: Read execution data, generate data.json, insert report.

        Args:
            execution_id: Test execution ID
            platform: Platform (ios/android)
            start_time: Optional start time
            end_time: Optional end time

        Returns:
            Generated data.json structure or None if failed
        """
        try:
            logger.info("=" * 80)
            logger.info(f"STANDALONE REPORT GENERATION")
            logger.info(f"Execution ID: {execution_id}")
            logger.info(f"Platform: {platform}")
            logger.info("=" * 80)

            # Step 1: Read execution data from execution_tracking
            logger.info("Step 1: Reading execution data from execution_tracking table...")
            execution_data = self.read_execution_data(execution_id)

            if not execution_data or not execution_data.get('records'):
                logger.error("❌ No execution data found")
                return None

            logger.info(f"✅ Found {execution_data['total_steps']} steps")

            # Step 2: Generate data.json structure
            logger.info("Step 2: Generating data.json structure...")
            data_json = self.generate_data_json(execution_data)

            if not data_json:
                logger.error("❌ Failed to generate data.json")
                return None

            logger.info(f"✅ Generated data.json with {len(data_json.get('testCases', []))} test cases")

            # Step 3: Insert into execution_reports table
            logger.info("Step 3: Inserting report into execution_reports table...")
            success = self.insert_execution_report(
                execution_id=execution_id,
                data_json=data_json,
                platform=platform,
                start_time=start_time,
                end_time=end_time
            )

            if not success:
                logger.error("❌ Failed to insert execution report")
                return None

            logger.info("✅ Report generation complete!")
            logger.info("=" * 80)

            return data_json

        except Exception as e:
            logger.error(f"Error in complete report generation: {e}")
            import traceback
            traceback.print_exc()
            return None


# Convenience functions for easy usage
def create_report_generator(platform: str = 'ios') -> StandaloneReportGenerator:
    """
    Create a StandaloneReportGenerator instance for the specified platform.

    Args:
        platform: 'ios' or 'android'

    Returns:
        StandaloneReportGenerator instance
    """
    if platform.lower() == 'android':
        db_path = '/Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db'
    else:
        db_path = '/Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db'

    return StandaloneReportGenerator(db_path)


def generate_report_from_execution(
    execution_id: str,
    platform: str = 'ios',
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Convenience function to generate a complete report.

    Args:
        execution_id: Test execution ID
        platform: 'ios' or 'android'
        start_time: Optional start time
        end_time: Optional end time

    Returns:
        Generated data.json structure or None if failed
    """
    generator = create_report_generator(platform)
    return generator.generate_complete_report(
        execution_id=execution_id,
        platform=platform,
        start_time=start_time,
        end_time=end_time
    )

