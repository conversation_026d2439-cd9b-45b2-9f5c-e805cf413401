"""
State Manager for app

This module contains all global state variables, session management functions,
and utility classes used throughout the application.
"""

import os
import sys
import uuid
import logging
import threading
import signal
from pathlib import Path
from flask import session, request

# Set up logger
logger = logging.getLogger(__name__)

# ============================================================================
# SOCKETIO DUMMY CLASS
# ============================================================================

class DummySocketIO:
    """Dummy SocketIO class to avoid errors when SocketIO is not available"""
    def emit(self, event, data):
        pass

socketio = DummySocketIO()

# ============================================================================
# DEVICE CONTROLLER MAPS
# ============================================================================

# Instead of using global variables, use dictionaries to store device controllers by device ID
device_controllers = {}
players = {}
action_factories = {}

# ============================================================================
# GLOBAL VARIABLES
# ============================================================================

# Current device state
current_device = None
current_device_id = None

# Recording state
recording_actions = []
action_factory = None
is_test_suite_execution = False  # Global flag to track if we're executing a test suite

# Report directories
current_report_dir = None
current_report_timestamp = None
current_screenshots_dir = None
current_action_logs = []  # Store action logs for the current test run

# Test execution state
current_test_idx = None
current_step_idx = None
current_suite_id = None

# Step execution timeout
step_execution_timer = None
step_timeout_occurred = False

# ============================================================================
# SESSION MANAGEMENT FUNCTIONS
# ============================================================================

def get_client_session_id():
    """Get the client session ID from request headers or body"""
    # Try to get from header first
    client_session_id = request.headers.get('X-Client-Session-ID')
    
    # If not in header, try to get from request body
    if not client_session_id and request.is_json:
        client_session_id = request.json.get('client_session_id')
    
    # If still not found, fall back to Flask session
    if not client_session_id:
        if 'client_session_id' not in session:
            session['client_session_id'] = f"server_{str(uuid.uuid4())[:8]}"
            logger.info(f"Generated new server-side session ID: {session['client_session_id']}")
        client_session_id = session['client_session_id']
    
    return client_session_id

def get_session_device_id():
    """Get the current device ID for this session"""
    client_session_id = get_client_session_id()
    return session.get(f'current_device_id_{client_session_id}')

def set_session_device_id(device_id):
    """Set the current device ID for this session"""
    client_session_id = get_client_session_id()
    session[f'current_device_id_{client_session_id}'] = device_id

def clear_session_device_id():
    """Clear the current device ID for this session"""
    client_session_id = get_client_session_id()
    session.pop(f'current_device_id_{client_session_id}', None)

def get_session_id():
    """Get or create a unique session ID for this session"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())[:8]  # Use first 8 characters for brevity
        logger.info(f"Generated new session ID: {session['session_id']}")
    return session['session_id']

def get_current_environment_id_from_session():
    """Get current environment ID from session"""
    return session.get('current_environment_id')

def ensure_default_environment():
    """Ensure a default environment exists and is set if no environment is currently selected"""
    try:
        from app.utils.directory_paths_db import directory_paths_db
        
        # Check if there's already an environment selected
        current_env_id = get_current_environment_id_from_session()
        if current_env_id is not None:
            # Verify the environment still exists
            env_details = directory_paths_db.get_environment_by_id(current_env_id)
            if env_details:
                return current_env_id  # Current environment is valid
            else:
                # Clear invalid environment from session
                session.pop('current_environment_id', None)
                logger.warning(f"Cleared invalid environment ID {current_env_id} from session")
        
        # Check if any environments exist
        environments = directory_paths_db.get_all_environments()
        if environments:
            # Use the first available environment
            first_env = environments[0]
            env_key = first_env.get('environment_id') or first_env.get('id')
            session['current_environment_id'] = env_key
            logger.info(f"Auto-selected existing environment: {first_env.get('name')} (ID: {env_key})")
            return env_key
        else:
            # Create a default environment
            default_env_id = directory_paths_db.create_environment("Default")
            if default_env_id:
                session['current_environment_id'] = default_env_id
                logger.info(f"Created and selected default environment (ID: {default_env_id})")
                return default_env_id
            else:
                logger.warning("Failed to create default environment")
                return None
    except Exception as e:
        logger.error(f"Error ensuring default environment: {e}")
        return None

# ============================================================================
# UTILITY CLASSES
# ============================================================================

class StepExecutionTimeout:
    """Class to handle step execution timeout"""
    def __init__(self, timeout, step_name, test_case_name):
        self.timeout = timeout
        self.step_name = step_name
        self.test_case_name = test_case_name
        self.timer = None
        self.timeout_occurred = False
    
    def start(self):
        """Start the timeout timer"""
        global step_timeout_occurred
        step_timeout_occurred = False
        
        def timeout_handler():
            """Handler called when timeout occurs"""
            global step_timeout_occurred
            step_timeout_occurred = True
            logger.error(f"========== STEP TIMEOUT DETECTED ==========")
            logger.error(f"Step execution timed out after {self.timeout} seconds")
            logger.error(f"Test Case: {self.test_case_name}")
            logger.error(f"Step: {self.step_name}")
            logger.error(f"========== STOPPING TEST EXECUTION ==========")
            
            # Force flush the logs to ensure they appear in real-time
            sys.stdout.flush()
            sys.stderr.flush()
            
            # Raise an exception in the main thread to interrupt execution
            os.kill(os.getpid(), signal.SIGUSR1)
        
        self.timer = threading.Timer(self.timeout, timeout_handler)
        self.timer.daemon = True  # Make the timer a daemon thread
        self.timer.start()
    
    def cancel(self):
        """Cancel the timeout timer"""
        if self.timer:
            self.timer.cancel()

class IndexHolder:
    """Class to hold an index value that can be shared across threads"""
    def __init__(self, initial_value=0):
        self.value = initial_value
        self.lock = threading.Lock()
    
    def increment(self):
        """Increment the index value"""
        with self.lock:
            self.value += 1
            return self.value
    
    def reset(self):
        """Reset the index value to 0"""
        with self.lock:
            self.value = 0
    
    def get(self):
        """Get the current index value"""
        with self.lock:
            return self.value
    
    def set(self, value):
        """Set the index value"""
        with self.lock:
            self.value = value

# ============================================================================
# MANAGERS AND DIRECTORIES (initialized in app_factory.py)
# ============================================================================

# These will be initialized in app_factory.py
test_suites_manager = None
import_export_manager = None
test_case_manager = None

TEST_CASES_DIR = None
REFERENCE_IMAGES_DIR = None
SCREENSHOTS_DIR = None
screenshots_dir = None
app_screenshots_dir = None

# ============================================================================
# CLEANUP FUNCTIONS
# ============================================================================

def cleanup_screenshots():
    """Clean up screenshots in the root screenshots directory"""
    if screenshots_dir and os.path.exists(screenshots_dir):
        logger.info(f"Cleaning up all screenshots in {screenshots_dir}")
        for filename in os.listdir(screenshots_dir):
            file_path = os.path.join(screenshots_dir, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
            except Exception as e:
                logger.error(f"Error deleting {file_path}: {e}")

def cleanup_app_screenshots():
    """Clean up screenshots in the app screenshots directory"""
    if app_screenshots_dir and os.path.exists(app_screenshots_dir):
        logger.info(f"Cleaning up all screenshots in {app_screenshots_dir}")
        for filename in os.listdir(app_screenshots_dir):
            file_path = os.path.join(app_screenshots_dir, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
            except Exception as e:
                logger.error(f"Error deleting {file_path}: {e}")

def shutdown_handler(signum, frame):
    """Handler for shutdown signals"""
    logger.info("Shutting down and cleaning up all processes...")
    
    # Shutdown all device controllers
    for device_id, controller in device_controllers.items():
        try:
            logger.info(f"Shutting down device controller for device {device_id}")
            controller.shutdown()
        except Exception as e:
            logger.error(f"Error during device controller shutdown for device {device_id}: {e}")
    
    # Clear the device controllers dictionary
    device_controllers.clear()
    players.clear()
    action_factories.clear()
    
    # Call additional cleanup functions
    cleanup_screenshots()
    logger.info("App shutdown complete")
    
    # Exit the application
    sys.exit(0)

def setup_signal_handlers():
    """Setup signal handlers - should only be called from main thread"""
    try:
        if threading.current_thread() is threading.main_thread():
            signal.signal(signal.SIGINT, shutdown_handler)
            signal.signal(signal.SIGTERM, shutdown_handler)
            logger.info("Signal handlers registered successfully")
        else:
            logger.warning("setup_signal_handlers called from non-main thread - skipping")
    except Exception as e:
        logger.error(f"Error setting up signal handlers: {e}")

