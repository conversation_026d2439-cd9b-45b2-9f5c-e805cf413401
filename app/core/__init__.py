"""
Core module for app (iOS) - contains app initialization and state management.

This module provides backward compatibility by re-exporting all core components.
"""

from .app_factory import create_app, get_app, app as _app_instance

# Re-export app - it will be initialized when get_app() is called
app = _app_instance
from .state_manager import (
    # Device controller maps
    device_controllers,
    players,
    action_factories,
    
    # Global variables
    current_device,
    current_device_id,
    recording_actions,
    action_factory,
    is_test_suite_execution,
    
    # Report directories
    current_report_dir,
    current_report_timestamp,
    current_screenshots_dir,
    current_action_logs,
    
    # Step execution timeout
    step_execution_timer,
    step_timeout_occurred,
    
    # Classes
    StepExecutionTimeout,
    IndexHolder,
    
    # Session management functions
    get_client_session_id,
    get_session_device_id,
    set_session_device_id,
    clear_session_device_id,
    get_session_id,
    get_current_environment_id_from_session,
    ensure_default_environment,
    
    # Managers
    test_suites_manager,
    import_export_manager,
    test_case_manager,
    
    # Directories
    TEST_CASES_DIR,
    REFERENCE_IMAGES_DIR,
    SCREENSHOTS_DIR,
    screenshots_dir,
    app_screenshots_dir,
    
    # SocketIO
    socketio,
    DummySocketIO,
    
    # Cleanup functions
    cleanup_screenshots,
    cleanup_app_screenshots,
    setup_signal_handlers,
    shutdown_handler,
)

__all__ = [
    'create_app',
    'app',
    'device_controllers',
    'players',
    'action_factories',
    'current_device',
    'current_device_id',
    'recording_actions',
    'action_factory',
    'is_test_suite_execution',
    'current_report_dir',
    'current_report_timestamp',
    'current_screenshots_dir',
    'current_action_logs',
    'step_execution_timer',
    'step_timeout_occurred',
    'StepExecutionTimeout',
    'IndexHolder',
    'get_client_session_id',
    'get_session_device_id',
    'set_session_device_id',
    'clear_session_device_id',
    'get_session_id',
    'get_current_environment_id_from_session',
    'ensure_default_environment',
    'test_suites_manager',
    'import_export_manager',
    'test_case_manager',
    'TEST_CASES_DIR',
    'REFERENCE_IMAGES_DIR',
    'SCREENSHOTS_DIR',
    'screenshots_dir',
    'app_screenshots_dir',
    'socketio',
    'DummySocketIO',
    'cleanup_screenshots',
    'cleanup_app_screenshots',
    'setup_signal_handlers',
    'shutdown_handler',
]

