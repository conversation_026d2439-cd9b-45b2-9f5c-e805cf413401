{"environments": [{"name": "Development", "variables": [{"name": "baseUrl", "initial_value": "https://dev-api.example.com", "current_value": "https://dev-api.example.com"}, {"name": "username", "initial_value": "testuser", "current_value": "testuser"}, {"name": "password", "initial_value": "testpass", "current_value": "testpass"}]}, {"name": "Testing", "variables": [{"name": "baseUrl", "initial_value": "https://test-api.example.com", "current_value": "https://test-api.example.com"}, {"name": "username", "initial_value": "qauser", "current_value": "qauser"}, {"name": "password", "initial_value": "qapass", "current_value": "qapass"}]}]}