"""
Mobile App Automation Tool - iOS Platform

This is the main entry point for the iOS platform app.
The application has been refactored into modular components for better maintainability.

Module Structure:
  - core/: Core application initialization and state management
  - routes_modules/: Route handlers organized by functionality
  - helpers/: Helper functions and utilities

This file maintains backward compatibility by re-exporting all components.
"""

import os
import sys
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# ============================================================================
# CORE COMPONENTS
# ============================================================================

# Import Flask app instance
from app.core.app_factory import app, create_app

# Import all state variables for backward compatibility
from app.core.state_manager import (
    # Device controller maps
    device_controllers,
    players,
    action_factories,

    # Global variables
    current_device,
    current_device_id,
    recording_actions,
    action_factory,
    is_test_suite_execution,

    # Report directories
    current_report_dir,
    current_report_timestamp,
    current_screenshots_dir,
    current_action_logs,

    # Test execution state
    current_test_idx,
    current_step_idx,
    current_suite_id,

    # Step execution timeout
    step_execution_timer,
    step_timeout_occurred,

    # Classes
    StepExecutionTimeout,
    IndexHolder,

    # Session management
    get_client_session_id,
    get_session_device_id,
    set_session_device_id,
    clear_session_device_id,
    get_session_id,
    get_current_environment_id_from_session,
    ensure_default_environment,

    # Managers
    test_suites_manager,
    import_export_manager,
    test_case_manager,

    # Directories
    TEST_CASES_DIR,
    REFERENCE_IMAGES_DIR,
    SCREENSHOTS_DIR,
    screenshots_dir,
    app_screenshots_dir,

    # SocketIO
    socketio,
    DummySocketIO,

    # Cleanup
    cleanup_screenshots,
    cleanup_app_screenshots,
    setup_signal_handlers,
    shutdown_handler,
)

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

from app.helpers.config_helpers import *
from app.helpers.directory_helpers import *

# ============================================================================
# ROUTE MODULES
# ============================================================================

# Route modules are now imported in app_factory.py via _register_route_modules()
# This avoids circular import issues

# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    # Setup signal handlers in main thread
    setup_signal_handlers()
    
    # Get port from environment variable or config file
    port = int(os.environ.get("FLASK_PORT", 8081))  # Default to 8081 for Android
    try:
        import config
        if not os.environ.get("FLASK_PORT"):
            port = getattr(config, "FLASK_PORT", 8081)
    except ImportError:
        pass
    
    print(f"Starting Mobile App Automation Tool (Android) on port {port}...")
    SECURE = os.getenv("SECURE_BUILD", "False").lower() == "true"
    host = "127.0.0.1" if SECURE else "0.0.0.0"
    debug = False if SECURE else True
    app.run(host=host, port=port, debug=debug)
