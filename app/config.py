"""
Configuration module for iOS app.
This module provides configuration settings and compatibility with legacy code.
"""

import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# Base configuration
BASE_DIR = Path(__file__).resolve().parent.parent
WORKSPACE_ROOT = Path(os.environ.get('AUTOMATION_WORKSPACE', Path.home() / 'MobileAutomationWorkspace'))
IOS_WORKSPACE = WORKSPACE_ROOT / 'ios'

# Default directories for iOS
DEFAULT_DIRECTORIES = {
    'TEST_CASES': IOS_WORKSPACE / 'test_cases',
    'REPORTS': IOS_WORKSPACE / 'reports',
    'SCREENSHOTS': IOS_WORKSPACE / 'screenshots',
    'REFERENCE_IMAGES': IOS_WORKSPACE / 'reference_images',
    'TEST_SUITES': IOS_WORKSPACE / 'test_suites',
    'RESULTS': IOS_WORKSPACE / 'reports' / 'suites',
    'RECORDINGS': IOS_WORKSPACE / 'recordings',
    'TEMP_FILES': IOS_WORKSPACE / 'temp',
}

# Ensure directories exist
for directory in DEFAULT_DIRECTORIES.values():
    try:
        directory.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logger.warning(f"Could not create directory {directory}: {e}")

# Screenshot settings
SCREENSHOT_SETTINGS = {
    'format': 'png',
    'quality': 90,
    'timeout': 30,
    'retry_count': 3,
}

# Default configuration values
DEFAULT_CONFIG = {
    'testcases_dir': 'test_cases',
    'reports_dir': 'reports_ios',
    'reference_images_dir': 'reference_images',
    'files_to_push_dir': 'files_to_push',
    'screenshot_settings': SCREENSHOT_SETTINGS,
}

# Appium configuration
APPIUM_CONFIG = {
    'PORT': 4723,
    'WDA_PORT': 8200,
    'TIMEOUT': 600,
}

# Flask configuration
class FlaskConfig:
    PORT = 8080
    DEBUG = True
    HOST = '0.0.0.0'

# Export for compatibility
DIRECTORIES = DEFAULT_DIRECTORIES
