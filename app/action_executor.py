"""
Enhanced Action Executor with AI Healing for iOS Platform
"""
import logging
import time
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Import AI healing components
try:
    from utils.ai_integration import AIHealingOrchestrator, HealingResult
    AI_HEALING_AVAILABLE = True
except ImportError as e:
    logging.getLogger(__name__).warning(f"AI healing not available: {e}")
    AI_HEALING_AVAILABLE = False

logger = logging.getLogger(__name__)

class ActionExecutor:
    """Enhanced action executor with AI healing for retry functionality"""

    def __init__(self):
        self.device_controller = None
        self.ai_healing_orchestrator = None
        self._initialize_device_controller()
        self._initialize_ai_healing()
    
    def _initialize_device_controller(self):
        """Initialize the device controller"""
        try:
            from app.appium_device_controller import AppiumDeviceController
            self.device_controller = AppiumDeviceController()
            logger.info("Device controller initialized for action executor")
        except Exception as e:
            logger.error(f"Failed to initialize device controller: {str(e)}")
            self.device_controller = None

    def _initialize_ai_healing(self):
        """Initialize AI healing orchestrator"""
        if not AI_HEALING_AVAILABLE:
            logger.info("AI healing not available - skipping initialization")
            return

        try:
            # Check if AI healing is enabled
            ai_enabled = os.getenv('AI_HEALING_ENABLED', 'true').lower() == 'true'
            if not ai_enabled:
                logger.info("AI healing disabled via environment variable")
                return

            # Initialize AI healing orchestrator
            self.ai_healing_orchestrator = AIHealingOrchestrator(
                output_dir="ai_healing_outputs/ios",
                max_attempts=int(os.getenv('AI_HEALING_MAX_ATTEMPTS', '3')),
                confidence_threshold=float(os.getenv('AI_HEALING_CONFIDENCE_THRESHOLD', '0.6'))
            )
            logger.info("AI healing orchestrator initialized for iOS platform")

        except Exception as e:
            logger.error(f"Failed to initialize AI healing: {e}")
            self.ai_healing_orchestrator = None
    
    def execute_action(self, action, step_index=0):
        """
        Execute a single action with AI healing fallback

        Args:
            action (dict): Action definition
            step_index (int): Step index for tracking

        Returns:
            dict: Execution result with AI healing information
        """
        start_time = time.time()

        try:
            if not self.device_controller:
                return {
                    'success': False,
                    'error': 'Device controller not available',
                    'duration': 0,
                    'ai_healing_used': False
                }

            action_type = action.get('type', '')
            logger.info(f"Executing action: {action_type}")

            # Try original action execution first
            try:
                result = self._execute_original_action(action)
                result['ai_healing_used'] = False
                result['duration'] = time.time() - start_time
                return result

            except Exception as original_error:
                logger.warning(f"Original action failed: {original_error}")

                # Attempt AI healing if available
                if self.ai_healing_orchestrator and hasattr(self.device_controller, 'driver'):
                    try:
                        healing_result = self._attempt_ai_healing(action, original_error)
                        if healing_result['success']:
                            healing_result['duration'] = time.time() - start_time
                            return healing_result
                    except Exception as healing_error:
                        logger.error(f"AI healing failed: {healing_error}")

                # If AI healing failed or not available, return original error
                return {
                    'success': False,
                    'error': str(original_error),
                    'duration': time.time() - start_time,
                    'ai_healing_used': False,
                    'ai_healing_attempted': self.ai_healing_orchestrator is not None
                }

        except Exception as e:
            logger.error(f"Action execution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time,
                'ai_healing_used': False
            }

    def _execute_original_action(self, action):
        """Execute the original action logic"""
        # Execute based on action type
        if action_type == 'launch_app':
            result = self._execute_launch_app(action)
        elif action_type == 'terminate_app':
            result = self._execute_terminate_app(action)
        elif action_type == 'click':
            result = self._execute_click(action)
        elif action_type == 'wait':
            result = self._execute_wait(action)
        elif action_type == 'addLog':
            result = self._execute_add_log(action)
        elif action_type == 'takeScreenshot':
            result = self._execute_take_screenshot(action)
        else:
            # For unknown action types, try to execute using the device controller
            result = self._execute_generic_action(action)

        return result

    def _attempt_ai_healing(self, action: Dict[str, Any], original_error: Exception) -> Dict[str, Any]:
        """Attempt AI healing for failed action"""
        try:
            logger.info(f"🩺 Attempting AI healing for failed action: {action.get('type', 'unknown')}")

            # Get action description for AI analysis
            action_description = self._get_action_description(action)

            # Create retry function for original action
            def retry_original():
                return self._execute_original_action(action)

            # Trigger AI healing
            healing_session = self.ai_healing_orchestrator.heal_test_failure(
                driver=self.device_controller.driver,
                failed_action=action_description,
                error_message=str(original_error),
                retry_original_action=retry_original
            )

            # Process healing results
            if healing_session.final_result == HealingResult.SUCCESS:
                logger.info("✅ AI healing successful!")
                return {
                    'success': True,
                    'message': f'Action healed successfully: {healing_session.success_action}',
                    'ai_healing_used': True,
                    'healing_session_id': healing_session.session_id,
                    'healing_attempts': len(healing_session.attempts),
                    'healing_time': healing_session.total_time
                }
            else:
                logger.warning(f"❌ AI healing failed: {healing_session.final_result.value}")
                return {
                    'success': False,
                    'error': f'AI healing failed: {healing_session.final_result.value}',
                    'ai_healing_used': True,
                    'healing_session_id': healing_session.session_id,
                    'healing_attempts': len(healing_session.attempts),
                    'healing_time': healing_session.total_time
                }

        except Exception as e:
            logger.error(f"AI healing attempt failed: {e}")
            return {
                'success': False,
                'error': f'AI healing error: {str(e)}',
                'ai_healing_used': True
            }

    def _get_action_description(self, action: Dict[str, Any]) -> str:
        """Generate human-readable description of action for AI analysis"""
        action_type = action.get('type', 'unknown')

        if action_type == 'click':
            locator = action.get('locator_value', 'unknown element')
            return f"Click on element with locator: {locator}"
        elif action_type == 'tap':
            locator = action.get('locator_value', 'unknown element')
            return f"Tap on element with locator: {locator}"
        elif action_type == 'type' or action_type == 'sendKeys':
            text = action.get('text', 'unknown text')
            locator = action.get('locator_value', 'unknown field')
            return f"Type '{text}' into field: {locator}"
        elif action_type == 'swipe':
            direction = action.get('direction', 'unknown direction')
            return f"Swipe {direction}"
        elif action_type == 'wait':
            timeout = action.get('timeout', 'unknown time')
            return f"Wait for {timeout} seconds"
        elif action_type == 'launch_app':
            app_id = action.get('bundle_id') or action.get('app_id', 'unknown app')
            return f"Launch app: {app_id}"
        else:
            return f"Execute {action_type} action"
    
    def _execute_launch_app(self, action):
        """Execute launch app action"""
        try:
            bundle_id = action.get('bundle_id') or action.get('app_id')
            if not bundle_id:
                return {'success': False, 'error': 'No bundle ID provided'}
            
            # Use device controller to launch app
            result = self.device_controller.launch_app(bundle_id)
            return {'success': True, 'message': f'Launched app: {bundle_id}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_terminate_app(self, action):
        """Execute terminate app action"""
        try:
            bundle_id = action.get('bundle_id') or action.get('app_id')
            if not bundle_id:
                return {'success': False, 'error': 'No bundle ID provided'}
            
            # Use device controller to terminate app
            result = self.device_controller.terminate_app(bundle_id)
            return {'success': True, 'message': f'Terminated app: {bundle_id}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_click(self, action):
        """Execute click action"""
        try:
            # Get locator information
            locator_type = action.get('locator_type', 'xpath')
            locator_value = action.get('locator_value', '')
            
            if not locator_value:
                return {'success': False, 'error': 'No locator value provided'}
            
            # Use device controller to click element
            result = self.device_controller.click_element(locator_type, locator_value)
            return {'success': True, 'message': f'Clicked element: {locator_value}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_wait(self, action):
        """Execute wait action"""
        try:
            wait_time = action.get('wait_time', 1)
            if isinstance(wait_time, str):
                wait_time = float(wait_time)
            
            time.sleep(wait_time / 1000.0)  # Convert ms to seconds
            return {'success': True, 'message': f'Waited for {wait_time}ms'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_add_log(self, action):
        """Execute add log action"""
        try:
            log_message = action.get('log_message', 'Log entry')
            logger.info(f"Add Log: {log_message}")
            return {'success': True, 'message': f'Added log: {log_message}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_take_screenshot(self, action):
        """Execute take screenshot action"""
        try:
            screenshot_name = action.get('screenshot_name', 'screenshot')
            # Use device controller to take screenshot
            result = self.device_controller.take_screenshot(screenshot_name)
            return {'success': True, 'message': f'Screenshot taken: {screenshot_name}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_generic_action(self, action):
        """Execute generic action using device controller"""
        try:
            # For now, just return success for unknown actions
            # In a real implementation, you would map this to the appropriate device controller method
            action_type = action.get('type', 'unknown')
            logger.info(f"Executing generic action: {action_type}")
            return {'success': True, 'message': f'Executed action: {action_type}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
