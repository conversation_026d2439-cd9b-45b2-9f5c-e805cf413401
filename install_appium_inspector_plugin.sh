#!/bin/bash

# Script to install and configure the Appium Inspector plugin

echo "Checking for Appium installation..."
if ! command -v appium &> /dev/null; then
    echo "Appium is not installed or not in PATH. Please install Appium first."
    exit 1
fi

echo "Checking for Appium Inspector plugin..."
PLUGIN_CHECK=$(appium plugin list --installed | grep inspector)

if [[ -z "$PLUGIN_CHECK" ]]; then
    echo "Appium Inspector plugin is not installed. Installing now..."
    appium plugin install --source=npm appium-inspector-plugin
    
    if [ $? -eq 0 ]; then
        echo "Appium Inspector plugin installed successfully!"
    else
        echo "Failed to install Appium Inspector plugin. Please check your npm and Appium installation."
        exit 1
    fi
else
    echo "Appium Inspector plugin is already installed."
fi

echo "Stopping any running Appium instances..."
pkill -f appium || true

echo "Starting Appium with Inspector plugin enabled..."
appium --use-plugins=inspector --allow-cors --base-path /wd/hub --relaxed-security &

echo "Waiting for <PERSON><PERSON><PERSON> to start..."
sleep 5

echo "Checking if Appium Inspector is accessible..."
if curl -s http://localhost:4723/inspector > /dev/null; then
    echo "Success! Appium Inspector is now available at http://localhost:4723/inspector"
    echo "You can now use the Web Inspector button in the application."
else
    echo "Could not access Appium Inspector. Please check if Appium is running correctly."
    echo "You can manually start Appium with: appium --use-plugins=inspector --allow-cors --base-path /wd/hub --relaxed-security"
fi

echo "Done!"

#TO FIX APPIUM INSPECTOR SHOWN BLANK

# import path from 'node:path';
# import {fileURLToPath} from 'node:url';

# import express from 'express';
# import {BasePlugin} from '@appium/base-plugin';
# const PLUGIN_ROOT_PATH = '/inspector';
# const INDEX_HTML = 'index.html';
# const ROOT_DIR = path.resolve(path.dirname(fileURLToPath(import.meta.url)), 'dist-browser');

# /**
#  * Appium Inspector Plugin class
#  * @extends {BasePlugin}
#  */
# export class AppiumInspectorPlugin extends BasePlugin {
#   /**
#    * Creates an instance of AppiumInspectorPlugin
#    * @param {string} name - The name of the plugin
#    * @param {Record<string, unknown>} cliArgs - Command line arguments
#    */
#   constructor(name, cliArgs) {
#     super(name, cliArgs);
#   }

#   /**
#    * Handles inspector page requests
#    * @param {import('express').Request} req - Express request object
#    * @param {import('express').Response} res - Express response object
#    * @returns {Promise<void>}
#    */
#   static async openInspector(req, res) {
#     res.sendFile(INDEX_HTML, {root: ROOT_DIR});
#   }

#   static async updateServer(expressApp) {
#     expressApp.get('/inspector', AppiumInspectorPlugin.openInspector);
#     expressApp.use('/inspector', express.static(ROOT_DIR, {index: INDEX_HTML}));
#   }
# }
