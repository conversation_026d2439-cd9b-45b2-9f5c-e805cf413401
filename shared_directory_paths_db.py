import os
import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# Try to import platform database adapter
try:
    from utils.platform_db_adapter import PlatformDBAdapter
    USE_PLATFORM_DB = True
except ImportError:
    USE_PLATFORM_DB = False

class SharedDirectoryPathsDB:
    """
    Shared database handler for storing directory paths configuration
    Uses shared_config.db for directory paths that are common across platforms
    Stores paths for:
    - Test Cases
    - Reference Images  
    - Test Suites
    - Reports
    - Files to Push
    """

    def __init__(self, platform='ios'):
        self.platform = platform
        
        # Check if we should use platform database adapter
        if USE_PLATFORM_DB:
            try:
                self.platform_db = PlatformDBAdapter(platform)
                self.use_platform_db = True
                # Set dummy paths for backward compatibility
                self.shared_db_path = f'{platform}_platform_db'
                self.platform_db_path = f'{platform}_platform_db'
                logger.info(f"Using platform database adapter for {platform}")
                return
            except Exception as e:
                logger.warning(f"Failed to use platform database adapter, falling back to legacy: {e}")
                self.use_platform_db = False
        else:
            self.use_platform_db = False
        
        # Check if we're running in consolidated mode
        self.use_consolidated = os.environ.get('USE_CONSOLIDATED_DB', 'false').lower() == 'true'
        
        if self.use_consolidated:
            # Use consolidated database
            try:
                from app.utils.consolidated_db_adapter import get_consolidated_db
                self.consolidated_db = get_consolidated_db()
                # Set dummy paths for backward compatibility
                self.shared_db_path = 'consolidated'
                self.platform_db_path = 'consolidated'
                logger.info("Using consolidated database for directory paths")
                return
            except Exception as e:
                logger.warning(f"Failed to use consolidated database, falling back to legacy: {e}")
                self.use_consolidated = False
        
        # Legacy database paths
        # Use shared database for directory paths
        self.shared_db_path = os.path.join('data', 'shared_config.db')

        # Platform-specific database for environment variables and other app-specific data
        instance_suffix = os.environ.get('INSTANCE_DB_SUFFIX', '')
        platform_suffix = f'_{platform}'
        combined_suffix = f"{platform_suffix}{instance_suffix}"

        if combined_suffix:
            db_filename = f"settings{combined_suffix}.db"
        else:
            db_filename = f"settings_{platform}.db"

        self.platform_db_path = os.path.join('data', db_filename)

        # Check if we're in migration mode to prevent unauthorized database creation
        migration_mode = os.environ.get('IOS_MIGRATION_MODE') == 'true' or os.environ.get('ANDROID_MIGRATION_MODE') == 'true'

        if not migration_mode:
            # Only create directories and initialize databases if not in migration mode
            os.makedirs(os.path.dirname(self.shared_db_path), exist_ok=True)
            os.makedirs(os.path.dirname(self.platform_db_path), exist_ok=True)

            # Initialize both databases
            self._init_shared_db()
            self._init_platform_db()
        else:
            # In migration mode, prevent ALL database creation in the root data directory
            # Platform-specific databases should only exist in app/data/ and app_android/data/
            instance_suffix = os.environ.get('INSTANCE_DB_SUFFIX', '')

            logger.info(f"Migration mode: Preventing shared database creation for {platform}")
            logger.info(f"Migration mode: Platform-specific databases should be in app/data/ or app_android/data/")

            # For platform-specific database, check if it's an allowed database file
            platform_db_name = os.path.basename(self.platform_db_path)
            allowed_platform_dbs = [
                f'{platform}_settings.db',
                f'{platform}_environments.db',
                f'{platform}_globals.db',
                f'{platform}_test_suites.db',
                f'{platform}_execution_tracker.db'
            ]

            if platform_db_name in allowed_platform_dbs:
                # This is an allowed platform database - check if it exists in the correct location
                correct_platform_db_path = os.path.join(f'app{"_android" if platform == "android" else ""}', 'data', platform_db_name)
                if os.path.exists(correct_platform_db_path):
                    # Update the path to point to the correct location
                    self.platform_db_path = correct_platform_db_path
                    logger.info(f"Migration mode: Using existing platform database at {correct_platform_db_path}")
                else:
                    logger.warning(f"Migration mode: Platform database not found at {correct_platform_db_path}")
            else:
                logger.info(f"Migration mode: Prevented unauthorized platform database creation: {platform_db_name}")

    def _init_shared_db(self):
        """Initialize the shared database with directory paths and other shared tables"""
        try:
            conn = sqlite3.connect(str(self.shared_db_path))
            cursor = conn.cursor()

            # Create directory_paths table in shared database
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS directory_paths (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                path_key TEXT UNIQUE,
                path_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create global_values table in shared database
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS global_values (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE,
                value TEXT,
                type TEXT DEFAULT 'string',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create environments table in shared database
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS environments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            conn.commit()
            conn.close()
            logger.info("Shared database initialized/verified")
        except Exception as e:
            logger.error(f"Error initializing shared database: {str(e)}")

    def _init_platform_db(self):
        """Initialize the platform-specific database with environment variables and other app-specific tables"""
        try:
            conn = sqlite3.connect(str(self.platform_db_path))
            cursor = conn.cursor()

            # Create platform-specific environment_variables table
            env_table_name = f"{self.platform}_environment_variables"
            cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS {env_table_name} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                environment_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                type TEXT DEFAULT 'default',
                initial_value TEXT,
                current_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(environment_id, name)
            )
            ''')

            # Create active_environment table for persistent environment state
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS active_environment (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                environment_id INTEGER,
                session_id TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            conn.commit()
            conn.close()
            logger.info(f"Platform-specific database initialized/verified for {self.platform}")
        except Exception as e:
            logger.error(f"Error initializing platform database: {str(e)}")

    def save_path(self, name, path):
        """
        Save a directory path to the shared database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES')
            path: The path to the directory

        Returns:
            bool: True if successful, False otherwise
        """
        if self.use_consolidated:
            try:
                self.consolidated_db.save_path(name, path)
                logger.info(f"Directory path saved to consolidated database: {name} = {path}")
                return True
            except Exception as e:
                logger.error(f"Error saving directory path to consolidated database: {str(e)}")
                return False
        
        try:
            conn = sqlite3.connect(str(self.shared_db_path))
            cursor = conn.cursor()

            # Check if the path already exists
            cursor.execute("SELECT id FROM directory_paths WHERE path_key = ?", (name,))
            existing = cursor.fetchone()

            if existing:
                # Update existing path
                cursor.execute(
                    "UPDATE directory_paths SET path_value = ?, updated_at = CURRENT_TIMESTAMP WHERE path_key = ?",
                    (path, name)
                )
            else:
                # Insert new path
                cursor.execute(
                    "INSERT INTO directory_paths (path_key, path_value) VALUES (?, ?)",
                    (name, path)
                )

            conn.commit()
            conn.close()
            logger.info(f"Directory path saved to shared database: {name} = {path}")
            return True
        except Exception as e:
            logger.error(f"Error saving directory path: {str(e)}")
            return False

    def get_path(self, name, default=None):
        """
        Get a directory path from the shared database
        Supports platform-specific paths (e.g., REPORTS_IOS, REPORTS_ANDROID)
        
        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES', 'REPORTS')
            default: Default value to return if the path is not found
        
        Returns:
            str: The path, or the default value if not found
        """
        if self.use_consolidated:
            try:
                result = self.consolidated_db.get_path(name)
                if result:
                    logger.debug(f"Found path in consolidated database for {name}: {result}")
                    return result
                logger.debug(f"No path found in consolidated database for {name}, using default: {default}")
                return default
            except Exception as e:
                logger.error(f"Error getting directory path from consolidated database: {str(e)}")
                return default
        
        try:
            conn = sqlite3.connect(self.shared_db_path)
            cursor = conn.cursor()
            
            # First try platform-specific path (e.g., REPORTS_IOS, REPORTS_ANDROID)
            platform_specific_name = f"{name}_{self.platform.upper()}"
            cursor.execute("SELECT path_value FROM directory_paths WHERE path_key = ?", (platform_specific_name,))
            result = cursor.fetchone()
            
            if result:
                conn.close()
                logger.debug(f"Found platform-specific path for {name}: {result[0]}")
                return result[0]
            
            # Fall back to generic path
            cursor.execute("SELECT path_value FROM directory_paths WHERE path_key = ?", (name,))
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                logger.debug(f"Found generic path for {name}: {result[0]}")
                return result[0]
            
            logger.debug(f"No path found for {name}, using default: {default}")
            return default
        except Exception as e:
            logger.error(f"Error getting directory path: {str(e)}")
            return default

    def get_all_paths(self):
        """
        Get all directory paths from the shared database

        Returns:
            dict: Dictionary of all directory paths
        """
        try:
            conn = sqlite3.connect(str(self.shared_db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT path_key, path_value FROM directory_paths")
            results = cursor.fetchall()

            conn.close()

            return {name: path for name, path in results}
        except Exception as e:
            logger.error(f"Error getting all directory paths: {str(e)}")
            return {}

    def save_all_paths(self, paths_dict):
        """
        Save multiple directory paths to the shared database

        Args:
            paths_dict: Dictionary of directory paths {name: path}

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Attempting to save all paths to shared database: {paths_dict}")
            if not paths_dict:
                logger.warning("Empty paths dictionary provided to save_all_paths")
                return False

            conn = sqlite3.connect(str(self.shared_db_path))
            cursor = conn.cursor()

            for name, path in paths_dict.items():
                logger.info(f"Processing path: {name} = {path}")
                # Check if the path already exists
                cursor.execute("SELECT id FROM directory_paths WHERE path_key = ?", (name,))
                existing = cursor.fetchone()

                if existing:
                    # Update existing path
                    logger.info(f"Updating existing path for {name}: {path}")
                    cursor.execute(
                        "UPDATE directory_paths SET path_value = ?, updated_at = CURRENT_TIMESTAMP WHERE path_key = ?",
                        (path, name)
                    )
                else:
                    # Insert new path
                    logger.info(f"Inserting new path for {name}: {path}")
                    cursor.execute(
                        "INSERT INTO directory_paths (path_key, path_value) VALUES (?, ?)",
                        (name, path)
                    )

            conn.commit()
            conn.close()
            logger.info(f"All directory paths saved successfully to shared database: {paths_dict}")
            return True
        except Exception as e:
            logger.error(f"Error saving all directory paths: {str(e)}")
            return False

    def count_directory_contents(self, name):
        """
        Count the number of items in a directory

        Args:
            name: The name of the directory in the database

        Returns:
            dict: Dictionary with counts of different file types
        """
        try:
            directory_path = self.get_path(name)
            if not directory_path:
                return {"error": f"Directory path for {name} not found in database"}

            path = Path(directory_path)
            if not path.exists():
                return {"error": f"Directory {directory_path} does not exist"}

            if not path.is_dir():
                return {"error": f"{directory_path} is not a directory"}

            counts = {}

            # Count based on directory type
            if name == "TEST_CASES":
                json_files = list(path.glob("**/*.json"))
                counts["test_cases"] = len(json_files)

            elif name == "REFERENCE_IMAGES":
                image_files = list(path.glob("**/*.png")) + list(path.glob("**/*.jpg")) + list(path.glob("**/*.jpeg"))
                counts["images"] = len(image_files)

            elif name == "TEST_SUITES":
                json_files = list(path.glob("**/*.json"))
                counts["test_suites"] = len(json_files)

            elif name == "REPORTS":
                html_files = list(path.glob("**/*.html"))
                counts["reports"] = len(html_files)

            elif name == "FILES_TO_PUSH":
                all_files = list(path.glob("**/*.*"))
                counts["files"] = len(all_files)

            # Also count total files as a fallback
            all_files = list(path.glob("**/*.*"))
            counts["total_files"] = len(all_files)

            return counts
        except Exception as e:
            logger.error(f"Error counting directory contents: {str(e)}")
            return {"error": str(e)}

    # --- Environment Methods (using shared database) ---
    def create_environment(self, name):
        if self.use_consolidated:
            try:
                env_id = self.consolidated_db.create_environment(name)
                logger.info(f"Environment '{name}' created with ID {env_id} in consolidated database.")
                return env_id
            except Exception as e:
                logger.error(f"Error creating environment '{name}' in consolidated database: {str(e)}")
                return None

        conn = None
        try:
            # Check if database file exists
            if not os.path.exists(str(self.shared_db_path)):
                logger.warning(f"Shared database not found: {self.shared_db_path}")
                return None

            conn = sqlite3.connect(str(self.shared_db_path))
            cursor = conn.cursor()
            cursor.execute("INSERT INTO environments (name) VALUES (?)", (name,))
            env_id = cursor.lastrowid
            conn.commit()
            conn.close()
            logger.info(f"Environment '{name}' created with ID {env_id}.")
            return env_id
        except sqlite3.IntegrityError:
            logger.warning(f"Environment '{name}' already exists.")
            if conn:
                conn.close()
            return None
        except Exception as e:
            logger.error(f"Error creating environment '{name}': {str(e)}")
            if conn:
                conn.close()
            return None

    def get_all_environments(self):
        """Return environments from centralized platform DB (db-data/<platform>.db)."""
        # Prefer consolidated if configured
        if getattr(self, 'use_consolidated', False):
            try:
                environments = self.consolidated_db.get_all_environments(self.platform)
                logger.debug(f"Retrieved {len(environments)} environments from consolidated database")
                return environments
            except Exception as e:
                logger.error(f"Error getting all environments from consolidated database: {str(e)}")
                return []

        # Use PlatformDBAdapter when available
        if hasattr(self, 'platform_db') and self.platform_db:
            try:
                conn = self.platform_db.get_connection('environments')
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT environment_id, name, description, is_active, created_at, updated_at
                    FROM environments
                    WHERE platform = ?
                    ORDER BY name
                    """,
                    (self.platform,)
                )
                rows = cursor.fetchall()
                conn.close()
                envs = []
                for env_id, name, description, is_active, created_at, updated_at in rows:
                    envs.append({
                        'id': env_id,  # backward-compatible alias
                        'environment_id': env_id,
                        'name': name,
                        'description': description,
                        'is_active': bool(is_active),
                        'created_at': created_at,
                        'updated_at': updated_at,
                        'platform': self.platform,
                    })
                return envs
            except Exception as e:
                logger.error(f"Error getting environments from centralized DB: {e}")
                return []

        # Legacy fallback (should not be used in DB-only mode)
        logger.warning("Legacy environment listing path used; this should be gated in DB-only mode.")
        return []

    def get_environment_by_name(self, name):
        conn = None
        try:
            # Check if database file exists
            if not os.path.exists(str(self.shared_db_path)):
                logger.warning(f"Shared database not found: {self.shared_db_path}")
                return None

            conn = sqlite3.connect(str(self.shared_db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM environments WHERE name = ?", (name,))
            row = cursor.fetchone()
            conn.close()
            return {"id": row[0], "name": row[1]} if row else None
        except Exception as e:
            logger.error(f"Error getting environment by name '{name}': {str(e)}")
            if conn:
                conn.close()
            return None

    def get_environment_by_id(self, env_id):
        """Fetch environment by its string environment_id from centralized DB."""
        # env_id may come as int/str; normalize to str
        env_key = str(env_id)
        if hasattr(self, 'platform_db') and self.platform_db:
            try:
                conn = self.platform_db.get_connection('environments')
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT environment_id, name, description, is_active, created_at, updated_at
                    FROM environments
                    WHERE environment_id = ? AND platform = ?
                    """,
                    (env_key, self.platform)
                )
                row = cursor.fetchone()
                conn.close()
                if not row:
                    return None
                environment_id, name, description, is_active, created_at, updated_at = row
                return {
                    'id': environment_id,
                    'environment_id': environment_id,
                    'name': name,
                    'description': description,
                    'is_active': bool(is_active),
                    'created_at': created_at,
                    'updated_at': updated_at,
                    'platform': self.platform,
                }
            except Exception as e:
                logger.error(f"Error getting environment by id '{env_id}': {e}")
                return None
        logger.warning("Legacy get_environment_by_id path used; returning None in DB-only mode")
        return None

    # --- Active Environment Methods (using platform-specific database) ---
    def set_active_environment(self, environment_id, session_id=None):
        """Set the active environment in the platform-specific database"""
        conn = None
        try:
            # Check if platform database file exists
            if not os.path.exists(str(self.platform_db_path)):
                logger.warning(f"Platform database not found: {self.platform_db_path}")
                return False

            conn = sqlite3.connect(self.platform_db_path)
            cursor = conn.cursor()

            # Insert or update the active environment
            cursor.execute('''
                INSERT OR REPLACE INTO active_environment (id, environment_id, session_id, updated_at)
                VALUES (1, ?, ?, CURRENT_TIMESTAMP)
            ''', (environment_id, session_id))

            conn.commit()
            conn.close()
            logger.info(f"Set active environment to ID {environment_id} for session {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error setting active environment: {str(e)}")
            if conn:
                conn.close()
            return False

    def get_active_environment(self, session_id=None):
        """Get the active environment from the platform-specific database"""
        conn = None
        try:
            # Check if platform database file exists
            if not os.path.exists(str(self.platform_db_path)):
                logger.warning(f"Platform database not found: {self.platform_db_path}")
                return None

            conn = sqlite3.connect(self.platform_db_path)
            cursor = conn.cursor()

            # Get the active environment
            cursor.execute('SELECT environment_id FROM active_environment WHERE id = 1')
            result = cursor.fetchone()

            conn.close()

            if result:
                environment_id = result[0]
                logger.debug(f"Retrieved active environment ID {environment_id} from database")
                return environment_id
            else:
                logger.debug("No active environment found in database")
                return None
        except Exception as e:
            logger.error(f"Error getting active environment: {str(e)}")
            if conn:
                conn.close()
            return None

    def get_variables_for_environment(self, environment_id):
        """Return variables for the given environment_id (string) from centralized DB."""
        env_key = str(environment_id)
        try:
            if hasattr(self, 'platform_db') and self.platform_db:
                conn = self.platform_db.get_connection('environments')
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT name, initial_value, current_value, type, created_at, updated_at
                    FROM environment_variables
                    WHERE environment_id = ?
                    ORDER BY name
                    """,
                    (env_key,)
                )
                rows = cursor.fetchall()
                conn.close()
                variables = []
                for name, initial_value, current_value, vtype, created_at, updated_at in rows:
                    variables.append({
                        # synthesize a stable id using name; routes should prefer name-based ops
                        'id': None,
                        'name': name,
                        'initial_value': initial_value or '',
                        'current_value': current_value or '',
                        'type': vtype or 'default',
                        'created_at': created_at,
                        'updated_at': updated_at
                    })
                return variables
            logger.warning("Legacy variable listing path used; returning empty list in DB-only mode")
            return []
        except Exception as e:
            logger.error(f"Error getting variables for environment {environment_id}: {e}")
            return []