2025-10-09 17:57:26,375 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-10-09 17:57:26,375 - __main__ - INFO - Android app configured to use only the 5 specified database files in app_android/data/
2025-10-09 17:57:26,389 - app_android.utils.directory_paths_db - INFO - Android DirectoryPathsDB initialized with centralized database
2025-10-09 17:57:26,389 - app_android.utils.directory_paths_db - INFO - Android DirectoryPathsDB initialized with centralized database
2025-10-09 17:57:26,397 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-10-09 17:57:26,397 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-10-09 17:57:26,545 - app_android.utils.directory_paths_db - INFO - Android DirectoryPathsDB initialized with centralized database
[2025-10-09 17:57:26,555] INFO in database: Centralized DB mode (Android): skipping legacy init_db
[2025-10-09 17:57:26,555] INFO in database: Checking initial database state...
[2025-10-09 17:57:26,557] INFO in database: Database state: 18 suites, 45 cases, 15 steps, 0 screenshots, 10 tracking entries
[2025-10-09 17:57:26,557] INFO in database: Sample suites: [('gTdFY4', 'TC3', 'active', None), ('H5KSoN', 'Kmart AU Android', 'active', None), ('zbvWeb', 'changes', 'active', None)]
[2025-10-09 17:57:26,558] INFO in test_case_manager: TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/android_data/test_cases
[2025-10-09 17:57:26,558] INFO in test_case_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db
[2025-10-09 17:57:26,558] INFO in test_suites_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db
[2025-10-09 17:57:26,558] INFO in test_case_manager: TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/android_data/test_cases
[2025-10-09 17:57:26,559] INFO in test_case_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db
[2025-10-09 17:57:26,559] INFO in test_suites_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db
[2025-10-09 17:57:26,786] INFO in global_values_db: Using centralized platform database for global values (Android)
[2025-10-09 17:57:26,933] WARNING in appium_device_controller: TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[2025-10-09 17:57:26,969] INFO in appium_device_controller: Successfully imported Airtest library.
[2025-10-09 17:57:26,971] INFO in database: Skipping legacy init_db() (DB-only mode). Set ALLOW_LEGACY_DB_INIT=true to enable.
[2025-10-09 17:57:26,971] INFO in database: Checking initial database state...
[2025-10-09 17:57:26,973] INFO in database: Database state: 16 suites, 59 cases, 298 steps, 0 screenshots, 0 tracking entries
[2025-10-09 17:57:26,973] INFO in database: Sample suites: [('EZyIO4', 'WW', 'active', '2025-08-30 05:45:29'), ('vuYvvC', 'TC3', 'active', '2025-08-30 05:45:29'), ('ReLhSZ', 'Kmart AU Android', 'active', '2025-08-30 05:45:29')]
[2025-10-09 17:57:26,973] INFO in directory_paths_db: iOS DirectoryPathsDB initialized with centralized database
[2025-10-09 17:57:27,350] INFO in report_generator_db: ReportGeneratorDB initialized with DB: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db
[2025-10-09 17:57:27,350] INFO in screenshot_manager_db: ScreenshotManagerDB initialized with DB: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/android.db
[2025-10-09 17:57:27,395] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8300
[2025-10-09 17:57:27,410] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-10-09 17:57:27,410] INFO in appium_device_controller: Appium server is already running and responsive
[2025-10-09 17:57:27,413] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-10-09 17:57:27,413] INFO in appium_device_controller: Appium server is already running and responsive
[2025-10-09 17:57:27,413] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-10-09 17:57:27,413] INFO in appium_device_controller: Optimized session manager initialized
✅ Loaded environment from /Users/<USER>/Documents/automation-tool/MobileAppAutomation/.env
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4724
  - WebDriverAgent port: 8300
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app_android.core.app_factory'
 * Debug mode: on
