2025-10-09 16:56:57,571 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-10-09 16:56:57,572 - __main__ - INFO - iOS app configured to use only the 5 specified database files in app/data/
2025-10-09 16:56:57,572 - __main__ - INFO - Using intelligent port management for iOS platform...
2025-10-09 16:56:57,572 - __main__ - WARNING - Port manager not available, falling back to basic cleanup
2025-10-09 16:56:57,572 - __main__ - INFO - Using custom ports (Flask: 8080, Appium: 4723, WDA: 8200) - preserving existing processes for multi-instance support
2025-10-09 16:56:57,572 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
[2025-10-09 16:56:57,777] INFO in database: Skipping legacy init_db() (DB-only mode). Set ALLOW_LEGACY_DB_INIT=true to enable.
[2025-10-09 16:56:57,777] INFO in database: Checking initial database state...
[2025-10-09 16:56:57,779] INFO in database: Database state: 16 suites, 59 cases, 298 steps, 0 screenshots, 7 tracking entries
[2025-10-09 16:56:57,779] INFO in database: Sample suites: [('EZyIO4', 'WW', 'active', '2025-08-30 05:45:29'), ('vuYvvC', 'TC3', 'active', '2025-08-30 05:45:29'), ('ReLhSZ', 'Kmart AU Android', 'active', '2025-08-30 05:45:29')]
[2025-10-09 16:56:57,780] INFO in test_case_manager: TestCaseManager initialized with directory: /Users/<USER>/MobileAutomationWorkspace/ios/test_cases
[2025-10-09 16:56:57,780] INFO in test_case_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db
[2025-10-09 16:56:57,781] INFO in database: Skipping legacy init_db() (DB-only mode). Set ALLOW_LEGACY_DB_INIT=true to enable.
[2025-10-09 16:56:57,781] INFO in database: Checking initial database state...
[2025-10-09 16:56:57,783] INFO in database: Database state: 16 suites, 59 cases, 298 steps, 0 screenshots, 7 tracking entries
[2025-10-09 16:56:57,783] INFO in database: Sample suites: [('EZyIO4', 'WW', 'active', '2025-08-30 05:45:29'), ('vuYvvC', 'TC3', 'active', '2025-08-30 05:45:29'), ('ReLhSZ', 'Kmart AU Android', 'active', '2025-08-30 05:45:29')]
[2025-10-09 16:56:57,783] INFO in test_suites_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db
[2025-10-09 16:56:57,783] INFO in test_case_manager: TestCaseManager initialized with directory: /Users/<USER>/MobileAutomationWorkspace/ios/test_cases
[2025-10-09 16:56:57,783] INFO in test_case_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db
[2025-10-09 16:56:57,784] INFO in test_suites_manager: Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db
[2025-10-09 16:56:58,732] WARNING in appium_device_controller: TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[2025-10-09 16:56:58,763] INFO in appium_device_controller: Successfully imported Airtest library.
[2025-10-09 16:56:58,767] INFO in directory_paths_db: iOS DirectoryPathsDB initialized with centralized database
[2025-10-09 16:56:58,785] WARNING in appium_device_controller: TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[2025-10-09 16:56:58,785] INFO in appium_device_controller: Successfully imported Airtest library.
[2025-10-09 16:56:59,118] INFO in directory_paths_db: iOS DirectoryPathsDB initialized with centralized database
[2025-10-09 16:56:59,122] INFO in directory_paths_db: Android DirectoryPathsDB initialized with centralized database
[2025-10-09 16:56:59,122] INFO in directory_paths_db: Android DirectoryPathsDB initialized with centralized database
[2025-10-09 16:56:59,179] INFO in report_generator_db: ReportGeneratorDB initialized with DB: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db
[2025-10-09 16:56:59,179] INFO in screenshot_manager_db: ScreenshotManagerDB initialized with DB: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/db-data/ios.db
[2025-10-09 16:56:59,197] INFO in global_values_db: Using centralized platform database for global values (iOS)
[2025-10-09 16:56:59,224] INFO in reference_image_routes: Reference images directory: /Users/<USER>/Documents/automation-tool/android_data/reference_images
[2025-10-09 16:56:59,238] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-10-09 16:56:59,253] INFO in appium_device_controller: Appium server is running and ready
[2025-10-09 16:56:59,253] INFO in appium_device_controller: Appium server is already running and responsive
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8200
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app.core.app_factory'
 * Debug mode: on
