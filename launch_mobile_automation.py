#!/usr/bin/env python3
"""
Mobile App Automation - Launch<PERSON>t

This script immediately launches the secure distribution GUI application.
No command-line interface - goes straight to the authentication screen.
"""

import os
import sys
from pathlib import Path

def main():
    """Launch the mobile automation application"""
    try:
        # Add the secure distribution app to Python path
        app_dir = Path(__file__).parent / 'secure_distribution_app'
        sys.path.insert(0, str(app_dir))
        
        # Hide console window on Windows
        if os.name == 'nt':
            try:
                import ctypes
                ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
            except:
                pass
        
        # Import and run the main application
        from main import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"Failed to import application modules: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install -r secure_distribution_app/requirements.txt")
        input("Press Enter to exit...")
        sys.exit(1)
    except Exception as e:
        print(f"Failed to launch application: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
