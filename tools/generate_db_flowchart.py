#!/usr/bin/env python3
"""
Generate a simple PDF flowchart showing DB tables used by iOS and Android apps.

Legend:
  - Green: Core/actively used
  - Blue: Used (auxiliary/config)
  - Gray: Legacy/compatibility
  - Orange: Low usage (candidate for review)

Output: docs/DB_Flow_Chart.pdf
"""

from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.lib.units import inch
from pathlib import Path


def draw_box(c, x, y, w, h, text, fill_color, stroke_color=colors.black):
    c.setFillColor(fill_color)
    c.setStrokeColor(stroke_color)
    c.rect(x, y, w, h, fill=1, stroke=1)
    c.setFillColor(colors.black)
    c.setFont("Helvetica-Bold", 10)
    text_x = x + 6
    text_y = y + h - 14
    for line in text.split("\n"):
        c.drawString(text_x, text_y, line)
        text_y -= 12


def draw_arrow(c, x1, y1, x2, y2, stroke_color=colors.black):
    c.setStrokeColor(stroke_color)
    c.setLineWidth(1.2)
    c.line(x1, y1, x2, y2)
    # simple arrow head
    import math
    angle = math.atan2(y2 - y1, x2 - x1)
    size = 6
    ax = x2 - size * math.cos(angle - math.pi / 6)
    ay = y2 - size * math.sin(angle - math.pi / 6)
    bx = x2 - size * math.cos(angle + math.pi / 6)
    by = y2 - size * math.sin(angle + math.pi / 6)
    c.line(x2, y2, ax, ay)
    c.line(x2, y2, bx, by)


def main():
    out_dir = Path("docs")
    out_dir.mkdir(parents=True, exist_ok=True)
    out_path = out_dir / "DB_Flow_Chart.pdf"

    c = canvas.Canvas(str(out_path), pagesize=letter)
    width, height = letter

    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(inch * 0.75, height - inch * 0.75, "Mobile Automation DB Tables (iOS & Android)")
    c.setFont("Helvetica", 10)
    c.drawString(inch * 0.75, height - inch * 0.95, "Overview of tables currently used by apps and their relations")

    # Legend
    legend_x = inch * 0.75
    legend_y = height - inch * 1.25
    def legend_item(x, y, color, text):
        c.setFillColor(color)
        c.rect(x, y, 12, 12, fill=1, stroke=0)
        c.setFillColor(colors.black)
        c.drawString(x + 16, y + 1, text)

    legend_item(legend_x, legend_y, colors.lightgreen, "Core/actively used")
    legend_item(legend_x + 160, legend_y, colors.lightblue, "Used (aux/config)")
    legend_item(legend_x + 320, legend_y, colors.lightgrey, "Legacy/compatibility")
    legend_item(legend_x + 480, legend_y, colors.orange, "Low usage (review)")

    # Columns
    col_left_x = inch * 0.75
    col_right_x = inch * 4.25
    col_top_y = height - inch * 1.6
    box_w = inch * 3.2
    box_h = 32
    vgap = 18

    # iOS column
    c.setFont("Helvetica-Bold", 12)
    c.drawString(col_left_x, col_top_y, "iOS (db-data/ios.db)")
    y = col_top_y - 18

    ios_boxes = [
        ("test_suites", colors.lightgreen),
        ("test_cases", colors.lightgreen),
        ("environments", colors.lightblue),
        ("environment_variables", colors.lightblue),
        ("execution_tracking", colors.lightgreen),
        ("screenshots (BLOB)", colors.lightgreen),
        ("execution_reports (report_data)", colors.lightgreen),
        ("reference_images", colors.lightblue),
        ("test_steps (legacy fallback)", colors.lightgrey),
        ("global_values", colors.lightblue),
        ("execution_settings", colors.lightblue),
        ("locator_repository", colors.orange),
    ]
    ios_positions = {}
    for name, col in ios_boxes:
        draw_box(c, col_left_x, y - box_h, box_w, box_h, name, col)
        ios_positions[name] = (col_left_x, y - box_h, box_w, box_h)
        y -= (box_h + vgap)

    # Android column
    c.setFont("Helvetica-Bold", 12)
    c.drawString(col_right_x, col_top_y, "Android (db-data/android.db)")
    y = col_top_y - 18
    and_boxes = [
        ("test_suites", colors.lightgreen),
        ("test_cases", colors.lightgreen),
        ("environments", colors.lightblue),
        ("environment_variables", colors.lightblue),
        ("execution_tracking", colors.lightgreen),
        ("screenshots (BLOB)", colors.lightgreen),
        ("execution_reports (report_data)", colors.lightgreen),
        ("reference_images", colors.lightblue),
        ("test_steps (legacy fallback)", colors.lightgrey),
        ("test_case_json_backups", colors.lightblue),
        ("global_values", colors.lightblue),
        ("execution_settings", colors.lightblue),
        ("locator_repository", colors.orange),
    ]
    and_positions = {}
    for name, col in and_boxes:
        draw_box(c, col_right_x, y - box_h, box_w, box_h, name, col)
        and_positions[name] = (col_right_x, y - box_h, box_w, box_h)
        y -= (box_h + vgap)

    # Arrows (iOS flow)
    def center_bottom(pos):
        x, y, w, h = pos
        return (x + w / 2, y)
    def center_top(pos):
        x, y, w, h = pos
        return (x + w / 2, y + h)

    flow_ios = [
        ("test_suites", "test_cases"),
        ("test_cases", "execution_tracking"),
        ("execution_tracking", "execution_reports (report_data)"),
    ]
    for a, b in flow_ios:
        ax, ay = center_bottom(ios_positions[a])
        bx, by = center_top(ios_positions[b])
        draw_arrow(c, ax, ay, bx, by)

    # screenshots -> execution_tracking (copy BLOB reference)
    s_pos = ios_positions["screenshots (BLOB)"]
    et_pos = ios_positions["execution_tracking"]
    draw_arrow(c, s_pos[0] + s_pos[2], s_pos[1] + s_pos[3]/2, et_pos[0] + et_pos[2], et_pos[1] + et_pos[3]/2, stroke_color=colors.darkgray)
    c.setFont("Helvetica", 8)
    c.drawString(et_pos[0] + et_pos[2] - 120, et_pos[1] + et_pos[3]/2 + 6, "attach screenshot BLOB")

    # Arrows (Android flow)
    flow_and = [
        ("test_suites", "test_cases"),
        ("test_cases", "execution_tracking"),
        ("execution_tracking", "execution_reports (report_data)"),
    ]
    for a, b in flow_and:
        ax, ay = center_bottom(and_positions[a])
        bx, by = center_top(and_positions[b])
        draw_arrow(c, ax, ay, bx, by)

    s_pos = and_positions["screenshots (BLOB)"]
    et_pos = and_positions["execution_tracking"]
    draw_arrow(c, s_pos[0], s_pos[1] + s_pos[3]/2, et_pos[0], et_pos[1] + et_pos[3]/2, stroke_color=colors.darkgray)
    c.setFont("Helvetica", 8)
    c.drawString(et_pos[0] + 6, et_pos[1] + et_pos[3]/2 + 6, "attach screenshot BLOB")

    # Footer notes
    c.setFont("Helvetica", 9)
    notes_y = inch * 0.7
    c.drawString(inch * 0.75, notes_y + 24, "Notes:")
    c.drawString(inch * 0.75, notes_y + 12, "- All tables shown are referenced by current code paths. Gray boxes are kept for legacy/fallback.")
    c.drawString(inch * 0.75, notes_y, "- Orange (locator_repository) appears low-usage; confirm before removal if not needed in your flows.")

    c.showPage()
    c.save()

    print(f"Wrote {out_path}")


if __name__ == "__main__":
    main()

