#!/usr/bin/env python3
"""
Regenerate the original PDF report for execution testsuite_execution_20251003_193357
"""
import os
import sys

# Add app_android to path
sys.path.insert(0, 'app_android')

from app_android.utils.pdf_report_generator import PDFReportGenerator

def regenerate_pdf():
    """Regenerate the original PDF report"""
    
    # Execution directory
    execution_id = 'testsuite_execution_20251003_193357'
    report_dir = os.path.join('reports_android', execution_id)
    
    # Data.json path
    data_json_path = os.path.join(report_dir, 'data.json')
    
    # Original PDF filename (matching the execution timestamp)
    pdf_filename = 'test_report_20251003_193357.pdf'
    pdf_path = os.path.join(report_dir, pdf_filename)
    
    # Backup the old empty PDF
    if os.path.exists(pdf_path):
        backup_path = pdf_path + '.old'
        os.rename(pdf_path, backup_path)
        print(f"✅ Backed up old PDF to: {backup_path}")
    
    print(f"\n📄 Regenerating PDF report...")
    print(f"   Execution: {execution_id}")
    print(f"   Output: {pdf_path}")
    
    # Create PDF generator
    generator = PDFReportGenerator()
    
    # Generate report
    success = generator.generate_report(data_json_path, pdf_path, report_dir)
    
    if success:
        print(f"\n✅ PDF report regenerated successfully!")
        print(f"   Location: {pdf_path}")
        
        # Check file size
        file_size = os.path.getsize(pdf_path)
        print(f"   Size: {file_size:,} bytes ({file_size / 1024:.1f} KB)")
        
        # Check screenshots
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        if os.path.exists(screenshots_dir):
            screenshot_files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
            print(f"   Screenshots: {len(screenshot_files)} files")
        
        # Open the PDF
        print(f"\n📂 Opening PDF...")
        os.system(f'open "{pdf_path}"')
        
        return True
    else:
        print(f"\n❌ PDF generation failed!")
        return False

if __name__ == '__main__':
    print("=" * 70)
    print("Regenerate PDF Report for testsuite_execution_20251003_193357")
    print("=" * 70)
    
    success = regenerate_pdf()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ COMPLETE")
    else:
        print("❌ FAILED")
    print("=" * 70)
    
    sys.exit(0 if success else 1)

