{"test_case_id": "xpath_locator_test_001", "name": "XPath Locator Test", "description": "Test case using XPath locators to verify locator repository tracking", "platform": "android", "actions": [{"action_id": "launch_app", "type": "launchApp", "name": "Launch Calculator App", "package_name": "com.coloros.calculator", "activity_name": ".Calculator"}, {"action_id": "click_number_5", "type": "clickElement", "name": "Click Number 5", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='5']", "fallback_locators": [{"locator_type": "accessibility_id", "locator_value": "five"}]}, {"action_id": "click_plus", "type": "clickElement", "name": "Click Plus Button", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc='plus']"}, {"action_id": "click_number_3", "type": "clickElement", "name": "Click Number 3", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='3']"}, {"action_id": "click_equals", "type": "clickElement", "name": "<PERSON><PERSON> Equals <PERSON><PERSON>", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc='equals']"}, {"action_id": "verify_result", "type": "waitTill", "name": "Verify Result is 8", "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,'8')]", "timeout": 5000}, {"action_id": "screenshot_result", "type": "takeScreenshot", "name": "Take Screenshot of Result"}], "status": "active", "created_date": "2025-10-04"}