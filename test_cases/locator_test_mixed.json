{"test_case_id": "mixed_locator_test_001", "name": "Mixed Locator Types Test", "description": "Test case using multiple locator types (xpath, accessibility_id, id) to verify locator repository tracking", "platform": "android", "actions": [{"action_id": "launch_app_3", "type": "launchApp", "name": "Launch Calculator App", "package_name": "com.coloros.calculator", "activity_name": ".Calculator"}, {"action_id": "click_clear_xpath", "type": "clickElement", "name": "<PERSON><PERSON> <PERSON> (XPath)", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc='clear']"}, {"action_id": "click_number_7_acc", "type": "clickElement", "name": "Click Number 7 (Accessibility ID)", "locator_type": "accessibility_id", "locator_value": "seven", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//android.widget.Button[@text='7']"}, {"locator_type": "id", "locator_value": "com.coloros.calculator:id/digit_7"}]}, {"action_id": "click_minus_xpath", "type": "clickElement", "name": "<PERSON><PERSON> <PERSON> (XPath)", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc='minus']"}, {"action_id": "click_number_4_id", "type": "clickElement", "name": "Click Number 4 (ID)", "locator_type": "id", "locator_value": "com.coloros.calculator:id/digit_4", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//android.widget.Button[@text='4']"}, {"locator_type": "accessibility_id", "locator_value": "four"}]}, {"action_id": "conditional_check", "type": "ifThenSteps", "name": "Check if Result Display Exists", "if_condition": {"locator_type": "xpath", "locator_value": "//android.widget.TextView[@resource-id='com.coloros.calculator:id/result']", "condition": "exists"}, "then_action": {"type": "clickElement", "locator_type": "accessibility_id", "locator_value": "equals"}}, {"action_id": "verify_result_mixed", "type": "waitTill", "name": "Verify Result is 3", "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,'3')]", "timeout": 5000}, {"action_id": "screenshot_final", "type": "takeScreenshot", "name": "Take Final Screenshot"}], "status": "active", "created_date": "2025-10-04"}