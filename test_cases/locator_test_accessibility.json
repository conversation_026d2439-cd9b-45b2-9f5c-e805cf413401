{"test_case_id": "accessibility_locator_test_001", "name": "Accessibility ID Locator Test", "description": "Test case using accessibility_id locators to verify locator repository tracking", "platform": "android", "actions": [{"action_id": "launch_app_2", "type": "launchApp", "name": "Launch Calculator App", "package_name": "com.coloros.calculator", "activity_name": ".Calculator"}, {"action_id": "click_clear", "type": "clickElement", "name": "Click Clear Button", "locator_type": "accessibility_id", "locator_value": "clear"}, {"action_id": "click_number_9", "type": "clickElement", "name": "Click Number 9", "locator_type": "accessibility_id", "locator_value": "nine", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//android.widget.Button[@text='9']"}]}, {"action_id": "click_multiply", "type": "clickElement", "name": "<PERSON><PERSON> Multiply <PERSON>", "locator_type": "accessibility_id", "locator_value": "multiply"}, {"action_id": "click_number_2", "type": "clickElement", "name": "Click Number 2", "locator_type": "accessibility_id", "locator_value": "two", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//android.widget.Button[@text='2']"}]}, {"action_id": "click_equals_2", "type": "clickElement", "name": "<PERSON><PERSON> Equals <PERSON><PERSON>", "locator_type": "accessibility_id", "locator_value": "equals"}, {"action_id": "screenshot_result_2", "type": "takeScreenshot", "name": "Take Screenshot of Result"}], "status": "active", "created_date": "2025-10-04"}