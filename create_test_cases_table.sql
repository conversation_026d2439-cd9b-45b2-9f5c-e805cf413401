-- Create test_cases table with optimized schema and indexing
-- This table will store complete test case information with efficient querying capabilities

CREATE TABLE IF NOT EXISTS test_cases (
    test_case_id TEXT PRIMARY KEY,  -- Unique identifier for each test case
    step_count INTEGER NOT NULL,    -- Number of steps in the test case
    created_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,  -- Creation timestamp
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Last modification timestamp
    data TEXT NOT NULL              -- JSON column containing complete test case information
);

-- Create index on test_case_id for optimal performance (PRIMARY KEY already creates this)
-- Additional indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_test_cases_created_date ON test_cases(created_date);
CREATE INDEX IF NOT EXISTS idx_test_cases_modified_date ON test_cases(modified_date);
CREATE INDEX IF NOT EXISTS idx_test_cases_step_count ON test_cases(step_count);

-- <PERSON>reate trigger to automatically update modified_date on updates
CREATE TRIGGER IF NOT EXISTS update_test_cases_modified_date 
    AFTER UPDATE ON test_cases
    FOR EACH ROW
    WHEN NEW.modified_date = OLD.modified_date
BEGIN
    UPDATE test_cases SET modified_date = CURRENT_TIMESTAMP WHERE test_case_id = NEW.test_case_id;
END;

-- Insert sample test case data to verify table structure
INSERT OR IGNORE INTO test_cases (test_case_id, step_count, data) VALUES 
('TC001', 5, '{"name": "Login Test", "description": "Test user login functionality", "steps": [{"action": "open_app", "target": "login_screen"}, {"action": "enter_text", "target": "username_field", "value": "testuser"}, {"action": "enter_text", "target": "password_field", "value": "password123"}, {"action": "tap", "target": "login_button"}, {"action": "verify", "target": "home_screen", "expected": "visible"}], "platform": "both", "priority": "high"}'),
('TC002', 3, '{"name": "Navigation Test", "description": "Test app navigation between screens", "steps": [{"action": "tap", "target": "menu_button"}, {"action": "tap", "target": "settings_option"}, {"action": "verify", "target": "settings_screen", "expected": "visible"}], "platform": "both", "priority": "medium"}');