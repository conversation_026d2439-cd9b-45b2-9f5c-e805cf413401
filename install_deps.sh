#!/bin/bash

# Exit on error
set -e

echo "===== Installing dependencies for Mobile App Automation Tool ====="

# Install system dependencies if not already installed
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if ! command -v brew &> /dev/null; then
        echo "Homebrew not found. Please install Homebrew first: https://brew.sh/"
        exit 1
    fi
    
    echo "Checking for Tesseract OCR..."
    if ! brew list tesseract &> /dev/null; then
        echo "Installing Tesseract OCR..."
        brew install tesseract
    else
        echo "Tesseract OCR already installed."
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "Checking for Tesseract OCR..."
    if ! command -v tesseract &> /dev/null; then
        echo "Installing Tesseract OCR..."
        sudo apt-get update
        sudo apt-get install -y tesseract-ocr
    else
        echo "Tesseract OCR already installed."
    fi
fi

# Create a Python virtual environment (optional but recommended)
echo "Do you want to create a Python virtual environment? (y/n)"
read create_venv

if [[ "$create_venv" == "y" ]]; then
    echo "Creating Python virtual environment..."
    python -m venv venv
    
    # Activate the virtual environment
    if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "linux-gnu"* ]]; then
        source venv/bin/activate
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        source venv/Scripts/activate
    fi
    
    echo "Virtual environment created and activated."
fi

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install Cython first (required for gevent)
echo "Installing Cython..."
pip install Cython==3.0.9

# Install main requirements
echo "Installing main requirements..."
pip install --upgrade -r requirements.txt



# iOS specific setup
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Setting up iOS device support..."
    pip install facebook-wda
    pip install tidevice
    
    echo "Do you want to install WebDriverAgent? (y/n)"
    read install_wda
    
    if [[ "$install_wda" == "y" ]]; then
        echo "Installing WebDriverAgent..."
        pip install -U tidevice
        
        echo "You can start WebDriverAgent on your connected iOS device with:"
        echo "tidevice wdaproxy -d"
    fi
fi

# Appium driver setup
echo "Checking Appium installation..."
if command -v appium &> /dev/null; then
    echo "Appium is installed. Installing required drivers..."
    
    # Check if XCUITest driver is installed
    XCUITEST_INSTALLED=$(appium driver list --installed | grep -c xcuitest || true)
    
    if [ "$XCUITEST_INSTALLED" -eq 0 ]; then
        echo "Installing Appium XCUITest driver..."
        appium driver install xcuitest || appium driver update xcuitest
    else
        echo "Appium XCUITest driver already installed."
    fi
    
    # Check if UiAutomator2 driver is installed (for Android)
    UIAUTOMATOR2_INSTALLED=$(appium driver list --installed | grep -c uiautomator2 || true)
    
    if [ "$UIAUTOMATOR2_INSTALLED" -eq 0 ]; then
        echo "Installing Appium UiAutomator2 driver..."
        appium driver install uiautomator2 || appium driver update uiautomator2
    else
        echo "Appium UiAutomator2 driver already installed."
    fi
    
    # Restart Appium to apply changes
    echo "Restarting Appium service..."
    pkill -f appium || true
    sleep 2
    
    # Start Appium in background with proper configuration
    appium --base-path /wd/hub --relaxed-security > appium.log 2>&1 &
    sleep 5
    echo "Appium started with required drivers."
else
    echo "WARNING: Appium is not installed or not in PATH. Please install Appium:"
    echo "npm install -g appium@next"
    echo "Then run:"
    echo "appium driver install xcuitest"
    echo "appium driver install uiautomator2"
fi

echo "===== All dependencies have been installed ====="
echo "To start the application, run: python run.py"