"""
Centralized error messages and codes for consistency across web backend, GUI, and logs.
"""

SECURE_BUILD_INCOMPLETE_CODE = "SECURE_BUILD_INCOMPLETE"
SECURE_BUILD_INCOMPLETE_TITLE = "Secure Build Incomplete"
SECURE_BUILD_INCOMPLETE_MESSAGE = (
    "The obfuscated platform application is missing. For security, only PyArmor-protected builds are allowed to run."
)
SECURE_BUILD_INCOMPLETE_ACTION = "Contact your administrator to rebuild the secure distribution."

NETWORK_ERROR_CODE = "NETWORK_ERROR"
NETWORK_ERROR_MESSAGE = "Network error communicating with backend. Please check your connection and try again."

LAUNCH_FAILED_CODE = "LAUNCH_FAILED"
LAUNCH_FAILED_MESSAGE = "Platform launch failed. Please review logs and try again."

def format_secure_build_missing(expected_path: str, details: str | None = None) -> str:
    msg = f"{SECURE_BUILD_INCOMPLETE_MESSAGE}\nMissing: {expected_path}"
    if details:
        msg += f"\nDetails: {details}"
    return msg

