"""
AI Error Handling Module for Mobile App Automation

This module provides comprehensive error handling and recovery mechanisms
for AI-powered test healing operations.
"""

import logging
import traceback
from typing import Dict, Any, Optional, Callable, Type, Union
from enum import Enum
from dataclasses import dataclass
from functools import wraps

from .ai_logging import get_ai_logger, LogLevel


class AIErrorType(Enum):
    """Types of AI-related errors"""
    API_CONNECTION_ERROR = "api_connection_error"
    API_AUTHENTICATION_ERROR = "api_authentication_error"
    API_RATE_LIMIT_ERROR = "api_rate_limit_error"
    API_TIMEOUT_ERROR = "api_timeout_error"
    INVALID_RESPONSE_ERROR = "invalid_response_error"
    SCREENSHOT_CAPTURE_ERROR = "screenshot_capture_error"
    UI_PARSING_ERROR = "ui_parsing_error"
    ELEMENT_NOT_FOUND_ERROR = "element_not_found_error"
    WEBDRIVER_ERROR = "webdriver_error"
    CONFIGURATION_ERROR = "configuration_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class AIErrorContext:
    """Context information for AI errors"""
    error_type: AIErrorType
    original_error: Exception
    session_id: str
    platform: str
    test_case: str
    step_index: int
    action_description: str
    recovery_attempted: bool = False
    recovery_successful: bool = False
    additional_info: Optional[Dict[str, Any]] = None


class AIErrorHandler:
    """Comprehensive error handler for AI operations"""
    
    def __init__(self, platform: str = "Unknown"):
        """
        Initialize AI error handler
        
        Args:
            platform: Platform identifier ("iOS" or "Android")
        """
        self.platform = platform
        self.logger = get_ai_logger(platform)
        self.error_counts: Dict[AIErrorType, int] = {}
        self.recovery_strategies: Dict[AIErrorType, Callable] = {}
        
        # Register default recovery strategies
        self._register_default_recovery_strategies()
    
    def _register_default_recovery_strategies(self):
        """Register default error recovery strategies"""
        self.recovery_strategies.update({
            AIErrorType.API_CONNECTION_ERROR: self._recover_api_connection,
            AIErrorType.API_TIMEOUT_ERROR: self._recover_api_timeout,
            AIErrorType.SCREENSHOT_CAPTURE_ERROR: self._recover_screenshot_capture,
            AIErrorType.UI_PARSING_ERROR: self._recover_ui_parsing,
            AIErrorType.ELEMENT_NOT_FOUND_ERROR: self._recover_element_not_found,
            AIErrorType.WEBDRIVER_ERROR: self._recover_webdriver_error
        })
    
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> AIErrorContext:
        """
        Handle AI-related error with appropriate recovery strategy
        
        Args:
            error: The exception that occurred
            context: Context information about the error
            
        Returns:
            AIErrorContext with error details and recovery information
        """
        # Classify the error
        error_type = self._classify_error(error)
        
        # Create error context
        error_context = AIErrorContext(
            error_type=error_type,
            original_error=error,
            session_id=context.get('session_id', 'unknown'),
            platform=self.platform,
            test_case=context.get('test_case', 'Unknown'),
            step_index=context.get('step_index', 0),
            action_description=context.get('action_description', 'Unknown action'),
            additional_info=context.get('additional_info', {})
        )
        
        # Update error counts
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Log the error
        self._log_error(error_context)
        
        # Attempt recovery if strategy exists
        if error_type in self.recovery_strategies:
            try:
                error_context.recovery_attempted = True
                recovery_result = self.recovery_strategies[error_type](error_context)
                error_context.recovery_successful = recovery_result
                
                if recovery_result:
                    self.logger.ai_logger.info(f"✅ Error recovery successful for {error_type.value}")
                else:
                    self.logger.ai_logger.warning(f"❌ Error recovery failed for {error_type.value}")
                    
            except Exception as recovery_error:
                self.logger.ai_logger.error(f"🚨 Recovery strategy failed: {recovery_error}")
                error_context.recovery_successful = False
        
        return error_context
    
    def _classify_error(self, error: Exception) -> AIErrorType:
        """Classify error into appropriate type"""
        error_message = str(error).lower()
        error_type_name = type(error).__name__.lower()
        
        # API-related errors
        if 'connection' in error_message or 'network' in error_message:
            return AIErrorType.API_CONNECTION_ERROR
        elif 'authentication' in error_message or 'unauthorized' in error_message or '401' in error_message:
            return AIErrorType.API_AUTHENTICATION_ERROR
        elif 'rate limit' in error_message or '429' in error_message:
            return AIErrorType.API_RATE_LIMIT_ERROR
        elif 'timeout' in error_message or 'timed out' in error_message:
            return AIErrorType.API_TIMEOUT_ERROR
        elif 'json' in error_message or 'parse' in error_message:
            return AIErrorType.INVALID_RESPONSE_ERROR
        
        # Screenshot and UI errors
        elif 'screenshot' in error_message:
            return AIErrorType.SCREENSHOT_CAPTURE_ERROR
        elif 'xml' in error_message or 'page source' in error_message:
            return AIErrorType.UI_PARSING_ERROR
        
        # WebDriver errors
        elif 'nosuchelementexception' in error_type_name or 'element not found' in error_message:
            return AIErrorType.ELEMENT_NOT_FOUND_ERROR
        elif 'webdriver' in error_message or 'selenium' in error_message or 'appium' in error_message:
            return AIErrorType.WEBDRIVER_ERROR
        
        # Configuration errors
        elif 'configuration' in error_message or 'api key' in error_message:
            return AIErrorType.CONFIGURATION_ERROR
        
        return AIErrorType.UNKNOWN_ERROR
    
    def _log_error(self, error_context: AIErrorContext):
        """Log error with appropriate level and details"""
        error_details = {
            'error_type': error_context.error_type.value,
            'error_message': str(error_context.original_error),
            'session_id': error_context.session_id,
            'test_case': error_context.test_case,
            'step_index': error_context.step_index,
            'action_description': error_context.action_description,
            'traceback': traceback.format_exc()
        }
        
        if error_context.additional_info:
            error_details.update(error_context.additional_info)
        
        # Log to AI error logger
        self.logger.log_api_error(
            error_context.session_id,
            error_context.error_type.value,
            str(error_context.original_error),
            error_context.test_case
        )
        
        # Log detailed error information
        self.logger.ai_logger.error(f"🚨 AI Error Details:")
        self.logger.ai_logger.error(f"   Type: {error_context.error_type.value}")
        self.logger.ai_logger.error(f"   Message: {str(error_context.original_error)}")
        self.logger.ai_logger.error(f"   Session: {error_context.session_id}")
        self.logger.ai_logger.error(f"   Action: {error_context.action_description}")
    
    # Recovery strategy implementations
    def _recover_api_connection(self, error_context: AIErrorContext) -> bool:
        """Attempt to recover from API connection errors"""
        try:
            import time
            import requests
            
            # Wait and retry connection
            time.sleep(2)
            
            # Test connection to API
            api_url = "https://api.together.xyz/v1/models"
            response = requests.get(api_url, timeout=10)
            
            return response.status_code == 200
            
        except Exception:
            return False
    
    def _recover_api_timeout(self, error_context: AIErrorContext) -> bool:
        """Attempt to recover from API timeout errors"""
        try:
            import time
            
            # Wait before retry
            time.sleep(5)
            
            # Could implement exponential backoff here
            return True
            
        except Exception:
            return False
    
    def _recover_screenshot_capture(self, error_context: AIErrorContext) -> bool:
        """Attempt to recover from screenshot capture errors"""
        try:
            # Could implement alternative screenshot methods
            # For now, just indicate recovery is possible
            return True
            
        except Exception:
            return False
    
    def _recover_ui_parsing(self, error_context: AIErrorContext) -> bool:
        """Attempt to recover from UI parsing errors"""
        try:
            # Could implement alternative parsing methods
            # For now, just indicate recovery is possible
            return True
            
        except Exception:
            return False
    
    def _recover_element_not_found(self, error_context: AIErrorContext) -> bool:
        """Attempt to recover from element not found errors"""
        try:
            # This is where AI healing would typically be triggered
            # For now, just indicate recovery is possible
            return True
            
        except Exception:
            return False
    
    def _recover_webdriver_error(self, error_context: AIErrorContext) -> bool:
        """Attempt to recover from WebDriver errors"""
        try:
            import time
            
            # Wait for WebDriver to stabilize
            time.sleep(1)
            return True
            
        except Exception:
            return False
    
    def register_recovery_strategy(self, error_type: AIErrorType, 
                                 strategy: Callable[[AIErrorContext], bool]):
        """Register custom recovery strategy for error type"""
        self.recovery_strategies[error_type] = strategy
        self.logger.ai_logger.info(f"Registered custom recovery strategy for {error_type.value}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        total_errors = sum(self.error_counts.values())
        
        stats = {
            'platform': self.platform,
            'total_errors': total_errors,
            'error_breakdown': {error_type.value: count for error_type, count in self.error_counts.items()},
            'most_common_error': None,
            'recovery_strategies_available': len(self.recovery_strategies)
        }
        
        if self.error_counts:
            most_common = max(self.error_counts.items(), key=lambda x: x[1])
            stats['most_common_error'] = {
                'type': most_common[0].value,
                'count': most_common[1],
                'percentage': (most_common[1] / total_errors) * 100
            }
        
        return stats


def ai_error_handler(platform: str = "Unknown"):
    """Decorator for AI error handling"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = AIErrorHandler(platform)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Extract context from function arguments
                context = {
                    'session_id': kwargs.get('session_id', 'unknown'),
                    'test_case': kwargs.get('test_case', 'Unknown'),
                    'step_index': kwargs.get('step_index', 0),
                    'action_description': kwargs.get('action_description', func.__name__),
                    'additional_info': {
                        'function': func.__name__,
                        'args_count': len(args),
                        'kwargs_keys': list(kwargs.keys())
                    }
                }
                
                error_context = error_handler.handle_error(e, context)
                
                # Re-raise if recovery was not successful
                if not error_context.recovery_successful:
                    raise
                
                # If recovery was successful, could retry the function
                # For now, just return None to indicate handled error
                return None
        
        return wrapper
    return decorator


def create_error_context(error: Exception, session_id: str, platform: str,
                        test_case: str = "Unknown", step_index: int = 0,
                        action_description: str = "Unknown action") -> Dict[str, Any]:
    """Helper function to create error context"""
    return {
        'session_id': session_id,
        'platform': platform,
        'test_case': test_case,
        'step_index': step_index,
        'action_description': action_description,
        'additional_info': {
            'error_type': type(error).__name__,
            'error_message': str(error)
        }
    }


def handle_ai_operation_safely(operation: Callable, context: Dict[str, Any],
                              platform: str = "Unknown") -> tuple[bool, Any, Optional[AIErrorContext]]:
    """
    Safely execute AI operation with error handling
    
    Args:
        operation: Function to execute
        context: Error context information
        platform: Platform identifier
        
    Returns:
        Tuple of (success, result, error_context)
    """
    error_handler = AIErrorHandler(platform)
    
    try:
        result = operation()
        return True, result, None
    except Exception as e:
        error_context = error_handler.handle_error(e, context)
        return False, None, error_context


# Global error handlers
_error_handlers: Dict[str, AIErrorHandler] = {}


def get_error_handler(platform: str = "Unknown") -> AIErrorHandler:
    """Get or create error handler for platform"""
    if platform not in _error_handlers:
        _error_handlers[platform] = AIErrorHandler(platform)
    return _error_handlers[platform]
