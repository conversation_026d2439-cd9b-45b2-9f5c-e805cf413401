"""
Compatibility shim for directory paths DB module.
Allows imports like `from utils.directory_paths_db import DirectoryPathsDB`
by delegating to app.utils.directory_paths_db.
"""

import sys
import os

# Ensure project root is on sys.path so app.utils can be imported
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

try:
    # Re-export symbols from the canonical module
    from app.utils.directory_paths_db import DirectoryPathsDB, directory_paths_db  # type: ignore
except Exception as e:
    # Provide a clearer error for debugging
    raise ImportError(f"Failed to import DirectoryPathsDB from app.utils.directory_paths_db: {e}")

