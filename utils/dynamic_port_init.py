#!/usr/bin/env python3
"""
Dynamic Port Initialization for Mobile Automation Framework

This module handles dynamic port allocation for iOS and Android applications,
ensuring no port conflicts when running multiple instances.
"""

import os
import sys
import logging
from typing import Dict, Optional
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.port_manager import get_port_manager, ensure_platform_ports

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_platform_ports(platform: str, custom_ports: Optional[Dict[str, int]] = None) -> Dict[str, int]:
    """
    Initialize dynamic ports for a platform (iOS or Android)
    
    Args:
        platform: 'ios' or 'android'
        custom_ports: Optional custom port configuration
        
    Returns:
        Dictionary with allocated ports: {'flask': port, 'appium': port, 'wda': port}
    """
    try:
        logger.info(f"Initializing dynamic ports for {platform}")
        
        # Get port manager and allocate ports
        service_config = ensure_platform_ports(platform, custom_ports)
        
        # Set environment variables for the allocated ports
        port_mapping = {
            'flask': service_config.flask_port,
            'appium': service_config.appium_port,
            'wda': service_config.wda_port
        }
        
        # Set environment variables
        os.environ['FLASK_PORT'] = str(port_mapping['flask'])
        os.environ['APPIUM_PORT'] = str(port_mapping['appium'])
        os.environ['WDA_PORT'] = str(port_mapping['wda'])
        
        logger.info(f"Allocated ports for {platform}: {port_mapping}")
        return port_mapping
        
    except Exception as e:
        logger.error(f"Failed to initialize ports for {platform}: {e}")
        # Fallback to default ports if dynamic allocation fails
        if platform == 'ios':
            fallback_ports = {'flask': 8080, 'appium': 4723, 'wda': 8200}
        else:  # android
            fallback_ports = {'flask': 8081, 'appium': 4724, 'wda': 8300}
            
        logger.warning(f"Using fallback ports for {platform}: {fallback_ports}")
        
        # Set environment variables for fallback ports
        os.environ['FLASK_PORT'] = str(fallback_ports['flask'])
        os.environ['APPIUM_PORT'] = str(fallback_ports['appium'])
        os.environ['WDA_PORT'] = str(fallback_ports['wda'])
        
        return fallback_ports

def cleanup_platform_ports(platform: str) -> bool:
    """
    Clean up allocated ports for a platform
    
    Args:
        platform: 'ios' or 'android'
        
    Returns:
        True if cleanup was successful
    """
    try:
        from utils.port_manager import cleanup_platform_ports as cleanup_ports
        cleaned_count = cleanup_ports(platform, force=False)
        logger.info(f"Cleaned up {cleaned_count} ports for {platform}")
        return True
    except Exception as e:
        logger.error(f"Failed to cleanup ports for {platform}: {e}")
        return False

def get_dashboard_port() -> int:
    """
    Get the appropriate dashboard port based on build type
    
    Returns:
        Port number for dashboard
    """
    # Use port 8080 for secure builds, fallback to 8090 for development
    if os.environ.get('SECURE_BUILD', 'false').lower() == 'true':
        default_port = 8080
    else:
        default_port = 8090
        
    return int(os.environ.get('DASHBOARD_PORT', default_port))

def check_port_conflicts(ports_to_check: Optional[list] = None) -> Dict[str, bool]:
    """
    Check for port conflicts across all allocated ports
    
    Args:
        ports_to_check: Optional list of specific ports to check
    
    Returns:
        Dict mapping service types to conflict status
    """
    try:
        port_manager = get_port_manager()
        
        conflicts = {
            'flask': False,
            'appium': False,
            'wda': False
        }
        
        if ports_to_check:
            # Check if any of the specified ports are already in use
            used_ports = set()
            
            # Get all currently allocated ports from the port manager
            for port, allocation in port_manager.allocations.items():
                used_ports.add(port)
            
            for port in ports_to_check:
                if port in used_ports:
                    # Check if port is actually available
                    if not port_manager.is_port_available(port):
                        # Determine which service type this port belongs to
                        allocation = port_manager.allocations.get(port)
                        if allocation:
                            service_type = allocation.service_type
                            if service_type in conflicts:
                                conflicts[service_type] = True
                        else:
                            # Unknown service, mark all as conflicted
                            for service in conflicts.keys():
                                conflicts[service] = True
                        
        return conflicts
        
    except Exception as e:
        logger.error(f"Failed to check port conflicts: {e}")
        return {'flask': False, 'appium': False, 'wda': False}  # Return no conflicts on error

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Dynamic Port Initialization')
    parser.add_argument('platform', choices=['ios', 'android'], help='Platform to initialize')
    parser.add_argument('--cleanup', action='store_true', help='Cleanup ports instead of initializing')
    parser.add_argument('--check-conflicts', action='store_true', help='Check for port conflicts')
    
    args = parser.parse_args()
    
    if args.check_conflicts:
        conflicts = check_port_conflicts()
        print(f"Port conflicts: {conflicts}")
    elif args.cleanup:
        success = cleanup_platform_ports(args.platform)
        print(f"Cleanup {'successful' if success else 'failed'} for {args.platform}")
    else:
        ports = initialize_platform_ports(args.platform)
        print(f"Initialized ports for {args.platform}: {ports}")