"""
Test Integration Helper for AI-Enhanced Test Processing

This module provides utilities to integrate the enhanced test processor
with existing test runners and execution flows.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

from .enhanced_test_processor import EnhancedTestProcessor, TestCaseResult

logger = logging.getLogger(__name__)


class TestIntegrationHelper:
    """Helper class for integrating AI-enhanced test processing"""
    
    def __init__(self, platform: str = "Unknown"):
        """
        Initialize test integration helper
        
        Args:
            platform: Platform identifier ("iOS" or "Android")
        """
        self.platform = platform
        self.logger = logging.getLogger(__name__)
        self.enhanced_processor = None
    
    def initialize_enhanced_processing(self, action_executor) -> bool:
        """
        Initialize enhanced test processing with the given action executor
        
        Args:
            action_executor: The action executor instance
            
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.enhanced_processor = EnhancedTestProcessor(action_executor, self.platform)
            self.logger.info(f"✅ Enhanced test processing initialized for {self.platform}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize enhanced test processing: {e}")
            return False
    
    def execute_test_case_enhanced(self, test_case_data: Dict[str, Any], 
                                  context: Optional[Dict[str, Any]] = None) -> TestCaseResult:
        """
        Execute test case with enhanced processing
        
        Args:
            test_case_data: JSON test case data
            context: Optional execution context
            
        Returns:
            TestCaseResult with detailed execution information
        """
        if not self.enhanced_processor:
            raise RuntimeError("Enhanced processor not initialized. Call initialize_enhanced_processing() first.")
        
        return self.enhanced_processor.execute_test_case(test_case_data, context)
    
    def execute_test_case_from_file(self, test_case_file: str, 
                                   test_cases_dir: str = "test_cases",
                                   context: Optional[Dict[str, Any]] = None) -> TestCaseResult:
        """
        Execute test case from JSON file with enhanced processing
        
        Args:
            test_case_file: Test case filename (with or without .json extension)
            test_cases_dir: Directory containing test cases
            context: Optional execution context
            
        Returns:
            TestCaseResult with detailed execution information
        """
        if not self.enhanced_processor:
            raise RuntimeError("Enhanced processor not initialized. Call initialize_enhanced_processing() first.")
        
        # Load test case data
        test_case_data = self._load_test_case_file(test_case_file, test_cases_dir)
        if not test_case_data:
            raise FileNotFoundError(f"Could not load test case: {test_case_file}")
        
        return self.enhanced_processor.execute_test_case(test_case_data, context)
    
    def _load_test_case_file(self, test_case_file: str, test_cases_dir: str) -> Optional[Dict[str, Any]]:
        """Load test case data from file"""
        try:
            # Ensure filename has .json extension
            if not test_case_file.endswith('.json'):
                test_case_file += '.json'
            
            file_path = Path(test_cases_dir) / test_case_file
            
            if not file_path.exists():
                self.logger.error(f"Test case file not found: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                test_case_data = json.load(f)
            
            self.logger.info(f"Loaded test case: {test_case_file}")
            return test_case_data
            
        except Exception as e:
            self.logger.error(f"Error loading test case {test_case_file}: {e}")
            return None
    
    def convert_legacy_result(self, legacy_success: bool, legacy_message: str, 
                             test_case_name: str, duration: float = 0.0) -> TestCaseResult:
        """
        Convert legacy test execution results to enhanced format
        
        Args:
            legacy_success: Legacy success boolean
            legacy_message: Legacy result message
            test_case_name: Name of the test case
            duration: Execution duration
            
        Returns:
            TestCaseResult compatible with enhanced processing
        """
        from .enhanced_test_processor import TestStepResult
        
        # Create a single step result representing the legacy execution
        step_result = TestStepResult(
            step_index=0,
            action={'type': 'legacy_execution', 'message': legacy_message},
            success=legacy_success,
            duration=duration,
            error_message=None if legacy_success else legacy_message,
            ai_healing_used=False
        )
        
        return TestCaseResult(
            test_case_name=test_case_name,
            total_steps=1,
            successful_steps=1 if legacy_success else 0,
            failed_steps=0 if legacy_success else 1,
            total_duration=duration,
            step_results=[step_result],
            ai_healing_sessions=0,
            successful_healings=0,
            overall_success=legacy_success
        )
    
    def generate_enhanced_report(self, result: TestCaseResult, 
                               output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate enhanced test report with AI healing information
        
        Args:
            result: TestCaseResult to generate report for
            output_file: Optional file to save report to
            
        Returns:
            Report data as dictionary
        """
        report_data = {
            'test_case_name': result.test_case_name,
            'platform': self.platform,
            'execution_summary': {
                'overall_success': result.overall_success,
                'total_duration': result.total_duration,
                'total_steps': result.total_steps,
                'successful_steps': result.successful_steps,
                'failed_steps': result.failed_steps,
                'success_rate': (result.successful_steps / result.total_steps) * 100 if result.total_steps > 0 else 0
            },
            'ai_healing_summary': {
                'sessions': result.ai_healing_sessions,
                'successful_healings': result.successful_healings,
                'healing_success_rate': (result.successful_healings / result.ai_healing_sessions) * 100 if result.ai_healing_sessions > 0 else 0,
                'enabled': result.ai_healing_sessions > 0 or self._is_ai_healing_available()
            },
            'step_details': []
        }
        
        # Add detailed step information
        for step_result in result.step_results:
            step_detail = {
                'step_index': step_result.step_index,
                'action_type': step_result.action.get('type', 'unknown'),
                'success': step_result.success,
                'duration': step_result.duration,
                'ai_healing_used': step_result.ai_healing_used,
                'healing_attempts': step_result.healing_attempts
            }
            
            if not step_result.success:
                step_detail['error_message'] = step_result.error_message
                step_detail['original_error'] = step_result.original_error
            
            if step_result.ai_healing_used:
                step_detail['healing_session_id'] = step_result.healing_session_id
            
            report_data['step_details'].append(step_detail)
        
        # Save to file if requested
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, default=str)
                self.logger.info(f"Enhanced report saved to: {output_file}")
            except Exception as e:
                self.logger.error(f"Failed to save report to {output_file}: {e}")
        
        return report_data
    
    def _is_ai_healing_available(self) -> bool:
        """Check if AI healing is available"""
        return (
            self.enhanced_processor and 
            self.enhanced_processor.ai_healing_available
        )
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get integration status information"""
        status = {
            'platform': self.platform,
            'enhanced_processor_initialized': self.enhanced_processor is not None,
            'ai_healing_available': self._is_ai_healing_available()
        }
        
        if self.enhanced_processor:
            status['ai_healing_stats'] = self.enhanced_processor.get_ai_healing_statistics()
        
        return status


def create_integration_helper(platform: str) -> TestIntegrationHelper:
    """Factory function to create test integration helper"""
    return TestIntegrationHelper(platform)


def wrap_legacy_execution(action_executor, platform: str):
    """
    Decorator to wrap legacy test execution methods with enhanced processing
    
    Args:
        action_executor: The action executor instance
        platform: Platform identifier
        
    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Initialize integration helper
            helper = TestIntegrationHelper(platform)
            if helper.initialize_enhanced_processing(action_executor):
                logger.info(f"🔄 Wrapping legacy execution with enhanced processing for {platform}")
                
                # Try to extract test case data from arguments
                test_case_data = None
                if args and isinstance(args[0], dict):
                    test_case_data = args[0]
                elif 'test_case_data' in kwargs:
                    test_case_data = kwargs['test_case_data']
                
                if test_case_data:
                    # Use enhanced processing
                    try:
                        result = helper.execute_test_case_enhanced(test_case_data)
                        return result.overall_success, f"Enhanced execution completed with {result.successful_steps}/{result.total_steps} successful steps"
                    except Exception as e:
                        logger.error(f"Enhanced execution failed, falling back to legacy: {e}")
            
            # Fall back to legacy execution
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
