#!/usr/bin/env python3
"""
Consolidated Database Adapter

This module provides an adapter to use the consolidated database
instead of the separate database files used by the original application.
"""

import sqlite3
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class ConsolidatedDBAdapter:
    """
    Adapter class to interface with the consolidated database.
    Provides methods that match the original database interfaces.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the consolidated database adapter.
        
        Args:
            db_path: Path to the consolidated database file.
                    If None, uses default location: database_consolidation/consolidated.db
        """
        if db_path:
            self.db_path = Path(db_path)
        else:
            # Default location in database_consolidation directory
            project_root = Path(__file__).resolve().parent.parent
            self.db_path = project_root / 'database_consolidation' / 'consolidated.db'
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"Consolidated database not found at: {self.db_path}")
        
        logger.info(f"Using consolidated database at: {self.db_path}")
    
    def get_connection(self):
        """Get a database connection."""
        return sqlite3.connect(str(self.db_path))
    
    # Global Values methods (compatible with GlobalValuesDB)
    def get_global_value(self, name: str, platform: str = 'shared') -> Optional[str]:
        """Get a global value by name and platform."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT key_value FROM globals WHERE key_name = ? AND platform = ?",
                (name, platform)
            )
            result = cursor.fetchone()
            return result[0] if result else None
        finally:
            conn.close()
    
    def set_global_value(self, name: str, value: str, platform: str = 'shared', 
                        value_type: str = 'string', description: str = ''):
        """Set a global value."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            global_id = f"global_{platform}_default_{name}"
            cursor.execute(
                """
                INSERT OR REPLACE INTO globals 
                (global_id, platform, category, key_name, key_value, value_type, description, port_specific)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (global_id, platform, 'variable', name, value, value_type, description, None)
            )
            conn.commit()
        finally:
            conn.close()
    
    def get_all_global_values(self, platform: str = 'shared') -> Dict[str, str]:
        """Get all global values for a platform."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT key_name, key_value FROM globals WHERE platform = ?",
                (platform,)
            )
            return dict(cursor.fetchall())
        finally:
            conn.close()
    
    # Settings methods (compatible with SharedConfigDB)
    def get_setting(self, key: str, platform: str = 'shared') -> Optional[str]:
        """Get a setting value."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT setting_value FROM settings WHERE setting_key = ? AND platform = ?",
                (key, platform)
            )
            result = cursor.fetchone()
            return result[0] if result else None
        finally:
            conn.close()
    
    def set_setting(self, key: str, value: str, platform: str = 'shared', description: str = ''):
        """Set a setting value."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            setting_id = f"setting_{platform}_default_{key}"
            cursor.execute(
                """
                INSERT OR REPLACE INTO settings 
                (setting_id, platform, setting_key, setting_value, description, port_specific)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (setting_id, platform, key, value, description, None)
            )
            conn.commit()
        finally:
            conn.close()
    
    # Directory paths methods
    def get_path(self, path_type: str) -> Optional[str]:
        """Get a directory path by type."""
        return self.get_setting(f"path_{path_type}")
    
    def save_path(self, path_type: str, path_value: str):
        """Save a directory path."""
        self.set_setting(f"path_{path_type}", path_value, description=f"Directory path for {path_type}")
    
    # Environment methods
    def get_environments(self) -> List[Dict[str, Any]]:
        """Get all environments."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT environment_id, name, description FROM environments ORDER BY name"
            )
            return [
                {'id': row[0], 'name': row[1], 'description': row[2]}
                for row in cursor.fetchall()
            ]
        finally:
            conn.close()
    
    def create_environment(self, name: str, description: str = '') -> str:
        """Create a new environment."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            environment_id = f"env_{name.lower().replace(' ', '_')}"
            cursor.execute(
                """
                INSERT OR REPLACE INTO environments 
                (environment_id, name, description)
                VALUES (?, ?, ?)
                """,
                (environment_id, name, description)
            )
            conn.commit()
            return environment_id
        finally:
            conn.close()
    
    # Test suite methods
    def get_test_suites(self, platform: str = None) -> List[Dict[str, Any]]:
        """Get test suites, optionally filtered by platform."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if platform:
                cursor.execute(
                    "SELECT suite_id, name, description, platform, status, json_payload, created_at, updated_at FROM test_suites WHERE platform = ?",
                    (platform,)
                )
            else:
                cursor.execute(
                    "SELECT suite_id, name, description, platform, status, json_payload, created_at, updated_at FROM test_suites"
                )
            return [
                {
                    'id': row[0],
                    'suite_id': row[0],
                    'name': row[1], 
                    'description': row[2],
                    'platform': row[3],
                    'status': row[4],
                    'json_payload': row[5],
                    'created_at': row[6],
                    'updated_at': row[7]
                }
                for row in cursor.fetchall()
            ]
        finally:
            conn.close()
    
    # Execution tracking methods
    def log_execution(self, test_name: str, status: str, platform: str, 
                     execution_time: float = None, error_message: str = None):
        """Log a test execution."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            execution_id = f"exec_{platform}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{test_name}"
            cursor.execute(
                """
                INSERT INTO execution_tracker 
                (execution_id, test_name, status, platform, execution_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (execution_id, test_name, status, platform, execution_time, error_message)
            )
            conn.commit()
        finally:
            conn.close()
    
    def get_recent_executions(self, limit: int = 100, platform: str = None) -> List[Dict[str, Any]]:
        """Get recent test executions."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if platform:
                cursor.execute(
                    """
                    SELECT execution_id, test_name, status, platform, execution_time, 
                           error_message, created_at
                    FROM execution_tracker 
                    WHERE platform = ?
                    ORDER BY created_at DESC 
                    LIMIT ?
                    """,
                    (platform, limit)
                )
            else:
                cursor.execute(
                    """
                    SELECT execution_id, test_name, status, platform, execution_time, 
                           error_message, created_at
                    FROM execution_tracker 
                    ORDER BY created_at DESC 
                    LIMIT ?
                    """,
                    (limit,)
                )
            return [
                {
                    'id': row[0],
                    'test_name': row[1],
                    'status': row[2],
                    'platform': row[3],
                    'execution_time': row[4],
                    'error_message': row[5],
                    'created_at': row[6]
                }
                for row in cursor.fetchall()
            ]
        finally:
            conn.close()


# Singleton instance for global access
_consolidated_db = None

def get_consolidated_db() -> ConsolidatedDBAdapter:
    """Get the singleton consolidated database adapter instance."""
    global _consolidated_db
    if _consolidated_db is None:
        _consolidated_db = ConsolidatedDBAdapter()
    return _consolidated_db