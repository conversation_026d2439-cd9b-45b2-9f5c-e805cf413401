"""
Enhanced Test Case Processor with AI Healing Integration

This module provides enhanced test case processing capabilities that integrate
AI-powered element recovery into the existing JSON test case execution flow.
"""

import json
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

# Import AI healing components
try:
    from utils.ai_integration import AIHealingOrchestrator, HealingResult
    AI_HEALING_AVAILABLE = True
except ImportError:
    AI_HEALING_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class TestStepResult:
    """Result of a single test step execution"""
    step_index: int
    action: Dict[str, Any]
    success: bool
    duration: float
    error_message: Optional[str] = None
    ai_healing_used: bool = False
    healing_session_id: Optional[str] = None
    healing_attempts: int = 0
    original_error: Optional[str] = None


@dataclass
class TestCaseResult:
    """Result of a complete test case execution"""
    test_case_name: str
    total_steps: int
    successful_steps: int
    failed_steps: int
    total_duration: float
    step_results: List[TestStepResult]
    ai_healing_sessions: int = 0
    successful_healings: int = 0
    overall_success: bool = False


class EnhancedTestProcessor:
    """Enhanced test case processor with AI healing integration"""
    
    def __init__(self, action_executor, platform: str = "Unknown"):
        """
        Initialize enhanced test processor
        
        Args:
            action_executor: The action executor instance (iOS or Android)
            platform: Platform identifier ("iOS" or "Android")
        """
        self.action_executor = action_executor
        self.platform = platform
        self.logger = logging.getLogger(__name__)
        
        # Initialize AI healing if available
        self.ai_healing_available = (
            AI_HEALING_AVAILABLE and 
            hasattr(action_executor, 'ai_healing_orchestrator') and
            action_executor.ai_healing_orchestrator is not None
        )
        
        if self.ai_healing_available:
            self.logger.info(f"✅ Enhanced test processor initialized with AI healing for {platform}")
        else:
            self.logger.info(f"ℹ️ Enhanced test processor initialized without AI healing for {platform}")
    
    def execute_test_case(self, test_case_data: Dict[str, Any], 
                         context: Optional[Dict[str, Any]] = None) -> TestCaseResult:
        """
        Execute a complete test case with AI healing support
        
        Args:
            test_case_data: JSON test case data
            context: Optional execution context
            
        Returns:
            TestCaseResult with detailed execution information
        """
        start_time = time.time()
        test_case_name = test_case_data.get('name', 'Unknown Test Case')
        actions = test_case_data.get('actions', [])
        
        self.logger.info(f"🚀 Starting enhanced test case execution: {test_case_name}")
        self.logger.info(f"📋 Total actions to execute: {len(actions)}")
        
        step_results = []
        successful_steps = 0
        failed_steps = 0
        ai_healing_sessions = 0
        successful_healings = 0
        
        # Execute each action with enhanced error handling
        for step_index, action in enumerate(actions):
            self.logger.info(f"📍 Executing step {step_index + 1}/{len(actions)}: {action.get('type', 'unknown')}")
            
            step_result = self._execute_test_step(
                step_index, action, test_case_data, context
            )
            
            step_results.append(step_result)
            
            if step_result.success:
                successful_steps += 1
                self.logger.info(f"✅ Step {step_index + 1} completed successfully")
            else:
                failed_steps += 1
                self.logger.error(f"❌ Step {step_index + 1} failed: {step_result.error_message}")
            
            # Track AI healing usage
            if step_result.ai_healing_used:
                ai_healing_sessions += 1
                if step_result.success:
                    successful_healings += 1
                    self.logger.info(f"🩺 AI healing successful for step {step_index + 1}")
                else:
                    self.logger.warning(f"🩺 AI healing failed for step {step_index + 1}")
            
            # Add step delay if specified
            step_delay = action.get('interval', 0)
            if step_delay > 0:
                time.sleep(step_delay)
        
        total_duration = time.time() - start_time
        overall_success = failed_steps == 0
        
        result = TestCaseResult(
            test_case_name=test_case_name,
            total_steps=len(actions),
            successful_steps=successful_steps,
            failed_steps=failed_steps,
            total_duration=total_duration,
            step_results=step_results,
            ai_healing_sessions=ai_healing_sessions,
            successful_healings=successful_healings,
            overall_success=overall_success
        )
        
        self._log_test_case_summary(result)
        return result
    
    def _execute_test_step(self, step_index: int, action: Dict[str, Any], 
                          test_case_data: Dict[str, Any], 
                          context: Optional[Dict[str, Any]]) -> TestStepResult:
        """Execute a single test step with enhanced error handling"""
        step_start_time = time.time()
        
        try:
            # Add context information to action for AI analysis
            enhanced_action = self._enhance_action_with_context(
                action, step_index, test_case_data, context
            )
            
            # Execute the action using the action executor
            execution_result = self.action_executor.execute_action(enhanced_action, step_index)
            
            step_duration = time.time() - step_start_time
            
            # Create step result from execution result
            step_result = TestStepResult(
                step_index=step_index,
                action=action,
                success=execution_result.get('success', False),
                duration=step_duration,
                error_message=execution_result.get('error'),
                ai_healing_used=execution_result.get('ai_healing_used', False),
                healing_session_id=execution_result.get('healing_session_id'),
                healing_attempts=execution_result.get('healing_attempts', 0),
                original_error=execution_result.get('original_error')
            )
            
            return step_result
            
        except Exception as e:
            step_duration = time.time() - step_start_time
            self.logger.error(f"Unexpected error in step {step_index}: {e}")
            
            return TestStepResult(
                step_index=step_index,
                action=action,
                success=False,
                duration=step_duration,
                error_message=f"Unexpected error: {str(e)}",
                ai_healing_used=False
            )
    
    def _enhance_action_with_context(self, action: Dict[str, Any], step_index: int,
                                   test_case_data: Dict[str, Any], 
                                   context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Enhance action with additional context for AI analysis"""
        enhanced_action = action.copy()
        
        # Add test case context
        enhanced_action['_test_context'] = {
            'test_case_name': test_case_data.get('name', 'Unknown'),
            'step_index': step_index,
            'total_steps': len(test_case_data.get('actions', [])),
            'platform': self.platform,
            'device_id': test_case_data.get('device_id'),
            'previous_steps': self._get_previous_steps_summary(test_case_data, step_index)
        }
        
        # Add execution context if provided
        if context:
            enhanced_action['_execution_context'] = context
        
        return enhanced_action
    
    def _get_previous_steps_summary(self, test_case_data: Dict[str, Any], 
                                   current_step: int) -> List[Dict[str, Any]]:
        """Get summary of previous steps for context"""
        actions = test_case_data.get('actions', [])
        previous_steps = []
        
        # Include up to 3 previous steps for context
        start_index = max(0, current_step - 3)
        for i in range(start_index, current_step):
            if i < len(actions):
                step_summary = {
                    'step_index': i,
                    'type': actions[i].get('type', 'unknown'),
                    'description': self._get_action_description(actions[i])
                }
                previous_steps.append(step_summary)
        
        return previous_steps
    
    def _get_action_description(self, action: Dict[str, Any]) -> str:
        """Generate human-readable description of action"""
        action_type = action.get('type', 'unknown')
        
        if action_type in ['click', 'tap']:
            locator = action.get('locator_value', action.get('text_to_find', 'unknown element'))
            return f"Click on: {locator}"
        elif action_type == 'type':
            text = action.get('text', 'unknown text')
            return f"Type: {text}"
        elif action_type == 'swipe':
            direction = action.get('direction', 'unknown direction')
            return f"Swipe {direction}"
        elif action_type == 'wait':
            timeout = action.get('timeout', 'unknown time')
            return f"Wait for {timeout} seconds"
        else:
            return f"Execute {action_type}"
    
    def _log_test_case_summary(self, result: TestCaseResult):
        """Log comprehensive test case execution summary"""
        self.logger.info("=" * 60)
        self.logger.info(f"📊 TEST CASE EXECUTION SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Test Case: {result.test_case_name}")
        self.logger.info(f"Platform: {self.platform}")
        self.logger.info(f"Overall Success: {'✅ PASSED' if result.overall_success else '❌ FAILED'}")
        self.logger.info(f"Total Duration: {result.total_duration:.2f} seconds")
        self.logger.info(f"Steps: {result.successful_steps}/{result.total_steps} successful")
        
        if result.ai_healing_sessions > 0:
            self.logger.info(f"🩺 AI Healing Sessions: {result.ai_healing_sessions}")
            self.logger.info(f"🩺 Successful Healings: {result.successful_healings}")
            healing_success_rate = (result.successful_healings / result.ai_healing_sessions) * 100
            self.logger.info(f"🩺 Healing Success Rate: {healing_success_rate:.1f}%")
        
        # Log failed steps
        if result.failed_steps > 0:
            self.logger.info("❌ Failed Steps:")
            for step_result in result.step_results:
                if not step_result.success:
                    self.logger.info(f"  Step {step_result.step_index + 1}: {step_result.error_message}")
        
        self.logger.info("=" * 60)
    
    def get_ai_healing_statistics(self) -> Dict[str, Any]:
        """Get AI healing statistics"""
        if not self.ai_healing_available:
            return {'available': False, 'message': 'AI healing not available'}
        
        try:
            stats = self.action_executor.ai_healing_orchestrator.get_healing_statistics()
            stats['available'] = True
            stats['platform'] = self.platform
            return stats
        except Exception as e:
            return {'available': False, 'error': str(e)}


def create_enhanced_processor(action_executor, platform: str) -> EnhancedTestProcessor:
    """Factory function to create enhanced test processor"""
    return EnhancedTestProcessor(action_executor, platform)
