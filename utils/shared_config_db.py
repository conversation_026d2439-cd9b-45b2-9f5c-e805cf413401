#!/usr/bin/env python3
"""
Shared Configuration Database Manager

This module manages the shared configuration database that contains:
- Global values
- Directory paths
- Environment configurations
- Environment variables
- Active environment settings

This data is shared across all port instances of the application.
"""

import sqlite3
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class SharedConfigDB:
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the shared configuration database manager.
        
        Args:
            db_path: Optional custom path to the database file.
                    If None, uses default location: data/shared_config.db
        """
        if db_path:
            self.db_path = Path(db_path)
        else:
            # Default location in data directory
            project_root = Path(__file__).resolve().parent.parent
            self.db_path = project_root / 'data' / 'shared_config.db'
        
        # Ensure the data directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize the database
        self._initialize_database()
        
        logger.info(f"Using shared configuration database at: {self.db_path}")
    
    def _initialize_database(self):
        """Initialize the database with required tables."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # Create global_values table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS global_values (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create directory_paths table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS directory_paths (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    path_type TEXT NOT NULL,
                    path_value TEXT NOT NULL,
                    is_default BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create environments table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS environments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create environment_variables table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS environment_variables (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    environment_id INTEGER,
                    platform TEXT,
                    key TEXT NOT NULL,
                    value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (environment_id) REFERENCES environments(id),
                    UNIQUE(environment_id, platform, key)
                )
            ''')
            
            # Create active_environment table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS active_environment (
                    id INTEGER PRIMARY KEY,
                    environment_id INTEGER,
                    platform TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (environment_id) REFERENCES environments(id),
                    UNIQUE(platform)
                )
            ''')
            
            conn.commit()
            logger.info("Shared configuration database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing shared configuration database: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    # Global Values Methods
    def set_global_value(self, key: str, value: str, description: Optional[str] = None) -> bool:
        """Set a global configuration value."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO global_values (key, value, description, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, value, description))
            conn.commit()
            logger.info(f"Set global value: {key} = {value}")
            return True
        except Exception as e:
            logger.error(f"Error setting global value {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_global_value(self, key: str) -> Optional[str]:
        """Get a global configuration value."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT value FROM global_values WHERE key = ?', (key,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting global value {key}: {e}")
            return None
        finally:
            conn.close()
    
    def get_all_global_values(self) -> Dict[str, str]:
        """Get all global configuration values."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT key, value FROM global_values')
            results = cursor.fetchall()
            return {key: value for key, value in results}
        except Exception as e:
            logger.error(f"Error getting all global values: {e}")
            return {}
        finally:
            conn.close()
    
    def delete_global_value(self, key: str) -> bool:
        """Delete a global configuration value."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM global_values WHERE key = ?', (key,))
            conn.commit()
            logger.info(f"Deleted global value: {key}")
            return True
        except Exception as e:
            logger.error(f"Error deleting global value {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    # Directory Paths Methods
    def set_directory_path(self, path_type: str, path_value: str, is_default: bool = False) -> bool:
        """Set a directory path configuration."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # If setting as default, unset other defaults for this path_type
            if is_default:
                cursor.execute('''
                    UPDATE directory_paths SET is_default = 0 
                    WHERE path_type = ? AND is_default = 1
                ''', (path_type,))
            
            cursor.execute('''
                INSERT OR REPLACE INTO directory_paths (path_type, path_value, is_default, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (path_type, path_value, is_default))
            conn.commit()
            logger.info(f"Set directory path: {path_type} = {path_value} (default: {is_default})")
            return True
        except Exception as e:
            logger.error(f"Error setting directory path {path_type}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_directory_path(self, path_type: str, use_default: bool = True) -> Optional[str]:
        """Get a directory path configuration."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            if use_default:
                cursor.execute('''
                    SELECT path_value FROM directory_paths 
                    WHERE path_type = ? AND is_default = 1
                ''', (path_type,))
            else:
                cursor.execute('''
                    SELECT path_value FROM directory_paths 
                    WHERE path_type = ? 
                    ORDER BY updated_at DESC LIMIT 1
                ''', (path_type,))
            
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting directory path {path_type}: {e}")
            return None
        finally:
            conn.close()
    
    def get_all_directory_paths(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all directory path configurations grouped by path_type."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT path_type, path_value, is_default, created_at, updated_at 
                FROM directory_paths ORDER BY path_type, updated_at DESC
            ''')
            results = cursor.fetchall()
            
            paths = {}
            for path_type, path_value, is_default, created_at, updated_at in results:
                if path_type not in paths:
                    paths[path_type] = []
                paths[path_type].append({
                    'path_value': path_value,
                    'is_default': bool(is_default),
                    'created_at': created_at,
                    'updated_at': updated_at
                })
            
            return paths
        except Exception as e:
            logger.error(f"Error getting all directory paths: {e}")
            return {}
        finally:
            conn.close()
    
    # Environment Methods
    def create_environment(self, name: str, description: Optional[str] = None) -> Optional[int]:
        """Create a new environment."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO environments (name, description)
                VALUES (?, ?)
            ''', (name, description))
            conn.commit()
            env_id = cursor.lastrowid
            logger.info(f"Created environment: {name} (ID: {env_id})")
            return env_id
        except Exception as e:
            logger.error(f"Error creating environment {name}: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()
    
    def get_environment(self, name: str) -> Optional[Dict[str, Any]]:
        """Get environment by name."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, description, is_active, created_at, updated_at
                FROM environments WHERE name = ?
            ''', (name,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'name': result[1],
                    'description': result[2],
                    'is_active': bool(result[3]),
                    'created_at': result[4],
                    'updated_at': result[5]
                }
            return None
        except Exception as e:
            logger.error(f"Error getting environment {name}: {e}")
            return None
        finally:
            conn.close()
    
    def get_all_environments(self) -> List[Dict[str, Any]]:
        """Get all environments."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, description, is_active, created_at, updated_at
                FROM environments ORDER BY name
            ''')
            results = cursor.fetchall()
            
            environments = []
            for result in results:
                environments.append({
                    'id': result[0],
                    'name': result[1],
                    'description': result[2],
                    'is_active': bool(result[3]),
                    'created_at': result[4],
                    'updated_at': result[5]
                })
            
            return environments
        except Exception as e:
            logger.error(f"Error getting all environments: {e}")
            return []
        finally:
            conn.close()
    
    def set_active_environment(self, environment_name: str, platform: str) -> bool:
        """Set the active environment for a platform."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # Get environment ID
            cursor.execute('SELECT id FROM environments WHERE name = ?', (environment_name,))
            env_result = cursor.fetchone()
            if not env_result:
                logger.error(f"Environment {environment_name} not found")
                return False
            
            env_id = env_result[0]
            
            # Set as active environment for the platform
            cursor.execute('''
                INSERT OR REPLACE INTO active_environment (environment_id, platform, updated_at)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (env_id, platform))
            
            conn.commit()
            logger.info(f"Set active environment for {platform}: {environment_name}")
            return True
        except Exception as e:
            logger.error(f"Error setting active environment {environment_name} for {platform}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_active_environment(self, platform: str) -> Optional[Dict[str, Any]]:
        """Get the active environment for a platform."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT e.id, e.name, e.description, e.is_active, e.created_at, e.updated_at
                FROM environments e
                JOIN active_environment ae ON e.id = ae.environment_id
                WHERE ae.platform = ?
            ''', (platform,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'name': result[1],
                    'description': result[2],
                    'is_active': bool(result[3]),
                    'created_at': result[4],
                    'updated_at': result[5]
                }
            return None
        except Exception as e:
            logger.error(f"Error getting active environment for {platform}: {e}")
            return None
        finally:
            conn.close()
    
    # Environment Variables Methods
    def set_environment_variable(self, environment_name: str, platform: str, key: str, value: str) -> bool:
        """Set an environment variable."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # Get environment ID
            cursor.execute('SELECT id FROM environments WHERE name = ?', (environment_name,))
            env_result = cursor.fetchone()
            if not env_result:
                logger.error(f"Environment {environment_name} not found")
                return False
            
            env_id = env_result[0]
            
            cursor.execute('''
                INSERT OR REPLACE INTO environment_variables 
                (environment_id, platform, key, value, updated_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (env_id, platform, key, value))
            
            conn.commit()
            logger.info(f"Set environment variable: {environment_name}.{platform}.{key} = {value}")
            return True
        except Exception as e:
            logger.error(f"Error setting environment variable {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_environment_variables(self, environment_name: str, platform: str) -> Dict[str, str]:
        """Get all environment variables for an environment and platform."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT ev.key, ev.value
                FROM environment_variables ev
                JOIN environments e ON ev.environment_id = e.id
                WHERE e.name = ? AND ev.platform = ?
            ''', (environment_name, platform))
            results = cursor.fetchall()
            return {key: value for key, value in results}
        except Exception as e:
            logger.error(f"Error getting environment variables for {environment_name}.{platform}: {e}")
            return {}
        finally:
            conn.close()
    
    def delete_environment_variable(self, environment_name: str, platform: str, key: str) -> bool:
        """Delete an environment variable."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM environment_variables 
                WHERE environment_id = (SELECT id FROM environments WHERE name = ?)
                AND platform = ? AND key = ?
            ''', (environment_name, platform, key))
            conn.commit()
            logger.info(f"Deleted environment variable: {environment_name}.{platform}.{key}")
            return True
        except Exception as e:
            logger.error(f"Error deleting environment variable {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()


# Singleton instance for global access
_shared_config_db = None

def get_shared_config_db() -> SharedConfigDB:
    """Get the singleton instance of SharedConfigDB."""
    global _shared_config_db
    if _shared_config_db is None:
        _shared_config_db = SharedConfigDB()
    return _shared_config_db