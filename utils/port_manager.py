#!/usr/bin/env python3
"""
Robust Port Management System for Mobile Automation Framework

This module provides comprehensive port management capabilities for concurrent
iOS and Android automation, including port allocation, conflict detection,
and intelligent cleanup mechanisms.
"""

import os
import sys
import time
import socket
import subprocess
import logging
import json
from typing import Dict, List, Optional, Tuple, Set
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class PortAllocation:
    """Represents a port allocation for a specific service"""
    port: int
    service_type: str  # 'flask', 'appium', 'wda'
    platform: str      # 'ios', 'android'
    process_id: Optional[int] = None
    allocated_at: Optional[str] = None
    last_checked: Optional[str] = None
    status: str = 'allocated'  # 'allocated', 'active', 'stale', 'free'

@dataclass
class ServiceConfig:
    """Configuration for a platform service"""
    platform: str
    flask_port: int
    appium_port: int
    wda_port: int

class PortManager:
    """
    Comprehensive port management system for mobile automation

    Features:
    - Intelligent port allocation and conflict detection
    - Platform-aware port management (iOS vs Android)
    - Process tracking and cleanup
    - Concurrent execution support
    - Stale process detection and cleanup
    """

    # Default port configurations
    DEFAULT_CONFIGS = {
        # Use non-overlapping defaults away from the auth server (which uses ~8081-8084)
        'ios': ServiceConfig('ios', 8090, 4723, 8200),
        'android': ServiceConfig('android', 8091, 4724, 8300)
    }

    # Port ranges for dynamic allocation (avoid 8080-8089 to reduce conflicts)
    PORT_RANGES = {
        'flask': (8090, 8120),
        'appium': (4720, 4760),
        'wda': (8200, 8310)
    }

    def __init__(self, state_file: Optional[str] = None):
        """
        Initialize the port manager

        Args:
            state_file: Path to the state file for persistent port tracking
        """
        self.state_file = state_file or os.path.join(
            os.path.dirname(__file__), '..', 'data', 'port_state.json'
        )
        self.allocations: Dict[int, PortAllocation] = {}
        self.load_state()

    def load_state(self) -> None:
        """Load port allocation state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    data = json.load(f)
                    self.allocations = {
                        int(port): PortAllocation(**allocation)
                        for port, allocation in data.items()
                    }
                logger.info(f"Loaded {len(self.allocations)} port allocations from state file")
            else:
                logger.info("No existing state file found, starting with clean state")
        except Exception as e:
            logger.warning(f"Failed to load port state: {e}")
            self.allocations = {}

    def save_state(self) -> None:
        """Save port allocation state to file"""
        try:
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            with open(self.state_file, 'w') as f:
                data = {
                    str(port): asdict(allocation)
                    for port, allocation in self.allocations.items()
                }
                json.dump(data, f, indent=2)
            logger.debug(f"Saved port state to {self.state_file}")
        except Exception as e:
            logger.warning(f"Failed to save port state: {e}")

    def is_port_available(self, port: int) -> bool:
        """Check if a port is available for use"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return True
        except OSError:
            return False

    def get_process_using_port(self, port: int) -> Optional[int]:
        """Get the process ID using a specific port"""
        try:
            if sys.platform != 'win32':
                result = subprocess.run(
                    ['lsof', '-ti', f':{port}'],
                    capture_output=True, text=True, check=False
                )
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    return int(pids[0]) if pids[0] else None
            else:
                # Windows implementation
                result = subprocess.run(
                    ['netstat', '-ano'],
                    capture_output=True, text=True, check=False
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) > 4:
                                return int(parts[-1])
        except (ValueError, subprocess.SubprocessError):
            pass
        return None

    def kill_process_on_port(self, port: int, force: bool = False) -> bool:
        """
        Kill process using a specific port

        Args:
            port: Port number
            force: If True, force kill immediately. If False, try graceful termination first

        Returns:
            True if successful, False otherwise
        """
        pid = self.get_process_using_port(port)
        if not pid:
            return True

        try:
            if sys.platform != 'win32':
                if not force:
                    # Try graceful termination first
                    subprocess.run(['kill', '-TERM', str(pid)], check=False)
                    time.sleep(2)

                    # Check if process is still running
                    if self.get_process_using_port(port):
                        subprocess.run(['kill', '-9', str(pid)], check=False)
                else:
                    subprocess.run(['kill', '-9', str(pid)], check=False)
            else:
                subprocess.run(['taskkill', '/F', '/PID', str(pid)], check=False)

            # Wait and verify
            time.sleep(1)
            return self.get_process_using_port(port) is None

        except subprocess.SubprocessError as e:
            logger.warning(f"Failed to kill process {pid} on port {port}: {e}")
            return False

    def allocate_service_ports(self, platform: str, custom_ports: Optional[Dict[str, int]] = None) -> ServiceConfig:
        """
        Allocate ports for a platform service with dynamic availability checks.

        Logic:
        - Start from preferred defaults or provided custom ports
        - If a preferred port is unavailable, find the next available port within the service range
        - Record the final chosen ports in the allocation state
        """
        # Preferred starting points
        preferred = self.DEFAULT_CONFIGS[platform]
        start_flask = (custom_ports or {}).get('flask', preferred.flask_port)
        start_appium = (custom_ports or {}).get('appium', preferred.appium_port)
        start_wda = (custom_ports or {}).get('wda', preferred.wda_port)

        # Resolve to available ports
        flask_port = self.find_available_port('flask', start_flask) or start_flask
        appium_port = self.find_available_port('appium', start_appium) or start_appium
        wda_port = self.find_available_port('wda', start_wda) or start_wda

        config = ServiceConfig(
            platform=platform,
            flask_port=flask_port,
            appium_port=appium_port,
            wda_port=wda_port
        )

        # Register allocations
        now = datetime.now().isoformat()
        # Remove any stale allocations for this platform to avoid duplicates on port change
        for port, alloc in list(self.allocations.items()):
            if alloc.platform == platform and alloc.status in ('allocated','stale','free'):
                # Only clear non-active ones
                self.allocations.pop(port, None)
        for service_type, port in [
            ('flask', config.flask_port),
            ('appium', config.appium_port),
            ('wda', config.wda_port)
        ]:
            self.allocations[port] = PortAllocation(
                port=port,
                service_type=service_type,
                platform=platform,
                allocated_at=now,
                last_checked=now,
                status='allocated'
            )

        self.save_state()
        return config

    def find_available_port(self, service_type: str, start_port: Optional[int] = None) -> Optional[int]:
        """Find an available port for a service type"""
        if start_port:
            if self.is_port_available(start_port):
                return start_port

        start, end = self.PORT_RANGES.get(service_type, (8000, 9000))
        for port in range(start, end):
            if self.is_port_available(port):
                return port
        return None

    def cleanup_stale_allocations(self, max_age_hours: int = 24) -> int:
        """
        Clean up stale port allocations

        Args:
            max_age_hours: Maximum age in hours before considering allocation stale

        Returns:
            Number of allocations cleaned up
        """
        cleaned = 0
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        for port, allocation in list(self.allocations.items()):
            try:
                allocated_time = datetime.fromisoformat(allocation.allocated_at)
                if allocated_time < cutoff_time:
                    # Check if process is still running
                    if not self.get_process_using_port(port):
                        del self.allocations[port]
                        cleaned += 1
                        logger.info(f"Cleaned up stale allocation for port {port}")
            except (ValueError, TypeError):
                # Invalid timestamp, remove allocation
                del self.allocations[port]
                cleaned += 1

        if cleaned > 0:
            self.save_state()

        return cleaned

    def smart_port_cleanup(self, platform: str, ports: List[int], preserve_other_platforms: bool = True) -> Dict[int, bool]:
        """
        Intelligently clean up ports based on platform and usage

        Args:
            platform: Platform requesting cleanup ('ios' or 'android')
            ports: List of ports to clean up
            preserve_other_platforms: If True, preserve processes from other platforms

        Returns:
            Dict mapping port to cleanup success status
        """
        results = {}

        for port in ports:
            allocation = self.allocations.get(port)

            # If port is allocated to a different platform and we should preserve it
            if (preserve_other_platforms and allocation and
                allocation.platform != platform and allocation.status == 'active'):
                logger.info(f"Preserving port {port} allocated to {allocation.platform} platform")
                results[port] = False
                continue

            # Check if process is actually running
            pid = self.get_process_using_port(port)
            if pid:
                logger.info(f"Cleaning up process {pid} on port {port} for {platform} platform")
                results[port] = self.kill_process_on_port(port, force=False)

                # Update allocation status
                if allocation:
                    allocation.status = 'free' if results[port] else 'stale'
                    allocation.last_checked = datetime.now().isoformat()
            else:
                logger.info(f"Port {port} is already free")
                results[port] = True

                # Update allocation status
                if allocation:
                    allocation.status = 'free'
                    allocation.last_checked = datetime.now().isoformat()

        self.save_state()
        return results
    def cleanup_stale_processes(self, max_age_hours: int = 24) -> int:
        """Compatibility helper to clean up stale allocations/process bookkeeping.

        This method is called by web routes before launching platform apps.
        It ensures older allocations are pruned and any allocations with no
        backing process are marked free. It does not aggressively kill running
        processes; for that, use smart_port_cleanup or release_platform_ports.

        Returns the total number of entries updated/removed.
        """
        updated = 0
        try:
            # Remove old allocations
            updated += self.cleanup_stale_allocations(max_age_hours=max_age_hours)
            now = datetime.now().isoformat()
            # Mark allocations without a running process as free
            for port, allocation in list(self.allocations.items()):
                pid = self.get_process_using_port(port)
                if not pid and allocation.status != 'free':
                    allocation.status = 'free'
                    allocation.last_checked = now
                    updated += 1
            if updated:
                self.save_state()
        except Exception as e:
            logger.warning(f"cleanup_stale_processes encountered an issue: {e}")
        return updated


    def get_platform_ports(self, platform: str) -> List[int]:
        """Get all ports allocated to a specific platform"""
        return [
            port for port, allocation in self.allocations.items()
            if allocation.platform == platform
        ]

    def get_port_status(self, port: int) -> Dict:
        """Get detailed status information for a port"""
        allocation = self.allocations.get(port)
        pid = self.get_process_using_port(port)
        available = self.is_port_available(port)

        return {
            'port': port,
            'available': available,
            'process_id': pid,
            'allocation': asdict(allocation) if allocation else None,
            'status': 'free' if available else 'occupied'
        }

    def release_platform_ports(self, platform: str, force: bool = False) -> int:
        """
        Release all ports allocated to a specific platform

        Args:
            platform: Platform to release ports for
            force: If True, force kill processes

        Returns:
            Number of ports successfully released
        """
        platform_ports = self.get_platform_ports(platform)
        released = 0

        for port in platform_ports:
            if self.kill_process_on_port(port, force=force):
                allocation = self.allocations.get(port)
                if allocation:
                    allocation.status = 'free'
                    allocation.last_checked = datetime.now().isoformat()
                released += 1
                logger.info(f"Released port {port} for {platform} platform")

        self.save_state()
        return released

    def ensure_ports_available(self, ports: List[int], platform: str) -> bool:
        """
        Ensure specified ports are available for use by a platform

        Args:
            ports: List of ports to ensure availability
            platform: Platform requesting the ports

        Returns:
            True if all ports are available, False otherwise
        """
        unavailable_ports = []

        for port in ports:
            if not self.is_port_available(port):
                allocation = self.allocations.get(port)

                # If port is allocated to same platform, try to clean it up
                if allocation and allocation.platform == platform:
                    if not self.kill_process_on_port(port):
                        unavailable_ports.append(port)
                # If port is allocated to different platform, don't clean it up
                elif allocation and allocation.platform != platform:
                    logger.warning(f"Port {port} is allocated to {allocation.platform}, cannot clean up for {platform}")
                    unavailable_ports.append(port)
                # If port is not tracked but occupied, try to clean it up
                else:
                    if not self.kill_process_on_port(port):
                        unavailable_ports.append(port)

        if unavailable_ports:
            logger.error(f"Could not free ports {unavailable_ports} for {platform}")
            return False

        return True

    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        status = {
            'total_allocations': len(self.allocations),
            'platforms': {},
            'port_ranges': self.PORT_RANGES,
            'default_configs': {k: asdict(v) for k, v in self.DEFAULT_CONFIGS.items()}
        }

        # Group by platform
        for platform in ['ios', 'android']:
            platform_ports = self.get_platform_ports(platform)
            active_ports = []
            free_ports = []

            for port in platform_ports:
                port_status = self.get_port_status(port)
                if port_status['available']:
                    free_ports.append(port)
                else:
                    active_ports.append(port)

            status['platforms'][platform] = {
                'total_ports': len(platform_ports),
                'active_ports': active_ports,
                'free_ports': free_ports,
                'all_ports': platform_ports
            }

        return status


# Global port manager instance
_port_manager = None

def get_port_manager() -> PortManager:
    """Get the global port manager instance"""
    global _port_manager
    if _port_manager is None:
        _port_manager = PortManager()
    return _port_manager

def cleanup_platform_ports(platform: str, force: bool = False) -> int:
    """Convenience function to clean up all ports for a platform"""
    return get_port_manager().release_platform_ports(platform, force=force)

def ensure_platform_ports(platform: str, custom_ports: Optional[Dict[str, int]] = None) -> ServiceConfig:
    """Convenience function to ensure platform ports are available"""
    pm = get_port_manager()
    config = pm.allocate_service_ports(platform, custom_ports)

    ports = [config.flask_port, config.appium_port, config.wda_port]
    if not pm.ensure_ports_available(ports, platform):
        raise RuntimeError(f"Could not ensure ports {ports} are available for {platform}")

    return config

if __name__ == '__main__':
    # CLI interface for port management
    import argparse

    parser = argparse.ArgumentParser(description='Port Management System')
    parser.add_argument('--status', action='store_true', help='Show system status')
    parser.add_argument('--cleanup', choices=['ios', 'android', 'all'], help='Clean up platform ports')
    parser.add_argument('--force', action='store_true', help='Force cleanup')
    parser.add_argument('--port', type=int, help='Check specific port status')

    args = parser.parse_args()

    pm = get_port_manager()

    if args.status:
        import pprint
        pprint.pprint(pm.get_system_status())
    elif args.cleanup:
        if args.cleanup == 'all':
            ios_cleaned = pm.release_platform_ports('ios', force=args.force)
            android_cleaned = pm.release_platform_ports('android', force=args.force)
            print(f"Cleaned up {ios_cleaned} iOS ports and {android_cleaned} Android ports")
        else:
            cleaned = pm.release_platform_ports(args.cleanup, force=args.force)
            print(f"Cleaned up {cleaned} {args.cleanup} ports")
    elif args.port:
        import pprint
        pprint.pprint(pm.get_port_status(args.port))
    else:
        parser.print_help()
