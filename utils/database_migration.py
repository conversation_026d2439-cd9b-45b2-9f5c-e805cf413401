#!/usr/bin/env python3
"""
Database Migration Script

This script migrates data from the existing multiple database files
to the new unified database structure:
- Shared configuration data -> shared_config.db
- Port-specific application data -> app_data_port_{port}.db
"""

import sqlite3
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

# Import our new database managers
from shared_config_db import SharedConfigDB
from port_specific_db import PortSpecificDB

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseMigration:
    def __init__(self, project_root: Optional[str] = None):
        """
        Initialize the database migration.
        
        Args:
            project_root: Optional path to project root. If None, auto-detects.
        """
        if project_root:
            self.project_root = Path(project_root)
        else:
            self.project_root = Path(__file__).resolve().parent.parent
        
        self.data_dir = self.project_root / 'data'
        self.app_data_dir = self.project_root / 'app' / 'data'
        
        # Initialize new database managers
        self.shared_db = SharedConfigDB()
        
        # Track discovered ports
        self.discovered_ports = set()
        
        logger.info(f"Migration initialized for project: {self.project_root}")
        logger.info(f"Data directories: {self.data_dir}, {self.app_data_dir}")
    
    def discover_existing_databases(self) -> Dict[str, List[Path]]:
        """Discover all existing database files and categorize them."""
        db_files = {
            'global_values': [],
            'settings': [],
            'test_execution': [],
            'directory_paths': [],
            'other': []
        }
        
        # Search in both data directories
        search_dirs = [self.data_dir, self.app_data_dir]
        
        for search_dir in search_dirs:
            if not search_dir.exists():
                continue
                
            for db_file in search_dir.glob('*.db'):
                filename = db_file.name.lower()
                
                if 'global_values' in filename:
                    db_files['global_values'].append(db_file)
                    # Extract port from filename if present
                    if '_port_' in filename:
                        try:
                            port = int(filename.split('_port_')[1].split('.')[0])
                            self.discovered_ports.add(port)
                        except (ValueError, IndexError):
                            pass
                elif 'settings' in filename:
                    db_files['settings'].append(db_file)
                    # Extract port from filename if present
                    if '_port_' in filename:
                        try:
                            port = int(filename.split('_port_')[1].split('.')[0])
                            self.discovered_ports.add(port)
                        except (ValueError, IndexError):
                            pass
                elif 'test_execution' in filename:
                    db_files['test_execution'].append(db_file)
                    # Extract port from filename if present
                    if '_port_' in filename:
                        try:
                            port = int(filename.split('_port_')[1].split('.')[0])
                            self.discovered_ports.add(port)
                        except (ValueError, IndexError):
                            pass
                elif 'directory_paths' in filename:
                    db_files['directory_paths'].append(db_file)
                else:
                    db_files['other'].append(db_file)
        
        # Add default ports if none discovered
        if not self.discovered_ports:
            self.discovered_ports.update([8080, 8081])
        
        logger.info(f"Discovered database files: {dict((k, len(v)) for k, v in db_files.items())}")
        logger.info(f"Discovered ports: {sorted(self.discovered_ports)}")
        
        return db_files
    
    def migrate_global_values(self, db_files: List[Path]) -> bool:
        """Migrate global values from existing databases."""
        logger.info("Migrating global values...")
        
        migrated_count = 0
        
        for db_file in db_files:
            if not db_file.exists():
                continue
                
            try:
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                
                # Check if global_values table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='global_values'")
                if not cursor.fetchone():
                    logger.warning(f"No global_values table found in {db_file}")
                    continue
                
                # Check column structure
                cursor.execute("PRAGMA table_info(global_values)")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'key' in columns and 'value' in columns:
                    cursor.execute("SELECT key, value FROM global_values")
                elif 'name' in columns and 'value' in columns:
                    cursor.execute("SELECT name, value FROM global_values")
                else:
                    logger.warning(f"Unknown column structure in {db_file}")
                    continue
                    
                rows = cursor.fetchall()
                
                for key, value in rows:
                    if self.shared_db.set_global_value(key, value, f"Migrated from {db_file.name}"):
                        migrated_count += 1
                
                conn.close()
                logger.info(f"Migrated {len(rows)} global values from {db_file.name}")
                
            except Exception as e:
                logger.error(f"Error migrating global values from {db_file}: {e}")
        
        logger.info(f"Total global values migrated: {migrated_count}")
        return migrated_count > 0
    
    def migrate_directory_paths(self, db_files: List[Path]) -> bool:
        """Migrate directory paths from existing databases."""
        logger.info("Migrating directory paths...")
        
        migrated_count = 0
        
        for db_file in db_files:
            if not db_file.exists():
                continue
                
            try:
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                
                # Check if directory_paths table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='directory_paths'")
                if not cursor.fetchone():
                    logger.warning(f"No directory_paths table found in {db_file}")
                    continue
                
                # Check column structure
                cursor.execute("PRAGMA table_info(directory_paths)")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'path_type' in columns and 'path_value' in columns:
                    cursor.execute("SELECT path_type, path_value FROM directory_paths")
                elif 'path_key' in columns and 'path_value' in columns:
                    cursor.execute("SELECT path_key, path_value FROM directory_paths")
                elif 'key' in columns and 'value' in columns:
                    cursor.execute("SELECT key, value FROM directory_paths")
                elif 'name' in columns and 'path' in columns:
                    cursor.execute("SELECT name, path FROM directory_paths")
                else:
                    logger.warning(f"Unknown column structure in directory_paths table of {db_file}")
                    continue
                    
                rows = cursor.fetchall()
                
                for path_type, path_value in rows:
                    if self.shared_db.set_directory_path(path_type, path_value, is_default=True):
                        migrated_count += 1
                
                conn.close()
                logger.info(f"Migrated {len(rows)} directory paths from {db_file.name}")
                
            except Exception as e:
                logger.error(f"Error migrating directory paths from {db_file}: {e}")
        
        logger.info(f"Total directory paths migrated: {migrated_count}")
        return migrated_count > 0
    
    def migrate_environments_and_variables(self, db_files: List[Path]) -> bool:
        """Migrate environments and environment variables from settings databases."""
        logger.info("Migrating environments and environment variables...")
        
        migrated_envs = 0
        migrated_vars = 0
        
        for db_file in db_files:
            if not db_file.exists():
                continue
                
            try:
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                
                # Determine platform from filename
                platform = 'ios' if 'ios' in db_file.name.lower() else 'android'
                
                # Migrate environments (check both 'environments' and 'env_environments' tables)
                for env_table in ['environments', 'env_environments']:
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{env_table}'")
                    if cursor.fetchone():
                        cursor.execute(f"PRAGMA table_info({env_table})")
                        columns = [row[1] for row in cursor.fetchall()]
                        
                        if 'name' in columns:
                            if 'description' in columns:
                                cursor.execute(f"SELECT name, description FROM {env_table}")
                                env_rows = cursor.fetchall()
                                for name, description in env_rows:
                                    env_id = self.shared_db.create_environment(name, description)
                                    if env_id:
                                        migrated_envs += 1
                            else:
                                cursor.execute(f"SELECT name FROM {env_table}")
                                env_rows = cursor.fetchall()
                                for name, in env_rows:
                                    env_id = self.shared_db.create_environment(name, None)
                                    if env_id:
                                        migrated_envs += 1
                        break  # Only process one table type
                
                # Migrate environment variables (check multiple table names)
                for var_table in ['ios_environment_variables', 'environment_variables', 'env_variables']:
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{var_table}'")
                    if cursor.fetchone():
                        cursor.execute(f"PRAGMA table_info({var_table})")
                        columns = [row[1] for row in cursor.fetchall()]
                        
                        if var_table == 'ios_environment_variables' and 'environment_name' in columns:
                            cursor.execute("SELECT environment_name, key, value FROM ios_environment_variables")
                            var_rows = cursor.fetchall()
                            for env_name, key, value in var_rows:
                                if self.shared_db.set_environment_variable(env_name, platform, key, value):
                                    migrated_vars += 1
                        elif 'environment_id' in columns and 'key' in columns and 'value' in columns:
                            cursor.execute(f"SELECT environment_id, key, value FROM {var_table}")
                            var_rows = cursor.fetchall()
                            
                            # Get environment names
                            cursor.execute("SELECT id, name FROM environments")
                            env_map = dict(cursor.fetchall())
                            
                            for env_id, key, value in var_rows:
                                env_name = env_map.get(env_id, 'default')
                                if self.shared_db.set_environment_variable(env_name, platform, key, value):
                                    migrated_vars += 1
                        elif 'env_id' in columns and 'var_name' in columns and 'var_value' in columns:
                            cursor.execute(f"SELECT env_id, var_name, var_value FROM {var_table}")
                            var_rows = cursor.fetchall()
                            for env_id, var_name, var_value in var_rows:
                                if self.shared_db.set_environment_variable(str(env_id), platform, var_name, var_value):
                                    migrated_vars += 1
                        break  # Only process one table type
                
                # Migrate active environment
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='active_environment'")
                if cursor.fetchone():
                    cursor.execute("SELECT environment_id FROM active_environment LIMIT 1")
                    active_result = cursor.fetchone()
                    if active_result:
                        cursor.execute("SELECT name FROM environments WHERE id = ?", (active_result[0],))
                        env_result = cursor.fetchone()
                        if env_result:
                            self.shared_db.set_active_environment(env_result[0], platform)
                
                conn.close()
                logger.info(f"Migrated environments and variables from {db_file.name}")
                
            except Exception as e:
                logger.error(f"Error migrating environments from {db_file}: {e}")
        
        logger.info(f"Total environments migrated: {migrated_envs}")
        logger.info(f"Total environment variables migrated: {migrated_vars}")
        return migrated_envs > 0 or migrated_vars > 0
    
    def migrate_test_execution_data(self, db_files: List[Path]) -> bool:
        """Migrate test execution data to port-specific databases."""
        logger.info("Migrating test execution data...")
        
        migrated_suites = 0
        migrated_cases = 0
        
        for db_file in db_files:
            if not db_file.exists():
                continue
                
            # Determine port from filename
            port = 8080  # default
            if '_port_' in db_file.name:
                try:
                    port = int(db_file.name.split('_port_')[1].split('.')[0])
                except (ValueError, IndexError):
                    pass
            elif 'android' in db_file.name.lower():
                port = 8081
            
            try:
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                
                # Get port-specific database
                port_db = PortSpecificDB(port)
                
                # Migrate test suites
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_suites'")
                if cursor.fetchone():
                    # Check which columns exist
                    cursor.execute("PRAGMA table_info(test_suites)")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # Build query based on available columns
                    base_cols = ['id', 'name']
                    optional_cols = ['description', 'created', 'updated', 'report_dir']
                    
                    select_cols = base_cols + [col for col in optional_cols if col in columns]
                    query = f"SELECT {', '.join(select_cols)} FROM test_suites"
                    
                    cursor.execute(query)
                    suite_rows = cursor.fetchall()
                    
                    for row in suite_rows:
                        suite_id = row[0]
                        name = row[1]
                        description = row[2] if len(row) > 2 and 'description' in select_cols else None
                        report_dir = None
                        if 'report_dir' in select_cols:
                            report_dir_idx = select_cols.index('report_dir')
                            if len(row) > report_dir_idx:
                                report_dir = row[report_dir_idx]
                        
                        if port_db.create_test_suite(suite_id, name, description, report_dir):
                            migrated_suites += 1
                
                # Migrate test cases
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
                if cursor.fetchone():
                    # Check which columns exist
                    cursor.execute("PRAGMA table_info(test_cases)")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # Build query based on available columns
                    base_cols = ['id', 'suite_id', 'name']
                    optional_cols = ['description', 'file_path']
                    
                    select_cols = base_cols + [col for col in optional_cols if col in columns]
                    query = f"SELECT {', '.join(select_cols)} FROM test_cases"
                    
                    cursor.execute(query)
                    case_rows = cursor.fetchall()
                    
                    for row in case_rows:
                        case_id = row[0]
                        suite_id = row[1]
                        name = row[2]
                        description = row[3] if len(row) > 3 and 'description' in select_cols else None
                        file_path = None
                        if 'file_path' in select_cols:
                            file_path_idx = select_cols.index('file_path')
                            if len(row) > file_path_idx:
                                file_path = row[file_path_idx]
                        
                        if port_db.create_test_case(case_id, suite_id, name, description, file_path):
                            migrated_cases += 1
                
                # Migrate execution settings
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_settings'")
                if cursor.fetchone():
                    # Check which columns exist
                    cursor.execute("PRAGMA table_info(execution_settings)")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # Build query based on available columns
                    base_cols = ['setting_key', 'setting_value']
                    optional_cols = ['description']
                    
                    select_cols = base_cols + [col for col in optional_cols if col in columns]
                    query = f"SELECT {', '.join(select_cols)} FROM execution_settings"
                    
                    cursor.execute(query)
                    setting_rows = cursor.fetchall()
                    
                    for row in setting_rows:
                        key = row[0]
                        value = row[1]
                        description = row[2] if len(row) > 2 and 'description' in select_cols else None
                        port_db.set_execution_setting(key, value, description)
                
                # Migrate locators repository
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='locators_repository'")
                if cursor.fetchone():
                    # Check which columns exist
                    cursor.execute("PRAGMA table_info(locators_repository)")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # Try different possible column names for element identification
                    element_col = None
                    for possible_name in ['element_name', 'name', 'element', 'locator_name']:
                        if possible_name in columns:
                            element_col = possible_name
                            break
                    
                    if element_col is None:
                        print(f"Warning: No element name column found in locators_repository for {db_file}")
                        continue
                    
                    # Build query based on available columns
                    base_cols = [element_col]
                    optional_cols = ['locator_type', 'locator_value', 'page_name', 'description']
                    
                    select_cols = base_cols + [col for col in optional_cols if col in columns]
                    query = f"SELECT {', '.join(select_cols)} FROM locators_repository"
                    
                    cursor.execute(query)
                    locator_rows = cursor.fetchall()
                    
                    for row in locator_rows:
                        element_name = row[0]
                        locator_type = row[1] if len(row) > 1 and 'locator_type' in select_cols else 'xpath'
                        locator_value = row[2] if len(row) > 2 and 'locator_value' in select_cols else ''
                        page_name = None
                        description = None
                        
                        if 'page_name' in select_cols:
                            page_idx = select_cols.index('page_name')
                            if len(row) > page_idx:
                                page_name = row[page_idx]
                        
                        if 'description' in select_cols:
                            desc_idx = select_cols.index('description')
                            if len(row) > desc_idx:
                                description = row[desc_idx]
                        
                        port_db.add_locator(element_name, locator_type, locator_value, page_name, description)
                
                conn.close()
                logger.info(f"Migrated test execution data from {db_file.name} to port {port}")
                
            except Exception as e:
                logger.error(f"Error migrating test execution data from {db_file}: {e}")
        
        logger.info(f"Total test suites migrated: {migrated_suites}")
        logger.info(f"Total test cases migrated: {migrated_cases}")
        return migrated_suites > 0 or migrated_cases > 0
    
    def create_backup(self, db_files: Dict[str, List[Path]]) -> Path:
        """Create a backup of all existing database files."""
        backup_dir = self.project_root / 'database_backup' / datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Creating backup in: {backup_dir}")
        
        backup_count = 0
        for category, files in db_files.items():
            category_dir = backup_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for db_file in files:
                if db_file.exists():
                    backup_file = category_dir / db_file.name
                    backup_file.write_bytes(db_file.read_bytes())
                    backup_count += 1
        
        # Create backup manifest
        manifest = {
            'backup_date': datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'files_backed_up': backup_count,
            'discovered_ports': list(self.discovered_ports),
            'categories': {k: [str(f) for f in v] for k, v in db_files.items()}
        }
        
        manifest_file = backup_dir / 'backup_manifest.json'
        manifest_file.write_text(json.dumps(manifest, indent=2))
        
        logger.info(f"Backup completed: {backup_count} files backed up")
        return backup_dir
    
    def run_migration(self, create_backup: bool = True, dry_run: bool = False) -> bool:
        """Run the complete migration process."""
        logger.info(f"Starting database migration (dry_run={dry_run})...")
        
        try:
            # Discover existing databases
            db_files = self.discover_existing_databases()
            
            if create_backup and not dry_run:
                backup_dir = self.create_backup(db_files)
                logger.info(f"Backup created at: {backup_dir}")
            
            if dry_run:
                logger.info("DRY RUN - No actual migration will be performed")
                return True
            
            # Migrate shared configuration data
            success = True
            
            # Migrate global values
            if db_files['global_values']:
                success &= self.migrate_global_values(db_files['global_values'])
            
            # Migrate directory paths
            if db_files['directory_paths']:
                success &= self.migrate_directory_paths(db_files['directory_paths'])
            
            # Migrate environments and variables from settings databases
            if db_files['settings']:
                success &= self.migrate_environments_and_variables(db_files['settings'])
            
            # Migrate test execution data to port-specific databases
            if db_files['test_execution']:
                success &= self.migrate_test_execution_data(db_files['test_execution'])
            
            if success:
                logger.info("Database migration completed successfully!")
                self.print_migration_summary()
            else:
                logger.error("Database migration completed with errors")
            
            return success
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def print_migration_summary(self):
        """Print a summary of the migration results."""
        logger.info("\n" + "="*50)
        logger.info("MIGRATION SUMMARY")
        logger.info("="*50)
        
        # Shared configuration summary
        global_values = self.shared_db.get_all_global_values()
        logger.info(f"Shared Configuration Database:")
        logger.info(f"  - Global values: {len(global_values)}")
        
        directory_paths = self.shared_db.get_all_directory_paths()
        logger.info(f"  - Directory path types: {len(directory_paths)}")
        
        environments = self.shared_db.get_all_environments()
        logger.info(f"  - Environments: {len(environments)}")
        
        # Port-specific database summaries
        for port in sorted(self.discovered_ports):
            try:
                port_db = PortSpecificDB(port)
                info = port_db.get_database_info()
                logger.info(f"Port {port} Database:")
                for table, count in info['tables'].items():
                    if count > 0:
                        logger.info(f"  - {table}: {count}")
            except Exception as e:
                logger.error(f"Error getting info for port {port}: {e}")
        
        logger.info("="*50)


def main():
    """Main function to run the migration."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Migrate database files to unified structure')
    parser.add_argument('--project-root', help='Path to project root directory')
    parser.add_argument('--no-backup', action='store_true', help='Skip creating backup')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be migrated without doing it')
    
    args = parser.parse_args()
    
    migration = DatabaseMigration(args.project_root)
    success = migration.run_migration(
        create_backup=not args.no_backup,
        dry_run=args.dry_run
    )
    
    if success:
        print("\nMigration completed successfully!")
        print("\nNext steps:")
        print("1. Update your application code to use the new database managers")
        print("2. Test the application with the new database structure")
        print("3. Remove old database files after confirming everything works")
    else:
        print("\nMigration failed. Check the logs for details.")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())