#!/usr/bin/env python3
"""
Port-Specific Database Manager

This module manages port-specific databases that contain:
- Test suites
- Test cases
- Test steps
- Screenshots
- Execution tracking
- Execution settings
- Locators repository

Each port instance has its own database file to avoid conflicts.
"""

import sqlite3
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class PortSpecificDB:
    def __init__(self, port: int, db_path: Optional[str] = None):
        """
        Initialize the port-specific database manager.
        
        Args:
            port: The port number for this instance
            db_path: Optional custom path to the database file.
                    If None, uses default location: data/app_data_port_{port}.db
        """
        self.port = port
        
        if db_path:
            self.db_path = Path(db_path)
        else:
            # Default location in data directory
            project_root = Path(__file__).resolve().parent.parent
            self.db_path = project_root / 'data' / f'app_data_port_{port}.db'
        
        # Check if we're in migration mode to prevent unauthorized database creation
        migration_mode = (os.environ.get('IOS_MIGRATION_MODE') == 'true' or
                         os.environ.get('ANDROID_MIGRATION_MODE') == 'true')

        if not migration_mode:
            # Ensure the data directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)

            # Initialize the database
            self._initialize_database()
        else:
            logger.info(f"Migration mode detected - skipping port-specific database creation for port {port}")
        
        logger.info(f"Using port-specific database for port {port} at: {self.db_path}")
    
    def _initialize_database(self):
        """Initialize the database with required tables."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # Create test_suites table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_suites (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    report_dir TEXT,
                    status TEXT DEFAULT 'pending'
                )
            ''')
            
            # Create test_cases table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_cases (
                    id TEXT PRIMARY KEY,
                    suite_id TEXT,
                    name TEXT NOT NULL,
                    description TEXT,
                    file_path TEXT,
                    status TEXT DEFAULT 'pending',
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (suite_id) REFERENCES test_suites(id)
                )
            ''')
            
            # Create test_steps table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_steps (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id TEXT,
                    step_number INTEGER,
                    action TEXT,
                    element TEXT,
                    value TEXT,
                    status TEXT DEFAULT 'pending',
                    error_message TEXT,
                    execution_time REAL,
                    screenshot_path TEXT,
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (case_id) REFERENCES test_cases(id)
                )
            ''')
            
            # Create screenshots table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS screenshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id TEXT,
                    step_id INTEGER,
                    file_path TEXT NOT NULL,
                    description TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (case_id) REFERENCES test_cases(id),
                    FOREIGN KEY (step_id) REFERENCES test_steps(id)
                )
            ''')
            
            # Create execution_tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    case_id TEXT,
                    execution_id TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    status TEXT,
                    error_message TEXT,
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (suite_id) REFERENCES test_suites(id),
                    FOREIGN KEY (case_id) REFERENCES test_cases(id)
                )
            ''')
            
            # Create execution_settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    description TEXT,
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create locators_repository table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS locators_repository (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    element_name TEXT NOT NULL,
                    locator_type TEXT NOT NULL,
                    locator_value TEXT NOT NULL,
                    page_name TEXT,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info(f"Port-specific database for port {self.port} initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing port-specific database for port {self.port}: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    # Test Suites Methods
    def create_test_suite(self, suite_id: str, name: str, description: Optional[str] = None, 
                         report_dir: Optional[str] = None) -> bool:
        """Create a new test suite."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO test_suites (id, name, description, report_dir)
                VALUES (?, ?, ?, ?)
            ''', (suite_id, name, description, report_dir))
            conn.commit()
            logger.info(f"Created test suite: {name} (ID: {suite_id})")
            return True
        except Exception as e:
            logger.error(f"Error creating test suite {suite_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_test_suite(self, suite_id: str) -> Optional[Dict[str, Any]]:
        """Get a test suite by ID."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, description, created, updated, report_dir, status
                FROM test_suites WHERE id = ?
            ''', (suite_id,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'name': result[1],
                    'description': result[2],
                    'created': result[3],
                    'updated': result[4],
                    'report_dir': result[5],
                    'status': result[6]
                }
            return None
        except Exception as e:
            logger.error(f"Error getting test suite {suite_id}: {e}")
            return None
        finally:
            conn.close()
    
    def get_all_test_suites(self) -> List[Dict[str, Any]]:
        """Get all test suites."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, description, created, updated, report_dir, status
                FROM test_suites ORDER BY updated DESC
            ''')
            results = cursor.fetchall()
            
            suites = []
            for result in results:
                suites.append({
                    'id': result[0],
                    'name': result[1],
                    'description': result[2],
                    'created': result[3],
                    'updated': result[4],
                    'report_dir': result[5],
                    'status': result[6]
                })
            
            return suites
        except Exception as e:
            logger.error(f"Error getting all test suites: {e}")
            return []
        finally:
            conn.close()
    
    def update_test_suite(self, suite_id: str, **kwargs) -> bool:
        """Update a test suite."""
        if not kwargs:
            return True
        
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # Build dynamic update query
            set_clauses = []
            values = []
            
            for key, value in kwargs.items():
                if key in ['name', 'description', 'report_dir', 'status']:
                    set_clauses.append(f"{key} = ?")
                    values.append(value)
            
            if not set_clauses:
                return True
            
            set_clauses.append("updated = CURRENT_TIMESTAMP")
            values.append(suite_id)
            
            query = f"UPDATE test_suites SET {', '.join(set_clauses)} WHERE id = ?"
            cursor.execute(query, values)
            conn.commit()
            
            logger.info(f"Updated test suite {suite_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating test suite {suite_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def delete_test_suite(self, suite_id: str) -> bool:
        """Delete a test suite and all related data."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            # Delete in order to respect foreign key constraints
            cursor.execute('DELETE FROM test_steps WHERE case_id IN (SELECT id FROM test_cases WHERE suite_id = ?)', (suite_id,))
            cursor.execute('DELETE FROM screenshots WHERE case_id IN (SELECT id FROM test_cases WHERE suite_id = ?)', (suite_id,))
            cursor.execute('DELETE FROM execution_tracking WHERE suite_id = ?', (suite_id,))
            cursor.execute('DELETE FROM test_cases WHERE suite_id = ?', (suite_id,))
            cursor.execute('DELETE FROM test_suites WHERE id = ?', (suite_id,))
            
            conn.commit()
            logger.info(f"Deleted test suite {suite_id} and all related data")
            return True
        except Exception as e:
            logger.error(f"Error deleting test suite {suite_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    # Test Cases Methods
    def create_test_case(self, case_id: str, suite_id: str, name: str, 
                        description: Optional[str] = None, file_path: Optional[str] = None) -> bool:
        """Create a new test case."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO test_cases (id, suite_id, name, description, file_path)
                VALUES (?, ?, ?, ?, ?)
            ''', (case_id, suite_id, name, description, file_path))
            conn.commit()
            logger.info(f"Created test case: {name} (ID: {case_id})")
            return True
        except Exception as e:
            logger.error(f"Error creating test case {case_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_test_cases_for_suite(self, suite_id: str) -> List[Dict[str, Any]]:
        """Get all test cases for a suite."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, suite_id, name, description, file_path, status, created, updated
                FROM test_cases WHERE suite_id = ? ORDER BY created
            ''', (suite_id,))
            results = cursor.fetchall()
            
            cases = []
            for result in results:
                cases.append({
                    'id': result[0],
                    'suite_id': result[1],
                    'name': result[2],
                    'description': result[3],
                    'file_path': result[4],
                    'status': result[5],
                    'created': result[6],
                    'updated': result[7]
                })
            
            return cases
        except Exception as e:
            logger.error(f"Error getting test cases for suite {suite_id}: {e}")
            return []
        finally:
            conn.close()
    
    # Execution Settings Methods
    def set_execution_setting(self, key: str, value: str, description: Optional[str] = None) -> bool:
        """Set an execution setting."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO execution_settings (setting_key, setting_value, description, updated)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, value, description))
            conn.commit()
            logger.info(f"Set execution setting: {key} = {value}")
            return True
        except Exception as e:
            logger.error(f"Error setting execution setting {key}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_execution_setting(self, key: str) -> Optional[str]:
        """Get an execution setting value."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT setting_value FROM execution_settings WHERE setting_key = ?', (key,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting execution setting {key}: {e}")
            return None
        finally:
            conn.close()
    
    def get_all_execution_settings(self) -> Dict[str, str]:
        """Get all execution settings."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT setting_key, setting_value FROM execution_settings')
            results = cursor.fetchall()
            return {key: value for key, value in results}
        except Exception as e:
            logger.error(f"Error getting all execution settings: {e}")
            return {}
        finally:
            conn.close()
    
    # Locators Repository Methods
    def add_locator(self, element_name: str, locator_type: str, locator_value: str,
                   page_name: Optional[str] = None, description: Optional[str] = None) -> bool:
        """Add a locator to the repository."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO locators_repository 
                (element_name, locator_type, locator_value, page_name, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (element_name, locator_type, locator_value, page_name, description))
            conn.commit()
            logger.info(f"Added locator: {element_name} ({locator_type}: {locator_value})")
            return True
        except Exception as e:
            logger.error(f"Error adding locator {element_name}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_locators(self, page_name: Optional[str] = None, element_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get locators from the repository."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            query = '''
                SELECT id, element_name, locator_type, locator_value, page_name, description, is_active, created, updated
                FROM locators_repository WHERE is_active = 1
            '''
            params = []
            
            if page_name:
                query += ' AND page_name = ?'
                params.append(page_name)
            
            if element_name:
                query += ' AND element_name = ?'
                params.append(element_name)
            
            query += ' ORDER BY page_name, element_name'
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            locators = []
            for result in results:
                locators.append({
                    'id': result[0],
                    'element_name': result[1],
                    'locator_type': result[2],
                    'locator_value': result[3],
                    'page_name': result[4],
                    'description': result[5],
                    'is_active': bool(result[6]),
                    'created': result[7],
                    'updated': result[8]
                })
            
            return locators
        except Exception as e:
            logger.error(f"Error getting locators: {e}")
            return []
        finally:
            conn.close()
    
    # Utility Methods
    def get_database_info(self) -> Dict[str, Any]:
        """Get information about the database."""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.cursor()
            
            info = {
                'port': self.port,
                'db_path': str(self.db_path),
                'file_size': self.db_path.stat().st_size if self.db_path.exists() else 0,
                'tables': {}
            }
            
            # Get table counts
            tables = ['test_suites', 'test_cases', 'test_steps', 'screenshots', 
                     'execution_tracking', 'execution_settings', 'locators_repository']
            
            for table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                info['tables'][table] = count
            
            return info
        except Exception as e:
            logger.error(f"Error getting database info: {e}")
            return {'port': self.port, 'error': str(e)}
        finally:
            conn.close()


# Cache for database instances
_port_db_instances = {}

def get_port_db(port: int) -> PortSpecificDB:
    """Get a port-specific database instance (cached)."""
    global _port_db_instances
    if port not in _port_db_instances:
        _port_db_instances[port] = PortSpecificDB(port)
    return _port_db_instances[port]