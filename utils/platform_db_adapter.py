#!/usr/bin/env python3
"""
Platform-Specific Database Adapter

This adapter manages the five core database files for each platform:
1. settings.db - Configuration and directory paths
2. test_suites.db - Test suite and test case data
3. environments.db - Environment-specific values
4. globals.db - Global variables and constants
5. execution_tracker.db - Test execution status and metrics
"""

import sqlite3
import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

class PlatformDBAdapter:
    """Database adapter for platform-specific database files."""
    
    def __init__(self, platform: str = 'ios'):
        """
        Initialize the platform database adapter.
        
        Args:
            platform: Either 'ios' or 'android'
        """
        self.platform = platform.lower()
        self.project_root = Path.cwd()
        self.db_data_dir = self.project_root / 'db-data'
        # Centralized database path per platform
        if self.platform == 'ios':
            self.db_path = self.db_data_dir / 'ios.db'
        elif self.platform == 'android':
            self.db_path = self.db_data_dir / 'android.db'
        else:
            raise ValueError(f"Unsupported platform: {platform}")

    
    def get_connection(self, db_type: str) -> sqlite3.Connection:
        """
        Return a connection to the centralized database (ios.db/android.db).
        The db_type is ignored in the centralized architecture because all tables
        live in the single platform database.
        """
        self.db_data_dir.mkdir(parents=True, exist_ok=True)
        return sqlite3.connect(str(self.db_path))

    
    def get_settings(self) -> Dict[str, Any]:
        """Get settings from centralized execution_settings table."""
        conn = self.get_connection('settings')
        try:
            cursor = conn.cursor()
            try:
                cursor.execute("SELECT setting_name, setting_value, category FROM execution_settings")
            except sqlite3.OperationalError:
                return {}
            rows = cursor.fetchall()
            settings = {}
            for name, value, category in rows:
                try:
                    settings[name] = json.loads(value) if value else value
                except Exception:
                    settings[name] = value
            return settings
        finally:
            conn.close()

    
    def set_setting(self, key: str, value: Any, data_type: str = 'text') -> None:
        """Upsert a setting into the centralized execution_settings table."""
        conn = self.get_connection('settings')
        try:
            cursor = conn.cursor()
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS execution_settings (
                    setting_name TEXT PRIMARY KEY,
                    setting_value TEXT,
                    description TEXT,
                    category TEXT,
                    is_default INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            value_str = json.dumps(value) if data_type == 'json' else (str(value) if value is not None else None)
            cursor.execute(
                """
                INSERT INTO execution_settings (setting_name, setting_value, description, category, is_default, created_at, updated_at)
                VALUES (?, ?, NULL, 'settings', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT(setting_name) DO UPDATE SET
                    setting_value=excluded.setting_value,
                    updated_at=CURRENT_TIMESTAMP
                """,
                (key, value_str),
            )
            conn.commit()
        finally:
            conn.close()

    
    def get_test_suites(self, platform: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all test suites from the test_suites database."""
        conn = self.get_connection('test_suites')
        try:
            cursor = conn.cursor()
            
            if platform:
                cursor.execute("""
                    SELECT id, name, description, platform, json_payload, created_at, updated_at
                    FROM test_suites WHERE platform = ?
                    ORDER BY name
                """, (platform,))
            else:
                cursor.execute("""
                    SELECT id, name, description, platform, json_payload, created_at, updated_at
                    FROM test_suites
                    ORDER BY name
                """)
            
            rows = cursor.fetchall()
            test_suites = []
            
            for row in rows:
                suite = {
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'platform': row[3],
                    'json_payload': json.loads(row[4]) if row[4] else {},
                    'created_at': row[5],
                    'updated_at': row[6]
                }
                test_suites.append(suite)
            
            return test_suites
        finally:
            conn.close()
    
    def save_test_suite(self, suite_data: Dict[str, Any]) -> int:
        """Save a test suite to the test_suites database."""
        conn = self.get_connection('test_suites')
        try:
            cursor = conn.cursor()
            
            json_payload = json.dumps(suite_data.get('json_payload', {}))
            
            if 'id' in suite_data and suite_data['id']:
                # Update existing suite
                cursor.execute("""
                    UPDATE test_suites 
                    SET name = ?, description = ?, platform = ?, json_payload = ?, updated_at = datetime('now')
                    WHERE id = ?
                """, (
                    suite_data['name'],
                    suite_data.get('description', ''),
                    suite_data.get('platform', self.platform),
                    json_payload,
                    suite_data['id']
                ))
                suite_id = suite_data['id']
            else:
                # Insert new suite
                cursor.execute("""
                    INSERT INTO test_suites (name, description, platform, json_payload, created_at, updated_at)
                    VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
                """, (
                    suite_data['name'],
                    suite_data.get('description', ''),
                    suite_data.get('platform', self.platform),
                    json_payload
                ))
                suite_id = cursor.lastrowid
            
            conn.commit()
            return suite_id
        finally:
            conn.close()
    
    def get_global_values(self) -> Dict[str, Any]:
        """Get all global values from the globals database."""
        conn = self.get_connection('globals')
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT key, value, data_type FROM globals")
            rows = cursor.fetchall()
            
            globals_dict = {}
            for key, value, data_type in rows:
                if data_type == 'json' and value:
                    try:
                        globals_dict[key] = json.loads(value)
                    except json.JSONDecodeError:
                        globals_dict[key] = value
                else:
                    globals_dict[key] = value
            
            return globals_dict
        finally:
            conn.close()
    
    def set_global_value(self, key: str, value: Any, data_type: str = 'text') -> None:
        """Set a global value in the globals database."""
        conn = self.get_connection('globals')
        try:
            cursor = conn.cursor()
            
            if data_type == 'json':
                value_str = json.dumps(value) if value is not None else None
            else:
                value_str = str(value) if value is not None else None
            
            cursor.execute("""
                INSERT OR REPLACE INTO globals (key, value, data_type, updated_at)
                VALUES (?, ?, ?, datetime('now'))
            """, (key, value_str, data_type))
            
            conn.commit()
        finally:
            conn.close()
    
    def log_execution(self, test_suite_id: int, test_case_id: Optional[int] = None, 
                     status: str = 'running', details: Optional[Dict] = None) -> int:
        """Log test execution to the execution_tracker database."""
        conn = self.get_connection('execution_tracker')
        try:
            cursor = conn.cursor()
            
            details_json = json.dumps(details) if details else None
            
            cursor.execute("""
                INSERT INTO execution_tracker 
                (test_suite_id, test_case_id, status, details, platform, started_at, updated_at)
                VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (test_suite_id, test_case_id, status, details_json, self.platform))
            
            execution_id = cursor.lastrowid
            conn.commit()
            return execution_id
        finally:
            conn.close()
    
    def update_execution_status(self, execution_id: int, status: str, 
                               details: Optional[Dict] = None) -> None:
        """Update execution status in the execution_tracker database."""
        conn = self.get_connection('execution_tracker')
        try:
            cursor = conn.cursor()
            
            details_json = json.dumps(details) if details else None
            
            if status in ['completed', 'failed', 'cancelled']:
                cursor.execute("""
                    UPDATE execution_tracker 
                    SET status = ?, details = ?, completed_at = datetime('now'), updated_at = datetime('now')
                    WHERE id = ?
                """, (status, details_json, execution_id))
            else:
                cursor.execute("""
                    UPDATE execution_tracker 
                    SET status = ?, details = ?, updated_at = datetime('now')
                    WHERE id = ?
                """, (status, details_json, execution_id))
            
            conn.commit()
        finally:
            conn.close()
    
    def get_environments(self) -> List[Dict[str, Any]]:
        """Get all environments from the environments database."""
        conn = self.get_connection('environments')
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, name, description, variables, is_active, created_at, updated_at
                FROM environments
                ORDER BY name
            """)
            rows = cursor.fetchall()
            
            environments = []
            for row in rows:
                env = {
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'variables': json.loads(row[3]) if row[3] else {},
                    'is_active': bool(row[4]),
                    'created_at': row[5],
                    'updated_at': row[6]
                }
                environments.append(env)
            
            return environments
        finally:
            conn.close()
