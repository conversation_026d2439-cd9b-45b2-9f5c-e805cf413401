#!/usr/bin/env python3
"""
Mobile Testing SaaS - Cross-Platform Local Device Agent Installer

This installer works on Windows, macOS, and Linux systems.
It automatically detects the platform and installs all necessary dependencies.
"""

import os
import sys
import platform
import subprocess
import urllib.request
import zipfile
import tarfile
import shutil
import json
from pathlib import Path
import tempfile

class Colors:
    """ANSI color codes for cross-platform terminal output"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color
    
    @classmethod
    def disable_on_windows(cls):
        """Disable colors on Windows if not supported"""
        if platform.system() == 'Windows':
            for attr in dir(cls):
                if not attr.startswith('_') and attr != 'disable_on_windows':
                    setattr(cls, attr, '')

class CrossPlatformInstaller:
    def __init__(self):
        self.system = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.python_version = sys.version_info
        
        # Disable colors on Windows if needed
        if self.system == 'windows':
            Colors.disable_on_windows()
        
        # Installation paths
        if self.system == 'windows':
            self.agent_dir = Path.home() / 'AppData' / 'Local' / 'MobileTestingAgent'
            self.config_dir = self.agent_dir / 'config'
        else:
            self.agent_dir = Path.home() / '.mobile-testing-agent'
            self.config_dir = self.agent_dir / 'config'
        
        # Ensure directories exist
        self.agent_dir.mkdir(parents=True, exist_ok=True)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Requirements
        self.min_python_version = (3, 8)
        self.min_node_version = (14, 0)
        
    def print_header(self):
        """Print installation header"""
        print(f"{Colors.BLUE}{'='*60}{Colors.NC}")
        print(f"{Colors.BLUE}  Mobile Testing SaaS - Local Agent Installer{Colors.NC}")
        print(f"{Colors.BLUE}  Cross-Platform Version 2.0{Colors.NC}")
        print(f"{Colors.BLUE}{'='*60}{Colors.NC}")
        print()
        print(f"{Colors.CYAN}Detected System: {Colors.WHITE}{self.system.title()}{Colors.NC}")
        print(f"{Colors.CYAN}Architecture: {Colors.WHITE}{self.architecture}{Colors.NC}")
        print(f"{Colors.CYAN}Python Version: {Colors.WHITE}{self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}{Colors.NC}")
        print()
    
    def print_step(self, message):
        """Print step message"""
        print(f"{Colors.GREEN}[STEP]{Colors.NC} {message}")
    
    def print_info(self, message):
        """Print info message"""
        print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")
    
    def print_warning(self, message):
        """Print warning message"""
        print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")
    
    def print_error(self, message):
        """Print error message"""
        print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")
    
    def print_success(self, message):
        """Print success message"""
        print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")
    
    def run_command(self, command, shell=True, check=True, capture_output=False):
        """Run command with proper error handling"""
        try:
            if isinstance(command, str) and not shell:
                command = command.split()
            
            result = subprocess.run(
                command, 
                shell=shell, 
                check=check, 
                capture_output=capture_output,
                text=True
            )
            return result
        except subprocess.CalledProcessError as e:
            self.print_error(f"Command failed: {command}")
            self.print_error(f"Error: {e}")
            if capture_output and e.stdout:
                self.print_error(f"Output: {e.stdout}")
            if capture_output and e.stderr:
                self.print_error(f"Error output: {e.stderr}")
            raise
    
    def check_python_version(self):
        """Check if Python version meets requirements"""
        self.print_step("Checking Python version...")
        
        if self.python_version < self.min_python_version:
            self.print_error(f"Python {self.min_python_version[0]}.{self.min_python_version[1]}+ is required")
            self.print_error(f"Current version: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
            return False
        
        self.print_success(f"Python {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro} is compatible")
        return True
    
    def check_node_js(self):
        """Check Node.js installation"""
        self.print_step("Checking Node.js installation...")
        
        try:
            result = self.run_command('node --version', capture_output=True)
            version_str = result.stdout.strip().lstrip('v')
            version_parts = version_str.split('.')
            major_version = int(version_parts[0])
            
            if major_version >= self.min_node_version[0]:
                self.print_success(f"Node.js {version_str} is compatible")
                return True
            else:
                self.print_warning(f"Node.js {version_str} found, but version {self.min_node_version[0]}+ is recommended")
                return False
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_warning("Node.js not found")
            return False
    
    def install_node_js(self):
        """Install Node.js based on platform"""
        self.print_step("Installing Node.js...")
        
        if self.system == 'windows':
            self.print_info("Please download and install Node.js from: https://nodejs.org/")
            self.print_info("After installation, restart this installer.")
            input("Press Enter after installing Node.js...")
        
        elif self.system == 'darwin':  # macOS
            # Try Homebrew first
            try:
                self.run_command('brew --version', capture_output=True)
                self.print_info("Installing Node.js via Homebrew...")
                self.run_command('brew install node')
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.print_info("Homebrew not found. Please install Node.js from: https://nodejs.org/")
                input("Press Enter after installing Node.js...")
        
        elif self.system == 'linux':
            # Try to detect package manager
            try:
                # Ubuntu/Debian
                self.run_command('apt --version', capture_output=True)
                self.print_info("Installing Node.js via apt...")
                self.run_command('curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -')
                self.run_command('sudo apt-get install -y nodejs')
            except (subprocess.CalledProcessError, FileNotFoundError):
                try:
                    # CentOS/RHEL/Fedora
                    self.run_command('yum --version', capture_output=True)
                    self.print_info("Installing Node.js via yum...")
                    self.run_command('curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -')
                    self.run_command('sudo yum install -y nodejs')
                except (subprocess.CalledProcessError, FileNotFoundError):
                    self.print_info("Please install Node.js manually from: https://nodejs.org/")
                    input("Press Enter after installing Node.js...")
    
    def install_appium(self):
        """Install Appium and drivers"""
        self.print_step("Installing Appium...")
        
        try:
            # Check if Appium is already installed
            self.run_command('appium --version', capture_output=True)
            self.print_info("Appium already installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_info("Installing Appium globally...")
            self.run_command('npm install -g appium')
            self.run_command('npm install -g appium-doctor')
        
        # Install drivers
        self.print_info("Installing Appium drivers...")
        self.run_command('appium driver install uiautomator2')
        
        if self.system == 'darwin':  # macOS only
            self.run_command('appium driver install xcuitest')
            self.print_success("Installed XCUITest driver for iOS testing")
        
        self.print_success("Appium installation completed")
    
    def install_android_tools(self):
        """Install Android development tools"""
        self.print_step("Setting up Android development tools...")
        
        # Check if ADB is available
        try:
            self.run_command('adb version', capture_output=True)
            self.print_success("Android Debug Bridge (ADB) already available")
            return
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        if self.system == 'windows':
            self.print_info("Please install Android SDK Platform Tools from:")
            self.print_info("https://developer.android.com/studio/releases/platform-tools")
            self.print_info("Add the platform-tools directory to your PATH environment variable")
        
        elif self.system == 'darwin':  # macOS
            try:
                self.run_command('brew --version', capture_output=True)
                self.print_info("Installing Android platform tools via Homebrew...")
                self.run_command('brew install android-platform-tools')
                self.print_success("Android platform tools installed")
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.print_info("Please install Android SDK Platform Tools manually")
        
        elif self.system == 'linux':
            try:
                self.run_command('apt --version', capture_output=True)
                self.print_info("Installing Android tools via apt...")
                self.run_command('sudo apt-get update')
                self.run_command('sudo apt-get install -y android-tools-adb android-tools-fastboot')
                self.print_success("Android tools installed")
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.print_info("Please install Android SDK Platform Tools manually")
    
    def install_ios_tools(self):
        """Install iOS development tools (macOS only)"""
        if self.system != 'darwin':
            self.print_info("iOS testing is only supported on macOS")
            return
        
        self.print_step("Setting up iOS development tools...")
        
        # Check Xcode Command Line Tools
        try:
            self.run_command('xcode-select --version', capture_output=True)
            self.print_success("Xcode Command Line Tools already installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_info("Installing Xcode Command Line Tools...")
            self.run_command('xcode-select --install')
            input("Press Enter after Xcode Command Line Tools installation completes...")
        
        # Install libimobiledevice for iOS device communication
        try:
            self.run_command('brew --version', capture_output=True)
            self.print_info("Installing iOS device tools via Homebrew...")
            self.run_command('brew install libimobiledevice')
            self.run_command('brew install ios-deploy')
            self.print_success("iOS development tools installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_warning("Homebrew not found. Please install libimobiledevice manually")
    
    def create_virtual_environment(self):
        """Create Python virtual environment"""
        self.print_step("Creating Python virtual environment...")
        
        venv_path = self.agent_dir / 'venv'
        
        if venv_path.exists():
            self.print_info("Virtual environment already exists")
            return venv_path
        
        # Create virtual environment
        self.run_command([sys.executable, '-m', 'venv', str(venv_path)])
        self.print_success(f"Virtual environment created at {venv_path}")
        
        return venv_path
    
    def get_pip_command(self, venv_path):
        """Get the pip command for the virtual environment"""
        if self.system == 'windows':
            return str(venv_path / 'Scripts' / 'pip.exe')
        else:
            return str(venv_path / 'bin' / 'pip')
    
    def get_python_command(self, venv_path):
        """Get the python command for the virtual environment"""
        if self.system == 'windows':
            return str(venv_path / 'Scripts' / 'python.exe')
        else:
            return str(venv_path / 'bin' / 'python')
    
    def install_python_dependencies(self, venv_path):
        """Install Python dependencies"""
        self.print_step("Installing Python dependencies...")
        
        pip_cmd = self.get_pip_command(venv_path)
        
        # Upgrade pip first
        self.run_command([pip_cmd, 'install', '--upgrade', 'pip'])
        
        # Install requirements
        requirements_file = Path(__file__).parent / 'requirements_saas.txt'
        if requirements_file.exists():
            self.run_command([pip_cmd, 'install', '-r', str(requirements_file)])
        else:
            # Install basic dependencies if requirements file not found
            dependencies = [
                'flask>=2.3.3',
                'flask-socketio>=5.3.6',
                'requests>=2.31.0',
                'websockets>=11.0.3',
                'appium-python-client>=3.1.0',
                'pure-python-adb>=0.3.0.dev0',
                'facebook-wda>=1.4.6',
                'opencv-python>=********',
                'numpy>=1.24.3',
                'pillow>=10.0.1',
                'pyyaml>=6.0.1',
                'python-dotenv>=1.0.0'
            ]
            
            for dep in dependencies:
                self.run_command([pip_cmd, 'install', dep])
        
        self.print_success("Python dependencies installed")
    
    def download_agent_files(self):
        """Download or copy agent files"""
        self.print_step("Setting up agent files...")
        
        # Copy local_device_agent.py if it exists
        agent_source = Path(__file__).parent / 'local_device_agent.py'
        agent_dest = self.agent_dir / 'local_device_agent.py'
        
        if agent_source.exists():
            shutil.copy2(agent_source, agent_dest)
            self.print_success("Agent files copied")
        else:
            self.print_warning("Agent source files not found in current directory")
            # In a real deployment, this would download from the SaaS platform
            self.print_info("In production, agent files would be downloaded from the SaaS platform")
    
    def create_configuration(self):
        """Create agent configuration"""
        self.print_step("Creating agent configuration...")
        
        config = {
            'agent_id': '',
            'api_key': '',
            'platform_url': 'ws://localhost:8080',
            'platform': self.system,
            'capabilities': {
                'android': True,
                'ios': self.system == 'darwin'
            },
            'auto_start': False,
            'log_level': 'INFO'
        }
        
        config_file = self.config_dir / 'agent.conf'
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        self.print_success(f"Configuration created at {config_file}")
        self.print_info("Please update the configuration with your SaaS platform details")
    
    def create_startup_scripts(self, venv_path):
        """Create startup scripts for the agent"""
        self.print_step("Creating startup scripts...")
        
        python_cmd = self.get_python_command(venv_path)
        agent_script = self.agent_dir / 'local_device_agent.py'
        
        if self.system == 'windows':
            # Create batch file for Windows
            script_content = f'''@echo off
cd /d "{self.agent_dir}"
"{python_cmd}" "{agent_script}"
pause
'''
            script_file = self.agent_dir / 'start_agent.bat'
            with open(script_file, 'w') as f:
                f.write(script_content)
        else:
            # Create shell script for Unix systems
            script_content = f'''#!/bin/bash
cd "{self.agent_dir}"
"{python_cmd}" "{agent_script}"
'''
            script_file = self.agent_dir / 'start_agent.sh'
            with open(script_file, 'w') as f:
                f.write(script_content)
            # Make executable
            os.chmod(script_file, 0o755)
        
        self.print_success(f"Startup script created: {script_file}")
    
    def run_diagnostics(self):
        """Run system diagnostics"""
        self.print_step("Running system diagnostics...")
        
        # Check Appium doctor if available
        try:
            result = self.run_command('appium-doctor', capture_output=True)
            self.print_info("Appium Doctor output:")
            print(result.stdout)
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_warning("Appium Doctor not available")
        
        # Check device connectivity
        self.print_info("Checking device connectivity...")
        
        # Android devices
        try:
            result = self.run_command('adb devices', capture_output=True)
            self.print_info("Android devices:")
            print(result.stdout)
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_warning("ADB not available - Android device detection disabled")
        
        # iOS devices (macOS only)
        if self.system == 'darwin':
            try:
                result = self.run_command('idevice_id -l', capture_output=True)
                if result.stdout.strip():
                    self.print_info("iOS devices detected:")
                    print(result.stdout)
                else:
                    self.print_info("No iOS devices detected")
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.print_warning("iOS device tools not available")
    
    def print_completion_message(self):
        """Print installation completion message"""
        print()
        print(f"{Colors.GREEN}{'='*60}{Colors.NC}")
        print(f"{Colors.GREEN}  Installation Completed Successfully!{Colors.NC}")
        print(f"{Colors.GREEN}{'='*60}{Colors.NC}")
        print()
        print(f"{Colors.CYAN}Agent Directory:{Colors.NC} {self.agent_dir}")
        print(f"{Colors.CYAN}Configuration:{Colors.NC} {self.config_dir / 'agent.conf'}")
        print()
        print(f"{Colors.YELLOW}Next Steps:{Colors.NC}")
        print(f"1. Update the configuration file with your SaaS platform details")
        print(f"2. Run the agent using the startup script")
        print(f"3. Register the agent with your SaaS platform")
        print()
        
        if self.system == 'windows':
            print(f"{Colors.CYAN}Start Agent:{Colors.NC} {self.agent_dir / 'start_agent.bat'}")
        else:
            print(f"{Colors.CYAN}Start Agent:{Colors.NC} {self.agent_dir / 'start_agent.sh'}")
        print()
    
    def install(self):
        """Main installation process"""
        try:
            self.print_header()
            
            # Check prerequisites
            if not self.check_python_version():
                return False
            
            # Check and install Node.js if needed
            if not self.check_node_js():
                self.install_node_js()
                if not self.check_node_js():
                    self.print_error("Node.js installation failed")
                    return False
            
            # Install Appium
            self.install_appium()
            
            # Install platform-specific tools
            self.install_android_tools()
            self.install_ios_tools()
            
            # Setup Python environment
            venv_path = self.create_virtual_environment()
            self.install_python_dependencies(venv_path)
            
            # Setup agent files and configuration
            self.download_agent_files()
            self.create_configuration()
            self.create_startup_scripts(venv_path)
            
            # Run diagnostics
            self.run_diagnostics()
            
            # Print completion message
            self.print_completion_message()
            
            return True
            
        except KeyboardInterrupt:
            self.print_error("Installation cancelled by user")
            return False
        except Exception as e:
            self.print_error(f"Installation failed: {e}")
            return False

def main():
    """Main entry point"""
    installer = CrossPlatformInstaller()
    success = installer.install()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()