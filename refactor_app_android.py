#!/usr/bin/env python3
"""
Automated refactoring script for app_android/app.py

This script breaks down the large app.py file into smaller, modular components
while maintaining 100% backward compatibility.
"""

import os
import re
import sys
from pathlib import Path

# Configuration
SOURCE_FILE = "app_android/app.py"
BACKUP_FILE = "app_android/app.py.backup"

# Route categories based on URL patterns
ROUTE_CATEGORIES = {
    'device_routes': [
        r'/api/devices',
        r'/api/device/',
        r'/api/session/',
        r'/api/check_inspector',
        r'/api/page_source',
        r'/api/session_info',
    ],
    'test_case_routes': [
        r'/api/test_cases/',
        r'/api/execute_test_case',
        r'/api/delete_test_case',
        r'/api/recording/',
        r'/api/test_cases_for_multi_step',
    ],
    'test_suite_routes': [
        r'/api/test_suites/',
    ],
    'report_routes': [
        r'/api/reports/',
        r'/api/generate_report',
        r'/api/report/',
        r'/reports/',
        r'/api/execution/',
    ],
    'environment_routes': [
        r'/api/environments',
        r'/api/environment_variables',
        r'/api/settings',
        r'/api/directory_paths',
    ],
    'action_routes': [
        r'/api/action/',
        r'/api/capture_image',
        r'/api/screenshot',
        r'/api/text_detection',
        r'/get_element_at_position',
        r'/appium/inspect_element',
    ],
    'reference_image_routes': [
        r'/api/reference_images',
        r'/api/reference_image',
        r'/api/validate_screenshot_name',
    ],
    'misc_routes': [
        r'/health',
        r'/api/debug/',
        r'/api/temp/',
        r'/api/logs/',
        r'/api/screenshots/',
        r'/api/upload_media',
        r'/api/test_debug',
        r'/api/test-execution/',
        r'/screenshot$',
        r'/screenshots/',
        r'/$',  # index route
        r'/action-templates',
        r'/test_suites$',
        r'/multi_device_test',
        r'/ios_app',
    ],
}

def create_backup():
    """Create a backup of the original file"""
    print(f"Creating backup: {BACKUP_FILE}")
    with open(SOURCE_FILE, 'r') as src:
        content = src.read()
    with open(BACKUP_FILE, 'w') as dst:
        dst.write(content)
    print("✓ Backup created")

def read_source_file():
    """Read the source file"""
    with open(SOURCE_FILE, 'r') as f:
        return f.read()

def extract_imports(content):
    """Extract all import statements from the beginning of the file"""
    lines = content.split('\n')
    imports = []
    in_imports = True
    
    for line in lines:
        stripped = line.strip()
        if in_imports:
            if (stripped.startswith('import ') or 
                stripped.startswith('from ') or
                stripped == '' or
                stripped.startswith('#')):
                imports.append(line)
            elif stripped and not stripped.startswith('#'):
                # First non-import, non-comment, non-empty line
                in_imports = False
        else:
            break
    
    return '\n'.join(imports)

def extract_route_function(content, route_decorator):
    """Extract a complete route function including its decorator"""
    # Find the decorator
    pattern = rf'(@app\.route\({re.escape(repr(route_decorator))}[^\)]*\)[^\n]*\n(?:@[^\n]+\n)*def [^(]+\([^)]*\):[^\n]*(?:\n(?:    |\t)[^\n]*)*)'
    
    matches = re.finditer(pattern, content, re.MULTILINE)
    functions = []
    
    for match in matches:
        functions.append(match.group(0))
    
    return functions

def categorize_routes(content):
    """Categorize all routes into their respective modules"""
    categorized = {category: [] for category in ROUTE_CATEGORIES.keys()}
    uncategorized = []
    
    # Find all route decorators
    route_pattern = r'@app\.route\([\'"]([^\'"]+)[\'"]'
    routes = re.findall(route_pattern, content)
    
    for route in routes:
        categorized_flag = False
        for category, patterns in ROUTE_CATEGORIES.items():
            for pattern in patterns:
                if re.search(pattern, route):
                    categorized[category].append(route)
                    categorized_flag = True
                    break
            if categorized_flag:
                break
        
        if not categorized_flag:
            uncategorized.append(route)
    
    return categorized, uncategorized

def main():
    """Main refactoring function"""
    print("=" * 80)
    print("App Android Refactoring Script")
    print("=" * 80)
    
    # Step 1: Create backup
    create_backup()
    
    # Step 2: Read source file
    print("\nReading source file...")
    content = read_source_file()
    print(f"✓ Read {len(content)} characters")
    
    # Step 3: Analyze routes
    print("\nAnalyzing routes...")
    categorized, uncategorized = categorize_routes(content)
    
    print("\nRoute Distribution:")
    for category, routes in categorized.items():
        print(f"  {category}: {len(routes)} routes")
    
    if uncategorized:
        print(f"\n  ⚠ Uncategorized: {len(uncategorized)} routes")
        for route in uncategorized[:10]:  # Show first 10
            print(f"    - {route}")
        if len(uncategorized) > 10:
            print(f"    ... and {len(uncategorized) - 10} more")
    
    # Step 4: Extract imports
    print("\nExtracting imports...")
    imports = extract_imports(content)
    print(f"✓ Extracted {len(imports.split(chr(10)))} import lines")
    
    print("\n" + "=" * 80)
    print("Analysis Complete!")
    print("=" * 80)
    print("\nNext steps:")
    print("1. Review the route distribution above")
    print("2. Manually extract route modules based on categories")
    print("3. Create backward compatibility layer in main app.py")
    print("4. Test thoroughly")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())

