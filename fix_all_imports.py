#!/usr/bin/env python3
"""
Comprehensive Import Fixer
Scans all route modules and ensures all necessary imports are present
"""

import re
from pathlib import Path

# Define all required imports for each module type
REQUIRED_IMPORTS = {
    'device_routes': {
        'standard': [
            'import os',
            'import json',
            'import base64',
            'import time',
            'import logging',
            'import glob',
            'import sys',
            'import traceback',
            'import shutil',
            'import threading',
            'import signal',
            'import uuid',
            'from pathlib import Path',
            'from datetime import datetime',
            'from functools import wraps',
        ],
        'flask': [
            'from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session, Response',
            'from werkzeug.utils import secure_filename',
        ],
        'pil': [
            'import PIL.Image',
            'import PIL.ImageDraw',
            'import io',
        ],
        'other': [
            'import xml.etree.ElementTree as ET',
            'import requests',
            'import re',
        ],
    },
    'action_routes': {
        'standard': [
            'import os',
            'import json',
            'import base64',
            'import time',
            'import logging',
            'import traceback',
            'import uuid',
            'from pathlib import Path',
            'from datetime import datetime',
        ],
        'flask': [
            'from flask import request, jsonify, session',
        ],
    },
    'test_case_routes': {
        'standard': [
            'import os',
            'import json',
            'import logging',
            'import traceback',
            'import uuid',
            'from pathlib import Path',
            'from datetime import datetime',
        ],
        'flask': [
            'from flask import request, jsonify, session, send_file',
        ],
    },
}

def check_and_fix_imports(file_path, platform='app_android'):
    """Check and fix imports in a route module"""
    
    module_name = file_path.stem
    
    if module_name not in REQUIRED_IMPORTS:
        print(f"  ⚠️  No import template for {module_name}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check if all required imports are present
    missing = []
    required = REQUIRED_IMPORTS[module_name]
    
    for category, imports in required.items():
        for imp in imports:
            # Normalize the import for comparison
            imp_normalized = imp.strip()
            if imp_normalized not in content:
                missing.append(imp_normalized)
    
    if missing:
        print(f"  ❌ Missing imports in {file_path.name}:")
        for imp in missing:
            print(f"     - {imp}")
        return False
    else:
        print(f"  ✅ All imports present in {file_path.name}")
        return True

def main():
    print("="*60)
    print("COMPREHENSIVE IMPORT CHECKER")
    print("="*60)
    
    # Check Android route modules
    print("\n📱 Checking Android Route Modules...")
    android_routes = Path("app_android/routes_modules")
    android_ok = True
    
    for module in ['device_routes.py', 'action_routes.py', 'test_case_routes.py']:
        file_path = android_routes / module
        if file_path.exists():
            if not check_and_fix_imports(file_path, 'app_android'):
                android_ok = False
        else:
            print(f"  ⚠️  File not found: {file_path}")
            android_ok = False
    
    # Check iOS route modules
    print("\n🍎 Checking iOS Route Modules...")
    ios_routes = Path("app/routes_modules")
    ios_ok = True
    
    for module in ['device_routes.py', 'action_routes.py', 'test_case_routes.py']:
        file_path = ios_routes / module
        if file_path.exists():
            if not check_and_fix_imports(file_path, 'app'):
                ios_ok = False
        else:
            print(f"  ⚠️  File not found: {file_path}")
            ios_ok = False
    
    # Summary
    print("\n" + "="*60)
    if android_ok and ios_ok:
        print("✅ ALL IMPORTS VERIFIED!")
        print("="*60)
        print("\n🎉 All route modules have correct imports!")
        print("\nNext steps:")
        print("  1. Restart the application: python run.py")
        print("  2. Test device connection")
        print("  3. Execute test suite")
        return 0
    else:
        print("❌ SOME IMPORTS MISSING!")
        print("="*60)
        print("\n⚠️  Please review the missing imports above")
        return 1

if __name__ == '__main__':
    exit(main())

