# Testing Guide: Execution Download Fix

**Date:** 2025-10-03  
**Purpose:** Verify the execution download functionality works correctly in both iOS and Android apps  
**Estimated Time:** 15-20 minutes

---

## Prerequisites

### 1. Start Both Apps

**Android App (Port 8081):**
```bash
cd /Users/<USER>/Documents/automation-tool/MobileAppAutomation
python3 app_android/app.py
```

**iOS App (Port 8080):**
```bash
cd /Users/<USER>/Documents/automation-tool/MobileAppAutomation
python3 app/app.py
```

### 2. Verify Execution Data Exists

**Check Android executions:**
```bash
ls -la reports_android/ | grep testsuite_execution
```

**Expected:** Should see multiple execution directories

**Check specific execution:**
```bash
ls -la reports_android/testsuite_execution_20251003_191006/
```

**Expected Output:**
```
-rw-r--r--  action_log.json
-rw-r--r--  data.json
drwxr-xr-x  screenshots/
-rw-r--r--  test_report_20251003_191006.pdf
-rw-r--r--  test_report_20251003_191006.zip
```

---

## Test Suite 1: Android App - Download Existing ZIP

### Test 1.1: Navigate to Test Executions Tab

**Steps:**
1. Open browser: `http://localhost:8081`
2. Click on "Test Executions" tab

**Expected Results:**
- ✅ Tab loads successfully
- ✅ Execution list displays with multiple entries
- ✅ Each execution shows:
  - Suite name
  - Status (passed/failed)
  - Start time
  - Test counts (total, passed, failed)
  - Download button

**Screenshot Location:** Take screenshot for documentation

### Test 1.2: Download Execution with Existing ZIP

**Steps:**
1. Find execution: `testsuite_execution_20251003_191006`
2. Click the "Download" button (download icon)
3. Wait for download to complete

**Expected Results:**
- ✅ No error messages in browser console
- ✅ ZIP file downloads successfully
- ✅ Filename: `test_report_20251003_191006.zip` (clean, no duplicate timestamp)
- ✅ File size: ~2-4 KB (small ZIP with PDF + JSON files)

**Verification:**
```bash
# Check if ZIP was copied to reports root
ls -la reports_android/test_report_20251003_191006.zip
```

### Test 1.3: Verify ZIP Contents

**Steps:**
1. Extract the downloaded ZIP file
2. Inspect contents

**Expected Contents:**
```
test_report_20251003_191006.zip
├── test_report_20251003_191006.pdf    # PDF report
├── action_log.json                     # Action log
└── data.json                           # Execution metadata
```

**Verification Commands:**
```bash
# Extract ZIP
unzip -l ~/Downloads/test_report_20251003_191006.zip

# Expected output:
# Archive:  test_report_20251003_191006.zip
#   Length      Date    Time    Name
# ---------  ---------- -----   ----
#      3798  10-03-2025 19:10   test_report_20251003_191006.pdf
#       360  10-03-2025 19:10   action_log.json
#      4295  10-03-2025 19:10   data.json
# ---------                     -------
#      8453                     3 files
```

**Expected Results:**
- ✅ ZIP contains exactly 3 files
- ✅ PDF file is present and valid
- ✅ action_log.json is present
- ✅ data.json is present

### Test 1.4: Verify PDF Report

**Steps:**
1. Open the PDF file from the extracted ZIP
2. Inspect contents

**Expected Results:**
- ✅ PDF opens successfully
- ✅ Contains execution summary
- ✅ Contains test case details
- ✅ Contains step-by-step actions
- ✅ Screenshots are embedded (if any)
- ✅ Status indicators are color-coded (green for pass, red for fail)

---

## Test Suite 2: Android App - Create New ZIP

### Test 2.1: Delete Existing ZIP

**Steps:**
```bash
# Delete the ZIP from the execution directory
rm reports_android/testsuite_execution_20251003_191006/test_report_20251003_191006.zip

# Delete the ZIP from reports root (if exists)
rm reports_android/test_report_20251003_191006.zip 2>/dev/null

# Verify deletion
ls -la reports_android/testsuite_execution_20251003_191006/
```

**Expected Results:**
- ✅ ZIP file is deleted
- ✅ PDF and JSON files still exist

### Test 2.2: Download Execution (Create New ZIP)

**Steps:**
1. Refresh the Test Executions tab in browser
2. Find execution: `testsuite_execution_20251003_191006`
3. Click the "Download" button

**Expected Results:**
- ✅ No error messages
- ✅ New ZIP file is created
- ✅ Filename: `testsuite_execution_20251003_191006.zip` (matches execution directory name)
- ✅ ZIP downloads successfully

**Backend Verification:**
```bash
# Check if new ZIP was created in reports root
ls -la reports_android/testsuite_execution_20251003_191006.zip

# Expected: File exists with recent timestamp
```

### Test 2.3: Verify New ZIP Contents

**Steps:**
1. Extract the newly downloaded ZIP
2. Verify contents are identical to Test 1.3

**Expected Results:**
- ✅ ZIP contains PDF, action_log.json, data.json
- ✅ All files are valid and readable

---

## Test Suite 3: iOS App - Feature Parity

### Test 3.1: Navigate to Test Executions Tab

**Steps:**
1. Open browser: `http://localhost:8080`
2. Click on "Test Executions" tab

**Expected Results:**
- ✅ Tab loads successfully
- ✅ Execution list displays
- ✅ UI is identical to Android app

### Test 3.2: Download Execution

**Steps:**
1. Find any execution in the list
2. Click the "Download" button

**Expected Results:**
- ✅ ZIP downloads successfully
- ✅ Filename format matches Android app (clean, no duplicate timestamp)
- ✅ ZIP contains PDF + JSON files

### Test 3.3: Compare with Android App

**Verification:**
- ✅ Download process is identical
- ✅ ZIP filename format is identical
- ✅ ZIP contents are identical
- ✅ Error handling is identical

---

## Test Suite 4: Edge Cases

### Test 4.1: Download Non-Existent Execution

**Steps:**
1. Use browser developer tools
2. Send POST request to: `http://localhost:8081/api/executions/nonexistent_execution/export`

**Expected Results:**
- ✅ Returns 404 error
- ✅ Error message: "Execution directory not found: nonexistent_execution"
- ✅ No server crash

### Test 4.2: Download Execution Without PDF

**Steps:**
```bash
# Create test execution directory without PDF
mkdir -p reports_android/test_no_pdf
echo '{"test": "data"}' > reports_android/test_no_pdf/data.json

# Try to download via browser
# POST to: http://localhost:8081/api/executions/test_no_pdf/export
```

**Expected Results:**
- ✅ Falls back to custom report generator
- ✅ OR returns error if no valid report files found
- ✅ No server crash

### Test 4.3: Download Legacy HTML Report

**Steps:**
1. Find an old execution directory (before PDF implementation)
2. Try to download it

**Expected Results:**
- ✅ Falls back to custom report generator
- ✅ Generates HTML-based ZIP
- ✅ ZIP contains HTML report + screenshots
- ✅ Download succeeds

---

## Test Suite 5: Performance & Reliability

### Test 5.1: Multiple Rapid Downloads

**Steps:**
1. Click download button on same execution 5 times rapidly
2. Wait for all downloads to complete

**Expected Results:**
- ✅ All downloads succeed
- ✅ No server errors
- ✅ No duplicate files created
- ✅ All ZIPs are identical

### Test 5.2: Concurrent Downloads

**Steps:**
1. Open Test Executions tab in 2 browser tabs
2. Click download on different executions simultaneously

**Expected Results:**
- ✅ Both downloads succeed
- ✅ No file conflicts
- ✅ No server errors

### Test 5.3: Large Execution Download

**Steps:**
1. Find execution with many test cases and screenshots
2. Download it

**Expected Results:**
- ✅ Download completes successfully
- ✅ ZIP file size is reasonable (not corrupted)
- ✅ All files are included in ZIP

---

## Test Suite 6: Error Handling

### Test 6.1: Check Backend Logs

**Steps:**
1. Perform a successful download
2. Check backend logs

**Expected Log Messages:**
```
INFO - Export request for execution: testsuite_execution_20251003_191006
INFO - Execution directory: /Users/<USER>/reports_android/testsuite_execution_20251003_191006
INFO - Found existing ZIP file: /Users/<USER>/reports_android/testsuite_execution_20251003_191006/test_report_20251003_191006.zip
INFO - Copied ZIP to download location: /Users/<USER>/reports_android/test_report_20251003_191006.zip
```

**Expected Results:**
- ✅ Clear, informative log messages
- ✅ No error or warning messages
- ✅ Full file paths are logged

### Test 6.2: Check Browser Console

**Steps:**
1. Open browser developer tools (F12)
2. Go to Console tab
3. Perform a download

**Expected Results:**
- ✅ No JavaScript errors
- ✅ No 500 errors
- ✅ No 404 errors
- ✅ Successful API responses

### Test 6.3: Check Network Tab

**Steps:**
1. Open browser developer tools (F12)
2. Go to Network tab
3. Perform a download

**Expected Network Requests:**
```
POST /api/executions/testsuite_execution_20251003_191006/export
  Status: 200 OK
  Response: {"success": true, "download_url": "/api/reports/download_export/test_report_20251003_191006.zip"}

GET /api/reports/download_export/test_report_20251003_191006.zip
  Status: 200 OK
  Content-Type: application/zip
  Content-Disposition: attachment; filename=test_report_20251003_191006.zip
```

**Expected Results:**
- ✅ Both requests return 200 OK
- ✅ Export endpoint returns download URL
- ✅ Download endpoint returns ZIP file
- ✅ No redirect loops

---

## Test Suite 7: Cleanup & Verification

### Test 7.1: Verify No Duplicate Files

**Steps:**
```bash
# Check for duplicate export files
find reports_android -name "export_*" -type f

# Check for duplicate export directories
find reports_android -name "export_*" -type d
```

**Expected Results:**
- ✅ Old export files with duplicate timestamps may exist (from before fix)
- ✅ No NEW export files are created after fix
- ✅ Only clean ZIP files in reports root

### Test 7.2: Verify File Permissions

**Steps:**
```bash
# Check permissions on created ZIP files
ls -la reports_android/*.zip
```

**Expected Results:**
- ✅ ZIP files are readable
- ✅ ZIP files have correct ownership
- ✅ No permission errors

### Test 7.3: Clean Up Test Files

**Steps:**
```bash
# Remove test execution directory (if created)
rm -rf reports_android/test_no_pdf

# Remove old export files (optional cleanup)
rm reports_android/export_*.zip
rm -rf reports_android/export_*
```

---

## Success Criteria

### Must Pass (Critical)

- ✅ Download button works in Test Executions tab
- ✅ ZIP file downloads successfully
- ✅ ZIP filename is clean (no duplicate timestamps)
- ✅ ZIP contains PDF + action_log.json + data.json
- ✅ PDF report is valid and readable
- ✅ No 500 errors in browser console
- ✅ No errors in backend logs
- ✅ Feature parity between iOS and Android apps

### Should Pass (Important)

- ✅ Existing ZIP files are reused (not regenerated)
- ✅ New ZIPs are created when needed
- ✅ Fallback to custom report generator works for legacy reports
- ✅ Multiple downloads don't cause conflicts
- ✅ Error messages are clear and helpful

### Nice to Have (Optional)

- ✅ Download is fast (< 2 seconds for small reports)
- ✅ ZIP file size is optimized
- ✅ Old export files are cleaned up automatically

---

## Troubleshooting

### Issue: Download button doesn't work

**Check:**
1. Browser console for JavaScript errors
2. Network tab for failed API requests
3. Backend logs for Python errors

**Solution:**
- Refresh the page
- Clear browser cache
- Restart the Flask app

### Issue: ZIP file is empty or corrupted

**Check:**
```bash
# Verify ZIP file integrity
unzip -t reports_android/testsuite_execution_20251003_191006.zip

# Check file size
ls -lh reports_android/testsuite_execution_20251003_191006.zip
```

**Solution:**
- Delete the ZIP and regenerate
- Check if PDF file exists in execution directory
- Verify file permissions

### Issue: 404 error when downloading

**Check:**
```bash
# Verify execution directory exists
ls -la reports_android/testsuite_execution_20251003_191006/

# Verify ZIP file exists
ls -la reports_android/test_report_20251003_191006.zip
```

**Solution:**
- Check execution_id matches directory name
- Verify reports directory path is correct
- Check backend logs for file path errors

---

## Reporting Results

### Test Report Template

```
# Execution Download Fix - Test Results

**Date:** [Date]
**Tester:** [Your Name]
**Apps Tested:** iOS (Port 8080), Android (Port 8081)

## Test Results Summary

| Test Suite | Tests Passed | Tests Failed | Notes |
|------------|--------------|--------------|-------|
| Suite 1: Android - Download Existing ZIP | X/4 | X/4 | |
| Suite 2: Android - Create New ZIP | X/3 | X/3 | |
| Suite 3: iOS - Feature Parity | X/3 | X/3 | |
| Suite 4: Edge Cases | X/3 | X/3 | |
| Suite 5: Performance | X/3 | X/3 | |
| Suite 6: Error Handling | X/3 | X/3 | |
| Suite 7: Cleanup | X/3 | X/3 | |

**Total:** X/22 passed, X/22 failed

## Critical Issues Found

[List any critical issues]

## Minor Issues Found

[List any minor issues]

## Recommendations

[Any recommendations for improvements]

## Conclusion

[Overall assessment: PASS / FAIL / NEEDS WORK]
```

---

## Next Steps After Testing

### If All Tests Pass ✅

1. Mark the fix as **PRODUCTION READY**
2. Update documentation
3. Close the issue ticket
4. Deploy to production (if applicable)

### If Tests Fail ❌

1. Document the failure details
2. Check backend logs for errors
3. Review the code changes
4. Report issues to developer
5. Retest after fixes are applied

---

**Total Testing Time:** ~15-20 minutes  
**Required Tools:** Browser, Terminal, ZIP extractor, PDF viewer  
**Difficulty Level:** Easy to Medium

Good luck with testing! 🚀

