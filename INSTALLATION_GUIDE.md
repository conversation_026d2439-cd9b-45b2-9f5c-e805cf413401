# INSTALLATION_GUIDE.md

## Installation and Running Instructions for End-Users

This guide provides step-by-step instructions for installing and running the distributed MobileAppAutomation applications on Windows, macOS, and Linux. These instructions assume you have received a secure build package (e.g., from the build process described in BUILD_GUIDE.md).

### General System Requirements
- **Hardware**: Modern CPU, at least 8GB RAM, sufficient storage for tools and reports.
- **Software**: Node.js and npm for Appium; browser (Chrome preferred).
- **Mobile Testing**: For Android - ADB; for iOS - Xcode (macOS only).
- **Network**: Internet for initial setup and dependencies.
- **Security**: Run as non-admin user; ensure antivirus allows the app.

### Security Considerations
- Verify package checksums provided by the distributor.
- Run in a sandboxed environment if possible.
- Do not modify executable files to maintain integrity.
- Use secure networks for any cloud integrations (e.g., Supabase).

### Installation and Running Procedures by OS

#### 1. Windows
**System Requirements**:
- Windows 10/11 (64-bit)
- Microsoft Visual C++ Redistributable
- ADB for Android testing

**Steps**:
1. Download and extract the distribution package (e.g., SecureMobileAppAutomation.zip).
2. If prompted by SmartScreen, click "More info" and "Run anyway" (assuming trusted source).
3. Open Command Prompt in the extracted folder.
4. Run the executable: `SecureMobileAppAutomation.exe`.
5. For iOS: Not fully supported on Windows; use macOS.
6. For Android: Ensure ADB is in PATH; run with admin if needed for device access.
7. The app will launch a web interface in your default browser.

**Troubleshooting**:
- Antivirus blocks: Add exception for the executable.
- Missing DLLs: Install Visual C++ Redistributable.
- Browser fails: Ensure Chrome is installed; check firewall.

#### 2. macOS
**System Requirements**:
- macOS 11+ (Big Sur or later)
- Xcode Command Line Tools
- Homebrew recommended

**Steps**:
1. Download the .app bundle or .dmg file.
2. If Gatekeeper warns, right-click and select "Open".
3. Move the app to Applications folder if desired.
4. Launch the app: Double-click SecureMobileAppAutomation.app.
5. Grant permissions for device access when prompted.
6. For iOS: Ensure Xcode is installed; app will handle WebDriverAgent.
7. For Android: Install ADB via Homebrew if needed.
8. Access the web UI that opens automatically.

**Troubleshooting**:
- Notarization issues: Contact distributor for signed build.
- Device not detected: Check USB permissions and restart.
- App crashes: Check Console.app for logs.

#### 3. Linux
**System Requirements**:
- Ubuntu 20.04+ or equivalent (64-bit)
- libusb, build-essential
- xdg-open for browser launch

**Steps**:
1. Download and extract the tar.gz package.
2. Make executable: `chmod +x SecureMobileAppAutomation`.
3. Run: `./SecureMobileAppAutomation`.
4. Install system deps if needed: `sudo apt install nodejs npm adb`.
5. For Android: Ensure ADB has permissions (add to udev rules).
6. The app will start and open the web interface.
7. Monitor terminal for logs.

**Troubleshooting**:
- Library missing: Install via apt (e.g., libssl-dev).
- No browser opens: Manually navigate to localhost:port shown in logs.
- Permission denied: Run with sudo for device access (not recommended long-term).

### Best Practices for Deployment
- Use virtual machines for testing environments.
- Configure firewalls to allow local ports (e.g., 4723 for Appium).
- Regularly update the app from trusted sources.
- Backup reports and configurations before upgrades.
- For production: Use containerization (Docker) if available.

For building from source, see BUILD_GUIDE.md.