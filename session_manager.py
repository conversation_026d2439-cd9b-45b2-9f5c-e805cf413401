#!/usr/bin/env python3
"""
Session Manager for SaaS Mobile Testing Platform

Handles user session lifecycle, cleanup, and resource management
for isolated iOS and Android testing instances.
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UserSession:
    """Represents a user's active session with platform instances"""
    user_id: str
    ios_session_id: Optional[str] = None
    android_session_id: Optional[str] = None
    last_activity: datetime = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.last_activity is None:
            self.last_activity = datetime.utcnow()
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.utcnow()
    
    def is_expired(self, timeout_hours: int = 24) -> bool:
        """Check if session has expired"""
        expiry_time = self.last_activity + timedelta(hours=timeout_hours)
        return datetime.utcnow() > expiry_time
    
    def has_active_instances(self) -> bool:
        """Check if session has any active instances"""
        return self.ios_session_id is not None or self.android_session_id is not None

class SessionManager:
    """Manages user sessions and handles cleanup of orphaned resources"""
    
    def __init__(self, orchestrator, user_sessions_dict):
        self.orchestrator = orchestrator
        self.user_sessions_dict = user_sessions_dict  # Reference to global user_sessions
        self.sessions: Dict[str, UserSession] = {}
        self.cleanup_interval = 300  # 5 minutes
        self.session_timeout_hours = 24
        self.inactive_timeout_hours = 2
        self._cleanup_thread = None
        self._stop_cleanup = False
        
        logger.info("SessionManager initialized")
    
    def start_cleanup_service(self):
        """Start the background cleanup service"""
        if self._cleanup_thread is None or not self._cleanup_thread.is_alive():
            self._stop_cleanup = False
            self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            self._cleanup_thread.start()
            logger.info("Session cleanup service started")
    
    def stop_cleanup_service(self):
        """Stop the background cleanup service"""
        self._stop_cleanup = True
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
            logger.info("Session cleanup service stopped")
    
    def _cleanup_loop(self):
        """Background cleanup loop"""
        while not self._stop_cleanup:
            try:
                self.cleanup_expired_sessions()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Error in cleanup loop: {str(e)}")
                time.sleep(self.cleanup_interval)
    
    def get_or_create_session(self, user_id: str) -> UserSession:
        """Get existing session or create new one for user"""
        if user_id not in self.sessions:
            self.sessions[user_id] = UserSession(user_id=user_id)
            logger.info(f"Created new session for user {user_id}")
        
        session = self.sessions[user_id]
        session.update_activity()
        return session
    
    def update_session_instance(self, user_id: str, platform: str, session_id: str):
        """Update session with new instance information"""
        session = self.get_or_create_session(user_id)
        
        if platform == 'ios':
            session.ios_session_id = session_id
        elif platform == 'android':
            session.android_session_id = session_id
        
        # Update global user_sessions dict
        self.user_sessions_dict[f"{user_id}_{platform}"] = session_id
        
        session.update_activity()
        logger.info(f"Updated session for user {user_id}, platform {platform}, instance {session_id}")
    
    def remove_session_instance(self, user_id: str, platform: str):
        """Remove instance from session"""
        if user_id in self.sessions:
            session = self.sessions[user_id]
            
            if platform == 'ios':
                session.ios_session_id = None
            elif platform == 'android':
                session.android_session_id = None
            
            # Remove from global user_sessions dict
            session_key = f"{user_id}_{platform}"
            if session_key in self.user_sessions_dict:
                del self.user_sessions_dict[session_key]
            
            session.update_activity()
            
            # Remove session if no active instances
            if not session.has_active_instances():
                del self.sessions[user_id]
                logger.info(f"Removed empty session for user {user_id}")
            else:
                logger.info(f"Removed {platform} instance from session for user {user_id}")
    
    def cleanup_expired_sessions(self):
        """Clean up expired and inactive sessions"""
        current_time = datetime.utcnow()
        expired_users = []
        
        for user_id, session in self.sessions.items():
            # Check for expired sessions
            if session.is_expired(self.session_timeout_hours):
                expired_users.append((user_id, 'expired'))
                continue
            
            # Check for inactive sessions
            inactive_time = current_time - session.last_activity
            if inactive_time > timedelta(hours=self.inactive_timeout_hours):
                expired_users.append((user_id, 'inactive'))
        
        # Clean up expired sessions
        for user_id, reason in expired_users:
            logger.info(f"Cleaning up {reason} session for user {user_id}")
            self._cleanup_user_session(user_id)
    
    def _cleanup_user_session(self, user_id: str):
        """Clean up all instances for a user session"""
        if user_id not in self.sessions:
            return
        
        session = self.sessions[user_id]
        
        # Stop iOS instance if exists
        if session.ios_session_id:
            try:
                self.orchestrator.stop_instance(session.ios_session_id)
                logger.info(f"Stopped iOS instance {session.ios_session_id} for user {user_id}")
            except Exception as e:
                logger.error(f"Error stopping iOS instance for user {user_id}: {str(e)}")
        
        # Stop Android instance if exists
        if session.android_session_id:
            try:
                self.orchestrator.stop_instance(session.android_session_id)
                logger.info(f"Stopped Android instance {session.android_session_id} for user {user_id}")
            except Exception as e:
                logger.error(f"Error stopping Android instance for user {user_id}: {str(e)}")
        
        # Remove from global user_sessions dict
        for platform in ['ios', 'android']:
            session_key = f"{user_id}_{platform}"
            if session_key in self.user_sessions_dict:
                del self.user_sessions_dict[session_key]
        
        # Remove session
        del self.sessions[user_id]
        logger.info(f"Cleaned up session for user {user_id}")
    
    def get_session_stats(self) -> Dict:
        """Get statistics about active sessions"""
        total_sessions = len(self.sessions)
        ios_instances = sum(1 for s in self.sessions.values() if s.ios_session_id)
        android_instances = sum(1 for s in self.sessions.values() if s.android_session_id)
        
        return {
            'total_sessions': total_sessions,
            'ios_instances': ios_instances,
            'android_instances': android_instances,
            'total_instances': ios_instances + android_instances
        }
    
    def force_cleanup_user(self, user_id: str) -> bool:
        """Force cleanup of a specific user's session"""
        try:
            self._cleanup_user_session(user_id)
            return True
        except Exception as e:
            logger.error(f"Error force cleaning user {user_id}: {str(e)}")
            return False
    
    def get_user_instances(self, user_id: str) -> List[Dict]:
        """Get all active instances for a user"""
        if user_id not in self.sessions:
            return []
        
        session = self.sessions[user_id]
        instances = []
        
        if session.ios_session_id:
            ios_instance = self.orchestrator.get_instance(session.ios_session_id)
            if ios_instance:
                instances.append({
                    'platform': 'ios',
                    'session_id': ios_instance.session_id,
                    'status': ios_instance.status,
                    'port': ios_instance.port,
                    'appium_port': ios_instance.appium_port,
                    'created_at': ios_instance.created_at.isoformat() if ios_instance.created_at else None
                })
        
        if session.android_session_id:
            android_instance = self.orchestrator.get_instance(session.android_session_id)
            if android_instance:
                instances.append({
                    'platform': 'android',
                    'session_id': android_instance.session_id,
                    'status': android_instance.status,
                    'port': android_instance.port,
                    'appium_port': android_instance.appium_port,
                    'created_at': android_instance.created_at.isoformat() if android_instance.created_at else None
                })
        
        return instances