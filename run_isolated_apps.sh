#!/bin/bash

# Script to run iOS and Android apps in complete isolation
# This script ensures both apps use different ports and configurations

echo "Starting iOS and Android apps in complete isolation..."

# Kill any existing processes on the ports we want to use
echo "Cleaning up existing processes..."
pkill -f "python.*run.py" || true
pkill -f "python.*run_android.py" || true
pkill -f "appium.*4723" || true
pkill -f "appium.*4724" || true

# Wait a moment for processes to terminate
sleep 2

# Start iOS app on port 8080 with Appium on 4723
echo "Starting iOS app on http://localhost:8080 with Appium on port 4723..."
cd "$(dirname "$0")"
python run.py --port 8080 --appium-port 4723 --wda-port 8200 &
IOS_PID=$!

# Start Android app on port 8081 with Appium on 4724
echo "Starting Android app on http://localhost:8081 with Appium on port 4724..."
python run_android.py --port 8081 --appium-port 4724 --wda-port 8300 &
ANDROID_PID=$!

echo ""
echo "Both apps are now running in isolation:"
echo "- iOS app: http://localhost:8080"
echo "- Android app: http://localhost:8081"
echo ""
echo "Appium servers:"
echo "- iOS Appium: port 4723"
echo "- Android Appium: port 4724"
echo ""
echo "Process IDs:"
echo "- iOS: $IOS_PID"
echo "- Android: $ANDROID_PID"
echo ""
echo "Press Ctrl+C to stop both applications"

# Wait for both processes
trap 'kill $IOS_PID $ANDROID_PID 2>/dev/null; exit' INT TERM
wait