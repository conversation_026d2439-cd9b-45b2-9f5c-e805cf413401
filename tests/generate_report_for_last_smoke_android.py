import os
import sqlite3
from pathlib import Path

from app_android.utils.database import get_db_path
from app_android.utils.database_report_generator import generate_html_report_from_database


def main():
    db_path = get_db_path("execution_tracker")
    con = sqlite3.connect(db_path)
    cur = con.cursor()
    cur.execute(
        "SELECT test_execution_id FROM execution_tracking WHERE suite_id=? AND test_execution_id IS NOT NULL ORDER BY id DESC LIMIT 1",
        ("SMOKE_SUITE",),
    )
    row = cur.fetchone()
    con.close()
    if not row or not row[0]:
        print("No SMOKE_SUITE execution found")
        return
    execution_id = row[0]
    print(f"Found execution_id: {execution_id}")
    repo_root = Path(__file__).resolve().parents[1]
    html_path = repo_root / "reports" / execution_id / "report_db_first.html"
    html_path.parent.mkdir(parents=True, exist_ok=True)
    ok = generate_html_report_from_database(execution_id, str(html_path))
    print(f"generate_html_report_from_database -> {ok}")
    print(f"Report: {html_path} (exists={html_path.exists()})")


if __name__ == "__main__":
    main()

