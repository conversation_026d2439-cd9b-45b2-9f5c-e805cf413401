import os
import sqlite3
import base64
from datetime import datetime
from pathlib import Path

from app_android.utils.database import (
    track_test_execution,
    save_screenshot_info,
    get_db_path,
)


def _ensure_png(path: Path):
    path.parent.mkdir(parents=True, exist_ok=True)
    # 1x1 transparent PNG
    png_b64 = (
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMBAe2m6QkAAAAASUVORK5CYII="
    )
    with open(path, "wb") as f:
        f.write(base64.b64decode(png_b64))
    return path


def main():
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    suite_id = "SMOKE_SUITE"
    test_case_id = "SMOKE_TC"
    test_idx = 0
    step_idx = 1
    execution_id = f"SMOKE_EXEC_{ts}"
    action_id = "SMOKE_ACT1"

    db_path = get_db_path("execution_tracker")
    print(f"DB file: {db_path} (exists={os.path.exists(db_path)})")

    # Create a dummy screenshot under reports structure (optional but realistic)
    repo_root = Path(__file__).resolve().parents[1]
    shots_dir = repo_root / "reports" / execution_id / "screenshots"
    shot_path = _ensure_png(shots_dir / "screenshot_0001.png")
    print(f"Created screenshot at: {shot_path}")

    # 1) Track 'running'
    ok1 = track_test_execution(
        suite_id=suite_id,
        test_idx=test_idx,
        filename=test_case_id,
        status="running",
        in_progress=True,
        step_idx=step_idx,
        action_type="info",
        action_params={"note": "smoke running"},
        action_id=action_id,
        execution_result={"status": "success", "message": "running"},
        test_case_id=test_case_id,
        test_execution_id=execution_id,
    )
    print(f"track_test_execution (running) -> {ok1}")

    # 2) Track 'passed'
    ok2 = track_test_execution(
        suite_id=suite_id,
        test_idx=test_idx,
        filename=test_case_id,
        status="passed",
        in_progress=False,
        step_idx=step_idx,
        action_type="info",
        action_params={"note": "smoke passed"},
        action_id=action_id,
        execution_result={"status": "success", "message": "passed"},
        test_case_id=test_case_id,
        test_execution_id=execution_id,
    )
    print(f"track_test_execution (passed) -> {ok2}")

    # 3) Save screenshot
    ok3 = save_screenshot_info(
        suite_id=suite_id,
        test_idx=test_idx,
        step_idx=step_idx,
        filename=test_case_id,
        path=str(shot_path),
        action_id=action_id,
    )
    print(f"save_screenshot_info -> {ok3}")

    # Verify DB rows
    con = sqlite3.connect(db_path)
    cur = con.cursor()

    cur.execute(
        "SELECT COUNT(*) FROM execution_tracking WHERE test_execution_id=?",
        (execution_id,),
    )
    et_count = cur.fetchone()[0]

    cur.execute(
        "SELECT COUNT(*), SUM(LENGTH(COALESCE(screenshot_blob,''))) FROM screenshots WHERE test_execution_id=?",
        (execution_id,),
    )
    sc_count, sc_bytes = cur.fetchone()

    print(f"Rows: execution_tracking={et_count}, screenshots={sc_count}, screenshot_bytes={sc_bytes}")

    con.close()


if __name__ == "__main__":
    main()

