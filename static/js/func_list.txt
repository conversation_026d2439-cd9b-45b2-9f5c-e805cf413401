// --- Global Variables ---
// ... existing code ...
// Add global variables at the top of the file, after other variable declarations
// Alias for showToast to maintain compatibility with both naming conventions
// Basic socket error handling
// Basic socket handlers as fallback
// Connect to selected device
// DOM elements
// Display element details in the modal
// Element inspection functionality
// Function to add a new action
// Function to add an action to the UI
// Function to check Appium connection status
// Function to clear all actions (with confirmation)
// Function to clear all actions from the UI
// Function to clear the action log
// Function to continue inspecting elements after viewing one
// Function to delete a test case
// Function to delete an action
// Function to disable screen picking mode
// Function to edit an action
// Function to enable picking coordinates from the screen
// Function to enter edit mode for an action
// Function to execute a specific action by index
// Function to execute all actions
// Function to execute an Assert Element action
// Function to exit edit mode
// Function to get all actions from the UI
// Function to handle a loaded test case (deprecated - included for backward compatibility)
// Function to handle image assertion
// Function to initialize element inspector
// Function to initialize socket connection
// Function to load a test case
// Function to load actions from session storage
// Function to open the load test case modal
// Function to populate the test cases table
// Function to reset action form fields
// Function to save actions to session storage
// Function to save the current test case
// Function to set up socket event handlers
// Function to show a visual indicator of the picked point
// Function to show the appropriate action form based on action type
// Function to show the element inspector modal
// Function to start picking coordinates for tap or swipe actions
// Function to start polling for execution status
// Function to stop execution
// Function to stop polling for execution status
// Function to update button states based on connection and actions
// Function to update indices of all actions
// Global variable to track polling status
// Handle Execute All button click
// Handle action execution result from the server
// Handle mouse down on screen
// Handle mouse move on screen
// Handle mouse up on screen
// Handle playback status updates from the server
// Handle recorded action from the server
// Handle test suite execution status updates
// Helper function to add a locator row
// Helper function to format dates
// Helper function to get key name
// Initialize Bootstrap tooltips
// Initialize Sortable on the actions list
// Initialize UI references
// Initialize on load
// Load connected devices
// Log an action to the action log
// Main JavaScript for Mobile App Automation Tool
// Placeholder function for element highlights
// Refresh the device screen
// Separate function to handle connect button click
// Set up event listeners
// Setup drag and drop for action list
// Setup event listeners for Pick from Screen buttons
// Setup event listeners for the cancel button
// Show or hide loading overlay
// Timeouts
// Toggle element inspection mode
// Update UI control state based on app state
// Update handleInspectionClick function
// Update socket handling for action results to stop polling when complete
// Update test case status in suite execution UI
// Use locator in the form
function addAction() {
function addActionToUI(action) {
function addLocatorRow(table, type, value, locatorType) {
function clearActionLog() {
function clearActions() {
function clearAllActions() {
function clearElementHighlights() {
function connectToDevice() {
function continueInspecting() {
function deleteAction(index) {
function deleteTestCase(filename) {
function disableScreenPicking() {
function displayElementDetails(element) {
function displayToast(type, message, title = 'Notification') {
function editAction(index) {
function enableScreenPicking(inputXId, inputYId) {
function enterEditMode(index) {
function executeAllActions() {
function exitEditMode() {
function formatDate(dateString) {
function getAllActions() {
function getKeyName(keyCode) {
function handleActionRecorded(data) {
function handleActionResult(data) {
function handleConnectButtonClick() {
function handleLoadedTestCase(data) {
function handleMouseDown(e) {
function handleMouseMove(e) {
function handleMouseUp(e) {
function handlePlaybackStatus(data) {
function handleSuiteExecutionStatus(data) {
function initElementInspector() {
function initSortable(actionsList) {
function initializeSocket() {
function initializeTooltips() {
function initializeUIReferences() {
function isDeviceConnected() {
function loadActionsFromSessionStorage() {
function loadConnectedDevices() {
function loadTestCase(filename) {
function logAction(source, message, type = 'info') {
function openLoadTestCaseModal() {
function populateTestCasesTable(testCases) {
function refreshDeviceScreen() {
function refreshScreenshot() {
function resetActionForm() {
function saveActionsToSessionStorage() {
function saveTestCase() {
function setupBasicSocketHandlers() {
function setupCancelButton() {
function setupDragAndDrop() {
function setupEventListeners() {
function setupOverlayCanvas() {
function setupPickFromScreenButtons() {
function setupSocketEventHandlers() {
function showActionForm(actionType) {
function showElementInspectorModal(elementData) {
function showLoading(show, message = 'Loading...') {
function showPickedPoint(x, y) {
function showToast(title, message, type) {
function startPickingCoordinates(mode) {
function startPollingExecutionStatus() {
function stopExecution() {
function stopPollingExecutionStatus() {
function toggleInspectionMode() {
function updateActionIndices() {
function updateActionResult(data) {
function updateButtonStates() {
function updateControlState() {
function updateTestCaseStatusInSuite(index, status) {
function useLocator(type, value) {
let actionLog;
let connectBtn;
let connectTimeout;
let currentDeviceId = null;
let deviceConnected = false;
let deviceListTimeout;
let deviceScreen;
let deviceSelect;
let executionStatusPollInterval = null;
let inspectionMode = false;
let isPlaying = false;
let isPollingExecutionStatus = false;
let isRecording = false;
let isSwiping = false;  // Added missing variable
let lastUploadedImageCoords = null;
let lastUploadedImageData = null;
let overlayCanvas;
let refreshDevicesBtn;
let refreshScreenBtn;
let screenContainer;
let screenshotInterval;
let screenshotTimeout;
let socket = null;
