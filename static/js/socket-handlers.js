/**
 * Socket.io event handlers for Mobile App Automation Tool
 */

// Initialize event handlers for socket connection 
function setupSocketEventHandlers() {
    if (!socket) {
        console.error("Socket not initialized");
        return;
    }
    
    // Handle connection status updates
    socket.on('connection_status', function(data) {
        console.log("Received connection status:", data);
        
        const connectBtn = document.getElementById('connectBtn');
        const deviceSelect = document.getElementById('deviceSelect');
        const refreshScreenBtn = document.getElementById('refreshScreenBtn');
        const inspectElementBtn = document.getElementById('inspectElementBtn');
        
        if (data.status === 'connected') {
            // Device is fully connected
            connectBtn.innerText = 'Disconnect';
            connectBtn.classList.remove('btn-success');
            connectBtn.classList.add('btn-danger');
            
            // Enable device controls
            refreshScreenBtn.removeAttribute('disabled');
            inspectElementBtn.removeAttribute('disabled');
            
            // Save the connected device ID
            currentDeviceId = data.device_id;
            
            // Start screenshot interval
            startScreenshotInterval();
            
            // Update UI button states
            updateButtonStates();
            
            // Log connection success
            logAction('Connection', data.message || 'Device connected successfully', 'success');
        }
        else if (data.status === 'disconnected') {
            // Device is disconnected
            connectBtn.innerText = 'Connect';
            connectBtn.classList.remove('btn-danger');
            connectBtn.classList.add('btn-success');
            
            // Disable device controls
            refreshScreenBtn.setAttribute('disabled', 'disabled');
            inspectElementBtn.setAttribute('disabled', 'disabled');
            
            // Stop screenshot interval
            stopScreenshotInterval();
            
            // Update default device screen
            const deviceScreen = document.getElementById('deviceScreen');
            if (deviceScreen) {
                deviceScreen.src = 'static/img/no_device.png';
            }
            
            // Clear device ID
            currentDeviceId = null;
            
            // Update UI button states
            updateButtonStates();
            
            // Log disconnection
            logAction('Connection', data.message || 'Device disconnected', 'warning');
        }
        else if (data.status === 'error') {
            // Connection error
            logAction('Error', data.message || 'Connection error', 'error');
            
            // Ensure connect button is in the right state
            connectBtn.innerText = 'Connect';
            connectBtn.classList.remove('btn-danger');
            connectBtn.classList.add('btn-success');
            
            // Allow connection retries
            deviceSelect.removeAttribute('disabled');
        }
        else if (data.status === 'connecting') {
            // Connection in progress
            logAction('Connection', data.message || 'Connecting to device...', 'info');
            
            // Disable device selector during connection
            deviceSelect.setAttribute('disabled', 'disabled');
        }
    });
    
    // Handle device screenshot updates
    socket.on('screenshot', function(data) {
        if (data.success) {
            // Auto refresh the screenshot
            refreshScreenshot();
            
            // If we have text for overlay, display it
            if (data.overlay_text) {
                const deviceScreen = document.getElementById('deviceScreen');
                // Display overlay text here
            }
        }
    });
    
    // Handle screenshot errors
    socket.on('screenshot_error', function(data) {
        console.error("Screenshot error:", data.message);
        
        // Count consecutive errors
        screenshotErrorCount = (screenshotErrorCount || 0) + 1;
        
        // If there are persistent screenshot errors, check device connection
        if (screenshotErrorCount > 3) {
            logAction('Error', 'Multiple screenshot errors. Device might be disconnected.', 'error');
            screenshotErrorCount = 0;
        }
    });
    
    // Handle action recorded from the server
    socket.on('action_recorded', function(data) {
        handleActionRecorded(data);
    });
    
    // Handle execution results
    socket.on('action_result', function(data) {
        handleActionResult(data);
    });
    
    // Handle playback status updates
    socket.on('playback_status', function(data) {
        handlePlaybackStatus(data);
    });
    
    // Handle suite execution status updates
    socket.on('suite_execution_status', function(data) {
        handleSuiteExecutionStatus(data);
    });
}

// Set up basic error handling for the socket connection
function setupBasicSocketHandlers() {
    if (!socket) return;
    
    socket.on('connect', function() {
        console.log('Socket connected');
        logAction('System', 'Socket connection established', 'success');
    });
    
    socket.on('disconnect', function() {
        console.log('Socket disconnected');
        logAction('System', 'Socket connection lost', 'error');
        
        // Stop screenshot interval if active
        stopScreenshotInterval();
    });
    
    socket.on('connect_error', function(error) {
        console.error('Socket connection error:', error);
        logAction('System', `Socket connection error: ${error.message}`, 'error');
    });
}

// Helper function to update connection status UI
function updateConnectionStatus(isConnected, deviceInfo) {
    // Update global state
    deviceConnected = isConnected;
    
    // Update connect button state
    const connectBtn = document.getElementById('connectBtn');
    if (connectBtn) {
        if (isConnected) {
            connectBtn.textContent = 'Disconnect';
            connectBtn.classList.remove('btn-primary');
            connectBtn.classList.add('btn-danger');
            connectBtn.disabled = false;
        } else {
            connectBtn.textContent = 'Connect';
            connectBtn.classList.remove('btn-danger');
            connectBtn.classList.add('btn-primary');
            connectBtn.disabled = false;
        }
    }
    
    // Update device info area if connected
    const deviceInfoContainer = document.getElementById('deviceInfo');
    if (deviceInfoContainer) {
        if (isConnected && deviceInfo) {
            deviceInfoContainer.innerHTML = `
                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-title">Device Information</h6>
                        <p class="mb-1"><strong>Model:</strong> ${deviceInfo.model || 'Unknown'}</p>
                        <p class="mb-1"><strong>Android:</strong> ${deviceInfo.android_version || 'Unknown'}</p>
                        <p class="mb-1"><strong>Screen:</strong> ${deviceInfo.screen_dimensions || 'Unknown'}</p>
                        <p class="mb-0"><strong>Battery:</strong> ${deviceInfo.battery_level || 'Unknown'}</p>
                    </div>
                </div>
            `;
            deviceInfoContainer.classList.remove('d-none');
        } else {
            deviceInfoContainer.innerHTML = '';
            deviceInfoContainer.classList.add('d-none');
        }
    }
    
    // Enable/disable action controls
    updateControlState();
} 