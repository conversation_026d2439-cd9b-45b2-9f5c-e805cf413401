// Terminal Log Parser - Direct log reading from the server output
const TerminalLogParser = {
  initialize: function() {
    console.log("Terminal Log Parser initialized");
    
    // Set up regular expressions to match action logs
    this.patterns = {
      executeStart: /Executing (tap|swipe|text|wait|key) (action|command)/,
      tapExecute: /Executing tap command with coordinates \((\d+), (\d+)\)/,
      tapSuccess: /Tap command executed successfully at \((\d+), (\d+)\)/,
      textExecute: /Inputting text: (.*)/,
      waitExecute: /Waiting for (\d+\.?\d*) seconds/,
      keyExecute: /Pressing key: (\w+)/,
      swipeExecute: /Executing swipe command from \((\d+), (\d+)\) to \((\d+), (\d+)\)/,
      swipeSuccess: /Swipe command executed successfully from \((\d+), (\d+)\) to \((\d+), (\d+)\)/
    };
    
    // Set up polling to read logs
    this.startPolling();
  },
  
  startPolling: function() {
    // Poll for logs every 500ms
    this.pollInterval = setInterval(() => {
      this.fetchLatestLogs();
    }, 500);
  },
  
  stopPolling: function() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }
  },
  
  fetchLatestLogs: function() {
    fetch('/api/get_latest_logs')
      .then(response => response.json())
      .then(data => {
        if (data.logs && data.logs.length > 0) {
          this.processLogs(data.logs);
        }
      })
      .catch(error => console.error("Error fetching logs:", error));
  },
  
  processLogs: function(logs) {
    logs.forEach(log => {
      // Add log to action log
      this.addToActionLog(log);
      
      // Check for action patterns and update UI
      this.updateActionStatusFromLog(log);
    });
  },
  
  addToActionLog: function(logEntry) {
    const actionLog = document.getElementById('action-log');
    if (!actionLog) return;
    
    // Skip non-action related logs
    if (!this.isActionRelatedLog(logEntry)) {
      return;
    }
    
    const entry = document.createElement('div');
    
    // Determine log type
    let logType = 'info';
    if (logEntry.includes("successfully")) {
      logType = 'success';
    } else if (logEntry.includes("Error") || logEntry.includes("Failed")) {
      logType = 'error';
    } else if (logEntry.includes("Executing")) {
      logType = 'info';
    }
    
    entry.className = `log-entry ${logType}`;
    const time = new Date().toLocaleTimeString();
    
    // Clean up the log entry for display
    const cleanedLog = this.cleanLogEntry(logEntry);
    
    entry.innerHTML = `<span class="log-time">[${time}]</span> <span class="log-type">Action</span> ${cleanedLog}`;
    actionLog.appendChild(entry);
    
    // Auto-scroll to bottom
    actionLog.scrollTop = actionLog.scrollHeight;
  },
  
  cleanLogEntry: function(logEntry) {
    // Remove timestamps and app prefixes
    return logEntry.replace(/^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}\] INFO in app: /, '');
  },
  
  isActionRelatedLog: function(logEntry) {
    // Only include logs related to actions
    return this.patterns.executeStart.test(logEntry) ||
           logEntry.includes("command executed successfully") ||
           logEntry.includes("Inputting text") ||
           logEntry.includes("Waiting for") ||
           logEntry.includes("Pressing key");
  },
  
  updateActionStatusFromLog: function(logEntry) {
    const cleanLog = this.cleanLogEntry(logEntry);
    
    // Try to match with tap execution
    let match = this.patterns.tapExecute.exec(cleanLog);
    if (match) {
      const x = match[1];
      const y = match[2];
      this.updateActionRowStatus(`Tap at (${x}, ${y})`, 'running');
      return;
    }
    
    // Try to match with tap success
    match = this.patterns.tapSuccess.exec(cleanLog);
    if (match) {
      const x = match[1];
      const y = match[2];
      this.updateActionRowStatus(`Tap at (${x}, ${y})`, 'success');
      return;
    }
    
    // Try to match with text input
    if (cleanLog.includes("Inputting text:")) {
      const text = cleanLog.split("Inputting text:")[1].trim();
      this.updateActionRowStatus(`Input text: "${text.substring(0, 20)}${text.length > 20 ? '...' : ''}"`, 'running');
      
      // Since there's no explicit "text success" message, we'll mark it as success after a delay
      setTimeout(() => {
        this.updateActionRowStatus(`Input text: "${text.substring(0, 20)}${text.length > 20 ? '...' : ''}"`, 'success');
      }, 1000);
      return;
    }
    
    // Try to match with wait
    if (cleanLog.includes("Waiting for")) {
      const duration = cleanLog.split("Waiting for")[1].split("seconds")[0].trim();
      this.updateActionRowStatus(`Wait for ${duration} seconds`, 'running');
      
      // Since waits take time, we'll mark them as success after the specified duration
      setTimeout(() => {
        this.updateActionRowStatus(`Wait for ${duration} seconds`, 'success');
      }, parseFloat(duration) * 1000);
      return;
    }
    
    // Try to match with key press
    if (cleanLog.includes("Pressing key:")) {
      const key = cleanLog.split("Pressing key:")[1].trim();
      this.updateActionRowStatus(`Press key: ${key}`, 'running');
      
      // Since there's no explicit "key success" message, we'll mark it as success after a delay
      setTimeout(() => {
        this.updateActionRowStatus(`Press key: ${key}`, 'success');
      }, 1000);
      return;
    }
  },
  
  updateActionRowStatus: function(actionText, status) {
    // Find all action rows
    const actionRows = document.querySelectorAll('tr[id^="action-"]');
    
    // Look for a row with matching text
    for (let i = 0; i < actionRows.length; i++) {
      const row = actionRows[i];
      const rowText = row.textContent || '';
      
      if (rowText.includes(actionText)) {
        // Found a matching row, update its status
        const statusCell = row.querySelector('.action-status');
        
        // Remove existing status classes
        row.classList.remove('running', 'success', 'error');
        
        // Update based on status
        if (status === 'running') {
          row.classList.add('running');
          if (statusCell) {
            statusCell.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
          }
        } else if (status === 'success') {
          row.classList.add('success');
          if (statusCell) {
            statusCell.innerHTML = '<i class="fa fa-check text-success"></i>';
          }
        } else if (status === 'error') {
          row.classList.add('error');
          if (statusCell) {
            statusCell.innerHTML = '<i class="fa fa-times text-danger"></i>';
          }
        }
        
        break;
      }
    }
  }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  TerminalLogParser.initialize();
}); 