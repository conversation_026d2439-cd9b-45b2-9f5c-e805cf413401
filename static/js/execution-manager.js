// Unified Execution Manager - handles both UI and Socket.IO communication
const ExecutionManager = {
  isRunning: false,
  currentActionIndex: -1,
  totalActions: 0,
  actions: [],
  
  // Initialize the execution manager
  initialize: function() {
    // Try to find buttons by ID first, then by text content
    const executeAllBtn = document.getElementById('execute-all-btn') || 
                         this.findButtonByText('Execute All');
    const stopExecutionBtn = document.getElementById('stop-execution-btn') || 
                            this.findButtonByText('Stop Execution');
    
    // Handle conflict with old executeAllActions function
    if (typeof window.executeAllActions === 'function') {
      console.warn('Detected old executeAllActions function, disabling it to prevent conflicts');
      // Save the old function in case it's needed
      window._oldExecuteAllActions = window.executeAllActions;
      // Replace with a function that redirects to our manager
      window.executeAllActions = function() {
        console.log('Redirecting old executeAllActions call to ExecutionManager');
        ExecutionManager.startExecution();
        return false;
      };
    }
    
    if (executeAllBtn) {
      // Remove any existing event listeners or onclick handlers
      executeAllBtn.removeAttribute('onclick');
      // Add our event listener
      executeAllBtn.addEventListener('click', this.startExecution.bind(this));
    }
    
    if (stopExecutionBtn) {
      stopExecutionBtn.removeAttribute('onclick');
      stopExecutionBtn.addEventListener('click', this.stopExecution.bind(this));
    }
    
    // Set up Socket.IO listeners
    socket.on('action_result', this.handleActionResult.bind(this));
    socket.on('action_log', this.handleActionLog.bind(this));
    
    console.log('Execution Manager initialized');
  },
  
  // Start execution of all actions
  startExecution: function() {
    // Clear the action log
    this.clearActionLog();
    
    // Collect all actions from the DOM
    this.actions = [];
    document.querySelectorAll('[id^="action-"]').forEach(actionRow => {
      if (actionRow.dataset.action) {
        this.actions.push(JSON.parse(actionRow.dataset.action));
      }
    });
    
    this.totalActions = this.actions.length;
    
    if (this.totalActions === 0) {
      this.logMessage('error', 'No actions to execute');
      return;
    }
    
    // Update UI to show execution is starting
    this.isRunning = true;
    this.currentActionIndex = -1;
    this.updateButtonStates();
    
    // Log the start
    this.logMessage('info', `Starting execution of ${this.totalActions} actions`);
    
    // Send to server
    socket.emit('execute_all_actions', { actions: this.actions });
  },
  
  // Stop execution
  stopExecution: function() {
    if (!this.isRunning) return;
    
    // Update UI to show stopping
    const stopBtn = document.getElementById('stop-execution-btn');
    if (stopBtn) {
      stopBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Stopping...';
      stopBtn.classList.add('stopping');
    }
    
    // Log the stop request
    this.logMessage('warning', 'Stop requested by user');
    
    // Tell the server to stop
    socket.emit('stop_execution', {});
  },
  
  // Handle action result from server
  handleActionResult: function(data) {
    console.log('Action result:', data);
    
    // Update current action index if provided
    if (data.action_index !== undefined) {
      this.currentActionIndex = data.action_index;
    }
    
    // Update total actions if provided
    if (data.total_actions) {
      this.totalActions = data.total_actions;
    }
    
    // Map the status to the appropriate log type
    let logType = 'info';
    switch (data.status) {
      case 'started':
        this.isRunning = true;
        logType = 'info';
        break;
        
      case 'running':
        this.isRunning = true;
        logType = 'info';
        break;
        
      case 'success':
        logType = 'execution';  // Use our custom execution type
        // If completed flag is set, execution is done
        if (data.completed) {
          this.isRunning = false;
        }
        break;
        
      case 'error':
      case 'failed':
        logType = 'error';
        // If completed flag is set or there's an error, execution is done
        if (data.completed) {
          this.isRunning = false;
        }
        break;
        
      case 'stopped':
        this.isRunning = false;
        logType = 'warning';
        break;
    }
    
    // Log the message with the correct type
    if (data.message) {
      this.logMessage(logType, data.message);
    }
    
    // Update UI buttons
    this.updateButtonStates();
    
    // Always refresh screenshot if available
    if (data.screenshot) {
      this.refreshScreenshot();
    }
  },
  
  // Handle action log from server
  handleActionLog: function(data) {
    this.logMessage(data.type || 'info', data.message);
  },
  
  // Log a message to the action log
  logMessage: function(type, message) {
    const logContainer = document.getElementById('action-log');
    if (!logContainer) return;
    
    // Create log entry
    const entry = document.createElement('div');
    
    // Fix the message type display
    let displayType = 'System';  // Default type
    let cssClass = 'info';
    
    // Map type to proper display text and CSS class
    if (type === 'error') {
      displayType = 'Error';
      cssClass = 'error';
    } else if (type === 'warning') {
      displayType = 'Warning';
      cssClass = 'warning';
    } else if (type === 'success') {
      displayType = 'Success';
      cssClass = 'success';
    } else if (type === 'info') {
      displayType = 'Info';
      cssClass = 'info';
    } else if (type === 'execution') {
      displayType = 'Execution';
      cssClass = 'success';
    }
    
    // Set the CSS class
    entry.className = `log-entry ${cssClass}`;
    
    // Format the log entry with the correct type prefix
    const time = new Date().toLocaleTimeString();
    entry.innerHTML = `<span class="log-time">[${time}]</span> <span class="log-type">${displayType}</span> ${message}`;
    
    logContainer.appendChild(entry);
    logContainer.scrollTop = logContainer.scrollHeight;
  },
  
  // Clear the action log
  clearActionLog: function() {
    const logContainer = document.getElementById('action-log');
    if (logContainer) {
      logContainer.innerHTML = '';
    }
  },
  
  // Update button states based on execution status
  updateButtonStates: function() {
    const executeAllBtn = document.getElementById('execute-all-btn');
    const stopExecutionBtn = document.getElementById('stop-execution-btn');
    
    if (executeAllBtn) {
      executeAllBtn.disabled = this.isRunning;
      if (this.isRunning) {
        executeAllBtn.classList.add('disabled');
      } else {
        executeAllBtn.classList.remove('disabled');
      }
    }
    
    if (stopExecutionBtn) {
      stopExecutionBtn.disabled = !this.isRunning;
      if (!this.isRunning) {
        stopExecutionBtn.classList.add('disabled');
        stopExecutionBtn.innerHTML = '<i class="fa fa-stop"></i> Stop Execution';
        stopExecutionBtn.classList.remove('stopping');
      } else {
        stopExecutionBtn.classList.remove('disabled');
      }
    }
  },
  
  // Refresh screenshot
  refreshScreenshot: function() {
    if (typeof refreshScreenshot === 'function') {
      refreshScreenshot();
    }
  },
  
  // Add this helper function to ExecutionManager
  findButtonByText: function(text) {
    // Try several selectors to find the button
    return document.querySelector(`button[id="execute-all-btn"]`) || 
           document.querySelector(`button:contains("${text}")`) ||
           Array.from(document.querySelectorAll('button')).find(btn => 
             btn.textContent.toLowerCase().includes(text.toLowerCase())
           );
  }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  ExecutionManager.initialize();
}); 