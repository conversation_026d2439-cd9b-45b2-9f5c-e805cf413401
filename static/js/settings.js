// Settings and Configuration Manager

// Define loadTestCases as a global function
let loadTestCases;

document.addEventListener('DOMContentLoaded', function() {
    console.log("Settings manager initialized");
    
    // Get elements
    const directoriesForm = document.getElementById('directoriesForm');
    const testcasesDirInput = document.getElementById('testcasesDir');
    const reportsDirInput = document.getElementById('reportsDir');
    const saveDirectoriesBtn = document.getElementById('saveDirectories');
    
    // Tab handlers - load appropriate data when tabs are shown
    document.getElementById('settings-tab-btn').addEventListener('click', function() {
        loadDirectories();
    });
    
    document.getElementById('test-cases-tab-btn').addEventListener('click', function() {
        loadTestCases();
    });
    
    document.getElementById('test-suites-tab-btn').addEventListener('click', function() {
        loadTestSuites();
    });
    
    document.getElementById('reports-tab-btn').addEventListener('click', function() {
        loadReports();
    });
    
    // Directory form submission
    if (directoriesForm) {
        directoriesForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDirectories();
        });
    }
    
    // Load directories when settings page is opened
    function loadDirectories() {
        console.log("Loading directories configuration");
        fetch('/get_directories')
            .then(response => response.json())
            .then(data => {
                console.log("Directories loaded:", data);
                if (testcasesDirInput) testcasesDirInput.value = data.testcases_dir || '';
                if (reportsDirInput) reportsDirInput.value = data.reports_dir || '';
            })
            .catch(error => {
                console.error("Error loading directories:", error);
                logAction('Error', 'Failed to load directories: ' + error.message, 'error');
            });
    }
    
    // Save directories
    function saveDirectories() {
        const testcasesDir = testcasesDirInput ? testcasesDirInput.value : '';
        const reportsDir = reportsDirInput ? reportsDirInput.value : '';
        
        console.log("Saving directories:", { testcasesDir, reportsDir });
        
        fetch('/set_directories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                testcases_dir: testcasesDir,
                reports_dir: reportsDir
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logAction('Settings', 'Directories saved successfully', 'success');
                // Refresh the test cases list if we're on that tab
                if (document.getElementById('test-cases-tab').classList.contains('active')) {
                    loadTestCases();
                }
            } else {
                logAction('Error', data.message || 'Failed to save directories', 'error');
            }
        })
        .catch(error => {
            console.error("Error saving directories:", error);
            logAction('Error', 'Failed to save directories: ' + error.message, 'error');
        });
    }
    
    // Test Cases Management - Make this function available globally
    loadTestCases = function() {
        console.log("Loading test cases");
        fetch('/test_cases')
            .then(response => response.json())
            .then(data => {
                populateTestCasesTable(data.test_cases || []);
            })
            .catch(error => {
                console.error("Error loading test cases:", error);
                logAction('Error', 'Failed to load test cases: ' + error.message, 'error');
            });
    }
    
    function populateTestCasesTable(testCases) {
        console.log("Populating test cases table with:", testCases);
        const tableBody = document.querySelector('#modalTestCasesTable tbody');
        if (!tableBody) {
            console.error("Test cases table body not found");
            return;
        }
        
        // Clear existing rows
        tableBody.innerHTML = '';
        
        if (testCases.length === 0) {
            // No test cases
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" class="text-center">No test cases found</td>';
            tableBody.appendChild(row);
            return;
        }
        
        // Add test cases to table
        testCases.forEach(testCase => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${testCase.name}</td>
                <td>${testCase.description || '-'}</td>
                <td>${formatDate(testCase.created)}</td>
                <td>${testCase.action_count}</td>
                <td>
                    <button class="btn btn-sm btn-primary load-test-case" data-filename="${testCase.filename}">
                        <i class="bi bi-folder-symlink"></i> Load
                    </button>
                    <button class="btn btn-sm btn-danger delete-test-case" data-filename="${testCase.filename}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
            
            // Add event listeners to buttons
            row.querySelector('.load-test-case').addEventListener('click', function() {
                loadTestCase(testCase.filename);
            });
            
            row.querySelector('.delete-test-case').addEventListener('click', function() {
                deleteTestCase(testCase.filename);
            });
        });
    }
    
    function loadTestCase(filename) {
        console.log("Loading test case:", filename);
        fetch(`/load_test_case/${filename}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Handle loading the test case into the UI
                    logAction('Test Case', `Loaded test case: ${data.test_case.name}`, 'success');
                    
                    // Get a reference to the main tab where we'll return focus
                    const deviceTab = document.getElementById('device-tab');
                    
                    // Close the modal and ensure proper focus management
                    const modalElement = document.getElementById('loadTestCaseModal');
                    if (modalElement) {
                        // First remove any active focus inside the modal
                        document.activeElement.blur();
                        
                        // Get Bootstrap modal instance and hide it
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        if (modal) {
                            // Hide the modal
                            modal.hide();
                            
                            // After modal is hidden, move focus to the main tab to avoid focus being trapped
                            modalElement.addEventListener('hidden.bs.modal', function handleHidden() {
                                // Move focus to device tab or other appropriate element
                                if (deviceTab) deviceTab.focus();
                                // Remove this one-time event listener
                                modalElement.removeEventListener('hidden.bs.modal', handleHidden);
                            }, { once: true });
                        }
                    }
                    
                    // Dispatch an event so other modules can handle loading the actions
                    document.dispatchEvent(new CustomEvent('testCaseLoaded', { 
                        detail: data.test_case 
                    }));
                } else {
                    logAction('Error', data.message || 'Failed to load test case', 'error');
                }
            })
            .catch(error => {
                console.error("Error loading test case:", error);
                logAction('Error', 'Failed to load test case: ' + error.message, 'error');
            });
    }
    
    function deleteTestCase(filename) {
        if (!confirm(`Are you sure you want to delete the test case "${filename}"?`)) {
            return;
        }
        
        console.log("Deleting test case:", filename);
        fetch(`/delete_test_case/${filename}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logAction('Test Case', 'Test case deleted successfully', 'success');
                loadTestCases(); // Refresh the list
            } else {
                logAction('Error', data.message || 'Failed to delete test case', 'error');
            }
        })
        .catch(error => {
            console.error("Error deleting test case:", error);
            logAction('Error', 'Failed to delete test case: ' + error.message, 'error');
        });
    }
    
    // Test Suites Management
    function loadTestSuites() {
        console.log("Loading test suites");
        fetch('/get_test_suites')
            .then(response => response.json())
            .then(data => {
                populateTestSuitesTable(data.test_suites || []);
            })
            .catch(error => {
                console.error("Error loading test suites:", error);
                logAction('Error', 'Failed to load test suites: ' + error.message, 'error');
            });
    }
    
    function populateTestSuitesTable(testSuites) {
        console.log("Populating test suites table with:", testSuites);
        const tableBody = document.querySelector('#test-suites-table tbody');
        if (!tableBody) {
            console.error("Test suites table body not found");
            return;
        }
        
        // Clear existing rows
        tableBody.innerHTML = '';
        
        if (testSuites.length === 0) {
            // No test suites
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" class="text-center">No test suites found</td>';
            tableBody.appendChild(row);
            return;
        }
        
        // Add test suites to table
        testSuites.forEach(suite => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${suite.name}</td>
                <td>${suite.description || '-'}</td>
                <td>${formatDate(suite.updated || suite.created)}</td>
                <td>${suite.test_count}</td>
                <td>
                    <button class="btn btn-sm btn-success execute-suite" data-filename="${suite.filename}">
                        <i class="bi bi-play-fill"></i>
                    </button>
                    <button class="btn btn-sm btn-primary edit-suite" data-filename="${suite.filename}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-suite" data-filename="${suite.filename}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
            
            // Add event listeners to buttons
        });
    }
    
    // Reports Management
    function loadReports() {
        console.log("Loading reports");
        fetch('/get_reports')
            .then(response => response.json())
            .then(data => {
                populateReportsTable(data.reports || []);
            })
            .catch(error => {
                console.error("Error loading reports:", error);
                logAction('Error', 'Failed to load reports: ' + error.message, 'error');
            });
    }
    
    function populateReportsTable(reports) {
        console.log("Populating reports table with:", reports);
        const tableBody = document.querySelector('#reportsTable tbody');
        if (!tableBody) {
            console.error("Reports table body not found");
            return;
        }
        
        // Clear existing rows
        tableBody.innerHTML = '';
        
        if (reports.length === 0) {
            // No reports
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="3" class="text-center">No reports found</td>';
            tableBody.appendChild(row);
            return;
        }
        
        // Add reports to table
        reports.forEach(report => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${report.test_case}</td>
                <td>${formatDate(report.timestamp)}</td>
                <td>
                    <a href="${report.url}" target="_blank" class="btn btn-sm btn-primary">
                        <i class="bi bi-eye"></i> View
                    </a>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }
    
    // Helper function for formatting dates
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? dateString : date.toLocaleString();
    }
}); 