/**
 * Shared utility functions for Mobile App Automation Tool
 */

// Screenshot handling utilities
let screenshotInterval = null;
let screenshotTimeout = null;

/**
 * Format a date string for display
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return date.toLocaleString();
}

/**
 * Refresh the device screenshot
 */
function refreshScreenshot() {
    const deviceScreen = document.getElementById('device-screen');
    if (!deviceScreen) return;
    
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    deviceScreen.src = `/screenshot?t=${timestamp}`;
    
    // Update the overlay canvas size to match the screenshot
    setupOverlayCanvas();
}

/**
 * Setup the canvas overlay for interaction with the device screen
 */
function setupOverlayCanvas() {
    const overlayCanvas = document.getElementById('overlay-canvas');
    const deviceScreen = document.getElementById('device-screen');
    if (!overlayCanvas || !deviceScreen) return;
    
    // Wait for the device screen to load
    if (deviceScreen.complete) {
        updateCanvasSize();
    } else {
        deviceScreen.onload = updateCanvasSize;
    }
    
    function updateCanvasSize() {
        overlayCanvas.width = deviceScreen.clientWidth;
        overlayCanvas.height = deviceScreen.clientHeight;
    }
}

/**
 * Start polling for screenshot updates at a regular interval
 */
function startScreenshotInterval(interval = 2000) {
    // Clear any existing screenshot interval to avoid duplicates
    if (screenshotInterval) {
        console.log("Clearing existing screenshot interval");
        clearInterval(screenshotInterval);
        screenshotInterval = null;
    }
    
    // Start a new interval
    console.log(`Starting screenshot interval (${interval}ms)`);
    screenshotInterval = setInterval(function() {
        refreshScreenshot();
    }, interval);
    
    // Do an immediate refresh
    refreshScreenshot();
}

/**
 * Stop polling for screenshot updates
 */
function stopScreenshotInterval() {
    if (screenshotInterval) {
        console.log("Stopping screenshot interval");
        clearInterval(screenshotInterval);
        screenshotInterval = null;
    }
    
    if (screenshotTimeout) {
        clearTimeout(screenshotTimeout);
        screenshotTimeout = null;
    }
}

/**
 * Show or hide a loading overlay
 */
function showLoading(show, message = 'Loading...') {
    const loadingOverlay = document.getElementById('loading-overlay');
    const loadingMessage = document.getElementById('loading-message');
    
    if (!loadingOverlay || !loadingMessage) return;
    
    if (show) {
        loadingMessage.textContent = message;
        loadingOverlay.style.display = 'flex';
    } else {
        loadingOverlay.style.display = 'none';
    }
}

/**
 * Log an action to the action log
 */
function logAction(source, message, type = 'info') {
    const actionLog = document.getElementById('action-log');
    if (!actionLog) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `
        <span class="log-time">${timestamp}</span>
        <span class="log-source">${source}</span>
        <span class="log-message">${message}</span>
    `;
    
    actionLog.appendChild(logEntry);
    actionLog.scrollTop = actionLog.scrollHeight;
}

/**
 * Clear the action log
 */
function clearActionLog() {
    const actionLog = document.getElementById('action-log');
    if (actionLog) {
        actionLog.innerHTML = '';
    }
}

/**
 * Show a toast notification
 * 
 * Both showToast and displayToast are provided for backward compatibility
 */
function showToast(title, message, type = 'info') {
    displayToast(type, message, title);
}

function displayToast(type, message, title = 'Notification') {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-header">
            <strong>${title}</strong>
            <button type="button" class="toast-close">&times;</button>
        </div>
        <div class="toast-body">${message}</div>
    `;
    
    // Add to container
    toastContainer.appendChild(toast);
    
    // Add close button event
    const closeButton = toast.querySelector('.toast-close');
    closeButton.addEventListener('click', function() {
        toast.classList.add('toast-hiding');
        setTimeout(() => {
            toast.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('toast-hiding');
            setTimeout(() => {
                if (toast.parentNode) toast.remove();
            }, 300);
        }
    }, 5000);
    
    // Animate in
    setTimeout(() => {
        toast.classList.add('toast-show');
    }, 10);
} 