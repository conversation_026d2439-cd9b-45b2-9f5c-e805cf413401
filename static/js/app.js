// Add this status manager to handle execution status updates
const ExecutionStatusManager = {
  panel: null,
  progressBar: null,
  currentLabel: null,
  progressText: null,
  logContainer: null,
  totalActions: 0,
  currentAction: 0,
  isRunning: false,
  
  initialize() {
    this.panel = document.getElementById('execution-status-panel');
    this.progressBar = document.getElementById('execution-progress-bar');
    this.currentLabel = document.getElementById('current-action-label');
    this.progressText = document.getElementById('progress-text');
    this.logContainer = document.getElementById('execution-log');
    
    // Set up minimize button
    document.getElementById('minimize-status-panel').addEventListener('click', () => {
      this.panel.classList.toggle('minimized');
    });
  },
  
  start(totalActions) {
    this.totalActions = totalActions;
    this.currentAction = 0;
    this.isRunning = true;
    this.panel.style.display = 'block';
    this.panel.classList.remove('minimized');
    this.progressBar.style.width = '0%';
    this.currentLabel.textContent = 'Starting execution...';
    this.progressText.textContent = `0/${totalActions}`;
    this.logContainer.innerHTML = '';
    this.addLogEntry('Execution started', 'info');
  },
  
  update(actionIndex, status, message) {
    if (!this.isRunning) return;
    
    this.currentAction = actionIndex + 1;
    const percent = Math.round((this.currentAction / this.totalActions) * 100);
    
    this.progressBar.style.width = `${percent}%`;
    this.currentLabel.textContent = message || `Executing action ${this.currentAction}`;
    this.progressText.textContent = `${this.currentAction}/${this.totalActions}`;
    
    this.addLogEntry(`Action ${actionIndex + 1}: ${message || status}`, status);
    
    // Auto-scroll log to bottom
    this.logContainer.scrollTop = this.logContainer.scrollHeight;
  },
  
  complete(success, message) {
    this.isRunning = false;
    this.progressBar.style.width = '100%';
    this.currentLabel.textContent = success ? 'Execution completed' : 'Execution failed';
    this.addLogEntry(message || (success ? 'All actions completed successfully' : 'Execution failed'), 
                     success ? 'success' : 'error');
    
    // Keep panel visible for 10 seconds after completion
    setTimeout(() => {
      if (!this.isRunning) {
        this.panel.classList.add('minimized');
      }
    }, 10000);
  },
  
  addLogEntry(message, type) {
    const entry = document.createElement('div');
    entry.className = `log-entry ${type || 'info'}`;
    entry.innerHTML = `<span class="log-time">[${new Date().toLocaleTimeString()}]</span> ${message}`;
    this.logContainer.appendChild(entry);
  }
};

// Update the socket.io handler for action results
socket.on('action_result', function(data) {
  // Only update the execution status panel, don't modify row classes or add spinners
  if (data.status === 'started') {
    ExecutionStatusManager.start(data.total_actions || 0);
  } 
  else if (data.status === 'running') {
    ExecutionStatusManager.update(
      data.action_index || 0, 
      'info', 
      data.message || `Executing action ${data.action_index + 1}`
    );
  }
  else if (data.status === 'success') {
    if (data.completed) {
      ExecutionStatusManager.complete(true, data.message);
    } else {
      ExecutionStatusManager.update(
        data.action_index || 0, 
        'success', 
        data.message || 'Action completed successfully'
      );
    }
  }
  else if (data.status === 'error' || data.status === 'failed') {
    if (data.completed) {
      ExecutionStatusManager.complete(false, data.message);
    } else {
      ExecutionStatusManager.update(
        data.action_index || 0, 
        'error', 
        data.message || 'Action failed'
      );
    }
  }
  else if (data.status === 'stopped') {
    ExecutionStatusManager.complete(false, data.message || 'Execution stopped');
  }
  
  // Always refresh the screenshot if available
  if (data.screenshot) {
    refreshScreenshot();
  }
  
  // Remove any spinner or status indicator that might still be present
  const allActionRows = document.querySelectorAll('.action-row');
  allActionRows.forEach(row => {
    // Remove all status classes
    row.classList.remove('running', 'success', 'error', 'warning');
    
    // Clear any spinners
    const statusCell = row.querySelector('.action-status');
    if (statusCell) {
      statusCell.innerHTML = '';
    }
  });
});

// Initialize the execution status manager when the document is ready
document.addEventListener('DOMContentLoaded', function() {
  ExecutionStatusManager.initialize();
});

// WebSocket health monitor
const SocketHealthMonitor = {
  pingInterval: null,
  connectionLost: false,
  
  start() {
    // Clear any existing interval
    this.stop();
    
    // Start ping every 5 seconds
    this.pingInterval = setInterval(() => {
      socket.emit('ping', {}, (response) => {
        if (this.connectionLost) {
          console.log('Connection restored');
          this.connectionLost = false;
          
          // If execution was in progress, request status update
          if (ExecutionStatusManager.isRunning) {
            socket.emit('check_execution_status');
          }
        }
      });
      
      // Set a timeout to detect missed pongs
      setTimeout(() => {
        if (!this.connectionLost) {
          console.log('WebSocket connection may be lost');
          this.connectionLost = true;
          
          // Show reconnection message
          if (ExecutionStatusManager.isRunning) {
            ExecutionStatusManager.addLogEntry('Connection lost, attempting to reconnect...', 'warning');
          }
        }
      }, 4000);
    }, 5000);
  },
  
  stop() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }
};

// Initialize the socket health monitor
document.addEventListener('DOMContentLoaded', function() {
  SocketHealthMonitor.start();
});

// Handle pong response
socket.on('pong', function(data) {
  // Connection is alive
}); 

// Remove or comment out all code that adds/removes classes from action rows
// For example, change this:
/*
function executeAction(index) {
    // Show spinner for the action
    $(`#action-${index} .action-status`).html('<i class="fa fa-spinner fa-spin"></i>');
    $(`#action-${index}`).removeClass('success error').addClass('running');
    
    // Execute the action
    socket.emit('execute_action', { index: index });
}
*/

// To this:
function executeAction(index) {
    // Execute the action without modifying UI row status
    socket.emit('execute_action', { index: index });
} 

// Improve action log display with more detailed formatting
socket.on('action_log', function(data) {
    var logContainer = document.getElementById('action-log');
    if (!logContainer) return;
    
    var entry = document.createElement('div');
    entry.className = 'log-entry ' + (data.type || 'info');
    
    var time = new Date().toLocaleTimeString();
    entry.innerHTML = '<span class="log-time">[' + time + ']</span> ' + data.message;
    
    logContainer.appendChild(entry);
    
    // Auto-scroll to bottom
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Log to console as well for debugging
    console.log(`[Action Log] ${data.type}: ${data.message}`);
});

// Helper function to get descriptions for each action
function get_action_description(action) {
    switch (action.type) {
        case 'tap':
            return `Tapping at (${action.x}, ${action.y})`;
        case 'swipe':
            return `Swiping from (${action.start_x}, ${action.start_y}) to (${action.end_x}, ${action.end_y})`;
        case 'text':
            return `Entering text: "${action.text.substring(0, 15)}${action.text.length > 15 ? '...' : ''}"`;
        case 'wait':
            return `Waiting for ${action.duration} seconds`;
        case 'key':
            return `Pressing key: ${action.key}`;
        default:
            return `${action.type} action`;
    }
} 

// Replace the existing stop button handler with this more robust version
function setupStopButton() {
  const stopButton = document.querySelector('[id="stop-execution-btn"]') || 
                    document.querySelector('button.btn-danger:contains("Stop Execution")') ||
                    document.querySelector('button:contains("Stop Execution")');
  
  if (stopButton) {
    console.log("Stop button found:", stopButton);
    
    // Remove any existing listeners to avoid duplicates
    stopButton.removeEventListener('click', handleStopExecution);
    
    // Add the click handler
    stopButton.addEventListener('click', handleStopExecution);
    
    // Make sure it's not disabled
    stopButton.disabled = false;
    stopButton.classList.remove('disabled');
    
    console.log("Stop button handler attached");
  } else {
    console.error("Stop button not found in the DOM");
  }
}

// Separate the handler function for reusability
function handleStopExecution() {
  console.log("Stop execution button clicked");
  
  // Visual feedback that stop was requested
  this.classList.add('stopping');
  this.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Stopping...';
  
  // Send the stop command to the server
  socket.emit('stop_execution', {}, function(response) {
    console.log('Stop execution acknowledged by server:', response);
  });
  
  // Show immediate visual feedback in the UI
  showNotification('Stopping execution...', 'warning');
}

// Call this function at multiple points to ensure it runs
document.addEventListener('DOMContentLoaded', setupStopButton);

// Also set up a MutationObserver to watch for dynamically added buttons
const observer = new MutationObserver(function(mutations) {
  for (let mutation of mutations) {
    if (mutation.type === 'childList' && mutation.addedNodes.length) {
      setupStopButton();
    }
  }
});

// Start observing the document body for DOM changes
observer.observe(document.body, { childList: true, subtree: true });

// Run setup immediately in case the DOM is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setupStopButton();
}

// Add notification helper
function showNotification(message, type) {
    const notificationArea = document.getElementById('notification-area') || 
                            createNotificationArea();
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = message;
    notificationArea.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => notification.remove(), 500);
    }, 5000);
}

// Create notification area if it doesn't exist
function createNotificationArea() {
    const area = document.createElement('div');
    area.id = 'notification-area';
    area.className = 'notification-area';
    document.body.appendChild(area);
    return area;
}

// Add this function to check and fix button states
function checkButtonStates() {
    // Get all important buttons
    const executeAllBtn = document.getElementById('execute-all-btn');
    const stopExecutionBtn = document.getElementById('stop-execution-btn');
    
    // Check and fix the stop button
    if (stopExecutionBtn) {
        console.log("Stop button state check:", stopExecutionBtn.disabled, stopExecutionBtn.classList.contains('disabled'));
        
        // Make sure it's not disabled
        stopExecutionBtn.disabled = false;
        stopExecutionBtn.classList.remove('disabled');
        
        // Add a direct click handler for extra reliability
        stopExecutionBtn.onclick = function() {
            console.log("Direct click on stop button");
            socket.emit('stop_execution', {});
            showNotification('Stopping execution...', 'warning');
            this.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Stopping...';
            return false; // Prevent default
        };
    }
    
    // Log button states
    console.log("Button state check complete");
}

// Run this periodically
setInterval(checkButtonStates, 3000);
// Also run when page loads
document.addEventListener('DOMContentLoaded', checkButtonStates);

// Add this function to clear the action log
function clearActionLog() {
    const actionLog = document.getElementById('action-log');
    if (actionLog) {
        actionLog.innerHTML = '';
        console.log("Action log cleared");
    }
}

// Modify the executeAction function to clear the log first
function executeAction(index) {
    // Clear the action log before executing
    clearActionLog();
    
    // Execute the action
    socket.emit('execute_action', { index: index });
}

// Add this to the Execute All handler
document.getElementById('execute-all-btn').addEventListener('click', function() {
    // Clear the action log
    clearActionLog();
    
    // Get all action data
    const actions = [];
    document.querySelectorAll('[id^="action-"]').forEach(actionRow => {
        const actionId = actionRow.id.replace('action-', '');
        if (actionRow.dataset.action) {
            actions.push(JSON.parse(actionRow.dataset.action));
        }
    });
    
    // Execute all actions
    socket.emit('execute_all_actions', { actions: actions });
});

// Add handler for clearing action log
socket.on('clear_action_log', function() {
    clearActionLog();
}); 