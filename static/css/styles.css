/* Execution Status Panel */
.execution-status-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  max-height: 300px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  transition: all 0.3s ease;
}

.execution-status-panel.minimized {
  height: 40px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.status-header h3 {
  margin: 0;
  font-size: 16px;
}

.minimize-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.status-content {
  padding: 10px;
  max-height: 250px;
  overflow-y: auto;
}

.progress-container {
  height: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-bar {
  height: 100%;
  width: 0;
  background-color: #4CAF50;
  transition: width 0.3s ease;
}

.status-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.status-log {
  font-family: monospace;
  font-size: 12px;
  max-height: 190px;
  overflow-y: auto;
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 3px;
}

.log-entry {
  margin-bottom: 6px;
  padding-bottom: 3px;
  border-bottom: 1px solid #eee;
  font-size: 13px;
}

.log-entry .log-time {
  color: #666;
  margin-right: 5px;
}

.log-entry .log-type {
  font-weight: bold;
  margin-right: 5px;
}

.log-entry.success { color: #2e7d32; }
.log-entry.error { color: #c62828; }
.log-entry.info { color: #1565c0; }
.log-entry.warning { color: #ef6c00; }

/* Add specific styling for execution logs */
.log-entry.execution {
  color: #2e7d32;
  font-weight: 500;
}

/* Add these styles for action status indicators */
.action-row.running {
  background-color: rgba(0, 123, 255, 0.1);
}

.action-row.success {
  background-color: rgba(40, 167, 69, 0.1);
}

.action-row.error {
  background-color: rgba(220, 53, 69, 0.1);
}

.action-row.warning {
  background-color: rgba(255, 193, 7, 0.1);
}

.action-status {
  width: 40px;
  text-align: center;
}

.fa-spinner {
  color: #007bff;
}

.fa-check {
  color: #28a745;
}

.fa-times {
  color: #dc3545;
}

/* Enhance action log styling */
.action-log-container {
  height: 200px;
  overflow-y: auto;
  background-color: #f5f5f5;
  font-family: monospace;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  line-height: 1.5;
}

/* Execute All button */
#execute-all-btn {
  background-color: #4CAF50;
  color: white;
  transition: background-color 0.3s;
}

#execute-all-btn:hover {
  background-color: #367c39;
}

#execute-all-btn.disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

/* Stop Execution button */
#stop-execution-btn {
  background-color: #f44336;
  color: white;
  transition: background-color 0.3s;
}

#stop-execution-btn:hover {
  background-color: #d32f2f;
}

#stop-execution-btn.disabled {
  background-color: #e57373;
  cursor: not-allowed;
}

#stop-execution-btn.stopping {
  background-color: #ff9800;
}

/* Notification Area */
.notification-area {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 350px;
}

.notification {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    animation: slide-in 0.3s ease-out;
}

.notification.success { background-color: #2ecc71; color: white; }
.notification.warning { background-color: #f39c12; color: white; }
.notification.error { background-color: #e74c3c; color: white; }
.notification.info { background-color: #3498db; color: white; }

.notification.fade-out {
    opacity: 0;
    transition: opacity 0.5s;
}

@keyframes slide-in {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Add styles for terminal log */
.terminal-log-container {
  height: 200px;
  overflow-y: auto;
  background-color: #000;
  color: #ddd;
  font-family: 'Courier New', monospace;
  padding: 10px;
  font-size: 12px;
  line-height: 1.4;
}

.terminal-log-entry {
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-break: break-all;
}

.terminal-log-entry.stdout {
  color: #ddd;
}

.terminal-log-entry.stderr {
  color: #f77;
} 

