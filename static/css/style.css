/* Main Styles */
body {
    background-color: #f8f9fa;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f1f3f5;
}

/* Device Screen */
#screenContainer {
    position: relative;
    display: inline-block;
    max-width: 100%;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: #000;
    margin: 0 auto;
    padding: 10px;
    width: 350px; /* Smaller width for better usability */
}

#deviceScreen {
    max-width: 100%;
    height: auto;
    display: block;
    border-radius: 4px;
}

/* Make the screen container responsive */
@media (max-width: 400px) {
    #screenContainer {
        width: 90%; /* Full width on small screens */
    }
}

/* Improve screen overlay for better visualization */
#overlayCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 15;
}

/* Recording Indicator */
.recording-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 15px;
    height: 15px;
    background-color: #f00;
    border-radius: 50%;
    animation: pulse 1s infinite;
    z-index: 20;
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    50% {
        transform: scale(1);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.8);
        opacity: 1;
    }
}

/* Code Output */
#codeOutput {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
}

/* Action Log */
.log-entry {
    border-bottom: 1px solid #eee;
    padding: 4px 8px;
    font-size: 0.9rem;
}

.log-info {
    background-color: #f8f9fa;
}

.log-warning {
    background-color: #fff3cd;
}

.log-error {
    background-color: #f8d7da;
}

.log-code {
    background-color: #f0f8ff;
    color: #333;
}

.log-timestamp {
    color: #666;
    font-size: 0.8rem;
}

.log-action {
    font-weight: bold;
    margin-right: 5px;
}

/* Code display in log */
.log-code {
    margin: 0;
    padding: 8px;
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    font-family: monospace;
    font-size: 0.85rem;
    overflow-x: auto;
    white-space: pre;
    margin-bottom: 4px;
}

/* Enhance tap animation */
.tap-marker {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 0, 0, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    animation: tap-animation 1s forwards;
    z-index: 20;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
}

@keyframes tap-animation {
    0% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.5);
    }
}

/* Swipe Path Animation */
.swipe-path {
    position: absolute;
    height: 4px;
    background-color: rgba(0, 128, 255, 0.7);
    pointer-events: none;
    z-index: 10;
}

.swipe-path:after {
    content: '';
    position: absolute;
    top: -3px;
    right: -3px;
    width: 10px;
    height: 10px;
    background-color: rgba(0, 128, 255, 0.8);
    border-radius: 50%;
}

.swipe-preview {
    opacity: 0.5;
    background-color: rgba(0, 200, 255, 0.5);
}

/* Change Highlight */
.change-highlight {
    position: absolute;
    border: 2px solid rgba(255, 200, 0, 0.7);
    background-color: rgba(255, 255, 0, 0.1);
    pointer-events: none;
    animation: highlight-fade 3s forwards;
    z-index: 5;
}

@keyframes highlight-fade {
    0% {
        opacity: 0.8;
    }
    80% {
        opacity: 0.8;
    }
    100% {
        opacity: 0;
    }
}

/* Loading Overlay */
#loadingOverlay {
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1000;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .card-body {
        padding: 1rem;
    }
    
    #deviceScreen {
        max-height: 50vh;
    }
    
    .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Element highlighting */
.element-highlight {
    position: absolute;
    border: 2px dashed rgba(0, 128, 0, 0.7);
    background-color: rgba(0, 255, 0, 0.1);
    pointer-events: none;
    z-index: 15;
}

.text-element {
    border-color: rgba(0, 128, 255, 0.7);
    background-color: rgba(0, 128, 255, 0.1);
}

.button-element {
    border-color: rgba(128, 0, 128, 0.7);
    background-color: rgba(128, 0, 128, 0.1);
}

.input-element {
    border-color: rgba(255, 128, 0, 0.7);
    background-color: rgba(255, 128, 0, 0.1);
}

/* Element tooltip */
.element-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 25;
    transform: translate(5px, -100%);
    max-width: 200px;
    word-break: break-word;
}

/* Elements panel */
#elementsPanel {
    max-height: 300px;
    overflow-y: auto;
}

.element-item {
    padding: 5px;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.element-item:hover {
    background-color: #e9ecef;
}

.element-text {
    font-weight: bold;
}

.element-type {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Add error indicator for action errors */
.error-indicator {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: #f53939;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Manual Action Builder Styles */
.action-form {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    margin-top: 10px;
}

#actionsList .list-group-item {
    transition: background-color 0.2s;
}

#actionsList .list-group-item:hover {
    background-color: #f8f9fa;
}

.badge-pill {
    margin-right: 8px;
}

.delete-action {
    opacity: 0.6;
    transition: opacity 0.2s;
}

.delete-action:hover {
    opacity: 1;
}

.screen-container {
    position: relative;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.device-screen {
    max-height: 80vh;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

#loadingMessage {
    margin-top: 15px;
    font-weight: 500;
    color: #333;
}

.action-log {
    height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    background-color: #f8f9fa;
}

/* Additional styles for action editing and visual feedback */

/* Action buttons */
.action-buttons {
    display: flex;
    align-items: center;
}

.action-buttons .btn {
    margin-right: 0.25rem;
}

/* Visual rolling logs overlay */
.rolling-log-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    max-height: 150px;
    overflow-y: auto;
    padding: 8px 12px;
    font-family: monospace;
    font-size: 12px;
    z-index: 100;
    backdrop-filter: blur(2px);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.rolling-log-overlay.hidden {
    transform: translateY(100%);
}

.log-message {
    margin-bottom: 4px;
    padding: 2px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    word-wrap: break-word;
}

.log-message.info {
    color: #8cc;
}

.log-message.success {
    color: #8c8;
}

.log-message.warning {
    color: #fc8;
}

.log-message.error {
    color: #f88;
}

/* Status indicators during playback */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.pending {
    background-color: #ffcc00;
    box-shadow: 0 0 5px #ffcc00;
}

.status-indicator.success {
    background-color: #44cc44;
    box-shadow: 0 0 5px #44cc44;
}

.status-indicator.error {
    background-color: #ff5555;
    box-shadow: 0 0 5px #ff5555;
}

.status-indicator.running {
    background-color: #44aaff;
    box-shadow: 0 0 5px #44aaff;
    animation: pulsate 1s infinite;
}

@keyframes pulsate {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Highlight current action in the list during playback */
.list-group-item.current-action {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Insert action separator */
.insert-separator {
    position: relative;
    height: 3px;
    background-color: #007bff;
    margin: 0;
    animation: pulse 2s infinite;
    border-radius: 3px;
    z-index: 10;
}

.insert-separator::before {
    content: "Insert Here";
    position: absolute;
    top: -20px;
    left: 10px;
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 10px;
    z-index: 11;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
        box-shadow: 0 0 2px #007bff;
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 8px #007bff;
    }
    100% {
        opacity: 0.6;
        box-shadow: 0 0 2px #007bff;
    }
}

/* Dropdown styling for insert options */
.action-buttons .dropdown-toggle::after {
    display: none; /* Hide default caret */
}

.action-buttons .dropdown-menu {
    min-width: 8rem;
    padding: 0.25rem 0;
    font-size: 0.875rem;
}

.action-buttons .dropdown-item {
    padding: 0.25rem 0.75rem;
}

.action-buttons .dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Assertion region preview */
.assert-region-preview {
    position: absolute;
    border: 2px dashed rgba(255, 165, 0, 0.9);
    background-color: rgba(255, 165, 0, 0.2);
    pointer-events: none;
    z-index: 25;
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

/* Assertion status indicators */
.assertion-success {
    position: absolute;
    background-color: rgba(0, 255, 0, 0.5);
    border: 2px solid rgba(0, 128, 0, 0.8);
    animation: flash-success 1.5s;
    z-index: 20;
}

.assertion-failure {
    position: absolute;
    background-color: rgba(255, 0, 0, 0.5);
    border: 2px solid rgba(128, 0, 0, 0.8);
    animation: flash-failure 1.5s;
    z-index: 20;
}

@keyframes flash-success {
    0% { opacity: 0; }
    25% { opacity: 0.8; }
    75% { opacity: 0.8; }
    100% { opacity: 0; }
}

@keyframes flash-failure {
    0% { opacity: 0; }
    25% { opacity: 0.8; }
    50% { opacity: 0.4; }
    75% { opacity: 0.8; }
    100% { opacity: 0; }
}

/* Action Items and Drag-and-Drop Styles */
.sortable-list {
    margin-top: 10px;
}

.sortable-item {
    cursor: move;
    transition: background-color 0.2s, transform 0.2s, box-shadow 0.2s;
    margin-bottom: 5px;
    border-radius: 4px;
    position: relative;
}

.sortable-item:hover {
    background-color: #f8f9fa;
}

.sortable-item.dragging {
    opacity: 0.4;
    z-index: 999;
}

.sortable-item.drag-over {
    border-top: 2px solid #007bff;
    transform: translateY(2px);
}

.drag-handle {
    cursor: grab;
    color: #6c757d;
    padding: 0 8px;
    border-right: 1px solid #dee2e6;
    margin-right: 10px;
}

.drag-handle i {
    font-size: 1.2rem;
}

.sortable-item.active-action {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Add this to your existing styles to enhance the action buttons */
.action-buttons .btn {
    box-shadow: none;
    transition: all 0.2s;
}

.action-buttons .btn:hover {
    transform: scale(1.1);
}

.action-content {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Animation for drag and drop hints */
@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.sortable-item.drag-hint {
    animation: pulse-border 2s infinite;
}

/* Action editing styles */
.action-item.editing {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    position: relative;
}

.action-item.editing::after {
    content: "Editing";
    position: absolute;
    top: 0;
    right: 5px;
    background-color: #ffc107;
    color: #212529;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 0 0 4px 4px;
    font-weight: bold;
    z-index: 5;
}

.edit-mode-indicator {
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

#addAction.btn-warning {
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 8px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* General styles */
body {
    background-color: #f4f6f9;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
    max-width: 1200px;
}

/* Connect device card */
.device-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.device-status {
    font-weight: bold;
    margin-bottom: 15px;
}

/* Action buttons */
.action-btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.connect-btn {
    background-color: #28a745;
    border-color: #28a745;
}

.disconnect-btn {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Test case list */
.test-case-list {
    max-height: 400px;
    overflow-y: auto;
}

.test-case-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.test-case-item:hover {
    background-color: #f0f0f0;
}

.test-case-item.active {
    background-color: #e2f0ff;
    border-left: 3px solid #007bff;
}

/* Screenshot area */
.screenshot-container {
    position: relative;
    overflow: hidden;
    border: 1px solid #ddd;
    background-color: #eee;
    text-align: center;
}

.screenshot-img {
    max-width: 100%;
    max-height: 500px;
}

/* Appium Inspector */
.inspector-container {
    display: flex;
    flex-direction: row;
    height: calc(100vh - 120px);
}

.elements-panel {
    flex: 0 0 300px;
    overflow-y: auto;
    border-right: 1px solid #ddd;
    padding: 10px;
}

.screen-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.element-details {
    flex: 0 0 200px;
    overflow-y: auto;
    border-top: 1px solid #ddd;
    padding: 10px;
    margin-top: 10px;
}

/* Spinner for loading states */
.spinner {
    width: 40px;
    height: 40px;
    margin: 20px auto;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

#imageSelectionOverlay {
    cursor: crosshair;
    z-index: 1000;
}

.image-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 10px;
    margin-bottom: 15px;
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
}

#selectedImagePreview {
    max-width: 100%;
    max-height: 150px;
}

.capture-image-btn {
    margin-bottom: 15px;
}

/* Make sure the device screen container has position relative for overlay positioning */
#deviceScreenContainer {
    position: relative;
}

/* Action item status styles */
.action-item {
    position: relative;
    transition: all 0.3s ease;
}

.action-item.executing {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.action-item.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.action-item.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

.action-loading {
    display: none;
}

.action-status {
    display: flex;
    align-items: center;
}

.action-status i {
    font-size: 12px;
}

.action-item.executing .action-status i {
    color: #ffc107;
}

.action-item.success .action-status i {
    color: #28a745;
}

.action-item.error .action-status i {
    color: #dc3545;
}

/* Spinner animation */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.2em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border .75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
} 