# Appium Environment Setup

This project uses the Python Appium client together with a standalone Appium server.
Follow these steps whenever you need to bootstrap a fresh automation workstation
or recover from a corrupted `node_modules` directory.

## Prerequisites

* Python virtual environment created via `python3 -m venv venv`
* Node.js ≥20.19.0 available (we recommend `~/.nvm/versions/node/v20.19.5`)

## One-time bootstrap

```bash
# 1. Activate Python dependencies
source venv/bin/activate
pip install -r requirements.txt

# 2. Expose the Node runtime that provides Appium
export NODE_BIN_DIR=$HOME/.nvm/versions/node/v20.19.5/bin
export PATH=$NODE_BIN_DIR:$PATH

# 3. Provision Appium server, drivers, and plugins
scripts/setup_appium_environment.sh
```

The script installs the Appium Inspector plugin by default and applies a
compatibility patch so it works with Appium 3.x. To adjust the plugin list,
override `APPIUM_PLUGINS` before running the script (set it to `""` to disable
plugin installation entirely).

By default the script installs `appium@3.0.2` together with
`appium-uiautomator2-driver@4.2.9` and `appium-xcuitest-driver@10.1.2`
into `./.appium-python`. Override `APPIUM_VERSION`, `APPIUM_DRIVERS`, or
`APPIUM_HOME` before invoking the script if you need different versions.

## Starting the local applications

Every time you open a new terminal:

```bash
source venv/bin/activate
export NODE_BIN_DIR=$HOME/.nvm/versions/node/v20.19.5/bin
export PATH=$NODE_BIN_DIR:$PATH
export APPIUM_HOME=$PWD/.appium-python

# Android interface
python run_android.py

# iOS interface
python run.py
```

The Inspector plugin is enabled automatically when present. To supply a custom
list, export `APPIUM_USE_PLUGINS="plugin-a,plugin-b"` before launching the
servers.

If a port is already in use, pass a different `--port` (`--flask-port` for `run.py`).

## Secure build considerations

The setup script only touches the local `./.appium-python` directory and installs
Appium globally through the Node runtime you provide. It does **not** modify the
application code or embedded secure build assets, so it is safe to run prior to
creating a secure build artifact.
