# Build Process

This document describes how to build, package, and verify the Mobile App Automation Platform.

## Prerequisites
- Python 3.8+
- Node.js and npm
- macOS/iOS: Xcode + CLT, libimobiledevice, tidevice
- Android: ADB in PATH

## One-time setup

```bash
./setup.sh
```

Notes:
- The script installs Python deps, Node deps, Appium drivers, and attempts to install Playwright Chromium (`python -m playwright install chromium`).
- If Playwright browser download fails (network restrictions, disk, permissions), the app falls back to system browser.

## Development build (from source)

```bash
# Start backend from source
python3 secure_distribution_app/web_server/app.py

# Run the GUI (PyQt)
./venv/bin/python gui_app/qt_gui.py
```

## PyInstaller builds (secure distribution)

1. Ensure your venv has all dependencies installed.
2. Run the build script you use (examples):
   - `python secure_distribution_app/build_final_secure_app.py`
   - or `pyinstaller secure_distribution_app/gui_app.spec`
3. Validate output in `secure_distribution_app/dist`.

### Including Playwright browsers in builds
- <PERSON><PERSON> downloads browsers at runtime into the user cache. Our runtime logic:
  - Tries Playwright Chromium. If missing, falls back to system browser.
  - You may pre-install Chromium in the build environment using:
    ```bash
    ./venv/bin/python -m playwright install chromium
    ```

## Updating dependencies and rebuilding
- Update `requirements.txt` as needed
- Re-run `./setup.sh`
- Rebuild with the desired script/spec

## Testing builds
- Launch backend from the packaged app.
- Validate endpoints:
  - `/health` returns 200
  - `/api/ports/active` returns 200 after session
- Launch GUI and perform Android/iOS launch.

## Platform specifics
- macOS notarization/signing may be required for distribution.
- Windows Defender/SmartScreen: add signing or provide instructions to unblock.
- Linux: ensure `xdg-open` is available for system browser fallback.

