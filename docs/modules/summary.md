# Modules Summary

This page provides a summary of all modules in the MobileAppAutomation application.

## app

### environments.json

---

### __init__.py

---

### app.py

**Functions:**

- `shutdown_handler()`
- `cleanup_screenshots()`
- `cleanup_app_screenshots()`
- `sigusr1_handler(signum, frame)`
- `initialize_report_directory()`
- `delete_all_screenshots()`
- `save_action_logs()`
- `initialize_report_directory()`
- `load_config()`
- `save_config(config)`
- `encode_image_to_base64(image_path)`
- `index()`
- `test_suites()`
- `get_screenshot()`
- `serve_screenshot(filename)`
- `get_reference_images()`
- `get_reference_image_preview()`
- `get_devices()`
- `connect_device()`
- `disconnect_device()`
- `check_inspector()`
- `get_device_dimensions()`
- `take_screenshot()`
- `capture_image()`
- `tap_action()`
- `swipe_action()`
- `text_action()`
- `key_action()`
- `start_recording()`
- `stop_recording()`
- `save_recording()`
- `list_recordings()`
- `get_element_at_position_legacy()`
- `get_element_at_position()`
- `stop_execution()`
- `execute_test_case()`
- `execute_single_action()`
- `execute_hook_action()`
- `get_page_source()`
- `get_session_info()`
- `list_test_case_files()`
- `load_test_case_file(filename)`
- `get_session_info_route()`
- `inspect_element()`
- `load_specific_test_case(filename)`
- `get_test_cases_for_multi_step()`
- `check_device_health()`
- `reconnect_device()`
- `uninstall_app()`
- `fix_emulator_issues()`
- `rename_recording()`
- `duplicate_recording()`
- `swipe_till_visible_action()`
- `list_test_suites()`
- `get_test_suite(suite_id)`
- `create_test_suite()`
- `update_test_suite_route(suite_id)`
- `duplicate_test_suite(suite_id)`
- `delete_test_suite(suite_id)`
- `delete_test_case_route(filename)`
- `run_test_suite(suite_id)`
- `get_latest_report()`
- `get_reports_list()`
- `download_report(filename)`
- `download_report_zip(filename)`
- `regenerate_report(report_id)`
- `clear_screenshots_route()`
- `reset_database_route()`
- `delete_report(filename)`
- `get_test_data(suite_id)`
- `generate_report_from_suite_id(suite_id)`
- `generate_report()`
- `serve_report(filename)`
- `upload_media()`
- `get_settings()`
- `update_settings()`
- `capture_image_area()`
- `set_clipboard()`
- `paste_clipboard()`
- `detect_text()`
- `emit(self, event, data)`
- `__init__(self, timeout, step_name, test_case_name)`
- `start(self)`
- `cancel(self)`
- `__init__(self, initial_value)`
- `__str__(self)`
- `__repr__(self)`
- `timeout_handler()`
- `update_directory_path(content, dir_key, dir_path)`

**Classes:**

- `DummySocketIO`
  - `emit(self, event, data)`
- `StepExecutionTimeout`
  - `__init__(self, timeout, step_name, test_case_name)`
  - `start(self)`
  - `cancel(self)`
- `IndexHolder`
  - `__init__(self, initial_value)`
  - `__str__(self)`
  - `__repr__(self)`

---

### test_suites_manager.py

**Functions:**

- `__init__(self)`
- `load_test_suites(self)`
- `create_test_suite(self, name, description, test_cases)`
- `update_test_suite(self, suite_id, updated_data)`
- `duplicate_test_suite(self, suite_id)`
- `delete_test_suite(self, suite_id)`

**Classes:**

- `TestSuitesManager`
  - `__init__(self)`
  - `load_test_suites(self)`
  - `create_test_suite(self, name, description, test_cases)`
  - `update_test_suite(self, suite_id, updated_data)`
  - `duplicate_test_suite(self, suite_id)`
  - `delete_test_suite(self, suite_id)`

---

## app/utils

### id_generator.py

**Functions:**

- `generate_action_id(length)`
- `add_action_ids_to_test_case(test_case_data)`

---

### text_detector.py

**Functions:**

- `detect_text_with_tesseract(image_path, text_to_find, output_dir, similarity_threshold)`
- `get_text_coordinates(image_path, text_to_find, device_width, device_height, output_dir, similarity_threshold)`
- `__init__(self)`
- `find_text(self, image_path, text_to_find, similarity_threshold)`
- `_calculate_similarity(self, text1, text2)`
- `_find_partial_matches(self, data, text_to_find, similarity_threshold)`

**Classes:**

- `TextDetector`
  - `__init__(self)`
  - `find_text(self, image_path, text_to_find, similarity_threshold)`
  - `_calculate_similarity(self, text1, text2)`
  - `_find_partial_matches(self, data, text_to_find, similarity_threshold)`

---

### database_update.py

**Functions:**

- `update_database_schema()`

---

### screenshot_handler.py

**Functions:**

- `ensure_directory_exists(directory_path)`
- `copy_screenshots_to_report(report_dir)`
- `copy_latest_screenshot_to_report(report_dir, action_id)`

---

### text_detection.py

**Functions:**

- `detect_text_in_image(image_path, text_to_find, output_dir)`
- `scale_coordinates(x, y, img_width, img_height, device_width, device_height)`

---

### reportGenerator.py

**Functions:**

- `getLatestReportUrl()`
- `ensure_report_directories(reports_dir, report_timestamp)`
- `copy_screenshots_for_report(report_timestamp, test_data)`
- `save_action_logs_to_file(logs, report_dir)`
- `regenerate_report_from_data_json(report_dir)`
- `create_zip_archive(report_dir)`
- `prepare_steps_for_report(test_suite_data)`
- `generateReport(test_suite_data, test_case_file)`
- `copy_screenshots_from_database(report_dir, test_data)`
- `ensure_report_screenshots(report_dir, test_data)`
- `get_image_number(filename)`

---

### coordinate_validator.py

**Functions:**

- `validate_coordinates(coords, device_width, device_height)`

---

### openReport.js

**Functions:**

- `openLatestReport()`
- `openReport(reportName)`
- `openUrlInBrowser(url)`

---

### test_case_manager.py

**Functions:**

- `__init__(self, test_cases_dir)`
- `get_test_cases(self)`
- `load_test_case(self, filename)`
- `save_test_case(self, test_case_data, filename, is_save_as)`
- `delete_test_case(self, filename)`
- `rename_test_case(self, filename, new_name)`
- `duplicate_test_case(self, filename)`
- `clean_duplicates(self)`

**Classes:**

- `TestCaseManager`
  - `__init__(self, test_cases_dir)`
  - `get_test_cases(self)`
  - `load_test_case(self, filename)`
  - `save_test_case(self, test_case_data, filename, is_save_as)`
  - `delete_test_case(self, filename)`
  - `rename_test_case(self, filename, new_name)`
  - `duplicate_test_case(self, filename)`
  - `clean_duplicates(self)`

---

### database.py

**Functions:**

- `ensure_db_directory()`
- `init_db()`
- `update_execution_tracking_schema()`
- `clear_test_tables()`
- `track_test_execution(suite_id, test_idx, filename, status, retry_count, max_retries, error, in_progress, step_idx, action_type, action_params, action_id)`
- `clear_execution_tracking()`
- `get_screenshots_for_suite(suite_id)`
- `get_test_steps_for_suite(suite_id)`
- `get_execution_tracking_for_suite(suite_id)`
- `get_action_id_for_step(suite_id, test_idx, step_idx)`
- `get_test_execution_status(suite_id, test_idx, filename)`
- `reset_test_case_execution_tracking(suite_id, test_idx)`
- `has_test_case_failures(suite_id, test_idx, force_return_true)`
- `get_hook_execution_count(suite_id, test_idx, step_idx, action_type)`
- `clear_database()`
- `save_test_suite(suite_data)`
- `get_test_suite(suite_id)`
- `get_latest_test_suite()`
- `save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id)`
- `update_test_suite(suite_id, test_suite_data)`
- `get_all_test_suites()`
- `delete_report_by_name(report_name)`
- `delete_report_by_dir(dir_name)`
- `reset_database()`
- `check_database_state()`
- `get_test_case_by_id(test_case_id)`
- `save_screenshot_info(suite_id, test_idx, step_idx, filename, path, retry_number, action_id)`
- `check_screenshot_exists(action_id)`
- `update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id)`
- `update_action_id(suite_id, test_idx, step_idx, action_id)`
- `clear_screenshots()`

---

### reset_database.py

**Functions:**

- `reset_database()`
- `create_new_database()`
- `check_database()`

---

### appium_device_controller.py

**Functions:**

- `__init__(self)`
- `test_airtest_connection(self, device_id, platform_name)`
- `_kill_existing_processes(self)`
- `check_appium_server(self)`
- `restart_appium_server(self)`
- `_ensure_appium_server(self)`
- `get_devices(self)`
- `connect_to_device(self, device_id, options, platform)`
- `_attempt_device_connection(self, device_id, options, platform)`
- `_connect_with_timeout(self)`
- `_init_platform_helpers(self)`
- `_is_port_in_use(self, port)`
- `_start_iproxy(self, device_id, local_port, device_port)`
- `_connect_ios_device(self, options)`
- `_connect_with_airtest(self, device_id, wda_url)`
- `reconnect_device(self)`
- `disconnect(self)`
- `shutdown(self)`
- `_init_airtest(self, device_id)`
- `_init_minimal_ios_device(self)`
- `_ensure_airtest_connected(self)`
- `take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)`
- `find_element_at_position(self, x, y)`
- `get_element_at_position(self, x, y)`
- `input_text(self, locator_type, locator_value, text, clear_first, timeout)`
- `tap(self, x, y, duration)`
- `tap_element(self, locator_type, locator_value, timeout, interval)`
- `tap_on_image(self, image_path, timeout, confidence)`
- `tap_on_text(self, text, timeout, fuzzy)`
- `double_tap(self, x, y)`
- `swipe(self, start_x, start_y, end_x, end_y, duration)`
- `long_press(self, x, y, duration)`
- `press_button(self, button_name)`
- `launch_app(self, bundle_id)`
- `terminate_app(self, bundle_id)`
- `uninstall_app(self, package_id)`
- `_uninstall_app_ios_direct(self, bundle_id)`
- `session_id(self)`
- `set_clipboard(self, content)`
- `paste_clipboard(self)`
- `capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)`
- `find_element_with_fallback(self, locators, timeout)`
- `find_element(self, locator_type, locator_value, timeout)`
- `find_image(self, image_path, threshold, timeout)`
- `get_screen_text(self)`
- `get_screen_content(self)`
- `_run_adb_command(self, command, device_id)`
- `recover_connection(self)`
- `_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)`
- `is_session_active(self)`
- `get_driver(self)`
- `get_device_dimensions(self)`
- `__init__(self, driver)`
- `tap(self, x, y, element, count)`
- `press(self, x, y, element, pressure)`
- `long_press(self, x, y, element, duration)`
- `wait(self, ms)`
- `move_to(self, x, y, element)`
- `release(self)`
- `perform(self)`
- `get_page_source()`

**Classes:**

- `AppiumDeviceController`
  - `__init__(self)`
  - `test_airtest_connection(self, device_id, platform_name)`
  - `_kill_existing_processes(self)`
  - `check_appium_server(self)`
  - `restart_appium_server(self)`
  - `_ensure_appium_server(self)`
  - `get_devices(self)`
  - `connect_to_device(self, device_id, options, platform)`
  - `_attempt_device_connection(self, device_id, options, platform)`
  - `_connect_with_timeout(self)`
  - `_init_platform_helpers(self)`
  - `_is_port_in_use(self, port)`
  - `_start_iproxy(self, device_id, local_port, device_port)`
  - `_connect_ios_device(self, options)`
  - `_connect_with_airtest(self, device_id, wda_url)`
  - `reconnect_device(self)`
  - `disconnect(self)`
  - `shutdown(self)`
  - `_init_airtest(self, device_id)`
  - `_init_minimal_ios_device(self)`
  - `_ensure_airtest_connected(self)`
  - `take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)`
  - `find_element_at_position(self, x, y)`
  - `get_element_at_position(self, x, y)`
  - `input_text(self, locator_type, locator_value, text, clear_first, timeout)`
  - `tap(self, x, y, duration)`
  - `tap_element(self, locator_type, locator_value, timeout, interval)`
  - `tap_on_image(self, image_path, timeout, confidence)`
  - `tap_on_text(self, text, timeout, fuzzy)`
  - `double_tap(self, x, y)`
  - `swipe(self, start_x, start_y, end_x, end_y, duration)`
  - `long_press(self, x, y, duration)`
  - `press_button(self, button_name)`
  - `launch_app(self, bundle_id)`
  - `terminate_app(self, bundle_id)`
  - `uninstall_app(self, package_id)`
  - `_uninstall_app_ios_direct(self, bundle_id)`
  - `session_id(self)`
  - `set_clipboard(self, content)`
  - `paste_clipboard(self)`
  - `capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)`
  - `find_element_with_fallback(self, locators, timeout)`
  - `find_element(self, locator_type, locator_value, timeout)`
  - `find_image(self, image_path, threshold, timeout)`
  - `get_screen_text(self)`
  - `get_screen_content(self)`
  - `_run_adb_command(self, command, device_id)`
  - `recover_connection(self)`
  - `_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)`
  - `is_session_active(self)`
  - `get_driver(self)`
  - `get_device_dimensions(self)`
- `TouchAction`
  - `__init__(self, driver)`
  - `tap(self, x, y, element, count)`
  - `press(self, x, y, element, pressure)`
  - `long_press(self, x, y, element, duration)`
  - `wait(self, ms)`
  - `move_to(self, x, y, element)`
  - `release(self)`
  - `perform(self)`

---

### ios_device.py

**Functions:**

- `__init__(self, device_id, wda_url)`
- `_get_screen_dimensions(self)`
- `touch(self, x, y, duration)`
- `double_click(self, x, y)`
- `swipe(self, fx, fy, tx, ty, duration)`
- `snapshot(self, filename, save_debug)`
- `home(self)`
- `press(self, key)`
- `alert_accept(self)`
- `alert_dismiss(self)`
- `alert_click(self, button_text)`
- `alert_wait(self, timeout)`
- `alert_exists(self)`
- `alert_buttons(self)`
- `get_clipboard(self, wda_bundle_id)`
- `set_clipboard(self, content, wda_bundle_id)`
- `text(self, text, enter)`
- `_press_return_key(self)`
- `paste(self, wda_bundle_id)`
- `get_ip_address(self)`
- `device_status(self)`
- `get_current_resolution(self)`
- `get_ip_address(self)`
- `push(self, local_path, remote_path)`
- `uninstall_app(self, bundle_id)`
- `clear_app(self, bundle_id)`
- `lock(self)`
- `unlock(self)`
- `is_locked(self)`
- `list_app(self)`
- `find_template(self, template_path, threshold, timeout, interval)`

**Classes:**

- `MinimalIOSDevice`
  - `__init__(self, device_id, wda_url)`
  - `_get_screen_dimensions(self)`
  - `touch(self, x, y, duration)`
  - `double_click(self, x, y)`
  - `swipe(self, fx, fy, tx, ty, duration)`
  - `snapshot(self, filename, save_debug)`
  - `home(self)`
  - `press(self, key)`
  - `alert_accept(self)`
  - `alert_dismiss(self)`
  - `alert_click(self, button_text)`
  - `alert_wait(self, timeout)`
  - `alert_exists(self)`
  - `alert_buttons(self)`
  - `get_clipboard(self, wda_bundle_id)`
  - `set_clipboard(self, content, wda_bundle_id)`
  - `text(self, text, enter)`
  - `_press_return_key(self)`
  - `paste(self, wda_bundle_id)`
  - `get_ip_address(self)`
  - `device_status(self)`
  - `get_current_resolution(self)`
  - `get_ip_address(self)`
  - `push(self, local_path, remote_path)`
  - `uninstall_app(self, bundle_id)`
  - `clear_app(self, bundle_id)`
  - `lock(self)`
  - `unlock(self)`
  - `is_locked(self)`
  - `list_app(self)`
  - `find_template(self, template_path, threshold, timeout, interval)`

---

### recorder.py

**Functions:**

- `__init__(self, device_controller)`
- `start_recording(self)`
- `stop_recording(self)`
- `add_action(self, action_data)`
- `_process_elements_in_background(self, screenshot_path, action_index)`
- `_detect_changes_fast(self, prev_screenshot, curr_screenshot)`
- `_detect_elements(self, screenshot_path)`
- `_detect_text_elements(self, screenshot_path)`
- `_detect_ui_elements_with_opencv(self, img)`
- `_generate_simple_code(self, action)`
- `generate_code(self, actions)`
- `save_recording(self, name)`
- `load_recording(self, filepath)`

**Classes:**

- `Recorder`
  - `__init__(self, device_controller)`
  - `start_recording(self)`
  - `stop_recording(self)`
  - `add_action(self, action_data)`
  - `_process_elements_in_background(self, screenshot_path, action_index)`
  - `_detect_changes_fast(self, prev_screenshot, curr_screenshot)`
  - `_detect_elements(self, screenshot_path)`
  - `_detect_text_elements(self, screenshot_path)`
  - `_detect_ui_elements_with_opencv(self, img)`
  - `_generate_simple_code(self, action)`
  - `generate_code(self, actions)`
  - `save_recording(self, name)`
  - `load_recording(self, filepath)`

---

### __init__.py

---

### reportGenerator.js

**Functions:**

- `generateReport(testSuite)`
- `getTimestamp()`
- `generateHtmlContent(testSuite, timestamp)`
- `calculateStatistics(testSuite)`
- `determineStatus(steps)`
- `formatTimestamp(timestamp)`
- `saveScreenshot(stepId, screenshotData)`
- `getLatestReportUrl()`

---

### test.json

---

### image_matcher.py

**Functions:**

- `__init__(self, device_id, max_workers, platform)`
- `set_device_id(self, device_id)`
- `set_platform(self, platform)`
- `set_airtest_device(self, airtest_device)`
- `take_screenshot_with_adb(self)`
- `take_screenshot_ios(self, screenshot_path)`
- `take_screenshot_android(self, screenshot_path)`
- `find_template(self, template_path, screenshot_path, threshold, debug)`
- `wait_for_template(self, template_path, timeout, interval, threshold)`
- `process_screenshot(screenshot_path)`

**Classes:**

- `ImageMatcher`
  - `__init__(self, device_id, max_workers, platform)`
  - `set_device_id(self, device_id)`
  - `set_platform(self, platform)`
  - `set_airtest_device(self, airtest_device)`
  - `take_screenshot_with_adb(self)`
  - `take_screenshot_ios(self, screenshot_path)`
  - `take_screenshot_android(self, screenshot_path)`
  - `find_template(self, template_path, screenshot_path, threshold, debug)`
  - `wait_for_template(self, template_path, timeout, interval, threshold)`

---

### parameter_utils.py

**Functions:**

- `substitute_parameters(text, log_substitution)`

---

### player.py

**Functions:**

- `__init__(self, device_controller, socketio, test_idx)`
- `load_recording(self, file_path)`
- `play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)`
- `_verify_device_connection(self)`
- `_recover_device_connection(self)`
- `stop(self)`
- `_find_hook_action(self)`
- `_convert_hook_to_action(self, hook_action)`
- `_execute_hook_action(self, hook_action)`
- `execute_action(self, action)`
- `play_with_validation(self, actions)`
- `_validate_visual_state(self, action)`
- `execute_all_actions(self, actions)`
- `stop_execution(self)`
- `_execute_tap(self, x, y)`
- `_execute_swipe(self, start_x, start_y, end_x, end_y, duration)`
- `_execute_text(self, text)`
- `_execute_key(self, key_code)`
- `_execute_wait(self, duration)`
- `_execute_launch_app(self, package)`
- `_execute_terminate_app(self, package)`
- `_execute_restart_app(self, package)`
- `_execute_wait_till(self, image, timeout, interval)`
- `_execute_exists_image(self, image)`
- `_execute_exists_element(self, locator_type, locator_value, timeout)`
- `_execute_double_click(self, x, y)`
- `_fallback_double_tap(self, x, y)`
- `_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)`
- `_execute_text_clear(self, text, delay)`
- `_execute_click_element(self, locator_type, locator_value, timeout)`
- `_execute_double_click_image(self, image_path, threshold, timeout)`
- `_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)`
- `_execute_click_image(self, image_path, threshold, timeout)`
- `_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)`
- `_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)`
- `_execute_swipe_till_visible(self, action)`
- `_is_element_visible(self, locator_type, locator_value)`
- `scale_ios_coordinates(self, coordinates)`

**Classes:**

- `Player`
  - `__init__(self, device_controller, socketio, test_idx)`
  - `load_recording(self, file_path)`
  - `play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)`
  - `_verify_device_connection(self)`
  - `_recover_device_connection(self)`
  - `stop(self)`
  - `_find_hook_action(self)`
  - `_convert_hook_to_action(self, hook_action)`
  - `_execute_hook_action(self, hook_action)`
  - `execute_action(self, action)`
  - `play_with_validation(self, actions)`
  - `_validate_visual_state(self, action)`
  - `execute_all_actions(self, actions)`
  - `stop_execution(self)`
  - `_execute_tap(self, x, y)`
  - `_execute_swipe(self, start_x, start_y, end_x, end_y, duration)`
  - `_execute_text(self, text)`
  - `_execute_key(self, key_code)`
  - `_execute_wait(self, duration)`
  - `_execute_launch_app(self, package)`
  - `_execute_terminate_app(self, package)`
  - `_execute_restart_app(self, package)`
  - `_execute_wait_till(self, image, timeout, interval)`
  - `_execute_exists_image(self, image)`
  - `_execute_exists_element(self, locator_type, locator_value, timeout)`
  - `_execute_double_click(self, x, y)`
  - `_fallback_double_tap(self, x, y)`
  - `_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)`
  - `_execute_text_clear(self, text, delay)`
  - `_execute_click_element(self, locator_type, locator_value, timeout)`
  - `_execute_double_click_image(self, image_path, threshold, timeout)`
  - `_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)`
  - `_execute_click_image(self, image_path, threshold, timeout)`
  - `_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)`
  - `_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)`
  - `_execute_swipe_till_visible(self, action)`
  - `_is_element_visible(self, locator_type, locator_value)`
  - `scale_ios_coordinates(self, coordinates)`

---

### global_values_db.py

**Functions:**

- `__init__(self, db_path)`
- `_init_db(self)`
- `_init_default_values(self)`
- `get_value(self, name, default)`
- `set_value(self, name, value)`
- `delete_value(self, name)`
- `get_all_values(self)`
- `save_values(self, global_values)`

**Classes:**

- `GlobalValuesDB`
  - `__init__(self, db_path)`
  - `_init_db(self)`
  - `_init_default_values(self)`
  - `get_value(self, name, default)`
  - `set_value(self, name, value)`
  - `delete_value(self, name)`
  - `get_all_values(self)`
  - `save_values(self, global_values)`

---

### file_utils.py

**Functions:**

- `save_json(data, relative_path)`
- `load_json(relative_path)`
- `ensure_directory(relative_path)`
- `ensure_dir_exists(directory)`
- `get_timestamp()`
- `save_to_file(content, filepath)`
- `read_from_file(filepath)`
- `compare_screenshots(screenshot1, screenshot2, threshold)`
- `base64_to_image(base64_str)`
- `image_to_base64(image)`

---

### random_data_generator.py

**Functions:**

- `get_generator_options()`
- `generate_data(generator_id)`

---

### screenshot_manager.py

**Functions:**

- `__init__(self)`
- `initialize(self, report_dir, timestamp)`
- `save_screenshot(self, screenshot_data, filename, action_id, device_controller)`

**Classes:**

- `ScreenshotManager`
  - `__init__(self)`
  - `initialize(self, report_dir, timestamp)`
  - `save_screenshot(self, screenshot_data, filename, action_id, device_controller)`

---

### build_data_json.py

**Functions:**

- `build_data_json_from_test_case(test_case_file, suite_id, execution_data, success, error)`

---

### id_generator.py

**Functions:**

- `generate_action_id(length)`
- `add_action_ids_to_test_case(test_case_data)`

---

### text_detector.py

**Functions:**

- `detect_text_with_tesseract(image_path, text_to_find, output_dir, similarity_threshold)`
- `get_text_coordinates(image_path, text_to_find, device_width, device_height, output_dir, similarity_threshold)`
- `__init__(self)`
- `find_text(self, image_path, text_to_find, similarity_threshold)`
- `_calculate_similarity(self, text1, text2)`
- `_find_partial_matches(self, data, text_to_find, similarity_threshold)`

**Classes:**

- `TextDetector`
  - `__init__(self)`
  - `find_text(self, image_path, text_to_find, similarity_threshold)`
  - `_calculate_similarity(self, text1, text2)`
  - `_find_partial_matches(self, data, text_to_find, similarity_threshold)`

---

### database_update.py

**Functions:**

- `update_database_schema()`

---

### screenshot_handler.py

**Functions:**

- `ensure_directory_exists(directory_path)`
- `copy_screenshots_to_report(report_dir)`
- `copy_latest_screenshot_to_report(report_dir, action_id)`

---

### text_detection.py

**Functions:**

- `detect_text_in_image(image_path, text_to_find, output_dir)`
- `scale_coordinates(x, y, img_width, img_height, device_width, device_height)`

---

### reportGenerator.py

**Functions:**

- `getLatestReportUrl()`
- `ensure_report_directories(reports_dir, report_timestamp)`
- `copy_screenshots_for_report(report_timestamp, test_data)`
- `save_action_logs_to_file(logs, report_dir)`
- `regenerate_report_from_data_json(report_dir)`
- `create_zip_archive(report_dir)`
- `prepare_steps_for_report(test_suite_data)`
- `generateReport(test_suite_data, test_case_file)`
- `copy_screenshots_from_database(report_dir, test_data)`
- `ensure_report_screenshots(report_dir, test_data)`
- `get_image_number(filename)`

---

### coordinate_validator.py

**Functions:**

- `validate_coordinates(coords, device_width, device_height)`

---

### openReport.js

**Functions:**

- `openLatestReport()`
- `openReport(reportName)`
- `openUrlInBrowser(url)`

---

### test_case_manager.py

**Functions:**

- `__init__(self, test_cases_dir)`
- `get_test_cases(self)`
- `load_test_case(self, filename)`
- `save_test_case(self, test_case_data, filename, is_save_as)`
- `delete_test_case(self, filename)`
- `rename_test_case(self, filename, new_name)`
- `duplicate_test_case(self, filename)`
- `clean_duplicates(self)`

**Classes:**

- `TestCaseManager`
  - `__init__(self, test_cases_dir)`
  - `get_test_cases(self)`
  - `load_test_case(self, filename)`
  - `save_test_case(self, test_case_data, filename, is_save_as)`
  - `delete_test_case(self, filename)`
  - `rename_test_case(self, filename, new_name)`
  - `duplicate_test_case(self, filename)`
  - `clean_duplicates(self)`

---

### database.py

**Functions:**

- `ensure_db_directory()`
- `init_db()`
- `update_execution_tracking_schema()`
- `clear_test_tables()`
- `track_test_execution(suite_id, test_idx, filename, status, retry_count, max_retries, error, in_progress, step_idx, action_type, action_params, action_id)`
- `clear_execution_tracking()`
- `get_screenshots_for_suite(suite_id)`
- `get_test_steps_for_suite(suite_id)`
- `get_execution_tracking_for_suite(suite_id)`
- `get_action_id_for_step(suite_id, test_idx, step_idx)`
- `get_test_execution_status(suite_id, test_idx, filename)`
- `reset_test_case_execution_tracking(suite_id, test_idx)`
- `has_test_case_failures(suite_id, test_idx, force_return_true)`
- `get_hook_execution_count(suite_id, test_idx, step_idx, action_type)`
- `clear_database()`
- `save_test_suite(suite_data)`
- `get_test_suite(suite_id)`
- `get_latest_test_suite()`
- `save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id)`
- `update_test_suite(suite_id, test_suite_data)`
- `get_all_test_suites()`
- `delete_report_by_name(report_name)`
- `delete_report_by_dir(dir_name)`
- `reset_database()`
- `check_database_state()`
- `get_test_case_by_id(test_case_id)`
- `save_screenshot_info(suite_id, test_idx, step_idx, filename, path, retry_number, action_id)`
- `check_screenshot_exists(action_id)`
- `update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id)`
- `update_action_id(suite_id, test_idx, step_idx, action_id)`
- `clear_screenshots()`

---

### reset_database.py

**Functions:**

- `reset_database()`
- `create_new_database()`
- `check_database()`

---

### appium_device_controller.py

**Functions:**

- `__init__(self)`
- `test_airtest_connection(self, device_id, platform_name)`
- `_kill_existing_processes(self)`
- `check_appium_server(self)`
- `restart_appium_server(self)`
- `_ensure_appium_server(self)`
- `get_devices(self)`
- `connect_to_device(self, device_id, options, platform)`
- `_attempt_device_connection(self, device_id, options, platform)`
- `_connect_with_timeout(self)`
- `_init_platform_helpers(self)`
- `_is_port_in_use(self, port)`
- `_start_iproxy(self, device_id, local_port, device_port)`
- `_connect_ios_device(self, options)`
- `_connect_with_airtest(self, device_id, wda_url)`
- `reconnect_device(self)`
- `disconnect(self)`
- `shutdown(self)`
- `_init_airtest(self, device_id)`
- `_init_minimal_ios_device(self)`
- `_ensure_airtest_connected(self)`
- `take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)`
- `find_element_at_position(self, x, y)`
- `get_element_at_position(self, x, y)`
- `input_text(self, locator_type, locator_value, text, clear_first, timeout)`
- `tap(self, x, y, duration)`
- `tap_element(self, locator_type, locator_value, timeout, interval)`
- `tap_on_image(self, image_path, timeout, confidence)`
- `tap_on_text(self, text, timeout, fuzzy)`
- `double_tap(self, x, y)`
- `swipe(self, start_x, start_y, end_x, end_y, duration)`
- `long_press(self, x, y, duration)`
- `press_button(self, button_name)`
- `launch_app(self, bundle_id)`
- `terminate_app(self, bundle_id)`
- `uninstall_app(self, package_id)`
- `_uninstall_app_ios_direct(self, bundle_id)`
- `session_id(self)`
- `set_clipboard(self, content)`
- `paste_clipboard(self)`
- `capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)`
- `find_element_with_fallback(self, locators, timeout)`
- `find_element(self, locator_type, locator_value, timeout)`
- `find_image(self, image_path, threshold, timeout)`
- `get_screen_text(self)`
- `get_screen_content(self)`
- `_run_adb_command(self, command, device_id)`
- `recover_connection(self)`
- `_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)`
- `is_session_active(self)`
- `get_driver(self)`
- `get_device_dimensions(self)`
- `__init__(self, driver)`
- `tap(self, x, y, element, count)`
- `press(self, x, y, element, pressure)`
- `long_press(self, x, y, element, duration)`
- `wait(self, ms)`
- `move_to(self, x, y, element)`
- `release(self)`
- `perform(self)`
- `get_page_source()`

**Classes:**

- `AppiumDeviceController`
  - `__init__(self)`
  - `test_airtest_connection(self, device_id, platform_name)`
  - `_kill_existing_processes(self)`
  - `check_appium_server(self)`
  - `restart_appium_server(self)`
  - `_ensure_appium_server(self)`
  - `get_devices(self)`
  - `connect_to_device(self, device_id, options, platform)`
  - `_attempt_device_connection(self, device_id, options, platform)`
  - `_connect_with_timeout(self)`
  - `_init_platform_helpers(self)`
  - `_is_port_in_use(self, port)`
  - `_start_iproxy(self, device_id, local_port, device_port)`
  - `_connect_ios_device(self, options)`
  - `_connect_with_airtest(self, device_id, wda_url)`
  - `reconnect_device(self)`
  - `disconnect(self)`
  - `shutdown(self)`
  - `_init_airtest(self, device_id)`
  - `_init_minimal_ios_device(self)`
  - `_ensure_airtest_connected(self)`
  - `take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)`
  - `find_element_at_position(self, x, y)`
  - `get_element_at_position(self, x, y)`
  - `input_text(self, locator_type, locator_value, text, clear_first, timeout)`
  - `tap(self, x, y, duration)`
  - `tap_element(self, locator_type, locator_value, timeout, interval)`
  - `tap_on_image(self, image_path, timeout, confidence)`
  - `tap_on_text(self, text, timeout, fuzzy)`
  - `double_tap(self, x, y)`
  - `swipe(self, start_x, start_y, end_x, end_y, duration)`
  - `long_press(self, x, y, duration)`
  - `press_button(self, button_name)`
  - `launch_app(self, bundle_id)`
  - `terminate_app(self, bundle_id)`
  - `uninstall_app(self, package_id)`
  - `_uninstall_app_ios_direct(self, bundle_id)`
  - `session_id(self)`
  - `set_clipboard(self, content)`
  - `paste_clipboard(self)`
  - `capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)`
  - `find_element_with_fallback(self, locators, timeout)`
  - `find_element(self, locator_type, locator_value, timeout)`
  - `find_image(self, image_path, threshold, timeout)`
  - `get_screen_text(self)`
  - `get_screen_content(self)`
  - `_run_adb_command(self, command, device_id)`
  - `recover_connection(self)`
  - `_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)`
  - `is_session_active(self)`
  - `get_driver(self)`
  - `get_device_dimensions(self)`
- `TouchAction`
  - `__init__(self, driver)`
  - `tap(self, x, y, element, count)`
  - `press(self, x, y, element, pressure)`
  - `long_press(self, x, y, element, duration)`
  - `wait(self, ms)`
  - `move_to(self, x, y, element)`
  - `release(self)`
  - `perform(self)`

---

### ios_device.py

**Functions:**

- `__init__(self, device_id, wda_url)`
- `_get_screen_dimensions(self)`
- `touch(self, x, y, duration)`
- `double_click(self, x, y)`
- `swipe(self, fx, fy, tx, ty, duration)`
- `snapshot(self, filename, save_debug)`
- `home(self)`
- `press(self, key)`
- `alert_accept(self)`
- `alert_dismiss(self)`
- `alert_click(self, button_text)`
- `alert_wait(self, timeout)`
- `alert_exists(self)`
- `alert_buttons(self)`
- `get_clipboard(self, wda_bundle_id)`
- `set_clipboard(self, content, wda_bundle_id)`
- `text(self, text, enter)`
- `_press_return_key(self)`
- `paste(self, wda_bundle_id)`
- `get_ip_address(self)`
- `device_status(self)`
- `get_current_resolution(self)`
- `get_ip_address(self)`
- `push(self, local_path, remote_path)`
- `uninstall_app(self, bundle_id)`
- `clear_app(self, bundle_id)`
- `lock(self)`
- `unlock(self)`
- `is_locked(self)`
- `list_app(self)`
- `find_template(self, template_path, threshold, timeout, interval)`

**Classes:**

- `MinimalIOSDevice`
  - `__init__(self, device_id, wda_url)`
  - `_get_screen_dimensions(self)`
  - `touch(self, x, y, duration)`
  - `double_click(self, x, y)`
  - `swipe(self, fx, fy, tx, ty, duration)`
  - `snapshot(self, filename, save_debug)`
  - `home(self)`
  - `press(self, key)`
  - `alert_accept(self)`
  - `alert_dismiss(self)`
  - `alert_click(self, button_text)`
  - `alert_wait(self, timeout)`
  - `alert_exists(self)`
  - `alert_buttons(self)`
  - `get_clipboard(self, wda_bundle_id)`
  - `set_clipboard(self, content, wda_bundle_id)`
  - `text(self, text, enter)`
  - `_press_return_key(self)`
  - `paste(self, wda_bundle_id)`
  - `get_ip_address(self)`
  - `device_status(self)`
  - `get_current_resolution(self)`
  - `get_ip_address(self)`
  - `push(self, local_path, remote_path)`
  - `uninstall_app(self, bundle_id)`
  - `clear_app(self, bundle_id)`
  - `lock(self)`
  - `unlock(self)`
  - `is_locked(self)`
  - `list_app(self)`
  - `find_template(self, template_path, threshold, timeout, interval)`

---

### recorder.py

**Functions:**

- `__init__(self, device_controller)`
- `start_recording(self)`
- `stop_recording(self)`
- `add_action(self, action_data)`
- `_process_elements_in_background(self, screenshot_path, action_index)`
- `_detect_changes_fast(self, prev_screenshot, curr_screenshot)`
- `_detect_elements(self, screenshot_path)`
- `_detect_text_elements(self, screenshot_path)`
- `_detect_ui_elements_with_opencv(self, img)`
- `_generate_simple_code(self, action)`
- `generate_code(self, actions)`
- `save_recording(self, name)`
- `load_recording(self, filepath)`

**Classes:**

- `Recorder`
  - `__init__(self, device_controller)`
  - `start_recording(self)`
  - `stop_recording(self)`
  - `add_action(self, action_data)`
  - `_process_elements_in_background(self, screenshot_path, action_index)`
  - `_detect_changes_fast(self, prev_screenshot, curr_screenshot)`
  - `_detect_elements(self, screenshot_path)`
  - `_detect_text_elements(self, screenshot_path)`
  - `_detect_ui_elements_with_opencv(self, img)`
  - `_generate_simple_code(self, action)`
  - `generate_code(self, actions)`
  - `save_recording(self, name)`
  - `load_recording(self, filepath)`

---

### __init__.py

---

### reportGenerator.js

**Functions:**

- `generateReport(testSuite)`
- `getTimestamp()`
- `generateHtmlContent(testSuite, timestamp)`
- `calculateStatistics(testSuite)`
- `determineStatus(steps)`
- `formatTimestamp(timestamp)`
- `saveScreenshot(stepId, screenshotData)`
- `getLatestReportUrl()`

---

### test.json

---

### image_matcher.py

**Functions:**

- `__init__(self, device_id, max_workers, platform)`
- `set_device_id(self, device_id)`
- `set_platform(self, platform)`
- `set_airtest_device(self, airtest_device)`
- `take_screenshot_with_adb(self)`
- `take_screenshot_ios(self, screenshot_path)`
- `take_screenshot_android(self, screenshot_path)`
- `find_template(self, template_path, screenshot_path, threshold, debug)`
- `wait_for_template(self, template_path, timeout, interval, threshold)`
- `process_screenshot(screenshot_path)`

**Classes:**

- `ImageMatcher`
  - `__init__(self, device_id, max_workers, platform)`
  - `set_device_id(self, device_id)`
  - `set_platform(self, platform)`
  - `set_airtest_device(self, airtest_device)`
  - `take_screenshot_with_adb(self)`
  - `take_screenshot_ios(self, screenshot_path)`
  - `take_screenshot_android(self, screenshot_path)`
  - `find_template(self, template_path, screenshot_path, threshold, debug)`
  - `wait_for_template(self, template_path, timeout, interval, threshold)`

---

### parameter_utils.py

**Functions:**

- `substitute_parameters(text, log_substitution)`

---

### player.py

**Functions:**

- `__init__(self, device_controller, socketio, test_idx)`
- `load_recording(self, file_path)`
- `play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)`
- `_verify_device_connection(self)`
- `_recover_device_connection(self)`
- `stop(self)`
- `_find_hook_action(self)`
- `_convert_hook_to_action(self, hook_action)`
- `_execute_hook_action(self, hook_action)`
- `execute_action(self, action)`
- `play_with_validation(self, actions)`
- `_validate_visual_state(self, action)`
- `execute_all_actions(self, actions)`
- `stop_execution(self)`
- `_execute_tap(self, x, y)`
- `_execute_swipe(self, start_x, start_y, end_x, end_y, duration)`
- `_execute_text(self, text)`
- `_execute_key(self, key_code)`
- `_execute_wait(self, duration)`
- `_execute_launch_app(self, package)`
- `_execute_terminate_app(self, package)`
- `_execute_restart_app(self, package)`
- `_execute_wait_till(self, image, timeout, interval)`
- `_execute_exists_image(self, image)`
- `_execute_exists_element(self, locator_type, locator_value, timeout)`
- `_execute_double_click(self, x, y)`
- `_fallback_double_tap(self, x, y)`
- `_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)`
- `_execute_text_clear(self, text, delay)`
- `_execute_click_element(self, locator_type, locator_value, timeout)`
- `_execute_double_click_image(self, image_path, threshold, timeout)`
- `_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)`
- `_execute_click_image(self, image_path, threshold, timeout)`
- `_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)`
- `_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)`
- `_execute_swipe_till_visible(self, action)`
- `_is_element_visible(self, locator_type, locator_value)`
- `scale_ios_coordinates(self, coordinates)`

**Classes:**

- `Player`
  - `__init__(self, device_controller, socketio, test_idx)`
  - `load_recording(self, file_path)`
  - `play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)`
  - `_verify_device_connection(self)`
  - `_recover_device_connection(self)`
  - `stop(self)`
  - `_find_hook_action(self)`
  - `_convert_hook_to_action(self, hook_action)`
  - `_execute_hook_action(self, hook_action)`
  - `execute_action(self, action)`
  - `play_with_validation(self, actions)`
  - `_validate_visual_state(self, action)`
  - `execute_all_actions(self, actions)`
  - `stop_execution(self)`
  - `_execute_tap(self, x, y)`
  - `_execute_swipe(self, start_x, start_y, end_x, end_y, duration)`
  - `_execute_text(self, text)`
  - `_execute_key(self, key_code)`
  - `_execute_wait(self, duration)`
  - `_execute_launch_app(self, package)`
  - `_execute_terminate_app(self, package)`
  - `_execute_restart_app(self, package)`
  - `_execute_wait_till(self, image, timeout, interval)`
  - `_execute_exists_image(self, image)`
  - `_execute_exists_element(self, locator_type, locator_value, timeout)`
  - `_execute_double_click(self, x, y)`
  - `_fallback_double_tap(self, x, y)`
  - `_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)`
  - `_execute_text_clear(self, text, delay)`
  - `_execute_click_element(self, locator_type, locator_value, timeout)`
  - `_execute_double_click_image(self, image_path, threshold, timeout)`
  - `_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)`
  - `_execute_click_image(self, image_path, threshold, timeout)`
  - `_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)`
  - `_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)`
  - `_execute_swipe_till_visible(self, action)`
  - `_is_element_visible(self, locator_type, locator_value)`
  - `scale_ios_coordinates(self, coordinates)`

---

### global_values_db.py

**Functions:**

- `__init__(self, db_path)`
- `_init_db(self)`
- `_init_default_values(self)`
- `get_value(self, name, default)`
- `set_value(self, name, value)`
- `delete_value(self, name)`
- `get_all_values(self)`
- `save_values(self, global_values)`

**Classes:**

- `GlobalValuesDB`
  - `__init__(self, db_path)`
  - `_init_db(self)`
  - `_init_default_values(self)`
  - `get_value(self, name, default)`
  - `set_value(self, name, value)`
  - `delete_value(self, name)`
  - `get_all_values(self)`
  - `save_values(self, global_values)`

---

### file_utils.py

**Functions:**

- `save_json(data, relative_path)`
- `load_json(relative_path)`
- `ensure_directory(relative_path)`
- `ensure_dir_exists(directory)`
- `get_timestamp()`
- `save_to_file(content, filepath)`
- `read_from_file(filepath)`
- `compare_screenshots(screenshot1, screenshot2, threshold)`
- `base64_to_image(base64_str)`
- `image_to_base64(image)`

---

### random_data_generator.py

**Functions:**

- `get_generator_options()`
- `generate_data(generator_id)`

---

### screenshot_manager.py

**Functions:**

- `__init__(self)`
- `initialize(self, report_dir, timestamp)`
- `save_screenshot(self, screenshot_data, filename, action_id, device_controller)`

**Classes:**

- `ScreenshotManager`
  - `__init__(self)`
  - `initialize(self, report_dir, timestamp)`
  - `save_screenshot(self, screenshot_data, filename, action_id, device_controller)`

---

### build_data_json.py

**Functions:**

- `build_data_json_from_test_case(test_case_file, suite_id, execution_data, success, error)`

---

## app/static/css

### modern-styles.css

---

### fixed-device-screen.css

---

### test-case.css

---

### execution-overlay.css

---

### fallback-locators.css

---

### test-cases-styles.css

---

### style.css

---

### test-suites-styles.css

---

### modern-styles.css

---

### fixed-device-screen.css

---

### test-case.css

---

### execution-overlay.css

---

### fallback-locators.css

---

### test-cases-styles.css

---

### style.css

---

### test-suites-styles.css

---

## app/static/js

### hook-action.js

---

### action-manager.js

---

### test_suites.js

---

### execution-overlay.js

---

### action-description.js

**Functions:**

- `getActionDescription(actionData)`

---

### main.js

**Functions:**

- `addLocatorItem(container, locator, type)`
- `switch(action.ios_function)`
- `switch(selectedFunction)`
- `type(which may be used by the new clipboard function implementation)`
- `cancelPickingHandler()`
- `escHandler(e)`
- `handleKeyDown(e)`
- `oldClickHandler()`

---

### socket-handlers.js

**Functions:**

- `setupSocketHandlers(socket)`
- `updateConnectionStatus(isConnected, deviceInfo)`
- `handleSuiteExecutionStatus(data)`

---

### tap-fallback-manager.js

---

### execution-manager.js

---

### random-data-generator.js

---

### utils.js

**Functions:**

- `formatDate(dateString)`
- `showToast(title, message, type = 'info')`
- `showLoading(show, message = 'Loading...')`
- `logAction(source, message, type = 'info')`

---

### reports.js

---

### multi-step-action.js

---

### fixed-device-screen.js

---

### app.js

**Functions:**

- `enhanceTabNavigation()`
- `setupAppSocketListeners(socket)`
- `setupDeviceScreen()`
- `executeAction(index)`
- `get_action_description(action)`
- `updateSliderPosition(activeTab)`

---

### settings.js

**Functions:**

- `loadDirectories()`
- `loadGlobalValues()`
- `saveDirectories()`
- `saveGlobalValues()`
- `collectGlobalValues()`
- `populateTestCasesTable(testCases)`
- `loadTestCase(filename)`
- `handleHidden()`
- `deleteTestCase(filename)`
- `loadTestSuites()`
- `populateTestSuitesTable(testSuites)`
- `loadReports()`
- `populateReportsTable(reports)`
- `formatDate(dateString)`
- `populateGlobalValuesTable(globalValues)`
- `addGlobalValueRow(name = '', value = '')`

---

### hook-action.js

---

### action-manager.js

---

### test_suites.js

---

### execution-overlay.js

---

### action-description.js

**Functions:**

- `getActionDescription(actionData)`

---

### main.js

**Functions:**

- `addLocatorItem(container, locator, type)`
- `switch(action.ios_function)`
- `switch(selectedFunction)`
- `type(which may be used by the new clipboard function implementation)`
- `cancelPickingHandler()`
- `escHandler(e)`
- `handleKeyDown(e)`
- `oldClickHandler()`

---

### socket-handlers.js

**Functions:**

- `setupSocketHandlers(socket)`
- `updateConnectionStatus(isConnected, deviceInfo)`
- `handleSuiteExecutionStatus(data)`

---

### tap-fallback-manager.js

---

### execution-manager.js

---

### random-data-generator.js

---

### utils.js

**Functions:**

- `formatDate(dateString)`
- `showToast(title, message, type = 'info')`
- `showLoading(show, message = 'Loading...')`
- `logAction(source, message, type = 'info')`

---

### reports.js

---

### multi-step-action.js

---

### fixed-device-screen.js

---

### app.js

**Functions:**

- `enhanceTabNavigation()`
- `setupAppSocketListeners(socket)`
- `setupDeviceScreen()`
- `executeAction(index)`
- `get_action_description(action)`
- `updateSliderPosition(activeTab)`

---

### settings.js

**Functions:**

- `loadDirectories()`
- `loadGlobalValues()`
- `saveDirectories()`
- `saveGlobalValues()`
- `collectGlobalValues()`
- `populateTestCasesTable(testCases)`
- `loadTestCase(filename)`
- `handleHidden()`
- `deleteTestCase(filename)`
- `loadTestSuites()`
- `populateTestSuitesTable(testSuites)`
- `loadReports()`
- `populateReportsTable(reports)`
- `formatDate(dateString)`
- `populateGlobalValuesTable(globalValues)`
- `addGlobalValueRow(name = '', value = '')`

---

## app/static/js/actions

### swipe-till-visible.js

**Functions:**

- `initSwipeTillVisibleAction()`
- `setupSwipeTillVisibleEventListeners()`
- `updateSwipeTillVisibleCoordinates(direction)`
- `initSliderValueDisplays()`
- `updateSliderDisplayValue(sliderId)`

---

### swipe-till-visible.js

**Functions:**

- `initSwipeTillVisibleAction()`
- `setupSwipeTillVisibleEventListeners()`
- `updateSwipeTillVisibleCoordinates(direction)`
- `initSliderValueDisplays()`
- `updateSliderDisplayValue(sliderId)`

---

## app/static/js/modules

### fallback-locators.js

**Functions:**

- `clickHandler(e)`

---

### tap-fallback-manager.js

---

### ElementInteractions.js

---

### TestCaseManager.js

---

### fallback-locators.js

**Functions:**

- `clickHandler(e)`

---

### tap-fallback-manager.js

---

### ElementInteractions.js

---

### TestCaseManager.js

---

## app/static/templates

### index.html

---

### index.html

---

## app/actions

### tap_if_image_exists_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `TapIfImageExistsAction`
  - `execute(self, params)`

---

### multi_step_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `MultiStepAction`
  - `execute(self, params)`

---

### swipe_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `SwipeAction`
  - `execute(self, params)`

---

### get_param_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `GetParamAction`
  - `execute(self, params)`

---

### wait_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `WaitAction`
  - `execute(self, params)`

---

### terminate_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `TerminateAppAction`
  - `execute(self, params)`

---

### double_click_image_action.py

**Functions:**

- `execute(self, params)`
- `_opencv_double_click_image(self, image_path, threshold, timeout)`

**Classes:**

- `DoubleClickImageAction`
  - `execute(self, params)`
  - `_opencv_double_click_image(self, image_path, threshold, timeout)`

---

### uninstall_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `UninstallAppAction`
  - `execute(self, params)`

---

### text_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `TextAction`
  - `execute(self, params)`

---

### wait_till_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `WaitTillAction`
  - `execute(self, params)`

---

### __init__.py

---

### base_action.py

**Functions:**

- `__init__(self, controller)`
- `execute(self, params)`
- `set_controller(self, controller)`
- `get_global_timeout(self, default)`
- `take_screenshot_after_action(self)`
- `scale_ios_coordinates(self, coordinates, device_info)`

**Classes:**

- `BaseAction`
  - `__init__(self, controller)`
  - `execute(self, params)`
  - `set_controller(self, controller)`
  - `get_global_timeout(self, default)`
  - `take_screenshot_after_action(self)`
  - `scale_ios_coordinates(self, coordinates, device_info)`

---

### hook_action.py

**Functions:**

- `execute(self, params)`
- `_get_hook_details(self, hook_type, hook_data)`

**Classes:**

- `HookAction`
  - `execute(self, params)`
  - `_get_hook_details(self, hook_type, hook_data)`

---

### input_text_action.py

**Functions:**

- `execute(self, params)`
- `_has_selenium_support(self)`

**Classes:**

- `InputTextAction`
  - `execute(self, params)`
  - `_has_selenium_support(self)`

---

### send_keys_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `SendKeysAction`
  - `execute(self, params)`

---

### set_param_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `SetParamAction`
  - `execute(self, params)`

---

### airplane_mode_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `AirplaneModeAction`
  - `execute(self, params)`

---

### ios_functions_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `IosFunctionsAction`
  - `execute(self, params)`

---

### swipe_till_visible_action.py

**Functions:**

- `execute(self, params)`
- `_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)`

**Classes:**

- `SwipeTillVisibleAction`
  - `execute(self, params)`
  - `_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)`

---

### click_image_action.py

**Functions:**

- `execute(self, params)`
- `_has_airtest_support(self)`

**Classes:**

- `ClickImageAction`
  - `execute(self, params)`
  - `_has_airtest_support(self)`

---

### tap_action.py

**Functions:**

- `scale_ios_coordinates(self, coordinates)`
- `execute(self, params)`

**Classes:**

- `TapAction`
  - `scale_ios_coordinates(self, coordinates)`
  - `execute(self, params)`

---

### if_else_steps_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `IfElseStepsAction`
  - `execute(self, params)`

---

### key_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `KeyAction`
  - `execute(self, params)`

---

### add_media_action.py

**Functions:**

- `execute(self, params, context)`

**Classes:**

- `AddMediaAction`
  - `execute(self, params, context)`

---

### hide_keyboard_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `HideKeyboardAction`
  - `execute(self, params)`

---

### tap_and_type_action.py

**Functions:**

- `__init__(self, controller)`
- `execute(self, params)`
- `_tap_and_type_at_coordinates(self, x, y, text, timeout)`
- `_use_direct_keys_endpoint(self, text)`
- `_simulate_keyboard_typing(self, text)`
- `_has_selenium_support(self)`

**Classes:**

- `TapAndTypeAction`
  - `__init__(self, controller)`
  - `execute(self, params)`
  - `_tap_and_type_at_coordinates(self, x, y, text, timeout)`
  - `_use_direct_keys_endpoint(self, text)`
  - `_simulate_keyboard_typing(self, text)`
  - `_has_selenium_support(self)`

---

### tap_on_text_action.py

**Functions:**

- `scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)`
- `get_common_element_coordinates(self, text, device_width, device_height)`
- `find_text_in_bottom_region(self, screenshot_path, text_to_find)`
- `execute(self, params)`

**Classes:**

- `TapOnTextAction`
  - `scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)`
  - `get_common_element_coordinates(self, text, device_width, device_height)`
  - `find_text_in_bottom_region(self, screenshot_path, text_to_find)`
  - `execute(self, params)`

---

### launch_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `LaunchAppAction`
  - `execute(self, params)`

---

### wait_element_action.py

**Functions:**

- `execute(self, params)`
- `_has_selenium_support(self)`

**Classes:**

- `WaitElementAction`
  - `execute(self, params)`
  - `_has_selenium_support(self)`

---

### compare_value_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `CompareValueAction`
  - `execute(self, params)`

---

### device_back_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `DeviceBackAction`
  - `execute(self, params)`

---

### click_element_action.py

**Functions:**

- `execute(self, params)`
- `_has_selenium_support(self)`

**Classes:**

- `ClickElementAction`
  - `execute(self, params)`
  - `_has_selenium_support(self)`

---

### random_data_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `RandomDataAction`
  - `execute(self, params)`

---

### get_value_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `GetValueAction`
  - `execute(self, params)`

---

### action_factory.py

**Functions:**

- `__init__(self, controller)`
- `_discover_action_handlers(self)`
- `execute_action(self, action_type, params)`
- `_ensure_double_tap_registered(self)`
- `set_controller(self, controller)`

**Classes:**

- `ActionFactory`
  - `__init__(self, controller)`
  - `_discover_action_handlers(self)`
  - `execute_action(self, action_type, params)`
  - `_ensure_double_tap_registered(self)`
  - `set_controller(self, controller)`

---

### restart_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `RestartAppAction`
  - `execute(self, params)`

---

### double_tap_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `DoubleTapAction`
  - `execute(self, params)`

---

### add_log_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `AddLogAction`
  - `execute(self, params)`

---

### tap_if_image_exists_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `TapIfImageExistsAction`
  - `execute(self, params)`

---

### multi_step_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `MultiStepAction`
  - `execute(self, params)`

---

### swipe_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `SwipeAction`
  - `execute(self, params)`

---

### get_param_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `GetParamAction`
  - `execute(self, params)`

---

### wait_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `WaitAction`
  - `execute(self, params)`

---

### terminate_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `TerminateAppAction`
  - `execute(self, params)`

---

### double_click_image_action.py

**Functions:**

- `execute(self, params)`
- `_opencv_double_click_image(self, image_path, threshold, timeout)`

**Classes:**

- `DoubleClickImageAction`
  - `execute(self, params)`
  - `_opencv_double_click_image(self, image_path, threshold, timeout)`

---

### uninstall_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `UninstallAppAction`
  - `execute(self, params)`

---

### text_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `TextAction`
  - `execute(self, params)`

---

### wait_till_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `WaitTillAction`
  - `execute(self, params)`

---

### __init__.py

---

### base_action.py

**Functions:**

- `__init__(self, controller)`
- `execute(self, params)`
- `set_controller(self, controller)`
- `get_global_timeout(self, default)`
- `take_screenshot_after_action(self)`
- `scale_ios_coordinates(self, coordinates, device_info)`

**Classes:**

- `BaseAction`
  - `__init__(self, controller)`
  - `execute(self, params)`
  - `set_controller(self, controller)`
  - `get_global_timeout(self, default)`
  - `take_screenshot_after_action(self)`
  - `scale_ios_coordinates(self, coordinates, device_info)`

---

### hook_action.py

**Functions:**

- `execute(self, params)`
- `_get_hook_details(self, hook_type, hook_data)`

**Classes:**

- `HookAction`
  - `execute(self, params)`
  - `_get_hook_details(self, hook_type, hook_data)`

---

### input_text_action.py

**Functions:**

- `execute(self, params)`
- `_has_selenium_support(self)`

**Classes:**

- `InputTextAction`
  - `execute(self, params)`
  - `_has_selenium_support(self)`

---

### send_keys_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `SendKeysAction`
  - `execute(self, params)`

---

### set_param_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `SetParamAction`
  - `execute(self, params)`

---

### airplane_mode_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `AirplaneModeAction`
  - `execute(self, params)`

---

### ios_functions_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `IosFunctionsAction`
  - `execute(self, params)`

---

### swipe_till_visible_action.py

**Functions:**

- `execute(self, params)`
- `_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)`

**Classes:**

- `SwipeTillVisibleAction`
  - `execute(self, params)`
  - `_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)`

---

### click_image_action.py

**Functions:**

- `execute(self, params)`
- `_has_airtest_support(self)`

**Classes:**

- `ClickImageAction`
  - `execute(self, params)`
  - `_has_airtest_support(self)`

---

### tap_action.py

**Functions:**

- `scale_ios_coordinates(self, coordinates)`
- `execute(self, params)`

**Classes:**

- `TapAction`
  - `scale_ios_coordinates(self, coordinates)`
  - `execute(self, params)`

---

### if_else_steps_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `IfElseStepsAction`
  - `execute(self, params)`

---

### key_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `KeyAction`
  - `execute(self, params)`

---

### add_media_action.py

**Functions:**

- `execute(self, params, context)`

**Classes:**

- `AddMediaAction`
  - `execute(self, params, context)`

---

### hide_keyboard_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `HideKeyboardAction`
  - `execute(self, params)`

---

### tap_and_type_action.py

**Functions:**

- `__init__(self, controller)`
- `execute(self, params)`
- `_tap_and_type_at_coordinates(self, x, y, text, timeout)`
- `_use_direct_keys_endpoint(self, text)`
- `_simulate_keyboard_typing(self, text)`
- `_has_selenium_support(self)`

**Classes:**

- `TapAndTypeAction`
  - `__init__(self, controller)`
  - `execute(self, params)`
  - `_tap_and_type_at_coordinates(self, x, y, text, timeout)`
  - `_use_direct_keys_endpoint(self, text)`
  - `_simulate_keyboard_typing(self, text)`
  - `_has_selenium_support(self)`

---

### tap_on_text_action.py

**Functions:**

- `scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)`
- `get_common_element_coordinates(self, text, device_width, device_height)`
- `find_text_in_bottom_region(self, screenshot_path, text_to_find)`
- `execute(self, params)`

**Classes:**

- `TapOnTextAction`
  - `scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)`
  - `get_common_element_coordinates(self, text, device_width, device_height)`
  - `find_text_in_bottom_region(self, screenshot_path, text_to_find)`
  - `execute(self, params)`

---

### launch_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `LaunchAppAction`
  - `execute(self, params)`

---

### wait_element_action.py

**Functions:**

- `execute(self, params)`
- `_has_selenium_support(self)`

**Classes:**

- `WaitElementAction`
  - `execute(self, params)`
  - `_has_selenium_support(self)`

---

### compare_value_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `CompareValueAction`
  - `execute(self, params)`

---

### device_back_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `DeviceBackAction`
  - `execute(self, params)`

---

### click_element_action.py

**Functions:**

- `execute(self, params)`
- `_has_selenium_support(self)`

**Classes:**

- `ClickElementAction`
  - `execute(self, params)`
  - `_has_selenium_support(self)`

---

### random_data_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `RandomDataAction`
  - `execute(self, params)`

---

### get_value_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `GetValueAction`
  - `execute(self, params)`

---

### action_factory.py

**Functions:**

- `__init__(self, controller)`
- `_discover_action_handlers(self)`
- `execute_action(self, action_type, params)`
- `_ensure_double_tap_registered(self)`
- `set_controller(self, controller)`

**Classes:**

- `ActionFactory`
  - `__init__(self, controller)`
  - `_discover_action_handlers(self)`
  - `execute_action(self, action_type, params)`
  - `_ensure_double_tap_registered(self)`
  - `set_controller(self, controller)`

---

### restart_app_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `RestartAppAction`
  - `execute(self, params)`

---

### double_tap_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `DoubleTapAction`
  - `execute(self, params)`

---

### add_log_action.py

**Functions:**

- `execute(self, params)`

**Classes:**

- `AddLogAction`
  - `execute(self, params)`

---

## app/templates

### index.html

---

### tap_on_text_form.html

---

### settings.html

---

### tap-fallback-modal.html

---

### test_suites.html

---

### index.html

---

### tap_on_text_form.html

---

### settings.html

---

### tap-fallback-modal.html

---

### test_suites.html

---

## app/routes

### __init__.py

---

### random_data_routes.py

**Functions:**

- `get_generators()`
- `generate_random_data()`

---

### __init__.py

---

### random_data_routes.py

**Functions:**

- `get_generators()`
- `generate_random_data()`

---

## test_cases

### Postcode_Flow_Copy_20250513180904_20250513180904.json

---

### Delivery__CNC_Copy_20250513201112_20250513201112.json

---

### Postcode_Flow_Copy_20250518114624_20250518114624.json

---

### OnePass_Account_Signin_20250510101051.json

---

### Delivery_Buy_Steps_20250512194232.json

---

### All_Sign_ins_Copy_20250514181123_20250514181123.json

---

### WishList_20250510110236.json

---

### SampleApp_20250401224106.json

---

### KmartProdSignin_Copy_20250501144222_20250501144222.json

---

### Delivery__Buy_20250505165058.json

---

### Delivery_Buy_Steps_Copy_20250513195816_20250513195816.json

---

### Postcode_Flow_20250502104451.json

---

### AU_MyAccount_Copy_20250514192836_20250514192836.json

---

### Browse__PDP_Copy_20250514183633_20250514183633.json

---

### WishList_Copy_20250514185153_20250514185153.json

---

### Kmart_Prod__Onboarding2_Copy_20250513175727_20250513175727.json

---

### Others_20250512190312.json

---

### apple_health_Copy_20250409201903.json

---

### KmartProdSignin_20250426221008.json

---

### Delivery__CNC_20250505163250.json

---

### apple_health_20250407210435.json

---

### Browse__PDP_20250510095542.json

---

### KmartSignin_Copy_20250513181428_20250513181428.json

---

### Login_User_CNC_payment_20250501215014.json

---

### All_Sign_ins_20250501131834.json

---

### KmartProdSignin_Copy_20250514190524_20250514190524.json

---

### Guest_Buy_20250424191014.json

---

### installkmartau_20250503214553.json

---

### Kmart_Prod__Onboarding1_20250422134746.json

---

### Kmart_Prod__Onboarding2_20250422143908.json

---

### health2_20250408214926.json

---

### AU_MyAccount_20250506181929.json

---

## image_comparison

### compare_images.py

**Functions:**

- `compare_images(image1_path, image2_path, threshold, min_area_percent, diff_threshold)`

---

### create_sample_images.py

**Functions:**

- `create_sample_image(filename, text, size, bg_color, text_color)`
- `main()`

---

### test_different.py

---

### generate_html_report.py

**Functions:**

- `main()`
- `__init__(self, baseline_dir, new_dir, output_dir, threshold)`
- `find_image_pairs(self)`
- `compare_images(self, baseline_path, new_path)`
- `image_to_base64(self, image_path)`
- `run_comparison(self)`
- `generate_html_report(self)`

**Classes:**

- `ImageComparator`
  - `__init__(self, baseline_dir, new_dir, output_dir, threshold)`
  - `find_image_pairs(self)`
  - `compare_images(self, baseline_path, new_path)`
  - `image_to_base64(self, image_path)`
  - `run_comparison(self)`
  - `generate_html_report(self)`

---

## image_comparison/report

### report.html

---

## scripts

### convert_multistep_test_cases.py

**Functions:**

- `load_test_case(filename)`
- `save_test_case(test_case_data, filename)`
- `convert_test_case(filename)`
- `main()`

---

### add_action_ids.py

**Functions:**

- `process_test_case(file_path)`
- `main()`

---

### update_database_action_ids.py

**Functions:**

- `load_test_case(file_path)`
- `get_test_steps_with_action_ids(test_cases_dir)`
- `update_database_with_action_ids(steps_with_action_ids)`
- `main()`

---

