# Bulk Locator Search and Replace Tool

## Overview

The Bulk Locator Search and Replace tool is a powerful utility designed to help users efficiently manage locators across multiple test cases and test suites. This tool provides a simplified, user-friendly interface for finding and updating locator values in bulk, with comprehensive backup and undo functionality.

## Features

### 🔍 **Search Functionality**
- Search for locators across all test cases or within specific folders
- Support for multiple locator types (XPath, ID, class name, accessibility ID, environment variables)
- Real-time search results with detailed information
- Line number estimation for easy navigation

### 🔄 **Replace Functionality**
- Bulk replacement of locator values across multiple files
- Automatic backup creation before making changes
- Confirmation modal with affected files preview
- Database synchronization for locator references

### ↩️ **Undo Functionality**
- Restore files from automatic backups
- Multiple backup versions available
- Timestamp-based backup selection
- Complete rollback of changes

### 🛡️ **Safety Features**
- Automatic backup creation before any modifications
- Confirmation dialogs for destructive operations
- Input validation for locator formats
- Error handling and user feedback

## User Interface Components

### 1. **Folder Path Input Field**
- **Purpose**: Specify the search scope within the test cases directory
- **Usage**: 
  - Leave empty to search all test cases
  - Enter relative path (e.g., "subfolder") to limit search scope
  - Use the Browse button (future enhancement) for visual folder selection

### 2. **Locator Input Field**
- **Purpose**: Enter the locator value to search for
- **Supported Formats**:
  - XPath: `//button[@id='submit']`
  - ID: `submit-button`
  - Class Name: `btn-primary`
  - Accessibility ID: `submit_btn`
  - Environment Variables: `env[submit_button_id]`
- **Features**:
  - Real-time validation
  - Format detection
  - Auto-complete suggestions (future enhancement)

### 3. **Action Buttons**

#### **Search Button**
- Performs search operation across specified scope
- Displays results in a structured table format
- Shows progress indicator during operation

#### **Search and Replace Button**
- Only enabled after successful search with results
- Opens confirmation modal with affected files preview
- Requires explicit user confirmation before proceeding

#### **Undo Last Changes Button**
- Only enabled when backups are available
- Opens backup selection modal
- Allows restoration from multiple backup versions

## Search Results Display

The search results are presented in a comprehensive table with the following columns:

| Column | Description |
|--------|-------------|
| **Filename** | Name of the test case file containing the locator |
| **Action Type** | Type of action using the locator (tap, swipe, wait, etc.) |
| **Locator Field** | Specific field name containing the locator |
| **Current Value** | The actual locator value found |
| **Line #** | Estimated line number in the file |

## Search and Replace Workflow

### Step 1: Search
1. Enter folder path (optional) and locator value
2. Click "Search" to find all occurrences
3. Review search results in the table

### Step 2: Replace
1. Click "Search and Replace" button
2. Review affected files in the modal
3. Enter new locator value
4. Check confirmation checkbox
5. Click "Confirm Replace"

### Step 3: Verification
1. Review success message with backup timestamp
2. Verify changes in affected test cases
3. Use "Undo" if needed to revert changes

## Backup and Undo System

### Automatic Backups
- Created automatically before any bulk replace operation
- Stored in `tmp/backup_locators/backup_[timestamp]/` directory
- Include all affected test case files
- Timestamped for easy identification

### Backup Format
```
tmp/backup_locators/
├── backup_20241211_143022/
│   ├── test_case_1.json
│   ├── test_case_2.json
│   └── test_case_3.json
└── backup_20241211_151545/
    ├── test_case_4.json
    └── test_case_5.json
```

### Undo Process
1. Click "Undo Last Changes"
2. Select backup from dropdown (newest first)
3. Review backup details (date, file count)
4. Click "Restore Backup"
5. Confirm restoration success

## Technical Implementation

### Backend Components

#### **BulkLocatorManager Class**
- **Location**: `app/utils/bulk_locator_manager.py` and `app_android/utils/bulk_locator_manager.py`
- **Purpose**: Core logic for search, replace, and backup operations
- **Key Methods**:
  - `search_locators()`: Find locator occurrences
  - `replace_locators()`: Perform bulk replacement
  - `undo_last_changes()`: Restore from backup
  - `validate_locator_format()`: Validate locator syntax

#### **API Endpoints**
- `POST /api/bulk_locator/search`: Search for locators
- `POST /api/bulk_locator/replace`: Perform bulk replacement
- `POST /api/bulk_locator/undo`: Undo changes from backup
- `GET /api/bulk_locator/backups`: Get available backups
- `POST /api/bulk_locator/validate`: Validate locator format

### Frontend Components

#### **BulkLocatorManager JavaScript Class**
- **Location**: `app/static/js/bulk-locator-manager.js` and `app_android/static/js/bulk-locator-manager.js`
- **Purpose**: Handle user interface interactions and API communication
- **Key Features**:
  - Real-time form validation
  - Progress indicators
  - Modal management
  - Error handling and user feedback

#### **CSS Styling**
- **Location**: `app/static/css/bulk-locator-manager.css` and `app_android/static/css/bulk-locator-manager.css`
- **Purpose**: Enhanced visual styling for the tool interface
- **Features**:
  - Responsive design
  - Loading states
  - Hover effects
  - Custom scrollbars

## Supported Locator Types

### 1. **XPath Locators**
```
//button[@id='submit']
//div[contains(@class, 'modal')]
.//input[@type='text']
```

### 2. **ID Locators**
```
submit-button
user-input
navigation-menu
```

### 3. **Class Name Locators**
```
btn-primary
form-control
nav-item
```

### 4. **Accessibility ID Locators**
```
submit_button
user_input_field
navigation_menu
```

### 5. **Environment Variables**
```
env[submit_button_id]
env[login_form_xpath]
env[navigation_locator]
```

## Error Handling

### Common Error Scenarios
1. **Invalid locator format**: Validation prevents invalid locators
2. **File access errors**: Proper error messages for permission issues
3. **Backup corruption**: Verification before restoration
4. **Network timeouts**: Retry mechanisms for API calls

### User Feedback
- **Success messages**: Clear confirmation of completed operations
- **Warning alerts**: Caution for potentially destructive actions
- **Error notifications**: Detailed error information with suggested actions
- **Progress indicators**: Real-time operation status

## Best Practices

### Before Using the Tool
1. **Backup your test cases** manually as an additional safety measure
2. **Test on a small subset** before bulk operations
3. **Verify locator format** using the validation feature
4. **Review search results** carefully before replacing

### During Operations
1. **Use specific search terms** to avoid unintended matches
2. **Review affected files** in the confirmation modal
3. **Double-check new locator values** before confirming
4. **Monitor progress indicators** for operation status

### After Operations
1. **Verify changes** in a few test cases manually
2. **Run test cases** to ensure functionality is preserved
3. **Keep backup timestamps** for future reference
4. **Document changes** for team awareness

## Troubleshooting

### Common Issues

#### **No Search Results Found**
- Verify locator value is correct
- Check folder path specification
- Ensure test cases contain the expected locator

#### **Replace Operation Failed**
- Check file permissions in test cases directory
- Verify sufficient disk space for backups
- Ensure no files are locked by other processes

#### **Undo Operation Failed**
- Verify backup files exist and are accessible
- Check file permissions for restoration
- Ensure target files are not locked

#### **Validation Errors**
- Review locator format against supported types
- Check for special characters or encoding issues
- Verify environment variable syntax

### Getting Help
1. Check browser console for detailed error messages
2. Review server logs for backend issues
3. Verify file permissions and disk space
4. Contact system administrator for persistent issues

## Future Enhancements

### Planned Features
1. **File browser integration** for folder path selection
2. **Regex search support** for advanced pattern matching
3. **Batch operations** for multiple locator replacements
4. **Export/import** of search results
5. **Integration with version control** for change tracking
6. **Locator usage analytics** and recommendations

### Performance Improvements
1. **Indexed search** for faster operations
2. **Parallel processing** for large file sets
3. **Incremental backups** to reduce storage usage
4. **Caching mechanisms** for repeated searches
