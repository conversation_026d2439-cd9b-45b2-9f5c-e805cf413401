# Secure Mobile Automation – User Guide

This document explains how licensed users install and operate the secure
Mobile App Automation bundle on their workstation.

---

## 1. Before You Begin

- Obtain your **license capsule** (`license.jwt`) from support.
- Ensure you have the appropriate installer for your platform:
  - Windows: `SecureMobileAppAutomation.msi` or `.msix`
  - macOS: `SecureMobileAppAutomation.pkg` or `.dmg`
  - Linux: `SecureMobileAppAutomation.AppImage` / `.deb` / `.rpm`
- Install the latest versions of Xcode Command Line Tools (macOS) or the Android
  USB drivers (Windows) if you plan to test on physical devices.
- Connect devices via USB and enable developer modes:
  - iOS: enable "Developer Mode" and trust the computer.
  - Android: enable USB debugging and verify with `adb devices`.

---

## 2. Install or Unpack the Application

Two delivery styles are available:

- **Portable ZIP bundle** – unzip `SecureMobileAppAutomation_<os>_<version>.zip`
  into a writeable folder (for example `~/SecureAutomation` or `C:\SecureAutomation`).
  This bundle already contains the embedded Python/Node runtimes and sanitized
  databases.
- **Native installer** – when provided, run the `.msix`, `.pkg/.dmg`, or `.deb/.rpm`
  package just like any other desktop app.

After extraction/installation, launch the suite as follows:

- **macOS/Linux**: run `./launch.sh` from Terminal (first run may require
  `chmod +x launch.sh`). Control-click → *Open* to bypass Gatekeeper for unsigned
  builds.
- **Windows**: double-click `launch.bat`. A console window opens and starts the
  automation services.

Alternatively, a desktop launcher with an embedded browser is available while
developing from source:

```bash
python -m desktop_launcher.main
```

The Qt launcher starts the backend, streams logs, and loads the dashboard inside
its window (or opens your default browser if QtWebEngine isn’t available).

Keep the folder intact—the automation workspace expects the bundled runtime
structure to remain unchanged.

---

## 3. Activate Your License

1. Launch the **Secure Distribution Launcher**.
2. Choose **License > Import License** and select `license.jwt`.
3. The launcher validates the signature, binds the license to your hardware, and
   stores encrypted tokens locally. When prompted, sign in with the provided
   Supabase credentials.
4. If activation fails, contact support with the displayed error code.

> Licenses are hardware-bound. Significant hardware changes will require a
> reset from support.

---

## 4. Starting the Automation Platforms

1. From the launcher dashboard, press **Launch Android App** or **Launch iOS App**.
2. The launcher automatically:
   - Detects connected devices (ADB, libimobiledevice, WebUSB).
   - Allocates isolated ports for Flask, Appium, and WDA.
   - Starts the embedded Python/Node runtimes with all dependencies.
3. When the status shows *Ready*, click **Open App**. Your browser navigates to
   `http://localhost:<port>` with an authenticated session.

### Troubleshooting
- If the device screen shows an unplug icon, ensure the device is unlocked and
  the trust/ADB prompts are accepted.
- Use **Tools > Restart Services** to restart Appium/WDA without closing the web app.
- Logs are available under `~/SecureMobileAppAutomation/logs` (Windows users:
  `%LOCALAPPDATA%\SecureMobileAppAutomation\logs`).

---

## 5. Updating the Application

1. Download the new installer from the secure portal.
2. Run it over the existing installation; settings and licenses persist.
3. After installation, the launcher verifies and migrates runtime assets.

> Tip: Keep your license file as backup; reinstalling from scratch requires it.

---

## 6. Security Practices

- Licenses and runtime tokens are encrypted; do not share binaries publicly.
- The launcher verifies integrity on startup; do not tamper with installed files.
- For shared machines, sign out after finishing work (top-right menu > Sign out).
- To revoke a device, open **License > Deactivate Device** and contact support.

---

## 7. Getting Support

- Email: <EMAIL>
- Provide: OS version, app version (Dashboard footer), device logs, and a short
  description of the scenario when raising a ticket.

Enjoy automated testing with the secure Mobile App Automation suite!
