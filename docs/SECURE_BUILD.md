# Secure Build and Obfuscation Guide

This guide documents how we build and verify the python-minifier–protected distribution for the Mobile App Automation Tool.

## Overview
- Distribution artifacts are generated under `secure_distribution_app/temp_build_final/obfuscated/`
- The backend strictly launches ONLY obfuscated entry scripts (created by python-minifier, with base64 fallback for edge cases):
  - Android: `secure_distribution_app/temp_build_final/obfuscated/run_android.py`
  - iOS: `secure_distribution_app/temp_build_final/obfuscated/run.py`
- If these files are missing, the backend returns a styled error page and logs a CRITICAL security error.

## Build Script
- Path: `secure_distribution_app/build_final_secure_app.py`
- Entry: `python3 secure_distribution_app/build_final_secure_app.py`
- Responsibilities:
  1) Prepare a temp source tree under `temp_build_final/`
  2) Obfuscate code with python-minifier (fast, strict; base64 stub fallback for problematic files)
  3) Build the packaged executable (PyInstaller)
  4) Create a distribution package under `secure_distribution_app/dist/`

## Python-Minifier Configuration (Fast, Strict)
- Implemented inside `FinalSecureAppBuilder.obfuscate_code_minify()`
- Settings used:
  - remove_literal_statements=True (remove docstrings); remove_annotations=True; combine_imports=True; hoist_literals=False; rename_locals=True; rename_globals=False
- Sources included:
  - `web_server`, `auth`, `security`, `utils`, `app`, `app_android`, `run.py`, `run_android.py`, `main_web_secure.py`
- The build FAILS if any target `.py` is missing in obfuscated output. If python-minifier fails on a file, we fallback to a small base64 loader stub to ensure no plain source remains.
- Entry scripts are marked executable (chmod +x) in the obfuscated output.

## Requirements
- Python 3.8+
- python-minifier installed (pip):
  - `python3 -m pip install python-minifier`
- No license required for python-minifier.



## Build Steps
1) Ensure dependencies (python-minifier, PyInstaller) are installed in your environment.
2) Run the builder:
   ```bash
   python3 secure_distribution_app/build_final_secure_app.py
   ```
3) On success, verify:
   - Obfuscated output: `secure_distribution_app/temp_build_final/obfuscated/`
   - Dist package: `secure_distribution_app/dist/SecureMobileAppAutomation/`

## Verifying Obfuscation Coverage
- Run the scanner:
  ```bash
  python3 secure_distribution_app/security/scan_obfuscation.py
  ```
- Outputs `secure_distribution_app/temp_build_final/obfuscation_report_current.json` and a summary to stdout.
- Ensure `Plain files: 0` for protected modules.

## SECURE_BUILD Runtime Behavior
- When platform apps (iOS/Android) launch, `SECURE_BUILD=True` is set by the backend.
- Entry scripts honor it (post-rebuild):
  - host binds to `127.0.0.1`
  - Flask `debug=False`

## Troubleshooting
- python-minifier errors
  - Some files may raise SyntaxError under aggressive options. The builder will retry without removing literal-only blocks; if it still fails, it falls back to a small base64 loader stub to ensure no plain source remains.
  - Confirm: `python3 -c "import python_minifier, sys; print(python_minifier.__version__)"`
- Plain files remain after obfuscation
  - Re-run build; check logs for which file fell back to loader stub.
  - Check the console for errors per file
- Import errors after obfuscation
  - Verify that directory structure preserved under `obfuscated/`
  - For iOS runner missing `config`, backend injects a minimal `config` shim on launch
- Backend shows "Secure Build Incomplete"
  - Confirm the presence of `obfuscated/run.py` and `obfuscated/run_android.py`
  - Re-run the build script

## Updating Distribution After Source Changes
1) Pull latest source changes
2) Re-run the build script
3) Re-scan obfuscation
4) Replace distribution package or bundle

## Support
- If issues persist, capture logs:
  - Backend: `secure_distribution_app/secure_web_app.log`
  - Launch logs: `secure_distribution_app/logs/android_launch_*.log`, `secure_distribution_app/logs/ios_launch_*.log`
  - Obfuscation scan: `secure_distribution_app/temp_build_final/obfuscation_report_current.json`

