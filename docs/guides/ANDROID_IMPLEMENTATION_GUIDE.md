# Android Support Implementation Guide

## Overview

This document describes the comprehensive Android support implementation for the Mobile App Automation Tool. The implementation provides full platform isolation while maintaining a unified user interface.

## Architecture

### Multi-Platform Architecture
- **iOS Backend**: `run.py` (Port 8080) - Uses `app/` folder
- **Android Backend**: `run_android.py` (Port 8081) - Uses `app_android/` folder
- **Unified Discovery**: `unified_device_discovery.py` - Aggregates devices from both platforms
- **Session Routing**: `session_router.py` - Routes requests to appropriate backends

### Folder Structure
```
MobileAppAutomation/
├── app/                          # iOS-specific code
├── app_android/                  # Android-specific code (duplicated and modified)
├── run.py                        # iOS entry point (Port 8080)
├── run_android.py               # Android entry point (Port 8081)
├── config.py                    # iOS configuration
├── config_android.py            # Android configuration
├── unified_device_discovery.py  # Cross-platform device discovery
├── session_router.py            # Request routing
└── unified_routes.py            # Unified API endpoints
```

## Platform-Specific Features

### Android Features (app_android/)
- **Device Discovery**: Uses `adb devices` command
- **Appium Driver**: UiAutomator2 with Android-specific capabilities
- **Action Types**: 
  - All existing actions adapted for Android
  - New `androidFunctions` action type with Android-specific functions
- **Configuration**: Android-specific settings in `config_android.py`

### iOS Features (app/)
- **Device Discovery**: Uses `idevice_id` and related tools
- **Appium Driver**: XCUITest with iOS-specific capabilities
- **Action Types**: All existing iOS actions maintained
- **Configuration**: iOS-specific settings in `config.py`

## How to Use Both Platforms

### Starting the Backends

#### Option 1: Run Both Platforms Simultaneously
```bash
# Terminal 1 - Start iOS backend
python run.py --port 8080 --appium-port 4723

# Terminal 2 - Start Android backend  
python run_android.py --port 8081 --appium-port 4724
```

#### Option 2: Run Single Platform
```bash
# iOS only
python run.py

# Android only
python run_android.py
```

### Device Discovery and Selection

1. **Unified Device Discovery**:
   - Both platforms' devices appear in the same UI
   - Devices are labeled with platform badges (iOS/Android)
   - Platform-specific routing happens automatically

2. **Platform Indicators**:
   - iOS devices: 🍎 iOS badge (blue)
   - Android devices: 🤖 Android badge (green)
   - Backend routing info displayed when device selected

### Session Management

1. **Automatic Routing**:
   - Device selection automatically routes to correct backend
   - iOS devices → Port 8080 (iOS backend)
   - Android devices → Port 8081 (Android backend)

2. **Session Creation**:
   - Same UI for both platforms
   - Platform-specific capabilities applied automatically
   - Error handling for backend availability

### Action Execution

1. **Cross-Platform Actions**:
   - `tap`, `swipe`, `type`, `wait`, `screenshot` work on both platforms
   - `tapOnText`, `tapOnImage`, `checkIfExists` work on both platforms
   - `swipeTillVisible`, `qrBarcodeScan` work on both platforms

2. **Platform-Specific Actions**:
   - **iOS**: `iosFunctions` - iOS-specific system functions
   - **Android**: `androidFunctions` - Android-specific system functions

### Android Functions Available

The `androidFunctions` action type provides:

#### System Navigation
- `home` - Press home button
- `back` - Press back button  
- `recent_apps` - Open recent apps
- `menu` - Press menu button
- `power` - Press power button

#### Volume Control
- `volume_up` - Increase volume
- `volume_down` - Decrease volume

#### Clipboard Operations
- `get_clipboard` - Get clipboard content
- `set_clipboard` - Set clipboard content

#### System Panels
- `open_notifications` - Open notification panel
- `open_quick_settings` - Open quick settings
- `hide_keyboard` - Hide keyboard

#### Device Information
- `get_device_info` - Get device details
- `list_packages` - List installed packages

#### App Management
- `clear_app_data` - Clear app data for package

#### Network Control
- `enable_wifi` / `disable_wifi` - WiFi control
- `enable_data` / `disable_data` - Mobile data control

#### Advanced Operations
- `take_screenshot` - ADB screenshot
- `input_text` - ADB text input
- `swipe_gesture` - ADB swipe with coordinates

## Configuration

### Android Configuration (config_android.py)
- **Platform**: Android
- **Default Port**: 8081
- **Automation**: UiAutomator2
- **ADB Settings**: Timeouts, capabilities
- **Android SDK**: Paths and tools
- **Key Codes**: Android system key mappings
- **Device Properties**: Android system properties to retrieve

### iOS Configuration (config.py)
- **Platform**: iOS  
- **Default Port**: 8080
- **Automation**: XCUITest
- **WebDriverAgent**: iOS-specific settings

## API Endpoints

### Unified Endpoints
- `GET /api/unified/devices` - Get all devices (iOS + Android)
- `GET /api/unified/devices/{id}` - Get specific device
- `POST /api/unified/session/start` - Start session with routing
- `GET /api/unified/platforms` - Get platform information

### Platform-Specific Endpoints
- iOS: `http://localhost:8080/api/*`
- Android: `http://localhost:8081/api/*`

## Prerequisites

### iOS Development
- Xcode and iOS development tools
- libimobiledevice tools (`idevice_id`, `idevicename`, etc.)
- WebDriverAgent setup

### Android Development
- Android SDK with platform-tools
- ADB in PATH
- USB debugging enabled on devices
- Android devices with developer options enabled

### Environment Setup
```bash
# Android SDK
export ANDROID_HOME=/path/to/android-sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Verify tools
adb version
idevice_id --version
```

## Testing the Implementation

### 1. Device Discovery Test
```bash
# Test iOS devices
idevice_id -l

# Test Android devices  
adb devices

# Test unified discovery
python -c "from unified_device_discovery import get_all_devices; print(get_all_devices())"
```

### 2. Backend Health Check
```bash
# Check iOS backend
curl http://localhost:8080/api/health

# Check Android backend
curl http://localhost:8081/api/health

# Check unified status
curl http://localhost:8080/api/unified/platforms
```

### 3. Session Testing
1. Start both backends
2. Connect iOS and Android devices
3. Open web UI (either port)
4. Verify both device types appear
5. Test session creation for each platform
6. Execute actions on both platforms

## Troubleshooting

### Common Issues

1. **ADB Not Found**
   - Install Android SDK platform-tools
   - Add to PATH: `export PATH=$PATH:$ANDROID_HOME/platform-tools`

2. **iOS Tools Missing**
   - Install libimobiledevice: `brew install libimobiledevice`
   - Verify with: `idevice_id --version`

3. **Port Conflicts**
   - iOS default: 8080, Android default: 8081
   - Appium: iOS 4723, Android 4724
   - Use `--port` and `--appium-port` flags to customize

4. **Device Not Detected**
   - iOS: Check device trust, developer mode
   - Android: Enable USB debugging, authorize computer

5. **Backend Connection Failed**
   - Verify backend is running on expected port
   - Check firewall settings
   - Ensure no port conflicts

## Benefits of This Implementation

1. **Platform Isolation**: Complete separation of iOS and Android code
2. **Unified UI**: Single interface for both platforms
3. **Parallel Execution**: Run iOS and Android tests simultaneously
4. **Automatic Routing**: Seamless platform detection and routing
5. **Extensibility**: Easy to add new platforms or features
6. **Maintainability**: Clear separation of concerns

## Future Enhancements

1. **Load Balancing**: Distribute tests across multiple devices
2. **Cloud Integration**: Support for cloud device farms
3. **Advanced Routing**: Intelligent device selection
4. **Performance Monitoring**: Cross-platform performance metrics
5. **Test Orchestration**: Coordinated multi-platform test execution
