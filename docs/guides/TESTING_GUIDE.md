# JSON Editor Testing Guide

## 🚀 Quick Start Testing

### Prerequisites
1. Start the iOS server: `python run.py` (port 8080)
2. Start the Android server: `python run_android.py` (port 8081)
3. Ensure you have existing test cases in your test cases directory

### Basic Workflow Test
1. **Navigate to Test Case Modification Tab**
2. **Search for Test Cases**: Use locator value, image files, or text values filters
3. **Open JSON Editor**: Click "Edit" button on any search result
4. **Verify JSON Editor Opens**: <PERSON><PERSON> should display with test case JSON and line numbers
5. **Test Search**: Use the search bar within the JSON editor to find specific content
6. **Make Changes**: Modify the JSON content
7. **Validate**: Click "Validate" to check for errors
8. **Save**: Click "Validate and Save" to persist changes
9. **Verify Updates**: Check that search results refresh automatically

## 🧪 Detailed Testing Scenarios

### 1. JSON Editor Modal Functionality
```
✅ Test Checklist:
□ Modal opens when clicking Edit button
□ JSON content loads with proper formatting
□ Line numbers display correctly
□ Line numbers sync with scrolling
□ Modal is resizable and responsive
□ Close button works properly
```

### 2. Search Functionality Within JSON Editor
```
✅ Test Checklist:
□ Search input accepts text
□ Case-sensitive toggle works
□ "Find" button performs search
□ Enter key triggers search
□ Next/Previous buttons navigate results
□ Match counter shows "X of Y" format
□ Clear button resets search
□ Auto-search works when editor opens with search criteria
```

### 3. Validation System Testing
```
✅ Test Invalid JSON Syntax:
{
  "name": "Test Case"
  "actions": [  // Missing comma
    {
      "type": "tap"
    }
  ]
}

✅ Test Missing Required Fields:
{
  "name": "",  // Empty name
  "actions": "not_an_array"  // Invalid actions
}

✅ Test Invalid Action Types:
{
  "name": "Test Case",
  "actions": [
    {
      "type": "invalid_action_type",
      "action_id": "test123456"
    }
  ]
}

✅ Test Invalid Action IDs:
{
  "name": "Test Case",
  "actions": [
    {
      "type": "tap",
      "action_id": "short"  // Too short
    },
    {
      "type": "addLog",
      "action_id": "al_toolong123"  // Too long for addLog
    }
  ]
}
```

### 4. Action ID Generation Testing
```
✅ Test Scenarios:
□ Add new action without action_id - should auto-generate
□ Add addLog action without action_id - should generate with 'al_' prefix
□ Verify generated IDs are exactly 10 characters
□ Verify addLog IDs are 'al_' + 7 characters
□ Verify IDs contain only alphanumeric characters
```

### 5. Save and Revert Testing
```
✅ Test Checklist:
□ Make changes and save successfully
□ Verify changes persist after page refresh
□ Test revert functionality restores original JSON
□ Verify backup is created in database
□ Test save with validation errors (should not save)
□ Verify UI updates after successful save
```

### 6. Backward Compatibility Testing
```
✅ Test Checklist:
□ Existing test cases load correctly
□ UUID-based identification still works
□ Search functionality still works with removed filters
□ No broken functionality from legacy editor removal
□ Database operations work correctly
```

## 🔍 Advanced Testing

### Database Testing
```sql
-- Check backup table exists
SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_json_backups';

-- View backup entries
SELECT * FROM test_case_json_backups ORDER BY backup_timestamp DESC LIMIT 5;

-- Check backup data integrity
SELECT test_case_filename, length(json_data), backup_timestamp 
FROM test_case_json_backups;
```

### API Testing (using curl or Postman)
```bash
# Test JSON backup API
curl -X POST http://localhost:8080/api/test_cases/json_backup \
  -H "Content-Type: application/json" \
  -d '{"filename": "test.json", "json_data": {"name": "Test", "actions": []}}'

# Test JSON validation API
curl -X POST http://localhost:8080/api/test_cases/validate_json \
  -H "Content-Type: application/json" \
  -d '{"test_case": {"name": "Test", "actions": []}, "filename": "test.json"}'

# Test JSON save API
curl -X POST http://localhost:8080/api/test_cases/save_json \
  -H "Content-Type: application/json" \
  -d '{"filename": "test.json", "test_case": {"name": "Test", "actions": []}}'
```

## 🐛 Common Issues and Solutions

### Issue: JSON Editor Modal Not Opening
**Solution**: Check browser console for JavaScript errors, ensure Bootstrap is loaded

### Issue: Search Not Working
**Solution**: Verify search input elements exist, check for JavaScript errors

### Issue: Validation Errors Not Displaying
**Solution**: Check API endpoints are responding, verify error message elements exist

### Issue: Save Not Working
**Solution**: Check validation passes first, verify API endpoints, check file permissions

### Issue: Revert Not Working
**Solution**: Verify backup was created, check database connection

## 📊 Performance Testing

### Large JSON Files
1. Test with test cases containing 50+ actions
2. Verify search performance with large content
3. Check line number rendering performance
4. Test save/load times with large files

### Memory Usage
1. Open multiple JSON editors
2. Perform multiple searches
3. Monitor browser memory usage
4. Test for memory leaks

## ✅ Success Criteria

The implementation is successful if:
- ✅ All manual test scenarios pass
- ✅ No JavaScript errors in browser console
- ✅ API endpoints respond correctly
- ✅ Database operations complete successfully
- ✅ UI updates properly after operations
- ✅ Backward compatibility maintained
- ✅ Performance is acceptable for typical use cases

## 📝 Test Results Template

```
Test Date: ___________
Tester: ___________
Platform: iOS / Android
Browser: ___________

Basic Functionality:
□ JSON Editor Opens: Pass/Fail
□ Search Works: Pass/Fail
□ Validation Works: Pass/Fail
□ Save Works: Pass/Fail
□ Revert Works: Pass/Fail

Advanced Features:
□ Action ID Generation: Pass/Fail
□ Auto-search: Pass/Fail
□ UI Updates: Pass/Fail
□ Error Handling: Pass/Fail

Issues Found:
1. ___________
2. ___________
3. ___________

Overall Result: Pass/Fail
```
