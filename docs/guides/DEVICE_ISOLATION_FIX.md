# Device Isolation Fix for Multi-Session Setup

## Problem Description

The multi-session setup was experiencing device isolation issues where both app sessions would connect to the same device instead of maintaining separate device connections. This was causing:

- Both browser tabs showing the same device screen
- Device connections being overwritten when a new session connected
- Inability to test multiple devices simultaneously

## Root Cause Analysis

The issue was caused by **global variable sharing** in the Flask application:

1. **Global `current_device_id`**: All Flask app instances shared the same global `current_device_id` variable
2. **Session State Confusion**: When one session connected to a device, it would overwrite the global state for all other sessions
3. **Screenshot URL Issues**: Screenshot endpoints were using the global device ID instead of session-specific device IDs

### Code Location of the Problem

In `app/app.py` lines 812-819:
```python
# Set as current device
current_device_id = device_id  # ❌ This was global across all sessions
current_device = device_id  # For backward compatibility

# Set global device_controller for backward compatibility with health check
global device_controller, player, action_factory
device_controller = controller  # ❌ Also global
```

## Solution Implemented

### 1. Hybrid Device Tracking

Implemented a hybrid approach that uses both request-based device_id and session-based fallback:

```python
# Session-based device tracking - each session maintains its own current device
def get_session_device_id():
    """Get the current device ID for this session"""
    return session.get('current_device_id')

def set_session_device_id(device_id):
    """Set the current device ID for this session"""
    session['current_device_id'] = device_id

def clear_session_device_id():
    """Clear the current device ID for this session"""
    session.pop('current_device_id', None)

# In API endpoints:
data = request.get_json() or {}
device_id = data.get('device_id') or get_session_device_id()
```

### 2. Updated API Endpoints

Modified all device-related API endpoints to use hybrid device tracking (request device_id + session fallback):

- `/api/device/connect` - Sets session-specific device ID
- `/api/device/disconnect` - Uses request or session device ID
- `/api/action/text` - Uses request device_id or session fallback
- `/api/action/execute` - Uses request device_id or session fallback
- `/api/action/execute_hook` - Uses request device_id or session fallback
- `/api/execute_test_case` - Uses request device_id or session fallback
- `/api/test_suites/<suite_id>/run` - Uses request device_id or session fallback
- `/screenshot` - Uses request deviceId parameter or session fallback

### 3. Frontend Request Enhancement

Updated JavaScript modules to include device_id in all action requests:

- **execution-manager.js**: Includes device_id in all action execution requests
- **main.js**: Includes device_id in individual action execution and connection requests
- **test_suites.js**: Includes device_id in test suite execution requests
- **deviceConnector.js**: Includes sessionId in screenshot URLs for URL-based sessions
- Session ID stored in localStorage for persistence across page reloads

### 4. Device Controller Management

The device controllers are still stored in a global dictionary (`device_controllers`) but are now accessed using session-specific device IDs, ensuring proper isolation.

## Files Modified

1. **`app/app.py`**:
   - Added session-based device tracking functions
   - Updated device connection/disconnection endpoints
   - Modified all API endpoints to use session-specific device controllers

2. **`app/static/js/main.js`**:
   - Updated `connectToDevice()` to send session_id
   - Modified screenshot URLs to include sessionId parameter
   - Enhanced fallback screenshot URLs with session information

3. **`app/static/js/modules/deviceConnector.js`**:
   - Updated automatic screenshot refresh to include sessionId
   - Enhanced device pre-selection with session awareness

## Testing the Fix

### Manual Testing Steps

1. **Start two app instances**:
   ```bash
   # Terminal 1
   python run.py
   
   # Terminal 2  
   python run.py --port 8081 --appium-port 4724
   ```

2. **Open both in separate browser tabs**:
   - Session 1: http://localhost:8080
   - Session 2: http://localhost:8081

3. **Connect different devices** (or same device to test isolation):
   - In Session 1: Select Device A and click Connect
   - In Session 2: Select Device B and click Connect

4. **Verify isolation**:
   - Each session should show its own device screen
   - Screenshots should be device-specific
   - Device connections should remain independent

### Automated Testing

Run the provided test scripts:

**Basic Fix Verification:**
```bash
python test_device_isolation_fix.py
```

**Multi-Session Testing:**
```bash
python test_session_isolation.py
```

The basic test will:
- Connect to a device
- Test action execution with device_id parameter
- Test action execution with session fallback
- Verify screenshot retrieval
- Clean up connection

The multi-session test will:
- Test device list retrieval for both sessions
- Connect to devices in each session
- Verify screenshot isolation
- Check session state isolation
- Clean up connections

## Expected Behavior After Fix

✅ **Each browser tab maintains its own device connection**
✅ **Screenshots are session and device-specific**  
✅ **Multiple devices can be tested simultaneously**
✅ **Device connections don't interfere with each other**
✅ **Session state is properly isolated**

## Backward Compatibility

The fix maintains backward compatibility by:
- Keeping global variables for legacy code that might depend on them
- Preserving existing API endpoint signatures
- Maintaining the same frontend interface

## Future Improvements

1. **Complete Global Variable Removal**: Eventually remove all global device variables once all code is verified to use session-based tracking
2. **Session Management UI**: Add UI indicators showing which session is connected to which device
3. **Session Cleanup**: Implement automatic session cleanup when browser tabs are closed
4. **Enhanced Logging**: Add session ID to all log messages for better debugging

## Troubleshooting

If device isolation issues persist:

1. **Clear browser storage**: Clear localStorage and sessionStorage
2. **Restart app instances**: Stop and restart both Flask instances
3. **Check logs**: Look for session ID in connection logs
4. **Verify ports**: Ensure each instance is using different ports
5. **Test with different browsers**: Use different browsers for each session to ensure complete isolation
