# End-to-End Testing Observations

**Date**: 2025-09-18

## Environment
- Backend port: 8085  
- GUI: PyQt via `python3 gui_app/launch_gui.py`  
- Test credentials: `<EMAIL>` / `test123`

## Steps and Results
1. Launched Qt GUI; backend startup timed out after 90 seconds (backend unreachable).  
2. Ran `curl http://localhost:8085/health`; received connection refused.  
3. Attempted manual backend launch (`secure_distribution_app/dist/SecureMobileAppAutomation`); still connection refused.  
4. <PERSON>gin screen did not appear; authentication not possible.

## Observations
- Secure distribution backend failed to start on port 8085.  
- GUI could not connect to backend; no login interface displayed.

## Next Actions
- Investigate backend startup failures and port configuration.  
- Verify presence and permissions of the backend executable.  
- Ensure all backend dependencies are installed and ports available.