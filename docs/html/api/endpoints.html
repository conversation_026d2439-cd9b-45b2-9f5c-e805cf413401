<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Reference - MobileAppAutomation Documentation</title>
    <style>
        :root {
            --primary-color: #4a6da7;
            --secondary-color: #333333;
            --background-color: #ffffff;
            --text-color: #333333;
            --code-background: #f5f7f9;
            --border-color: #e1e4e8;
            --link-color: #0366d6;
            --link-hover-color: #0253a5;
            --sidebar-width: 280px;
            --content-max-width: 800px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--code-background);
            border-right: 1px solid var(--border-color);
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            margin: 0;
            color: var(--primary-color);
        }

        .sidebar-header p {
            margin: 5px 0 0 0;
            font-size: 0.9rem;
            color: var(--secondary-color);
        }

        .sidebar-menu {
            margin: 0;
            padding: 0;
            list-style-type: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 5px 10px;
            color: var(--secondary-color);
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .sidebar-menu a:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--link-hover-color);
        }

        .sidebar-menu a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .section-title {
            font-size: 0.8rem;
            text-transform: uppercase;
            color: #666;
            margin: 15px 0 5px 10px;
        }

        .submenu {
            margin: 0 0 0 20px;
            padding: 0;
            list-style-type: none;
        }

        .content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 40px;
            max-width: var(--content-max-width);
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--secondary-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 {
            font-size: 2.2rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        h2 {
            font-size: 1.8rem;
        }

        h3 {
            font-size: 1.4rem;
        }

        a {
            color: var(--link-color);
            text-decoration: none;
        }

        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }

        code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            background-color: var(--code-background);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }

        pre {
            background-color: var(--code-background);
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }

        pre code {
            background-color: transparent;
            padding: 0;
        }

        blockquote {
            margin: 0;
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        table th, table td {
            padding: 6px 13px;
            border: 1px solid var(--border-color);
        }

        table th {
            background-color: var(--code-background);
            font-weight: bold;
        }

        table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        img {
            max-width: 100%;
            box-sizing: border-box;
        }

        .mermaid {
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding: 10px;
            }

            .content {
                margin-left: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>MobileAppAutomation</h1>
            <p>Documentation</p>
        </div>
        <ul class="sidebar-menu">
            <li><a href="index.html">Home</a></li>
            
            <div class="section-title">Overview</div>
            <li><a href="overview/project_overview.html">Project Overview</a></li>
            
            <div class="section-title">Architecture</div>
            <li><a href="architecture/overview.html">System Architecture</a></li>
            
            <div class="section-title">API Reference</div>
            <li><a href="api/endpoints.html">API Endpoints</a></li>
            
            <div class="section-title">Guides</div>
            <li><a href="guides/getting_started.html">Getting Started</a></li>
            <li><a href="guides/android_testing.html">Android Testing</a></li>
            <li><a href="guides/ios_testing.html">iOS Testing</a></li>
            <li><a href="guides/visual_testing.html">Visual Testing</a></li>
            
            <div class="section-title">Modules</div>
            <li><a href="modules/app.html">App Module</a></li>
            <li><a href="modules/utils.html">Utils Module</a></li>
            <li><a href="modules/actions.html">Actions Module</a></li>
            
            <div class="section-title">Troubleshooting</div>
            <li><a href="troubleshooting/common_issues.html">Common Issues</a></li>
        </ul>
    </div>
    <div class="content">
        <h1>API Reference</h1>

<p>This document describes the API endpoints available in the MobileAppAutomation system.</p>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html> 