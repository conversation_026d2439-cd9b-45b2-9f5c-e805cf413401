<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MobileAppAutomation Codebase Analysis - MobileAppAutomation Documentation</title>
    <style>
        :root {
            --primary-color: #4a6da7;
            --secondary-color: #333333;
            --background-color: #ffffff;
            --text-color: #333333;
            --code-background: #f5f7f9;
            --border-color: #e1e4e8;
            --link-color: #0366d6;
            --link-hover-color: #0253a5;
            --sidebar-width: 280px;
            --content-max-width: 800px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--code-background);
            border-right: 1px solid var(--border-color);
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            margin: 0;
            color: var(--primary-color);
        }

        .sidebar-header p {
            margin: 5px 0 0 0;
            font-size: 0.9rem;
            color: var(--secondary-color);
        }

        .sidebar-menu {
            margin: 0;
            padding: 0;
            list-style-type: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 5px 10px;
            color: var(--secondary-color);
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .sidebar-menu a:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--link-hover-color);
        }

        .sidebar-menu a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .section-title {
            font-size: 0.8rem;
            text-transform: uppercase;
            color: #666;
            margin: 15px 0 5px 10px;
        }

        .submenu {
            margin: 0 0 0 20px;
            padding: 0;
            list-style-type: none;
        }

        .content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 40px;
            max-width: var(--content-max-width);
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--secondary-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 {
            font-size: 2.2rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        h2 {
            font-size: 1.8rem;
        }

        h3 {
            font-size: 1.4rem;
        }

        a {
            color: var(--link-color);
            text-decoration: none;
        }

        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }

        code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            background-color: var(--code-background);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }

        pre {
            background-color: var(--code-background);
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }

        pre code {
            background-color: transparent;
            padding: 0;
        }

        blockquote {
            margin: 0;
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        table th, table td {
            padding: 6px 13px;
            border: 1px solid var(--border-color);
        }

        table th {
            background-color: var(--code-background);
            font-weight: bold;
        }

        table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        img {
            max-width: 100%;
            box-sizing: border-box;
        }

        .mermaid {
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding: 10px;
            }

            .content {
                margin-left: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>MobileAppAutomation</h1>
            <p>Documentation</p>
        </div>
        <ul class="sidebar-menu">
            <li><a href="index.html">Home</a></li>
            
            <div class="section-title">Overview</div>
            <li><a href="overview/project_overview.html">Project Overview</a></li>
            
            <div class="section-title">Architecture</div>
            <li><a href="architecture/overview.html">System Architecture</a></li>
            
            <div class="section-title">API Reference</div>
            <li><a href="api/endpoints.html">API Endpoints</a></li>
            
            <div class="section-title">Guides</div>
            <li><a href="guides/getting_started.html">Getting Started</a></li>
            <li><a href="guides/android_testing.html">Android Testing</a></li>
            <li><a href="guides/ios_testing.html">iOS Testing</a></li>
            <li><a href="guides/visual_testing.html">Visual Testing</a></li>
            
            <div class="section-title">Modules</div>
            <li><a href="modules/app.html">App Module</a></li>
            <li><a href="modules/utils.html">Utils Module</a></li>
            <li><a href="modules/actions.html">Actions Module</a></li>
            
            <div class="section-title">Troubleshooting</div>
            <li><a href="troubleshooting/common_issues.html">Common Issues</a></li>
        </ul>
    </div>
    <div class="content">
        <h1>MobileAppAutomation Codebase Analysis</h1>

<h2>Overview</h2>

<p>This document provides a summary of the analyzed codebase structure for the MobileAppAutomation project.</p>

<h2>Statistics</h2>
<ul><li><strong>Total Files</strong>: 244</li>
<li><strong>Lines of Code</strong>: 134886</li>
<li><strong>Functions</strong>: 845</li>
<li><strong>Classes</strong>: 99</li>
<li><strong>API Endpoints</strong>: 72</li></ul>

<h2>File Types</h2>
<ul><li><strong>.json</strong>: 35 files</li>
<li><strong>.py</strong>: 134 files</li>
<li><strong>.js</strong>: 46 files</li>
<li><strong>.css</strong>: 16 files</li>
<li><strong>.html</strong>: 13 files</li></ul>

<h2>API Endpoints</h2>
<ul><li><code>/api/report/initialize</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/screenshots/delete_all</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/logs/save</code> [POST] - <em>app/app.py</em></li>
<li><code>/</code> [GET] - <em>app/app.py</em></li>
<li><code>/test_suites</code> [GET] - <em>app/app.py</em></li>
<li><code>/screenshot</code> [GET] - <em>app/app.py</em></li>
<li><code>/screenshots/<path:filename></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reference_images</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reference_image_preview</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/devices</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/device/connect</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/device/disconnect</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/check_inspector</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/device/dimensions</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/screenshot</code> [GET, POST] - <em>app/app.py</em></li>
<li><code>/api/capture_image</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/tap</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/swipe</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/text</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/key</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/recording/start</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/recording/stop</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/recording/save</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/recording/list</code> [GET] - <em>app/app.py</em></li>
<li><code>/get_element_at_position</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/element/at_position</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/stop</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/execute_test_case</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/execute</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/execute_hook</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/page_source</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/session_info</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/test_cases/files</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/test_cases/load_file/<filename></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/session/info</code> [GET] - <em>app/app.py</em></li>
<li><code>/appium/inspect_element</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/test_cases/load/<filename></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/test_cases_for_multi_step</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/device/health_check</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/device/reconnect</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/device/uninstall_app</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/device/fix_emulator</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/recording/rename</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/recording/duplicate</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/action/swipeTillVisible</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/test_suites/list</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/test_suites/<suite_id></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/test_suites/create</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/test_suites/<suite_id>/update</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/test_suites/<suite_id>/duplicate</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/test_suites/<suite_id></code> [DELETE] - <em>app/app.py</em></li>
<li><code>/api/delete_test_case/<filename></code> [DELETE] - <em>app/app.py</em></li>
<li><code>/api/test_suites/<suite_id>/run</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/reports/latest</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reports/list</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reports/download/<path:filename></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reports/download_zip/<path:filename></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reports/regenerate/<path:report_id></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/database/clear_screenshots</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/database/reset</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/reports/delete/<path:filename></code> [DELETE] - <em>app/app.py</em></li>
<li><code>/api/reports/get_test_data/<suite_id></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/reports/generate/<suite_id></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/generate_report</code> [POST] - <em>app/app.py</em></li>
<li><code>/reports/<path:filename></code> [GET] - <em>app/app.py</em></li>
<li><code>/api/upload_media</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/settings</code> [GET] - <em>app/app.py</em></li>
<li><code>/api/settings</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/capture_image_area</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/ios/set_clipboard</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/ios/paste_clipboard</code> [POST] - <em>app/app.py</em></li>
<li><code>/api/text_detection</code> [POST] - <em>app/app.py</em></li></ul>

<h2>Module Structure</h2>

<h3>app</h3>
<ul><li>environments.json</li>
<li>__init__.py</li>
<li>app.py</li>
<li>test_suites_manager.py</li></ul>
<h3>app/utils</h3>
<ul><li>id_generator.py</li>
<li>text_detector.py</li>
<li>database_update.py</li>
<li>screenshot_handler.py</li>
<li>text_detection.py</li>
<li>reportGenerator.py</li>
<li>coordinate_validator.py</li>
<li>openReport.js</li>
<li>test_case_manager.py</li>
<li>database.py</li>
<li>reset_database.py</li>
<li>appium_device_controller.py</li>
<li>ios_device.py</li>
<li>recorder.py</li>
<li>__init__.py</li>
<li>reportGenerator.js</li>
<li>test.json</li>
<li>image_matcher.py</li>
<li>parameter_utils.py</li>
<li>player.py</li>
<li>global_values_db.py</li>
<li>file_utils.py</li>
<li>random_data_generator.py</li>
<li>screenshot_manager.py</li>
<li>build_data_json.py</li>
<li>id_generator.py</li>
<li>text_detector.py</li>
<li>database_update.py</li>
<li>screenshot_handler.py</li>
<li>text_detection.py</li>
<li>reportGenerator.py</li>
<li>coordinate_validator.py</li>
<li>openReport.js</li>
<li>test_case_manager.py</li>
<li>database.py</li>
<li>reset_database.py</li>
<li>appium_device_controller.py</li>
<li>ios_device.py</li>
<li>recorder.py</li>
<li>__init__.py</li>
<li>reportGenerator.js</li>
<li>test.json</li>
<li>image_matcher.py</li>
<li>parameter_utils.py</li>
<li>player.py</li>
<li>global_values_db.py</li>
<li>file_utils.py</li>
<li>random_data_generator.py</li>
<li>screenshot_manager.py</li>
<li>build_data_json.py</li></ul>
<h3>app/static/css</h3>
<ul><li>modern-styles.css</li>
<li>fixed-device-screen.css</li>
<li>test-case.css</li>
<li>execution-overlay.css</li>
<li>fallback-locators.css</li>
<li>test-cases-styles.css</li>
<li>style.css</li>
<li>test-suites-styles.css</li>
<li>modern-styles.css</li>
<li>fixed-device-screen.css</li>
<li>test-case.css</li>
<li>execution-overlay.css</li>
<li>fallback-locators.css</li>
<li>test-cases-styles.css</li>
<li>style.css</li>
<li>test-suites-styles.css</li></ul>
<h3>app/static/js</h3>
<ul><li>hook-action.js</li>
<li>action-manager.js</li>
<li>test_suites.js</li>
<li>execution-overlay.js</li>
<li>action-description.js</li>
<li>main.js</li>
<li>socket-handlers.js</li>
<li>tap-fallback-manager.js</li>
<li>execution-manager.js</li>
<li>random-data-generator.js</li>
<li>utils.js</li>
<li>reports.js</li>
<li>multi-step-action.js</li>
<li>fixed-device-screen.js</li>
<li>app.js</li>
<li>settings.js</li>
<li>hook-action.js</li>
<li>action-manager.js</li>
<li>test_suites.js</li>
<li>execution-overlay.js</li>
<li>action-description.js</li>
<li>main.js</li>
<li>socket-handlers.js</li>
<li>tap-fallback-manager.js</li>
<li>execution-manager.js</li>
<li>random-data-generator.js</li>
<li>utils.js</li>
<li>reports.js</li>
<li>multi-step-action.js</li>
<li>fixed-device-screen.js</li>
<li>app.js</li>
<li>settings.js</li></ul>
<h3>app/static/js/actions</h3>
<ul><li>swipe-till-visible.js</li>
<li>swipe-till-visible.js</li></ul>
<h3>app/static/js/modules</h3>
<ul><li>fallback-locators.js</li>
<li>tap-fallback-manager.js</li>
<li>ElementInteractions.js</li>
<li>TestCaseManager.js</li>
<li>fallback-locators.js</li>
<li>tap-fallback-manager.js</li>
<li>ElementInteractions.js</li>
<li>TestCaseManager.js</li></ul>
<h3>app/static/templates</h3>
<ul><li>index.html</li>
<li>index.html</li></ul>
<h3>app/actions</h3>
<ul><li>tap_if_image_exists_action.py</li>
<li>multi_step_action.py</li>
<li>swipe_action.py</li>
<li>get_param_action.py</li>
<li>wait_action.py</li>
<li>terminate_app_action.py</li>
<li>double_click_image_action.py</li>
<li>uninstall_app_action.py</li>
<li>text_action.py</li>
<li>wait_till_action.py</li>
<li>__init__.py</li>
<li>base_action.py</li>
<li>hook_action.py</li>
<li>input_text_action.py</li>
<li>send_keys_action.py</li>
<li>set_param_action.py</li>
<li>airplane_mode_action.py</li>
<li>ios_functions_action.py</li>
<li>swipe_till_visible_action.py</li>
<li>click_image_action.py</li>
<li>tap_action.py</li>
<li>if_else_steps_action.py</li>
<li>key_action.py</li>
<li>add_media_action.py</li>
<li>hide_keyboard_action.py</li>
<li>tap_and_type_action.py</li>
<li>tap_on_text_action.py</li>
<li>launch_app_action.py</li>
<li>wait_element_action.py</li>
<li>compare_value_action.py</li>
<li>device_back_action.py</li>
<li>click_element_action.py</li>
<li>random_data_action.py</li>
<li>get_value_action.py</li>
<li>action_factory.py</li>
<li>restart_app_action.py</li>
<li>double_tap_action.py</li>
<li>add_log_action.py</li>
<li>tap_if_image_exists_action.py</li>
<li>multi_step_action.py</li>
<li>swipe_action.py</li>
<li>get_param_action.py</li>
<li>wait_action.py</li>
<li>terminate_app_action.py</li>
<li>double_click_image_action.py</li>
<li>uninstall_app_action.py</li>
<li>text_action.py</li>
<li>wait_till_action.py</li>
<li>__init__.py</li>
<li>base_action.py</li>
<li>hook_action.py</li>
<li>input_text_action.py</li>
<li>send_keys_action.py</li>
<li>set_param_action.py</li>
<li>airplane_mode_action.py</li>
<li>ios_functions_action.py</li>
<li>swipe_till_visible_action.py</li>
<li>click_image_action.py</li>
<li>tap_action.py</li>
<li>if_else_steps_action.py</li>
<li>key_action.py</li>
<li>add_media_action.py</li>
<li>hide_keyboard_action.py</li>
<li>tap_and_type_action.py</li>
<li>tap_on_text_action.py</li>
<li>launch_app_action.py</li>
<li>wait_element_action.py</li>
<li>compare_value_action.py</li>
<li>device_back_action.py</li>
<li>click_element_action.py</li>
<li>random_data_action.py</li>
<li>get_value_action.py</li>
<li>action_factory.py</li>
<li>restart_app_action.py</li>
<li>double_tap_action.py</li>
<li>add_log_action.py</li></ul>
<h3>app/templates</h3>
<ul><li>index.html</li>
<li>tap_on_text_form.html</li>
<li>settings.html</li>
<li>tap-fallback-modal.html</li>
<li>test_suites.html</li>
<li>index.html</li>
<li>tap_on_text_form.html</li>
<li>settings.html</li>
<li>tap-fallback-modal.html</li>
<li>test_suites.html</li></ul>
<h3>app/routes</h3>
<ul><li>__init__.py</li>
<li>random_data_routes.py</li>
<li>__init__.py</li>
<li>random_data_routes.py</li></ul>
<h3>test_cases</h3>
<ul><li>Postcode_Flow_Copy_20250513180904_20250513180904.json</li>
<li>Delivery__CNC_Copy_20250513201112_20250513201112.json</li>
<li>Postcode_Flow_Copy_20250518114624_20250518114624.json</li>
<li>OnePass_Account_Signin_20250510101051.json</li>
<li>Delivery_Buy_Steps_20250512194232.json</li>
<li>All_Sign_ins_Copy_20250514181123_20250514181123.json</li>
<li>WishList_20250510110236.json</li>
<li>SampleApp_20250401224106.json</li>
<li>KmartProdSignin_Copy_20250501144222_20250501144222.json</li>
<li>Delivery__Buy_20250505165058.json</li>
<li>Delivery_Buy_Steps_Copy_20250513195816_20250513195816.json</li>
<li>Postcode_Flow_20250502104451.json</li>
<li>AU_MyAccount_Copy_20250514192836_20250514192836.json</li>
<li>Browse__PDP_Copy_20250514183633_20250514183633.json</li>
<li>WishList_Copy_20250514185153_20250514185153.json</li>
<li>Kmart_Prod__Onboarding2_Copy_20250513175727_20250513175727.json</li>
<li>Others_20250512190312.json</li>
<li>apple_health_Copy_20250409201903.json</li>
<li>KmartProdSignin_20250426221008.json</li>
<li>Delivery__CNC_20250505163250.json</li>
<li>apple_health_20250407210435.json</li>
<li>Browse__PDP_20250510095542.json</li>
<li>KmartSignin_Copy_20250513181428_20250513181428.json</li>
<li>Login_User_CNC_payment_20250501215014.json</li>
<li>All_Sign_ins_20250501131834.json</li>
<li>KmartProdSignin_Copy_20250514190524_20250514190524.json</li>
<li>Guest_Buy_20250424191014.json</li>
<li>installkmartau_20250503214553.json</li>
<li>Kmart_Prod__Onboarding1_20250422134746.json</li>
<li>Kmart_Prod__Onboarding2_20250422143908.json</li>
<li>health2_20250408214926.json</li>
<li>AU_MyAccount_20250506181929.json</li></ul>
<h3>image_comparison</h3>
<ul><li>compare_images.py</li>
<li>create_sample_images.py</li>
<li>test_different.py</li>
<li>generate_html_report.py</li></ul>
<h3>image_comparison/report</h3>
<ul><li>report.html</li></ul>
<h3>scripts</h3>
<ul><li>convert_multistep_test_cases.py</li>
<li>add_action_ids.py</li>
<li>update_database_action_ids.py</li></ul>

    </div>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html> 