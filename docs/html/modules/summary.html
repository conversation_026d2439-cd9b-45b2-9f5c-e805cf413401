<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modules Summary - MobileAppAutomation Documentation</title>
    <style>
        :root {
            --primary-color: #4a6da7;
            --secondary-color: #333333;
            --background-color: #ffffff;
            --text-color: #333333;
            --code-background: #f5f7f9;
            --border-color: #e1e4e8;
            --link-color: #0366d6;
            --link-hover-color: #0253a5;
            --sidebar-width: 280px;
            --content-max-width: 800px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--code-background);
            border-right: 1px solid var(--border-color);
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            margin: 0;
            color: var(--primary-color);
        }

        .sidebar-header p {
            margin: 5px 0 0 0;
            font-size: 0.9rem;
            color: var(--secondary-color);
        }

        .sidebar-menu {
            margin: 0;
            padding: 0;
            list-style-type: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 5px 10px;
            color: var(--secondary-color);
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .sidebar-menu a:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--link-hover-color);
        }

        .sidebar-menu a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .section-title {
            font-size: 0.8rem;
            text-transform: uppercase;
            color: #666;
            margin: 15px 0 5px 10px;
        }

        .submenu {
            margin: 0 0 0 20px;
            padding: 0;
            list-style-type: none;
        }

        .content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 40px;
            max-width: var(--content-max-width);
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--secondary-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 {
            font-size: 2.2rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        h2 {
            font-size: 1.8rem;
        }

        h3 {
            font-size: 1.4rem;
        }

        a {
            color: var(--link-color);
            text-decoration: none;
        }

        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }

        code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            background-color: var(--code-background);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }

        pre {
            background-color: var(--code-background);
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }

        pre code {
            background-color: transparent;
            padding: 0;
        }

        blockquote {
            margin: 0;
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        table th, table td {
            padding: 6px 13px;
            border: 1px solid var(--border-color);
        }

        table th {
            background-color: var(--code-background);
            font-weight: bold;
        }

        table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        img {
            max-width: 100%;
            box-sizing: border-box;
        }

        .mermaid {
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding: 10px;
            }

            .content {
                margin-left: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>MobileAppAutomation</h1>
            <p>Documentation</p>
        </div>
        <ul class="sidebar-menu">
            <li><a href="index.html">Home</a></li>
            
            <div class="section-title">Overview</div>
            <li><a href="overview/project_overview.html">Project Overview</a></li>
            
            <div class="section-title">Architecture</div>
            <li><a href="architecture/overview.html">System Architecture</a></li>
            
            <div class="section-title">API Reference</div>
            <li><a href="api/endpoints.html">API Endpoints</a></li>
            
            <div class="section-title">Guides</div>
            <li><a href="guides/getting_started.html">Getting Started</a></li>
            <li><a href="guides/android_testing.html">Android Testing</a></li>
            <li><a href="guides/ios_testing.html">iOS Testing</a></li>
            <li><a href="guides/visual_testing.html">Visual Testing</a></li>
            
            <div class="section-title">Modules</div>
            <li><a href="modules/app.html">App Module</a></li>
            <li><a href="modules/utils.html">Utils Module</a></li>
            <li><a href="modules/actions.html">Actions Module</a></li>
            
            <div class="section-title">Troubleshooting</div>
            <li><a href="troubleshooting/common_issues.html">Common Issues</a></li>
        </ul>
    </div>
    <div class="content">
        <h1>Modules Summary</h1>

<p>This page provides a summary of all modules in the MobileAppAutomation application.</p>

<h2>app</h2>

<h3>environments.json</h3>

<p>---</p>

<h3>__init__.py</h3>

<p>---</p>

<h3>app.py</h3>

<strong>Functions:</strong>
<ul><li><code>shutdown_handler()</code></li>
<li><code>cleanup_screenshots()</code></li>
<li><code>cleanup_app_screenshots()</code></li>
<li><code>sigusr1_handler(signum, frame)</code></li>
<li><code>initialize_report_directory()</code></li>
<li><code>delete_all_screenshots()</code></li>
<li><code>save_action_logs()</code></li>
<li><code>initialize_report_directory()</code></li>
<li><code>load_config()</code></li>
<li><code>save_config(config)</code></li>
<li><code>encode_image_to_base64(image_path)</code></li>
<li><code>index()</code></li>
<li><code>test_suites()</code></li>
<li><code>get_screenshot()</code></li>
<li><code>serve_screenshot(filename)</code></li>
<li><code>get_reference_images()</code></li>
<li><code>get_reference_image_preview()</code></li>
<li><code>get_devices()</code></li>
<li><code>connect_device()</code></li>
<li><code>disconnect_device()</code></li>
<li><code>check_inspector()</code></li>
<li><code>get_device_dimensions()</code></li>
<li><code>take_screenshot()</code></li>
<li><code>capture_image()</code></li>
<li><code>tap_action()</code></li>
<li><code>swipe_action()</code></li>
<li><code>text_action()</code></li>
<li><code>key_action()</code></li>
<li><code>start_recording()</code></li>
<li><code>stop_recording()</code></li>
<li><code>save_recording()</code></li>
<li><code>list_recordings()</code></li>
<li><code>get_element_at_position_legacy()</code></li>
<li><code>get_element_at_position()</code></li>
<li><code>stop_execution()</code></li>
<li><code>execute_test_case()</code></li>
<li><code>execute_single_action()</code></li>
<li><code>execute_hook_action()</code></li>
<li><code>get_page_source()</code></li>
<li><code>get_session_info()</code></li>
<li><code>list_test_case_files()</code></li>
<li><code>load_test_case_file(filename)</code></li>
<li><code>get_session_info_route()</code></li>
<li><code>inspect_element()</code></li>
<li><code>load_specific_test_case(filename)</code></li>
<li><code>get_test_cases_for_multi_step()</code></li>
<li><code>check_device_health()</code></li>
<li><code>reconnect_device()</code></li>
<li><code>uninstall_app()</code></li>
<li><code>fix_emulator_issues()</code></li>
<li><code>rename_recording()</code></li>
<li><code>duplicate_recording()</code></li>
<li><code>swipe_till_visible_action()</code></li>
<li><code>list_test_suites()</code></li>
<li><code>get_test_suite(suite_id)</code></li>
<li><code>create_test_suite()</code></li>
<li><code>update_test_suite_route(suite_id)</code></li>
<li><code>duplicate_test_suite(suite_id)</code></li>
<li><code>delete_test_suite(suite_id)</code></li>
<li><code>delete_test_case_route(filename)</code></li>
<li><code>run_test_suite(suite_id)</code></li>
<li><code>get_latest_report()</code></li>
<li><code>get_reports_list()</code></li>
<li><code>download_report(filename)</code></li>
<li><code>download_report_zip(filename)</code></li>
<li><code>regenerate_report(report_id)</code></li>
<li><code>clear_screenshots_route()</code></li>
<li><code>reset_database_route()</code></li>
<li><code>delete_report(filename)</code></li>
<li><code>get_test_data(suite_id)</code></li>
<li><code>generate_report_from_suite_id(suite_id)</code></li>
<li><code>generate_report()</code></li>
<li><code>serve_report(filename)</code></li>
<li><code>upload_media()</code></li>
<li><code>get_settings()</code></li>
<li><code>update_settings()</code></li>
<li><code>capture_image_area()</code></li>
<li><code>set_clipboard()</code></li>
<li><code>paste_clipboard()</code></li>
<li><code>detect_text()</code></li>
<li><code>emit(self, event, data)</code></li>
<li><code>__init__(self, timeout, step_name, test_case_name)</code></li>
<li><code>start(self)</code></li>
<li><code>cancel(self)</code></li>
<li><code>__init__(self, initial_value)</code></li>
<li><code>__str__(self)</code></li>
<li><code>__repr__(self)</code></li>
<li><code>timeout_handler()</code></li>
<li><code>update_directory_path(content, dir_key, dir_path)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DummySocketIO</code></li>
<li><code>emit(self, event, data)</code></li>
<li><code>StepExecutionTimeout</code></li>
<li><code>__init__(self, timeout, step_name, test_case_name)</code></li>
<li><code>start(self)</code></li>
<li><code>cancel(self)</code></li>
<li><code>IndexHolder</code></li>
<li><code>__init__(self, initial_value)</code></li>
<li><code>__str__(self)</code></li>
<li><code>__repr__(self)</code></li></ul>

<p>---</p>

<h3>test_suites_manager.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self)</code></li>
<li><code>load_test_suites(self)</code></li>
<li><code>create_test_suite(self, name, description, test_cases)</code></li>
<li><code>update_test_suite(self, suite_id, updated_data)</code></li>
<li><code>duplicate_test_suite(self, suite_id)</code></li>
<li><code>delete_test_suite(self, suite_id)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TestSuitesManager</code></li>
<li><code>__init__(self)</code></li>
<li><code>load_test_suites(self)</code></li>
<li><code>create_test_suite(self, name, description, test_cases)</code></li>
<li><code>update_test_suite(self, suite_id, updated_data)</code></li>
<li><code>duplicate_test_suite(self, suite_id)</code></li>
<li><code>delete_test_suite(self, suite_id)</code></li></ul>

<p>---</p>

<h2>app/utils</h2>

<h3>id_generator.py</h3>

<strong>Functions:</strong>
<ul><li><code>generate_action_id(length)</code></li>
<li><code>add_action_ids_to_test_case(test_case_data)</code></li></ul>

<p>---</p>

<h3>text_detector.py</h3>

<strong>Functions:</strong>
<ul><li><code>detect_text_with_tesseract(image_path, text_to_find, output_dir, similarity_threshold)</code></li>
<li><code>get_text_coordinates(image_path, text_to_find, device_width, device_height, output_dir, similarity_threshold)</code></li>
<li><code>__init__(self)</code></li>
<li><code>find_text(self, image_path, text_to_find, similarity_threshold)</code></li>
<li><code>_calculate_similarity(self, text1, text2)</code></li>
<li><code>_find_partial_matches(self, data, text_to_find, similarity_threshold)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TextDetector</code></li>
<li><code>__init__(self)</code></li>
<li><code>find_text(self, image_path, text_to_find, similarity_threshold)</code></li>
<li><code>_calculate_similarity(self, text1, text2)</code></li>
<li><code>_find_partial_matches(self, data, text_to_find, similarity_threshold)</code></li></ul>

<p>---</p>

<h3>database_update.py</h3>

<strong>Functions:</strong>
<ul><li><code>update_database_schema()</code></li></ul>

<p>---</p>

<h3>screenshot_handler.py</h3>

<strong>Functions:</strong>
<ul><li><code>ensure_directory_exists(directory_path)</code></li>
<li><code>copy_screenshots_to_report(report_dir)</code></li>
<li><code>copy_latest_screenshot_to_report(report_dir, action_id)</code></li></ul>

<p>---</p>

<h3>text_detection.py</h3>

<strong>Functions:</strong>
<ul><li><code>detect_text_in_image(image_path, text_to_find, output_dir)</code></li>
<li><code>scale_coordinates(x, y, img_width, img_height, device_width, device_height)</code></li></ul>

<p>---</p>

<h3>reportGenerator.py</h3>

<strong>Functions:</strong>
<ul><li><code>getLatestReportUrl()</code></li>
<li><code>ensure_report_directories(reports_dir, report_timestamp)</code></li>
<li><code>copy_screenshots_for_report(report_timestamp, test_data)</code></li>
<li><code>save_action_logs_to_file(logs, report_dir)</code></li>
<li><code>regenerate_report_from_data_json(report_dir)</code></li>
<li><code>create_zip_archive(report_dir)</code></li>
<li><code>prepare_steps_for_report(test_suite_data)</code></li>
<li><code>generateReport(test_suite_data, test_case_file)</code></li>
<li><code>copy_screenshots_from_database(report_dir, test_data)</code></li>
<li><code>ensure_report_screenshots(report_dir, test_data)</code></li>
<li><code>get_image_number(filename)</code></li></ul>

<p>---</p>

<h3>coordinate_validator.py</h3>

<strong>Functions:</strong>
<ul><li><code>validate_coordinates(coords, device_width, device_height)</code></li></ul>

<p>---</p>

<h3>openReport.js</h3>

<strong>Functions:</strong>
<ul><li><code>openLatestReport()</code></li>
<li><code>openReport(reportName)</code></li>
<li><code>openUrlInBrowser(url)</code></li></ul>

<p>---</p>

<h3>test_case_manager.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, test_cases_dir)</code></li>
<li><code>get_test_cases(self)</code></li>
<li><code>load_test_case(self, filename)</code></li>
<li><code>save_test_case(self, test_case_data, filename, is_save_as)</code></li>
<li><code>delete_test_case(self, filename)</code></li>
<li><code>rename_test_case(self, filename, new_name)</code></li>
<li><code>duplicate_test_case(self, filename)</code></li>
<li><code>clean_duplicates(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TestCaseManager</code></li>
<li><code>__init__(self, test_cases_dir)</code></li>
<li><code>get_test_cases(self)</code></li>
<li><code>load_test_case(self, filename)</code></li>
<li><code>save_test_case(self, test_case_data, filename, is_save_as)</code></li>
<li><code>delete_test_case(self, filename)</code></li>
<li><code>rename_test_case(self, filename, new_name)</code></li>
<li><code>duplicate_test_case(self, filename)</code></li>
<li><code>clean_duplicates(self)</code></li></ul>

<p>---</p>

<h3>database.py</h3>

<strong>Functions:</strong>
<ul><li><code>ensure_db_directory()</code></li>
<li><code>init_db()</code></li>
<li><code>update_execution_tracking_schema()</code></li>
<li><code>clear_test_tables()</code></li>
<li><code>track_test_execution(suite_id, test_idx, filename, status, retry_count, max_retries, error, in_progress, step_idx, action_type, action_params, action_id)</code></li>
<li><code>clear_execution_tracking()</code></li>
<li><code>get_screenshots_for_suite(suite_id)</code></li>
<li><code>get_test_steps_for_suite(suite_id)</code></li>
<li><code>get_execution_tracking_for_suite(suite_id)</code></li>
<li><code>get_action_id_for_step(suite_id, test_idx, step_idx)</code></li>
<li><code>get_test_execution_status(suite_id, test_idx, filename)</code></li>
<li><code>reset_test_case_execution_tracking(suite_id, test_idx)</code></li>
<li><code>has_test_case_failures(suite_id, test_idx, force_return_true)</code></li>
<li><code>get_hook_execution_count(suite_id, test_idx, step_idx, action_type)</code></li>
<li><code>clear_database()</code></li>
<li><code>save_test_suite(suite_data)</code></li>
<li><code>get_test_suite(suite_id)</code></li>
<li><code>get_latest_test_suite()</code></li>
<li><code>save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id)</code></li>
<li><code>update_test_suite(suite_id, test_suite_data)</code></li>
<li><code>get_all_test_suites()</code></li>
<li><code>delete_report_by_name(report_name)</code></li>
<li><code>delete_report_by_dir(dir_name)</code></li>
<li><code>reset_database()</code></li>
<li><code>check_database_state()</code></li>
<li><code>get_test_case_by_id(test_case_id)</code></li>
<li><code>save_screenshot_info(suite_id, test_idx, step_idx, filename, path, retry_number, action_id)</code></li>
<li><code>check_screenshot_exists(action_id)</code></li>
<li><code>update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id)</code></li>
<li><code>update_action_id(suite_id, test_idx, step_idx, action_id)</code></li>
<li><code>clear_screenshots()</code></li></ul>

<p>---</p>

<h3>reset_database.py</h3>

<strong>Functions:</strong>
<ul><li><code>reset_database()</code></li>
<li><code>create_new_database()</code></li>
<li><code>check_database()</code></li></ul>

<p>---</p>

<h3>appium_device_controller.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self)</code></li>
<li><code>test_airtest_connection(self, device_id, platform_name)</code></li>
<li><code>_kill_existing_processes(self)</code></li>
<li><code>check_appium_server(self)</code></li>
<li><code>restart_appium_server(self)</code></li>
<li><code>_ensure_appium_server(self)</code></li>
<li><code>get_devices(self)</code></li>
<li><code>connect_to_device(self, device_id, options, platform)</code></li>
<li><code>_attempt_device_connection(self, device_id, options, platform)</code></li>
<li><code>_connect_with_timeout(self)</code></li>
<li><code>_init_platform_helpers(self)</code></li>
<li><code>_is_port_in_use(self, port)</code></li>
<li><code>_start_iproxy(self, device_id, local_port, device_port)</code></li>
<li><code>_connect_ios_device(self, options)</code></li>
<li><code>_connect_with_airtest(self, device_id, wda_url)</code></li>
<li><code>reconnect_device(self)</code></li>
<li><code>disconnect(self)</code></li>
<li><code>shutdown(self)</code></li>
<li><code>_init_airtest(self, device_id)</code></li>
<li><code>_init_minimal_ios_device(self)</code></li>
<li><code>_ensure_airtest_connected(self)</code></li>
<li><code>take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>find_element_at_position(self, x, y)</code></li>
<li><code>get_element_at_position(self, x, y)</code></li>
<li><code>input_text(self, locator_type, locator_value, text, clear_first, timeout)</code></li>
<li><code>tap(self, x, y, duration)</code></li>
<li><code>tap_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>tap_on_image(self, image_path, timeout, confidence)</code></li>
<li><code>tap_on_text(self, text, timeout, fuzzy)</code></li>
<li><code>double_tap(self, x, y)</code></li>
<li><code>swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>long_press(self, x, y, duration)</code></li>
<li><code>press_button(self, button_name)</code></li>
<li><code>launch_app(self, bundle_id)</code></li>
<li><code>terminate_app(self, bundle_id)</code></li>
<li><code>uninstall_app(self, package_id)</code></li>
<li><code>_uninstall_app_ios_direct(self, bundle_id)</code></li>
<li><code>session_id(self)</code></li>
<li><code>set_clipboard(self, content)</code></li>
<li><code>paste_clipboard(self)</code></li>
<li><code>capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)</code></li>
<li><code>find_element_with_fallback(self, locators, timeout)</code></li>
<li><code>find_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>find_image(self, image_path, threshold, timeout)</code></li>
<li><code>get_screen_text(self)</code></li>
<li><code>get_screen_content(self)</code></li>
<li><code>_run_adb_command(self, command, device_id)</code></li>
<li><code>recover_connection(self)</code></li>
<li><code>_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>is_session_active(self)</code></li>
<li><code>get_driver(self)</code></li>
<li><code>get_device_dimensions(self)</code></li>
<li><code>__init__(self, driver)</code></li>
<li><code>tap(self, x, y, element, count)</code></li>
<li><code>press(self, x, y, element, pressure)</code></li>
<li><code>long_press(self, x, y, element, duration)</code></li>
<li><code>wait(self, ms)</code></li>
<li><code>move_to(self, x, y, element)</code></li>
<li><code>release(self)</code></li>
<li><code>perform(self)</code></li>
<li><code>get_page_source()</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AppiumDeviceController</code></li>
<li><code>__init__(self)</code></li>
<li><code>test_airtest_connection(self, device_id, platform_name)</code></li>
<li><code>_kill_existing_processes(self)</code></li>
<li><code>check_appium_server(self)</code></li>
<li><code>restart_appium_server(self)</code></li>
<li><code>_ensure_appium_server(self)</code></li>
<li><code>get_devices(self)</code></li>
<li><code>connect_to_device(self, device_id, options, platform)</code></li>
<li><code>_attempt_device_connection(self, device_id, options, platform)</code></li>
<li><code>_connect_with_timeout(self)</code></li>
<li><code>_init_platform_helpers(self)</code></li>
<li><code>_is_port_in_use(self, port)</code></li>
<li><code>_start_iproxy(self, device_id, local_port, device_port)</code></li>
<li><code>_connect_ios_device(self, options)</code></li>
<li><code>_connect_with_airtest(self, device_id, wda_url)</code></li>
<li><code>reconnect_device(self)</code></li>
<li><code>disconnect(self)</code></li>
<li><code>shutdown(self)</code></li>
<li><code>_init_airtest(self, device_id)</code></li>
<li><code>_init_minimal_ios_device(self)</code></li>
<li><code>_ensure_airtest_connected(self)</code></li>
<li><code>take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>find_element_at_position(self, x, y)</code></li>
<li><code>get_element_at_position(self, x, y)</code></li>
<li><code>input_text(self, locator_type, locator_value, text, clear_first, timeout)</code></li>
<li><code>tap(self, x, y, duration)</code></li>
<li><code>tap_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>tap_on_image(self, image_path, timeout, confidence)</code></li>
<li><code>tap_on_text(self, text, timeout, fuzzy)</code></li>
<li><code>double_tap(self, x, y)</code></li>
<li><code>swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>long_press(self, x, y, duration)</code></li>
<li><code>press_button(self, button_name)</code></li>
<li><code>launch_app(self, bundle_id)</code></li>
<li><code>terminate_app(self, bundle_id)</code></li>
<li><code>uninstall_app(self, package_id)</code></li>
<li><code>_uninstall_app_ios_direct(self, bundle_id)</code></li>
<li><code>session_id(self)</code></li>
<li><code>set_clipboard(self, content)</code></li>
<li><code>paste_clipboard(self)</code></li>
<li><code>capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)</code></li>
<li><code>find_element_with_fallback(self, locators, timeout)</code></li>
<li><code>find_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>find_image(self, image_path, threshold, timeout)</code></li>
<li><code>get_screen_text(self)</code></li>
<li><code>get_screen_content(self)</code></li>
<li><code>_run_adb_command(self, command, device_id)</code></li>
<li><code>recover_connection(self)</code></li>
<li><code>_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>is_session_active(self)</code></li>
<li><code>get_driver(self)</code></li>
<li><code>get_device_dimensions(self)</code></li>
<li><code>TouchAction</code></li>
<li><code>__init__(self, driver)</code></li>
<li><code>tap(self, x, y, element, count)</code></li>
<li><code>press(self, x, y, element, pressure)</code></li>
<li><code>long_press(self, x, y, element, duration)</code></li>
<li><code>wait(self, ms)</code></li>
<li><code>move_to(self, x, y, element)</code></li>
<li><code>release(self)</code></li>
<li><code>perform(self)</code></li></ul>

<p>---</p>

<h3>ios_device.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_id, wda_url)</code></li>
<li><code>_get_screen_dimensions(self)</code></li>
<li><code>touch(self, x, y, duration)</code></li>
<li><code>double_click(self, x, y)</code></li>
<li><code>swipe(self, fx, fy, tx, ty, duration)</code></li>
<li><code>snapshot(self, filename, save_debug)</code></li>
<li><code>home(self)</code></li>
<li><code>press(self, key)</code></li>
<li><code>alert_accept(self)</code></li>
<li><code>alert_dismiss(self)</code></li>
<li><code>alert_click(self, button_text)</code></li>
<li><code>alert_wait(self, timeout)</code></li>
<li><code>alert_exists(self)</code></li>
<li><code>alert_buttons(self)</code></li>
<li><code>get_clipboard(self, wda_bundle_id)</code></li>
<li><code>set_clipboard(self, content, wda_bundle_id)</code></li>
<li><code>text(self, text, enter)</code></li>
<li><code>_press_return_key(self)</code></li>
<li><code>paste(self, wda_bundle_id)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>device_status(self)</code></li>
<li><code>get_current_resolution(self)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>push(self, local_path, remote_path)</code></li>
<li><code>uninstall_app(self, bundle_id)</code></li>
<li><code>clear_app(self, bundle_id)</code></li>
<li><code>lock(self)</code></li>
<li><code>unlock(self)</code></li>
<li><code>is_locked(self)</code></li>
<li><code>list_app(self)</code></li>
<li><code>find_template(self, template_path, threshold, timeout, interval)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>MinimalIOSDevice</code></li>
<li><code>__init__(self, device_id, wda_url)</code></li>
<li><code>_get_screen_dimensions(self)</code></li>
<li><code>touch(self, x, y, duration)</code></li>
<li><code>double_click(self, x, y)</code></li>
<li><code>swipe(self, fx, fy, tx, ty, duration)</code></li>
<li><code>snapshot(self, filename, save_debug)</code></li>
<li><code>home(self)</code></li>
<li><code>press(self, key)</code></li>
<li><code>alert_accept(self)</code></li>
<li><code>alert_dismiss(self)</code></li>
<li><code>alert_click(self, button_text)</code></li>
<li><code>alert_wait(self, timeout)</code></li>
<li><code>alert_exists(self)</code></li>
<li><code>alert_buttons(self)</code></li>
<li><code>get_clipboard(self, wda_bundle_id)</code></li>
<li><code>set_clipboard(self, content, wda_bundle_id)</code></li>
<li><code>text(self, text, enter)</code></li>
<li><code>_press_return_key(self)</code></li>
<li><code>paste(self, wda_bundle_id)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>device_status(self)</code></li>
<li><code>get_current_resolution(self)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>push(self, local_path, remote_path)</code></li>
<li><code>uninstall_app(self, bundle_id)</code></li>
<li><code>clear_app(self, bundle_id)</code></li>
<li><code>lock(self)</code></li>
<li><code>unlock(self)</code></li>
<li><code>is_locked(self)</code></li>
<li><code>list_app(self)</code></li>
<li><code>find_template(self, template_path, threshold, timeout, interval)</code></li></ul>

<p>---</p>

<h3>recorder.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_controller)</code></li>
<li><code>start_recording(self)</code></li>
<li><code>stop_recording(self)</code></li>
<li><code>add_action(self, action_data)</code></li>
<li><code>_process_elements_in_background(self, screenshot_path, action_index)</code></li>
<li><code>_detect_changes_fast(self, prev_screenshot, curr_screenshot)</code></li>
<li><code>_detect_elements(self, screenshot_path)</code></li>
<li><code>_detect_text_elements(self, screenshot_path)</code></li>
<li><code>_detect_ui_elements_with_opencv(self, img)</code></li>
<li><code>_generate_simple_code(self, action)</code></li>
<li><code>generate_code(self, actions)</code></li>
<li><code>save_recording(self, name)</code></li>
<li><code>load_recording(self, filepath)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>Recorder</code></li>
<li><code>__init__(self, device_controller)</code></li>
<li><code>start_recording(self)</code></li>
<li><code>stop_recording(self)</code></li>
<li><code>add_action(self, action_data)</code></li>
<li><code>_process_elements_in_background(self, screenshot_path, action_index)</code></li>
<li><code>_detect_changes_fast(self, prev_screenshot, curr_screenshot)</code></li>
<li><code>_detect_elements(self, screenshot_path)</code></li>
<li><code>_detect_text_elements(self, screenshot_path)</code></li>
<li><code>_detect_ui_elements_with_opencv(self, img)</code></li>
<li><code>_generate_simple_code(self, action)</code></li>
<li><code>generate_code(self, actions)</code></li>
<li><code>save_recording(self, name)</code></li>
<li><code>load_recording(self, filepath)</code></li></ul>

<p>---</p>

<h3>__init__.py</h3>

<p>---</p>

<h3>reportGenerator.js</h3>

<strong>Functions:</strong>
<ul><li><code>generateReport(testSuite)</code></li>
<li><code>getTimestamp()</code></li>
<li><code>generateHtmlContent(testSuite, timestamp)</code></li>
<li><code>calculateStatistics(testSuite)</code></li>
<li><code>determineStatus(steps)</code></li>
<li><code>formatTimestamp(timestamp)</code></li>
<li><code>saveScreenshot(stepId, screenshotData)</code></li>
<li><code>getLatestReportUrl()</code></li></ul>

<p>---</p>

<h3>test.json</h3>

<p>---</p>

<h3>image_matcher.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_id, max_workers, platform)</code></li>
<li><code>set_device_id(self, device_id)</code></li>
<li><code>set_platform(self, platform)</code></li>
<li><code>set_airtest_device(self, airtest_device)</code></li>
<li><code>take_screenshot_with_adb(self)</code></li>
<li><code>take_screenshot_ios(self, screenshot_path)</code></li>
<li><code>take_screenshot_android(self, screenshot_path)</code></li>
<li><code>find_template(self, template_path, screenshot_path, threshold, debug)</code></li>
<li><code>wait_for_template(self, template_path, timeout, interval, threshold)</code></li>
<li><code>process_screenshot(screenshot_path)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ImageMatcher</code></li>
<li><code>__init__(self, device_id, max_workers, platform)</code></li>
<li><code>set_device_id(self, device_id)</code></li>
<li><code>set_platform(self, platform)</code></li>
<li><code>set_airtest_device(self, airtest_device)</code></li>
<li><code>take_screenshot_with_adb(self)</code></li>
<li><code>take_screenshot_ios(self, screenshot_path)</code></li>
<li><code>take_screenshot_android(self, screenshot_path)</code></li>
<li><code>find_template(self, template_path, screenshot_path, threshold, debug)</code></li>
<li><code>wait_for_template(self, template_path, timeout, interval, threshold)</code></li></ul>

<p>---</p>

<h3>parameter_utils.py</h3>

<strong>Functions:</strong>
<ul><li><code>substitute_parameters(text, log_substitution)</code></li></ul>

<p>---</p>

<h3>player.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_controller, socketio, test_idx)</code></li>
<li><code>load_recording(self, file_path)</code></li>
<li><code>play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)</code></li>
<li><code>_verify_device_connection(self)</code></li>
<li><code>_recover_device_connection(self)</code></li>
<li><code>stop(self)</code></li>
<li><code>_find_hook_action(self)</code></li>
<li><code>_convert_hook_to_action(self, hook_action)</code></li>
<li><code>_execute_hook_action(self, hook_action)</code></li>
<li><code>execute_action(self, action)</code></li>
<li><code>play_with_validation(self, actions)</code></li>
<li><code>_validate_visual_state(self, action)</code></li>
<li><code>execute_all_actions(self, actions)</code></li>
<li><code>stop_execution(self)</code></li>
<li><code>_execute_tap(self, x, y)</code></li>
<li><code>_execute_swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>_execute_text(self, text)</code></li>
<li><code>_execute_key(self, key_code)</code></li>
<li><code>_execute_wait(self, duration)</code></li>
<li><code>_execute_launch_app(self, package)</code></li>
<li><code>_execute_terminate_app(self, package)</code></li>
<li><code>_execute_restart_app(self, package)</code></li>
<li><code>_execute_wait_till(self, image, timeout, interval)</code></li>
<li><code>_execute_exists_image(self, image)</code></li>
<li><code>_execute_exists_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click(self, x, y)</code></li>
<li><code>_fallback_double_tap(self, x, y)</code></li>
<li><code>_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>_execute_text_clear(self, text, delay)</code></li>
<li><code>_execute_click_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)</code></li>
<li><code>_execute_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)</code></li>
<li><code>_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)</code></li>
<li><code>_execute_swipe_till_visible(self, action)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value)</code></li>
<li><code>scale_ios_coordinates(self, coordinates)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>Player</code></li>
<li><code>__init__(self, device_controller, socketio, test_idx)</code></li>
<li><code>load_recording(self, file_path)</code></li>
<li><code>play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)</code></li>
<li><code>_verify_device_connection(self)</code></li>
<li><code>_recover_device_connection(self)</code></li>
<li><code>stop(self)</code></li>
<li><code>_find_hook_action(self)</code></li>
<li><code>_convert_hook_to_action(self, hook_action)</code></li>
<li><code>_execute_hook_action(self, hook_action)</code></li>
<li><code>execute_action(self, action)</code></li>
<li><code>play_with_validation(self, actions)</code></li>
<li><code>_validate_visual_state(self, action)</code></li>
<li><code>execute_all_actions(self, actions)</code></li>
<li><code>stop_execution(self)</code></li>
<li><code>_execute_tap(self, x, y)</code></li>
<li><code>_execute_swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>_execute_text(self, text)</code></li>
<li><code>_execute_key(self, key_code)</code></li>
<li><code>_execute_wait(self, duration)</code></li>
<li><code>_execute_launch_app(self, package)</code></li>
<li><code>_execute_terminate_app(self, package)</code></li>
<li><code>_execute_restart_app(self, package)</code></li>
<li><code>_execute_wait_till(self, image, timeout, interval)</code></li>
<li><code>_execute_exists_image(self, image)</code></li>
<li><code>_execute_exists_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click(self, x, y)</code></li>
<li><code>_fallback_double_tap(self, x, y)</code></li>
<li><code>_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>_execute_text_clear(self, text, delay)</code></li>
<li><code>_execute_click_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)</code></li>
<li><code>_execute_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)</code></li>
<li><code>_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)</code></li>
<li><code>_execute_swipe_till_visible(self, action)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value)</code></li>
<li><code>scale_ios_coordinates(self, coordinates)</code></li></ul>

<p>---</p>

<h3>global_values_db.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, db_path)</code></li>
<li><code>_init_db(self)</code></li>
<li><code>_init_default_values(self)</code></li>
<li><code>get_value(self, name, default)</code></li>
<li><code>set_value(self, name, value)</code></li>
<li><code>delete_value(self, name)</code></li>
<li><code>get_all_values(self)</code></li>
<li><code>save_values(self, global_values)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>GlobalValuesDB</code></li>
<li><code>__init__(self, db_path)</code></li>
<li><code>_init_db(self)</code></li>
<li><code>_init_default_values(self)</code></li>
<li><code>get_value(self, name, default)</code></li>
<li><code>set_value(self, name, value)</code></li>
<li><code>delete_value(self, name)</code></li>
<li><code>get_all_values(self)</code></li>
<li><code>save_values(self, global_values)</code></li></ul>

<p>---</p>

<h3>file_utils.py</h3>

<strong>Functions:</strong>
<ul><li><code>save_json(data, relative_path)</code></li>
<li><code>load_json(relative_path)</code></li>
<li><code>ensure_directory(relative_path)</code></li>
<li><code>ensure_dir_exists(directory)</code></li>
<li><code>get_timestamp()</code></li>
<li><code>save_to_file(content, filepath)</code></li>
<li><code>read_from_file(filepath)</code></li>
<li><code>compare_screenshots(screenshot1, screenshot2, threshold)</code></li>
<li><code>base64_to_image(base64_str)</code></li>
<li><code>image_to_base64(image)</code></li></ul>

<p>---</p>

<h3>random_data_generator.py</h3>

<strong>Functions:</strong>
<ul><li><code>get_generator_options()</code></li>
<li><code>generate_data(generator_id)</code></li></ul>

<p>---</p>

<h3>screenshot_manager.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self)</code></li>
<li><code>initialize(self, report_dir, timestamp)</code></li>
<li><code>save_screenshot(self, screenshot_data, filename, action_id, device_controller)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ScreenshotManager</code></li>
<li><code>__init__(self)</code></li>
<li><code>initialize(self, report_dir, timestamp)</code></li>
<li><code>save_screenshot(self, screenshot_data, filename, action_id, device_controller)</code></li></ul>

<p>---</p>

<h3>build_data_json.py</h3>

<strong>Functions:</strong>
<ul><li><code>build_data_json_from_test_case(test_case_file, suite_id, execution_data, success, error)</code></li></ul>

<p>---</p>

<h3>id_generator.py</h3>

<strong>Functions:</strong>
<ul><li><code>generate_action_id(length)</code></li>
<li><code>add_action_ids_to_test_case(test_case_data)</code></li></ul>

<p>---</p>

<h3>text_detector.py</h3>

<strong>Functions:</strong>
<ul><li><code>detect_text_with_tesseract(image_path, text_to_find, output_dir, similarity_threshold)</code></li>
<li><code>get_text_coordinates(image_path, text_to_find, device_width, device_height, output_dir, similarity_threshold)</code></li>
<li><code>__init__(self)</code></li>
<li><code>find_text(self, image_path, text_to_find, similarity_threshold)</code></li>
<li><code>_calculate_similarity(self, text1, text2)</code></li>
<li><code>_find_partial_matches(self, data, text_to_find, similarity_threshold)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TextDetector</code></li>
<li><code>__init__(self)</code></li>
<li><code>find_text(self, image_path, text_to_find, similarity_threshold)</code></li>
<li><code>_calculate_similarity(self, text1, text2)</code></li>
<li><code>_find_partial_matches(self, data, text_to_find, similarity_threshold)</code></li></ul>

<p>---</p>

<h3>database_update.py</h3>

<strong>Functions:</strong>
<ul><li><code>update_database_schema()</code></li></ul>

<p>---</p>

<h3>screenshot_handler.py</h3>

<strong>Functions:</strong>
<ul><li><code>ensure_directory_exists(directory_path)</code></li>
<li><code>copy_screenshots_to_report(report_dir)</code></li>
<li><code>copy_latest_screenshot_to_report(report_dir, action_id)</code></li></ul>

<p>---</p>

<h3>text_detection.py</h3>

<strong>Functions:</strong>
<ul><li><code>detect_text_in_image(image_path, text_to_find, output_dir)</code></li>
<li><code>scale_coordinates(x, y, img_width, img_height, device_width, device_height)</code></li></ul>

<p>---</p>

<h3>reportGenerator.py</h3>

<strong>Functions:</strong>
<ul><li><code>getLatestReportUrl()</code></li>
<li><code>ensure_report_directories(reports_dir, report_timestamp)</code></li>
<li><code>copy_screenshots_for_report(report_timestamp, test_data)</code></li>
<li><code>save_action_logs_to_file(logs, report_dir)</code></li>
<li><code>regenerate_report_from_data_json(report_dir)</code></li>
<li><code>create_zip_archive(report_dir)</code></li>
<li><code>prepare_steps_for_report(test_suite_data)</code></li>
<li><code>generateReport(test_suite_data, test_case_file)</code></li>
<li><code>copy_screenshots_from_database(report_dir, test_data)</code></li>
<li><code>ensure_report_screenshots(report_dir, test_data)</code></li>
<li><code>get_image_number(filename)</code></li></ul>

<p>---</p>

<h3>coordinate_validator.py</h3>

<strong>Functions:</strong>
<ul><li><code>validate_coordinates(coords, device_width, device_height)</code></li></ul>

<p>---</p>

<h3>openReport.js</h3>

<strong>Functions:</strong>
<ul><li><code>openLatestReport()</code></li>
<li><code>openReport(reportName)</code></li>
<li><code>openUrlInBrowser(url)</code></li></ul>

<p>---</p>

<h3>test_case_manager.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, test_cases_dir)</code></li>
<li><code>get_test_cases(self)</code></li>
<li><code>load_test_case(self, filename)</code></li>
<li><code>save_test_case(self, test_case_data, filename, is_save_as)</code></li>
<li><code>delete_test_case(self, filename)</code></li>
<li><code>rename_test_case(self, filename, new_name)</code></li>
<li><code>duplicate_test_case(self, filename)</code></li>
<li><code>clean_duplicates(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TestCaseManager</code></li>
<li><code>__init__(self, test_cases_dir)</code></li>
<li><code>get_test_cases(self)</code></li>
<li><code>load_test_case(self, filename)</code></li>
<li><code>save_test_case(self, test_case_data, filename, is_save_as)</code></li>
<li><code>delete_test_case(self, filename)</code></li>
<li><code>rename_test_case(self, filename, new_name)</code></li>
<li><code>duplicate_test_case(self, filename)</code></li>
<li><code>clean_duplicates(self)</code></li></ul>

<p>---</p>

<h3>database.py</h3>

<strong>Functions:</strong>
<ul><li><code>ensure_db_directory()</code></li>
<li><code>init_db()</code></li>
<li><code>update_execution_tracking_schema()</code></li>
<li><code>clear_test_tables()</code></li>
<li><code>track_test_execution(suite_id, test_idx, filename, status, retry_count, max_retries, error, in_progress, step_idx, action_type, action_params, action_id)</code></li>
<li><code>clear_execution_tracking()</code></li>
<li><code>get_screenshots_for_suite(suite_id)</code></li>
<li><code>get_test_steps_for_suite(suite_id)</code></li>
<li><code>get_execution_tracking_for_suite(suite_id)</code></li>
<li><code>get_action_id_for_step(suite_id, test_idx, step_idx)</code></li>
<li><code>get_test_execution_status(suite_id, test_idx, filename)</code></li>
<li><code>reset_test_case_execution_tracking(suite_id, test_idx)</code></li>
<li><code>has_test_case_failures(suite_id, test_idx, force_return_true)</code></li>
<li><code>get_hook_execution_count(suite_id, test_idx, step_idx, action_type)</code></li>
<li><code>clear_database()</code></li>
<li><code>save_test_suite(suite_data)</code></li>
<li><code>get_test_suite(suite_id)</code></li>
<li><code>get_latest_test_suite()</code></li>
<li><code>save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id)</code></li>
<li><code>update_test_suite(suite_id, test_suite_data)</code></li>
<li><code>get_all_test_suites()</code></li>
<li><code>delete_report_by_name(report_name)</code></li>
<li><code>delete_report_by_dir(dir_name)</code></li>
<li><code>reset_database()</code></li>
<li><code>check_database_state()</code></li>
<li><code>get_test_case_by_id(test_case_id)</code></li>
<li><code>save_screenshot_info(suite_id, test_idx, step_idx, filename, path, retry_number, action_id)</code></li>
<li><code>check_screenshot_exists(action_id)</code></li>
<li><code>update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id)</code></li>
<li><code>update_action_id(suite_id, test_idx, step_idx, action_id)</code></li>
<li><code>clear_screenshots()</code></li></ul>

<p>---</p>

<h3>reset_database.py</h3>

<strong>Functions:</strong>
<ul><li><code>reset_database()</code></li>
<li><code>create_new_database()</code></li>
<li><code>check_database()</code></li></ul>

<p>---</p>

<h3>appium_device_controller.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self)</code></li>
<li><code>test_airtest_connection(self, device_id, platform_name)</code></li>
<li><code>_kill_existing_processes(self)</code></li>
<li><code>check_appium_server(self)</code></li>
<li><code>restart_appium_server(self)</code></li>
<li><code>_ensure_appium_server(self)</code></li>
<li><code>get_devices(self)</code></li>
<li><code>connect_to_device(self, device_id, options, platform)</code></li>
<li><code>_attempt_device_connection(self, device_id, options, platform)</code></li>
<li><code>_connect_with_timeout(self)</code></li>
<li><code>_init_platform_helpers(self)</code></li>
<li><code>_is_port_in_use(self, port)</code></li>
<li><code>_start_iproxy(self, device_id, local_port, device_port)</code></li>
<li><code>_connect_ios_device(self, options)</code></li>
<li><code>_connect_with_airtest(self, device_id, wda_url)</code></li>
<li><code>reconnect_device(self)</code></li>
<li><code>disconnect(self)</code></li>
<li><code>shutdown(self)</code></li>
<li><code>_init_airtest(self, device_id)</code></li>
<li><code>_init_minimal_ios_device(self)</code></li>
<li><code>_ensure_airtest_connected(self)</code></li>
<li><code>take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>find_element_at_position(self, x, y)</code></li>
<li><code>get_element_at_position(self, x, y)</code></li>
<li><code>input_text(self, locator_type, locator_value, text, clear_first, timeout)</code></li>
<li><code>tap(self, x, y, duration)</code></li>
<li><code>tap_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>tap_on_image(self, image_path, timeout, confidence)</code></li>
<li><code>tap_on_text(self, text, timeout, fuzzy)</code></li>
<li><code>double_tap(self, x, y)</code></li>
<li><code>swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>long_press(self, x, y, duration)</code></li>
<li><code>press_button(self, button_name)</code></li>
<li><code>launch_app(self, bundle_id)</code></li>
<li><code>terminate_app(self, bundle_id)</code></li>
<li><code>uninstall_app(self, package_id)</code></li>
<li><code>_uninstall_app_ios_direct(self, bundle_id)</code></li>
<li><code>session_id(self)</code></li>
<li><code>set_clipboard(self, content)</code></li>
<li><code>paste_clipboard(self)</code></li>
<li><code>capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)</code></li>
<li><code>find_element_with_fallback(self, locators, timeout)</code></li>
<li><code>find_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>find_image(self, image_path, threshold, timeout)</code></li>
<li><code>get_screen_text(self)</code></li>
<li><code>get_screen_content(self)</code></li>
<li><code>_run_adb_command(self, command, device_id)</code></li>
<li><code>recover_connection(self)</code></li>
<li><code>_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>is_session_active(self)</code></li>
<li><code>get_driver(self)</code></li>
<li><code>get_device_dimensions(self)</code></li>
<li><code>__init__(self, driver)</code></li>
<li><code>tap(self, x, y, element, count)</code></li>
<li><code>press(self, x, y, element, pressure)</code></li>
<li><code>long_press(self, x, y, element, duration)</code></li>
<li><code>wait(self, ms)</code></li>
<li><code>move_to(self, x, y, element)</code></li>
<li><code>release(self)</code></li>
<li><code>perform(self)</code></li>
<li><code>get_page_source()</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AppiumDeviceController</code></li>
<li><code>__init__(self)</code></li>
<li><code>test_airtest_connection(self, device_id, platform_name)</code></li>
<li><code>_kill_existing_processes(self)</code></li>
<li><code>check_appium_server(self)</code></li>
<li><code>restart_appium_server(self)</code></li>
<li><code>_ensure_appium_server(self)</code></li>
<li><code>get_devices(self)</code></li>
<li><code>connect_to_device(self, device_id, options, platform)</code></li>
<li><code>_attempt_device_connection(self, device_id, options, platform)</code></li>
<li><code>_connect_with_timeout(self)</code></li>
<li><code>_init_platform_helpers(self)</code></li>
<li><code>_is_port_in_use(self, port)</code></li>
<li><code>_start_iproxy(self, device_id, local_port, device_port)</code></li>
<li><code>_connect_ios_device(self, options)</code></li>
<li><code>_connect_with_airtest(self, device_id, wda_url)</code></li>
<li><code>reconnect_device(self)</code></li>
<li><code>disconnect(self)</code></li>
<li><code>shutdown(self)</code></li>
<li><code>_init_airtest(self, device_id)</code></li>
<li><code>_init_minimal_ios_device(self)</code></li>
<li><code>_ensure_airtest_connected(self)</code></li>
<li><code>take_screenshot(self, filename, save_debug, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>find_element_at_position(self, x, y)</code></li>
<li><code>get_element_at_position(self, x, y)</code></li>
<li><code>input_text(self, locator_type, locator_value, text, clear_first, timeout)</code></li>
<li><code>tap(self, x, y, duration)</code></li>
<li><code>tap_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>tap_on_image(self, image_path, timeout, confidence)</code></li>
<li><code>tap_on_text(self, text, timeout, fuzzy)</code></li>
<li><code>double_tap(self, x, y)</code></li>
<li><code>swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>long_press(self, x, y, duration)</code></li>
<li><code>press_button(self, button_name)</code></li>
<li><code>launch_app(self, bundle_id)</code></li>
<li><code>terminate_app(self, bundle_id)</code></li>
<li><code>uninstall_app(self, package_id)</code></li>
<li><code>_uninstall_app_ios_direct(self, bundle_id)</code></li>
<li><code>session_id(self)</code></li>
<li><code>set_clipboard(self, content)</code></li>
<li><code>paste_clipboard(self)</code></li>
<li><code>capture_image_area(self, selection_x, selection_y, selection_width, selection_height, displayed_width, displayed_height, natural_width, natural_height, image_name, save_debug)</code></li>
<li><code>find_element_with_fallback(self, locators, timeout)</code></li>
<li><code>find_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>find_image(self, image_path, threshold, timeout)</code></li>
<li><code>get_screen_text(self)</code></li>
<li><code>get_screen_content(self)</code></li>
<li><code>_run_adb_command(self, command, device_id)</code></li>
<li><code>recover_connection(self)</code></li>
<li><code>_save_screenshot_to_database(self, filename, test_idx, step_idx, suite_id, action_id)</code></li>
<li><code>is_session_active(self)</code></li>
<li><code>get_driver(self)</code></li>
<li><code>get_device_dimensions(self)</code></li>
<li><code>TouchAction</code></li>
<li><code>__init__(self, driver)</code></li>
<li><code>tap(self, x, y, element, count)</code></li>
<li><code>press(self, x, y, element, pressure)</code></li>
<li><code>long_press(self, x, y, element, duration)</code></li>
<li><code>wait(self, ms)</code></li>
<li><code>move_to(self, x, y, element)</code></li>
<li><code>release(self)</code></li>
<li><code>perform(self)</code></li></ul>

<p>---</p>

<h3>ios_device.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_id, wda_url)</code></li>
<li><code>_get_screen_dimensions(self)</code></li>
<li><code>touch(self, x, y, duration)</code></li>
<li><code>double_click(self, x, y)</code></li>
<li><code>swipe(self, fx, fy, tx, ty, duration)</code></li>
<li><code>snapshot(self, filename, save_debug)</code></li>
<li><code>home(self)</code></li>
<li><code>press(self, key)</code></li>
<li><code>alert_accept(self)</code></li>
<li><code>alert_dismiss(self)</code></li>
<li><code>alert_click(self, button_text)</code></li>
<li><code>alert_wait(self, timeout)</code></li>
<li><code>alert_exists(self)</code></li>
<li><code>alert_buttons(self)</code></li>
<li><code>get_clipboard(self, wda_bundle_id)</code></li>
<li><code>set_clipboard(self, content, wda_bundle_id)</code></li>
<li><code>text(self, text, enter)</code></li>
<li><code>_press_return_key(self)</code></li>
<li><code>paste(self, wda_bundle_id)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>device_status(self)</code></li>
<li><code>get_current_resolution(self)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>push(self, local_path, remote_path)</code></li>
<li><code>uninstall_app(self, bundle_id)</code></li>
<li><code>clear_app(self, bundle_id)</code></li>
<li><code>lock(self)</code></li>
<li><code>unlock(self)</code></li>
<li><code>is_locked(self)</code></li>
<li><code>list_app(self)</code></li>
<li><code>find_template(self, template_path, threshold, timeout, interval)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>MinimalIOSDevice</code></li>
<li><code>__init__(self, device_id, wda_url)</code></li>
<li><code>_get_screen_dimensions(self)</code></li>
<li><code>touch(self, x, y, duration)</code></li>
<li><code>double_click(self, x, y)</code></li>
<li><code>swipe(self, fx, fy, tx, ty, duration)</code></li>
<li><code>snapshot(self, filename, save_debug)</code></li>
<li><code>home(self)</code></li>
<li><code>press(self, key)</code></li>
<li><code>alert_accept(self)</code></li>
<li><code>alert_dismiss(self)</code></li>
<li><code>alert_click(self, button_text)</code></li>
<li><code>alert_wait(self, timeout)</code></li>
<li><code>alert_exists(self)</code></li>
<li><code>alert_buttons(self)</code></li>
<li><code>get_clipboard(self, wda_bundle_id)</code></li>
<li><code>set_clipboard(self, content, wda_bundle_id)</code></li>
<li><code>text(self, text, enter)</code></li>
<li><code>_press_return_key(self)</code></li>
<li><code>paste(self, wda_bundle_id)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>device_status(self)</code></li>
<li><code>get_current_resolution(self)</code></li>
<li><code>get_ip_address(self)</code></li>
<li><code>push(self, local_path, remote_path)</code></li>
<li><code>uninstall_app(self, bundle_id)</code></li>
<li><code>clear_app(self, bundle_id)</code></li>
<li><code>lock(self)</code></li>
<li><code>unlock(self)</code></li>
<li><code>is_locked(self)</code></li>
<li><code>list_app(self)</code></li>
<li><code>find_template(self, template_path, threshold, timeout, interval)</code></li></ul>

<p>---</p>

<h3>recorder.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_controller)</code></li>
<li><code>start_recording(self)</code></li>
<li><code>stop_recording(self)</code></li>
<li><code>add_action(self, action_data)</code></li>
<li><code>_process_elements_in_background(self, screenshot_path, action_index)</code></li>
<li><code>_detect_changes_fast(self, prev_screenshot, curr_screenshot)</code></li>
<li><code>_detect_elements(self, screenshot_path)</code></li>
<li><code>_detect_text_elements(self, screenshot_path)</code></li>
<li><code>_detect_ui_elements_with_opencv(self, img)</code></li>
<li><code>_generate_simple_code(self, action)</code></li>
<li><code>generate_code(self, actions)</code></li>
<li><code>save_recording(self, name)</code></li>
<li><code>load_recording(self, filepath)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>Recorder</code></li>
<li><code>__init__(self, device_controller)</code></li>
<li><code>start_recording(self)</code></li>
<li><code>stop_recording(self)</code></li>
<li><code>add_action(self, action_data)</code></li>
<li><code>_process_elements_in_background(self, screenshot_path, action_index)</code></li>
<li><code>_detect_changes_fast(self, prev_screenshot, curr_screenshot)</code></li>
<li><code>_detect_elements(self, screenshot_path)</code></li>
<li><code>_detect_text_elements(self, screenshot_path)</code></li>
<li><code>_detect_ui_elements_with_opencv(self, img)</code></li>
<li><code>_generate_simple_code(self, action)</code></li>
<li><code>generate_code(self, actions)</code></li>
<li><code>save_recording(self, name)</code></li>
<li><code>load_recording(self, filepath)</code></li></ul>

<p>---</p>

<h3>__init__.py</h3>

<p>---</p>

<h3>reportGenerator.js</h3>

<strong>Functions:</strong>
<ul><li><code>generateReport(testSuite)</code></li>
<li><code>getTimestamp()</code></li>
<li><code>generateHtmlContent(testSuite, timestamp)</code></li>
<li><code>calculateStatistics(testSuite)</code></li>
<li><code>determineStatus(steps)</code></li>
<li><code>formatTimestamp(timestamp)</code></li>
<li><code>saveScreenshot(stepId, screenshotData)</code></li>
<li><code>getLatestReportUrl()</code></li></ul>

<p>---</p>

<h3>test.json</h3>

<p>---</p>

<h3>image_matcher.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_id, max_workers, platform)</code></li>
<li><code>set_device_id(self, device_id)</code></li>
<li><code>set_platform(self, platform)</code></li>
<li><code>set_airtest_device(self, airtest_device)</code></li>
<li><code>take_screenshot_with_adb(self)</code></li>
<li><code>take_screenshot_ios(self, screenshot_path)</code></li>
<li><code>take_screenshot_android(self, screenshot_path)</code></li>
<li><code>find_template(self, template_path, screenshot_path, threshold, debug)</code></li>
<li><code>wait_for_template(self, template_path, timeout, interval, threshold)</code></li>
<li><code>process_screenshot(screenshot_path)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ImageMatcher</code></li>
<li><code>__init__(self, device_id, max_workers, platform)</code></li>
<li><code>set_device_id(self, device_id)</code></li>
<li><code>set_platform(self, platform)</code></li>
<li><code>set_airtest_device(self, airtest_device)</code></li>
<li><code>take_screenshot_with_adb(self)</code></li>
<li><code>take_screenshot_ios(self, screenshot_path)</code></li>
<li><code>take_screenshot_android(self, screenshot_path)</code></li>
<li><code>find_template(self, template_path, screenshot_path, threshold, debug)</code></li>
<li><code>wait_for_template(self, template_path, timeout, interval, threshold)</code></li></ul>

<p>---</p>

<h3>parameter_utils.py</h3>

<strong>Functions:</strong>
<ul><li><code>substitute_parameters(text, log_substitution)</code></li></ul>

<p>---</p>

<h3>player.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, device_controller, socketio, test_idx)</code></li>
<li><code>load_recording(self, file_path)</code></li>
<li><code>play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)</code></li>
<li><code>_verify_device_connection(self)</code></li>
<li><code>_recover_device_connection(self)</code></li>
<li><code>stop(self)</code></li>
<li><code>_find_hook_action(self)</code></li>
<li><code>_convert_hook_to_action(self, hook_action)</code></li>
<li><code>_execute_hook_action(self, hook_action)</code></li>
<li><code>execute_action(self, action)</code></li>
<li><code>play_with_validation(self, actions)</code></li>
<li><code>_validate_visual_state(self, action)</code></li>
<li><code>execute_all_actions(self, actions)</code></li>
<li><code>stop_execution(self)</code></li>
<li><code>_execute_tap(self, x, y)</code></li>
<li><code>_execute_swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>_execute_text(self, text)</code></li>
<li><code>_execute_key(self, key_code)</code></li>
<li><code>_execute_wait(self, duration)</code></li>
<li><code>_execute_launch_app(self, package)</code></li>
<li><code>_execute_terminate_app(self, package)</code></li>
<li><code>_execute_restart_app(self, package)</code></li>
<li><code>_execute_wait_till(self, image, timeout, interval)</code></li>
<li><code>_execute_exists_image(self, image)</code></li>
<li><code>_execute_exists_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click(self, x, y)</code></li>
<li><code>_fallback_double_tap(self, x, y)</code></li>
<li><code>_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>_execute_text_clear(self, text, delay)</code></li>
<li><code>_execute_click_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)</code></li>
<li><code>_execute_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)</code></li>
<li><code>_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)</code></li>
<li><code>_execute_swipe_till_visible(self, action)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value)</code></li>
<li><code>scale_ios_coordinates(self, coordinates)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>Player</code></li>
<li><code>__init__(self, device_controller, socketio, test_idx)</code></li>
<li><code>load_recording(self, file_path)</code></li>
<li><code>play(self, actions, delay, callback, suite_id, test_case_start_index, test_case_end_index, test_idx)</code></li>
<li><code>_verify_device_connection(self)</code></li>
<li><code>_recover_device_connection(self)</code></li>
<li><code>stop(self)</code></li>
<li><code>_find_hook_action(self)</code></li>
<li><code>_convert_hook_to_action(self, hook_action)</code></li>
<li><code>_execute_hook_action(self, hook_action)</code></li>
<li><code>execute_action(self, action)</code></li>
<li><code>play_with_validation(self, actions)</code></li>
<li><code>_validate_visual_state(self, action)</code></li>
<li><code>execute_all_actions(self, actions)</code></li>
<li><code>stop_execution(self)</code></li>
<li><code>_execute_tap(self, x, y)</code></li>
<li><code>_execute_swipe(self, start_x, start_y, end_x, end_y, duration)</code></li>
<li><code>_execute_text(self, text)</code></li>
<li><code>_execute_key(self, key_code)</code></li>
<li><code>_execute_wait(self, duration)</code></li>
<li><code>_execute_launch_app(self, package)</code></li>
<li><code>_execute_terminate_app(self, package)</code></li>
<li><code>_execute_restart_app(self, package)</code></li>
<li><code>_execute_wait_till(self, image, timeout, interval)</code></li>
<li><code>_execute_exists_image(self, image)</code></li>
<li><code>_execute_exists_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click(self, x, y)</code></li>
<li><code>_fallback_double_tap(self, x, y)</code></li>
<li><code>_execute_wait_till_element(self, locator_type, locator_value, timeout, interval)</code></li>
<li><code>_execute_text_clear(self, text, delay)</code></li>
<li><code>_execute_click_element(self, locator_type, locator_value, timeout)</code></li>
<li><code>_execute_double_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count, interval)</code></li>
<li><code>_execute_click_image(self, image_path, threshold, timeout)</code></li>
<li><code>_execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators, fallback_type, fallback_params)</code></li>
<li><code>_execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators)</code></li>
<li><code>_execute_swipe_till_visible(self, action)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value)</code></li>
<li><code>scale_ios_coordinates(self, coordinates)</code></li></ul>

<p>---</p>

<h3>global_values_db.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, db_path)</code></li>
<li><code>_init_db(self)</code></li>
<li><code>_init_default_values(self)</code></li>
<li><code>get_value(self, name, default)</code></li>
<li><code>set_value(self, name, value)</code></li>
<li><code>delete_value(self, name)</code></li>
<li><code>get_all_values(self)</code></li>
<li><code>save_values(self, global_values)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>GlobalValuesDB</code></li>
<li><code>__init__(self, db_path)</code></li>
<li><code>_init_db(self)</code></li>
<li><code>_init_default_values(self)</code></li>
<li><code>get_value(self, name, default)</code></li>
<li><code>set_value(self, name, value)</code></li>
<li><code>delete_value(self, name)</code></li>
<li><code>get_all_values(self)</code></li>
<li><code>save_values(self, global_values)</code></li></ul>

<p>---</p>

<h3>file_utils.py</h3>

<strong>Functions:</strong>
<ul><li><code>save_json(data, relative_path)</code></li>
<li><code>load_json(relative_path)</code></li>
<li><code>ensure_directory(relative_path)</code></li>
<li><code>ensure_dir_exists(directory)</code></li>
<li><code>get_timestamp()</code></li>
<li><code>save_to_file(content, filepath)</code></li>
<li><code>read_from_file(filepath)</code></li>
<li><code>compare_screenshots(screenshot1, screenshot2, threshold)</code></li>
<li><code>base64_to_image(base64_str)</code></li>
<li><code>image_to_base64(image)</code></li></ul>

<p>---</p>

<h3>random_data_generator.py</h3>

<strong>Functions:</strong>
<ul><li><code>get_generator_options()</code></li>
<li><code>generate_data(generator_id)</code></li></ul>

<p>---</p>

<h3>screenshot_manager.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self)</code></li>
<li><code>initialize(self, report_dir, timestamp)</code></li>
<li><code>save_screenshot(self, screenshot_data, filename, action_id, device_controller)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ScreenshotManager</code></li>
<li><code>__init__(self)</code></li>
<li><code>initialize(self, report_dir, timestamp)</code></li>
<li><code>save_screenshot(self, screenshot_data, filename, action_id, device_controller)</code></li></ul>

<p>---</p>

<h3>build_data_json.py</h3>

<strong>Functions:</strong>
<ul><li><code>build_data_json_from_test_case(test_case_file, suite_id, execution_data, success, error)</code></li></ul>

<p>---</p>

<h2>app/static/css</h2>

<h3>modern-styles.css</h3>

<p>---</p>

<h3>fixed-device-screen.css</h3>

<p>---</p>

<h3>test-case.css</h3>

<p>---</p>

<h3>execution-overlay.css</h3>

<p>---</p>

<h3>fallback-locators.css</h3>

<p>---</p>

<h3>test-cases-styles.css</h3>

<p>---</p>

<h3>style.css</h3>

<p>---</p>

<h3>test-suites-styles.css</h3>

<p>---</p>

<h3>modern-styles.css</h3>

<p>---</p>

<h3>fixed-device-screen.css</h3>

<p>---</p>

<h3>test-case.css</h3>

<p>---</p>

<h3>execution-overlay.css</h3>

<p>---</p>

<h3>fallback-locators.css</h3>

<p>---</p>

<h3>test-cases-styles.css</h3>

<p>---</p>

<h3>style.css</h3>

<p>---</p>

<h3>test-suites-styles.css</h3>

<p>---</p>

<h2>app/static/js</h2>

<h3>hook-action.js</h3>

<p>---</p>

<h3>action-manager.js</h3>

<p>---</p>

<h3>test_suites.js</h3>

<p>---</p>

<h3>execution-overlay.js</h3>

<p>---</p>

<h3>action-description.js</h3>

<strong>Functions:</strong>
<ul><li><code>getActionDescription(actionData)</code></li></ul>

<p>---</p>

<h3>main.js</h3>

<strong>Functions:</strong>
<ul><li><code>addLocatorItem(container, locator, type)</code></li>
<li><code>switch(action.ios_function)</code></li>
<li><code>switch(selectedFunction)</code></li>
<li><code>type(which may be used by the new clipboard function implementation)</code></li>
<li><code>cancelPickingHandler()</code></li>
<li><code>escHandler(e)</code></li>
<li><code>handleKeyDown(e)</code></li>
<li><code>oldClickHandler()</code></li></ul>

<p>---</p>

<h3>socket-handlers.js</h3>

<strong>Functions:</strong>
<ul><li><code>setupSocketHandlers(socket)</code></li>
<li><code>updateConnectionStatus(isConnected, deviceInfo)</code></li>
<li><code>handleSuiteExecutionStatus(data)</code></li></ul>

<p>---</p>

<h3>tap-fallback-manager.js</h3>

<p>---</p>

<h3>execution-manager.js</h3>

<p>---</p>

<h3>random-data-generator.js</h3>

<p>---</p>

<h3>utils.js</h3>

<strong>Functions:</strong>
<ul><li><code>formatDate(dateString)</code></li>
<li><code>showToast(title, message, type = 'info')</code></li>
<li><code>showLoading(show, message = 'Loading...')</code></li>
<li><code>logAction(source, message, type = 'info')</code></li></ul>

<p>---</p>

<h3>reports.js</h3>

<p>---</p>

<h3>multi-step-action.js</h3>

<p>---</p>

<h3>fixed-device-screen.js</h3>

<p>---</p>

<h3>app.js</h3>

<strong>Functions:</strong>
<ul><li><code>enhanceTabNavigation()</code></li>
<li><code>setupAppSocketListeners(socket)</code></li>
<li><code>setupDeviceScreen()</code></li>
<li><code>executeAction(index)</code></li>
<li><code>get_action_description(action)</code></li>
<li><code>updateSliderPosition(activeTab)</code></li></ul>

<p>---</p>

<h3>settings.js</h3>

<strong>Functions:</strong>
<ul><li><code>loadDirectories()</code></li>
<li><code>loadGlobalValues()</code></li>
<li><code>saveDirectories()</code></li>
<li><code>saveGlobalValues()</code></li>
<li><code>collectGlobalValues()</code></li>
<li><code>populateTestCasesTable(testCases)</code></li>
<li><code>loadTestCase(filename)</code></li>
<li><code>handleHidden()</code></li>
<li><code>deleteTestCase(filename)</code></li>
<li><code>loadTestSuites()</code></li>
<li><code>populateTestSuitesTable(testSuites)</code></li>
<li><code>loadReports()</code></li>
<li><code>populateReportsTable(reports)</code></li>
<li><code>formatDate(dateString)</code></li>
<li><code>populateGlobalValuesTable(globalValues)</code></li>
<li><code>addGlobalValueRow(name = '', value = '')</code></li></ul>

<p>---</p>

<h3>hook-action.js</h3>

<p>---</p>

<h3>action-manager.js</h3>

<p>---</p>

<h3>test_suites.js</h3>

<p>---</p>

<h3>execution-overlay.js</h3>

<p>---</p>

<h3>action-description.js</h3>

<strong>Functions:</strong>
<ul><li><code>getActionDescription(actionData)</code></li></ul>

<p>---</p>

<h3>main.js</h3>

<strong>Functions:</strong>
<ul><li><code>addLocatorItem(container, locator, type)</code></li>
<li><code>switch(action.ios_function)</code></li>
<li><code>switch(selectedFunction)</code></li>
<li><code>type(which may be used by the new clipboard function implementation)</code></li>
<li><code>cancelPickingHandler()</code></li>
<li><code>escHandler(e)</code></li>
<li><code>handleKeyDown(e)</code></li>
<li><code>oldClickHandler()</code></li></ul>

<p>---</p>

<h3>socket-handlers.js</h3>

<strong>Functions:</strong>
<ul><li><code>setupSocketHandlers(socket)</code></li>
<li><code>updateConnectionStatus(isConnected, deviceInfo)</code></li>
<li><code>handleSuiteExecutionStatus(data)</code></li></ul>

<p>---</p>

<h3>tap-fallback-manager.js</h3>

<p>---</p>

<h3>execution-manager.js</h3>

<p>---</p>

<h3>random-data-generator.js</h3>

<p>---</p>

<h3>utils.js</h3>

<strong>Functions:</strong>
<ul><li><code>formatDate(dateString)</code></li>
<li><code>showToast(title, message, type = 'info')</code></li>
<li><code>showLoading(show, message = 'Loading...')</code></li>
<li><code>logAction(source, message, type = 'info')</code></li></ul>

<p>---</p>

<h3>reports.js</h3>

<p>---</p>

<h3>multi-step-action.js</h3>

<p>---</p>

<h3>fixed-device-screen.js</h3>

<p>---</p>

<h3>app.js</h3>

<strong>Functions:</strong>
<ul><li><code>enhanceTabNavigation()</code></li>
<li><code>setupAppSocketListeners(socket)</code></li>
<li><code>setupDeviceScreen()</code></li>
<li><code>executeAction(index)</code></li>
<li><code>get_action_description(action)</code></li>
<li><code>updateSliderPosition(activeTab)</code></li></ul>

<p>---</p>

<h3>settings.js</h3>

<strong>Functions:</strong>
<ul><li><code>loadDirectories()</code></li>
<li><code>loadGlobalValues()</code></li>
<li><code>saveDirectories()</code></li>
<li><code>saveGlobalValues()</code></li>
<li><code>collectGlobalValues()</code></li>
<li><code>populateTestCasesTable(testCases)</code></li>
<li><code>loadTestCase(filename)</code></li>
<li><code>handleHidden()</code></li>
<li><code>deleteTestCase(filename)</code></li>
<li><code>loadTestSuites()</code></li>
<li><code>populateTestSuitesTable(testSuites)</code></li>
<li><code>loadReports()</code></li>
<li><code>populateReportsTable(reports)</code></li>
<li><code>formatDate(dateString)</code></li>
<li><code>populateGlobalValuesTable(globalValues)</code></li>
<li><code>addGlobalValueRow(name = '', value = '')</code></li></ul>

<p>---</p>

<h2>app/static/js/actions</h2>

<h3>swipe-till-visible.js</h3>

<strong>Functions:</strong>
<ul><li><code>initSwipeTillVisibleAction()</code></li>
<li><code>setupSwipeTillVisibleEventListeners()</code></li>
<li><code>updateSwipeTillVisibleCoordinates(direction)</code></li>
<li><code>initSliderValueDisplays()</code></li>
<li><code>updateSliderDisplayValue(sliderId)</code></li></ul>

<p>---</p>

<h3>swipe-till-visible.js</h3>

<strong>Functions:</strong>
<ul><li><code>initSwipeTillVisibleAction()</code></li>
<li><code>setupSwipeTillVisibleEventListeners()</code></li>
<li><code>updateSwipeTillVisibleCoordinates(direction)</code></li>
<li><code>initSliderValueDisplays()</code></li>
<li><code>updateSliderDisplayValue(sliderId)</code></li></ul>

<p>---</p>

<h2>app/static/js/modules</h2>

<h3>fallback-locators.js</h3>

<strong>Functions:</strong>
<ul><li><code>clickHandler(e)</code></li></ul>

<p>---</p>

<h3>tap-fallback-manager.js</h3>

<p>---</p>

<h3>ElementInteractions.js</h3>

<p>---</p>

<h3>TestCaseManager.js</h3>

<p>---</p>

<h3>fallback-locators.js</h3>

<strong>Functions:</strong>
<ul><li><code>clickHandler(e)</code></li></ul>

<p>---</p>

<h3>tap-fallback-manager.js</h3>

<p>---</p>

<h3>ElementInteractions.js</h3>

<p>---</p>

<h3>TestCaseManager.js</h3>

<p>---</p>

<h2>app/static/templates</h2>

<h3>index.html</h3>

<p>---</p>

<h3>index.html</h3>

<p>---</p>

<h2>app/actions</h2>

<h3>tap_if_image_exists_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapIfImageExistsAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>multi_step_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>MultiStepAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>swipe_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SwipeAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>get_param_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>GetParamAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>wait_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>WaitAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>terminate_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TerminateAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>double_click_image_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_opencv_double_click_image(self, image_path, threshold, timeout)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DoubleClickImageAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_opencv_double_click_image(self, image_path, threshold, timeout)</code></li></ul>

<p>---</p>

<h3>uninstall_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>UninstallAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>text_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TextAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>wait_till_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>WaitTillAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>__init__.py</h3>

<p>---</p>

<h3>base_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>set_controller(self, controller)</code></li>
<li><code>get_global_timeout(self, default)</code></li>
<li><code>take_screenshot_after_action(self)</code></li>
<li><code>scale_ios_coordinates(self, coordinates, device_info)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>BaseAction</code></li>
<li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>set_controller(self, controller)</code></li>
<li><code>get_global_timeout(self, default)</code></li>
<li><code>take_screenshot_after_action(self)</code></li>
<li><code>scale_ios_coordinates(self, coordinates, device_info)</code></li></ul>

<p>---</p>

<h3>hook_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_get_hook_details(self, hook_type, hook_data)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>HookAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_get_hook_details(self, hook_type, hook_data)</code></li></ul>

<p>---</p>

<h3>input_text_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>InputTextAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>send_keys_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SendKeysAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>set_param_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SetParamAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>airplane_mode_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AirplaneModeAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>ios_functions_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>IosFunctionsAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>swipe_till_visible_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SwipeTillVisibleAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)</code></li></ul>

<p>---</p>

<h3>click_image_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_airtest_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ClickImageAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_airtest_support(self)</code></li></ul>

<p>---</p>

<h3>tap_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>scale_ios_coordinates(self, coordinates)</code></li>
<li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapAction</code></li>
<li><code>scale_ios_coordinates(self, coordinates)</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>if_else_steps_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>IfElseStepsAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>key_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>KeyAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>add_media_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params, context)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AddMediaAction</code></li>
<li><code>execute(self, params, context)</code></li></ul>

<p>---</p>

<h3>hide_keyboard_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>HideKeyboardAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>tap_and_type_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_tap_and_type_at_coordinates(self, x, y, text, timeout)</code></li>
<li><code>_use_direct_keys_endpoint(self, text)</code></li>
<li><code>_simulate_keyboard_typing(self, text)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapAndTypeAction</code></li>
<li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_tap_and_type_at_coordinates(self, x, y, text, timeout)</code></li>
<li><code>_use_direct_keys_endpoint(self, text)</code></li>
<li><code>_simulate_keyboard_typing(self, text)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>tap_on_text_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)</code></li>
<li><code>get_common_element_coordinates(self, text, device_width, device_height)</code></li>
<li><code>find_text_in_bottom_region(self, screenshot_path, text_to_find)</code></li>
<li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapOnTextAction</code></li>
<li><code>scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)</code></li>
<li><code>get_common_element_coordinates(self, text, device_width, device_height)</code></li>
<li><code>find_text_in_bottom_region(self, screenshot_path, text_to_find)</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>launch_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>LaunchAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>wait_element_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>WaitElementAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>compare_value_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>CompareValueAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>device_back_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DeviceBackAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>click_element_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ClickElementAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>random_data_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>RandomDataAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>get_value_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>GetValueAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>action_factory.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, controller)</code></li>
<li><code>_discover_action_handlers(self)</code></li>
<li><code>execute_action(self, action_type, params)</code></li>
<li><code>_ensure_double_tap_registered(self)</code></li>
<li><code>set_controller(self, controller)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ActionFactory</code></li>
<li><code>__init__(self, controller)</code></li>
<li><code>_discover_action_handlers(self)</code></li>
<li><code>execute_action(self, action_type, params)</code></li>
<li><code>_ensure_double_tap_registered(self)</code></li>
<li><code>set_controller(self, controller)</code></li></ul>

<p>---</p>

<h3>restart_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>RestartAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>double_tap_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DoubleTapAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>add_log_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AddLogAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>tap_if_image_exists_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapIfImageExistsAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>multi_step_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>MultiStepAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>swipe_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SwipeAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>get_param_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>GetParamAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>wait_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>WaitAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>terminate_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TerminateAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>double_click_image_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_opencv_double_click_image(self, image_path, threshold, timeout)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DoubleClickImageAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_opencv_double_click_image(self, image_path, threshold, timeout)</code></li></ul>

<p>---</p>

<h3>uninstall_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>UninstallAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>text_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TextAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>wait_till_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>WaitTillAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>__init__.py</h3>

<p>---</p>

<h3>base_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>set_controller(self, controller)</code></li>
<li><code>get_global_timeout(self, default)</code></li>
<li><code>take_screenshot_after_action(self)</code></li>
<li><code>scale_ios_coordinates(self, coordinates, device_info)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>BaseAction</code></li>
<li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>set_controller(self, controller)</code></li>
<li><code>get_global_timeout(self, default)</code></li>
<li><code>take_screenshot_after_action(self)</code></li>
<li><code>scale_ios_coordinates(self, coordinates, device_info)</code></li></ul>

<p>---</p>

<h3>hook_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_get_hook_details(self, hook_type, hook_data)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>HookAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_get_hook_details(self, hook_type, hook_data)</code></li></ul>

<p>---</p>

<h3>input_text_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>InputTextAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>send_keys_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SendKeysAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>set_param_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SetParamAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>airplane_mode_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AirplaneModeAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>ios_functions_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>IosFunctionsAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>swipe_till_visible_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>SwipeTillVisibleAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_is_element_visible(self, locator_type, locator_value, image_filename, text_to_find, threshold, timeout)</code></li></ul>

<p>---</p>

<h3>click_image_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_airtest_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ClickImageAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_airtest_support(self)</code></li></ul>

<p>---</p>

<h3>tap_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>scale_ios_coordinates(self, coordinates)</code></li>
<li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapAction</code></li>
<li><code>scale_ios_coordinates(self, coordinates)</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>if_else_steps_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>IfElseStepsAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>key_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>KeyAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>add_media_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params, context)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AddMediaAction</code></li>
<li><code>execute(self, params, context)</code></li></ul>

<p>---</p>

<h3>hide_keyboard_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>HideKeyboardAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>tap_and_type_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_tap_and_type_at_coordinates(self, x, y, text, timeout)</code></li>
<li><code>_use_direct_keys_endpoint(self, text)</code></li>
<li><code>_simulate_keyboard_typing(self, text)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapAndTypeAction</code></li>
<li><code>__init__(self, controller)</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_tap_and_type_at_coordinates(self, x, y, text, timeout)</code></li>
<li><code>_use_direct_keys_endpoint(self, text)</code></li>
<li><code>_simulate_keyboard_typing(self, text)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>tap_on_text_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)</code></li>
<li><code>get_common_element_coordinates(self, text, device_width, device_height)</code></li>
<li><code>find_text_in_bottom_region(self, screenshot_path, text_to_find)</code></li>
<li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>TapOnTextAction</code></li>
<li><code>scale_coordinates(self, x, y, reference_width, reference_height, device_width, device_height)</code></li>
<li><code>get_common_element_coordinates(self, text, device_width, device_height)</code></li>
<li><code>find_text_in_bottom_region(self, screenshot_path, text_to_find)</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>launch_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>LaunchAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>wait_element_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>WaitElementAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>compare_value_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>CompareValueAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>device_back_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DeviceBackAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>click_element_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ClickElementAction</code></li>
<li><code>execute(self, params)</code></li>
<li><code>_has_selenium_support(self)</code></li></ul>

<p>---</p>

<h3>random_data_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>RandomDataAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>get_value_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>GetValueAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>action_factory.py</h3>

<strong>Functions:</strong>
<ul><li><code>__init__(self, controller)</code></li>
<li><code>_discover_action_handlers(self)</code></li>
<li><code>execute_action(self, action_type, params)</code></li>
<li><code>_ensure_double_tap_registered(self)</code></li>
<li><code>set_controller(self, controller)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ActionFactory</code></li>
<li><code>__init__(self, controller)</code></li>
<li><code>_discover_action_handlers(self)</code></li>
<li><code>execute_action(self, action_type, params)</code></li>
<li><code>_ensure_double_tap_registered(self)</code></li>
<li><code>set_controller(self, controller)</code></li></ul>

<p>---</p>

<h3>restart_app_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>RestartAppAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>double_tap_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>DoubleTapAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h3>add_log_action.py</h3>

<strong>Functions:</strong>
<ul><li><code>execute(self, params)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>AddLogAction</code></li>
<li><code>execute(self, params)</code></li></ul>

<p>---</p>

<h2>app/templates</h2>

<h3>index.html</h3>

<p>---</p>

<h3>tap_on_text_form.html</h3>

<p>---</p>

<h3>settings.html</h3>

<p>---</p>

<h3>tap-fallback-modal.html</h3>

<p>---</p>

<h3>test_suites.html</h3>

<p>---</p>

<h3>index.html</h3>

<p>---</p>

<h3>tap_on_text_form.html</h3>

<p>---</p>

<h3>settings.html</h3>

<p>---</p>

<h3>tap-fallback-modal.html</h3>

<p>---</p>

<h3>test_suites.html</h3>

<p>---</p>

<h2>app/routes</h2>

<h3>__init__.py</h3>

<p>---</p>

<h3>random_data_routes.py</h3>

<strong>Functions:</strong>
<ul><li><code>get_generators()</code></li>
<li><code>generate_random_data()</code></li></ul>

<p>---</p>

<h3>__init__.py</h3>

<p>---</p>

<h3>random_data_routes.py</h3>

<strong>Functions:</strong>
<ul><li><code>get_generators()</code></li>
<li><code>generate_random_data()</code></li></ul>

<p>---</p>

<h2>test_cases</h2>

<h3>Postcode_Flow_Copy_20250513180904_20250513180904.json</h3>

<p>---</p>

<h3>Delivery__CNC_Copy_20250513201112_20250513201112.json</h3>

<p>---</p>

<h3>Postcode_Flow_Copy_20250518114624_20250518114624.json</h3>

<p>---</p>

<h3>OnePass_Account_Signin_20250510101051.json</h3>

<p>---</p>

<h3>Delivery_Buy_Steps_20250512194232.json</h3>

<p>---</p>

<h3>All_Sign_ins_Copy_20250514181123_20250514181123.json</h3>

<p>---</p>

<h3>WishList_20250510110236.json</h3>

<p>---</p>

<h3>SampleApp_20250401224106.json</h3>

<p>---</p>

<h3>KmartProdSignin_Copy_20250501144222_20250501144222.json</h3>

<p>---</p>

<h3>Delivery__Buy_20250505165058.json</h3>

<p>---</p>

<h3>Delivery_Buy_Steps_Copy_20250513195816_20250513195816.json</h3>

<p>---</p>

<h3>Postcode_Flow_20250502104451.json</h3>

<p>---</p>

<h3>AU_MyAccount_Copy_20250514192836_20250514192836.json</h3>

<p>---</p>

<h3>Browse__PDP_Copy_20250514183633_20250514183633.json</h3>

<p>---</p>

<h3>WishList_Copy_20250514185153_20250514185153.json</h3>

<p>---</p>

<h3>Kmart_Prod__Onboarding2_Copy_20250513175727_20250513175727.json</h3>

<p>---</p>

<h3>Others_20250512190312.json</h3>

<p>---</p>

<h3>apple_health_Copy_20250409201903.json</h3>

<p>---</p>

<h3>KmartProdSignin_20250426221008.json</h3>

<p>---</p>

<h3>Delivery__CNC_20250505163250.json</h3>

<p>---</p>

<h3>apple_health_20250407210435.json</h3>

<p>---</p>

<h3>Browse__PDP_20250510095542.json</h3>

<p>---</p>

<h3>KmartSignin_Copy_20250513181428_20250513181428.json</h3>

<p>---</p>

<h3>Login_User_CNC_payment_20250501215014.json</h3>

<p>---</p>

<h3>All_Sign_ins_20250501131834.json</h3>

<p>---</p>

<h3>KmartProdSignin_Copy_20250514190524_20250514190524.json</h3>

<p>---</p>

<h3>Guest_Buy_20250424191014.json</h3>

<p>---</p>

<h3>installkmartau_20250503214553.json</h3>

<p>---</p>

<h3>Kmart_Prod__Onboarding1_20250422134746.json</h3>

<p>---</p>

<h3>Kmart_Prod__Onboarding2_20250422143908.json</h3>

<p>---</p>

<h3>health2_20250408214926.json</h3>

<p>---</p>

<h3>AU_MyAccount_20250506181929.json</h3>

<p>---</p>

<h2>image_comparison</h2>

<h3>compare_images.py</h3>

<strong>Functions:</strong>
<ul><li><code>compare_images(image1_path, image2_path, threshold, min_area_percent, diff_threshold)</code></li></ul>

<p>---</p>

<h3>create_sample_images.py</h3>

<strong>Functions:</strong>
<ul><li><code>create_sample_image(filename, text, size, bg_color, text_color)</code></li>
<li><code>main()</code></li></ul>

<p>---</p>

<h3>test_different.py</h3>

<p>---</p>

<h3>generate_html_report.py</h3>

<strong>Functions:</strong>
<ul><li><code>main()</code></li>
<li><code>__init__(self, baseline_dir, new_dir, output_dir, threshold)</code></li>
<li><code>find_image_pairs(self)</code></li>
<li><code>compare_images(self, baseline_path, new_path)</code></li>
<li><code>image_to_base64(self, image_path)</code></li>
<li><code>run_comparison(self)</code></li>
<li><code>generate_html_report(self)</code></li></ul>

<strong>Classes:</strong>
<ul><li><code>ImageComparator</code></li>
<li><code>__init__(self, baseline_dir, new_dir, output_dir, threshold)</code></li>
<li><code>find_image_pairs(self)</code></li>
<li><code>compare_images(self, baseline_path, new_path)</code></li>
<li><code>image_to_base64(self, image_path)</code></li>
<li><code>run_comparison(self)</code></li>
<li><code>generate_html_report(self)</code></li></ul>

<p>---</p>

<h2>image_comparison/report</h2>

<h3>report.html</h3>

<p>---</p>

<h2>scripts</h2>

<h3>convert_multistep_test_cases.py</h3>

<strong>Functions:</strong>
<ul><li><code>load_test_case(filename)</code></li>
<li><code>save_test_case(test_case_data, filename)</code></li>
<li><code>convert_test_case(filename)</code></li>
<li><code>main()</code></li></ul>

<p>---</p>

<h3>add_action_ids.py</h3>

<strong>Functions:</strong>
<ul><li><code>process_test_case(file_path)</code></li>
<li><code>main()</code></li></ul>

<p>---</p>

<h3>update_database_action_ids.py</h3>

<strong>Functions:</strong>
<ul><li><code>load_test_case(file_path)</code></li>
<li><code>get_test_steps_with_action_ids(test_cases_dir)</code></li>
<li><code>update_database_with_action_ids(steps_with_action_ids)</code></li>
<li><code>main()</code></li></ul>

<p>---</p>


    </div>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html> 