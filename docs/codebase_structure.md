# MobileAppAutomation Codebase Analysis

## Overview

This document provides a summary of the analyzed codebase structure for the MobileAppAutomation project.

## Statistics

- **Total Files**: 244
- **Lines of Code**: 134886
- **Functions**: 845
- **Classes**: 99
- **API Endpoints**: 72

## File Types

- **.json**: 35 files
- **.py**: 134 files
- **.js**: 46 files
- **.css**: 16 files
- **.html**: 13 files

## API Endpoints

- `/api/report/initialize` [POST] - *app/app.py*
- `/api/screenshots/delete_all` [POST] - *app/app.py*
- `/api/logs/save` [POST] - *app/app.py*
- `/` [GET] - *app/app.py*
- `/test_suites` [GET] - *app/app.py*
- `/screenshot` [GET] - *app/app.py*
- `/screenshots/<path:filename>` [GET] - *app/app.py*
- `/api/reference_images` [GET] - *app/app.py*
- `/api/reference_image_preview` [GET] - *app/app.py*
- `/api/devices` [GET] - *app/app.py*
- `/api/device/connect` [POST] - *app/app.py*
- `/api/device/disconnect` [POST] - *app/app.py*
- `/api/check_inspector` [GET] - *app/app.py*
- `/api/device/dimensions` [GET] - *app/app.py*
- `/api/screenshot` [GET, POST] - *app/app.py*
- `/api/capture_image` [POST] - *app/app.py*
- `/api/action/tap` [POST] - *app/app.py*
- `/api/action/swipe` [POST] - *app/app.py*
- `/api/action/text` [POST] - *app/app.py*
- `/api/action/key` [POST] - *app/app.py*
- `/api/recording/start` [POST] - *app/app.py*
- `/api/recording/stop` [POST] - *app/app.py*
- `/api/recording/save` [POST] - *app/app.py*
- `/api/recording/list` [GET] - *app/app.py*
- `/get_element_at_position` [POST] - *app/app.py*
- `/api/element/at_position` [POST] - *app/app.py*
- `/api/action/stop` [POST] - *app/app.py*
- `/api/execute_test_case` [POST] - *app/app.py*
- `/api/action/execute` [POST] - *app/app.py*
- `/api/action/execute_hook` [POST] - *app/app.py*
- `/api/page_source` [GET] - *app/app.py*
- `/api/session_info` [GET] - *app/app.py*
- `/api/test_cases/files` [GET] - *app/app.py*
- `/api/test_cases/load_file/<filename>` [GET] - *app/app.py*
- `/api/session/info` [GET] - *app/app.py*
- `/appium/inspect_element` [POST] - *app/app.py*
- `/api/test_cases/load/<filename>` [GET] - *app/app.py*
- `/api/test_cases_for_multi_step` [GET] - *app/app.py*
- `/api/device/health_check` [GET] - *app/app.py*
- `/api/device/reconnect` [POST] - *app/app.py*
- `/api/device/uninstall_app` [POST] - *app/app.py*
- `/api/device/fix_emulator` [POST] - *app/app.py*
- `/api/recording/rename` [POST] - *app/app.py*
- `/api/recording/duplicate` [POST] - *app/app.py*
- `/api/action/swipeTillVisible` [POST] - *app/app.py*
- `/api/test_suites/list` [GET] - *app/app.py*
- `/api/test_suites/<suite_id>` [GET] - *app/app.py*
- `/api/test_suites/create` [POST] - *app/app.py*
- `/api/test_suites/<suite_id>/update` [POST] - *app/app.py*
- `/api/test_suites/<suite_id>/duplicate` [POST] - *app/app.py*
- `/api/test_suites/<suite_id>` [DELETE] - *app/app.py*
- `/api/delete_test_case/<filename>` [DELETE] - *app/app.py*
- `/api/test_suites/<suite_id>/run` [POST] - *app/app.py*
- `/api/reports/latest` [GET] - *app/app.py*
- `/api/reports/list` [GET] - *app/app.py*
- `/api/reports/download/<path:filename>` [GET] - *app/app.py*
- `/api/reports/download_zip/<path:filename>` [GET] - *app/app.py*
- `/api/reports/regenerate/<path:report_id>` [GET] - *app/app.py*
- `/api/database/clear_screenshots` [POST] - *app/app.py*
- `/api/database/reset` [POST] - *app/app.py*
- `/api/reports/delete/<path:filename>` [DELETE] - *app/app.py*
- `/api/reports/get_test_data/<suite_id>` [GET] - *app/app.py*
- `/api/reports/generate/<suite_id>` [GET] - *app/app.py*
- `/api/generate_report` [POST] - *app/app.py*
- `/reports/<path:filename>` [GET] - *app/app.py*
- `/api/upload_media` [POST] - *app/app.py*
- `/api/settings` [GET] - *app/app.py*
- `/api/settings` [POST] - *app/app.py*
- `/api/capture_image_area` [POST] - *app/app.py*
- `/api/ios/set_clipboard` [POST] - *app/app.py*
- `/api/ios/paste_clipboard` [POST] - *app/app.py*
- `/api/text_detection` [POST] - *app/app.py*

## Module Structure

### app

- environments.json
- __init__.py
- app.py
- test_suites_manager.py
### app/utils

- id_generator.py
- text_detector.py
- database_update.py
- screenshot_handler.py
- text_detection.py
- reportGenerator.py
- coordinate_validator.py
- openReport.js
- test_case_manager.py
- database.py
- reset_database.py
- appium_device_controller.py
- ios_device.py
- recorder.py
- __init__.py
- reportGenerator.js
- test.json
- image_matcher.py
- parameter_utils.py
- player.py
- global_values_db.py
- file_utils.py
- random_data_generator.py
- screenshot_manager.py
- build_data_json.py
- id_generator.py
- text_detector.py
- database_update.py
- screenshot_handler.py
- text_detection.py
- reportGenerator.py
- coordinate_validator.py
- openReport.js
- test_case_manager.py
- database.py
- reset_database.py
- appium_device_controller.py
- ios_device.py
- recorder.py
- __init__.py
- reportGenerator.js
- test.json
- image_matcher.py
- parameter_utils.py
- player.py
- global_values_db.py
- file_utils.py
- random_data_generator.py
- screenshot_manager.py
- build_data_json.py
### app/static/css

- modern-styles.css
- fixed-device-screen.css
- test-case.css
- execution-overlay.css
- fallback-locators.css
- test-cases-styles.css
- style.css
- test-suites-styles.css
- modern-styles.css
- fixed-device-screen.css
- test-case.css
- execution-overlay.css
- fallback-locators.css
- test-cases-styles.css
- style.css
- test-suites-styles.css
### app/static/js

- hook-action.js
- action-manager.js
- test_suites.js
- execution-overlay.js
- action-description.js
- main.js
- socket-handlers.js
- tap-fallback-manager.js
- execution-manager.js
- random-data-generator.js
- utils.js
- reports.js
- multi-step-action.js
- fixed-device-screen.js
- app.js
- settings.js
- hook-action.js
- action-manager.js
- test_suites.js
- execution-overlay.js
- action-description.js
- main.js
- socket-handlers.js
- tap-fallback-manager.js
- execution-manager.js
- random-data-generator.js
- utils.js
- reports.js
- multi-step-action.js
- fixed-device-screen.js
- app.js
- settings.js
### app/static/js/actions

- swipe-till-visible.js
- swipe-till-visible.js
### app/static/js/modules

- fallback-locators.js
- tap-fallback-manager.js
- ElementInteractions.js
- TestCaseManager.js
- fallback-locators.js
- tap-fallback-manager.js
- ElementInteractions.js
- TestCaseManager.js
### app/static/templates

- index.html
- index.html
### app/actions

- tap_if_image_exists_action.py
- multi_step_action.py
- swipe_action.py
- get_param_action.py
- wait_action.py
- terminate_app_action.py
- double_click_image_action.py
- uninstall_app_action.py
- text_action.py
- wait_till_action.py
- __init__.py
- base_action.py
- hook_action.py
- input_text_action.py
- send_keys_action.py
- set_param_action.py
- airplane_mode_action.py
- ios_functions_action.py
- swipe_till_visible_action.py
- click_image_action.py
- tap_action.py
- if_else_steps_action.py
- key_action.py
- add_media_action.py
- hide_keyboard_action.py
- tap_and_type_action.py
- tap_on_text_action.py
- launch_app_action.py
- wait_element_action.py
- compare_value_action.py
- device_back_action.py
- click_element_action.py
- random_data_action.py
- get_value_action.py
- action_factory.py
- restart_app_action.py
- double_tap_action.py
- add_log_action.py
- tap_if_image_exists_action.py
- multi_step_action.py
- swipe_action.py
- get_param_action.py
- wait_action.py
- terminate_app_action.py
- double_click_image_action.py
- uninstall_app_action.py
- text_action.py
- wait_till_action.py
- __init__.py
- base_action.py
- hook_action.py
- input_text_action.py
- send_keys_action.py
- set_param_action.py
- airplane_mode_action.py
- ios_functions_action.py
- swipe_till_visible_action.py
- click_image_action.py
- tap_action.py
- if_else_steps_action.py
- key_action.py
- add_media_action.py
- hide_keyboard_action.py
- tap_and_type_action.py
- tap_on_text_action.py
- launch_app_action.py
- wait_element_action.py
- compare_value_action.py
- device_back_action.py
- click_element_action.py
- random_data_action.py
- get_value_action.py
- action_factory.py
- restart_app_action.py
- double_tap_action.py
- add_log_action.py
### app/templates

- index.html
- tap_on_text_form.html
- settings.html
- tap-fallback-modal.html
- test_suites.html
- index.html
- tap_on_text_form.html
- settings.html
- tap-fallback-modal.html
- test_suites.html
### app/routes

- __init__.py
- random_data_routes.py
- __init__.py
- random_data_routes.py
### test_cases

- Postcode_Flow_Copy_20250513180904_20250513180904.json
- Delivery__CNC_Copy_20250513201112_20250513201112.json
- Postcode_Flow_Copy_20250518114624_20250518114624.json
- OnePass_Account_Signin_20250510101051.json
- Delivery_Buy_Steps_20250512194232.json
- All_Sign_ins_Copy_20250514181123_20250514181123.json
- WishList_20250510110236.json
- SampleApp_20250401224106.json
- KmartProdSignin_Copy_20250501144222_20250501144222.json
- Delivery__Buy_20250505165058.json
- Delivery_Buy_Steps_Copy_20250513195816_20250513195816.json
- Postcode_Flow_20250502104451.json
- AU_MyAccount_Copy_20250514192836_20250514192836.json
- Browse__PDP_Copy_20250514183633_20250514183633.json
- WishList_Copy_20250514185153_20250514185153.json
- Kmart_Prod__Onboarding2_Copy_20250513175727_20250513175727.json
- Others_20250512190312.json
- apple_health_Copy_20250409201903.json
- KmartProdSignin_20250426221008.json
- Delivery__CNC_20250505163250.json
- apple_health_20250407210435.json
- Browse__PDP_20250510095542.json
- KmartSignin_Copy_20250513181428_20250513181428.json
- Login_User_CNC_payment_20250501215014.json
- All_Sign_ins_20250501131834.json
- KmartProdSignin_Copy_20250514190524_20250514190524.json
- Guest_Buy_20250424191014.json
- installkmartau_20250503214553.json
- Kmart_Prod__Onboarding1_20250422134746.json
- Kmart_Prod__Onboarding2_20250422143908.json
- health2_20250408214926.json
- AU_MyAccount_20250506181929.json
### image_comparison

- compare_images.py
- create_sample_images.py
- test_different.py
- generate_html_report.py
### image_comparison/report

- report.html
### scripts

- convert_multistep_test_cases.py
- add_action_ids.py
- update_database_action_ids.py
