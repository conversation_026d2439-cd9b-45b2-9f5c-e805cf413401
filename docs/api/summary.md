# API Endpoints Summary

This page provides a summary of all API endpoints in the MobileAppAutomation application.

## `/api/report/initialize`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/screenshots/delete_all`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/logs/save`

**Methods:** POST

**Defined in:** app/app.py

---

## `/`

**Methods:** GET

**Defined in:** app/app.py

---

## `/test_suites`

**Methods:** GET

**Defined in:** app/app.py

---

## `/screenshot`

**Methods:** GET

**Defined in:** app/app.py

---

## `/screenshots/<path:filename>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reference_images`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reference_image_preview`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/devices`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/device/connect`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/device/disconnect`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/check_inspector`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/device/dimensions`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/screenshot`

**Methods:** GET, POST

**Defined in:** app/app.py

---

## `/api/capture_image`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/tap`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/swipe`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/text`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/key`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/recording/start`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/recording/stop`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/recording/save`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/recording/list`

**Methods:** GET

**Defined in:** app/app.py

---

## `/get_element_at_position`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/element/at_position`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/stop`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/execute_test_case`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/execute`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/execute_hook`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/page_source`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/session_info`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/test_cases/files`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/test_cases/load_file/<filename>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/session/info`

**Methods:** GET

**Defined in:** app/app.py

---

## `/appium/inspect_element`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/test_cases/load/<filename>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/test_cases_for_multi_step`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/device/health_check`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/device/reconnect`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/device/uninstall_app`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/device/fix_emulator`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/recording/rename`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/recording/duplicate`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/action/swipeTillVisible`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/test_suites/list`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/test_suites/<suite_id>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/test_suites/create`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/test_suites/<suite_id>/update`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/test_suites/<suite_id>/duplicate`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/test_suites/<suite_id>`

**Methods:** DELETE

**Defined in:** app/app.py

---

## `/api/delete_test_case/<filename>`

**Methods:** DELETE

**Defined in:** app/app.py

---

## `/api/test_suites/<suite_id>/run`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/reports/latest`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reports/list`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reports/download/<path:filename>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reports/download_zip/<path:filename>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reports/regenerate/<path:report_id>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/database/clear_screenshots`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/database/reset`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/reports/delete/<path:filename>`

**Methods:** DELETE

**Defined in:** app/app.py

---

## `/api/reports/get_test_data/<suite_id>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/reports/generate/<suite_id>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/generate_report`

**Methods:** POST

**Defined in:** app/app.py

---

## `/reports/<path:filename>`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/upload_media`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/settings`

**Methods:** GET

**Defined in:** app/app.py

---

## `/api/settings`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/capture_image_area`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/ios/set_clipboard`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/ios/paste_clipboard`

**Methods:** POST

**Defined in:** app/app.py

---

## `/api/text_detection`

**Methods:** POST

**Defined in:** app/app.py

---

