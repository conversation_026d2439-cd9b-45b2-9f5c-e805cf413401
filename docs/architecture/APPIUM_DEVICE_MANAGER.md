# Appium Device Manager (ADM)

A comprehensive command-line interface for managing Appium Grid devices and sessions, inspired by the AppiumDeviceManager application. This tool provides enhanced device management capabilities, robust connection handling, and comprehensive monitoring for mobile test automation.

## Features

- **Grid Management**: Start, stop, and restart Appium Grid with enhanced error handling
- **Device Discovery**: Automatically detect and list available iOS and Android devices
- **Session Management**: Track and manage active test sessions across multiple devices
- **Health Monitoring**: Comprehensive health checks and real-time monitoring
- **Connection Testing**: Test connectivity to devices with retry logic and fallback
- **Log Management**: Easy access to Grid and node logs for troubleshooting
- **Configuration Display**: View current Grid and node configurations
- **Parallel Testing Support**: Enhanced support for running multiple sessions simultaneously

## Installation

The Appium Device Manager is included with the MobileAppAutomation project. No additional installation is required.

## Quick Start

### Using the ADM Command

```bash
# Make the script executable (first time only)
chmod +x adm

# Start the Appium Grid
./adm start

# Check Grid status
./adm status

# List available devices
./adm devices

# Monitor Grid continuously
./adm monitor
```

### Using Python Directly

```bash
# Run with Python
python appium_device_manager.py status

# Or use the grid manager directly
python grid_manager.py health
```

## Commands

### Grid Management

#### Start Grid
```bash
./adm start [--wait]
```
- Starts the Appium Grid Hub and all configured nodes
- `--wait`: Wait for all nodes to be ready before returning

#### Stop Grid
```bash
./adm stop
```
- Stops all Grid components gracefully

#### Restart Grid
```bash
./adm restart [--wait]
```
- Stops and starts the Grid with enhanced error handling
- `--wait`: Wait for all nodes to be ready after restart

### Status and Monitoring

#### Grid Status
```bash
./adm status
```
- Shows comprehensive Grid health information
- Lists registered nodes and their status
- Displays active sessions and any issues

#### Device List
```bash
./adm devices
```
- Lists all available devices with detailed information
- Shows platform, device name, version, automation type, and status
- Displays current session usage

#### Active Sessions
```bash
./adm sessions
```
- Lists all active test sessions
- Shows session duration, platform, and device information

#### Real-time Monitoring
```bash
./adm monitor [--interval SECONDS]
```
- Continuously monitors Grid status
- Refreshes every 5 seconds by default
- Press Ctrl+C to stop monitoring

### Diagnostics and Troubleshooting

#### Connection Testing
```bash
./adm test [--platform PLATFORM]
```
- Tests connectivity to devices
- Supports both Grid and direct connections
- `--platform`: Test specific platform (iOS or Android)

#### Log Viewing
```bash
./adm logs COMPONENT [--lines N]
```
- View Grid component logs
- Components: `hub`, `ios`, `android`
- `--lines`: Number of recent lines to show (default: 50)

Examples:
```bash
./adm logs hub --lines 100
./adm logs ios
./adm logs android --lines 25
```

#### Configuration Display
```bash
./adm config
```
- Shows current Grid configuration
- Displays node configurations
- Shows connection URLs and settings

## Grid Manager API

The `GridManager` class provides programmatic access to Grid functionality:

```python
from grid_manager import GridManager

# Create manager instance
manager = GridManager(timeout=30, retry_attempts=3)

# Start Grid
success, message = manager.start_grid()

# Get device information
devices = manager.get_device_info()

# Health check
health = manager.health_check()

# Get connection URL with retry logic
url = manager.get_connection_url_with_retry("iOS")
```

### Key Methods

- `start_grid()`: Start Grid services
- `stop_grid()`: Stop Grid services
- `restart_grid()`: Restart Grid services
- `get_device_info()`: Get available devices
- `health_check()`: Comprehensive health check
- `get_connection_url_with_retry()`: Get connection URL with fallback
- `create_session()`: Create and track sessions
- `end_session()`: End and cleanup sessions

## Configuration

The Grid Manager uses the existing `grid_config.py` for configuration:

```python
# Grid URLs
GRID_HUB_URL = "http://localhost:4444/wd/hub"
GRID_STATUS_URL = "http://localhost:4444/status"

# Direct connection URLs (fallback)
IOS_APPIUM_URL = "http://localhost:4723/wd/hub"
ANDROID_APPIUM_URL = "http://localhost:4724/wd/hub"
```

## Enhanced Features

### Retry Logic and Fallback

The Grid Manager includes intelligent retry logic:

1. **Grid Connection**: Attempts to use Grid first
2. **Node Availability**: Checks for available platform-specific nodes
3. **Fallback**: Falls back to direct Appium connection if Grid unavailable
4. **Exponential Backoff**: Uses exponential backoff for retries

### Session Tracking

Automatic session tracking provides:

- Session lifecycle management
- Duration tracking
- Platform and device association
- Cleanup on termination

### Health Monitoring

Comprehensive health checks include:

- Grid Hub status
- Node registration status
- Active session counts
- Issue detection and reporting

## Example Usage

### Basic Grid Management

```bash
# Start Grid and wait for readiness
./adm start --wait

# Check status
./adm status

# List devices
./adm devices

# Test connections
./adm test

# Stop Grid
./adm stop
```

### Troubleshooting Workflow

```bash
# Check Grid health
./adm status

# View recent hub logs
./adm logs hub --lines 50

# Test iOS connection
./adm test --platform iOS

# Monitor continuously
./adm monitor
```

### Integration with Tests

```python
from grid_manager import get_grid_manager
from appium import webdriver

# Get Grid Manager instance
manager = get_grid_manager()

# Ensure Grid is ready
if not manager.is_hub_ready():
    manager.start_grid()

# Get connection URL with retry
url = manager.get_connection_url_with_retry("iOS")

# Create driver
driver = webdriver.Remote(url, capabilities)

# Create session tracking
session_id = manager.create_session("iOS", capabilities)

# ... run tests ...

# Cleanup
driver.quit()
manager.end_session(session_id)
```

## Troubleshooting

### Common Issues

1. **Grid Not Starting**
   ```bash
   ./adm logs hub
   ./adm config
   ```

2. **Nodes Not Registering**
   ```bash
   ./adm logs ios
   ./adm logs android
   ./adm test
   ```

3. **Connection Failures**
   ```bash
   ./adm test --platform iOS
   ./adm status
   ```

4. **Port Conflicts**
   ```bash
   lsof -i :4444  # Grid Hub
   lsof -i :4723  # iOS Appium
   lsof -i :4724  # Android Appium
   ```

### Log Locations

- Hub logs: `grid/logs/hub.log`
- iOS node logs: `grid/logs/ios-registration.log`
- Android node logs: `grid/logs/android-registration.log`

## Advanced Usage

### Custom Timeout and Retry Settings

```python
from grid_manager import GridManager

# Custom settings
manager = GridManager(timeout=60, retry_attempts=5)
```

### Monitoring Integration

```python
# Continuous health monitoring
while True:
    health = manager.health_check()
    if health['issues']:
        # Handle issues
        pass
    time.sleep(30)
```

### Session Management

```python
# Get active sessions
sessions = manager.get_active_sessions()

# End specific session
manager.end_session(session_id)

# Get session info
for session in sessions:
    print(f"Session {session.session_id}: {session.platform.value}")
```

## Integration with Existing Tools

The Appium Device Manager integrates seamlessly with:

- **Existing Grid Configuration**: Uses current `grid/` directory structure
- **Test Scripts**: Provides enhanced connection handling
- **CI/CD Pipelines**: Command-line interface for automation
- **Monitoring Systems**: JSON output for integration

## Performance Considerations

- **Parallel Sessions**: Supports multiple simultaneous sessions
- **Resource Management**: Automatic cleanup and session tracking
- **Connection Pooling**: Efficient connection reuse
- **Timeout Handling**: Configurable timeouts for different scenarios

## Future Enhancements

- Web-based dashboard for Grid monitoring
- Integration with test reporting systems
- Advanced device filtering and selection
- Automated device provisioning
- Performance metrics and analytics

---

**Note**: This Appium Device Manager provides functionality similar to the legacy AppiumDeviceManager workspace, but is specifically designed for this project's Grid architecture and requirements.
