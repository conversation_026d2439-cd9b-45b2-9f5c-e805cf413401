# Dynamic Port Management System

This document describes the dynamic port management system implemented for the Mobile App Automation tool, which allows running multiple iOS and Android automation instances simultaneously without port conflicts.

## Overview

The dynamic port management system automatically allocates unique ports for:
- **Flask applications** (iOS and Android apps)
- **Appium servers** (for device communication)
- **WDA (WebDriverAgent)** ports (for iOS device control)
- **Dashboard** (unified control interface)

## Key Features

### 1. Automatic Port Allocation
- Dynamically assigns available ports from predefined ranges
- Prevents port conflicts when running multiple instances
- Maintains port state across sessions
- Supports both iOS and Android platforms simultaneously

### 2. Secure Build Support
- Dashboard automatically uses port 8080 for secure builds
- Falls back to port 8090 for development builds
- Environment variable `SECURE_BUILD=true` triggers secure port mode

### 3. Conflict Detection and Resolution
- Automatically detects port conflicts
- Provides fallback mechanisms
- Cleans up stale port allocations

## Port Ranges

| Service | Port Range | Default iOS | Default Android |
|---------|------------|-------------|----------------|
| Flask   | 8080-8120  | 8080        | 8081           |
| Appium  | 4720-4760  | 4723        | 4724           |
| WDA     | 8200-8310  | 8200        | 8300           |
| Dashboard | 8080/8090 | N/A         | N/A            |

## Usage

### 1. Using the Unified Launcher (Recommended)

```bash
# Start both iOS and Android apps with dynamic ports
python3 start_dynamic_apps.py

# Start only iOS app
python3 start_dynamic_apps.py --platforms ios

# Start only Android app
python3 start_dynamic_apps.py --platforms android

# Start with custom dashboard port
python3 start_dynamic_apps.py --dashboard-port 8085

# Start with custom iOS ports
python3 start_dynamic_apps.py --ios-flask-port 8090 --ios-appium-port 4730

# Start without dashboard
python3 start_dynamic_apps.py --no-dashboard
```

### 2. Individual Platform Apps

#### iOS App
```bash
# Dynamic port allocation (recommended)
python3 run.py

# Custom ports
python3 run.py --flask-port 8085 --appium-port 4730 --wda-port 8210

# Static ports (legacy mode)
python3 run.py --use-static-ports
```

#### Android App
```bash
# Dynamic port allocation (recommended)
python3 run_android.py

# Custom ports
python3 run_android.py --flask-port 8086 --appium-port 4731 --wda-port 8211

# Static ports (legacy mode)
python3 run_android.py --use-static-ports
```

### 3. Dashboard Only
```bash
# Development mode (port 8090)
python3 dashboard_app.py

# Secure build mode (port 8080)
SECURE_BUILD=true python3 dashboard_app.py

# Custom port
DASHBOARD_PORT=8095 python3 dashboard_app.py
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|----------|
| `SECURE_BUILD` | Enable secure build mode for dashboard | `false` |
| `DASHBOARD_PORT` | Override dashboard port | `8090` (dev) / `8080` (secure) |
| `FLASK_PORT` | Override Flask app port | Dynamic allocation |
| `APPIUM_PORT` | Override Appium server port | Dynamic allocation |
| `WDA_PORT` | Override WDA port | Dynamic allocation |

## Port Management Utilities

### Check System Status
```bash
python3 utils/port_manager.py --status
```

### Clean Up Ports
```bash
# Clean up all ports
python3 utils/port_manager.py --cleanup all

# Clean up iOS ports only
python3 utils/port_manager.py --cleanup ios

# Clean up Android ports only
python3 utils/port_manager.py --cleanup android

# Force cleanup (kill processes)
python3 utils/port_manager.py --cleanup all --force
```

### Check Specific Port
```bash
python3 utils/port_manager.py --port 8080
```

### Initialize Platform Ports
```bash
# Initialize iOS ports
python3 utils/dynamic_port_init.py ios

# Initialize Android ports
python3 utils/dynamic_port_init.py android

# Check for conflicts
python3 utils/dynamic_port_init.py ios --check-conflicts

# Cleanup platform ports
python3 utils/dynamic_port_init.py ios --cleanup
```

## Multiple Instance Examples

### Running Multiple iOS Instances
```bash
# Terminal 1: First iOS instance (uses default ports)
python3 run.py

# Terminal 2: Second iOS instance (gets next available ports)
python3 run.py

# Terminal 3: Third iOS instance with custom ports
python3 run.py --flask-port 8095 --appium-port 4735 --wda-port 8215
```

### Running iOS and Android Simultaneously
```bash
# Terminal 1: Start both platforms
python3 start_dynamic_apps.py

# Or start them separately:
# Terminal 1: iOS
python3 run.py

# Terminal 2: Android
python3 run_android.py

# Terminal 3: Dashboard
python3 dashboard_app.py
```

## Troubleshooting

### Port Conflicts
If you encounter port conflicts:

1. **Check current allocations:**
   ```bash
   python3 utils/port_manager.py --status
   ```

2. **Clean up stale allocations:**
   ```bash
   python3 utils/port_manager.py --cleanup all
   ```

3. **Force kill processes on specific ports:**
   ```bash
   python3 utils/port_manager.py --cleanup all --force
   ```

4. **Use custom ports:**
   ```bash
   python3 run.py --flask-port 8095 --appium-port 4735
   ```

### Common Issues

1. **"Port already in use" errors:**
   - Run cleanup command
   - Check for zombie processes: `lsof -i :PORT_NUMBER`
   - Use `--force` flag for cleanup

2. **Dynamic allocation fails:**
   - Check available ports in range
   - Use `--use-static-ports` as fallback
   - Specify custom ports manually

3. **Dashboard not connecting to apps:**
   - Ensure apps are started before dashboard
   - Check environment variables are set correctly
   - Verify no firewall blocking connections

### Logs and Debugging

Enable detailed logging:
```bash
export PYTHONPATH="$PWD:$PYTHONPATH"
export LOG_LEVEL=DEBUG
python3 start_dynamic_apps.py
```

Check port manager logs:
```bash
python3 -c "import logging; logging.basicConfig(level=logging.DEBUG); from utils.port_manager import get_port_manager; pm = get_port_manager(); print(pm.get_system_status())"
```

## Architecture

### Components

1. **PortManager** (`utils/port_manager.py`)
   - Core port allocation and management
   - State persistence
   - Process monitoring

2. **DynamicPortInit** (`utils/dynamic_port_init.py`)
   - Platform-specific port initialization
   - Environment variable management
   - Conflict detection

3. **DynamicAppLauncher** (`start_dynamic_apps.py`)
   - Unified launcher for multiple platforms
   - Process monitoring and cleanup
   - Signal handling

### State Management

Port allocations are persisted in:
- **File:** `~/.mobile_automation/port_state.json`
- **Format:** JSON with port mappings and metadata
- **Cleanup:** Automatic cleanup of stale allocations

### Integration Points

1. **iOS App** (`run.py`)
   - Modified to use dynamic port initialization
   - Fallback to static ports if needed

2. **Android App** (`run_android.py`)
   - Modified to use dynamic port initialization
   - Fallback to static ports if needed

3. **Dashboard** (`dashboard_app.py`)
   - Automatic port selection based on build type
   - Service URL configuration from environment

4. **Configuration Files**
   - `config.py`: iOS configuration with dynamic ports
   - `config_android.py`: Android configuration with dynamic ports

## Migration from Static Ports

If you're upgrading from a static port configuration:

1. **Backup existing configuration:**
   ```bash
   cp config.py config.py.backup
   cp config_android.py config_android.py.backup
   ```

2. **Use legacy mode temporarily:**
   ```bash
   python3 run.py --use-static-ports
   python3 run_android.py --use-static-ports
   ```

3. **Gradually migrate to dynamic ports:**
   ```bash
   # Test with dynamic ports
   python3 run.py
   
   # If successful, remove --use-static-ports flag
   ```

4. **Update any external scripts or configurations** that reference hardcoded ports.

## Best Practices

1. **Always use the unified launcher** for multiple instances
2. **Clean up ports** when switching between different configurations
3. **Use environment variables** for configuration instead of hardcoded values
4. **Monitor port usage** in production environments
5. **Set up proper logging** for debugging port issues
6. **Use secure build mode** for production deployments

## Support

For issues related to dynamic port management:

1. Check the troubleshooting section above
2. Review logs with DEBUG level enabled
3. Use port management utilities to diagnose issues
4. Fall back to static ports if dynamic allocation fails

The dynamic port system is designed to be backward compatible, so existing static configurations will continue to work with the `--use-static-ports` flag.