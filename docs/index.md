# Mobile Automation Framework Documentation

This directory contains all documentation for the Mobile Automation Framework, organized by category.

## Documentation Categories

### Setup and Installation Guides
Location: `docs/setup/`

- [Device Agent Setup](setup/DEVICE_AGENT_SETUP.md)
- [Readme Agent](setup/README_AGENT.md)
- [Readme Build](setup/README_BUILD.md)

### Deployment and Production Guides
Location: `docs/deployment/`

- [Deployment Guide](deployment/DEPLOYMENT_GUIDE.md)
- [Deployment Guide Comprehensive](deployment/DEPLOYMENT_GUIDE_COMPREHENSIVE.md)
- [Production Deployment Guide](deployment/PRODUCTION_DEPLOYMENT_GUIDE.md)
- [Saas Readiness Assessment](deployment/SAAS_READINESS_ASSESSMENT.md)

### Android-Specific Documentation
Location: `docs/android/`

- [Android Connection Fixes Summary](android/ANDROID_CONNECTION_FIXES_SUMMARY.md)
- [Android Session Stability Fixes](android/ANDROID_SESSION_STABILITY_FIXES.md)
- [Android Cleanup Element Wait Fixes Summary](android/ANDROID_CLEANUP_ELEMENT_WAIT_FIXES_SUMMARY.md)
- [Android Element Identification Fixes](android/ANDROID_ELEMENT_IDENTIFICATION_FIXES.md)
- [Android Performance Recommendations](android/ANDROID_PERFORMANCE_RECOMMENDATIONS.md)
- [Android Cleanup Solution Summary](android/ANDROID_CLEANUP_SOLUTION_SUMMARY.md)

### Architecture and System Design
Location: `docs/architecture/`

- [Appium Grid Implementation](architecture/APPIUM_GRID_IMPLEMENTATION.md)
- [Appium Device Manager](architecture/APPIUM_DEVICE_MANAGER.md)
- [Appium Device Manager Integration](architecture/APPIUM_DEVICE_MANAGER_INTEGRATION.md)
- [Concurrent Execution Solution](architecture/CONCURRENT_EXECUTION_SOLUTION.md)
- [Dynamic Ports Readme](architecture/DYNAMIC_PORTS_README.md)

### Analysis and Implementation Reports
Location: `docs/reports/`

- [Comprehensive Image Fixes Report](reports/COMPREHENSIVE_IMAGE_FIXES_REPORT.md)
- [Comprehensive Fixes Implementation Report](reports/COMPREHENSIVE_FIXES_IMPLEMENTATION_REPORT.md)
- [Comprehensive Test Analysis Report](reports/COMPREHENSIVE_TEST_ANALYSIS_REPORT.md)
- [Critical Issues Resolution Report](reports/CRITICAL_ISSUES_RESOLUTION_REPORT.md)
- [Aluminium Ai Integration Report](reports/ALUMINIUM_AI_INTEGRATION_REPORT.md)

### Solution Summaries and Fixes
Location: `docs/solutions/`

- [Environment Resolution Solution Summary](solutions/ENVIRONMENT_RESOLUTION_SOLUTION_SUMMARY.md)
- [Final Solution Summary](solutions/FINAL_SOLUTION_SUMMARY.md)
- [Multistep Cleanup Solution](solutions/MULTISTEP_CLEANUP_SOLUTION.md)
- [Environment Resolution Fix Summary](solutions/ENVIRONMENT_RESOLUTION_FIX_SUMMARY.md)
- [Environment Isolation Fix Summary](solutions/ENVIRONMENT_ISOLATION_FIX_SUMMARY.md)

### User Guides and Tutorials
Location: `docs/guides/`

- [Cleanup Verification Guide](guides/CLEANUP_VERIFICATION_GUIDE.md)
- [Ocr Optimization Readme](guides/OCR_OPTIMIZATION_README.md)

## Main Documentation

- [README](../README.md) - Main project documentation (in project root)

## Navigation

- [Project Root](../) - Return to main project directory
- [Source Code](../app/) - iOS application source
- [Android Source](../app_android/) - Android application source
- [Tools](../tools/) - Development and testing tools

## Contributing

When adding new documentation:

1. Place files in the appropriate category directory
2. Update this index.md file
3. Use clear, descriptive filenames
4. Follow the existing documentation format

## File Organization

All temporary files, logs, and generated content are automatically organized:
- Temporary files: Configured temp directories (see settings)
- Screenshots: Platform-specific directories
- Reports: Platform-specific report directories
- Logs: Temp directories (not in project root)
