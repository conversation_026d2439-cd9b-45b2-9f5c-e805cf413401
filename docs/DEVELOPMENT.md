# Development Setup Guide

## Environment setup

```bash
# Create venv and install deps
./setup.sh

# Start backend from source
python3 secure_distribution_app/web_server/app.py

# Run GUI
./venv/bin/python gui_app/qt_gui.py
```

## Browser compatibility testing
- Preferred: Playwright Chromium (installed by setup.sh)
- If Chromium is missing or cannot launch, the GUI automatically falls back to the system browser.
- macOS: if Google Chrome is installed, we try `channel='chrome'` to avoid downloads.

## Authentication/session
- The GUI passes session_token via query string to platform apps on localhost.
- The return_url parameter points back to the backend dashboard.

## Debugging tips
- Backend logs:
  - Ensure `[AndroidLaunch]` lines appear when launching Android
  - Verify endpoints: `/health`, `/api/ports/status`, `/api/ports/active`
- Platform logs:
  - Check `secure_distribution_app/logs/android_launch_*.log` and `ios_launch_*.log`
- If Playwright errors:
  - Run `./venv/bin/playwright install chromium`
  - Or rely on fallback to system browser

## Common issues
- 404 on `/api/ports/active`: You are running an older bundled backend. Start `secure_distribution_app/web_server/app.py` from source.
- Exit code 1 on Android launch: Ensure correct interpreter and script path in logs; old binary may be running.
- Corporate firewall blocks Playwright download: Use system browser fallback.

