# Deployment Guide

## Packaging & Distribution
- Build secure binaries with the scripts in `secure_distribution_app/` (e.g., `build_final_secure_app.py`).
- Distribute artifacts from `secure_distribution_app/dist`.

## System requirements
- Python 3.8+ for development mode (not required for packaged binaries)
- Node.js & npm for Appium
- macOS (for iOS testing): Xcode + CLT, libimobiledevice, tidevice
- Android: ADB in PATH

## Browser strategy
- At runtime, the platform prefers Playwright Chromium.
- If Chromium is not available or fails to launch, it falls back to the system browser (Windows/macOS/Linux).
- This ensures the tool never fails due to browser download issues.

## Installation steps (end users)
- If using package/binary, run the provided start script.
- For development mode, run `./setup.sh`.

## Troubleshooting (browser-related)
- Error: `Executable doesn't exist ... playwright` → install browsers with `playwright install chromium`, or rely on fallback.
- Corporate firewall blocks downloads → fallback system browser is used automatically.
- No default browser configured → manually open the platform URL printed in GUI logs.

## Verification checklist
- Backend `/health` returns 200
- `/api/ports/active` returns session ports after login
- Android launch binds to 8091 (or dynamic) and Appium to 4724
- iOS launch binds to 8090 and Appium to 4723
- G<PERSON> opens platform either via Playwright Chromium or system browser

