# Secure Distribution Build Guide

This guide describes how to produce the hardened, multi-platform delivery
package for the Mobile App Automation suite after code changes.

---

## 1. Prerequisites

- macOS 13+/Windows 11/Ubuntu 22.04 build host (macOS recommended for signing).
- Xcode command line tools (macOS) or Build Tools for Visual Studio (Windows).
- Docker 24+ (used for reproducible build environments).
- Python 3.11+ with virtualenv, Node.js 20+, Yarn or npm, and Rust toolchain.
- Access to code-signing identities:
  - Apple Developer ID (macOS notarisation).
  - Authenticode certificate (Windows).
  - GPG key (Linux packages).
- Supabase service credentials stored in `secure_distribution_app/.env`.

```bash
# Ensure base environment is installed
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
npm install
npm run appium:install
```

---

## 2. Build Workflow Overview

1. **Sync source** – `git pull` and review open changes.
2. **Run automated tests** – `pytest`, unit tests, and smoke runs against a local
   device where possible.
3. **Generate secure assets** – execute the bundler which now packages Python
   wheels, Node modules, Appium drivers, sanitised databases, and manifests.
4. **Produce platform installers** – the PyInstaller bundle is wrapped in per-OS
   installer formats, signed, and notarised.
5. **Publish** – upload installers, license manifests, and integrity hashes to the
   release channel.

---

## 3. Automated Build Command

All steps above are scripted in `secure_distribution_app/build_final_secure_app.py`.
Run it from the project root after activating the virtual environment:

```bash
source venv/bin/activate
python3 secure_distribution_app/build_final_secure_app.py
```

The script performs the following automatically:

- Copies `app/` and `app_android/` plus configuration (`config.py`,
  `config_android.py`, `shared_directory_paths_db.py`).
- Vendors Python dependencies from the project virtual environment into
  `runtime/python_env` (with a pip fallback).
- Mirrors Node dependencies and Appium driver caches into `runtime/node_env`.
- Sanitises platform databases and seeds the default environment records.
- Obfuscates Python modules using python-minifier, ensuring detachable `config`
  modules are preserved.
- Generates the PyInstaller executable and wraps it inside `dist/` bundles with
  a manifest describing runtime assets.

The resulting files:

- `secure_distribution_app/dist/SecureMobileAppAutomation`
- `secure_distribution_app/dist/SecureMobileAppAutomation_bundle`

Archive both and upload to your release repository.

---

## 4. Platform-Specific Packaging

After the base build completes, run the packaging helpers:

```bash
# Portable ZIP bundle (current platform)
SECURE_BUILD_VERSION=2.0.0 ./scripts/package_portable.sh
# -> outputs release/portable/SecureMobileAppAutomation_<os>_<version>.zip

# macOS dmg (requires packaging on macOS)
SECURE_BUILD_VERSION=2.0.0 SECURE_BUILD_NUMBER=20250921 ./scripts/package_macos.sh

# Windows and Linux native installers – run on their respective OS builds
./scripts/package_windows.ps1 dist/SecureMobileAppAutomation.exe
./scripts/package_linux.sh dist/SecureMobileAppAutomation
```

### Desktop Launcher (PySide6)

For development or lightweight redistribution, you can run the Qt-based launcher
directly on any machine with the repo:

```bash
python -m desktop_launcher.main
```

When using the portable ZIP bundle, the launch script automatically exposes the
same runtime. A dedicated Briefcase/py2app packaging step can be added later if
you want a native `.app` or `.msi` for the desktop launcher.

Each script signs the binaries, injects the license bootstrapper, and outputs an
installer artefact under `release/`.

> **Tip:** For CI pipelines, execute these scripts inside Docker images with the
> appropriate toolchain (Ubuntu for Linux packages, Windows Server runners for
> MSIX, macOS runners for notarisation).

---

## 5. Incremental Builds After Code Changes

1. Pull latest changes and merge your branch.
2. Run unit tests + linting.
3. Execute `build_final_secure_app.py`.
4. Regenerate installers with the packaging scripts.
5. Update the release metadata (hashes, version, release notes).
6. Push installers to the distribution portal.

Always increment the semantic version in `build_config['version']` before
publishing and tag the repository (`git tag vX.Y.Z`).

---

## 6. Verification Checklist

- ✅ `dist/` artefacts exist and launch correctly on the build machine.
- ✅ macOS binary notarised (`spctl --assess` passes).
- ✅ Windows executable signed (`signtool verify /pa`).
- ✅ Linux packages install cleanly in a fresh VM.
- ✅ `runtime/python_env` and `runtime/node_env` contain expected dependencies.
- ✅ `db-data/*.db` only include sanctioned tables & seeded environments.
- ✅ Release manifest updated with checksums.

Following these steps ensures every build ships with identical runtime assets,
protects against tampering, and supports device detection out of the box.
