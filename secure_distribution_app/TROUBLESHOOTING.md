# Troubleshooting Supabase Schema Issues

## 🚨 **Error: column "device_fingerprint" does not exist**

This error typically occurs due to one of these issues:

### **Root Cause Analysis**

1. **Execution Order Issue**: SQL statements are executing out of order
2. **Transaction Rollback**: Previous statements failed, causing table creation to rollback
3. **Partial Execution**: Only part of the schema script executed successfully
4. **Existing Objects**: Conflicting existing tables or constraints

### **🔧 Step-by-Step Debugging**

#### **Step 1: Check Current Database State**

Run this in Supabase SQL Editor to see what exists:

```sql
-- Check if table exists
SELECT table_name, table_schema 
FROM information_schema.tables 
WHERE table_name = 'user_profiles';

-- Check what columns exist (if table exists)
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check for any existing indexes
SELECT indexname, tablename, indexdef 
FROM pg_indexes 
WHERE tablename = 'user_profiles';
```

#### **Step 2: Clean Slate Approach**

If you see partial objects, clean them up first:

```sql
-- Run this to completely clean up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS handle_user_profiles_updated_at ON public.user_profiles;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.handle_updated_at() CASCADE;
DROP TABLE IF EXISTS public.user_profiles CASCADE;
```

#### **Step 3: Use Minimal Schema First**

Instead of the full schema, try the minimal version first:

1. **Copy contents of `database/minimal_schema.sql`**
2. **Paste into Supabase SQL Editor**
3. **Run the script**
4. **Verify it completes without errors**

#### **Step 4: Use Debug Schema for Detailed Diagnosis**

If minimal schema works, try the debug version:

1. **Copy contents of `database/debug_schema.sql`**
2. **Run each section separately** (marked as SECTION 1, SECTION 2, etc.)
3. **Stop at the first section that gives an error**
4. **Report which section failed**

### **🔍 Common Solutions**

#### **Solution 1: Execute in Smaller Chunks**

Instead of running the entire script at once:

```sql
-- Chunk 1: Create table only
CREATE TABLE public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    device_fingerprint TEXT,
    license_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT unique_user_profile UNIQUE(user_id)
);
```

Verify this works, then continue with:

```sql
-- Chunk 2: Create indexes
CREATE INDEX idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX idx_user_profiles_device_fingerprint ON public.user_profiles(device_fingerprint);
CREATE INDEX idx_user_profiles_license_number ON public.user_profiles(license_number);
```

#### **Solution 2: Check Supabase Permissions**

Ensure you have the right permissions:

```sql
-- Check your current role
SELECT current_user, current_database();

-- Check permissions on public schema
SELECT grantee, privilege_type 
FROM information_schema.schema_privileges 
WHERE schema_name = 'public';
```

#### **Solution 3: Use Alternative Column Reference**

If the issue persists, try creating the index differently:

```sql
-- Instead of:
CREATE INDEX idx_user_profiles_device_fingerprint ON public.user_profiles(device_fingerprint);

-- Try:
CREATE INDEX idx_user_profiles_device_fingerprint ON public.user_profiles USING btree (device_fingerprint);
```

### **🧪 Testing the Fix**

After applying any solution, test with:

```sql
-- Test 1: Verify table structure
\d public.user_profiles

-- Test 2: Test column access
SELECT device_fingerprint FROM public.user_profiles LIMIT 1;

-- Test 3: Test insert
INSERT INTO public.user_profiles (user_id, device_fingerprint) 
VALUES (gen_random_uuid(), 'test-fingerprint');

-- Test 4: Clean up test data
DELETE FROM public.user_profiles WHERE device_fingerprint = 'test-fingerprint';
```

### **📋 Debugging Checklist**

- [ ] Ran cleanup script to remove partial objects
- [ ] Verified no existing `user_profiles` table
- [ ] Tried minimal schema first
- [ ] Executed schema in small chunks
- [ ] Checked Supabase error logs
- [ ] Verified permissions on public schema
- [ ] Confirmed auth.users table exists

### **🆘 If All Else Fails**

1. **Export Current Database State**:
   ```sql
   -- Get current schema
   SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
   ```

2. **Create New Supabase Project**: Sometimes starting fresh is faster

3. **Manual Table Creation**: Use Supabase Table Editor instead of SQL:
   - Go to Table Editor
   - Click "Create a new table"
   - Name: `user_profiles`
   - Add columns manually:
     - `id`: uuid, primary key, default: `gen_random_uuid()`
     - `user_id`: uuid, foreign key to `auth.users.id`
     - `device_fingerprint`: text
     - `license_number`: text
     - `created_at`: timestamptz, default: `now()`
     - `updated_at`: timestamptz, default: `now()`
     - `metadata`: jsonb, default: `{}`

### **📞 Getting Help**

If you're still stuck, provide this information:

1. **Exact error message** (copy/paste)
2. **Which SQL script you ran** (full schema, minimal, or debug)
3. **Output of the debugging queries** from Step 1
4. **Supabase project region** and **PostgreSQL version**

### **✅ Success Indicators**

You'll know it's working when:

- [ ] No errors when running the schema script
- [ ] `user_profiles` table appears in Table Editor
- [ ] All columns are visible and accessible
- [ ] Can insert test data without errors
- [ ] Flask app connects and creates user profiles automatically
