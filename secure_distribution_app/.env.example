# Mobile App Automation - Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION (Required)
# =============================================================================

# Supabase Project URL
# Get this from: Supabase Dashboard > Settings > API
# Format: https://your-project-id.supabase.co
SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Anonymous (Public) Key
# Get this from: Supabase Dashboard > Settings > API
# This key is safe to use in client applications
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_anon_key_here

# Supabase Service Role Key (Optional - for admin operations)
# Get this from: Supabase Dashboard > Settings > API
# WARNING: Keep this secret! Only use server-side
# SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_service_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Environment
# Values: development, production, testing
APP_ENV=development

# Enable/Disable Development Mode
# Values: true, false
# When true: Shows debug info, detailed logging, relaxed security
DEVELOPMENT_MODE=true

# Logging Level
# Values: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Application Secret Key (for session encryption)
# Generate a random string for production
SECRET_KEY=your-secret-key-change-this-in-production

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Enable Code Obfuscation (requires PyArmor license)
# Values: true, false
ENABLE_OBFUSCATION=false

# Enable Integrity Checking
# Values: true, false
ENABLE_INTEGRITY_CHECK=true

# Enable Hardware Fingerprinting for License Binding
# Values: true, false
ENABLE_HARDWARE_BINDING=true

# License Validation Interval (seconds)
# How often to check license validity in background
LICENSE_VALIDATION_INTERVAL=300

# Grace Period for Expired Licenses (days)
LICENSE_GRACE_PERIOD_DAYS=7

# Warning Period Before License Expiry (days)
LICENSE_WARNING_PERIOD_DAYS=30

# =============================================================================
# GOOGLE DRIVE CONFIGURATION (Legacy - Not Used in Streamlined Version)
# =============================================================================

# NOTE: The streamlined version uses encrypted hardcoded links instead of API
# These settings are kept for reference but are not used

# Google Drive API Credentials File Path
# GOOGLE_CREDENTIALS_FILE=path/to/oauth_credentials.json

# Google Drive Service Account File Path
# GOOGLE_SERVICE_ACCOUNT_FILE=path/to/service_account.json

# Google Drive Folder ID for Package Storage
# GOOGLE_DRIVE_FOLDER_ID=your_folder_id_here

# =============================================================================
# PACKAGE CONFIGURATION
# =============================================================================

# App Package File IDs (for reference - actual links are encrypted in code)
# These are the Google Drive file IDs for your packages
IOS_APP_DRIVE_FILE_ID=your_ios_file_id_here
ANDROID_APP_DRIVE_FILE_ID=your_android_file_id_here
UNIFIED_APP_DRIVE_FILE_ID=your_unified_file_id_here

# Package Download Timeout (seconds)
DOWNLOAD_TIMEOUT=300

# Package Download Chunk Size (bytes)
DOWNLOAD_CHUNK_SIZE=8192

# Maximum Download Retries
DOWNLOAD_MAX_RETRIES=3

# =============================================================================
# SYSTEM TRAY CONFIGURATION
# =============================================================================

# Enable System Tray Functionality
# Values: true, false
ENABLE_SYSTEM_TRAY=true

# Auto-minimize to Tray After App Launch
# Values: true, false
AUTO_MINIMIZE_TO_TRAY=true

# System Tray Update Interval (seconds)
TRAY_UPDATE_INTERVAL=60

# =============================================================================
# DATABASE CONFIGURATION (Supabase Tables)
# =============================================================================

# User Profiles Table Name
USER_PROFILES_TABLE=user_profiles

# User Sessions Table Name
USER_SESSIONS_TABLE=user_sessions

# Audit Logs Table Name
AUDIT_LOGS_TABLE=audit_logs

# Download Logs Table Name
DOWNLOAD_LOGS_TABLE=download_logs

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Request Timeout (seconds)
REQUEST_TIMEOUT=30

# Connection Pool Size
CONNECTION_POOL_SIZE=10

# User Agent for HTTP Requests
USER_AGENT=MobileAppAutomation/2.0.0

# =============================================================================
# LOCAL STORAGE CONFIGURATION
# =============================================================================

# Local Storage Directory Name
# This will be created in user's AppData/home directory
LOCAL_STORAGE_DIR=.mobile_app_automation

# Cache Directory Name
CACHE_DIR=.secure_app_cache

# Enable File Encryption for Local Storage
# Values: true, false
ENCRYPT_LOCAL_FILES=true

# =============================================================================
# PLATFORM-SPECIFIC CONFIGURATION
# =============================================================================

# iOS App Default Port
IOS_APP_PORT=8090

# Android App Default Port
ANDROID_APP_PORT=8091

# Browser Auto-open Delay (seconds)
BROWSER_OPEN_DELAY=2

# App Launch Timeout (seconds)
APP_LAUNCH_TIMEOUT=30

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================

# Skip License Validation (development only)
# Values: true, false
# WARNING: Only use for development/testing
SKIP_LICENSE_VALIDATION=false

# Mock Supabase Responses (testing only)
# Values: true, false
MOCK_SUPABASE=false

# Enable Debug Logging for Network Requests
# Values: true, false
DEBUG_NETWORK=false

# Enable Debug Logging for License Operations
# Values: true, false
DEBUG_LICENSE=false

# =============================================================================
# EXAMPLE PRODUCTION CONFIGURATION
# =============================================================================

# For production deployment, use these settings:
# APP_ENV=production
# DEVELOPMENT_MODE=false
# LOG_LEVEL=WARNING
# ENABLE_OBFUSCATION=true
# ENABLE_INTEGRITY_CHECK=true
# ENABLE_HARDWARE_BINDING=true
# SKIP_LICENSE_VALIDATION=false
# MOCK_SUPABASE=false
# DEBUG_NETWORK=false
# DEBUG_LICENSE=false

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env in the same directory
# 2. Fill in your Supabase URL and keys
# 3. Set your application environment (development/production)
# 4. Configure security settings as needed
# 5. Test the application with your settings

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit .env files to version control
# - Keep SUPABASE_SERVICE_KEY secret and secure
# - Use strong SECRET_KEY in production
# - Regularly rotate API keys and secrets
# - Monitor logs for suspicious activity
