#!/usr/bin/env python3
"""
Configuration module for Mobile App Automation

This module handles configuration loading from environment variables
and provides fallback values for production builds.
"""

import os
import sys
from pathlib import Path

class Config:
    """Configuration class that handles environment loading and fallbacks"""
    
    def __init__(self):
        self.load_environment()
        self.validate_config()
    
    def load_environment(self):
        """Load environment variables from .env file"""
        try:
            # For PyInstaller, check multiple locations
            possible_locations = [
                Path(__file__).parent / '.env',  # Source directory
                Path(sys.executable).parent / '.env',  # Executable directory
                Path(os.getcwd()) / '.env',  # Current working directory
            ]
            
            # If running as PyInstaller bundle, check the bundle directory
            if getattr(sys, 'frozen', False):
                bundle_dir = Path(sys._MEIPASS)
                possible_locations.insert(0, bundle_dir / '.env')
            
            env_loaded = False
            for env_file in possible_locations:
                if env_file.exists():
                    with open(env_file, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#') and '=' in line:
                                key, value = line.split('=', 1)
                                os.environ[key.strip()] = value.strip()
                    env_loaded = True
                    break
            
            if not env_loaded:
                print("⚠️ No .env file found, using embedded configuration")
                
        except Exception as e:
            print(f"❌ Failed to load .env file: {e}")
    
    def validate_config(self):
        """Validate that required configuration is available"""
        required_vars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing environment variables: {missing_vars}")
            return False
        
        return True
    
    @property
    def supabase_url(self):
        """Get Supabase URL from environment or fallback"""
        return os.environ.get('SUPABASE_URL', self.get_fallback_supabase_url())
    
    @property
    def supabase_anon_key(self):
        """Get Supabase anonymous key from environment or fallback"""
        return os.environ.get('SUPABASE_ANON_KEY', self.get_fallback_supabase_key())
    
    @property
    def app_env(self):
        """Get application environment"""
        return os.environ.get('APP_ENV', 'production')
    
    @property
    def development_mode(self):
        """Check if development mode is enabled"""
        return os.environ.get('DEVELOPMENT_MODE', 'false').lower() == 'true'
    
    @property
    def log_level(self):
        """Get logging level"""
        return os.environ.get('LOG_LEVEL', 'INFO')
    
    def get_fallback_supabase_url(self):
        """
        Fallback Supabase URL for production builds
        
        IMPORTANT: Replace this with your actual Supabase URL before building
        """
        # TODO: Replace with your actual Supabase URL
        return "https://onuqryetexwoqscozfnb.supabase.co"
    
    def get_fallback_supabase_key(self):
        """
        Fallback Supabase anonymous key for production builds
        
        IMPORTANT: Replace this with your actual Supabase anonymous key before building
        """
        # TODO: Replace with your actual Supabase anonymous key
        return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9udXFyeWV0ZXh3b3FzY296Zm5iIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyODk1NDgsImV4cCI6MjA3MDg2NTU0OH0.N0NaAxxHon2Cm_gXaYRkI31kq9gNwxiY8pN3TTdwLw0"

# Global configuration instance
config = Config()

def get_config():
    """Get the global configuration instance"""
    return config

def get_supabase_credentials():
    """Get Supabase credentials for easy access"""
    cfg = get_config()
    return {
        'url': cfg.supabase_url,
        'key': cfg.supabase_anon_key
    }

# For backward compatibility
SUPABASE_URL = config.supabase_url
SUPABASE_ANON_KEY = config.supabase_anon_key
