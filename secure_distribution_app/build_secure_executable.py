#!/usr/bin/env python3
"""
Secure Executable Builder

This script creates secure, obfuscated executables for cross-platform distribution.
It includes code obfuscation, integrity checking, and anti-reverse engineering measures.
"""

import os
import sys
import subprocess
import shutil
import logging
from pathlib import Path
import json
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecureExecutableBuilder:
    """Builds secure executables with obfuscation and protection"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / 'build'
        self.dist_dir = self.project_root / 'dist'
        self.temp_dir = self.project_root / 'temp_build'

        # Ensure directories exist
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)

    def check_dependencies(self):
        """Check if required dependencies are installed"""
        try:
            logger.info("Checking build dependencies...")

            # Check PyInstaller
            try:
                import PyInstaller
                logger.info(f"✅ PyInstaller found: {PyInstaller.__version__}")
            except ImportError:
                logger.error("❌ PyInstaller not found")
                return False

            # Check if pyinstaller command is available
            try:
                result = subprocess.run(['pyinstaller', '--version'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    logger.info(f"✅ PyInstaller command available: {result.stdout.strip()}")
                else:
                    logger.error("❌ PyInstaller command not working")
                    return False
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                logger.error(f"❌ PyInstaller command not found: {e}")
                return False

            # Check other optional dependencies
            try:
                import pyarmor
                # Try to get version, but don't fail if not available
                try:
                    version = getattr(pyarmor, '__version__', 'unknown')
                    logger.info(f"✅ PyArmor found: {version}")
                except:
                    logger.info("✅ PyArmor found (version unknown)")
            except ImportError:
                logger.warning("⚠️ PyArmor not found - code obfuscation will be skipped")

            return True

        except Exception as e:
            logger.error(f"Dependency check failed: {e}")
            return False

    def install_dependencies(self):
        """Install required dependencies"""
        try:
            logger.info("Installing build dependencies...")

            # Install from requirements.txt
            requirements_file = self.project_root / 'requirements.txt'
            if requirements_file.exists():
                logger.info("Installing from requirements.txt...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
                ], capture_output=True, text=True)

                if result.returncode == 0:
                    logger.info("✅ Dependencies installed successfully")
                    return True
                else:
                    logger.error(f"❌ Failed to install dependencies: {result.stderr}")
                    return False
            else:
                logger.error("❌ requirements.txt not found")
                return False

        except Exception as e:
            logger.error(f"Dependency installation failed: {e}")
            return False
    
    def prepare_build_environment(self):
        """Prepare the build environment"""
        try:
            logger.info("Preparing build environment...")
            
            # Clean previous builds
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
            
            # Create temporary build directory
            self.temp_dir.mkdir()
            
            # Copy source files to temp directory
            self._copy_source_files()
            
            # Generate integrity manifest
            self._generate_integrity_manifest()
            
            logger.info("Build environment prepared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to prepare build environment: {e}")
            return False
    
    def _copy_source_files(self):
        """Copy source files to temporary build directory"""
        try:
            # Files and directories to include
            include_items = [
                'main.py',
                'gui/',
                'auth/',
                'security/',
                'downloader/',
                'launcher/',
                'requirements.txt'
            ]
            
            for item in include_items:
                src_path = self.project_root / item
                dst_path = self.temp_dir / item
                
                if src_path.is_file():
                    # Copy file
                    dst_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src_path, dst_path)
                elif src_path.is_dir():
                    # Copy directory
                    shutil.copytree(src_path, dst_path)
            
            logger.info("Source files copied to temporary directory")
            
        except Exception as e:
            logger.error(f"Failed to copy source files: {e}")
            raise
    
    def _generate_integrity_manifest(self):
        """Generate integrity manifest for the build"""
        try:
            from security.integrity import IntegrityChecker
            
            # Change to temp directory for integrity checking
            original_cwd = os.getcwd()
            os.chdir(self.temp_dir)
            
            try:
                checker = IntegrityChecker()
                manifest = checker.generate_integrity_manifest()
                checker.save_integrity_manifest(manifest)
                
                logger.info("Integrity manifest generated")
                
            finally:
                os.chdir(original_cwd)
                
        except Exception as e:
            logger.error(f"Failed to generate integrity manifest: {e}")
            raise
    
    def obfuscate_code(self):
        """Obfuscate the Python code using PyArmor"""
        try:
            logger.info("Obfuscating code with PyArmor...")
            
            # Check if PyArmor is available
            try:
                subprocess.run(['pyarmor', '--version'], check=True, 
                             capture_output=True, text=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("PyArmor not available, skipping code obfuscation")
                return True
            
            # Create obfuscated directory
            obfuscated_dir = self.temp_dir / 'obfuscated'
            obfuscated_dir.mkdir(exist_ok=True)
            
            # Obfuscate main script
            cmd = [
                'pyarmor', 'gen',
                '--output', str(obfuscated_dir),
                '--recursive',
                '--enable-jit',
                '--mix-str',
                '--assert-call',
                '--assert-import',
                str(self.temp_dir / 'main.py')
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Replace original files with obfuscated ones
                shutil.rmtree(self.temp_dir / 'main.py')
                for item in obfuscated_dir.iterdir():
                    if item.is_file():
                        shutil.copy2(item, self.temp_dir / item.name)
                    elif item.is_dir():
                        dst = self.temp_dir / item.name
                        if dst.exists():
                            shutil.rmtree(dst)
                        shutil.copytree(item, dst)
                
                # Clean up obfuscated directory
                shutil.rmtree(obfuscated_dir)
                
                logger.info("Code obfuscation completed successfully")
                return True
            else:
                logger.error(f"PyArmor obfuscation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Code obfuscation failed: {e}")
            return False
    
    def create_pyinstaller_spec(self, platform='auto'):
        """Create PyInstaller spec file with security options"""
        try:
            # Build data files list dynamically
            data_files = [
                ('gui', 'gui'),
                ('auth', 'auth'),
                ('security', 'security'),
                ('downloader', 'downloader'),
                ('launcher', 'launcher'),
            ]

            # Add integrity file if it exists
            integrity_file = Path('.integrity')
            if integrity_file.exists():
                data_files.insert(0, ('.integrity', '.'))

            # Convert to string format for spec file
            datas_str = ',\n        '.join([f"('{src}', '{dst}')" for src, dst in data_files])

            spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path.cwd()))

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[str(Path.cwd())],
    binaries=[],
    datas=[
        {datas_str}
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'requests',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'psutil',
        'threading',
        'json',
        'base64',
        'hashlib',
        'tempfile',
        'zipfile',
        'webbrowser',
        'subprocess',
        'signal',
        'uuid',
        'platform',
        'pathlib',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MobileAppAutomation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if Path('assets/icon.ico').exists() else None,
)
'''
            
            spec_file = self.temp_dir / 'secure_app.spec'
            with open(spec_file, 'w') as f:
                f.write(spec_content)
            
            logger.info("PyInstaller spec file created")
            return str(spec_file)
            
        except Exception as e:
            logger.error(f"Failed to create PyInstaller spec: {e}")
            return None
    
    def build_executable(self, spec_file):
        """Build the executable using PyInstaller"""
        try:
            logger.info("Building executable with PyInstaller...")
            
            # Change to temp directory
            original_cwd = os.getcwd()
            os.chdir(self.temp_dir)
            
            try:
                cmd = [
                    'pyinstaller',
                    '--clean',
                    '--noconfirm',
                    '--log-level=INFO',
                    spec_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("Executable built successfully")
                    return True
                else:
                    logger.error(f"PyInstaller build failed: {result.stderr}")
                    return False
                    
            finally:
                os.chdir(original_cwd)
                
        except Exception as e:
            logger.error(f"Executable build failed: {e}")
            return False
    
    def package_final_executable(self):
        """Package the final executable with additional security"""
        try:
            logger.info("Packaging final executable...")
            
            # Find the built executable
            temp_dist_dir = self.temp_dir / 'dist'
            if not temp_dist_dir.exists():
                raise Exception("Built executable not found")
            
            # Copy to final distribution directory
            for item in temp_dist_dir.iterdir():
                dst = self.dist_dir / item.name
                if dst.exists():
                    if dst.is_dir():
                        shutil.rmtree(dst)
                    else:
                        dst.unlink()
                
                if item.is_dir():
                    shutil.copytree(item, dst)
                else:
                    shutil.copy2(item, dst)
            
            # Create version info file
            version_info = {
                'version': '1.0.0',
                'build_date': str(Path().cwd()),
                'platform': sys.platform,
                'python_version': sys.version
            }
            
            version_file = self.dist_dir / 'version.json'
            with open(version_file, 'w') as f:
                json.dump(version_info, f, indent=2)
            
            logger.info("Final executable packaged successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to package final executable: {e}")
            return False
    
    def cleanup_build_files(self):
        """Clean up temporary build files"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
            
            logger.info("Build files cleaned up")
            
        except Exception as e:
            logger.warning(f"Failed to clean up build files: {e}")
    
    def build(self, platform='auto', obfuscate=False):
        """Build the complete secure executable"""
        try:
            logger.info("Starting secure executable build process...")

            # Check dependencies first
            if not self.check_dependencies():
                logger.info("Attempting to install missing dependencies...")
                if not self.install_dependencies():
                    logger.error("Failed to install dependencies. Please install manually:")
                    logger.error("pip install -r requirements.txt")
                    return False

                # Check again after installation
                if not self.check_dependencies():
                    logger.error("Dependencies still missing after installation")
                    return False

            # Prepare build environment
            if not self.prepare_build_environment():
                return False
            
            # Obfuscate code if requested
            if obfuscate:
                if not self.obfuscate_code():
                    logger.warning("Code obfuscation failed, continuing without obfuscation")
            
            # Create PyInstaller spec
            spec_file = self.create_pyinstaller_spec(platform)
            if not spec_file:
                return False
            
            # Build executable
            if not self.build_executable(spec_file):
                return False
            
            # Package final executable
            if not self.package_final_executable():
                return False
            
            # Clean up
            self.cleanup_build_files()
            
            logger.info("Secure executable build completed successfully!")
            logger.info(f"Output directory: {self.dist_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"Build process failed: {e}")
            self.cleanup_build_files()
            return False

def main():
    """Main entry point for the build script"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Build secure executable for distribution')
    parser.add_argument('--platform', choices=['windows', 'macos', 'linux', 'auto'], 
                       default='auto', help='Target platform')
    parser.add_argument('--obfuscate', action='store_true',
                       help='Enable code obfuscation (requires PyArmor license)')

    args = parser.parse_args()

    builder = SecureExecutableBuilder()
    success = builder.build(
        platform=args.platform,
        obfuscate=args.obfuscate
    )
    
    if success:
        print("\\n✅ Build completed successfully!")
        print(f"📁 Output directory: {builder.dist_dir}")
        print("\\n📋 Next steps:")
        print("1. Test the executable on target platforms")
        print("2. Create installation packages if needed")
        print("3. Distribute to subscribers")
    else:
        print("\\n❌ Build failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
