#!/usr/bin/env python3
"""
Secure Mobile App Distribution - Web-Based Entry Point

This is the updated main entry point for the secure distribution system that uses
a modern web-based architecture with dynamic port management and integrated
authentication.

Features:
- Web-based authentication with premium identity providers
- Dynamic port allocation to avoid conflicts
- Integrated iOS and Android app launching
- Process management and cleanup
- Cross-platform compatibility
- AI-powered test healing integration
"""

import sys
import os
import logging
import subprocess
import signal
import time
import socket
from pathlib import Path
from datetime import datetime
import threading
import webbrowser

# Add the current directory and parent directory to Python path for imports
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(parent_dir))

# Load environment variables from .env file
def load_environment():
    """Load environment variables from .env file"""
    try:
        # For PyInstaller, check multiple locations
        possible_locations = [
            current_dir / '.env',  # Source directory
            Path(sys.executable).parent / '.env',  # Executable directory
            Path(os.getcwd()) / '.env',  # Current working directory
        ]

        # If running as PyInstaller bundle, check the bundle directory
        if getattr(sys, 'frozen', False):
            bundle_dir = Path(sys._MEIPASS)
            possible_locations.insert(0, bundle_dir / '.env')
            print(f"🔍 Running as PyInstaller bundle, checking: {bundle_dir}")

        env_loaded = False
        for env_file in possible_locations:
            if env_file.exists():
                print(f"📁 Loading environment from: {env_file}")
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
                print("✅ Environment variables loaded successfully")
                env_loaded = True
                break

        if not env_loaded:
            print("⚠️ No .env file found in any location")
            print("📍 Searched locations:")
            for loc in possible_locations:
                print(f"   - {loc}")

    except Exception as e:
        print(f"❌ Failed to load .env file: {e}")
        import traceback
        traceback.print_exc()

# Load environment variables before importing other modules
load_environment()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('secure_web_app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import port management utilities
try:
    from utils.port_manager import get_port_manager, ensure_platform_ports
    logger.info("✅ Port management utilities imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import port management utilities: {e}")
    # Create a fallback port manager
    class FallbackPortManager:
        def find_available_port(self, start=8080, end=8200):
            for port in range(start, end):
                if self.is_port_available(port):
                    return port
            return None
        
        def is_port_available(self, port):
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                try:
                    s.bind(('localhost', port))
                    return True
                except OSError:
                    return False
    
    def get_port_manager():
        return FallbackPortManager()

class SecureWebApp:
    """Main application class for the secure web-based distribution system"""
    
    def __init__(self):
        self.auth_server_process = None
        self.auth_server_thread = None
        self.ios_app_process = None
        self.android_app_process = None
        self.auth_port = None
        self.is_running = False
        self.port_manager = get_port_manager()

        # Process tracking
        self.processes = {}

        # Ensure we only open the browser once
        self.browser_opened = False

    def cleanup_old_processes(self):
        """Clean up any existing processes on common ports"""
        try:
            logger.info("🧹 Cleaning up old processes...")
            
            # Common ports to check
            ports_to_check = [8080, 8081, 8082, 8090, 4723, 4724]
            
            for port in ports_to_check:
                try:
                    # Kill processes using these ports
                    if os.name == 'nt':  # Windows
                        subprocess.run(['netstat', '-ano'], capture_output=True, check=False)
                        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], capture_output=True, check=False)
                    else:  # Unix-like
                        subprocess.run(['pkill', '-f', f':{port}'], capture_output=True, check=False)
                        subprocess.run(['pkill', '-f', 'run.py'], capture_output=True, check=False)
                        subprocess.run(['pkill', '-f', 'run_android.py'], capture_output=True, check=False)
                        subprocess.run(['pkill', '-f', 'appium'], capture_output=True, check=False)
                except Exception as e:
                    logger.debug(f"Process cleanup for port {port}: {e}")
            
            # Wait a moment for processes to terminate
            time.sleep(2)
            logger.info("✅ Process cleanup completed")
            
        except Exception as e:
            logger.warning(f"Process cleanup failed: {e}")
    
    def find_available_port(self, start_port=8080, end_port=8200):
        """Find an available port in the given range"""
        for port in range(start_port, end_port):
            if self.is_port_available(port):
                return port
        return None
    
    def is_port_available(self, port):
        """Check if a port is available"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return True
            except OSError:
                return False
    
    def start_auth_server(self):
        """Start the authentication web server"""
        try:
            logger.info("🚀 Starting authentication server...")

            # Find available port for auth server
            self.auth_port = self.find_available_port(8080, 8200)
            if not self.auth_port:
                raise RuntimeError("No available ports for authentication server")

            logger.info(f"📡 Using port {self.auth_port} for authentication server")
            print(f"🌐 Authentication server starting on port {self.auth_port}")  # Explicit output for testing

            # Prefer in-process server when running as a bundled executable
            if getattr(sys, 'frozen', False):
                logger.info("Running in frozen mode - starting Flask server in-process")
                try:
                    # Import the Flask app module and run it in a thread
                    from web_server.app import run_server
                    import threading
                    self.auth_server_thread = threading.Thread(
                        target=run_server, kwargs={'host': 'localhost', 'port': int(self.auth_port), 'debug': False}, daemon=True
                    )
                    # Set env for the server
                    os.environ['FLASK_HOST'] = 'localhost'
                    os.environ['FLASK_PORT'] = str(self.auth_port)
                    os.environ['FLASK_DEBUG'] = 'False'
                    os.environ['SECURE_BUILD'] = 'True'
                    self.auth_server_thread.start()
                except Exception as e:
                    logger.error(f"Failed to start in-process server: {e}")
                    return False
            else:
                # Start the web server as a subprocess for source runs
                web_server_script = current_dir / 'web_server' / 'app.py'
                if not web_server_script.exists():
                    raise FileNotFoundError(f"Web server script not found: {web_server_script}")

                # Use system Python with dependencies installed
                python_executable = sys.executable
                if not python_executable or 'python' not in python_executable.lower():
                    python_candidates = ['python3', 'python', '/usr/bin/python3', '/usr/local/bin/python3']
                    for candidate in python_candidates:
                        try:
                            result = subprocess.run([candidate, '--version'], capture_output=True, text=True)
                            if result.returncode == 0:
                                python_executable = candidate
                                break
                        except:
                            continue

                logger.info(f"Using Python executable: {python_executable}")

                # Set environment variables for the web server
                env = os.environ.copy()
                env['FLASK_HOST'] = 'localhost'
                env['FLASK_PORT'] = str(self.auth_port)
                env['FLASK_DEBUG'] = 'False'
                env['SECURE_BUILD'] = 'True'

                # Start the authentication server
                self.auth_server_process = subprocess.Popen(
                    [python_executable, str(web_server_script)],
                    cwd=str(current_dir),
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

            # Wait for server to start (port open + health ready)
            max_wait = 30
            for i in range(max_wait):
                port_is_open = not self.is_port_available(self.auth_port)
                health_ok = False
                if port_is_open:
                    try:
                        import urllib.request
                        with urllib.request.urlopen(f"http://localhost:{self.auth_port}/health", timeout=1) as resp:
                            health_ok = resp.status == 200
                    except Exception:
                        health_ok = False
                if port_is_open and health_ok:
                    logger.info(f"✅ Authentication server ready on port {self.auth_port}")
                    return True
                time.sleep(1)

            # If we reach here, readiness failed
            if self.auth_server_process and self.auth_server_process.poll() is None:
                logger.error("❌ Server process running but readiness not confirmed")
            else:
                if self.auth_server_process:
                    stdout, stderr = self.auth_server_process.communicate()
                    logger.error(f"❌ Authentication server failed to start: {stderr}")
            return False

        except Exception as e:
            logger.error(f"❌ Failed to start authentication server: {e}")
            return False
    
    def open_browser(self):
        """Open the web browser to the authentication page"""
        try:
            if self.auth_port:
                url = f"http://localhost:{self.auth_port}"
                logger.info(f"🌐 Opening browser to: {url}")
                
                # Wait a moment for server to be fully ready
                time.sleep(2)
                
                # Open browser (guarded)
                if os.getenv('AUTO_OPEN_BROWSER', '0') == '1' and not self.browser_opened:
                    webbrowser.open(url)
                    self.browser_opened = True
                return True
        except Exception as e:
            logger.error(f"❌ Failed to open browser: {e}")
        return False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """Graceful shutdown of all processes"""
        try:
            logger.info("🛑 Shutting down secure web application...")
            self.is_running = False
            
            # Terminate all processes
            processes_to_terminate = [
                ('Authentication Server', self.auth_server_process),
                ('iOS App', self.ios_app_process),
                ('Android App', self.android_app_process)
            ]
            
            for name, process in processes_to_terminate:
                if process and process.poll() is None:
                    logger.info(f"🛑 Terminating {name}...")
                    try:
                        process.terminate()
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"Force killing {name}...")
                        process.kill()
                    except Exception as e:
                        logger.error(f"Error terminating {name}: {e}")
            
            logger.info("✅ Shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        finally:
            sys.exit(0)
    
    def run(self):
        """Main application entry point"""
        try:
            logger.info("🚀 Starting Secure Mobile App Distribution (Web-Based)...")
            
            # Setup signal handlers
            self.setup_signal_handlers()
            
            # Clean up old processes
            self.cleanup_old_processes()
            
            # Start authentication server
            if not self.start_auth_server():
                logger.error("❌ Failed to start authentication server")
                return False
            
            # Open browser
            # Open browser only once, after readiness confirmed
            if self.auth_port:
                try:
                    import urllib.request
                    with urllib.request.urlopen(f"http://localhost:{self.auth_port}/health", timeout=2) as resp:
                        if resp.status == 200:
                            self.open_browser()
                        else:
                            logger.warning("Health check not OK, not opening browser")
                except Exception as e:
                    logger.warning(f"Health check failed before opening browser: {e}")
                    print(f"\n🌐 Please open your browser and navigate to: http://localhost:{self.auth_port}")
            
            # Keep the application running
            self.is_running = True
            logger.info("✅ Secure web application started successfully")
            
            print(f"\n" + "="*60)
            print(f"🎉 SECURE MOBILE APP AUTOMATION PLATFORM")
            print(f"="*60)
            print(f"🌐 Web Interface: http://localhost:{self.auth_port}")
            print(f"📱 Platform: Cross-platform (iOS & Android)")
            print(f"🔐 Authentication: Premium identity provider ready")
            print(f"🤖 AI Healing: Enabled")
            print(f"⚡ Dynamic Ports: Enabled")
            print(f"="*60)
            print(f"Press Ctrl+C to stop the application")
            print(f"="*60 + "\n")
            
            # Keep running until interrupted
            try:
                while self.is_running:
                    time.sleep(1)
                    
                    # Check if auth server is still running
                    if self.auth_server_process and self.auth_server_process.poll() is not None:
                        logger.error("❌ Authentication server stopped unexpectedly")
                        break
                        
            except KeyboardInterrupt:
                logger.info("Application interrupted by user")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Application error: {e}")
            return False
        finally:
            self.shutdown()

def main():
    """Application entry point"""
    try:
        # Hide console window on Windows if running as executable
        if os.name == 'nt' and getattr(sys, 'frozen', False):
            try:
                import ctypes
                ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
            except:
                pass

        # Create and run the application
        app = SecureWebApp()
        success = app.run()

        if not success:
            sys.exit(1)

    except Exception as e:
        logger.error(f"Critical application error: {e}")
        print(f"❌ Critical error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
