@echo off
REM Secure Mobile App Automation Platform - Windows Launcher
REM This script launches the secure web-based mobile app automation platform

setlocal enabledelayedexpansion

echo.
echo ================================================================
echo   SECURE MOBILE APP AUTOMATION PLATFORM - WINDOWS LAUNCHER
echo ================================================================
echo.

REM Get the directory where this script is located
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

REM Change to the secure distribution app directory
cd /d "%SCRIPT_DIR%"

echo [INFO] Starting from directory: %SCRIPT_DIR%
echo [INFO] Project root: %PROJECT_ROOT%

REM Check if we're running from a built executable or source
if exist "%SCRIPT_DIR%main_web_secure.exe" (
    echo [INFO] Running from built executable
    set "EXECUTABLE=%SCRIPT_DIR%main_web_secure.exe"
    goto :run_executable
)

REM Running from source - check for Python and virtual environment
echo [INFO] Running from source code

REM Check for virtual environment
set "VENV_PYTHON=%PROJECT_ROOT%\venv\Scripts\python.exe"
if exist "%VENV_PYTHON%" (
    echo [INFO] Using virtual environment Python: %VENV_PYTHON%
    set "PYTHON_EXECUTABLE=%VENV_PYTHON%"
) else (
    echo [WARNING] Virtual environment not found, using system Python
    set "PYTHON_EXECUTABLE=python"
)

REM Check if Python is available
"%PYTHON_EXECUTABLE%" --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found. Please install Python or check your PATH.
    echo [ERROR] Expected virtual environment at: %VENV_PYTHON%
    pause
    exit /b 1
)

REM Check if main script exists
if not exist "%SCRIPT_DIR%main_web_secure.py" (
    echo [ERROR] Main script not found: %SCRIPT_DIR%main_web_secure.py
    pause
    exit /b 1
)

echo [INFO] Python executable: %PYTHON_EXECUTABLE%

REM Clean up any existing processes
echo [INFO] Cleaning up existing processes...
taskkill /F /IM python.exe >nul 2>&1
taskkill /F /IM pythonw.exe >nul 2>&1

REM Kill processes on common ports
for %%p in (8080 8081 8082 8090 4723 4724) do (
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :%%p') do (
        taskkill /F /PID %%a >nul 2>&1
    )
)

echo [INFO] Process cleanup completed

REM Wait a moment for processes to terminate
timeout /t 2 /nobreak >nul

REM Check for .env file
if not exist "%SCRIPT_DIR%.env" (
    echo [WARNING] .env file not found at: %SCRIPT_DIR%.env
    echo [WARNING] Please ensure Supabase credentials are configured
)

REM Launch the application
echo [INFO] Launching Secure Mobile App Automation Platform...
echo.

:run_executable
if defined EXECUTABLE (
    echo [INFO] Starting executable: %EXECUTABLE%
    "%EXECUTABLE%"
) else (
    echo [INFO] Starting Python application: %PYTHON_EXECUTABLE% main_web_secure.py
    "%PYTHON_EXECUTABLE%" main_web_secure.py
)

REM Check exit code
if errorlevel 1 (
    echo.
    echo [ERROR] Application exited with error code: %errorlevel%
    echo [ERROR] Check the logs for more details
    echo.
    pause
    exit /b %errorlevel%
)

echo.
echo [INFO] Application exited normally
echo.
pause
