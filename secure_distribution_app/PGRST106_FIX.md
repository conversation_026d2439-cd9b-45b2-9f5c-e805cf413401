# Fix PGRST106 Error - Step by Step Guide

## 🚨 **Understanding PGRST106 Error**

The error `"The schema must be one of the following: api"` (PGRST106) means:
- PostgREST (Supabase's API layer) cannot find your table
- The table doesn't exist, isn't in the right schema, or lacks proper permissions
- Row Level Security (RLS) might be blocking API access

## 🔧 **Step-by-Step Fix**

### **Step 1: Run Database Verification**

First, let's diagnose exactly what's wrong:

```bash
cd secure_distribution_app
/usr/bin/python3 scripts/verify_database.py
```

This will tell you exactly what's missing or misconfigured.

### **Step 2: Create the Database Schema**

1. **Open Supabase Dashboard**:
   - Go to your Supabase project
   - Navigate to "SQL Editor" in the left sidebar

2. **Run the Bulletproof Schema**:
   - Copy the entire contents of `database/bulletproof_schema.sql`
   - Paste into a new query in the SQL Editor
   - Click "Run" to execute

3. **Verify Success**:
   - You should see status messages like "TABLE CREATED", "PERMISSIONS GRANTED", etc.
   - The final message should be "🎉 SCHEMA SETUP COMPLETE! 🎉"

### **Step 3: Verify Table Creation**

After running the schema script, verify it worked:

1. **Check in Table Editor**:
   - Go to "Table Editor" in Supabase dashboard
   - You should see `user_profiles` table listed

2. **Check Table Structure**:
   - Click on the `user_profiles` table
   - Verify these columns exist:
     - `id` (uuid, primary key)
     - `user_id` (uuid, foreign key)
     - `device_fingerprint` (text)
     - `license_number` (text)
     - `created_at` (timestamptz)
     - `updated_at` (timestamptz)
     - `metadata` (jsonb)

3. **Check RLS Settings**:
   - In the table view, click the "Settings" tab
   - Verify "Enable RLS" is checked
   - Check that policies exist under "Policies" tab

### **Step 4: Test Database Access**

Run the verification script again:

```bash
/usr/bin/python3 scripts/verify_database.py
```

You should now see:
- ✅ user_profiles table is accessible with anonymous key
- ✅ All tests passing

### **Step 5: Create Test User**

Now try creating the test user:

```bash
/usr/bin/python3 scripts/create_test_user.py
```

Expected output:
- ✅ Supabase client initialized
- ✅ user_profiles table exists and is accessible
- ✅ Test user created successfully!

## 🔍 **Common Issues and Solutions**

### **Issue 1: "Table still not accessible after schema creation"**

**Solution**: Check RLS policies
```sql
-- Run this in Supabase SQL Editor to see current policies
SELECT policyname, permissive, roles, cmd 
FROM pg_policies 
WHERE tablename = 'user_profiles';
```

If no policies exist, run:
```sql
-- Create a permissive policy for testing
CREATE POLICY "Enable all access for testing" ON public.user_profiles
    FOR ALL USING (true);
```

### **Issue 2: "Permission denied for table user_profiles"**

**Solution**: Grant explicit permissions
```sql
-- Run this in Supabase SQL Editor
GRANT ALL ON public.user_profiles TO authenticated, anon;
GRANT USAGE ON SCHEMA public TO authenticated, anon;
```

### **Issue 3: "Function gen_random_uuid() does not exist"**

**Solution**: Enable UUID extension
```sql
-- Run this in Supabase SQL Editor
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

### **Issue 4: "Foreign key constraint fails"**

**Solution**: Check auth.users table exists
```sql
-- Verify auth schema is accessible
SELECT count(*) FROM auth.users;
```

## 🧪 **Testing the Complete Flow**

After fixing the database:

### **1. Test User Creation**
```bash
/usr/bin/python3 scripts/create_test_user.py
```

### **2. Test Flask Authentication**
```bash
/usr/bin/python3 main_browser_auth.py
```

Then visit: http://localhost:8080/login
- Email: `<EMAIL>`
- Password: `test123`

### **3. Verify User Profile Creation**

In Supabase dashboard:
1. Go to "Table Editor" → `user_profiles`
2. You should see a record for the test user
3. Check that `device_fingerprint` and `metadata` are populated

## 🔧 **Alternative: Manual Table Creation**

If SQL scripts keep failing, create the table manually:

1. **Go to Table Editor** in Supabase
2. **Click "Create a new table"**
3. **Name**: `user_profiles`
4. **Add columns**:
   - `id`: uuid, primary key, default: `gen_random_uuid()`
   - `user_id`: uuid, foreign key to `auth.users(id)`
   - `device_fingerprint`: text
   - `license_number`: text
   - `created_at`: timestamptz, default: `now()`
   - `updated_at`: timestamptz, default: `now()`
   - `metadata`: jsonb, default: `{}`
5. **Enable RLS**: Check the "Enable RLS" option
6. **Add Policy**: Create a policy that allows all operations for testing

## 📞 **Getting Help**

If you're still stuck, run the verification script and share:

1. **Complete output** from `scripts/verify_database.py`
2. **Error messages** from Supabase SQL Editor
3. **Screenshots** of your Table Editor showing what tables exist
4. **Your Supabase project region** and PostgreSQL version

## ✅ **Success Checklist**

- [ ] Database verification script passes all tests
- [ ] `user_profiles` table visible in Supabase Table Editor
- [ ] Test user creation script completes successfully
- [ ] Flask app can authenticate users
- [ ] User profiles are created automatically
- [ ] Dashboard loads with real user data

Once all items are checked, your Supabase authentication is fully working! 🎉
