{"summary": {"total_tests": 18, "passed_tests": 17, "failed_tests": 1, "success_rate": 94.44444444444444, "timestamp": "2025-09-04T11:14:01.730553"}, "test_results": {"Executable exists": {"passed": true, "details": "secure_distribution_app/dist/SecureMobileAppAutomation", "duration": null, "timestamp": "2025-09-04T11:13:34.139128"}, "Executable permissions": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:34.139168"}, "Windows launcher exists": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:34.139212"}, "Unix launcher exists": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:34.139247"}, "Unix launcher executable": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:34.139279"}, ".env.example exists": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:34.139318"}, "Executable launches successfully": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:44.143699"}, "Auth server port detection": {"passed": false, "details": "No active ports found in range 8080-8200", "duration": null, "timestamp": "2025-09-04T11:13:49.178336"}, "Source launch successful": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:13:59.185505"}, "Auth server port detected": {"passed": true, "details": "Port 8080", "duration": null, "timestamp": "2025-09-04T11:13:59.186028"}, "Home page loads": {"passed": true, "details": "Title: Mobile App Auto-Test Platform", "duration": null, "timestamp": "2025-09-04T11:14:00.568313"}, "Login button present": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:14:00.606646"}, "Register button present": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:14:00.606663"}, "Login form elements present": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:14:01.202409"}, "Dashboard redirects to login": {"passed": true, "details": "", "duration": null, "timestamp": "2025-09-04T11:14:01.730256"}, "Platform selection test": {"passed": true, "details": "Skipped - no test credentials", "duration": null, "timestamp": "2025-09-04T11:14:01.730272"}, "Full auth flow": {"passed": true, "details": "Skipped - no test credentials", "duration": null, "timestamp": "2025-09-04T11:14:01.730284"}, "App launch integration": {"passed": true, "details": "Skipped - no test credentials", "duration": null, "timestamp": "2025-09-04T11:14:01.730294"}}}