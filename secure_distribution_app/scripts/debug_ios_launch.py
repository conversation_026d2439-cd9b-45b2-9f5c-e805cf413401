#!/usr/bin/env python3
"""
Debug iOS Launch Issues

This script systematically debugs the iOS app launch failures.
"""

import os
import sys
import subprocess
import time
import socket
from pathlib import Path

def check_ports():
    """Check if required ports are available"""
    print("🔍 STEP 1: Checking Port Availability")
    print("=" * 50)
    
    ports = {
        8081: "iOS Flask App",
        4723: "iOS Appium Server", 
        8100: "WebDriverAgent"
    }
    
    port_issues = []
    
    for port, description in ports.items():
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"❌ Port {port} ({description}) is OCCUPIED")
            port_issues.append(port)
        else:
            print(f"✅ Port {port} ({description}) is available")
    
    return len(port_issues) == 0, port_issues

def check_ios_script():
    """Check iOS script and its dependencies"""
    print("\n🔍 STEP 2: Checking iOS Script")
    print("=" * 50)
    
    ios_script = Path(__file__).parent.parent.parent / "run.py"
    
    if not ios_script.exists():
        print(f"❌ iOS script not found at: {ios_script}")
        return False
    
    print(f"✅ iOS script found at: {ios_script}")
    
    # Check if script is executable
    if not os.access(ios_script, os.X_OK):
        print("⚠️ iOS script is not executable, making it executable...")
        os.chmod(ios_script, 0o755)
    
    print("✅ iOS script is executable")
    return True

def test_ios_script_directly():
    """Test running the iOS script directly"""
    print("\n🔍 STEP 3: Testing iOS Script Directly")
    print("=" * 50)
    
    ios_script = Path(__file__).parent.parent.parent / "run.py"
    
    # Test with help flag first
    print("📋 Testing iOS script with --help flag...")
    
    try:
        result = subprocess.run(
            [sys.executable, str(ios_script), "--help"],
            cwd=str(ios_script.parent),
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ iOS script responds to --help")
            print("📋 Available options:")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        else:
            print(f"❌ iOS script --help failed with exit code: {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ iOS script --help timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing iOS script: {e}")
        return False
    
    return True

def check_dependencies():
    """Check required dependencies"""
    print("\n🔍 STEP 4: Checking Dependencies")
    print("=" * 50)
    
    required_modules = [
        'flask',
        'appium',
        'selenium', 
        'pathlib',
        'sqlite3'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} is available")
        except ImportError:
            print(f"❌ {module} is MISSING")
            missing_modules.append(module)
    
    return len(missing_modules) == 0, missing_modules

def check_database_paths():
    """Check database and directory paths"""
    print("\n🔍 STEP 5: Checking Database Paths")
    print("=" * 50)
    
    base_dir = Path(__file__).parent.parent.parent
    
    critical_paths = [
        base_dir / "app",
        base_dir / "app" / "data",
        base_dir / "data",
        base_dir / "app" / "utils",
    ]
    
    path_issues = []
    
    for path in critical_paths:
        if path.exists():
            print(f"✅ {path} exists")
        else:
            print(f"❌ {path} is MISSING")
            path_issues.append(path)
    
    return len(path_issues) == 0, path_issues

def create_missing_directories():
    """Create missing directories"""
    print("\n🔧 STEP 6: Creating Missing Directories")
    print("=" * 50)
    
    base_dir = Path(__file__).parent.parent.parent
    
    directories_to_create = [
        base_dir / "app" / "data",
        base_dir / "data",
        base_dir / "temp_ios",
        base_dir / "logs",
        base_dir / "recordings_ios",
        base_dir / "screenshots_ios",
        base_dir / "reports_ios"
    ]
    
    for directory in directories_to_create:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created/verified: {directory}")
        except Exception as e:
            print(f"❌ Failed to create {directory}: {e}")

def test_ios_launch_with_debug():
    """Test iOS launch with detailed debugging"""
    print("\n🔍 STEP 7: Testing iOS Launch with Debug")
    print("=" * 50)
    
    ios_script = Path(__file__).parent.parent.parent / "run.py"
    
    # Launch command exactly as Flask would
    launch_command = [
        sys.executable, 
        str(ios_script),
        '--flask-port', '8081',
        '--appium-port', '4723',
        '--wda-port', '8100'
    ]
    
    print(f"📋 Launch command: {' '.join(launch_command)}")
    print("🚀 Attempting to launch iOS app...")
    
    try:
        # Start the process
        process = subprocess.Popen(
            launch_command,
            cwd=str(ios_script.parent),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for startup
        time.sleep(3)

        # Check if process is still running
        if process.poll() is None:
            print("✅ iOS app process started successfully!")
            print(f"📋 Process ID: {process.pid}")

            # Wait longer for iOS app to fully initialize (up to 30 seconds)
            print("⏳ Waiting for iOS app to initialize (up to 30 seconds)...")
            for attempt in range(30):
                time.sleep(1)

                # Check if process is still running
                if process.poll() is not None:
                    print(f"❌ iOS app process terminated during startup (exit code: {process.returncode})")
                    stdout, stderr = process.communicate()
                    print(f"📋 STDOUT:\n{stdout}")
                    print(f"📋 STDERR:\n{stderr}")
                    return False

                # Try to connect to the port
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', 8081))
                sock.close()

                if result == 0:
                    print(f"✅ iOS app is responding on port 8081! (took {attempt + 1} seconds)")

                    # Terminate the test process
                    process.terminate()
                    process.wait(timeout=5)
                    print("✅ Test process terminated cleanly")
                    return True

                if attempt % 5 == 0 and attempt > 0:
                    print(f"⏳ Still waiting... ({attempt}/30 seconds)")

            print("⚠️ iOS app started but not responding on port 8081 after 30 seconds")
            print("📋 Capturing output for debugging...")

            # Get current output without terminating
            try:
                stdout, stderr = process.communicate(timeout=2)
                print(f"📋 STDOUT:\n{stdout}")
                print(f"📋 STDERR:\n{stderr}")
            except subprocess.TimeoutExpired:
                print("📋 Process still running, terminating...")
                process.terminate()
                try:
                    stdout, stderr = process.communicate(timeout=5)
                    print(f"📋 STDOUT:\n{stdout}")
                    print(f"📋 STDERR:\n{stderr}")
                except subprocess.TimeoutExpired:
                    process.kill()
                    print("📋 Process killed forcefully")

            return False
        else:
            # Process failed
            stdout, stderr = process.communicate()
            print(f"❌ iOS app process failed with exit code: {process.returncode}")
            print(f"📋 STDOUT:\n{stdout}")
            print(f"📋 STDERR:\n{stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error launching iOS app: {e}")
        return False

def kill_conflicting_processes():
    """Kill any processes that might be using required ports"""
    print("\n🔧 STEP 8: Killing Conflicting Processes")
    print("=" * 50)
    
    ports_to_clear = [8081, 4723, 8100]
    
    for port in ports_to_clear:
        try:
            # Find process using the port
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True
            )
            
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        print(f"🔧 Killing process {pid} using port {port}")
                        subprocess.run(['kill', '-9', pid])
            else:
                print(f"✅ No processes using port {port}")
                
        except Exception as e:
            print(f"⚠️ Error checking port {port}: {e}")

def main():
    """Main debugging function"""
    print("🔍 iOS LAUNCH DEBUGGING TOOL")
    print("=" * 60)
    
    # Step 1: Check ports
    ports_ok, port_issues = check_ports()
    
    # Step 2: Check iOS script
    script_ok = check_ios_script()
    
    # Step 3: Test script directly
    script_test_ok = test_ios_script_directly() if script_ok else False
    
    # Step 4: Check dependencies
    deps_ok, missing_deps = check_dependencies()
    
    # Step 5: Check database paths
    paths_ok, missing_paths = check_database_paths()
    
    # Step 6: Create missing directories
    create_missing_directories()
    
    # Step 7: Kill conflicting processes if needed
    if not ports_ok:
        kill_conflicting_processes()
        time.sleep(2)
        ports_ok, _ = check_ports()
    
    # Step 8: Test launch
    launch_ok = test_ios_launch_with_debug() if all([script_ok, deps_ok, ports_ok]) else False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DEBUGGING SUMMARY")
    print("=" * 60)
    print(f"{'✅' if ports_ok else '❌'} Ports Available: {'YES' if ports_ok else 'NO'}")
    print(f"{'✅' if script_ok else '❌'} iOS Script: {'FOUND' if script_ok else 'MISSING'}")
    print(f"{'✅' if script_test_ok else '❌'} Script Test: {'PASS' if script_test_ok else 'FAIL'}")
    print(f"{'✅' if deps_ok else '❌'} Dependencies: {'OK' if deps_ok else 'MISSING'}")
    print(f"{'✅' if paths_ok else '❌'} Database Paths: {'OK' if paths_ok else 'MISSING'}")
    print(f"{'✅' if launch_ok else '❌'} Launch Test: {'SUCCESS' if launch_ok else 'FAILED'}")
    
    if not deps_ok:
        print(f"\n🔧 MISSING DEPENDENCIES: {', '.join(missing_deps)}")
        print("💡 Install with: pip install " + " ".join(missing_deps))
    
    if not ports_ok:
        print(f"\n🔧 PORT CONFLICTS: {port_issues}")
        print("💡 Kill processes or use different ports")
    
    if launch_ok:
        print("\n🎉 iOS APP LAUNCH IS WORKING!")
        print("🌐 Try the Flask dashboard again")
    else:
        print("\n❌ iOS APP LAUNCH STILL FAILING")
        print("💡 Check the detailed output above for specific issues")
    
    return launch_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
