#!/usr/bin/env python3
"""
Debug Authentication Issues

This script comprehensively debugs the 401 authentication error
and verifies the complete authentication flow.
"""

import os
import sys
import json
import requests
from pathlib import Path
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def test_supabase_connection():
    """Test basic Supabase connection and credentials"""
    print("🔍 STEP 1: Testing Supabase Connection")
    print("=" * 50)
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        print(f"📋 Supabase URL: {supabase_url}")
        print(f"📋 Anon Key: {anon_key[:20]}...")
        print(f"📋 Service Key: {service_key[:20]}...")
        
        if not supabase_url or not anon_key:
            print("❌ Missing Supabase credentials")
            return False
        
        # Test anonymous client
        supabase_anon = create_client(supabase_url, anon_key)
        print("✅ Anonymous client created")
        
        # Test service role client
        if service_key:
            supabase_service = create_client(supabase_url, service_key)
            print("✅ Service role client created")
        else:
            print("⚠️ No service role key")
            supabase_service = None
        
        return True, supabase_anon, supabase_service
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False, None, None

def check_auth_user_exists():
    """Check if auth user exists and can authenticate"""
    print("\n🔍 STEP 2: Checking Auth User")
    print("=" * 50)
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, anon_key)
        
        test_email = "<EMAIL>"
        test_password = "test123"
        
        print(f"📧 Testing credentials: {test_email} / {test_password}")
        
        # Try to sign in
        try:
            response = supabase.auth.sign_in_with_password({
                "email": test_email,
                "password": test_password
            })
            
            if response.user and response.session:
                print("✅ Auth user exists and can sign in!")
                print(f"   User ID: {response.user.id}")
                print(f"   Email: {response.user.email}")
                print(f"   Email Confirmed: {response.user.email_confirmed_at is not None}")
                print(f"   Access Token: {response.session.access_token[:30]}...")
                print(f"   Refresh Token: {response.session.refresh_token[:30]}...")
                
                return True, response.user, response.session
            else:
                print("❌ Sign-in returned no user/session")
                return False, None, None
                
        except Exception as signin_error:
            print(f"❌ Sign-in failed: {signin_error}")
            
            # Check if it's a credentials issue or user doesn't exist
            error_str = str(signin_error).lower()
            if 'invalid' in error_str or 'credentials' in error_str:
                print("💡 User doesn't exist or wrong credentials")
                print("🔧 Create user in Supabase Dashboard:")
                print("   1. Go to Authentication → Users")
                print("   2. Add User: <EMAIL> / test123")
                print("   3. Check 'Email Confirm'")
            else:
                print(f"💡 Other auth error: {signin_error}")
            
            return False, None, None
        
    except Exception as e:
        print(f"❌ Auth check error: {e}")
        return False, None, None

def check_user_profile(user_id):
    """Check if user profile exists and is properly linked"""
    print("\n🔍 STEP 3: Checking User Profile")
    print("=" * 50)
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not service_key:
            print("⚠️ No service key - using anon key")
            service_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, service_key)
        
        print(f"🔍 Looking for profile with user_id: {user_id}")
        
        # Check if profile exists
        result = supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
        
        if result.data:
            print("✅ User profile found!")
            profile = result.data[0]
            print(f"   Profile ID: {profile['id']}")
            print(f"   User ID: {profile['user_id']}")
            print(f"   Device Fingerprint: {profile.get('device_fingerprint', 'N/A')}")
            print(f"   License Number: {profile.get('license_number', 'N/A')}")
            print(f"   Metadata: {json.dumps(profile.get('metadata', {}), indent=2)}")
            
            return True, profile
        else:
            print("❌ No user profile found")
            print("🔧 Profile needs to be created")
            return False, None
        
    except Exception as e:
        print(f"❌ Profile check error: {e}")
        return False, None

def test_flask_login_api():
    """Test the Flask login API directly"""
    print("\n🔍 STEP 4: Testing Flask Login API")
    print("=" * 50)
    
    try:
        # Test if Flask server is running
        try:
            response = requests.get('http://localhost:8080', timeout=5)
            print("✅ Flask server is running")
        except requests.exceptions.ConnectionError:
            print("❌ Flask server is not running")
            print("🔧 Start the server: python secure_distribution_app/main_browser_auth.py")
            return False
        except Exception as e:
            print(f"⚠️ Server check error: {e}")
        
        # Test login API
        login_data = {
            "email": "<EMAIL>",
            "password": "test123"
        }
        
        print("🔧 Testing login API...")
        print(f"📧 Sending: {login_data}")
        
        try:
            response = requests.post(
                'http://localhost:8080/api/auth/login',
                json=login_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"📋 Response Status: {response.status_code}")
            print(f"📋 Response Headers: {dict(response.headers)}")
            
            try:
                response_data = response.json()
                print(f"📋 Response Data: {json.dumps(response_data, indent=2)}")
            except:
                print(f"📋 Response Text: {response.text}")
            
            if response.status_code == 200:
                print("✅ Login API successful!")
                return True
            elif response.status_code == 401:
                print("❌ 401 Unauthorized - This is the issue!")
                return False
            else:
                print(f"❌ Unexpected status code: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ Login API timeout")
            return False
        except Exception as api_error:
            print(f"❌ Login API error: {api_error}")
            return False
        
    except Exception as e:
        print(f"❌ Flask API test error: {e}")
        return False

def create_missing_profile(user):
    """Create user profile if missing"""
    print("\n🔧 STEP 5: Creating Missing Profile")
    print("=" * 50)
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(supabase_url, service_key)
        
        # Generate device fingerprint
        device_fingerprint = f"debug-{user.id[:8]}-{datetime.now().strftime('%Y%m%d')}"
        
        profile_data = {
            'user_id': user.id,
            'device_fingerprint': device_fingerprint,
            'license_number': 'TEST-LICENSE-001',
            'metadata': {
                'email': user.email,
                'login_count': 0,
                'test_user': True,
                'created_via_debug': True,
                'created_at': datetime.now().isoformat()
            }
        }
        
        print("🔧 Creating profile...")
        print(f"📋 Profile data: {json.dumps(profile_data, indent=2, default=str)}")
        
        result = supabase.table('user_profiles').insert(profile_data).execute()
        
        if result.data:
            print("✅ Profile created successfully!")
            print(f"   Profile ID: {result.data[0]['id']}")
            return True
        else:
            print("❌ Profile creation failed - no data returned")
            return False
        
    except Exception as e:
        error_str = str(e)
        if 'duplicate' in error_str.lower() or 'unique' in error_str.lower():
            print("✅ Profile already exists (duplicate error is OK)")
            return True
        else:
            print(f"❌ Profile creation error: {e}")
            return False

def main():
    """Main debugging function"""
    print("🔍 AUTHENTICATION DEBUG TOOL")
    print("=" * 60)
    print(f"🕐 Started at: {datetime.now()}")
    print()
    
    # Load environment
    if not load_environment():
        print("❌ Environment loading failed")
        return False
    
    # Step 1: Test Supabase connection
    conn_success, supabase_anon, supabase_service = test_supabase_connection()
    if not conn_success:
        return False
    
    # Step 2: Check auth user
    auth_success, user, session = check_auth_user_exists()
    if not auth_success:
        print("\n💡 SOLUTION: Create auth user first")
        return False
    
    # Step 3: Check user profile
    profile_success, profile = check_user_profile(user.id)
    if not profile_success:
        print("\n🔧 Creating missing profile...")
        profile_created = create_missing_profile(user)
        if not profile_created:
            return False
    
    # Step 4: Test Flask API
    flask_success = test_flask_login_api()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DEBUGGING SUMMARY")
    print("=" * 60)
    print(f"✅ Supabase Connection: {'PASS' if conn_success else 'FAIL'}")
    print(f"✅ Auth User Exists: {'PASS' if auth_success else 'FAIL'}")
    print(f"✅ User Profile: {'PASS' if profile_success else 'CREATED'}")
    print(f"✅ Flask Login API: {'PASS' if flask_success else 'FAIL'}")
    
    if all([conn_success, auth_success, flask_success]):
        print("\n🎉 ALL TESTS PASSED!")
        print("🌐 Authentication should work at: http://localhost:8080/login")
        return True
    else:
        print("\n❌ ISSUES FOUND - Check the steps above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
