#!/usr/bin/env python3
"""
Quick verifier to ensure Android launch can run from this repo directly.
Run: ./venv/bin/python secure_distribution_app/scripts/verify_android_launch_env.py
"""
from pathlib import Path
import sys, os, subprocess

root = Path(__file__).resolve().parents[2]
print(f"Project root: {root}")
script = root / 'run_android.py'
venv_py = root / 'venv' / 'bin' / 'python3'
print(f"run_android.py exists: {script.exists()} at {script}")
print(f"venv python exists: {venv_py.exists()} at {venv_py}")

if not script.exists():
    sys.exit("run_android.py not found - cannot verify")

python = str(venv_py) if venv_py.exists() else sys.executable
print(f"Using python: {python}")

env = os.environ.copy()
# Ensure imports work
env['PYTHONPATH'] = f"{root}:{env.get('PYTHONPATH','')}" if env.get('PYTHONPATH') else str(root)

cmd = [python, str(script), '--use-static-ports', '--port', '8091', '--appium-port', '4724', '--wda-port', '8300']
print(' '.join(cmd))

try:
    proc = subprocess.Popen(cmd, cwd=str(root), env=env, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    # Wait briefly for start and then kill (we just verify it starts)
    out = proc.stdout.readline().strip()
    print(f"First line: {out}")
    proc.terminate()
except Exception as e:
    sys.exit(f"Verifier failed: {e}")
print("Verifier executed; inspect first line above for expected banner")

