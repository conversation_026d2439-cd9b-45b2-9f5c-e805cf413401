#!/usr/bin/env python3
"""
Update Profile Data

Add missing device fingerprint and license number to the profile.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def update_profile():
    """Update the profile with missing data"""
    try:
        from supabase import create_client
        
        # Load environment
        if not load_environment():
            return False
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(supabase_url, service_key)
        
        print("🔧 Updating profile data...")
        
        # Get the test user
        anon_supabase = create_client(supabase_url, os.getenv('SUPABASE_ANON_KEY'))
        response = anon_supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if not response.user:
            print("❌ Cannot get user")
            return False
        
        user_id = response.user.id
        
        # Generate device fingerprint
        try:
            from auth.license_manager import LicenseManager
            license_manager = LicenseManager(supabase)
            device_fingerprint = license_manager.generate_hardware_fingerprint()
        except Exception:
            device_fingerprint = f"updated-{user_id[:8]}"
        
        # Update the profile
        update_data = {
            'device_fingerprint': device_fingerprint,
            'license_number': 'TEST-LICENSE-001',
            'updated_at': 'now()',
            'metadata': {
                'email': '<EMAIL>',
                'login_count': 0,
                'test_user': True,
                'updated_via_script': True,
                'device_fingerprint_added': True
            }
        }
        
        result = supabase.table('user_profiles').update(update_data).eq('user_id', user_id).execute()
        
        if result.data:
            print("✅ Profile updated successfully!")
            profile = result.data[0]
            print(f"   Device Fingerprint: {profile['device_fingerprint']}")
            print(f"   License Number: {profile['license_number']}")
            return True
        else:
            print("❌ Profile update failed")
            return False
        
    except Exception as e:
        print(f"❌ Update error: {e}")
        return False

if __name__ == "__main__":
    success = update_profile()
    if success:
        print("\n🎉 Profile updated! Ready for Flask testing.")
    sys.exit(0 if success else 1)
