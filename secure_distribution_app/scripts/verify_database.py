#!/usr/bin/env python3
"""
Verify Supabase Database Setup

This script verifies that the Supabase database is properly configured
and can diagnose common PGRST106 and schema issues.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment variables loaded")
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def verify_database():
    """Comprehensive database verification"""
    try:
        from supabase import create_client
        
        # Load environment
        if not load_environment():
            print("❌ Failed to load environment variables")
            return False
        
        # Get Supabase credentials
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_anon_key = os.getenv('SUPABASE_ANON_KEY')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not supabase_url or not supabase_anon_key:
            print("❌ Missing Supabase credentials in .env file")
            return False
        
        print(f"🔗 Connecting to: {supabase_url}")
        
        # Test 1: Basic connection with anon key
        print("\n📋 Test 1: Basic Connection (Anonymous Key)")
        try:
            supabase = create_client(supabase_url, supabase_anon_key)
            print("✅ Supabase client created successfully")
        except Exception as e:
            print(f"❌ Failed to create Supabase client: {e}")
            return False
        
        # Test 2: Auth API access
        print("\n📋 Test 2: Auth API Access")
        try:
            session = supabase.auth.get_session()
            print("✅ Auth API is accessible")
        except Exception as e:
            print(f"⚠️ Auth API test: {e}")
        
        # Test 3: Database API access (user_profiles table)
        print("\n📋 Test 3: user_profiles Table Access (Anonymous)")
        try:
            result = supabase.table('user_profiles').select('*').limit(1).execute()
            print("✅ user_profiles table is accessible with anonymous key")
            print(f"   Current record count: {len(result.data)}")
        except Exception as e:
            print(f"❌ user_profiles table access failed: {e}")
            
            # Analyze the error
            error_str = str(e)
            if 'PGRST106' in error_str:
                print("\n🔍 PGRST106 Error Analysis:")
                print("   - This means PostgREST cannot find the table in an accessible schema")
                print("   - The table might not exist or RLS is blocking access")
            elif 'permission denied' in error_str.lower():
                print("\n🔍 Permission Error Analysis:")
                print("   - Table exists but RLS policies are blocking access")
            elif 'relation' in error_str.lower() and 'does not exist' in error_str.lower():
                print("\n🔍 Table Missing Analysis:")
                print("   - The user_profiles table doesn't exist in the database")
        
        # Test 4: Service role access (if available)
        if service_key:
            print("\n📋 Test 4: Service Role Access")
            try:
                service_supabase = create_client(supabase_url, service_key)
                result = service_supabase.table('user_profiles').select('*').limit(1).execute()
                print("✅ user_profiles table is accessible with service role")
                print(f"   Current record count: {len(result.data)}")
                
                # If service role works but anon doesn't, it's a permissions issue
                print("\n💡 Diagnosis: Table exists but anonymous access is blocked by RLS")
                
            except Exception as e:
                print(f"❌ Service role access failed: {e}")
                print("💡 This suggests the table doesn't exist at all")
        else:
            print("\n📋 Test 4: Service Role Access")
            print("⚠️ No SUPABASE_SERVICE_ROLE_KEY found in environment")
            print("💡 Add your service role key to .env for more detailed diagnostics")
        
        # Test 5: Check table structure (if accessible)
        print("\n📋 Test 5: Table Structure Verification")
        try:
            # Try to get table info using a different approach
            result = supabase.rpc('get_table_info', {'table_name': 'user_profiles'}).execute()
            print("✅ Table structure information retrieved")
        except Exception as e:
            print(f"⚠️ Could not retrieve table structure: {e}")
        
        # Test 6: Test user creation flow
        print("\n📋 Test 6: User Creation Flow Test")
        try:
            # Try to create a test user (this will fail if user exists, which is OK)
            test_response = supabase.auth.sign_up({
                "email": "<EMAIL>",
                "password": "test123456"
            })
            
            if test_response.user:
                print("✅ User creation API works")
                # Clean up test user
                try:
                    supabase.auth.admin.delete_user(test_response.user.id)
                    print("✅ Test user cleaned up")
                except:
                    print("⚠️ Could not clean up test user (this is OK)")
            else:
                print("⚠️ User creation returned no user object")
                
        except Exception as e:
            error_str = str(e)
            if 'already registered' in error_str.lower():
                print("✅ User creation API works (test user already exists)")
            else:
                print(f"⚠️ User creation test: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure supabase-py is installed: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 Supabase Database Verification Tool")
    print("=" * 50)
    
    success = verify_database()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Database verification completed!")
        print("\n📋 Next Steps:")
        print("1. If user_profiles table access failed, run database/bulletproof_schema.sql")
        print("2. If everything passed, try running scripts/create_test_user.py")
        print("3. If issues persist, check Supabase dashboard for RLS policies")
    else:
        print("❌ Database verification failed!")
        print("\n🛠️ Troubleshooting:")
        print("1. Check your .env file has correct Supabase credentials")
        print("2. Verify your Supabase project is active")
        print("3. Run the database schema script in Supabase SQL Editor")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
