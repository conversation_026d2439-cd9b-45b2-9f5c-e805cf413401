#!/usr/bin/env python3
"""
Enhanced Test User Creation Script

This script creates a test user using the service role key to bypass
potential RLS and trigger issues.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime, timezone

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment variables loaded")
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def create_test_user_with_service_role():
    """Create test user using service role to bypass restrictions"""
    try:
        from supabase import create_client
        
        # Load environment
        if not load_environment():
            print("❌ Failed to load environment variables")
            return False
        
        # Get Supabase credentials
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not supabase_url or not service_key:
            print("❌ Missing Supabase credentials in .env file")
            print("💡 Make sure your .env file contains:")
            print("   SUPABASE_URL=https://your-project.supabase.co")
            print("   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key")
            return False
        
        print(f"🔗 Connecting with service role to: {supabase_url[:30]}...")
        
        # Initialize Supabase client with service role
        supabase = create_client(supabase_url, service_key)
        print("✅ Supabase service client initialized")
        
        # Test user credentials
        test_email = "<EMAIL>"
        test_password = "test123"
        
        print(f"🔧 Creating test user: {test_email}")
        
        # Method 1: Try using admin API to create user directly
        try:
            print("🔧 Attempting user creation with admin API...")
            
            # Create user with admin API (bypasses normal signup flow)
            admin_response = supabase.auth.admin.create_user({
                "email": test_email,
                "password": test_password,
                "email_confirm": True,  # Auto-confirm email
                "user_metadata": {
                    "first_name": "Test",
                    "last_name": "User"
                }
            })
            
            if admin_response.user:
                user = admin_response.user
                print(f"✅ Test user created successfully with admin API!")
                print(f"   User ID: {user.id}")
                print(f"   Email: {user.email}")
                print(f"   Created: {user.created_at}")
                print(f"   Email Confirmed: {user.email_confirmed_at is not None}")
                
                # Create user profile manually
                print("🔧 Creating user profile...")
                
                # Generate device fingerprint
                device_fingerprint = None
                try:
                    from auth.license_manager import LicenseManager
                    license_manager = LicenseManager(supabase)
                    device_fingerprint = license_manager.generate_hardware_fingerprint()
                    print(f"✅ Device fingerprint generated: {device_fingerprint}")
                except Exception as e:
                    print(f"⚠️ Could not generate device fingerprint: {e}")
                    device_fingerprint = f"manual-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                # Create profile data
                profile_data = {
                    'user_id': user.id,
                    'device_fingerprint': device_fingerprint,
                    'license_number': 'TEST-LICENSE-001',
                    'metadata': {
                        'email': test_email,
                        'login_count': 0,
                        'test_user': True,
                        'created_via_admin_api': True,
                        'created_at': datetime.now(timezone.utc).isoformat()
                    }
                }
                
                # Insert profile using service role (bypasses RLS)
                try:
                    profile_result = supabase.table('user_profiles').insert(profile_data).execute()
                    print("✅ User profile created successfully!")
                    print(f"   Profile ID: {profile_result.data[0]['id']}")
                    
                    # Verify the profile was created
                    verify_result = supabase.table('user_profiles').select('*').eq('user_id', user.id).execute()
                    if verify_result.data:
                        print("✅ Profile verification successful!")
                        print(f"   Profile data: {json.dumps(verify_result.data[0], indent=2, default=str)}")
                    
                except Exception as profile_error:
                    print(f"⚠️ Profile creation failed: {profile_error}")
                    print("💡 User created but profile creation failed")
                
                return True
            else:
                print("❌ Admin API returned no user object")
                return False
                
        except Exception as admin_error:
            print(f"❌ Admin API user creation failed: {admin_error}")
            
            # Method 2: Try regular signup with service role
            print("\n🔧 Attempting regular signup with service role...")
            try:
                signup_response = supabase.auth.sign_up({
                    "email": test_email,
                    "password": test_password,
                    "options": {
                        "data": {
                            "first_name": "Test",
                            "last_name": "User"
                        }
                    }
                })
                
                if signup_response.user:
                    print("✅ User created with regular signup!")
                    print(f"   User ID: {signup_response.user.id}")
                    return True
                else:
                    print("❌ Regular signup failed")
                    
            except Exception as signup_error:
                print(f"❌ Regular signup failed: {signup_error}")
        
        return False
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure supabase-py is installed: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        import traceback
        print(f"📋 Full error details:\n{traceback.format_exc()}")
        return False

def test_user_login():
    """Test that the created user can log in"""
    try:
        from supabase import create_client
        
        # Get credentials
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        if not supabase_url or not anon_key:
            print("❌ Missing credentials for login test")
            return False
        
        # Create client with anonymous key (like the Flask app would)
        supabase = create_client(supabase_url, anon_key)
        
        print("\n🧪 Testing user login...")
        
        # Try to sign in
        login_response = supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if login_response.user:
            print("✅ User login test successful!")
            print(f"   User ID: {login_response.user.id}")
            print(f"   Email: {login_response.user.email}")
            print(f"   Access Token: {login_response.session.access_token[:20]}...")
            
            # Test profile access
            try:
                profile_result = supabase.table('user_profiles').select('*').eq('user_id', login_response.user.id).execute()
                if profile_result.data:
                    print("✅ User profile accessible after login!")
                else:
                    print("⚠️ User can login but profile not found")
            except Exception as profile_e:
                print(f"⚠️ Profile access test failed: {profile_e}")
            
            return True
        else:
            print("❌ User login test failed")
            return False
            
    except Exception as e:
        print(f"❌ Login test error: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Enhanced Supabase Test User Creator")
    print("=" * 40)
    
    # Create user
    user_created = create_test_user_with_service_role()
    
    if user_created:
        # Test login
        login_works = test_user_login()
        
        if login_works:
            print("\n🎉 Complete success!")
            print("📋 Test user setup completed successfully!")
            print("🌐 You can now test the login at: http://localhost:8080/login")
            print("📝 Credentials: <EMAIL> / test123")
        else:
            print("\n⚠️ User created but login test failed")
            print("💡 Check Supabase Auth settings and email confirmation")
    else:
        print("\n❌ Test user creation failed!")
        print("💡 Check the error messages above for troubleshooting")
    
    return user_created

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
