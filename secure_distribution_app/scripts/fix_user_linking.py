#!/usr/bin/env python3
"""
Fix User Linking Issues

This script specifically addresses the user profile linking problem
that commonly causes 401 authentication errors.
"""

import os
import sys
import json
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def get_auth_user():
    """Get the auth user by signing in"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, anon_key)
        
        print("🔍 Getting auth user...")
        
        response = supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if response.user:
            print(f"✅ Auth user found: {response.user.id}")
            return response.user
        else:
            print("❌ No auth user found")
            return None
            
    except Exception as e:
        print(f"❌ Auth user error: {e}")
        return None

def find_orphaned_profiles():
    """Find user profiles that aren't linked to real auth users"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(supabase_url, service_key)
        
        print("🔍 Looking for orphaned profiles...")
        
        # Get all profiles
        profiles_result = supabase.table('user_profiles').select('*').execute()
        
        if not profiles_result.data:
            print("❌ No profiles found")
            return []
        
        print(f"📋 Found {len(profiles_result.data)} profiles")
        
        orphaned_profiles = []
        
        for profile in profiles_result.data:
            print(f"\n📋 Profile {profile['id']}:")
            print(f"   User ID: {profile['user_id']}")
            print(f"   Email in metadata: {profile.get('metadata', {}).get('email', 'N/A')}")
            
            # Check if this user_id exists in auth.users
            # We can't query auth.users directly, so we'll check by email
            email = profile.get('metadata', {}).get('email')
            if email:
                try:
                    # Try to sign in with this email to see if auth user exists
                    test_supabase = create_client(supabase_url, os.getenv('SUPABASE_ANON_KEY'))
                    
                    # We can't test without password, so we'll mark profiles with test email
                    if email == "<EMAIL>":
                        print("   ✅ This is our test profile")
                        orphaned_profiles.append(profile)
                    else:
                        print("   ⚠️ Unknown email")
                        
                except Exception:
                    print("   ❌ Cannot verify auth user")
            else:
                print("   ❌ No email in metadata")
                orphaned_profiles.append(profile)
        
        return orphaned_profiles
        
    except Exception as e:
        print(f"❌ Profile search error: {e}")
        return []

def link_profile_to_auth_user(auth_user, profile):
    """Link a profile to the correct auth user"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(supabase_url, service_key)
        
        print(f"🔧 Linking profile {profile['id']} to auth user {auth_user.id}")
        
        # Update the profile with correct user_id
        update_data = {
            'user_id': auth_user.id,
            'updated_at': 'now()',
            'metadata': {
                **profile.get('metadata', {}),
                'linked_at': 'now()',
                'auth_user_linked': True,
                'email': auth_user.email
            }
        }
        
        result = supabase.table('user_profiles').update(update_data).eq('id', profile['id']).execute()
        
        if result.data:
            print("✅ Profile linked successfully!")
            return True
        else:
            print("❌ Profile linking failed")
            return False
            
    except Exception as e:
        print(f"❌ Linking error: {e}")
        return False

def verify_linking():
    """Verify that the profile is correctly linked"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, anon_key)
        
        print("🔍 Verifying linking...")
        
        # Sign in to get auth user
        response = supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if not response.user:
            print("❌ Cannot sign in")
            return False
        
        user = response.user
        
        # Try to get profile
        profile_result = supabase.table('user_profiles').select('*').eq('user_id', user.id).execute()
        
        if profile_result.data:
            profile = profile_result.data[0]
            print("✅ Profile found after linking!")
            print(f"   Profile ID: {profile['id']}")
            print(f"   User ID: {profile['user_id']}")
            print(f"   Auth User ID: {user.id}")
            print(f"   Match: {profile['user_id'] == user.id}")
            
            return profile['user_id'] == user.id
        else:
            print("❌ No profile found after linking")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def create_new_profile_if_needed(auth_user):
    """Create a new profile if none exists"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(supabase_url, service_key)
        
        print("🔧 Creating new profile...")
        
        # Generate device fingerprint
        try:
            from auth.license_manager import LicenseManager
            license_manager = LicenseManager(supabase)
            device_fingerprint = license_manager.generate_hardware_fingerprint()
        except Exception:
            device_fingerprint = f"fallback-{auth_user.id[:8]}"
        
        profile_data = {
            'user_id': auth_user.id,
            'device_fingerprint': device_fingerprint,
            'license_number': 'TEST-LICENSE-001',
            'metadata': {
                'email': auth_user.email,
                'login_count': 0,
                'test_user': True,
                'created_via_fix_script': True
            }
        }
        
        result = supabase.table('user_profiles').insert(profile_data).execute()
        
        if result.data:
            print("✅ New profile created!")
            return True
        else:
            print("❌ Profile creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Profile creation error: {e}")
        return False

def main():
    """Main function"""
    print("🔧 USER LINKING FIX TOOL")
    print("=" * 40)
    
    # Load environment
    if not load_environment():
        return False
    
    # Get auth user
    auth_user = get_auth_user()
    if not auth_user:
        print("\n❌ Cannot get auth user")
        print("💡 Create user in Supabase dashboard first:")
        print("   Email: <EMAIL>")
        print("   Password: test123")
        return False
    
    # Find orphaned profiles
    orphaned_profiles = find_orphaned_profiles()
    
    if orphaned_profiles:
        print(f"\n🔧 Found {len(orphaned_profiles)} profiles to fix")
        
        # Link the first profile with test email
        test_profile = None
        for profile in orphaned_profiles:
            email = profile.get('metadata', {}).get('email')
            if email == "<EMAIL>":
                test_profile = profile
                break
        
        if test_profile:
            print("\n🔧 Linking existing profile...")
            link_success = link_profile_to_auth_user(auth_user, test_profile)
            
            if link_success:
                # Verify the linking worked
                verify_success = verify_linking()
                
                if verify_success:
                    print("\n🎉 LINKING SUCCESSFUL!")
                    print("🌐 Try logging in at: http://localhost:8080/login")
                    return True
                else:
                    print("\n❌ Linking verification failed")
                    return False
            else:
                print("\n❌ Linking failed")
                return False
        else:
            print("\n🔧 No matching profile found, creating new one...")
            create_success = create_new_profile_if_needed(auth_user)
            
            if create_success:
                verify_success = verify_linking()
                if verify_success:
                    print("\n🎉 NEW PROFILE CREATED AND LINKED!")
                    return True
            
            return False
    else:
        print("\n🔧 No orphaned profiles found, creating new profile...")
        create_success = create_new_profile_if_needed(auth_user)
        
        if create_success:
            verify_success = verify_linking()
            if verify_success:
                print("\n🎉 PROFILE CREATED AND VERIFIED!")
                return True
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
