#!/usr/bin/env python3
"""
Manual User Creation Script

This script creates a user manually by inserting directly into auth.users
and user_profiles tables, bypassing all triggers and signup flows.
"""

import os
import sys
import uuid
import hashlib
from pathlib import Path
from datetime import datetime, timezone

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def create_user_manually():
    """Create user by direct database insertion"""
    try:
        from supabase import create_client
        
        # Load environment
        if not load_environment():
            print("❌ Failed to load environment variables")
            return False
        
        # Get Supabase credentials
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not supabase_url or not service_key:
            print("❌ Missing Supabase credentials")
            return False
        
        # Initialize Supabase client with service role
        supabase = create_client(supabase_url, service_key)
        print("✅ Connected to Supabase with service role")
        
        # Test user data
        test_email = "<EMAIL>"
        test_password = "test123"
        user_id = str(uuid.uuid4())
        
        print(f"🔧 Creating user manually: {test_email}")
        print(f"   User ID: {user_id}")
        
        # Check if user already exists
        try:
            existing_check = supabase.table('auth.users').select('*').eq('email', test_email).execute()
            if existing_check.data:
                print("ℹ️ User already exists in auth.users table")
                existing_user = existing_check.data[0]
                user_id = existing_user['id']
                print(f"   Existing User ID: {user_id}")
            else:
                print("❌ Cannot directly insert into auth.users table via API")
                print("💡 We'll create the profile and test with existing auth methods")
        except Exception as e:
            print(f"⚠️ Could not check auth.users: {e}")
        
        # Try to sign in first to see if user exists
        print("🔍 Checking if user already exists via sign-in...")
        try:
            anon_key = os.getenv('SUPABASE_ANON_KEY')
            anon_supabase = create_client(supabase_url, anon_key)
            
            signin_response = anon_supabase.auth.sign_in_with_password({
                "email": test_email,
                "password": test_password
            })
            
            if signin_response.user:
                print("✅ User already exists and can sign in!")
                user_id = signin_response.user.id
                print(f"   User ID: {user_id}")
                
                # Check if profile exists
                profile_check = supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
                
                if profile_check.data:
                    print("✅ User profile already exists!")
                    print("🎉 Everything is already set up!")
                    return True
                else:
                    print("🔧 User exists but no profile - creating profile...")
                    # Continue to create profile below
            else:
                print("❌ User doesn't exist or can't sign in")
                return False
                
        except Exception as signin_error:
            print(f"❌ Sign-in test failed: {signin_error}")
            print("💡 User might not exist - we need to create it through Supabase dashboard")
            return False
        
        # Create user profile
        print("🔧 Creating user profile...")
        
        # Generate device fingerprint
        device_fingerprint = None
        try:
            from auth.license_manager import LicenseManager
            license_manager = LicenseManager(supabase)
            device_fingerprint = license_manager.generate_hardware_fingerprint()
            print(f"✅ Device fingerprint generated: {device_fingerprint}")
        except Exception as e:
            print(f"⚠️ Could not generate device fingerprint: {e}")
            device_fingerprint = f"manual-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Create profile data
        profile_data = {
            'user_id': user_id,
            'device_fingerprint': device_fingerprint,
            'license_number': 'TEST-LICENSE-001',
            'metadata': {
                'email': test_email,
                'login_count': 0,
                'test_user': True,
                'created_manually': True,
                'created_at': datetime.now(timezone.utc).isoformat()
            }
        }
        
        try:
            # Insert profile using service role (bypasses RLS)
            profile_result = supabase.table('user_profiles').insert(profile_data).execute()
            print("✅ User profile created successfully!")
            print(f"   Profile ID: {profile_result.data[0]['id']}")
            
            return True
            
        except Exception as profile_error:
            error_str = str(profile_error)
            if 'duplicate key' in error_str.lower() or 'unique constraint' in error_str.lower():
                print("ℹ️ User profile already exists!")
                return True
            else:
                print(f"❌ Profile creation failed: {profile_error}")
                return False
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error creating user manually: {e}")
        return False

def test_complete_flow():
    """Test the complete authentication flow"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, anon_key)
        
        print("\n🧪 Testing complete authentication flow...")
        
        # Test 1: Sign in
        print("1. Testing sign-in...")
        login_response = supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if not login_response.user:
            print("❌ Sign-in failed")
            return False
        
        print("✅ Sign-in successful!")
        user_id = login_response.user.id
        access_token = login_response.session.access_token
        
        # Test 2: Profile access
        print("2. Testing profile access...")
        profile_result = supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
        
        if not profile_result.data:
            print("❌ No profile found")
            return False
        
        print("✅ Profile accessible!")
        profile = profile_result.data[0]
        
        # Test 3: Session validation
        print("3. Testing session validation...")
        try:
            user_check = supabase.auth.get_user(access_token)
            if user_check:
                print("✅ Session validation works!")
            else:
                print("⚠️ Session validation unclear")
        except Exception as session_e:
            print(f"⚠️ Session validation: {session_e}")
        
        print("\n🎉 Complete authentication flow working!")
        print(f"📋 User ID: {user_id}")
        print(f"📋 Profile ID: {profile['id']}")
        print(f"📋 Device Fingerprint: {profile['device_fingerprint']}")
        print(f"📋 License: {profile['license_number']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flow test error: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Manual User Creation Tool")
    print("=" * 30)
    
    # Create user and profile
    creation_success = create_user_manually()
    
    if creation_success:
        # Test complete flow
        flow_success = test_complete_flow()
        
        if flow_success:
            print("\n🎉 COMPLETE SUCCESS!")
            print("📋 Authentication system is fully working!")
            print("🌐 Test the web interface at: http://localhost:8080/login")
            print("📝 Credentials: <EMAIL> / test123")
            print("\n🚀 Next steps:")
            print("1. Start the Flask app: python secure_distribution_app/main_browser_auth.py")
            print("2. Open browser to: http://localhost:8080/login")
            print("3. Login with test credentials")
            print("4. Verify dashboard loads with user data")
        else:
            print("\n⚠️ User created but flow test failed")
    else:
        print("\n❌ User creation failed!")
        print("💡 You may need to create the user manually in Supabase dashboard:")
        print("   1. Go to Authentication → Users")
        print("   2. Click 'Add User'")
        print("   3. Email: <EMAIL>")
        print("   4. Password: test123")
        print("   5. Check 'Email Confirm'")
        print("   6. Click 'Create User'")
        print("   7. Run this script again to create the profile")
    
    return creation_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
