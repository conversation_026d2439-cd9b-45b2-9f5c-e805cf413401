#!/usr/bin/env python3
"""
Correct User Creation Script

This script uses proper Supabase Auth API methods without trying to access
protected auth.users table directly.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime, timezone

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def check_user_exists():
    """Check if user exists using proper Auth API methods"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        if not supabase_url or not anon_key:
            print("❌ Missing credentials")
            return False, None
        
        # Use anonymous client (like the web app would)
        supabase = create_client(supabase_url, anon_key)
        
        print("🔍 Checking if test user exists...")
        
        # Try to sign in - this is the correct way to check if user exists
        try:
            response = supabase.auth.sign_in_with_password({
                "email": "<EMAIL>",
                "password": "test123"
            })
            
            if response.user:
                print("✅ User exists and can sign in!")
                print(f"   User ID: {response.user.id}")
                print(f"   Email: {response.user.email}")
                print(f"   Email Confirmed: {response.user.email_confirmed_at is not None}")
                return True, response.user
            else:
                print("❌ Sign-in returned no user")
                return False, None
                
        except Exception as signin_error:
            error_str = str(signin_error)
            if 'invalid' in error_str.lower() or 'credentials' in error_str.lower():
                print("❌ User doesn't exist or wrong credentials")
                return False, None
            else:
                print(f"❌ Sign-in error: {signin_error}")
                return False, None
        
    except Exception as e:
        print(f"❌ Error checking user: {e}")
        return False, None

def create_user_with_admin_api():
    """Create user using Admin API with detailed error handling"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not supabase_url or not service_key:
            print("❌ Missing service role credentials")
            return False, None
        
        # Use service role client
        supabase = create_client(supabase_url, service_key)
        
        print("🔧 Creating user with Admin API...")
        
        try:
            response = supabase.auth.admin.create_user({
                "email": "<EMAIL>",
                "password": "test123",
                "email_confirm": True,  # Auto-confirm email
                "user_metadata": {
                    "first_name": "Test",
                    "last_name": "User",
                    "created_by": "script"
                }
            })
            
            if response.user:
                print("✅ User created successfully with Admin API!")
                print(f"   User ID: {response.user.id}")
                print(f"   Email: {response.user.email}")
                print(f"   Created: {response.user.created_at}")
                print(f"   Email Confirmed: {response.user.email_confirmed_at is not None}")
                return True, response.user
            else:
                print("❌ Admin API returned no user")
                return False, None
                
        except Exception as admin_error:
            error_str = str(admin_error)
            print(f"❌ Admin API error: {admin_error}")
            
            # Analyze specific error types
            if 'already exists' in error_str.lower() or 'already registered' in error_str.lower():
                print("💡 User already exists - this is actually good!")
                # Try to get the existing user info
                user_exists, user = check_user_exists()
                return user_exists, user
            elif 'email' in error_str.lower():
                print("💡 Email validation issue - check Supabase Auth settings")
            elif 'password' in error_str.lower():
                print("💡 Password policy issue - check Supabase Auth settings")
            elif 'database' in error_str.lower():
                print("💡 Database constraint issue - check triggers and constraints")
            
            return False, None
        
    except Exception as e:
        print(f"❌ Error with admin API: {e}")
        return False, None

def create_user_profile(user):
    """Create user profile for the given user"""
    try:
        from supabase import create_client

        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

        supabase = create_client(supabase_url, service_key)

        print("🔧 Creating user profile...")

        # Check if profile already exists
        existing_profile = supabase.table('user_profiles').select('*').eq('user_id', user.id).execute()

        if existing_profile.data:
            print("✅ User profile already exists!")
            print(f"   Profile: {json.dumps(existing_profile.data[0], indent=2, default=str)}")
            return True

        # Generate device fingerprint
        device_fingerprint = None
        try:
            from auth.license_manager import LicenseManager
            license_manager = LicenseManager(supabase)
            device_fingerprint = license_manager.generate_hardware_fingerprint()
            print(f"✅ Device fingerprint: {device_fingerprint}")
        except Exception as e:
            print(f"⚠️ Device fingerprint error: {e}")
            device_fingerprint = f"fallback-{user.id[:8]}"

        # First, let's check what columns actually exist in the table
        print("🔍 Checking table schema...")
        try:
            # Try a simple select to see what columns are available
            schema_check = supabase.table('user_profiles').select('*').limit(1).execute()
            print("✅ Table is accessible")
        except Exception as schema_error:
            print(f"❌ Table schema issue: {schema_error}")
            print("💡 You may need to run the schema fix script first")
            return False

        # Create profile with only the columns that should exist
        print("🔧 Attempting to create profile...")

        # Try with minimal required fields first
        try:
            profile_data = {
                'user_id': user.id,
                'metadata': {
                    'email': user.email,
                    'login_count': 0,
                    'test_user': True,
                    'created_via_script': True,
                    'created_at': datetime.now(timezone.utc).isoformat(),
                    'device_fingerprint': device_fingerprint,
                    'license_number': 'TEST-LICENSE-001'
                }
            }

            result = supabase.table('user_profiles').insert(profile_data).execute()

            if result.data:
                print("✅ User profile created successfully (minimal schema)!")
                print(f"   Profile ID: {result.data[0]['id']}")
                return True
            else:
                print("❌ Profile creation returned no data")
                return False

        except Exception as minimal_error:
            print(f"❌ Minimal profile creation failed: {minimal_error}")

            # If that fails, the table schema needs to be fixed
            print("\n💡 The user_profiles table is missing required columns.")
            print("🔧 Please run the schema fix script:")
            print("   1. Copy contents of: database/fix_user_profiles_schema.sql")
            print("   2. Paste in Supabase SQL Editor")
            print("   3. Click 'Run'")
            print("   4. Run this script again")
            return False

    except Exception as e:
        error_str = str(e)
        if 'duplicate' in error_str.lower() or 'unique' in error_str.lower():
            print("✅ Profile already exists (duplicate key error is OK)")
            return True
        elif 'device_fingerprint' in error_str and 'not found' in error_str:
            print("❌ device_fingerprint column missing from table")
            print("💡 Run the schema fix script to add missing columns")
            return False
        else:
            print(f"❌ Profile creation error: {e}")
            return False

def test_complete_authentication():
    """Test the complete authentication flow"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, anon_key)
        
        print("\n🧪 Testing complete authentication flow...")
        
        # Test sign-in
        response = supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if not response.user:
            print("❌ Authentication failed")
            return False
        
        print("✅ Authentication successful!")
        user = response.user
        session = response.session
        
        # Test profile access
        profile_result = supabase.table('user_profiles').select('*').eq('user_id', user.id).execute()
        
        if not profile_result.data:
            print("❌ No profile found")
            return False
        
        print("✅ Profile access successful!")
        profile = profile_result.data[0]
        
        # Display results
        print("\n📋 Authentication Test Results:")
        print(f"   User ID: {user.id}")
        print(f"   Email: {user.email}")
        print(f"   Access Token: {session.access_token[:20]}...")
        print(f"   Profile ID: {profile['id']}")
        print(f"   Device Fingerprint: {profile['device_fingerprint']}")
        print(f"   License: {profile['license_number']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test error: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Correct Supabase User Creation")
    print("=" * 35)
    
    # Load environment
    if not load_environment():
        print("❌ Environment loading failed")
        return False
    
    # Step 1: Check if user already exists
    user_exists, existing_user = check_user_exists()
    
    if user_exists:
        print("✅ User already exists!")
        user = existing_user
    else:
        # Step 2: Create user with Admin API
        print("🔧 User doesn't exist, creating...")
        user_created, new_user = create_user_with_admin_api()
        
        if not user_created:
            print("\n❌ User creation failed!")
            print("💡 Manual creation required:")
            print("   1. Go to Supabase Dashboard → Authentication → Users")
            print("   2. Click 'Add User'")
            print("   3. Email: <EMAIL>")
            print("   4. Password: test123")
            print("   5. Check 'Email Confirm'")
            print("   6. Click 'Create User'")
            print("   7. Run this script again")
            return False
        
        user = new_user
    
    # Step 3: Create/verify profile
    profile_success = create_user_profile(user)
    
    if not profile_success:
        print("❌ Profile creation failed")
        return False
    
    # Step 4: Test complete flow
    auth_success = test_complete_authentication()
    
    if auth_success:
        print("\n🎉 COMPLETE SUCCESS!")
        print("📋 Supabase authentication is fully working!")
        print("🌐 Test at: http://localhost:8080/login")
        print("📝 Credentials: <EMAIL> / test123")
        return True
    else:
        print("\n⚠️ Setup completed but authentication test failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
