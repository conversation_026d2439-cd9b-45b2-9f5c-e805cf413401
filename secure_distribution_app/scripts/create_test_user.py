#!/usr/bin/env python3
"""
Create Test User in Supabase

This script creates a test user in Supabase Auth for testing the authentication system.
Enhanced version with better error handling and verification.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime, timezone

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment variables loaded")
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def verify_database_setup(supabase):
    """Verify that the database schema is set up correctly"""
    try:
        print("🔍 Verifying database setup...")

        # Check if user_profiles table exists by trying to query it
        result = supabase.table('user_profiles').select('*').limit(1).execute()
        print("✅ user_profiles table exists and is accessible")
        return True

    except Exception as e:
        error_str = str(e)
        print(f"❌ Database verification failed: {e}")

        # Handle specific PGRST106 error
        if 'PGRST106' in error_str or 'schema must be one of' in error_str:
            print("\n🔍 PGRST106 Error Diagnosis:")
            print("   This error means the user_profiles table is not accessible via the Supabase API")
            print("   Possible causes:")
            print("   1. Table doesn't exist in the public schema")
            print("   2. Row Level Security (RLS) is blocking access")
            print("   3. API permissions are not set correctly")
            print("   4. Table was created in wrong schema")

            print("\n🛠️ To fix this:")
            print("   1. Run the bulletproof_schema.sql script in Supabase SQL Editor")
            print("   2. Make sure you're using the correct Supabase project")
            print("   3. Check that RLS policies allow access")

        elif 'permission denied' in error_str.lower():
            print("\n🔍 Permission Error Diagnosis:")
            print("   The table exists but you don't have permission to access it")
            print("   Check Row Level Security policies in Supabase dashboard")

        elif 'relation' in error_str.lower() and 'does not exist' in error_str.lower():
            print("\n🔍 Table Missing Diagnosis:")
            print("   The user_profiles table doesn't exist")
            print("   Run the database schema script first")

        print("\n💡 Please run the database/bulletproof_schema.sql script in Supabase SQL Editor first")
        return False

def test_supabase_connection(supabase):
    """Test basic Supabase connection and permissions"""
    try:
        print("🔗 Testing Supabase connection...")

        # Try to access auth.users (this should always work)
        try:
            # This is a safe query that should work if Supabase is connected
            auth_response = supabase.auth.get_session()
            print("✅ Supabase Auth API is accessible")
        except Exception as auth_e:
            print(f"⚠️ Auth API test: {auth_e}")

        # Try to query a system table to test basic API access
        try:
            # Query information_schema which should be accessible
            result = supabase.rpc('version').execute()
            print("✅ Supabase RPC functions are accessible")
        except Exception as rpc_e:
            print(f"⚠️ RPC test failed: {rpc_e}")

        return True

    except Exception as e:
        print(f"❌ Basic Supabase connection test failed: {e}")
        return False

def create_test_user():
    """Create test user in Supabase with enhanced error handling"""
    try:
        from supabase import create_client

        # Load environment
        if not load_environment():
            print("❌ Failed to load environment variables")
            return False

        # Get Supabase credentials
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_anon_key = os.getenv('SUPABASE_ANON_KEY')

        if not supabase_url or not supabase_anon_key:
            print("❌ Missing Supabase credentials in .env file")
            print("💡 Make sure your .env file contains:")
            print("   SUPABASE_URL=https://your-project.supabase.co")
            print("   SUPABASE_ANON_KEY=your-anon-key")
            return False

        print(f"🔗 Connecting to Supabase: {supabase_url[:30]}...")

        # Initialize Supabase client
        supabase = create_client(supabase_url, supabase_anon_key)
        print("✅ Supabase client initialized")

        # Test basic connection first
        if not test_supabase_connection(supabase):
            print("⚠️ Basic connection test failed, but continuing...")

        # Verify database setup
        if not verify_database_setup(supabase):
            print("\n🔧 Attempting to diagnose the issue further...")

            # Try alternative verification methods
            try:
                # Try using service role if available
                service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
                if service_key:
                    print("🔑 Trying with service role key...")
                    service_supabase = create_client(supabase_url, service_key)
                    service_result = service_supabase.table('user_profiles').select('*').limit(1).execute()
                    print("✅ Table accessible with service role - this is a permissions issue")
                    print("💡 The table exists but RLS policies are blocking anonymous access")
                else:
                    print("💡 No service role key found in environment")
            except Exception as service_e:
                print(f"🔑 Service role test failed: {service_e}")

            return False

        # Try to initialize license manager for device fingerprinting
        device_fingerprint = None
        try:
            from auth.license_manager import LicenseManager
            license_manager = LicenseManager(supabase)
            device_fingerprint = license_manager.generate_hardware_fingerprint()
            print(f"✅ Device fingerprint generated: {device_fingerprint}")
        except Exception as e:
            print(f"⚠️ Could not generate device fingerprint: {e}")
            print("💡 Continuing without device fingerprint...")
            device_fingerprint = f"manual-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Test user credentials
        test_email = "<EMAIL>"
        test_password = "test123"

        print(f"🔧 Creating test user: {test_email}")

        # Check if user already exists first
        try:
            existing_sign_in = supabase.auth.sign_in_with_password({
                "email": test_email,
                "password": test_password
            })

            if existing_sign_in.user:
                print(f"ℹ️ User {test_email} already exists and credentials work!")
                print(f"   User ID: {existing_sign_in.user.id}")

                # Check if profile exists
                profile_result = supabase.table('user_profiles').select('*').eq('user_id', existing_sign_in.user.id).execute()

                if profile_result.data:
                    print("✅ User profile already exists")
                    print(f"   Profile: {json.dumps(profile_result.data[0], indent=2, default=str)}")
                else:
                    print("⚠️ User exists but no profile found, creating profile...")
                    # Create profile for existing user
                    profile_data = {
                        'user_id': existing_sign_in.user.id,
                        'device_fingerprint': device_fingerprint,
                        'license_number': 'TEST-LICENSE-001',
                        'metadata': {
                            'email': test_email,
                            'login_count': 0,
                            'test_user': True,
                            'created_via_script': True
                        }
                    }

                    try:
                        profile_result = supabase.table('user_profiles').insert(profile_data).execute()
                        print("✅ User profile created successfully!")
                    except Exception as profile_error:
                        print(f"⚠️ Profile creation failed: {profile_error}")

                return True

        except Exception:
            # User doesn't exist or credentials don't work, try to create
            pass

        # Try to create new user
        try:
            print("🔧 Creating new user account...")
            auth_response = supabase.auth.sign_up({
                "email": test_email,
                "password": test_password,
                "options": {
                    "data": {
                        "first_name": "Test",
                        "last_name": "User"
                    }
                }
            })

            if auth_response.user:
                print(f"✅ Test user created successfully!")
                print(f"   User ID: {auth_response.user.id}")
                print(f"   Email: {auth_response.user.email}")
                print(f"   Created: {auth_response.user.created_at}")

                # Wait a moment for any database triggers to complete
                import time
                time.sleep(2)

                # Check if profile was created automatically by triggers
                profile_result = supabase.table('user_profiles').select('*').eq('user_id', auth_response.user.id).execute()

                if profile_result.data:
                    print("✅ User profile created automatically by database triggers!")
                    print(f"   Profile: {json.dumps(profile_result.data[0], indent=2, default=str)}")
                else:
                    print("🔧 Creating user profile manually...")
                    # Create user profile manually
                    profile_data = {
                        'user_id': auth_response.user.id,
                        'device_fingerprint': device_fingerprint,
                        'license_number': 'TEST-LICENSE-001',
                        'metadata': {
                            'email': test_email,
                            'login_count': 0,
                            'test_user': True,
                            'created_via_script': True
                        }
                    }

                    try:
                        profile_result = supabase.table('user_profiles').insert(profile_data).execute()
                        print("✅ User profile created successfully!")
                        print(f"   Profile: {json.dumps(profile_result.data[0], indent=2, default=str)}")
                    except Exception as profile_error:
                        print(f"⚠️ Profile creation failed: {profile_error}")
                        print("💡 User created but profile creation failed - this might be OK if triggers handle it")

                return True
            else:
                print("❌ User creation failed - no user returned")
                return False

        except Exception as auth_error:
            error_msg = str(auth_error)
            print(f"❌ User creation failed: {auth_error}")

            if 'already registered' in error_msg.lower() or 'already exists' in error_msg.lower():
                print("💡 User might already exist with different credentials")
            elif 'email' in error_msg.lower():
                print("💡 Check email format and Supabase email settings")
            elif 'password' in error_msg.lower():
                print("💡 Check password requirements in Supabase Auth settings")

            return False

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure supabase-py is installed: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        import traceback
        print(f"📋 Full error details:\n{traceback.format_exc()}")
        return False

def main():
    """Main function"""
    print("🧪 Supabase Test User Creator")
    print("=" * 30)
    
    success = create_test_user()
    
    if success:
        print("\n🎉 Test user setup completed!")
        print("📋 You can now test the login with:")
        print("   Email: <EMAIL>")
        print("   Password: test123")
        print("\n🌐 Access the login page at: http://localhost:8080/login")
    else:
        print("\n❌ Test user setup failed!")
        print("💡 Check your Supabase credentials and database setup")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
