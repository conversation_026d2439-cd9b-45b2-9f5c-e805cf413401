#!/usr/bin/env python3
"""
GUI Launcher entrypoint for PyInstaller/PyArmor build.
Chooses tkinter or PyQt GUI at runtime, no web fallback.
"""
import sys
from pathlib import Path


def check_tk():
    try:
        import tkinter  # noqa
        return True
    except Exception:
        return False


def main():
    # Resolve gui_app location in frozen bundle or source tree
    if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
        base = Path(sys._MEIPASS) / 'gui_app'
    else:
        base = Path(__file__).parent.parent / 'gui_app'
    sys.path.insert(0, str(base))
    if check_tk():
        from main_gui import main as tk_main
        return tk_main()
    else:
        from qt_gui import main as qt_main
        return qt_main()


if __name__ == '__main__':
    main()

