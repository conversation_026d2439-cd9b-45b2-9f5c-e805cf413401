-- Cleanup Test User Script
-- This script removes the test user from both auth.users and user_profiles tables
-- Run this if you need to start over or clean up test data

-- ============================================================================
-- STEP 1: Remove test user profile
-- ============================================================================

DO $$
DECLARE
    test_email TEXT := '<EMAIL>';
    test_user_id UUID;
    profile_count INTEGER;
    user_count INTEGER;
BEGIN
    -- Get user ID from auth.users
    SELECT id INTO test_user_id 
    FROM auth.users 
    WHERE email = test_email;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Found test user with ID: %', test_user_id;
        
        -- Remove from user_profiles first (due to foreign key constraint)
        DELETE FROM public.user_profiles 
        WHERE user_id = test_user_id;
        
        GET DIAGNOSTICS profile_count = ROW_COUNT;
        RAISE NOTICE 'Removed % profile records', profile_count;
        
        -- Remove from auth.users
        DELETE FROM auth.users 
        WHERE id = test_user_id;
        
        GET DIAGNOSTICS user_count = ROW_COUNT;
        RAISE NOTICE 'Removed % user records', user_count;
        
        RAISE NOTICE 'Test user cleanup completed successfully';
    ELSE
        RAISE NOTICE 'No test user found with email: %', test_email;
    END IF;
END $$;

-- ============================================================================
-- STEP 2: Verify cleanup
-- ============================================================================

-- Check that user is removed from auth.users
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ User removed from auth.users'
        ELSE '❌ User still exists in auth.users'
    END as auth_cleanup_status
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check that profile is removed from user_profiles
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ Profile removed from user_profiles'
        ELSE '❌ Profile still exists in user_profiles'
    END as profile_cleanup_status
FROM public.user_profiles up
JOIN auth.users au ON up.user_id = au.id
WHERE au.email = '<EMAIL>';

SELECT '🧹 CLEANUP COMPLETE! 🧹' as final_status;
