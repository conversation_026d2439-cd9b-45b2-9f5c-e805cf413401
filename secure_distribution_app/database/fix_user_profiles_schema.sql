-- Fix user_profiles Table Schema
-- This script checks the current schema and adds missing columns

-- ============================================================================
-- STEP 1: Check current table structure
-- ============================================================================

-- Check what columns currently exist
SELECT 
    'CURRENT SCHEMA' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- ============================================================================
-- STEP 2: Add missing columns if they don't exist
-- ============================================================================

-- Add device_fingerprint column if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public' 
        AND column_name = 'device_fingerprint'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD COLUMN device_fingerprint TEXT;
        
        RAISE NOTICE 'Added device_fingerprint column';
    ELSE
        RAISE NOTICE 'device_fingerprint column already exists';
    END IF;
END $$;

-- Add license_number column if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public' 
        AND column_name = 'license_number'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD COLUMN license_number TEXT;
        
        RAISE NOTICE 'Added license_number column';
    ELSE
        RAISE NOTICE 'license_number column already exists';
    END IF;
END $$;

-- Add created_at column if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public' 
        AND column_name = 'created_at'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;
        
        RAISE NOTICE 'Added created_at column';
    ELSE
        RAISE NOTICE 'created_at column already exists';
    END IF;
END $$;

-- Add updated_at column if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public' 
        AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;
        
        RAISE NOTICE 'Added updated_at column';
    ELSE
        RAISE NOTICE 'updated_at column already exists';
    END IF;
END $$;

-- Add metadata column if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public' 
        AND column_name = 'metadata'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD COLUMN metadata JSONB DEFAULT '{}'::jsonb;
        
        RAISE NOTICE 'Added metadata column';
    ELSE
        RAISE NOTICE 'metadata column already exists';
    END IF;
END $$;

-- ============================================================================
-- STEP 3: Create indexes for new columns
-- ============================================================================

-- Index for device_fingerprint
CREATE INDEX IF NOT EXISTS idx_user_profiles_device_fingerprint 
ON public.user_profiles(device_fingerprint);

-- Index for license_number
CREATE INDEX IF NOT EXISTS idx_user_profiles_license_number 
ON public.user_profiles(license_number);

-- Index for created_at
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at 
ON public.user_profiles(created_at);

SELECT 'INDEXES CREATED' as status;

-- ============================================================================
-- STEP 4: Create updated_at trigger if missing
-- ============================================================================

-- Create function to handle updated_at
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS handle_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER handle_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

SELECT 'UPDATED_AT TRIGGER CREATED' as status;

-- ============================================================================
-- STEP 5: Verify the final schema
-- ============================================================================

-- Check final table structure
SELECT 
    'FINAL SCHEMA' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check that all required columns exist
SELECT 
    'REQUIRED COLUMNS CHECK' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'id') 
        THEN '✅ id' 
        ELSE '❌ id' 
    END as id_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'user_id') 
        THEN '✅ user_id' 
        ELSE '❌ user_id' 
    END as user_id_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'device_fingerprint') 
        THEN '✅ device_fingerprint' 
        ELSE '❌ device_fingerprint' 
    END as device_fingerprint_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'license_number') 
        THEN '✅ license_number' 
        ELSE '❌ license_number' 
    END as license_number_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'metadata') 
        THEN '✅ metadata' 
        ELSE '❌ metadata' 
    END as metadata_column;

-- Test that we can insert a record with all columns
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_profile_id UUID;
BEGIN
    -- Try to insert a test record
    INSERT INTO public.user_profiles (
        user_id,
        device_fingerprint,
        license_number,
        metadata
    ) VALUES (
        test_user_id,
        'test-schema-fix',
        'TEST-SCHEMA-001',
        '{"test": "schema_fix"}'::jsonb
    ) RETURNING id INTO test_profile_id;
    
    RAISE NOTICE 'Test insert successful! Profile ID: %', test_profile_id;
    
    -- Clean up test record
    DELETE FROM public.user_profiles WHERE id = test_profile_id;
    
    RAISE NOTICE 'Test record cleaned up';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Test insert failed: %', SQLERRM;
END $$;

SELECT '🎉 SCHEMA FIX COMPLETE! 🎉' as final_status;
SELECT 'user_profiles table now has all required columns' as result;
