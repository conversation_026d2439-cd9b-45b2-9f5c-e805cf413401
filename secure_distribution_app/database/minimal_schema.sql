-- Minimal <PERSON>hema Creation Script
-- This is the simplest version that should work without errors

-- Clean slate
DROP TABLE IF EXISTS public.user_profiles CASCADE;

-- Create the table with explicit column definitions
CREATE TABLE public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    device_fingerprint TEXT,
    license_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Add foreign key constraint separately
ALTER TABLE public.user_profiles 
ADD CONSTRAINT fk_user_profiles_user_id 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add unique constraint
ALTER TABLE public.user_profiles 
ADD CONSTRAINT unique_user_profile UNIQUE(user_id);

-- <PERSON><PERSON> indexes one by one
CREATE INDEX idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX idx_user_profiles_device_fingerprint ON public.user_profiles(device_fingerprint);
CREATE INDEX idx_user_profiles_license_number ON public.user_profiles(license_number);

-- Verify the table was created correctly
SELECT 'Table created successfully!' as status;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Test that we can reference the device_fingerprint column
SELECT COUNT(*) as device_fingerprint_test 
FROM public.user_profiles 
WHERE device_fingerprint IS NULL;

SELECT 'All tests passed! ✅' as final_status;
