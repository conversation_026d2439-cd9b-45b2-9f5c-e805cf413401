-- Link Profile to Auth User
-- Run this after creating the auth user in Supabase dashboard
-- This script updates the user_profiles table with the real auth user ID

-- ============================================================================
-- STEP 1: Find the auth user and profile to link
-- ============================================================================

DO $$
DECLARE
    auth_user_id UUID;
    profile_id UUID;
    test_email TEXT := '<EMAIL>';
    profile_count INTEGER;
    auth_user_count INTEGER;
BEGIN
    RAISE NOTICE 'Linking profile to auth user for: %', test_email;
    
    -- Check if auth user exists
    SELECT COUNT(*) INTO auth_user_count 
    FROM auth.users 
    WHERE email = test_email;
    
    IF auth_user_count = 0 THEN
        RAISE EXCEPTION 'No auth user found with email: %. Please create the user in Supabase dashboard first.', test_email;
    END IF;
    
    -- Get auth user ID
    SELECT id INTO auth_user_id 
    FROM auth.users 
    WHERE email = test_email;
    
    RAISE NOTICE 'Found auth user ID: %', auth_user_id;
    
    -- Check if profile exists
    SELECT COUNT(*) INTO profile_count 
    FROM public.user_profiles 
    WHERE metadata->>'email' = test_email;
    
    IF profile_count = 0 THEN
        RAISE EXCEPTION 'No profile found with email: %. Please run the profile creation script first.', test_email;
    END IF;
    
    -- Get profile ID
    SELECT id INTO profile_id 
    FROM public.user_profiles 
    WHERE metadata->>'email' = test_email
    LIMIT 1;
    
    RAISE NOTICE 'Found profile ID: %', profile_id;
    
    -- Update the profile with the real auth user ID
    UPDATE public.user_profiles 
    SET 
        user_id = auth_user_id,
        updated_at = NOW(),
        metadata = metadata || jsonb_build_object(
            'linked_at', NOW()::TEXT,
            'placeholder_user_id', false,
            'auth_user_linked', true
        )
    WHERE id = profile_id;
    
    RAISE NOTICE 'Profile successfully linked to auth user!';
    RAISE NOTICE 'Profile ID: %', profile_id;
    RAISE NOTICE 'Auth User ID: %', auth_user_id;
    RAISE NOTICE 'Email: %', test_email;
    
END $$;

-- ============================================================================
-- STEP 2: Verify the linking
-- ============================================================================

-- Check the linked profile
SELECT 
    'LINKED PROFILE VERIFICATION' as check_type,
    up.id as profile_id,
    up.user_id as auth_user_id,
    up.device_fingerprint,
    up.license_number,
    up.metadata->>'email' as email,
    up.metadata->>'auth_user_linked' as linked_status,
    up.updated_at
FROM public.user_profiles up
WHERE up.metadata->>'email' = '<EMAIL>';

-- Check the auth user
SELECT 
    'AUTH USER VERIFICATION' as check_type,
    au.id as auth_user_id,
    au.email,
    au.email_confirmed_at IS NOT NULL as email_confirmed,
    au.created_at
FROM auth.users au
WHERE au.email = '<EMAIL>';

-- Check that they're properly linked
SELECT 
    'LINK VERIFICATION' as check_type,
    au.email,
    au.id as auth_user_id,
    up.id as profile_id,
    up.device_fingerprint,
    up.license_number,
    '✅ SUCCESSFULLY LINKED' as status
FROM auth.users au
JOIN public.user_profiles up ON au.id = up.user_id
WHERE au.email = '<EMAIL>';

SELECT '🎉 PROFILE LINKED TO AUTH USER! 🎉' as final_status;
SELECT 'Ready to test login at: http://localhost:8080/login' as next_step;
SELECT 'Credentials: <EMAIL> / test123' as login_info;
