-- Debug Schema Creation Script
-- Run each section separately to identify where the error occurs

-- ============================================================================
-- SECTION 1: Clean up existing objects (run this first)
-- ============================================================================

-- Drop existing triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS handle_user_profiles_updated_at ON public.user_profiles;

-- Drop existing functions
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.handle_updated_at();

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;

-- Drop existing table
DROP TABLE IF EXISTS public.user_profiles;

SELECT 'SECTION 1 COMPLETED: Cleanup finished' as status;

-- ============================================================================
-- SECTION 2: Create the table (run this second)
-- ============================================================================

CREATE TABLE public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    device_fingerprint TEXT,
    license_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Ensure one profile per user
    CONSTRAINT unique_user_profile UNIQUE(user_id)
);

-- Verify table creation
SELECT 'SECTION 2 COMPLETED: Table created successfully' as status;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- ============================================================================
-- SECTION 3: Create indexes (run this third)
-- ============================================================================

CREATE INDEX idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX idx_user_profiles_device_fingerprint ON public.user_profiles(device_fingerprint);
CREATE INDEX idx_user_profiles_license_number ON public.user_profiles(license_number);
CREATE INDEX idx_user_profiles_created_at ON public.user_profiles(created_at);

SELECT 'SECTION 3 COMPLETED: Indexes created successfully' as status;

-- ============================================================================
-- SECTION 4: Enable RLS and create policies (run this fourth)
-- ============================================================================

-- Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

SELECT 'SECTION 4 COMPLETED: RLS and policies created successfully' as status;

-- ============================================================================
-- SECTION 5: Create functions (run this fifth)
-- ============================================================================

-- Function to handle updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (user_id, metadata)
    VALUES (
        NEW.id,
        jsonb_build_object(
            'email', NEW.email,
            'created_at', NEW.created_at,
            'login_count', 0
        )
    );
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Failed to create user profile for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

SELECT 'SECTION 5 COMPLETED: Functions created successfully' as status;

-- ============================================================================
-- SECTION 6: Create triggers (run this sixth)
-- ============================================================================

-- Trigger for updated_at
CREATE TRIGGER handle_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

SELECT 'SECTION 6 COMPLETED: Triggers created successfully' as status;

-- ============================================================================
-- SECTION 7: Grant permissions (run this seventh)
-- ============================================================================

GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.user_profiles TO authenticated;
GRANT SELECT ON public.user_profiles TO anon;

SELECT 'SECTION 7 COMPLETED: Permissions granted successfully' as status;

-- ============================================================================
-- SECTION 8: Final verification (run this last)
-- ============================================================================

-- Check table exists and has correct structure
SELECT 
    'Table exists: ' || CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'user_profiles' AND table_schema = 'public'
    ) THEN 'YES' ELSE 'NO' END as table_status;

-- Check columns exist
SELECT 
    'device_fingerprint column exists: ' || CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public' 
        AND column_name = 'device_fingerprint'
    ) THEN 'YES' ELSE 'NO' END as column_status;

-- Check indexes exist
SELECT 
    'Indexes created: ' || count(*)::text as index_count
FROM pg_indexes 
WHERE tablename = 'user_profiles' AND schemaname = 'public';

-- Check RLS is enabled
SELECT 
    'RLS enabled: ' || CASE WHEN rowsecurity THEN 'YES' ELSE 'NO' END as rls_status
FROM pg_class 
WHERE relname = 'user_profiles' AND relnamespace = (
    SELECT oid FROM pg_namespace WHERE nspname = 'public'
);

SELECT 'SECTION 8 COMPLETED: All verification checks finished' as status;
SELECT '🎉 SCHEMA SETUP COMPLETE! 🎉' as final_status;
