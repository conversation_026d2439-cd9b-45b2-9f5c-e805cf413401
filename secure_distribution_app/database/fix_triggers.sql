-- Fix Database Triggers and Constraints
-- This script diagnoses and fixes issues causing "Database error creating new user"

-- ============================================================================
-- STEP 1: Diagnose current trigger issues
-- ============================================================================

-- Check existing triggers on auth.users
SELECT 
    trigger_name,
    event_manipulation,
    action_statement,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'users' 
AND event_object_schema = 'auth';

-- Check existing functions that might be causing issues
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%user%';

-- ============================================================================
-- STEP 2: Temporarily disable problematic triggers
-- ============================================================================

-- Disable the automatic profile creation trigger temporarily
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Drop the problematic function
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

SELECT 'TRIGGERS DISABLED' as status;

-- ============================================================================
-- STEP 3: Test user creation without triggers
-- ============================================================================

-- This should now work without trigger interference
-- (We'll test this programmatically)

-- ============================================================================
-- STEP 4: Create a safer trigger function
-- ============================================================================

-- Create a more robust function that handles errors gracefully
CREATE OR REPLACE FUNCTION public.handle_new_user_safe()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if it doesn't already exist
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = NEW.id) THEN
        BEGIN
            INSERT INTO public.user_profiles (user_id, metadata)
            VALUES (
                NEW.id,
                jsonb_build_object(
                    'email', COALESCE(NEW.email, ''),
                    'created_at', COALESCE(NEW.created_at, NOW()),
                    'login_count', 0,
                    'auto_created', true
                )
            );
            
            RAISE NOTICE 'User profile created for user %', NEW.id;
            
        EXCEPTION
            WHEN unique_violation THEN
                -- Profile already exists, that's OK
                RAISE NOTICE 'User profile already exists for user %', NEW.id;
            WHEN foreign_key_violation THEN
                -- User doesn't exist yet, that's OK
                RAISE NOTICE 'User % not ready for profile creation', NEW.id;
            WHEN OTHERS THEN
                -- Log error but don't fail the user creation
                RAISE WARNING 'Failed to create user profile for user %: % %', NEW.id, SQLSTATE, SQLERRM;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

SELECT 'SAFE FUNCTION CREATED' as status;

-- ============================================================================
-- STEP 5: Create the safer trigger (optional - we'll test without it first)
-- ============================================================================

-- Don't create the trigger yet - we'll test user creation without it first
-- CREATE TRIGGER on_auth_user_created_safe
--     AFTER INSERT ON auth.users
--     FOR EACH ROW
--     EXECUTE FUNCTION public.handle_new_user_safe();

SELECT 'TRIGGER SETUP READY (NOT ENABLED YET)' as status;

-- ============================================================================
-- STEP 6: Verify the fix
-- ============================================================================

-- Check that triggers are disabled
SELECT 
    COUNT(*) as trigger_count,
    'Should be 0 if triggers are disabled' as note
FROM information_schema.triggers 
WHERE event_object_table = 'users' 
AND event_object_schema = 'auth'
AND trigger_name LIKE '%user%';

-- Check that user_profiles table is still accessible
SELECT COUNT(*) as profile_count FROM public.user_profiles;

SELECT 'VERIFICATION COMPLETE' as status;
SELECT 'Now test user creation without triggers' as next_step;
