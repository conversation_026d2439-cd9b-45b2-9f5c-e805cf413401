-- Fix Trigger Issue and Create Test User
-- This script fixes the problematic trigger and then creates the test user

-- ============================================================================
-- STEP 1: Diagnose and fix the trigger issue
-- ============================================================================

-- Check what triggers exist on auth.users
SELECT 
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'users' 
AND event_object_schema = 'auth';

-- Check what functions are causing issues
SELECT 
    routine_name,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%user%profile%';

-- ============================================================================
-- STEP 2: Remove problematic triggers and functions
-- ============================================================================

-- Drop all triggers that might be causing issues
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users CASCADE;
DROP TRIGGER IF EXISTS create_user_profile_trigger ON auth.users CASCADE;
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users CASCADE;

-- Drop problematic functions
DROP FUNCTION IF EXISTS public.create_user_profile() CASCADE;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.handle_new_user_safe() CASCADE;

SELECT 'PROBLEMATIC TRIGGERS AND FUNCTIONS REMOVED' as status;

-- ============================================================================
-- STEP 3: Create a correct trigger function (optional)
-- ============================================================================

-- Create a new, correct function that matches our actual schema
CREATE OR REPLACE FUNCTION public.handle_new_user_correct()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if it doesn't exist and if user_profiles table has correct schema
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = NEW.id) THEN
        BEGIN
            -- Insert with correct column names that actually exist
            INSERT INTO public.user_profiles (
                user_id,
                device_fingerprint,
                license_number,
                metadata
            ) VALUES (
                NEW.id,
                'auto-' || NEW.id::TEXT,
                NULL,
                jsonb_build_object(
                    'email', COALESCE(NEW.email, ''),
                    'created_at', COALESCE(NEW.created_at, NOW()),
                    'login_count', 0,
                    'auto_created', true
                )
            );
            
            RAISE NOTICE 'Profile created for user %', NEW.id;
            
        EXCEPTION
            WHEN OTHERS THEN
                -- Log error but don't fail user creation
                RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

SELECT 'CORRECT TRIGGER FUNCTION CREATED' as status;

-- ============================================================================
-- STEP 4: Create test user without triggers (safer approach)
-- ============================================================================

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create user without any triggers active
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_password TEXT := 'test123';
    hashed_password TEXT;
    user_exists BOOLEAN := FALSE;
BEGIN
    -- Check if user exists
    SELECT EXISTS(SELECT 1 FROM auth.users WHERE email = test_email) INTO user_exists;
    
    IF user_exists THEN
        RAISE NOTICE 'User % already exists', test_email;
        SELECT id INTO test_user_id FROM auth.users WHERE email = test_email;
    ELSE
        -- Hash password
        hashed_password := crypt(test_password, gen_salt('bf'));
        
        RAISE NOTICE 'Creating user % with ID %', test_email, test_user_id;
        
        -- Insert user (triggers are disabled)
        INSERT INTO auth.users (
            instance_id,
            id,
            aud,
            role,
            email,
            encrypted_password,
            email_confirmed_at,
            invited_at,
            confirmation_token,
            confirmation_sent_at,
            created_at,
            updated_at,
            raw_app_meta_data,
            raw_user_meta_data
        ) VALUES (
            '00000000-0000-0000-0000-000000000000',
            test_user_id,
            'authenticated',
            'authenticated',
            test_email,
            hashed_password,
            NOW(),
            NOW(),
            '',
            NOW(),
            NOW(),
            NOW(),
            '{"provider": "email", "providers": ["email"]}',
            '{"first_name": "Test", "last_name": "User"}'
        );
        
        RAISE NOTICE 'User created successfully';
    END IF;
    
    -- Create profile manually
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = test_user_id) THEN
        INSERT INTO public.user_profiles (
            user_id,
            device_fingerprint,
            license_number,
            metadata
        ) VALUES (
            test_user_id,
            'manual-test-' || EXTRACT(EPOCH FROM NOW())::TEXT,
            'TEST-LICENSE-001',
            jsonb_build_object(
                'email', test_email,
                'login_count', 0,
                'test_user', true,
                'created_manually', true,
                'first_name', 'Test',
                'last_name', 'User'
            )
        );
        
        RAISE NOTICE 'Profile created successfully';
    ELSE
        RAISE NOTICE 'Profile already exists';
    END IF;
    
    RAISE NOTICE 'Test user setup complete!';
END $$;

-- ============================================================================
-- STEP 5: Optionally re-enable the correct trigger
-- ============================================================================

-- Uncomment this if you want automatic profile creation for future users
-- CREATE TRIGGER on_auth_user_created_correct
--     AFTER INSERT ON auth.users
--     FOR EACH ROW
--     EXECUTE FUNCTION public.handle_new_user_correct();

SELECT 'TRIGGER REMAINS DISABLED FOR SAFETY' as trigger_status;

-- ============================================================================
-- STEP 6: Verify everything works
-- ============================================================================

-- Test user
SELECT 
    'USER CHECK' as type,
    id,
    email,
    email_confirmed_at IS NOT NULL as confirmed,
    created_at
FROM auth.users 
WHERE email = '<EMAIL>';

-- Test profile
SELECT 
    'PROFILE CHECK' as type,
    up.id,
    up.user_id,
    up.device_fingerprint,
    up.license_number,
    up.metadata
FROM public.user_profiles up
JOIN auth.users au ON up.user_id = au.id
WHERE au.email = '<EMAIL>';

-- Test password
SELECT 
    'PASSWORD CHECK' as type,
    CASE 
        WHEN encrypted_password = crypt('test123', encrypted_password) 
        THEN '✅ VALID' 
        ELSE '❌ INVALID' 
    END as status
FROM auth.users 
WHERE email = '<EMAIL>';

SELECT '🎉 TRIGGER FIXED AND USER CREATED! 🎉' as final_status;
