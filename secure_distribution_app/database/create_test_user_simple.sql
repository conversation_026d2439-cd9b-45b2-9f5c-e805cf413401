-- Simple Test User Creation Script
-- This script creates a test user without any triggers or complex logic
-- Run this in Supabase SQL Editor

-- ============================================================================
-- STEP 1: Disable ALL triggers on auth.users to prevent conflicts
-- ============================================================================

-- Disable all triggers on auth.users table
ALTER TABLE auth.users DISABLE TRIGGER ALL;

SELECT 'ALL TRIGGERS DISABLED ON auth.users' as status;

-- ============================================================================
-- STEP 2: Enable required extensions
-- ============================================================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;

SELECT 'EXTENSIONS ENABLED' as status;

-- ============================================================================
-- STEP 3: Create the test user directly
-- ============================================================================

-- Generate UUID and hash password
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_password TEXT := 'test123';
    hashed_password TEXT;
    existing_count INTEGER;
BEGIN
    -- Check if user already exists
    SELECT COUNT(*) INTO existing_count 
    FROM auth.users 
    WHERE email = test_email;
    
    IF existing_count > 0 THEN
        RAISE NOTICE 'User already exists: %', test_email;
        -- Get existing user ID for profile creation
        SELECT id INTO test_user_id FROM auth.users WHERE email = test_email;
    ELSE
        -- Hash password
        hashed_password := crypt(test_password, gen_salt('bf'));
        
        -- Insert user with minimal required fields
        INSERT INTO auth.users (
            instance_id,
            id,
            aud,
            role,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_app_meta_data,
            raw_user_meta_data
        ) VALUES (
            '00000000-0000-0000-0000-000000000000',
            test_user_id,
            'authenticated',
            'authenticated',
            test_email,
            hashed_password,
            NOW(), -- Email confirmed immediately
            NOW(),
            NOW(),
            '{"provider": "email", "providers": ["email"]}',
            '{"first_name": "Test", "last_name": "User"}'
        );
        
        RAISE NOTICE 'User created with ID: %', test_user_id;
    END IF;
    
    -- Create user profile (check if exists first)
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = test_user_id) THEN
        INSERT INTO public.user_profiles (
            id,
            user_id,
            device_fingerprint,
            license_number,
            created_at,
            updated_at,
            metadata
        ) VALUES (
            gen_random_uuid(),
            test_user_id,
            'test-fingerprint-' || EXTRACT(EPOCH FROM NOW())::TEXT,
            'TEST-LICENSE-001',
            NOW(),
            NOW(),
            jsonb_build_object(
                'email', test_email,
                'login_count', 0,
                'test_user', true,
                'created_via_simple_sql', true,
                'first_name', 'Test',
                'last_name', 'User'
            )
        );
        
        RAISE NOTICE 'User profile created for: %', test_user_id;
    ELSE
        RAISE NOTICE 'User profile already exists for: %', test_user_id;
    END IF;
    
    RAISE NOTICE 'Setup complete! Email: % | Password: %', test_email, test_password;
END $$;

-- ============================================================================
-- STEP 4: Re-enable triggers (optional - you can skip this if having issues)
-- ============================================================================

-- Re-enable triggers (comment this out if you want to keep them disabled)
-- ALTER TABLE auth.users ENABLE TRIGGER ALL;

SELECT 'TRIGGERS REMAIN DISABLED (safer for testing)' as trigger_status;

-- ============================================================================
-- STEP 5: Verify creation
-- ============================================================================

-- Check user was created
SELECT 
    'USER VERIFICATION' as check_type,
    id,
    email,
    email_confirmed_at IS NOT NULL as email_confirmed,
    created_at
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check profile was created
SELECT 
    'PROFILE VERIFICATION' as check_type,
    up.id as profile_id,
    up.user_id,
    up.device_fingerprint,
    up.license_number,
    up.metadata->>'email' as email_in_metadata
FROM public.user_profiles up
JOIN auth.users au ON up.user_id = au.id
WHERE au.email = '<EMAIL>';

-- Test password hash
SELECT 
    'PASSWORD TEST' as check_type,
    email,
    CASE 
        WHEN encrypted_password = crypt('test123', encrypted_password) 
        THEN '✅ PASSWORD VALID' 
        ELSE '❌ PASSWORD INVALID' 
    END as password_status
FROM auth.users 
WHERE email = '<EMAIL>';

-- ============================================================================
-- FINAL STATUS
-- ============================================================================

SELECT '🎉 SIMPLE USER CREATION COMPLETE! 🎉' as final_status;
SELECT 'Credentials: <EMAIL> / test123' as login_info;
SELECT 'Test at: http://localhost:8080/login' as next_step;
