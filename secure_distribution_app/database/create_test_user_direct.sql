-- Create Test User Profile Only
-- This script creates a user profile in user_profiles table only
-- You'll need to create the auth user manually in Supabase dashboard

-- ============================================================================
-- STEP 1: Enable required extensions
-- ============================================================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

SELECT 'EXTENSIONS ENABLED' as status;

-- ============================================================================
-- STEP 2: Create user profile with placeholder user_id
-- ============================================================================

DO $$
DECLARE
    test_user_id UUID := gen_random_uuid(); -- Placeholder UUID
    test_email TEXT := '<EMAIL>';
    existing_profile_count INTEGER;
    profile_id UUID;
BEGIN
    RAISE NOTICE 'Creating user profile for test user...';
    RAISE NOTICE 'Placeholder User ID: %', test_user_id;
    RAISE NOTICE 'Email: %', test_email;

    -- Check if a profile already exists for this email in metadata
    SELECT COUNT(*) INTO existing_profile_count
    FROM public.user_profiles
    WHERE metadata->>'email' = test_email;

    IF existing_profile_count > 0 THEN
        RAISE NOTICE 'User profile already exists for email: %', test_email;

        -- Get existing profile info
        SELECT id, user_id INTO profile_id, test_user_id
        FROM public.user_profiles
        WHERE metadata->>'email' = test_email
        LIMIT 1;

        RAISE NOTICE 'Existing Profile ID: %', profile_id;
        RAISE NOTICE 'Existing User ID: %', test_user_id;
    ELSE
        RAISE NOTICE 'Creating new user profile...';

        -- Insert into user_profiles table
        INSERT INTO public.user_profiles (
            id,
            user_id,
            device_fingerprint,
            license_number,
            created_at,
            updated_at,
            metadata
        ) VALUES (
            gen_random_uuid(), -- id
            test_user_id, -- user_id (placeholder - will be updated when real user is created)
            'test-fingerprint-' || EXTRACT(EPOCH FROM NOW())::TEXT, -- device_fingerprint
            'TEST-LICENSE-001', -- license_number
            NOW(), -- created_at
            NOW(), -- updated_at
            jsonb_build_object(
                'email', test_email,
                'login_count', 0,
                'test_user', true,
                'created_via_sql', true,
                'placeholder_user_id', true,
                'instructions', 'Create auth user in dashboard then update user_id',
                'created_at', NOW()::TEXT,
                'first_name', 'Test',
                'last_name', 'User'
            ) -- metadata
        ) RETURNING id INTO profile_id;

        RAISE NOTICE 'User profile created successfully!';
        RAISE NOTICE 'Profile ID: %', profile_id;
    END IF;

    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. Go to Supabase Dashboard → Authentication → Users';
    RAISE NOTICE '2. Click "Add User"';
    RAISE NOTICE '3. Email: %', test_email;
    RAISE NOTICE '4. Password: test123';
    RAISE NOTICE '5. Check "Email Confirm"';
    RAISE NOTICE '6. Click "Create User"';
    RAISE NOTICE '7. Copy the new user ID from the dashboard';
    RAISE NOTICE '8. Run the update script to link the profile';
    RAISE NOTICE '';

END $$;

-- ============================================================================
-- STEP 3: Verify the creation
-- ============================================================================

-- Check auth.users table
SELECT 
    'AUTH USER VERIFICATION' as check_type,
    id,
    email,
    email_confirmed_at IS NOT NULL as email_confirmed,
    created_at,
    raw_user_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check user_profiles table
SELECT 
    'PROFILE VERIFICATION' as check_type,
    up.id as profile_id,
    up.user_id,
    up.device_fingerprint,
    up.license_number,
    up.created_at,
    up.metadata
FROM public.user_profiles up
JOIN auth.users au ON up.user_id = au.id
WHERE au.email = '<EMAIL>';

-- ============================================================================
-- STEP 4: Test authentication (optional verification)
-- ============================================================================

-- This query verifies the password hash works correctly
-- (You can't actually test sign-in via SQL, but this confirms the hash)
SELECT 
    'PASSWORD VERIFICATION' as check_type,
    email,
    encrypted_password IS NOT NULL as has_password_hash,
    email_confirmed_at IS NOT NULL as email_confirmed,
    CASE 
        WHEN encrypted_password = crypt('test123', encrypted_password) 
        THEN 'PASSWORD HASH VALID' 
        ELSE 'PASSWORD HASH INVALID' 
    END as password_check
FROM auth.users 
WHERE email = '<EMAIL>';

-- ============================================================================
-- FINAL STATUS
-- ============================================================================

SELECT '🎉 TEST USER CREATION COMPLETE! 🎉' as final_status;
SELECT 'You can now test login at: http://localhost:8080/login' as next_step;
SELECT 'Credentials: <EMAIL> / test123' as credentials;
