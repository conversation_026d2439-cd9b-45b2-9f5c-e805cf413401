-- Improved Bulletproof Supabase Schema Creation Script
-- Enhanced security, performance, and best practices

-- ============================================================================
-- STEP 1: Clean up existing objects
-- ============================================================================
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users CASCADE;
DROP TRIGGER IF EXISTS handle_user_profiles_updated_at ON public.user_profiles CASCADE;

DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.handle_updated_at() CASCADE;

-- Drop existing policies and table
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON public.user_profiles;
DROP TABLE IF EXISTS public.user_profiles CASCADE;

-- ============================================================================
-- STEP 2: Create table with enhanced constraints
-- ============================================================================
CREATE TABLE public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL UNIQUE,
    device_fingerprint TEXT CHECK (length(device_fingerprint) <= 255),
    license_number TEXT CHECK (license_number ~ '^[A-Z0-9-]{5,20}$'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Foreign key constraint
    CONSTRAINT fk_user_profiles_user_id 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- ============================================================================
-- STEP 3: Create indexes with performance in mind
-- ============================================================================
CREATE INDEX idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX idx_user_profiles_device_fingerprint ON public.user_profiles(device_fingerprint);
CREATE INDEX idx_user_profiles_created_at ON public.user_profiles(created_at);

-- ============================================================================
-- STEP 4: Set up secure permissions
-- ============================================================================
-- Revoke all existing privileges
REVOKE ALL ON public.user_profiles FROM PUBLIC;

-- Grant minimal, specific permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_profiles TO authenticated;
GRANT SELECT, INSERT ON public.user_profiles TO anon;
GRANT ALL ON public.user_profiles TO service_role;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated, service_role;

-- ============================================================================
-- STEP 5: Enable RLS with granular policies
-- ============================================================================
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Specific, restrictive policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT 
    TO authenticated 
    USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT 
    TO authenticated 
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE 
    TO authenticated 
    USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own profile" ON public.user_profiles
    FOR DELETE 
    TO authenticated 
    USING ((SELECT auth.uid()) = user_id);

-- ============================================================================
-- STEP 6: Create secure helper functions
-- ============================================================================
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Set search path to prevent schema-based attacks
    SET search_path TO '';
    
    -- Prevent unauthorized updates to critical fields
    IF (NEW.user_id IS DISTINCT FROM OLD.user_id) THEN
        RAISE EXCEPTION 'Cannot modify user_id';
    END IF;

    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    v_default_metadata JSONB;
BEGIN
    -- Set search path to prevent schema-based attacks
    SET search_path TO '';

    -- Create comprehensive default metadata
    v_default_metadata := jsonb_build_object(
        'email', NEW.email,
        'created_at', NOW(),
        'login_count', 0,
        'auto_created', true,
        'last_sign_in_at', NULL,
        'provider', NEW.raw_app_meta_data->>'provider' 
    );

    -- Attempt to insert profile, with comprehensive error handling
    BEGIN
        INSERT INTO public.user_profiles (
            user_id, 
            metadata
        ) VALUES (
            NEW.id,
            v_default_metadata
        );
    EXCEPTION 
        WHEN unique_violation THEN
            -- Log the error, but don't block user creation
            RAISE WARNING 'Profile for user % already exists', NEW.id;
        WHEN OTHERS THEN
            -- Comprehensive error logging
            RAISE WARNING 'Failed to create user profile for %: %', 
                NEW.id, 
                SQLERRM;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 7: Create secure triggers
-- ============================================================================
CREATE TRIGGER handle_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- STEP 8: Verification and Comments
-- ============================================================================
COMMENT ON TABLE public.user_profiles IS 
    'Stores additional user profile information with strict access controls';

COMMENT ON COLUMN public.user_profiles.metadata IS 
    'Extensible JSON metadata for storing dynamic user information';

-- Optional: Print completion message
SELECT '🎉 Enhanced User Profiles Schema Setup Complete! 🎉' as status;