#!/bin/bash
# Secure Mobile App Automation Platform - Unix/macOS Launcher
# This script launches the secure web-based mobile app automation platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo
echo "================================================================"
echo "  SECURE MOBILE APP AUTOMATION PLATFORM - UNIX/MACOS LAUNCHER"
echo "================================================================"
echo

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "Starting from directory: $SCRIPT_DIR"
print_info "Project root: $PROJECT_ROOT"

# Change to the secure distribution app directory
cd "$SCRIPT_DIR"

# Check if we're running from a built executable or source
if [[ -f "$SCRIPT_DIR/main_web_secure" ]]; then
    print_info "Running from built executable"
    EXECUTABLE="$SCRIPT_DIR/main_web_secure"
    
    # Make sure it's executable
    chmod +x "$EXECUTABLE"
    
    # Run the executable
    print_info "Starting executable: $EXECUTABLE"
    "$EXECUTABLE"
    exit $?
fi

# Running from source - check for Python and virtual environment
print_info "Running from source code"

# Check for virtual environment
VENV_PYTHON="$PROJECT_ROOT/venv/bin/python"
if [[ -f "$VENV_PYTHON" ]]; then
    print_info "Using virtual environment Python: $VENV_PYTHON"
    PYTHON_EXECUTABLE="$VENV_PYTHON"
else
    print_warning "Virtual environment not found, using system Python"
    
    # Try to find Python 3
    if command -v python3 &> /dev/null; then
        PYTHON_EXECUTABLE="python3"
    elif command -v python &> /dev/null; then
        PYTHON_EXECUTABLE="python"
    else
        print_error "Python not found. Please install Python 3 or create a virtual environment."
        print_error "Expected virtual environment at: $VENV_PYTHON"
        exit 1
    fi
fi

# Verify Python version
PYTHON_VERSION=$($PYTHON_EXECUTABLE --version 2>&1)
print_info "Python executable: $PYTHON_EXECUTABLE ($PYTHON_VERSION)"

# Check if main script exists
if [[ ! -f "$SCRIPT_DIR/main_web_secure.py" ]]; then
    print_error "Main script not found: $SCRIPT_DIR/main_web_secure.py"
    exit 1
fi

# Clean up any existing processes
print_info "Cleaning up existing processes..."

# Kill Python processes that might be running our apps
pkill -f "run.py" 2>/dev/null || true
pkill -f "run_android.py" 2>/dev/null || true
pkill -f "main_web_secure.py" 2>/dev/null || true
pkill -f "app.py" 2>/dev/null || true

# Kill processes on common ports
for port in 8080 8081 8082 8090 4723 4724; do
    # Find and kill processes using these ports
    if command -v lsof &> /dev/null; then
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
    fi
done

# Kill Appium processes
pkill -f "appium" 2>/dev/null || true

print_info "Process cleanup completed"

# Wait a moment for processes to terminate
sleep 2

# Check for .env file
if [[ ! -f "$SCRIPT_DIR/.env" ]]; then
    print_warning ".env file not found at: $SCRIPT_DIR/.env"
    print_warning "Please ensure Supabase credentials are configured"
fi

# Check for required dependencies if running from source
if [[ -f "$PROJECT_ROOT/requirements.txt" ]]; then
    print_info "Checking Python dependencies..."
    
    # Check if we can import required modules
    if ! $PYTHON_EXECUTABLE -c "import flask, supabase" 2>/dev/null; then
        print_warning "Some dependencies may be missing"
        print_info "Installing/updating dependencies..."
        
        # Install dependencies
        $PYTHON_EXECUTABLE -m pip install -r "$PROJECT_ROOT/requirements.txt" || {
            print_error "Failed to install dependencies"
            exit 1
        }
    fi
fi

# Set up environment variables
export PYTHONPATH="$PROJECT_ROOT:$SCRIPT_DIR:$PYTHONPATH"
export SECURE_BUILD="True"

# Detect OS and show appropriate platform options
OS_TYPE=$(uname -s)
case "$OS_TYPE" in
    Darwin*)
        print_info "Detected macOS - iOS and Android automation available"
        export PLATFORM_SUPPORT="ios,android"
        ;;
    Linux*)
        print_info "Detected Linux - Android automation available"
        export PLATFORM_SUPPORT="android"
        ;;
    *)
        print_info "Detected $OS_TYPE - Android automation available"
        export PLATFORM_SUPPORT="android"
        ;;
esac

# Launch the application
print_info "Launching Secure Mobile App Automation Platform..."
echo

# Set up signal handlers for graceful shutdown
trap 'print_info "Shutting down..."; exit 0' INT TERM

# Run the application
$PYTHON_EXECUTABLE main_web_secure.py

# Check exit code
EXIT_CODE=$?
if [[ $EXIT_CODE -ne 0 ]]; then
    echo
    print_error "Application exited with error code: $EXIT_CODE"
    print_error "Check the logs for more details"
    echo
    exit $EXIT_CODE
fi

echo
print_success "Application exited normally"
echo
