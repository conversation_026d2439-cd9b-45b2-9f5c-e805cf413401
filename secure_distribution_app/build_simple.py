#!/usr/bin/env python3
"""
Simple Build Script for Mobile App Automation

Creates a basic executable without obfuscation or integrity checking.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_simple_spec():
    """Create a simple PyInstaller spec file"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('auth', 'auth'),
        ('security', 'security'),
        ('downloader', 'downloader'),
        ('launcher', 'launcher'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        'supabase',
        'cryptography',
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'psutil',
        'threading',
        'urllib.request',
        'urllib.parse',
        'base64',
        'hashlib',
        'tempfile',
        'zipfile',
        'shutil',
        'platform',
        'webbrowser',
        'subprocess',
        'json',
        'pathlib',
        'os',
        'sys',
        'time',
        'logging',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MobileAppAutomation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # Disable UPX as it can cause issues with tkinter
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=True,  # Enable for macOS
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

app = BUNDLE(
    exe,
    name='MobileAppAutomation.app',
    icon=None,
    bundle_identifier='com.mobileautomation.app',
)
'''
    
    with open('simple_app.spec', 'w') as f:
        f.write(spec_content)
    
    logger.info("Simple spec file created")

def build_executable():
    """Build the executable using PyInstaller"""
    try:
        logger.info("Building executable with PyInstaller...")
        
        # Create spec file
        create_simple_spec()
        
        # Run PyInstaller
        cmd = ['pyinstaller', '--clean', 'simple_app.spec']
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Executable built successfully!")
            return True
        else:
            logger.error(f"❌ PyInstaller failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Build failed: {e}")
        return False

def main():
    """Main entry point"""
    try:
        print("🚀 Simple Build - Mobile App Automation")
        print("=" * 40)
        
        # Check if we're in the right directory
        if not Path('main.py').exists():
            print("❌ main.py not found. Please run from secure_distribution_app directory.")
            sys.exit(1)
        
        # Build executable
        if build_executable():
            print("\n✅ Build completed successfully!")
            print("\n📁 Output location:")
            
            # Check for different output formats
            if Path('dist/MobileAppAutomation.app').exists():
                print("   macOS App: dist/MobileAppAutomation.app")
            elif Path('dist/MobileAppAutomation').exists():
                print("   Executable: dist/MobileAppAutomation")
            elif Path('dist/MobileAppAutomation.exe').exists():
                print("   Windows Exe: dist/MobileAppAutomation.exe")
            else:
                print("   Check dist/ directory for output files")
            
            print("\n📋 Next steps:")
            print("1. Test the executable")
            print("2. Upload unified package to Google Drive")
            print("3. Update encrypted link in hidden_link_downloader.py")
            print("4. Distribute to users")
        else:
            print("\n❌ Build failed!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Build cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Build process failed: {e}")
        print(f"\n❌ Build process failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
