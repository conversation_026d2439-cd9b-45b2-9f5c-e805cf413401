#!/usr/bin/env python3
"""
Build Script for Browser-Based Authentication Version

Creates a working executable with browser-based authentication instead of tkinter login.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_tkinter():
    """Check if tkinter is available"""
    try:
        import tkinter
        logger.info("✅ tkinter is available")
        return True
    except ImportError:
        logger.error("❌ tkinter is not available")
        return False

def create_pyinstaller_spec():
    """Create PyInstaller spec file for browser auth version"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_browser_auth.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env', '.'),
        ('templates', 'templates'),
        ('auth', 'auth'),
        ('gui', 'gui'),
        ('security', 'security'),
        ('downloader', 'downloader'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'webbrowser',
        'http.server',
        'socketserver',
        'urllib.parse',
        'threading',
        'json',
        'pathlib',
        'supabase',
        'cryptography',
        'pystray',
        'PIL',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MobileAppAutomationBrowser',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# Create macOS app bundle
app = BUNDLE(
    exe,
    name='MobileAppAutomationBrowser.app',
    icon=None,
    bundle_identifier='com.mobileautomation.browser',
    info_plist={
        'CFBundleName': 'Mobile App Automation Browser',
        'CFBundleDisplayName': 'Mobile App Automation Browser',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'NSHighResolutionCapable': True,
        'LSUIElement': False,  # Show in dock
    },
)
'''
    
    spec_file = Path('browser_auth.spec')
    with open(spec_file, 'w') as f:
        f.write(spec_content)
    
    logger.info("Browser auth spec file created")
    return spec_file

def build_executable():
    """Build the executable using PyInstaller"""
    try:
        # Get Python executable
        python_exe = sys.executable
        logger.info(f"Using Python: {python_exe}")
        
        # Create spec file
        spec_file = create_pyinstaller_spec()
        
        # Build command
        cmd = [
            python_exe, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            str(spec_file)
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        
        # Run PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Executable built successfully!")
            
            # List created files
            dist_dir = Path('dist')
            if dist_dir.exists():
                for item in dist_dir.iterdir():
                    logger.info(f"Created: {item}")
            
            return True
        else:
            logger.error(f"❌ Build failed with return code {result.returncode}")
            logger.error(f"STDOUT: {result.stdout}")
            logger.error(f"STDERR: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Build error: {e}")
        return False

def test_executable():
    """Test the built executable"""
    try:
        logger.info("Testing executable...")
        
        # Test standalone executable
        exe_path = Path('dist/MobileAppAutomationBrowser')
        if exe_path.exists():
            logger.info("Testing standalone executable...")
            
            # Run with timeout
            result = subprocess.run(
                [str(exe_path)], 
                timeout=5,
                capture_output=True,
                text=True
            )
            
            logger.info("✅ Executable started (timeout reached, which is expected for GUI)")
            return True
        else:
            logger.error("❌ Executable not found")
            return False
            
    except subprocess.TimeoutExpired:
        logger.info("✅ Executable started (timeout reached, which is expected for GUI)")
        return True
    except Exception as e:
        logger.error(f"Test error: {e}")
        return False

def main():
    """Main entry point"""
    try:
        print("🚀 Browser Auth Application Builder - Mobile App Automation")
        print("=" * 60)
        
        # Check if we're in the right directory
        if not Path('main_browser_auth.py').exists():
            print("❌ main_browser_auth.py not found. Please run from secure_distribution_app directory.")
            sys.exit(1)
        
        # Check Python and tkinter
        print(f"🐍 Python: {sys.version}")
        print(f"📍 Python path: {sys.executable}")
        
        if not check_tkinter():
            print("❌ tkinter is not available in this Python installation.")
            print("💡 Try using system Python: /usr/bin/python3")
            sys.exit(1)
        
        logger.info("Building browser auth executable with PyInstaller...")
        
        # Build executable
        if not build_executable():
            print("❌ Build failed!")
            sys.exit(1)
        
        print("\n✅ Build completed successfully!")
        
        # Test executable
        logger.info("Testing executable...")
        if not test_executable():
            print("⚠️ Executable test failed, but build completed")
        
        # Show results
        print(f"\n📁 Output location:")
        print(f"   macOS App: dist/MobileAppAutomationBrowser.app")
        print(f"   Executable: dist/MobileAppAutomationBrowser")
        
        print(f"\n🧪 Testing:")
        print(f"   Run: open dist/MobileAppAutomationBrowser.app")
        print(f"   Or:  ./dist/MobileAppAutomationBrowser")
        
        print(f"\n📋 Next steps:")
        print("1. Test the browser-based authentication")
        print("2. Verify single window operation (no duplicates)")
        print("3. Test download interface functionality")
        print("4. Verify system tray integration")
        
    except KeyboardInterrupt:
        print("\n❌ Build cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Build script error: {e}")
        print(f"❌ Build script failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
