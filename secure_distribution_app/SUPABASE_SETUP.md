# Supabase Authentication Setup Guide

This guide will help you set up Supabase database authentication for the Mobile App Automation platform.

## 🔧 **Prerequisites**

1. **Supabase Account**: Create a free account at [supabase.com](https://supabase.com)
2. **Supabase Project**: Create a new project in your Supabase dashboard
3. **Environment Variables**: Your `.env` file should contain:
   ```
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   ```

## 📊 **Step 1: Create Database Schema**

1. **Open Supabase SQL Editor**:
   - Go to your Supabase project dashboard
   - Navigate to "SQL Editor" in the left sidebar
   - Click "New Query"

2. **Run the Schema Script**:
   - Copy the contents of `database/supabase_schema.sql`
   - Paste into the SQL editor
   - Click "Run" to execute

3. **Verify Tables Created**:
   - Go to "Table Editor" in the left sidebar
   - You should see a `user_profiles` table

## 👤 **Step 2: Create Test User**

### Option A: Using Supabase Dashboard (Recommended)

1. **Go to Authentication**:
   - Navigate to "Authentication" → "Users" in your Supabase dashboard
   - Click "Add User"

2. **Create Test User**:
   - Email: `<EMAIL>`
   - Password: `test123`
   - Email Confirm: ✅ (check this box)
   - Click "Create User"

3. **Verify User Created**:
   - The user should appear in the users list
   - Note the User ID (UUID)

### Option B: Using the Script (Alternative)

```bash
cd secure_distribution_app
/usr/bin/python3 scripts/create_test_user.py
```

## 🔐 **Step 3: Configure Row Level Security (RLS)**

The schema script automatically sets up RLS policies, but verify they're active:

1. **Check RLS Status**:
   - Go to "Table Editor" → `user_profiles`
   - Click on the table settings
   - Ensure "Enable RLS" is checked

2. **Verify Policies**:
   - Go to "Authentication" → "Policies"
   - You should see policies for `user_profiles` table

## 🧪 **Step 4: Test the Integration**

1. **Start the Application**:
   ```bash
   cd secure_distribution_app
   /usr/bin/python3 main_browser_auth.py
   ```

2. **Access Login Page**:
   - Browser should open to: http://localhost:8080
   - Click "Login to Dashboard"

3. **Test Authentication**:
   - Email: `<EMAIL>`
   - Password: `test123`
   - Click "Login"

4. **Verify Success**:
   - Should redirect to dashboard
   - User profile should be created automatically

## 🔍 **Step 5: Verify Database Integration**

1. **Check User Profile Creation**:
   ```sql
   SELECT * FROM user_profiles WHERE user_id = (
     SELECT id FROM auth.users WHERE email = '<EMAIL>'
   );
   ```

2. **Check Device Fingerprint**:
   - The `device_fingerprint` field should contain a hardware fingerprint
   - The `metadata` field should contain login information

## 🛠️ **Troubleshooting**

### Common Issues:

#### 1. **"Database error saving new user"**
- **Cause**: `user_profiles` table doesn't exist
- **Solution**: Run the schema script in Supabase SQL Editor

#### 2. **"Invalid email or password"**
- **Cause**: User doesn't exist or wrong credentials
- **Solution**: Create user in Supabase dashboard or check credentials

#### 3. **"Session validation failed"**
- **Cause**: Supabase credentials incorrect
- **Solution**: Check `.env` file has correct SUPABASE_URL and SUPABASE_ANON_KEY

#### 4. **"Failed to initialize Supabase client"**
- **Cause**: Missing or invalid environment variables
- **Solution**: Verify `.env` file exists and contains valid Supabase credentials

### Debug Commands:

```bash
# Test Supabase connection
/usr/bin/python3 -c "
from supabase import create_client
import os
# Load .env manually
with open('.env') as f:
    for line in f:
        if '=' in line:
            key, value = line.strip().split('=', 1)
            os.environ[key] = value
supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_ANON_KEY'))
print('✅ Supabase connection successful')
"

# Test license manager
/usr/bin/python3 -c "
import sys
sys.path.append('.')
from auth.license_manager import LicenseManager
from supabase import create_client
import os
# Load environment and test
print('Testing license manager...')
"
```

## 📋 **Features Implemented**

### ✅ **Authentication Features**:
- Supabase Auth integration
- Custom login/register forms
- Session management with tokens
- Automatic user profile creation
- Device fingerprinting
- License number tracking

### ✅ **Security Features**:
- Row Level Security (RLS)
- Hardware fingerprinting
- Session validation
- Secure token handling

### ✅ **Database Features**:
- User profiles table
- Automatic triggers
- Metadata storage
- License management

## 🚀 **Next Steps**

1. **Create Additional Users**: Add more test users through Supabase dashboard
2. **Customize Profiles**: Add additional fields to `user_profiles` table
3. **Implement License Validation**: Add license expiry and validation logic
4. **Add User Management**: Create admin interface for user management

## 📞 **Support**

If you encounter issues:

1. Check Supabase project status
2. Verify environment variables
3. Review Supabase logs in dashboard
4. Check Flask server logs for detailed error messages

The authentication system is now fully integrated with Supabase and ready for production use!
