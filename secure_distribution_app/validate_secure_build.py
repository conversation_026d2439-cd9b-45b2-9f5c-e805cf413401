#!/usr/bin/env python3
"""
Secure Build Validation Script

This script validates the updated secure build implementation by checking
all components, dependencies, and functionality.
"""

import os
import sys
import subprocess
import socket
import logging
from pathlib import Path
import importlib.util

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecureBuildValidator:
    """Validates the secure build implementation"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.secure_app_dir = Path(__file__).parent
        self.validation_results = {}
        
    def print_header(self, title):
        """Print a formatted header"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def print_result(self, test_name, passed, details=""):
        """Print test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        self.validation_results[test_name] = passed
        return passed
    
    def check_file_exists(self, file_path, description):
        """Check if a file exists"""
        exists = file_path.exists()
        self.print_result(f"File exists: {description}", exists, str(file_path))
        return exists
    
    def check_python_module(self, module_name):
        """Check if a Python module can be imported"""
        try:
            __import__(module_name)
            self.print_result(f"Python module: {module_name}", True)
            return True
        except ImportError as e:
            self.print_result(f"Python module: {module_name}", False, str(e))
            return False
    
    def check_port_available(self, port):
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                self.print_result(f"Port {port} available", True)
                return True
        except OSError:
            self.print_result(f"Port {port} available", False, "Port in use")
            return False
    
    def validate_file_structure(self):
        """Validate the file structure"""
        self.print_header("FILE STRUCTURE VALIDATION")
        
        required_files = [
            (self.secure_app_dir / "main_web_secure.py", "Main web entry point"),
            (self.secure_app_dir / "web_server" / "app.py", "Web server application"),
            (self.secure_app_dir / "start.bat", "Windows launcher"),
            (self.secure_app_dir / "start.sh", "Unix/macOS launcher"),
            (self.secure_app_dir / "build_final_secure_app.py", "Build script"),
            (self.project_root / "templates" / "index.html", "Main template"),
            (self.project_root / "templates" / "login.html", "Login template"),
            (self.project_root / "templates" / "dashboard.html", "Dashboard template"),
            (self.project_root / "utils" / "port_manager.py", "Port manager"),
            (self.project_root / "run.py", "iOS app entry point"),
            (self.project_root / "run_android.py", "Android app entry point"),
        ]
        
        all_files_exist = True
        for file_path, description in required_files:
            if not self.check_file_exists(file_path, description):
                all_files_exist = False
        
        return all_files_exist
    
    def validate_dependencies(self):
        """Validate Python dependencies"""
        self.print_header("DEPENDENCY VALIDATION")
        
        required_modules = [
            'flask',
            'supabase',
            'pathlib',
            'socket',
            'subprocess',
            'threading',
            'webbrowser',
            'logging',
            'json',
            'datetime',
            'secrets'
        ]
        
        optional_modules = [
            'PyInstaller',
            'pyarmor'
        ]
        
        all_required = True
        for module in required_modules:
            if not self.check_python_module(module):
                all_required = False
        
        print(f"\nOptional modules:")
        for module in optional_modules:
            self.check_python_module(module)
        
        return all_required
    
    def validate_port_manager_integration(self):
        """Validate port manager integration"""
        self.print_header("PORT MANAGER INTEGRATION")
        
        try:
            # Add project root to path
            sys.path.insert(0, str(self.project_root))
            
            # Try to import port manager
            from utils.port_manager import get_port_manager, ensure_platform_ports
            
            self.print_result("Port manager import", True)
            
            # Test port manager functionality
            try:
                pm = get_port_manager()
                self.print_result("Port manager instantiation", True)
                
                # Test port availability check
                available_port = pm.find_available_port('flask', 8080)
                if available_port:
                    self.print_result("Port allocation test", True, f"Found port: {available_port}")
                else:
                    self.print_result("Port allocation test", False, "No available ports found")
                
                return True
                
            except Exception as e:
                self.print_result("Port manager functionality", False, str(e))
                return False
                
        except ImportError as e:
            self.print_result("Port manager import", False, str(e))
            return False
    
    def validate_ai_integration(self):
        """Validate AI integration"""
        self.print_header("AI INTEGRATION VALIDATION")

        try:
            # Check if AI modules exist
            ai_modules = [
                self.project_root / "utils" / "ai_integration" / "__init__.py",
                self.project_root / "utils" / "ai_integration" / "ai_healing_orchestrator.py",
                self.project_root / "utils" / "ai_integration" / "together_ai_client.py",
                self.project_root / "utils" / "ai_integration" / "failure_context_capture.py",
            ]

            ai_components_present = False
            for module_path in ai_modules:
                if module_path.exists():
                    ai_components_present = True
                    self.print_result(f"AI module: {module_path.name}", True, str(module_path))
                else:
                    # Treat missing AI modules as optional for secure builds
                    self.print_result(
                        f"AI module optional: {module_path.name}",
                        True,
                        "Not present - skipping AI integration checks"
                    )

            if not ai_components_present:
                # Nothing to validate, treat as optional success
                self.print_result(
                    "AI integration optional",
                    True,
                    "AI modules not included in this build"
                )
                return True

            # Try to import AI modules only when present
            try:
                sys.path.insert(0, str(self.project_root))
                from utils.ai_integration import ai_healing_orchestrator  # noqa: F401
                self.print_result("AI module import", True)
                return True
            except ImportError as e:
                self.print_result("AI module import", False, str(e))
                return False

        except Exception as e:
            self.print_result("AI integration check", False, str(e))
            return False
    
    def validate_launcher_scripts(self):
        """Validate launcher scripts"""
        self.print_header("LAUNCHER SCRIPT VALIDATION")
        
        # Check Windows launcher
        start_bat = self.secure_app_dir / "start.bat"
        bat_valid = self.check_file_exists(start_bat, "Windows launcher (start.bat)")
        
        # Check Unix launcher
        start_sh = self.secure_app_dir / "start.sh"
        sh_valid = self.check_file_exists(start_sh, "Unix/macOS launcher (start.sh)")
        
        # Check if shell script is executable (Unix only)
        if sh_valid and os.name != 'nt':
            is_executable = os.access(start_sh, os.X_OK)
            self.print_result("Shell script executable", is_executable)
            sh_valid = sh_valid and is_executable
        
        return bat_valid and sh_valid
    
    def validate_build_system(self):
        """Validate build system"""
        self.print_header("BUILD SYSTEM VALIDATION")
        
        build_script = self.secure_app_dir / "build_final_secure_app.py"
        if not self.check_file_exists(build_script, "Build script"):
            return False
        
        # Check if build script can be imported
        try:
            spec = importlib.util.spec_from_file_location("build_script", build_script)
            build_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(build_module)
            
            # Check if main class exists
            if hasattr(build_module, 'FinalSecureAppBuilder'):
                self.print_result("Build script structure", True)
                return True
            else:
                self.print_result("Build script structure", False, "FinalSecureAppBuilder class not found")
                return False
                
        except Exception as e:
            self.print_result("Build script validation", False, str(e))
            return False
    
    def validate_web_server_config(self):
        """Validate web server configuration"""
        self.print_header("WEB SERVER CONFIGURATION")
        
        web_app = self.secure_app_dir / "web_server" / "app.py"
        if not self.check_file_exists(web_app, "Web server app"):
            return False
        
        # Check for required routes and functions
        try:
            with open(web_app, 'r') as f:
                content = f.read()
            
            required_elements = [
                "@app.route('/')",
                "@app.route('/login')",
                "@app.route('/dashboard')",
                "@app.route('/app_android')",
                "@app.route('/app_ios')",
                "def app_android",
                "def app_ios",
                "port_manager",
                "dynamic",
            ]
            
            all_present = True
            for element in required_elements:
                if element in content:
                    self.print_result(f"Web server element: {element}", True)
                else:
                    self.print_result(f"Web server element: {element}", False)
                    all_present = False
            
            return all_present
            
        except Exception as e:
            self.print_result("Web server content validation", False, str(e))
            return False
    
    def run_validation(self):
        """Run complete validation"""
        print("🔍 SECURE BUILD VALIDATION STARTING...")
        
        validation_tests = [
            ("File Structure", self.validate_file_structure),
            ("Dependencies", self.validate_dependencies),
            ("Port Manager Integration", self.validate_port_manager_integration),
            ("AI Integration", self.validate_ai_integration),
            ("Launcher Scripts", self.validate_launcher_scripts),
            ("Build System", self.validate_build_system),
            ("Web Server Configuration", self.validate_web_server_config),
        ]
        
        passed_tests = 0
        total_tests = len(validation_tests)
        
        for test_name, test_func in validation_tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                logger.error(f"Validation test '{test_name}' failed with exception: {e}")
        
        # Print summary
        self.print_header("VALIDATION SUMMARY")
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if passed_tests == total_tests:
            print("🎉 ALL VALIDATIONS PASSED!")
            print("✅ Secure build implementation is ready for deployment")
            return True
        else:
            print("⚠️ SOME VALIDATIONS FAILED!")
            print("❌ Please address the failed tests before deployment")
            
            # Show failed tests
            failed_tests = [name for name, result in self.validation_results.items() if not result]
            if failed_tests:
                print(f"\nFailed tests:")
                for test in failed_tests:
                    print(f"  - {test}")
            
            return False

def main():
    """Main entry point"""
    try:
        validator = SecureBuildValidator()
        success = validator.run_validation()
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"Validation failed with critical error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
