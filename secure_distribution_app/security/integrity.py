"""
Integrity Checker for Secure Distribution Application

Provides application integrity verification and anti-tampering protection.
"""

import os
import sys
import hashlib
import logging
import json
from pathlib import Path
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class IntegrityChecker:
    """Handles application integrity verification"""
    
    def __init__(self):
        self.app_root = Path(__file__).parent.parent
        self.integrity_file = self.app_root / '.integrity'
        self.critical_files = [
            'main.py',
            'gui/login_window.py',
            'gui/download_interface.py',
            'gui/platform_selector.py',
            'gui/system_tray.py',
            'auth/session_manager.py',
            'auth/license_manager.py',
            'security/integrity.py',
            'security/obfuscation.py',
            'downloader/hidden_link_downloader.py',
            'launcher/app_launcher.py'
        ]
    
    def calculate_file_hash(self, file_path: Path) -> Optional[str]:
        """Calculate SHA-256 hash of a file"""
        try:
            if not file_path.exists():
                return None
            
            sha256_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            
            return sha256_hash.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to calculate hash for {file_path}: {e}")
            return None
    
    def generate_integrity_manifest(self) -> Dict[str, str]:
        """Generate integrity manifest for critical files"""
        manifest = {}

        for file_path in self.critical_files:
            full_path = self.app_root / file_path

            # Check if file exists before trying to hash it
            if not full_path.exists():
                logger.warning(f"Skipping missing file: {file_path}")
                continue

            file_hash = self.calculate_file_hash(full_path)

            if file_hash:
                manifest[file_path] = file_hash
            else:
                logger.warning(f"Could not generate hash for {file_path}")

        return manifest
    
    def save_integrity_manifest(self, manifest: Dict[str, str]):
        """Save integrity manifest to file"""
        try:
            with open(self.integrity_file, 'w') as f:
                json.dump(manifest, f, indent=2)
            
            logger.info("Integrity manifest saved")
            
        except Exception as e:
            logger.error(f"Failed to save integrity manifest: {e}")
    
    def load_integrity_manifest(self) -> Optional[Dict[str, str]]:
        """Load integrity manifest from file"""
        try:
            if not self.integrity_file.exists():
                logger.warning("Integrity manifest not found")
                return None
            
            with open(self.integrity_file, 'r') as f:
                manifest = json.load(f)
            
            return manifest
            
        except Exception as e:
            logger.error(f"Failed to load integrity manifest: {e}")
            return None
    
    def verify_file_integrity(self, file_path: str, expected_hash: str) -> bool:
        """Verify integrity of a single file"""
        full_path = self.app_root / file_path
        current_hash = self.calculate_file_hash(full_path)
        
        if current_hash is None:
            logger.error(f"File not found or unreadable: {file_path}")
            return False
        
        if current_hash != expected_hash:
            logger.error(f"Integrity check failed for {file_path}")
            logger.error(f"Expected: {expected_hash}")
            logger.error(f"Current:  {current_hash}")
            return False
        
        return True
    
    def verify_application_integrity(self) -> bool:
        """Verify integrity of the entire application"""
        try:
            # Load expected manifest
            expected_manifest = self.load_integrity_manifest()
            
            if expected_manifest is None:
                # If no manifest exists, generate one for future use
                logger.warning("No integrity manifest found, generating new one")
                current_manifest = self.generate_integrity_manifest()
                self.save_integrity_manifest(current_manifest)
                return True  # First run, assume integrity is good
            
            # Verify each file in the manifest
            failed_files = []
            
            for file_path, expected_hash in expected_manifest.items():
                if not self.verify_file_integrity(file_path, expected_hash):
                    failed_files.append(file_path)
            
            if failed_files:
                logger.error(f"Integrity verification failed for files: {failed_files}")
                return False
            
            logger.info("Application integrity verification passed")
            return True
            
        except Exception as e:
            logger.error(f"Integrity verification error: {e}")
            return False
    
    def detect_debugger(self) -> bool:
        """Detect if a debugger is attached"""
        try:
            # Check for common debugger indicators
            debugger_indicators = [
                # Python debugger modules
                'pdb',
                'bdb',
                'pydevd',
                'debugpy',
                # IDE debuggers
                'pydev',
                'pycharm'
            ]
            
            for module_name in debugger_indicators:
                if module_name in sys.modules:
                    logger.warning(f"Debugger detected: {module_name}")
                    return True
            
            # Check for trace function
            if sys.gettrace() is not None:
                logger.warning("Trace function detected")
                return True
            
            # Check for profiling
            if sys.getprofile() is not None:
                logger.warning("Profile function detected")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Debugger detection error: {e}")
            return False
    
    def detect_virtual_machine(self) -> bool:
        """Detect if running in a virtual machine"""
        try:
            vm_indicators = []
            
            # Check system information for VM indicators
            import platform
            system_info = platform.platform().lower()
            
            vm_signatures = [
                'vmware', 'virtualbox', 'vbox', 'qemu', 'kvm',
                'xen', 'hyper-v', 'parallels', 'bochs'
            ]
            
            for signature in vm_signatures:
                if signature in system_info:
                    vm_indicators.append(f"System info: {signature}")
            
            # Check processor information
            try:
                processor = platform.processor().lower()
                for signature in vm_signatures:
                    if signature in processor:
                        vm_indicators.append(f"Processor: {signature}")
            except:
                pass
            
            # Check for VM-specific files (Linux/Windows)
            vm_files = [
                '/proc/scsi/scsi',  # Linux VM detection
                '/sys/class/dmi/id/product_name',  # Linux DMI info
                'C:\\Windows\\System32\\drivers\\vmmouse.sys',  # VMware
                'C:\\Windows\\System32\\drivers\\vmhgfs.sys',   # VMware
                'C:\\Windows\\System32\\drivers\\VBoxMouse.sys', # VirtualBox
            ]
            
            for vm_file in vm_files:
                if os.path.exists(vm_file):
                    try:
                        with open(vm_file, 'r', errors='ignore') as f:
                            content = f.read().lower()
                            for signature in vm_signatures:
                                if signature in content:
                                    vm_indicators.append(f"File content: {vm_file}")
                                    break
                    except:
                        pass
            
            if vm_indicators:
                logger.warning(f"Virtual machine detected: {vm_indicators}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"VM detection error: {e}")
            return False
    
    def perform_security_checks(self) -> Dict[str, bool]:
        """Perform comprehensive security checks"""
        results = {
            'integrity': self.verify_application_integrity(),
            'debugger': not self.detect_debugger(),  # True if no debugger
            'vm': not self.detect_virtual_machine()   # True if not in VM
        }
        
        return results
    
    def is_environment_secure(self) -> bool:
        """Check if the current environment is secure"""
        security_checks = self.perform_security_checks()
        
        # Log security check results
        for check, passed in security_checks.items():
            status = "PASSED" if passed else "FAILED"
            logger.info(f"Security check {check}: {status}")
        
        # For development, we might want to be less strict
        # In production, all checks should pass
        development_mode = os.environ.get('DEVELOPMENT_MODE', 'false').lower() == 'true'
        
        if development_mode:
            # In development mode, only require integrity check
            return security_checks['integrity']
        else:
            # In production mode, require all checks to pass
            return all(security_checks.values())

def create_integrity_manifest():
    """Utility function to create integrity manifest for distribution"""
    checker = IntegrityChecker()
    manifest = checker.generate_integrity_manifest()
    checker.save_integrity_manifest(manifest)
    print("Integrity manifest created successfully")
    return manifest

if __name__ == "__main__":
    # Allow running this module directly to create integrity manifest
    create_integrity_manifest()
