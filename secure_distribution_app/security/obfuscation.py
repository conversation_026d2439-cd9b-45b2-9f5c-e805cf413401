"""
Runtime Protection and Obfuscation for Secure Distribution Application

Provides runtime protection against reverse engineering and tampering.
"""

import os
import sys
import time
import random
import threading
import logging
import ctypes
from typing import Optional, Callable
import hashlib
import base64

logger = logging.getLogger(__name__)

class RuntimeProtection:
    """Provides runtime protection against reverse engineering"""
    
    def __init__(self):
        self.protection_active = False
        self.monitoring_thread = None
        self.stop_monitoring = False
        self.anti_debug_checks = []
        self.integrity_callbacks = []
        
        # Obfuscated strings (in real implementation, these would be more heavily obfuscated)
        self._obfuscated_strings = {
            'debug_detected': self._decode_string('ZGVidWdnZXIgZGV0ZWN0ZWQ='),
            'tamper_detected': self._decode_string('dGFtcGVyaW5nIGRldGVjdGVk'),
            'vm_detected': self._decode_string('dmlydHVhbCBtYWNoaW5lIGRldGVjdGVk')
        }
    
    def _decode_string(self, encoded: str) -> str:
        """Decode obfuscated string"""
        try:
            return base64.b64decode(encoded).decode('utf-8')
        except:
            return "unknown"
    
    def _encode_string(self, text: str) -> str:
        """Encode string for obfuscation"""
        return base64.b64encode(text.encode('utf-8')).decode('utf-8')
    
    def initialize(self) -> bool:
        """Initialize runtime protection"""
        try:
            logger.info("Initializing runtime protection...")
            
            # Register anti-debugging checks
            self._register_anti_debug_checks()
            
            # Start monitoring thread
            self._start_monitoring()
            
            self.protection_active = True
            logger.info("Runtime protection initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Runtime protection initialization failed: {e}")
            return False
    
    def _register_anti_debug_checks(self):
        """Register various anti-debugging checks"""
        self.anti_debug_checks = [
            self._check_trace_function,
            self._check_debugger_modules,
            self._check_timing_attack,
            self._check_memory_patterns
        ]
    
    def _check_trace_function(self) -> bool:
        """Check for trace function (debugger indicator)"""
        try:
            return sys.gettrace() is None and sys.getprofile() is None
        except:
            return False
    
    def _check_debugger_modules(self) -> bool:
        """Check for loaded debugger modules"""
        try:
            dangerous_modules = [
                'pdb', 'bdb', 'pydevd', 'debugpy', 'pydev',
                'pycharm', 'winpdb', 'rpdb', 'pudb'
            ]
            
            for module in dangerous_modules:
                if module in sys.modules:
                    return False
            
            return True
        except:
            return False
    
    def _check_timing_attack(self) -> bool:
        """Check for timing-based debugging detection"""
        try:
            start_time = time.perf_counter()
            
            # Perform some operations
            dummy_operations = 0
            for i in range(1000):
                dummy_operations += i * 2
            
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            
            # If execution took too long, might be debugged
            return execution_time < 0.1  # Adjust threshold as needed
            
        except:
            return False
    
    def _check_memory_patterns(self) -> bool:
        """Check for suspicious memory patterns"""
        try:
            # Simple check for memory debugging tools
            import gc
            
            # Get garbage collection stats
            gc_stats = gc.get_stats()
            
            # Check for unusual object counts (might indicate memory debugging)
            total_objects = sum(stat['collections'] for stat in gc_stats)
            
            # This is a simple heuristic - in practice, you'd want more sophisticated checks
            return total_objects < 10000  # Adjust threshold as needed
            
        except:
            return True  # If we can't check, assume it's safe
    
    def _perform_anti_debug_checks(self) -> bool:
        """Perform all anti-debugging checks"""
        try:
            for check in self.anti_debug_checks:
                if not check():
                    logger.warning(f"Anti-debug check failed: {check.__name__}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Anti-debug check error: {e}")
            return False
    
    def _start_monitoring(self):
        """Start the monitoring thread"""
        try:
            self.stop_monitoring = False
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
        except Exception as e:
            logger.error(f"Failed to start monitoring thread: {e}")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while not self.stop_monitoring:
            try:
                # Perform anti-debugging checks
                if not self._perform_anti_debug_checks():
                    self._handle_security_violation("debug")
                
                # Random delay to make timing attacks harder
                delay = random.uniform(1.0, 3.0)
                time.sleep(delay)
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                time.sleep(1.0)
    
    def _handle_security_violation(self, violation_type: str):
        """Handle detected security violations"""
        try:
            logger.critical(f"Security violation detected: {violation_type}")
            
            # In a real implementation, you might want to:
            # 1. Encrypt and send violation report to server
            # 2. Gradually degrade functionality
            # 3. Clear sensitive data from memory
            # 4. Exit gracefully
            
            # For now, we'll just log and continue
            # In production, you might want to exit the application
            
            if violation_type == "debug":
                logger.critical(self._obfuscated_strings['debug_detected'])
            elif violation_type == "tamper":
                logger.critical(self._obfuscated_strings['tamper_detected'])
            elif violation_type == "vm":
                logger.critical(self._obfuscated_strings['vm_detected'])
            
            # Call registered callbacks
            for callback in self.integrity_callbacks:
                try:
                    callback(violation_type)
                except Exception as e:
                    logger.error(f"Integrity callback error: {e}")
            
        except Exception as e:
            logger.error(f"Security violation handler error: {e}")
    
    def register_integrity_callback(self, callback: Callable[[str], None]):
        """Register a callback for integrity violations"""
        self.integrity_callbacks.append(callback)
    
    def obfuscate_sensitive_data(self, data: str) -> str:
        """Obfuscate sensitive data in memory"""
        try:
            # Simple XOR obfuscation (in practice, use stronger methods)
            key = random.randint(1, 255)
            obfuscated = ''.join(chr(ord(c) ^ key) for c in data)
            
            # Store the key separately (in practice, derive it from system info)
            return f"{key:02x}{self._encode_string(obfuscated)}"
            
        except Exception as e:
            logger.error(f"Data obfuscation error: {e}")
            return data
    
    def deobfuscate_sensitive_data(self, obfuscated_data: str) -> str:
        """Deobfuscate sensitive data"""
        try:
            if len(obfuscated_data) < 2:
                return obfuscated_data
            
            # Extract key and data
            key = int(obfuscated_data[:2], 16)
            encoded_data = obfuscated_data[2:]
            
            # Decode and deobfuscate
            decoded_data = base64.b64decode(encoded_data).decode('utf-8')
            original = ''.join(chr(ord(c) ^ key) for c in decoded_data)
            
            return original
            
        except Exception as e:
            logger.error(f"Data deobfuscation error: {e}")
            return obfuscated_data
    
    def secure_memory_clear(self, data: str):
        """Securely clear sensitive data from memory"""
        try:
            # Overwrite the string data in memory
            # Note: This is not guaranteed to work in Python due to string immutability
            # In practice, you'd use ctypes or other low-level methods
            
            if isinstance(data, str):
                # Convert to bytes and overwrite
                data_bytes = data.encode('utf-8')
                for i in range(len(data_bytes)):
                    data_bytes[i] = 0
            
        except Exception as e:
            logger.error(f"Memory clear error: {e}")
    
    def generate_runtime_key(self, seed: str) -> str:
        """Generate a runtime key based on system characteristics"""
        try:
            import platform
            
            # Combine system information with seed
            system_data = f"{platform.machine()}{platform.processor()}{seed}"
            
            # Generate hash
            key_hash = hashlib.sha256(system_data.encode()).hexdigest()
            
            return key_hash[:32]  # Return first 32 characters
            
        except Exception as e:
            logger.error(f"Runtime key generation error: {e}")
            return hashlib.sha256(seed.encode()).hexdigest()[:32]
    
    def cleanup(self):
        """Clean up runtime protection"""
        try:
            logger.info("Cleaning up runtime protection...")
            
            # Stop monitoring
            self.stop_monitoring = True
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=2.0)
            
            # Clear sensitive data
            self._obfuscated_strings.clear()
            self.integrity_callbacks.clear()
            
            self.protection_active = False
            logger.info("Runtime protection cleanup complete")
            
        except Exception as e:
            logger.error(f"Runtime protection cleanup error: {e}")
    
    def is_active(self) -> bool:
        """Check if runtime protection is active"""
        return self.protection_active

# Decorator for protecting sensitive functions
def protected_function(func):
    """Decorator to add runtime protection to sensitive functions"""
    def wrapper(*args, **kwargs):
        # Perform quick security check before executing
        protection = RuntimeProtection()
        if not protection._perform_anti_debug_checks():
            logger.warning(f"Security check failed for function: {func.__name__}")
            return None
        
        return func(*args, **kwargs)
    
    return wrapper

# Context manager for protected code blocks
class ProtectedContext:
    """Context manager for protecting code blocks"""
    
    def __init__(self):
        self.protection = RuntimeProtection()
    
    def __enter__(self):
        if not self.protection._perform_anti_debug_checks():
            raise RuntimeError("Security violation detected")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Perform cleanup if needed
        pass
