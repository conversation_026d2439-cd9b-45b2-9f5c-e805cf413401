#!/usr/bin/env python3
"""
Mobile App Automation - Browser-Based Authentication

New main application that uses browser-based authentication instead of tkinter GUI.
Eliminates duplicate window issues and provides better user experience.
"""

import os
import sys
import time
import logging
import threading
import webbrowser
from pathlib import Path
from typing import Optional, Dict, Any

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
def load_environment():
    """Load environment variables from .env file"""
    try:
        # Check if running as PyInstaller bundle
        if getattr(sys, 'frozen', False):
            # Running as PyInstaller bundle
            bundle_dir = Path(sys._MEIPASS)
            env_file = bundle_dir / '.env'
            print(f"🔍 Running as PyInstaller bundle, checking: {bundle_dir}")
            print(f"📁 Loading environment from: {env_file}")
        else:
            # Running as script
            env_file = current_dir / '.env'
            print(f"📁 Loading environment from: {env_file}")
        
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            
            print("✅ Environment variables loaded successfully")
            
            # Verify critical variables
            supabase_url = os.getenv('SUPABASE_URL', '')
            supabase_key = os.getenv('SUPABASE_ANON_KEY', '')
            
            if supabase_url and supabase_key:
                print(f"✅ SUPABASE_URL: {supabase_url[:30]}...")
                print(f"✅ SUPABASE_ANON_KEY: {supabase_key[:30]}...")
                return True
            else:
                print("❌ Missing Supabase credentials in environment")
                return False
        else:
            print(f"❌ Environment file not found: {env_file}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment variables. Exiting.")
    sys.exit(1)

# Import application modules after environment is loaded
try:
    from auth.browser_auth import BrowserAuthManager
    from auth.session_manager import SessionManager
    from gui.system_tray import SystemTrayManager, is_system_tray_available
    from gui.download_gui import DownloadGUI
    from security.integrity import IntegrityChecker
    from security.obfuscation import RuntimeProtection
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    print(f"❌ Import error: {e}")
    sys.exit(1)

class BrowserAuthApp:
    """Main application with browser-based authentication"""
    
    def __init__(self):
        self.is_running = False
        self.is_authenticated = False
        self.user_data = None
        
        # Components
        self.browser_auth = None
        self.session_manager = None
        self.system_tray = None
        self.download_gui = None
        self.integrity_checker = None
        self.runtime_protection = None
        
        # Configuration
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        logger.info("BrowserAuthApp initialized")
    
    def initialize_security(self) -> bool:
        """Initialize security components"""
        try:
            logger.info("Initializing security components...")
            
            # Initialize integrity checker
            self.integrity_checker = IntegrityChecker()
            
            # Initialize runtime protection
            self.runtime_protection = RuntimeProtection()
            
            logger.info("Security components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Security initialization failed: {e}")
            return False
    
    def initialize_browser_auth(self) -> bool:
        """Initialize browser-based authentication"""
        try:
            logger.info("Initializing browser authentication with Flask server...")

            self.browser_auth = BrowserAuthManager(
                host='localhost',
                port=8080
            )

            logger.info("Browser authentication initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Browser auth initialization failed: {e}")
            return False
    
    def initialize_session_manager(self) -> bool:
        """Initialize session manager"""
        try:
            logger.info("Initializing session manager...")
            
            self.session_manager = SessionManager()
            
            logger.info("Session manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Session manager initialization failed: {e}")
            return False
    
    def initialize_system_tray(self) -> bool:
        """Initialize system tray"""
        try:
            if is_system_tray_available():
                logger.info("Initializing system tray...")
                
                self.system_tray = SystemTrayManager(
                    session_manager=self.session_manager,
                    app_launcher=None,
                    on_show_callback=self.show_download_interface,
                    on_exit_callback=self.shutdown,
                    parent_root=None  # No tkinter root needed
                )
                
                logger.info("System tray initialized successfully")
            else:
                logger.warning("System tray not available")
            
            return True
            
        except Exception as e:
            logger.error(f"System tray initialization failed: {e}")
            return False
    
    def start_authentication(self) -> bool:
        """Start the browser-based authentication flow"""
        try:
            logger.info("Starting authentication flow...")
            
            print("\n🌐 Starting web server for authentication...")
            print("📋 Please complete the authentication in your browser.")
            print("🔄 The web server will handle login and dashboard access.")

            # Start auth flow (Flask server)
            success = self.browser_auth.start_auth_flow(
                on_success=self.on_auth_success,
                on_error=self.on_auth_error
            )

            if not success:
                logger.error("Failed to start authentication flow")
                return False

            # For Flask server mode, we don't wait for authentication completion
            # The server handles everything through the web interface
            logger.info("Web server started successfully")
            return True
                
        except Exception as e:
            logger.error(f"Authentication flow failed: {e}")
            return False
    
    def on_auth_success(self, user_data: Dict[str, Any]):
        """Handle successful authentication"""
        self.is_authenticated = True
        self.user_data = user_data
        
        logger.info("Authentication successful!")
        print("✅ Authentication successful!")
        print("🎉 Welcome to Mobile App Automation!")
        
        # Show download interface
        self.show_download_interface()
    
    def on_auth_error(self, error: str):
        """Handle authentication error"""
        self.is_authenticated = False
        self.user_data = None
        
        logger.error(f"Authentication failed: {error}")
        print(f"❌ Authentication failed: {error}")
        print("💡 Please try again or check your credentials.")
    
    def show_download_interface(self):
        """Show the download interface GUI"""
        try:
            if self.download_gui:
                # GUI already exists, just show it
                self.download_gui.show()
            else:
                # Create new download GUI
                logger.info("Creating download interface GUI...")
                self.download_gui = DownloadGUI(
                    user_data=self.user_data or {},
                    on_close_callback=self.on_download_gui_close
                )

                # Show the GUI
                self.download_gui.show()
                logger.info("Download interface GUI created and shown")
                print("🖥️ Download interface opened")

        except Exception as e:
            logger.error(f"Failed to show download interface: {e}")
            print(f"❌ Failed to open download interface: {e}")

    def on_download_gui_close(self):
        """Handle download GUI close"""
        try:
            logger.info("Download GUI closed")
            self.download_gui = None

        except Exception as e:
            logger.error(f"Error handling download GUI close: {e}")
    
    def run_background_loop(self):
        """Run the main application loop in background"""
        try:
            logger.info("Starting background application loop...")
            
            while self.is_running:
                # Keep application alive
                time.sleep(1)
                
                # Check if we need to handle any background tasks
                if self.is_authenticated and self.user_data:
                    # Application is running and authenticated
                    pass
            
            logger.info("Background loop ended")
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
            self.shutdown()
        except Exception as e:
            logger.error(f"Background loop error: {e}")
            self.shutdown()
    
    def shutdown(self):
        """Shutdown the application"""
        try:
            logger.info("Shutting down application...")
            
            self.is_running = False
            
            # Cleanup browser auth
            if self.browser_auth:
                self.browser_auth.cleanup()
            
            # Cleanup download GUI
            if self.download_gui:
                self.download_gui.destroy()
                self.download_gui = None

            # Cleanup system tray
            if self.system_tray:
                # System tray cleanup is handled automatically
                pass
            
            logger.info("Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        finally:
            sys.exit(0)
    
    def run(self) -> bool:
        """Main application entry point"""
        try:
            logger.info("Starting Mobile App Automation with Browser Authentication...")
            print("🚀 Mobile App Automation - Browser Authentication")
            print("=" * 50)
            
            # Initialize security components
            if not self.initialize_security():
                return False
            
            # Initialize browser authentication
            if not self.initialize_browser_auth():
                return False
            
            # Initialize session manager
            if not self.initialize_session_manager():
                return False
            
            # Initialize system tray
            if not self.initialize_system_tray():
                return False
            
            # Start authentication flow
            if not self.start_authentication():
                return False
            
            # Start background loop
            self.is_running = True
            print("🔄 Application running in background...")
            print("💡 Access the web interface at: http://localhost:8080")
            print("📱 Use the dashboard to launch iOS/Android automation tools")
            print("🛑 Press Ctrl+C to exit")

            self.run_background_loop()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
            self.shutdown()
            return True
        except Exception as e:
            logger.error(f"Application error: {e}")
            print(f"❌ Application error: {e}")
            self.shutdown()
            return False

def main():
    """Application entry point"""
    try:
        # Create and run the application
        app = BrowserAuthApp()
        success = app.run()
        
        if not success:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Critical application error: {e}")
        print(f"❌ Critical error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
