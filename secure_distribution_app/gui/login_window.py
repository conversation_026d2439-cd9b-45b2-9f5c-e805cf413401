"""
Login Window for Secure Distribution Application

Provides a modern, secure login interface with registration capabilities.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import logging
from typing import Callable, Optional, Dict, Any

logger = logging.getLogger(__name__)

class LoginWindow:
    """Login window with modern UI and secure authentication"""
    
    def __init__(self, parent: tk.Tk, session_manager, 
                 on_login_success: Callable[[Dict[str, Any]], None],
                 on_register_success: Callable[[Dict[str, Any]], None]):
        self.parent = parent
        self.session_manager = session_manager
        self.on_login_success = on_login_success
        self.on_register_success = on_register_success
        
        # Create the login window
        self.window = tk.Toplevel(parent)
        self.window.title("Secure Access - Login")
        self.window.geometry("400x500")
        self.window.resizable(False, False)

        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()

        # Force window to be visible and on top
        self.window.lift()
        self.window.attributes('-topmost', True)
        self.window.after(100, lambda: self.window.attributes('-topmost', False))
        
        # Variables for form data
        self.email_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.license_var = tk.StringVar()
        self.first_name_var = tk.StringVar()
        self.last_name_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()
        
        # UI state
        self.is_register_mode = False
        self.is_processing = False
        
        # Create UI
        self.create_ui()

        # Force window update and make sure it's visible
        self.window.update_idletasks()
        self.window.update()

        # Bind Enter key to login
        self.window.bind('<Return>', lambda e: self.handle_login())

        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)

        # Focus on email field (after UI is created)
        try:
            self.email_entry.focus()
            logger.info("LoginWindow created and focused successfully")
        except Exception as e:
            logger.warning(f"Could not focus on email field: {e}")

        # Final window positioning and visibility
        self.center_window()
        self.window.deiconify()  # Ensure window is visible
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        main_frame = ttk.Frame(self.window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Mobile App Automation", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 5))
        
        subtitle_label = ttk.Label(main_frame, text="Secure Access Portal", 
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(0, 30))
        
        # Form container
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, pady=(0, 5))
        self.email_entry = ttk.Entry(form_frame, textvariable=self.email_var, 
                                    font=("Arial", 10), width=30)
        self.email_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Password field
        ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(form_frame, textvariable=self.password_var,
                                       show="*", font=("Arial", 10), width=30)
        self.password_entry.pack(fill=tk.X, pady=(0, 15))

        # License number field (for login)
        self.license_frame = ttk.Frame(form_frame)
        self.license_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(self.license_frame, text="License Number:").pack(anchor=tk.W, pady=(0, 5))
        self.license_entry = ttk.Entry(self.license_frame, textvariable=self.license_var,
                                      font=("Arial", 10), width=30)
        self.license_entry.pack(fill=tk.X)
        
        # Registration fields (initially hidden)
        self.register_frame = ttk.Frame(form_frame)
        
        # First name
        ttk.Label(self.register_frame, text="First Name:").pack(anchor=tk.W, pady=(0, 5))
        self.first_name_entry = ttk.Entry(self.register_frame, textvariable=self.first_name_var, 
                                         font=("Arial", 10), width=30)
        self.first_name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Last name
        ttk.Label(self.register_frame, text="Last Name:").pack(anchor=tk.W, pady=(0, 5))
        self.last_name_entry = ttk.Entry(self.register_frame, textvariable=self.last_name_var, 
                                        font=("Arial", 10), width=30)
        self.last_name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Confirm password
        ttk.Label(self.register_frame, text="Confirm Password:").pack(anchor=tk.W, pady=(0, 5))
        self.confirm_password_entry = ttk.Entry(self.register_frame, textvariable=self.confirm_password_var, 
                                               show="*", font=("Arial", 10), width=30)
        self.confirm_password_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Primary action button
        self.primary_button = ttk.Button(buttons_frame, text="Login", 
                                        command=self.handle_primary_action,
                                        style="Accent.TButton")
        self.primary_button.pack(fill=tk.X, pady=(0, 10))
        
        # Secondary action button
        self.secondary_button = ttk.Button(buttons_frame, text="Create Account", 
                                          command=self.toggle_mode)
        self.secondary_button.pack(fill=tk.X)
        
        # Progress bar (initially hidden)
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=10)
        self.progress_label = ttk.Label(self.progress_frame, text="Processing...")
        self.progress_label.pack()
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="", foreground="red")
        self.status_label.pack(pady=(10, 0))
    
    def toggle_mode(self):
        """Toggle between login and registration mode"""
        self.is_register_mode = not self.is_register_mode

        if self.is_register_mode:
            # Switch to registration mode
            self.register_frame.pack(fill=tk.X, pady=(0, 15))
            self.license_frame.pack_forget()  # Hide license field for registration
            self.primary_button.config(text="Create Account")
            self.secondary_button.config(text="Back to Login")
            self.window.title("Secure Access - Register")
            self.first_name_entry.focus()
        else:
            # Switch to login mode
            self.register_frame.pack_forget()
            self.license_frame.pack(fill=tk.X, pady=(0, 15))  # Show license field for login
            self.primary_button.config(text="Login")
            self.secondary_button.config(text="Create Account")
            self.window.title("Secure Access - Login")
            self.email_entry.focus()

        # Clear status
        self.status_label.config(text="")
    
    def handle_primary_action(self):
        """Handle the primary action (login or register)"""
        if self.is_register_mode:
            self.handle_register()
        else:
            self.handle_login()
    
    def handle_login(self):
        """Handle login attempt"""
        if self.is_processing:
            return

        # Validate input
        email = self.email_var.get().strip()
        password = self.password_var.get()
        license_number = self.license_var.get().strip()

        if not email or not password:
            self.show_status("Please enter both email and password", "error")
            return

        if not license_number:
            self.show_status("Please enter your license number", "error")
            return

        # Start login process in background thread
        self.start_processing("Authenticating...")

        def login_thread():
            try:
                if not self.session_manager:
                    raise Exception("Session manager not available. Please check your configuration.")

                result = self.session_manager.login(email, password, license_number)

                # Update UI in main thread
                self.window.after(0, lambda: self.handle_login_result(result))

            except Exception as e:
                logger.error(f"Login error: {e}")
                self.window.after(0, lambda: self.handle_login_error(str(e)))

        threading.Thread(target=login_thread, daemon=True).start()
    
    def handle_register(self):
        """Handle registration attempt"""
        if self.is_processing:
            return
        
        # Validate input
        email = self.email_var.get().strip()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        first_name = self.first_name_var.get().strip()
        last_name = self.last_name_var.get().strip()
        
        if not all([email, password, confirm_password, first_name, last_name]):
            self.show_status("Please fill in all fields", "error")
            return
        
        if password != confirm_password:
            self.show_status("Passwords do not match", "error")
            return
        
        if len(password) < 8:
            self.show_status("Password must be at least 8 characters", "error")
            return
        
        # Start registration process in background thread
        self.start_processing("Creating account...")
        
        def register_thread():
            try:
                if not self.session_manager:
                    raise Exception("Session manager not available. Please check your configuration.")

                result = self.session_manager.register(
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    license_number=None  # License assigned separately by admin
                )

                # Update UI in main thread
                self.window.after(0, lambda: self.handle_register_result(result))

            except Exception as e:
                logger.error(f"Registration error: {e}")
                self.window.after(0, lambda: self.handle_register_error(str(e)))
        
        threading.Thread(target=register_thread, daemon=True).start()
    
    def handle_login_result(self, result):
        """Handle login result"""
        self.stop_processing()
        
        if result.get('success'):
            user_data = result.get('user_data', {})
            self.on_login_success(user_data)
        else:
            error_message = result.get('error', 'Login failed')
            self.show_status(error_message, "error")
    
    def handle_login_error(self, error_message):
        """Handle login error"""
        self.stop_processing()
        self.show_status(f"Login failed: {error_message}", "error")
    
    def handle_register_result(self, result):
        """Handle registration result"""
        self.stop_processing()
        
        if result.get('success'):
            user_data = result.get('user_data', {})
            self.on_register_success(user_data)
        else:
            error_message = result.get('error', 'Registration failed')
            self.show_status(error_message, "error")
    
    def handle_register_error(self, error_message):
        """Handle registration error"""
        self.stop_processing()
        self.show_status(f"Registration failed: {error_message}", "error")
    
    def start_processing(self, message):
        """Start processing state"""
        self.is_processing = True
        self.primary_button.config(state="disabled")
        self.secondary_button.config(state="disabled")
        self.progress_frame.pack(fill=tk.X, pady=(20, 0))
        self.progress_bar.start()
        self.progress_label.config(text=message)
        self.status_label.config(text="")
    
    def stop_processing(self):
        """Stop processing state"""
        self.is_processing = False
        self.primary_button.config(state="normal")
        self.secondary_button.config(state="normal")
        self.progress_frame.pack_forget()
        self.progress_bar.stop()
    
    def show_status(self, message, status_type="info"):
        """Show status message"""
        color = "red" if status_type == "error" else "green" if status_type == "success" else "blue"
        self.status_label.config(text=message, foreground=color)
    
    def center_window(self):
        """Center the window on screen"""
        try:
            self.window.update_idletasks()
            width = 400
            height = 500

            # Get screen dimensions
            screen_width = self.window.winfo_screenwidth()
            screen_height = self.window.winfo_screenheight()

            # Calculate position
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2

            # Set window geometry
            self.window.geometry(f"{width}x{height}+{x}+{y}")
            logger.info(f"LoginWindow centered at {x},{y}")

        except Exception as e:
            logger.warning(f"Could not center login window: {e}")

    def on_close(self):
        """Handle window close"""
        if not self.is_processing:
            self.parent.quit()
    
    def destroy(self):
        """Destroy the window"""
        if hasattr(self, 'window') and self.window:
            self.window.destroy()
