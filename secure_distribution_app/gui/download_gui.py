"""
Post-Authentication Download GUI

Simple tkinter interface that appears only after successful browser authentication.
Shows download options for iOS and Android automation tools.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import logging
from typing import Optional, Callable, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class DownloadGUI:
    """Simple post-authentication download interface"""
    
    def __init__(self, user_data: Dict[str, Any], on_close_callback: Optional[Callable] = None):
        self.user_data = user_data
        self.on_close_callback = on_close_callback
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("Mobile App Automation - Download Center")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # Configure window
        self.root.configure(bg='#f8f9fa')
        
        # Center window
        self.center_window()
        
        # Create UI
        self.create_ui()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        logger.info("DownloadGUI created successfully")
    
    def center_window(self):
        """Center the window on screen"""
        try:
            self.root.update_idletasks()
            width = 600
            height = 500
            
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # Calculate position
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            
            # Set window geometry
            self.root.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception as e:
            logger.warning(f"Could not center window: {e}")
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Title
        title_label = ttk.Label(
            header_frame, 
            text="Mobile App Automation", 
            font=("Arial", 20, "bold")
        )
        title_label.pack()
        
        # Subtitle
        subtitle_label = ttk.Label(
            header_frame, 
            text="Download Center", 
            font=("Arial", 12)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # User info
        user_email = self.user_data.get('email', 'Unknown User')
        user_label = ttk.Label(
            header_frame, 
            text=f"Welcome, {user_email}", 
            font=("Arial", 10),
            foreground="gray"
        )
        user_label.pack(pady=(10, 0))
        
        # Download options frame
        download_frame = ttk.Frame(main_frame)
        download_frame.pack(fill=tk.BOTH, expand=True)
        
        # iOS Download Section
        ios_frame = ttk.LabelFrame(download_frame, text="iOS Automation Tools", padding="20")
        ios_frame.pack(fill=tk.X, pady=(0, 20))
        
        # iOS icon and description
        ios_desc_frame = ttk.Frame(ios_frame)
        ios_desc_frame.pack(fill=tk.X, pady=(0, 15))
        
        ios_icon_label = ttk.Label(ios_desc_frame, text="🍎", font=("Arial", 24))
        ios_icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        ios_text_frame = ttk.Frame(ios_desc_frame)
        ios_text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(ios_text_frame, text="iOS Testing Suite", font=("Arial", 14, "bold")).pack(anchor=tk.W)
        ttk.Label(ios_text_frame, text="XCUITest integration, real device testing, simulator support").pack(anchor=tk.W)
        ttk.Label(ios_text_frame, text="Requires: macOS and Xcode", font=("Arial", 9), foreground="gray").pack(anchor=tk.W, pady=(5, 0))
        
        # iOS download button
        ios_btn = ttk.Button(
            ios_frame, 
            text="Download iOS Tools", 
            command=self.download_ios,
            style="Accent.TButton"
        )
        ios_btn.pack(pady=(10, 0))
        
        # Android Download Section
        android_frame = ttk.LabelFrame(download_frame, text="Android Automation Tools", padding="20")
        android_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Android icon and description
        android_desc_frame = ttk.Frame(android_frame)
        android_desc_frame.pack(fill=tk.X, pady=(0, 15))
        
        android_icon_label = ttk.Label(android_desc_frame, text="🤖", font=("Arial", 24))
        android_icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        android_text_frame = ttk.Frame(android_desc_frame)
        android_text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(android_text_frame, text="Android Testing Suite", font=("Arial", 14, "bold")).pack(anchor=tk.W)
        ttk.Label(android_text_frame, text="Appium integration, real device testing, emulator support").pack(anchor=tk.W)
        ttk.Label(android_text_frame, text="Cross-platform compatible", font=("Arial", 9), foreground="gray").pack(anchor=tk.W, pady=(5, 0))
        
        # Android download button
        android_btn = ttk.Button(
            android_frame, 
            text="Download Android Tools", 
            command=self.download_android,
            style="Accent.TButton"
        )
        android_btn.pack(pady=(10, 0))
        
        # Footer
        footer_frame = ttk.Frame(main_frame)
        footer_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Help and close buttons
        button_frame = ttk.Frame(footer_frame)
        button_frame.pack()
        
        help_btn = ttk.Button(button_frame, text="Help & Documentation", command=self.show_help)
        help_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        close_btn = ttk.Button(button_frame, text="Close", command=self.on_close)
        close_btn.pack(side=tk.LEFT)
        
        # Status label
        self.status_label = ttk.Label(footer_frame, text="", font=("Arial", 9))
        self.status_label.pack(pady=(10, 0))
    
    def download_ios(self):
        """Handle iOS download"""
        try:
            self.status_label.config(text="Preparing iOS download...", foreground="blue")
            self.root.update()
            
            # In a real implementation, this would trigger the actual download
            # For now, show a message and open documentation
            
            result = messagebox.showinfo(
                "iOS Download", 
                "iOS automation tools download will begin shortly.\n\n"
                "The download package includes:\n"
                "• XCUITest integration tools\n"
                "• Device management utilities\n"
                "• Test automation frameworks\n"
                "• Documentation and examples\n\n"
                "Would you like to view the installation guide?",
                type=messagebox.OKCANCEL
            )
            
            if result == 'ok':
                # Open documentation
                webbrowser.open("https://developer.apple.com/documentation/xctest")
            
            self.status_label.config(text="iOS download initiated", foreground="green")
            logger.info("iOS download requested")
            
        except Exception as e:
            logger.error(f"iOS download error: {e}")
            self.status_label.config(text="iOS download failed", foreground="red")
            messagebox.showerror("Download Error", f"Failed to start iOS download: {e}")
    
    def download_android(self):
        """Handle Android download"""
        try:
            self.status_label.config(text="Preparing Android download...", foreground="blue")
            self.root.update()
            
            # In a real implementation, this would trigger the actual download
            # For now, show a message and open documentation
            
            result = messagebox.showinfo(
                "Android Download", 
                "Android automation tools download will begin shortly.\n\n"
                "The download package includes:\n"
                "• Appium integration tools\n"
                "• Device management utilities\n"
                "• UI automation frameworks\n"
                "• Documentation and examples\n\n"
                "Would you like to view the installation guide?",
                type=messagebox.OKCANCEL
            )
            
            if result == 'ok':
                # Open documentation
                webbrowser.open("https://appium.io/docs/en/about-appium/intro/")
            
            self.status_label.config(text="Android download initiated", foreground="green")
            logger.info("Android download requested")
            
        except Exception as e:
            logger.error(f"Android download error: {e}")
            self.status_label.config(text="Android download failed", foreground="red")
            messagebox.showerror("Download Error", f"Failed to start Android download: {e}")
    
    def show_help(self):
        """Show help and documentation"""
        try:
            help_text = """
Mobile App Automation - Help

Getting Started:
1. Download the tools for your target platform (iOS or Android)
2. Follow the installation guide for your operating system
3. Configure your development environment
4. Start creating automated tests

System Requirements:
• iOS: macOS with Xcode installed
• Android: Any OS with Android SDK

Support:
• Documentation: Available online
• Community: Join our forums
• Contact: <EMAIL>

Version: 1.0.0
            """
            
            messagebox.showinfo("Help & Documentation", help_text.strip())
            
        except Exception as e:
            logger.error(f"Help display error: {e}")
    
    def on_close(self):
        """Handle window close"""
        try:
            if self.on_close_callback:
                self.on_close_callback()
            
            self.root.destroy()
            logger.info("DownloadGUI closed")
            
        except Exception as e:
            logger.error(f"Error closing DownloadGUI: {e}")
    
    def show(self):
        """Show the GUI"""
        try:
            self.root.deiconify()
            self.root.lift()
            self.root.focus_force()
            logger.info("DownloadGUI shown")
            
        except Exception as e:
            logger.error(f"Error showing DownloadGUI: {e}")
    
    def run(self):
        """Run the GUI main loop"""
        try:
            self.root.mainloop()
            
        except Exception as e:
            logger.error(f"GUI main loop error: {e}")
    
    def destroy(self):
        """Destroy the GUI"""
        try:
            if self.root:
                self.root.destroy()
                
        except Exception as e:
            logger.error(f"Error destroying DownloadGUI: {e}")
