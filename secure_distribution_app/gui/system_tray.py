"""
System Tray Manager for Secure Distribution Application

Handles background operation, license monitoring, and system tray functionality.
"""

import os
import sys
import logging
import threading
import time
from typing import Callable, Optional
from pathlib import Path
import tkinter as tk
from tkinter import messagebox

# Try to import pystray for system tray functionality
try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image, ImageDraw
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False
    pystray = None

logger = logging.getLogger(__name__)

class SystemTrayManager:
    """Manages system tray functionality and background operations"""
    
    def __init__(self, session_manager, app_launcher, on_show_callback: Callable = None,
                 on_exit_callback: Callable = None, parent_root=None):
        self.session_manager = session_manager
        self.app_launcher = app_launcher
        self.on_show_callback = on_show_callback
        self.on_exit_callback = on_exit_callback
        self.parent_root = parent_root  # Store reference to main root window
        
        # Tray state
        self.tray_icon = None
        self.is_running = False
        self.monitoring_thread = None
        
        # License monitoring
        self.last_license_check = 0
        self.license_check_interval = 300  # 5 minutes
        
        # Create tray icon if available
        if PYSTRAY_AVAILABLE:
            self.create_tray_icon()
        else:
            logger.warning("pystray not available - system tray functionality disabled")
    
    def create_tray_icon(self):
        """Create system tray icon"""
        try:
            # Create a simple icon
            icon_image = self.create_icon_image()
            
            # Create menu items
            menu_items = [
                item('Show Application', self.show_application),
                item('License Status', self.show_license_status),
                pystray.Menu.SEPARATOR,
                item('Running Apps', self.show_running_apps),
                item('Stop All Apps', self.stop_all_apps),
                pystray.Menu.SEPARATOR,
                item('Exit', self.exit_application)
            ]
            
            # Create tray icon
            self.tray_icon = pystray.Icon(
                "MobileAppAutomation",
                icon_image,
                "Mobile App Automation",
                menu=pystray.Menu(*menu_items)
            )
            
            logger.info("System tray icon created")
            
        except Exception as e:
            logger.error(f"Failed to create system tray icon: {e}")
            self.tray_icon = None
    
    def create_icon_image(self, color='blue'):
        """Create a simple icon image"""
        try:
            # Create a 64x64 image
            image = Image.new('RGB', (64, 64), color='white')
            draw = ImageDraw.Draw(image)
            
            # Draw a simple mobile phone icon
            # Phone outline
            draw.rectangle([20, 10, 44, 54], outline=color, width=2)
            
            # Screen
            draw.rectangle([22, 15, 42, 45], fill='lightblue')
            
            # Home button
            draw.ellipse([29, 47, 35, 53], fill=color)
            
            return image
            
        except Exception as e:
            logger.error(f"Failed to create icon image: {e}")
            # Fallback to a simple colored square
            image = Image.new('RGB', (64, 64), color=color)
            return image
    
    def start_background_operation(self):
        """Start background operation with system tray"""
        try:
            if not PYSTRAY_AVAILABLE:
                logger.warning("System tray not available - running in foreground mode")
                return False
            
            if not self.tray_icon:
                logger.error("System tray icon not created")
                return False
            
            self.is_running = True
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            # Run tray icon (this blocks)
            logger.info("Starting system tray operation")
            self.tray_icon.run()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start background operation: {e}")
            return False
    
    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_running:
            try:
                current_time = time.time()
                
                # Check license status periodically
                if current_time - self.last_license_check > self.license_check_interval:
                    self._check_license_status()
                    self.last_license_check = current_time
                
                # Update tray icon based on status
                self._update_tray_icon_status()
                
                # Sleep for 30 seconds
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _check_license_status(self):
        """Check license status and handle expiry"""
        try:
            if not self.session_manager.is_authenticated():
                logger.warning("User not authenticated during license check")
                return
            
            # Validate license
            validation_result = self.session_manager.validate_license()
            
            if not validation_result.get('valid'):
                logger.warning("License validation failed during monitoring")
                self._handle_license_expiry()
            else:
                license_info = self.session_manager.get_license_status()
                if license_info.get('status') == 'warning':
                    self._show_license_warning(license_info.get('message'))
                    
        except Exception as e:
            logger.error(f"License status check error: {e}")
    
    def _handle_license_expiry(self):
        """Handle license expiry"""
        try:
            logger.warning("License expired - terminating all applications")
            
            # Stop all running apps
            if self.app_launcher:
                self.app_launcher.stop_all_apps()
            
            # Clear session
            self.session_manager.clear_session()
            
            # Show notification
            if self.tray_icon:
                self.tray_icon.notify("License Expired", 
                                    "Your license has expired. All applications have been stopped.")
            
            # Exit application
            self.exit_application()
            
        except Exception as e:
            logger.error(f"Error handling license expiry: {e}")
    
    def _show_license_warning(self, message: str):
        """Show license warning notification"""
        try:
            if self.tray_icon:
                self.tray_icon.notify("License Warning", message)
                
        except Exception as e:
            logger.error(f"Error showing license warning: {e}")
    
    def _update_tray_icon_status(self):
        """Update tray icon based on current status"""
        try:
            if not self.tray_icon:
                return
            
            # Determine icon color based on status
            if not self.session_manager.is_authenticated():
                color = 'red'  # Not authenticated
            elif not self.session_manager.is_license_valid():
                color = 'orange'  # License issues
            else:
                license_status = self.session_manager.get_license_status().get('status', 'unknown')
                if license_status == 'warning':
                    color = 'yellow'  # License warning
                else:
                    color = 'green'  # All good
            
            # Update icon (this is a simplified approach)
            # In a real implementation, you might cache different colored icons
            new_icon = self.create_icon_image(color)
            self.tray_icon.icon = new_icon
            
        except Exception as e:
            logger.error(f"Error updating tray icon status: {e}")
    
    def show_application(self, icon=None, item=None):
        """Show the main application window"""
        try:
            if self.on_show_callback:
                self.on_show_callback()
                
        except Exception as e:
            logger.error(f"Error showing application: {e}")
    
    def show_license_status(self, icon=None, item=None):
        """Show license status information"""
        try:
            if self.session_manager.is_authenticated():
                license_info = self.session_manager.get_license_status()
                user_info = self.session_manager.get_user_info()
                
                status = license_info.get('status', 'unknown')
                expiry_date = license_info.get('expiry_date', 'Not set')
                days_remaining = license_info.get('days_remaining')
                
                message = f"User: {user_info.get('email', 'Unknown')}\\n"
                message += f"License Status: {status.title()}\\n"
                message += f"Expiry Date: {expiry_date}\\n"
                
                if days_remaining is not None:
                    message += f"Days Remaining: {days_remaining}"
                
                # Show in a simple dialog using parent root
                if self.parent_root:
                    messagebox.showinfo("License Status", message, parent=self.parent_root)
                else:
                    # Fallback: create temporary root
                    temp_root = tk.Tk()
                    temp_root.withdraw()
                    messagebox.showinfo("License Status", message)
                    temp_root.destroy()
            else:
                if self.parent_root:
                    messagebox.showwarning("License Status", "Not authenticated", parent=self.parent_root)
                else:
                    # Fallback: create temporary root
                    temp_root = tk.Tk()
                    temp_root.withdraw()
                    messagebox.showwarning("License Status", "Not authenticated")
                    temp_root.destroy()
                
        except Exception as e:
            logger.error(f"Error showing license status: {e}")
    
    def show_running_apps(self, icon=None, item=None):
        """Show information about running apps"""
        try:
            # Check if main window has running processes
            running_count = 0
            message = "Running Applications:\\n\\n"

            # Try to get running apps from main application
            try:
                import tkinter as tk
                from tkinter import messagebox

                # This is a simplified check - in the new system,
                # running apps are managed by the platform selector
                message = "Mobile automation platforms may be running.\\n\\n"
                message += "Use the main application window to view and manage running platforms."

                # Show in a simple dialog using parent root
                if self.parent_root:
                    messagebox.showinfo("Running Applications", message, parent=self.parent_root)
                else:
                    # Fallback: create temporary root
                    temp_root = tk.Tk()
                    temp_root.withdraw()
                    messagebox.showinfo("Running Applications", message)
                    temp_root.destroy()

            except Exception:
                # Fallback message
                if self.parent_root:
                    messagebox.showinfo("Running Applications", "Please use the main application to view running platforms.", parent=self.parent_root)
                else:
                    # Fallback: create temporary root
                    temp_root = tk.Tk()
                    temp_root.withdraw()
                    messagebox.showinfo("Running Applications", "Please use the main application to view running platforms.")
                    temp_root.destroy()

        except Exception as e:
            logger.error(f"Error showing running apps: {e}")

    def stop_all_apps(self, icon=None, item=None):
        """Stop all running applications"""
        try:
            # In the new system, this would need to communicate with the main application
            # For now, show a message directing user to the main application
            import tkinter as tk
            from tkinter import messagebox

            if self.parent_root:
                result = messagebox.askyesno(
                    "Stop Applications",
                    "To stop running applications, please use the main application window.\\n\\n"
                    "Would you like to show the main application?",
                    parent=self.parent_root
                )
            else:
                # Fallback: create temporary root
                temp_root = tk.Tk()
                temp_root.withdraw()
                result = messagebox.askyesno(
                    "Stop Applications",
                    "To stop running applications, please use the main application window.\\n\\n"
                    "Would you like to show the main application?"
                )
                temp_root.destroy()

            if result:
                self.show_application()

        except Exception as e:
            logger.error(f"Error stopping all apps: {e}")
    
    def exit_application(self, icon=None, item=None):
        """Exit the application"""
        try:
            logger.info("Exiting application from system tray")
            
            # Stop monitoring
            self.is_running = False
            
            # Stop all apps
            if self.app_launcher:
                self.app_launcher.stop_all_apps()
            
            # Clear session
            if self.session_manager:
                self.session_manager.clear_session()
            
            # Call exit callback
            if self.on_exit_callback:
                self.on_exit_callback()
            
            # Stop tray icon
            if self.tray_icon:
                self.tray_icon.stop()
            
        except Exception as e:
            logger.error(f"Error during application exit: {e}")
    
    def minimize_to_tray(self):
        """Minimize application to system tray"""
        try:
            if PYSTRAY_AVAILABLE and self.tray_icon:
                logger.info("Minimizing to system tray")
                return True
            else:
                logger.warning("System tray not available - cannot minimize")
                return False
                
        except Exception as e:
            logger.error(f"Error minimizing to tray: {e}")
            return False
    
    def stop(self):
        """Stop the system tray manager"""
        try:
            self.is_running = False
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=2.0)
            
            if self.tray_icon:
                self.tray_icon.stop()
                
        except Exception as e:
            logger.error(f"Error stopping system tray manager: {e}")

def is_system_tray_available() -> bool:
    """Check if system tray functionality is available"""
    return PYSTRAY_AVAILABLE
