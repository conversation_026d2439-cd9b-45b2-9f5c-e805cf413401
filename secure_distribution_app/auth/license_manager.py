"""
License Management System for Secure Distribution Application

Handles license validation, expiry tracking, grace periods, and enforcement.
"""

import os
import json
import logging
import hashlib
import platform
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path
import tempfile
import uuid
import threading
import time

logger = logging.getLogger(__name__)

class LicenseManager:
    """Comprehensive license management with validation and enforcement"""
    
    def __init__(self, supabase_client=None):
        self.supabase = supabase_client
        self.license_cache = {}
        self.validation_callbacks = []
        self.enforcement_active = True
        
        # License validation settings
        self.grace_period_days = 7
        self.warning_period_days = 30
        self.validation_interval = 300  # 5 minutes
        
        # Hardware fingerprinting
        self.hardware_fingerprint = self.generate_hardware_fingerprint()
    
    def generate_hardware_fingerprint(self) -> str:
        """Generate unique hardware fingerprint for license binding"""
        try:
            # Collect system information
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node()[:8],  # Partial hostname for privacy
                'system': platform.system(),
                'release': platform.release()
            }
            
            # Create deterministic fingerprint
            fingerprint_data = json.dumps(system_info, sort_keys=True)
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]
            
            logger.info(f"Hardware fingerprint generated: {fingerprint}")
            return fingerprint
            
        except Exception as e:
            logger.error(f"Failed to generate hardware fingerprint: {e}")
            return "unknown_hardware"
    
    def validate_license(self, user_id: str, license_number: str = None) -> Dict[str, Any]:
        """Comprehensive license validation"""
        try:
            if not self.supabase:
                return self._offline_license_validation(license_number)
            
            # Get user profile from Supabase
            user_profile = self.supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
            
            if not user_profile.data:
                return {
                    'valid': False,
                    'error': 'User profile not found',
                    'status': 'invalid'
                }
            
            profile = user_profile.data[0]
            
            # Validate license number if provided
            stored_license = profile.get('license_number')
            if license_number and stored_license != license_number:
                return {
                    'valid': False,
                    'error': 'Invalid license number',
                    'status': 'invalid'
                }
            
            # Check if license is active
            if not profile.get('is_active', True):
                return {
                    'valid': False,
                    'error': 'License has been deactivated',
                    'status': 'deactivated'
                }
            
            # Validate hardware binding if enabled
            if profile.get('hardware_fingerprint'):
                if profile['hardware_fingerprint'] != self.hardware_fingerprint:
                    return {
                        'valid': False,
                        'error': 'License is bound to different hardware',
                        'status': 'hardware_mismatch'
                    }
            
            # Check license expiry
            license_expiry = profile.get('license_expiry')
            if license_expiry:
                expiry_date = datetime.fromisoformat(license_expiry.replace('Z', '+00:00'))
                current_date = datetime.now(expiry_date.tzinfo)
                
                if current_date > expiry_date:
                    # Check grace period
                    grace_end = expiry_date + timedelta(days=self.grace_period_days)
                    if current_date > grace_end:
                        return {
                            'valid': False,
                            'error': 'License has expired',
                            'status': 'expired',
                            'expiry_date': license_expiry,
                            'grace_period_ended': True
                        }
                    else:
                        # In grace period
                        days_remaining = (grace_end - current_date).days
                        return {
                            'valid': True,
                            'status': 'grace_period',
                            'message': f'License expired but in grace period ({days_remaining} days remaining)',
                            'expiry_date': license_expiry,
                            'grace_days_remaining': days_remaining,
                            'warning': True
                        }
                
                # Check warning period
                warning_start = expiry_date - timedelta(days=self.warning_period_days)
                if current_date > warning_start:
                    days_remaining = (expiry_date - current_date).days
                    return {
                        'valid': True,
                        'status': 'warning',
                        'message': f'License expires in {days_remaining} days',
                        'expiry_date': license_expiry,
                        'days_remaining': days_remaining,
                        'warning': True
                    }
                
                # License is valid
                days_remaining = (expiry_date - current_date).days
                return {
                    'valid': True,
                    'status': 'active',
                    'expiry_date': license_expiry,
                    'days_remaining': days_remaining
                }
            
            # No expiry date set - perpetual license
            return {
                'valid': True,
                'status': 'perpetual',
                'expiry_date': None
            }
            
        except Exception as e:
            logger.error(f"License validation error: {e}")
            return {
                'valid': False,
                'error': f'License validation failed: {str(e)}',
                'status': 'error'
            }
    
    def _offline_license_validation(self, license_number: str) -> Dict[str, Any]:
        """Offline license validation for when Supabase is not available"""
        try:
            # This is a simplified offline validation
            # In production, you might use cryptographic signatures
            
            if not license_number:
                return {
                    'valid': False,
                    'error': 'License number required',
                    'status': 'invalid'
                }
            
            # Basic format validation
            if len(license_number) < 10:
                return {
                    'valid': False,
                    'error': 'Invalid license format',
                    'status': 'invalid'
                }
            
            # For demo purposes, accept any license with specific pattern
            if license_number.startswith('DEMO-') or license_number.startswith('TEST-'):
                return {
                    'valid': True,
                    'status': 'demo',
                    'message': 'Demo license - limited functionality'
                }
            
            return {
                'valid': False,
                'error': 'Cannot validate license offline',
                'status': 'offline'
            }
            
        except Exception as e:
            logger.error(f"Offline license validation error: {e}")
            return {
                'valid': False,
                'error': f'Offline validation failed: {str(e)}',
                'status': 'error'
            }
    
    def bind_license_to_hardware(self, user_id: str) -> bool:
        """Bind license to current hardware"""
        try:
            if not self.supabase:
                logger.warning("Cannot bind license - Supabase not available")
                return False
            
            # Update user profile with hardware fingerprint
            result = self.supabase.table('user_profiles').update({
                'hardware_fingerprint': self.hardware_fingerprint,
                'hardware_bound_at': datetime.utcnow().isoformat()
            }).eq('user_id', user_id).execute()
            
            if result.data:
                logger.info("License bound to hardware successfully")
                return True
            else:
                logger.error("Failed to bind license to hardware")
                return False
                
        except Exception as e:
            logger.error(f"Hardware binding error: {e}")
            return False
    
    def update_last_validation(self, user_id: str):
        """Update last validation timestamp"""
        try:
            if self.supabase:
                self.supabase.table('user_profiles').update({
                    'last_validation': datetime.utcnow().isoformat()
                }).eq('user_id', user_id).execute()
                
        except Exception as e:
            logger.warning(f"Failed to update last validation: {e}")
    
    def get_license_usage_stats(self, user_id: str) -> Dict[str, Any]:
        """Get license usage statistics"""
        try:
            if not self.supabase:
                return {}
            
            # Get user profile
            profile = self.supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
            
            if profile.data:
                data = profile.data[0]
                return {
                    'license_number': data.get('license_number'),
                    'created_at': data.get('created_at'),
                    'last_login': data.get('last_login'),
                    'last_validation': data.get('last_validation'),
                    'hardware_bound': bool(data.get('hardware_fingerprint')),
                    'hardware_bound_at': data.get('hardware_bound_at'),
                    'total_logins': data.get('total_logins', 0)
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Failed to get license usage stats: {e}")
            return {}
    
    def register_validation_callback(self, callback):
        """Register callback for license validation events"""
        self.validation_callbacks.append(callback)
    
    def _notify_validation_callbacks(self, event_type: str, data: Dict[str, Any]):
        """Notify registered callbacks about validation events"""
        for callback in self.validation_callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"Validation callback error: {e}")
    
    def create_license_report(self, user_id: str) -> Dict[str, Any]:
        """Create comprehensive license report"""
        try:
            validation_result = self.validate_license(user_id)
            usage_stats = self.get_license_usage_stats(user_id)
            
            report = {
                'timestamp': datetime.utcnow().isoformat(),
                'user_id': user_id,
                'hardware_fingerprint': self.hardware_fingerprint,
                'validation_result': validation_result,
                'usage_stats': usage_stats,
                'system_info': {
                    'platform': platform.platform(),
                    'python_version': platform.python_version(),
                    'architecture': platform.architecture()[0]
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to create license report: {e}")
            return {}
    
    def enforce_license_compliance(self, user_id: str) -> bool:
        """Enforce license compliance - return False if access should be denied"""
        try:
            if not self.enforcement_active:
                return True
            
            validation_result = self.validate_license(user_id)
            
            # Allow access for valid licenses
            if validation_result.get('valid'):
                status = validation_result.get('status')
                
                # Log warnings for grace period and warning status
                if status in ['grace_period', 'warning']:
                    self._notify_validation_callbacks('license_warning', validation_result)
                
                return True
            
            # Deny access for invalid licenses
            self._notify_validation_callbacks('license_violation', validation_result)
            return False
            
        except Exception as e:
            logger.error(f"License enforcement error: {e}")
            return False  # Fail secure - deny access on error
    
    def disable_enforcement(self):
        """Disable license enforcement (for testing)"""
        self.enforcement_active = False
        logger.warning("License enforcement disabled")
    
    def enable_enforcement(self):
        """Enable license enforcement"""
        self.enforcement_active = True
        logger.info("License enforcement enabled")

class LicenseValidator:
    """Simplified license validator for quick checks"""
    
    @staticmethod
    def is_valid_license_format(license_number: str) -> bool:
        """Check if license number has valid format"""
        if not license_number or len(license_number) < 10:
            return False
        
        # Add your license format validation logic here
        # For example: XXX-XXXX-XXXX format
        return True
    
    @staticmethod
    def generate_demo_license() -> str:
        """Generate a demo license for testing"""
        return f"DEMO-{uuid.uuid4().hex[:8].upper()}"
    
    @staticmethod
    def generate_test_license() -> str:
        """Generate a test license for development"""
        return f"TEST-{uuid.uuid4().hex[:8].upper()}"
