"""
Browser-Based Authentication Module

Handles authentication through the user's default web browser using custom Flask templates.
Eliminates GUI duplication issues by removing tkinter-based login windows.
"""

import os
import sys
import time
import json
import logging
import webbrowser
import threading
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, Callable

logger = logging.getLogger(__name__)

class FlaskServerManager:
    """Manages the Flask web server for custom template authentication"""

    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.server_process = None
        self.is_running = False

    def start_server(self) -> bool:
        """Start the Flask web server"""
        try:
            # Import the Flask app
            web_server_dir = Path(__file__).parent.parent / "web_server"
            app_file = web_server_dir / "app.py"

            if not app_file.exists():
                logger.error(f"Flask app not found: {app_file}")
                return False

            # Start Flask server as subprocess
            cmd = [sys.executable, str(app_file)]
            env = os.environ.copy()
            env['FLASK_HOST'] = self.host
            env['FLASK_PORT'] = str(self.port)

            self.server_process = subprocess.Popen(
                cmd,
                cwd=str(web_server_dir),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # Wait a moment for server to start
            time.sleep(2)

            # Check if process is still running
            if self.server_process.poll() is None:
                self.is_running = True
                logger.info(f"Flask server started on http://{self.host}:{self.port}")
                return True
            else:
                logger.error("Flask server failed to start")
                return False

        except Exception as e:
            logger.error(f"Failed to start Flask server: {e}")
            return False

    def stop_server(self):
        """Stop the Flask web server"""
        try:
            if self.server_process:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                self.server_process = None

            self.is_running = False
            logger.info("Flask server stopped")

        except Exception as e:
            logger.error(f"Error stopping Flask server: {e}")

    def get_url(self, path='') -> str:
        """Get URL for the Flask server"""
        return f"http://{self.host}:{self.port}{path}"

class BrowserAuthManager:
    """Manages browser-based authentication flow using custom Flask templates"""

    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port

        # Authentication state
        self.is_authenticated = False
        self.user_data = None
        self.auth_error = None

        # Callbacks
        self.on_success_callback: Optional[Callable] = None
        self.on_error_callback: Optional[Callable] = None

        # Flask server manager
        self.flask_server = FlaskServerManager(host=host, port=port)

        # Auth URL (points to our custom login page)
        self.auth_url = f"http://{host}:{port}/"

        logger.info("BrowserAuthManager initialized with custom templates")
    
    def start_auth_flow(self, on_success: Callable = None, on_error: Callable = None) -> bool:
        """Start the browser-based authentication flow"""
        try:
            self.on_success_callback = on_success
            self.on_error_callback = on_error

            # Start Flask server
            if not self.flask_server.start_server():
                if on_error:
                    on_error("Failed to start web server")
                return False

            # Open browser to auth URL
            logger.info(f"Opening browser to: {self.auth_url}")
            webbrowser.open(self.auth_url)

            return True

        except Exception as e:
            logger.error(f"Failed to start auth flow: {e}")
            if on_error:
                on_error(str(e))
            return False
    
    def on_auth_success(self, user_data: Dict[str, Any]):
        """Handle successful authentication"""
        self.is_authenticated = True
        self.user_data = user_data
        self.auth_error = None
        
        logger.info("Authentication successful")
        
        if self.on_success_callback:
            self.on_success_callback(user_data)
        
        # Stop server after successful auth
        threading.Timer(2.0, self.stop_callback_server).start()
    
    def on_auth_error(self, error: str):
        """Handle authentication error"""
        self.is_authenticated = False
        self.user_data = None
        self.auth_error = error
        
        logger.error(f"Authentication failed: {error}")
        
        if self.on_error_callback:
            self.on_error_callback(error)
    
    def wait_for_auth(self, timeout: int = 300) -> bool:
        """Wait for authentication to complete (for Flask server, this is manual)"""
        # For Flask server authentication, we don't automatically detect completion
        # The user will authenticate through the web interface
        # This method can be used to keep the application running

        start_time = time.time()

        print("🌐 Web server is running. Please complete authentication in your browser.")
        print("🔄 The application will continue running until you close it.")
        print("📋 Access the dashboard at: http://localhost:8080/dashboard")

        # Keep running until timeout or manual stop
        while time.time() - start_time < timeout:
            if not self.flask_server.is_running:
                break
            time.sleep(1)

        return True  # Always return True for Flask server mode

    def get_user_data(self) -> Optional[Dict[str, Any]]:
        """Get authenticated user data"""
        return self.user_data if self.is_authenticated else None

    def cleanup(self):
        """Clean up resources"""
        self.flask_server.stop_server()
        logger.info("BrowserAuthManager cleaned up")
