#!/usr/bin/env python3
"""
Final Secure App Builder - Updated for Web-Based Architecture

This script creates secure, obfuscated executables for the updated web-based
mobile app automation platform with dynamic port management and AI integration.
"""

import os
import sys
import argparse
import subprocess
import shutil
import logging
import json
import hashlib
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalSecureAppBuilder:
    """Builds secure executables for the updated web-based architecture"""

    def __init__(self, use_pyarmor: bool = False):
        self.project_root = Path(__file__).parent.parent
        self.secure_app_dir = Path(__file__).parent
        self.build_dir = self.secure_app_dir / 'build'
        self.dist_dir = self.secure_app_dir / 'dist'
        self.temp_dir = self.secure_app_dir / 'temp_build_final'
        self.use_pyarmor = use_pyarmor
        self.pyarmor_available = False
        self.obfuscation_strategy = 'minify'
        self.runtime_root_name = 'runtime'
        self.runtime_assets = {
            'ios/test_cases': self.project_root / 'test_cases_ios',
            'ios/test_suites': self.project_root / 'test_suites_ios',
            'ios/reference_images': self.project_root / 'reference_images_ios',
            'ios/reports': self.project_root / 'reports_ios',
            'ios/screenshots': self.project_root / 'screenshots_ios',
            'ios/recordings': self.project_root / 'recordings_ios',
            'ios/temp': self.project_root / 'temp_ios',
            'android/test_cases': self.project_root / 'test_cases_android',
            'android/test_suites': self.project_root / 'test_suites_android',
            'android/reference_images': self.project_root / 'reference_images_android',
            'android/reports': self.project_root / 'reports_android',
            'android/screenshots': self.project_root / 'screenshots_android',
            'android/recordings': self.project_root / 'recordings_android',
            'android/temp': self.project_root / 'temp_android',
            'shared/files_to_push': self.project_root / 'files_to_push',
            '.appium-python': self.project_root / '.appium-python',
        }

        # Ensure directories exist
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

        # Build configuration
        self.build_config = {
            'app_name': 'SecureMobileAppAutomation',
            'version': '2.0.0',
            'author': 'Mobile Automation Team',
            'description': 'Secure Mobile App Automation Platform with AI Integration',
            'build_date': datetime.now().isoformat(),
            'features': [
                'Web-based authentication',
                'Dynamic port management',
                'AI-powered test healing',
                'Cross-platform support',
                'Enhanced reporting',
                'Supabase integration'
            ]
        }

    def check_dependencies(self):
        """Check if required dependencies are installed"""
        try:
            logger.info("Checking build dependencies...")

            # Check PyInstaller
            try:
                import PyInstaller
                logger.info(f"✅ PyInstaller found: {PyInstaller.__version__}")
            except ImportError:
                logger.error("❌ PyInstaller not found")
                return False

            # Check PyArmor (import + CLI sanity check) only when requested
            if self.use_pyarmor:
                try:
                    import pyarmor  # noqa: F401
                    try:
                        cli_check = subprocess.run([sys.executable, '-m', 'pyarmor', '--version'], capture_output=True, text=True)
                        if cli_check.returncode == 0:
                            self.pyarmor_available = True
                            logger.info("✅ PyArmor available")
                        else:
                            logger.warning("⚠️ PyArmor CLI not available (will fallback): %s", cli_check.stderr.strip())
                    except Exception as cli_e:
                        logger.warning("⚠️ PyArmor CLI check error: %s", cli_e)
                except ImportError:
                    logger.warning("⚠️ PyArmor not available - will fallback to python-minifier obfuscation")

            # Check required Python modules (include obfuscator)
            required_modules = ['flask', 'supabase', 'pathlib', 'python_minifier']
            missing_modules = []
            for module in required_modules:
                try:
                    __import__(module)
                    logger.info(f"✅ {module} available")
                except ImportError:
                    logger.warning(f"⚠️ {module} not found")
                    missing_modules.append(module)

            # Install missing modules if needed
            if missing_modules:
                logger.info(f"Installing missing modules: {missing_modules}")
                try:
                    for module in missing_modules:
                        result = subprocess.run([
                            sys.executable, '-m', 'pip', 'install',
                            '--break-system-packages', module
                        ], capture_output=True, text=True)
                        if result.returncode == 0:
                            logger.info(f"✅ {module} installed successfully")
                        else:
                            logger.error(f"❌ Failed to install {module}: {result.stderr}")
                            return False
                except Exception as e:
                    logger.error(f"Module installation failed: {e}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Dependency check failed: {e}")
            return False

    def prepare_source_files(self):
        """Prepare source files for building"""
        try:
            logger.info("Preparing source files...")

            # Clean temp directory
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
            self.temp_dir.mkdir(exist_ok=True)

            # Copy main application files
            files_to_copy = [
                'main_web_secure.py',
                'web_server/',
                'auth/',
                'security/',
                'app/',
                'app_android/',
                'run.py',
                'run_android.py',
                '.env.example',
                'requirements.txt'
            ]

            # Also copy iOS/Android app folders and entry scripts from project root
            ios_app_dir = self.project_root / 'app'
            if ios_app_dir.exists():
                shutil.copytree(ios_app_dir, self.temp_dir / 'app', dirs_exist_ok=True)
                logger.info("Copied iOS app directory")
            android_app_dir = self.project_root / 'app_android'
            if android_app_dir.exists():
                shutil.copytree(android_app_dir, self.temp_dir / 'app_android', dirs_exist_ok=True)
                logger.info("Copied Android app directory")
            # Entry scripts and runtime config from project root
            for root_src, name in [
                (self.project_root, 'run.py'),
                (self.project_root, 'run_android.py'),
                (self.project_root, 'config.py'),
                (self.project_root, 'config_android.py'),
                (self.project_root, 'shared_directory_paths_db.py'),
            ]:
                src = root_src / name
                if src.exists():
                    shutil.copy2(src, self.temp_dir / name)
                    logger.info(f"Copied project root file: {name}")

            # Ensure main_web_secure.py exists
            main_src = self.secure_app_dir / 'main_web_secure.py'
            main_dst = self.temp_dir / 'main_web_secure.py'
            if main_src.exists() and not main_dst.exists():
                shutil.copy2(main_src, main_dst)
                logger.info("Ensured main_web_secure.py present")

            for file_path in files_to_copy:
                src = self.secure_app_dir / file_path
                if src.exists():
                    if src.is_file():
                        dst = self.temp_dir / file_path
                        dst.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(src, dst)
                        logger.info(f"Copied file: {file_path}")
                    else:
                        dst = self.temp_dir / file_path
                        shutil.copytree(src, dst, dirs_exist_ok=True)
            # Ensure a .env exists for PyInstaller datas; create placeholder if missing
            try:
                env_dst = self.temp_dir / '.env'
                if not env_dst.exists():
                    env_src_candidates = [
                        self.secure_app_dir / '.env',
                        self.project_root / '.env'
                    ]
                    copied = False
                    for env_src in env_src_candidates:
                        if env_src.exists():
                            shutil.copy2(env_src, env_dst)
                            copied = True
                            break
                    if not copied:
                        with open(env_dst, 'w') as ef:
                            ef.write('# Generated placeholder .env for bundled app\n')
                            ef.write('AUTO_OPEN_BROWSER=0\n')
            except Exception as e:
                logger.warning(f"Failed to create .env placeholder: {e}")



            # Copy templates from project root
            templates_src = self.project_root / 'templates'
            if templates_src.exists():
                templates_dst = self.temp_dir / 'templates'
                shutil.copytree(templates_src, templates_dst, dirs_exist_ok=True)
                logger.info("Copied templates directory")

            # Copy utils directory for port management
            utils_src = self.project_root / 'utils'
            if utils_src.exists():
                utils_dst = self.temp_dir / 'utils'
                shutil.copytree(utils_src, utils_dst, dirs_exist_ok=True)
                logger.info("Copied utils directory")

            # Copy shared data assets and sanitize databases
            self._prepare_runtime_assets()

            runtime_root = self.temp_dir / self.runtime_root_name
            self._prepare_python_runtime(runtime_root)
            self._prepare_node_runtime(runtime_root)

            # Copy AI integration if available
            ai_src = self.project_root / 'utils' / 'ai_integration'
            if ai_src.exists():
                ai_dst = self.temp_dir / 'utils' / 'ai_integration'
                shutil.copytree(ai_src, ai_dst, dirs_exist_ok=True)
                logger.info("Copied AI integration")

            # Create build info file
            build_info = {
                **self.build_config,
                'obfuscation_strategy': self.obfuscation_strategy,
                'source_files': [str(f.relative_to(self.temp_dir)) for f in self.temp_dir.rglob('*.py')]
            }

            with open(self.temp_dir / 'build_info.json', 'w') as f:
                json.dump(build_info, f, indent=2)

            logger.info("✅ Source files prepared successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to prepare source files: {e}")
            return False

    def _prepare_runtime_assets(self):
        """Copy runtime assets and sanitize platform databases."""
        try:
            source_db_dir = self.project_root / 'db-data'
            if source_db_dir.exists():
                target_db_dir = self.temp_dir / 'db-data'
                shutil.copytree(source_db_dir, target_db_dir, dirs_exist_ok=True)
                logger.info("Copied db-data directory")

                for db_file in target_db_dir.glob('*.db'):
                    self._sanitize_platform_db(db_file)

            runtime_root = self.temp_dir / self.runtime_root_name
            runtime_root.mkdir(parents=True, exist_ok=True)

            for subpath, source in self.runtime_assets.items():
                destination = runtime_root / subpath
                destination.parent.mkdir(parents=True, exist_ok=True)
                if source.exists() and source.is_dir():
                    shutil.copytree(source, destination, dirs_exist_ok=True)
                    logger.info(f"Copied runtime directory: {subpath}")
                else:
                    destination.mkdir(parents=True, exist_ok=True)
        except Exception as asset_err:
            logger.warning(f"Runtime asset preparation encountered an issue: {asset_err}")

    def _prepare_python_runtime(self, runtime_root: Path) -> None:
        """Vendor Python dependencies into the runtime bundle."""
        try:
            requirements_file = self.project_root / 'requirements.txt'
            if not requirements_file.exists():
                logger.warning("requirements.txt not found; skipping embedded Python environment")
                return

            python_env_dir = runtime_root / 'python_env'
            if python_env_dir.exists():
                shutil.rmtree(python_env_dir)
            python_env_dir.mkdir(parents=True, exist_ok=True)

            # Prefer copying from existing project virtual environment to ensure parity
            copied_from_venv = False
            project_venv = self.project_root / 'venv'
            if project_venv.exists():
                site_package_candidates = list((project_venv / 'lib').glob('python*/site-packages'))
                site_package_candidates.append(project_venv / 'Lib' / 'site-packages')
                site_package_candidates.append(project_venv / 'lib64' / 'python' / 'site-packages')

                for candidate in site_package_candidates:
                    if candidate.exists():
                        logger.info(f"Copying dependencies from project venv: {candidate}")
                        shutil.copytree(candidate, python_env_dir, dirs_exist_ok=True)
                        copied_from_venv = True
                        break

            if not copied_from_venv:
                logger.info(f"Bundling Python dependencies into {python_env_dir}")
                pip_cmd = [
                    sys.executable,
                    '-m',
                    'pip',
                    'install',
                    '--upgrade',
                    '--no-warn-script-location',
                    '--no-input',
                    '--disable-pip-version-check',
                    '-r',
                    str(requirements_file),
                    '--target',
                    str(python_env_dir),
                ]

                env = os.environ.copy()
                env.setdefault('PYTHONWARNINGS', 'ignore:DEPRECATION')
                process = subprocess.run(pip_cmd, capture_output=True, text=True, env=env)
                if process.returncode != 0:
                    logger.error("Failed to vendor Python dependencies: %s", process.stderr.strip())
                    raise RuntimeError("pip install into runtime bundle failed")

            (python_env_dir / 'README_RUNTIME_ENV.txt').write_text(
                "This directory contains vendored Python packages for the secure build runtime.\n",
                encoding='utf-8'
            )
            logger.info("Python runtime environment prepared successfully")
        except Exception as python_err:
            logger.error(f"Unable to prepare embedded Python environment: {python_err}")
            raise

    def _prepare_node_runtime(self, runtime_root: Path) -> None:
        """Embed Node.js dependencies required for Appium and tooling."""
        try:
            package_json = self.project_root / 'package.json'
            node_modules = self.project_root / 'node_modules'
            if not package_json.exists() or not node_modules.exists():
                logger.warning("Node runtime assets missing; skipping embedded Node environment")
                return

            node_runtime_root = runtime_root / 'node_env'
            if node_runtime_root.exists():
                shutil.rmtree(node_runtime_root)
            node_runtime_root.mkdir(parents=True, exist_ok=True)

            logger.info(f"Copying Node.js runtime into {node_runtime_root}")
            shutil.copy2(package_json, node_runtime_root / 'package.json')
            lock_file = self.project_root / 'package-lock.json'
            if lock_file.exists():
                shutil.copy2(lock_file, node_runtime_root / 'package-lock.json')

            node_modules_dest = node_runtime_root / 'node_modules'
            shutil.copytree(node_modules, node_modules_dest, dirs_exist_ok=True)

            logger.info("Node runtime environment prepared successfully")
        except Exception as node_err:
            logger.error(f"Unable to prepare embedded Node environment: {node_err}")
            raise

    def _sanitize_platform_db(self, db_path: Path) -> None:
        """Create a minimized-but-functional copy of the platform database."""
        try:
            platform = 'ios' if 'ios' in db_path.name else 'android'
            backup_path = db_path.with_suffix('.original')
            if not backup_path.exists():
                shutil.copy2(db_path, backup_path)

            required_tables = {
                'global_values': {'copy_rows': True},
                'execution_settings': {'copy_rows': False},
                'environments': {'copy_rows': False},
                'environment_variables': {'copy_rows': False},
                'active_environment': {'copy_rows': False},
                'test_suites': {'copy_rows': False},
                'test_cases': {'copy_rows': False},
                'test_steps': {'copy_rows': False},
                'execution_tracking': {'copy_rows': False},
                'execution_reports': {'copy_rows': False},
                'locator_repository': {'copy_rows': False},
                'reference_images': {'copy_rows': False},
                'screenshots': {'copy_rows': False},
                'test_case_json_backups': {'copy_rows': False},
            }

            fallback_table_sql: Dict[str, str] = {
                'global_values': (
                    """
                    CREATE TABLE IF NOT EXISTS global_values (
                        name TEXT PRIMARY KEY,
                        value TEXT,
                        type TEXT
                    )
                    """
                ),
                'execution_settings': (
                    """
                    CREATE TABLE IF NOT EXISTS execution_settings (
                        setting_name TEXT PRIMARY KEY,
                        setting_value TEXT,
                        description TEXT,
                        category TEXT,
                        is_default INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                ),
                'environments': (
                    """
                    CREATE TABLE IF NOT EXISTS environments (
                        environment_id TEXT PRIMARY KEY,
                        platform TEXT NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        is_active INTEGER DEFAULT 0,
                        port_specific TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                ),
                'environment_variables': (
                    """
                    CREATE TABLE IF NOT EXISTS environment_variables (
                        environment_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        initial_value TEXT,
                        current_value TEXT,
                        type TEXT DEFAULT 'default',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (environment_id, name),
                        FOREIGN KEY (environment_id) REFERENCES environments(environment_id) ON DELETE CASCADE
                    )
                    """
                ),
                'active_environment': (
                    """
                    CREATE TABLE IF NOT EXISTS active_environment (
                        id INTEGER PRIMARY KEY CHECK (id = 1),
                        environment_id TEXT,
                        session_id TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                ),
                'test_suites': (
                    """
                    CREATE TABLE IF NOT EXISTS test_suites (
                        suite_id TEXT PRIMARY KEY,
                        platform TEXT NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        file_path TEXT,
                        test_case_count INTEGER DEFAULT 0,
                        step_count INTEGER DEFAULT 0,
                        json_payload TEXT,
                        status TEXT DEFAULT 'active',
                        version TEXT DEFAULT '1.0',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by TEXT,
                        last_modified_by TEXT
                    )
                    """
                ),
                'test_cases': (
                    """
                    CREATE TABLE IF NOT EXISTS test_cases (
                        test_case_id TEXT PRIMARY KEY,
                        suite_id TEXT NOT NULL,
                        platform TEXT NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        file_path TEXT,
                        step_count INTEGER DEFAULT 0,
                        json_payload TEXT,
                        test_idx INTEGER,
                        status TEXT DEFAULT 'active',
                        version TEXT DEFAULT '1.0',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by TEXT,
                        last_modified_by TEXT,
                        FOREIGN KEY (suite_id) REFERENCES test_suites(suite_id) ON DELETE CASCADE
                    )
                    """
                ),
                'test_steps': (
                    """
                    CREATE TABLE IF NOT EXISTS test_steps (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        suite_id TEXT,
                        test_idx INTEGER,
                        step_idx INTEGER,
                        name TEXT,
                        action_type TEXT,
                        action_id TEXT,
                        status TEXT,
                        duration TEXT,
                        timestamp TEXT,
                        screenshot_path TEXT,
                        error TEXT,
                        enabled INTEGER DEFAULT 1
                    )
                    """
                ),
                'execution_tracking': (
                    """
                    CREATE TABLE IF NOT EXISTS execution_tracking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        suite_id TEXT,
                        test_idx INTEGER,
                        step_idx INTEGER,
                        filename TEXT,
                        action_type TEXT,
                        action_params TEXT,
                        action_id TEXT,
                        status TEXT,
                        retry_count INTEGER DEFAULT 0,
                        max_retries INTEGER DEFAULT 3,
                        last_error TEXT,
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        in_progress INTEGER DEFAULT 0,
                        execution_result TEXT,
                        test_case_id TEXT,
                        test_execution_id TEXT
                    )
                    """
                ),
                'execution_reports': (
                    """
                    CREATE TABLE IF NOT EXISTS execution_reports (
                        report_id TEXT PRIMARY KEY,
                        test_execution_id TEXT,
                        suite_id TEXT,
                        test_case_id TEXT,
                        platform TEXT,
                        status TEXT,
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        duration INTEGER,
                        error_message TEXT,
                        screenshot_data BLOB,
                        report_data TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                ),
                'locator_repository': (
                    """
                    CREATE TABLE IF NOT EXISTS locator_repository (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_case_name TEXT,
                        test_case_id TEXT,
                        action_id TEXT,
                        locator_type TEXT,
                        locator_value TEXT,
                        platform TEXT,
                        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_used_date TIMESTAMP
                    )
                    """
                ),
                'reference_images': (
                    """
                    CREATE TABLE IF NOT EXISTS reference_images (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        image_name TEXT UNIQUE NOT NULL,
                        image_data BLOB NOT NULL,
                        image_format TEXT NOT NULL,
                        file_size INTEGER,
                        checksum TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                ),
                'screenshots': (
                    """
                    CREATE TABLE IF NOT EXISTS screenshots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        suite_id TEXT,
                        test_idx INTEGER,
                        step_idx INTEGER,
                        filename TEXT,
                        path TEXT,
                        timestamp TEXT,
                        action_id TEXT,
                        custom_screenshot_name TEXT,
                        custom_screenshot_filename TEXT,
                        custom_screenshot_path TEXT
                    )
                    """
                ),
                'test_case_json_backups': (
                    """
                    CREATE TABLE IF NOT EXISTS test_case_json_backups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_case_id TEXT,
                        json_payload TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                ),
            }

            table_definitions: Dict[str, str] = {}
            table_rows: Dict[str, Dict[str, List[Any]]] = {}

            with sqlite3.connect(str(backup_path)) as conn_old:
                cursor = conn_old.cursor()
                cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                for name, sql in cursor.fetchall():
                    if name in required_tables and sql:
                        table_definitions[name] = sql
                        if required_tables[name].get('copy_rows'):
                            cursor.execute(f"SELECT * FROM {name}")
                            rows = cursor.fetchall()
                            columns = [desc[0] for desc in cursor.description]
                            table_rows[name] = {'rows': rows, 'columns': columns}

            tmp_db = db_path.with_suffix('.tmp')
            if tmp_db.exists():
                tmp_db.unlink()

            with sqlite3.connect(str(tmp_db)) as conn_new:
                cur_new = conn_new.cursor()

                for table_name, config in required_tables.items():
                    create_sql = table_definitions.get(table_name) or fallback_table_sql.get(table_name)
                    if not create_sql:
                        logger.debug(f"Skipping table {table_name} - definition not found in source database")
                        continue

                    cur_new.execute(create_sql)

                    if config.get('copy_rows') and table_name in table_rows:
                        meta = table_rows[table_name]
                        rows = meta['rows']
                        columns = meta['columns']
                        if rows:
                            placeholders = ','.join('?' for _ in columns)
                            insert_sql = f"INSERT INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
                            cur_new.executemany(insert_sql, rows)

                cur_new.execute("SELECT COUNT(*) FROM environments")
                env_count = cur_new.fetchone()[0]
                if env_count == 0:
                    now = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
                    env_id = f"{platform.upper()}_DEFAULT"
                    cur_new.execute(
                        """
                        INSERT INTO environments (environment_id, platform, name, description, is_active, port_specific, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """,
                        (
                            env_id,
                            platform,
                            "Secure Default Environment",
                            "Auto-generated placeholder environment for secure build",
                            1,
                            None,
                            now,
                            now,
                        ),
                    )

                    try:
                        cur_new.execute(
                            """
                            INSERT OR REPLACE INTO active_environment (id, environment_id, session_id, updated_at)
                            VALUES (1, ?, NULL, ?)
                            """,
                            (
                                env_id,
                                now,
                            ),
                        )
                    except sqlite3.OperationalError as insert_err:
                        logger.debug(f"active_environment table not available to seed: {insert_err}")

                conn_new.commit()

            os.replace(tmp_db, db_path)
            logger.info(f"Sanitized database with functional schema: {db_path}")

        except Exception as db_err:
            logger.warning(f"Failed to sanitize database {db_path}: {db_err}")

    def obfuscate_with_pyarmor(self):
        """Obfuscate code using PyArmor for security compliance."""
        try:
            logger.info("Starting PyArmor obfuscation...")
            obfuscated_dir = self.temp_dir / 'obfuscated'
            if obfuscated_dir.exists():
                shutil.rmtree(obfuscated_dir)
            obfuscated_dir.mkdir(exist_ok=True)
            py_files = list(self.temp_dir.rglob('*.py'))
            for i, file in enumerate(py_files):
                rel_path = file.relative_to(self.temp_dir)
                if rel_path.parts and rel_path.parts[0] == 'obfuscated':
                    continue
                out_dir = obfuscated_dir / rel_path.parent
                out_dir.mkdir(parents=True, exist_ok=True)
                cmd = ['pyarmor', 'gen', '--output', str(out_dir), str(rel_path)]
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(self.temp_dir))
                if result.returncode != 0:
                    logger.error(f"PyArmor failed for {rel_path}: {result.stderr}")
                    return False
                logger.info(f"PyArmor completed for {rel_path}")
            # Copy non-Python files and directories, skipping the obfuscated dir itself
            for item in self.temp_dir.rglob('*'):
                rel_path = item.relative_to(self.temp_dir)
                if rel_path.parts and rel_path.parts[0] == 'obfuscated':
                    continue
                dest = obfuscated_dir / rel_path
                if item.is_dir():
                    dest.mkdir(parents=True, exist_ok=True)
                elif item.is_file() and item.suffix != '.py':
                    dest.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, dest)
            # Ensure entry scripts are executable
            for entry in ['run.py', 'run_android.py']:
                entry_path = obfuscated_dir / entry
                if entry_path.exists():
                    entry_path.chmod(entry_path.stat().st_mode | 0o111)
            self.obfuscation_strategy = 'pyarmor'
            self._write_obfuscation_strategy_marker()
            logger.info("✅ PyArmor obfuscation completed")
            return True
        except Exception as e:
            logger.error(f"PyArmor obfuscation failed: {e}")
            return False
    def obfuscate_code_minify(self):
        """Fast obfuscation using python-minifier across protected modules with strict verification."""
        try:
            import python_minifier
        except Exception as e:
            logger.error("python_minifier not available: %s", e)
            return False
        try:
            logger.info("Starting python-minifier obfuscation (strict mode)...")
            obfuscated_dir = self.temp_dir / 'obfuscated'
            if obfuscated_dir.exists():
                shutil.rmtree(obfuscated_dir)
            obfuscated_dir.mkdir(parents=True, exist_ok=True)

            protected_roots = ['web_server', 'auth', 'security', 'utils', 'app', 'app_android']
            entry_files = ['run.py', 'run_android.py', 'main_web_secure.py', 'config.py', 'config_android.py', 'shared_directory_paths_db.py']
            entry_set = set(entry_files)

            processed = 0
            unchanged = []

            def _write_base64_loader(dst_path: Path, original: str):
                import base64 as _b64
                data = _b64.b64encode(original.encode('utf-8')).decode('ascii')
                stub = (
                    "# OBFB64 v1\n"
                    "def __obf_exec__():\n"
                    "    import base64 as _b\n"
                    "    _g = globals()\n"
                    "    _src = _b.b64decode(b'" + data + "').decode('utf-8','ignore')\n"
                    "    exec(compile(_src, __file__, 'exec'), _g)\n"
                    "__obf_exec__(); del __obf_exec__\n"
                )
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                dst_path.write_text(stub, encoding='utf-8')

            def minify_to(src_path: Path, dst_path: Path):
                is_entry = src_path.name in entry_set
                nonlocal processed
                original = src_path.read_text(encoding='utf-8', errors='ignore')
                try:
                    minimized = python_minifier.minify(
                        original,
                        remove_literal_statements=True,   # remove docstrings/literal-only blocks
                        combine_imports=True,
                        hoist_literals=False,
                        remove_annotations=True,
                        rename_locals=True,
                        rename_globals=False,
                        preserve_globals=None,
                    )
                except Exception:
                    try:
                        # Fallback: keep literal-only statements to avoid SyntaxError in edge cases
                        minimized = python_minifier.minify(
                            original,
                            remove_literal_statements=False,
                            combine_imports=True,
                            hoist_literals=False,
                            remove_annotations=True,
                            rename_locals=True,
                            rename_globals=False,
                            preserve_globals=None,
                        )
                    except Exception:
                        if not is_entry:
                            # Final fallback for non-entry modules: base64 loader stub ensures no plain source
                            _write_base64_loader(dst_path, original)
                            processed += 1
                            return
                        else:
                            # For entry scripts, write original to avoid runtime issues (rare)
                            dst_path.parent.mkdir(parents=True, exist_ok=True)
                            dst_path.write_text(original, encoding='utf-8')
                            processed += 1
                            return
                # If minifier succeeded but produced identical output
                if minimized.strip() == original.strip():
                    if not is_entry:
                        _write_base64_loader(dst_path, original)
                        processed += 1
                        return
                    # For entry scripts, allow unchanged minimized output
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                dst_path.write_text(minimized, encoding='utf-8')
                processed += 1

            # Process packages
            for root in protected_roots:
                src_root = self.temp_dir / root
                if not src_root.exists():
                    continue
                for py in src_root.rglob('*.py'):
                    rel = py.relative_to(self.temp_dir)
                    dst = obfuscated_dir / rel
                    try:
                        minify_to(py, dst)
                    except Exception as e:
                        logger.error("Minify failed for %s: %s", rel, e)
                        return False

            # Process entry scripts
            for single in entry_files:
                src_file = self.temp_dir / single
                if not src_file.exists():
                    continue
                dst = obfuscated_dir / single
                try:
                    minify_to(src_file, dst)
                except Exception as e:
                    logger.error("Minify failed for %s: %s", single, e)
                    return False

            # Copy non-Python assets
            for item in self.temp_dir.rglob('*'):
                if item.is_file() and item.suffix != '.py' and 'obfuscated' not in str(item):
                    rel_path = item.relative_to(self.temp_dir)
                    dst = obfuscated_dir / rel_path
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    try:
                        shutil.copy2(item, dst)
                    except Exception as e:
                        logger.warning("Asset copy failed for %s: %s", rel_path, e)

            # Strict verification
            issues = []
            expected = set()
            for r in protected_roots:
                rp = self.temp_dir / r
                if rp.exists():
                    for py in rp.rglob('*.py'):
                        expected.add(str(py.relative_to(self.temp_dir)))
            for single in entry_files:
                if (self.temp_dir / single).exists():
                    expected.add(single)

            present = set()
            for py in obfuscated_dir.rglob('*.py'):
                present.add(str(py.relative_to(obfuscated_dir)))

            missing = sorted(expected - present)
            if missing:
                logger.error("Missing processed files: %s", missing[:10])
                return False
            if unchanged:
                logger.warning("Unchanged files after minification (first 10): %s", unchanged[:10])
                # Note: some very small modules may remain effectively unchanged. Proceeding.

            # Entry scripts executable
            for entry in [obfuscated_dir/'run.py', obfuscated_dir/'run_android.py']:
                if entry.exists():
                    try:
                        entry.chmod(entry.stat().st_mode | 0o111)
                    except Exception:
                        pass

            self.obfuscation_strategy = 'minify'
            self._write_obfuscation_strategy_marker()
            logger.info("✅ python-minifier obfuscation completed (files processed: %d)", processed)
            return True
        except Exception as e:
            logger.error("Code obfuscation failed: %s", e)
            return False

    def _write_obfuscation_strategy_marker(self):
        """Persist the obfuscation strategy for runtime inspection."""
        try:
            marker = self.temp_dir / 'obfuscation_strategy.json'
            marker.write_text(json.dumps({'strategy': self.obfuscation_strategy}, indent=2), encoding='utf-8')
        except Exception as e:
            logger.warning(f"Failed to write obfuscation strategy marker: {e}")

        info_path = self.temp_dir / 'build_info.json'
        if info_path.exists():
            try:
                data = json.loads(info_path.read_text(encoding='utf-8'))
            except Exception:
                data = {}
            data['obfuscation_strategy'] = self.obfuscation_strategy
            try:
                info_path.write_text(json.dumps(data, indent=2), encoding='utf-8')
            except Exception as e:
                logger.warning(f"Failed to update build info with obfuscation strategy: {e}")

    def obfuscate_code(self):
        """Obfuscate ALL Python code using Opy (strict verification)."""
        try:
            logger.info("Starting Opy obfuscation (strict mode)...")

            import tempfile
            from pathlib import Path as _P
            try:
                import opy  # presence check
                import opy.opy  # CLI module
            except Exception as e:
                logger.error("Opy not importable in this venv: %s. Install with: %s -m pip install Opy", e, sys.executable)
                return False

            obfuscated_dir = self.temp_dir / 'obfuscated'
            if obfuscated_dir.exists():
                shutil.rmtree(obfuscated_dir)
            obfuscated_dir.mkdir(parents=True, exist_ok=True)

            # === Opy obfuscation start ===
            protected_roots = ['web_server', 'auth', 'security', 'utils', 'app', 'app_android']
            entry_files = ['run.py', 'run_android.py', 'main_web_secure.py', 'config.py', 'config_android.py']

            with tempfile.TemporaryDirectory() as tmpdir:
                stage_src = _P(tmpdir) / 'src'
                stage_out = _P(tmpdir) / 'out'
                stage_src.mkdir(parents=True, exist_ok=True)
                stage_out.mkdir(parents=True, exist_ok=True)

                # Copy protected roots and entry scripts under single src dir
                for p in protected_roots:
                    src_path = self.temp_dir / p
                    if src_path.exists():
                        shutil.copytree(src_path, stage_src / p, dirs_exist_ok=True)
                for single in entry_files:
                    src_file = self.temp_dir / single
                    if src_file.exists():
                        (stage_src / single).parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(src_file, stage_src / single)

                # Minimal Opy config to avoid known NameError in some versions
                cfg_path = stage_src / 'opy_config.txt'
                cfg_path.write_text('\n'.join([
                    'pep8Comments = False',
                    'pep8_comments = False',
                    'obfuscateStrings = False',
                    'obfuscateDocStrings = False',
                ]), encoding='utf-8')

                # Run Opy: opy <src_dir> <out_dir> <config_file>
                import runpy
                backup_argv = sys.argv[:]
                sys.argv = ['opy', str(stage_src), str(stage_out), str(cfg_path)]
                try:
                    runpy.run_module('opy.opy', run_name='__main__')
                except SystemExit as e:
                    if e.code not in (0, None):
                        logger.error("Opy exited with code %s", e.code)
                        return False
                except Exception as e:
                    logger.error("Opy execution failed: %s", e)
                    return False
                finally:
                    sys.argv = backup_argv

                # Copy results back
                for p in protected_roots:
                    sp = stage_out / p
                    if sp.exists():
                        shutil.copytree(sp, obfuscated_dir / p, dirs_exist_ok=True)
                for single in entry_files:
                    sp = stage_out / single
                    if sp.exists():
                        (obfuscated_dir / single).parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(sp, obfuscated_dir / single)

            # Copy non-Python assets
            for item in self.temp_dir.rglob('*'):
                if item.is_file() and item.suffix != '.py' and 'obfuscated' not in str(item):
                    rel_path = item.relative_to(self.temp_dir)
                    dst = obfuscated_dir / rel_path
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    try:
                        shutil.copy2(item, dst)
                    except Exception as e:
                        logger.warning("Asset copy failed for %s: %s", rel_path, e)

            # Strict verification: ensure every target .py file exists and differs from original
            issues = []
            processed = 0
            for p in protected_roots:
                src_root = self.temp_dir / p
                if not src_root.exists():
                    continue
                for py in src_root.rglob('*.py'):
                    rel = py.relative_to(self.temp_dir)
                    obf = obfuscated_dir / rel
                    if not obf.exists():
                        issues.append(f"missing:{rel}")
                        continue
                    try:
                        orig_txt = py.read_text(encoding='utf-8', errors='ignore')
                        obf_txt = obf.read_text(encoding='utf-8', errors='ignore')
                        if obf_txt.strip() == orig_txt.strip():
                            issues.append(f"unchanged:{rel}")
                        else:
                            processed += 1
                    except Exception as e:
                        issues.append(f"error:{rel}:{e}")
            for single in entry_files:
                src = self.temp_dir / single
                if src.exists():
                    rel = src.relative_to(self.temp_dir)
                    obf = obfuscated_dir / rel
                    if not obf.exists():
                        issues.append(f"missing:{rel}")
                    else:
                        try:
                            orig_txt = src.read_text(encoding='utf-8', errors='ignore')
                            obf_txt = obf.read_text(encoding='utf-8', errors='ignore')
                            if obf_txt.strip() == orig_txt.strip():
                                issues.append(f"unchanged:{rel}")
                            else:
                                processed += 1
                        except Exception as e:
                            issues.append(f"error:{rel}:{e}")

            if issues:
                logger.error("Opy verification issues: %s", issues[:10])
                return False

            # Ensure entry scripts executable
            for entry in [obfuscated_dir/'run.py', obfuscated_dir/'run_android.py']:
                if entry.exists():
                    try:
                        m = entry.stat().st_mode
                        entry.chmod(m | 0o111)
                    except Exception:
                        pass

            logger.info("✅ Opy obfuscation completed (files processed: %d)", processed)
            return True

            # Copy non-Python assets
            for item in self.temp_dir.rglob('*'):
                if item.is_file() and item.suffix != '.py' and 'obfuscated' not in str(item):
                    rel_path = item.relative_to(self.temp_dir)
                    dst = obfuscated_dir / rel_path
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    try:
                        shutil.copy2(item, dst)
                    except Exception as e:
                        logger.warning("Asset copy failed for %s: %s", rel_path, e)

            # Strict verification: ensure every target .py file exists and differs from original
            issues = []
            processed = 0
            for p in protected_roots:
                src_root = self.temp_dir / p
                if not src_root.exists():
                    continue
                for py in src_root.rglob('*.py'):
                    rel = py.relative_to(self.temp_dir)
                    obf = obfuscated_dir / rel
                    if not obf.exists():
                        issues.append(f"missing:{rel}")
                        continue
                    try:
                        orig_txt = py.read_text(encoding='utf-8', errors='ignore')
                        obf_txt = obf.read_text(encoding='utf-8', errors='ignore')
                        if obf_txt.strip() == orig_txt.strip():
                            issues.append(f"unchanged:{rel}")
                        else:
                            processed += 1
                    except Exception as e:
                        issues.append(f"error:{rel}:{e}")
            for single in entry_files:
                src = self.temp_dir / single
                if src.exists():
                    rel = src.relative_to(self.temp_dir)
                    obf = obfuscated_dir / rel
                    if not obf.exists():
                        issues.append(f"missing:{rel}")
                    else:
                        try:
                            orig_txt = src.read_text(encoding='utf-8', errors='ignore')
                            obf_txt = obf.read_text(encoding='utf-8', errors='ignore')
                            if obf_txt.strip() == orig_txt.strip():
                                issues.append(f"unchanged:{rel}")
                            else:
                                processed += 1
                        except Exception as e:
                            issues.append(f"error:{rel}:{e}")

            if issues:
                logger.error("Opy verification issues: %s", issues[:10])
                return False

            # Ensure entry scripts executable
            for entry in [obfuscated_dir/'run.py', obfuscated_dir/'run_android.py']:
                if entry.exists():
                    try:
                        m = entry.stat().st_mode
                        entry.chmod(m | 0o111)
                    except Exception:
                        pass

            logger.info("✅ Opy obfuscation completed (files processed: %d)", processed)
            return True

        except Exception as e:
            logger.error(f"Code obfuscation failed: {e}")
            return False

        except Exception as e:
            logger.error(f"Code obfuscation failed: {e}")
            return False

        except Exception as e:
            logger.error(f"Code obfuscation failed: {e}")
            return False

    def create_pyinstaller_spec(self):
        """Create PyInstaller spec file for the web-based app"""
        try:
            logger.info("Creating PyInstaller spec file...")

            # Determine source directory (obfuscated if available, otherwise temp)
            obfuscated_dir = self.temp_dir / 'obfuscated'
            source_dir = obfuscated_dir if obfuscated_dir.exists() else self.temp_dir

            main_script = source_dir / 'main_web_secure.py'
            # Runtime hook to set env: AUTO_OPEN_BROWSER=0 inside the bundle
            runtime_hook = self.temp_dir / 'runtime_set_env.py'
            with open(runtime_hook, 'w') as rh:
                rh.write('import os\n')
                rh.write("os.environ.setdefault('AUTO_OPEN_BROWSER', '0')\n")
                rh.write("os.environ.setdefault('FLASK_ENV', 'production')\n")
                rh.write(f"os.environ.setdefault('SECURE_BUILD_OBFUSCATION', '{self.obfuscation_strategy}')\n")
            # Ensure the entry script exists; if missing, copy it now
            if not main_script.exists():
                fallback_src = self.secure_app_dir / 'main_web_secure.py'
                if fallback_src.exists():
                    shutil.copy2(fallback_src, main_script)
                    logger.info("Entry script was missing; copied main_web_secure.py into temp_build_final")
                else:
                    raise FileNotFoundError(f"Missing entry script: {main_script}")


            spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Build configuration
block_cipher = None
source_dir = Path(r"{source_dir}")

# Inject environment at runtime (AUTO_OPEN_BROWSER=0)
runtime_hook = Path(r"{runtime_hook}")

a = Analysis(
    [r"{main_script}"],
    pathex=[str(source_dir)],
    binaries=[],
    datas=[
        (str(source_dir / "templates"), "templates"),
        (str(source_dir / "web_server"), "web_server"),
        (str(source_dir / "auth"), "auth"),
        (str(source_dir / "security"), "security"),
        (str(source_dir / "utils"), "utils"),
        (str(source_dir / "app"), "app"),
        (str(source_dir / "app_android"), "app_android"),
        (str(source_dir / "run.py"), "."),
        (str(source_dir / "run_android.py"), "."),
        (str(source_dir / "config.py"), "."),
        (str(source_dir / "config_android.py"), "."),
        (str(source_dir / "shared_directory_paths_db.py"), "."),
        (str(source_dir / ".env.example"), "."),
        (str(source_dir / ".env"), "."),
        (str(source_dir / "requirements.txt"), "."),
        (str(source_dir / "build_info.json"), "."),
    ],
    hiddenimports=[
        'flask',
        'supabase',
        'pathlib',
        'socket',
        'subprocess',
        'threading',
        'webbrowser',
        'logging',
        'json',
        'datetime',
        'secrets',
        'utils.port_manager',
        'utils.ai_integration',
        'web_server.app',
        'auth.license_manager',
        'shared_directory_paths_db',
        'config',
        'config_android',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[str(runtime_hook)],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.build_config["app_name"]}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''

            spec_file = self.temp_dir / f'{self.build_config["app_name"]}.spec'
            with open(spec_file, 'w') as f:
                f.write(spec_content)

            logger.info(f"✅ PyInstaller spec file created: {spec_file}")
            return spec_file

        except Exception as e:
            logger.error(f"Failed to create PyInstaller spec: {e}")
            return None

    def build_executable(self, spec_file):
        """Build executable using PyInstaller"""
        try:
            logger.info("Building executable with PyInstaller...")

            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--clean',
                '--noconfirm',
                '--distpath', str(self.dist_dir),
                '--workpath', str(self.build_dir),
                str(spec_file)
            ]

            logger.info(f"Running: {' '.join(cmd)}")

            result = subprocess.run(cmd, cwd=str(self.temp_dir), capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("✅ Executable built successfully")

                # Copy launcher scripts to dist directory
                app_dist_dir = self.dist_dir / self.build_config["app_name"]

                # Copy start scripts
                start_bat = self.secure_app_dir / 'start.bat'
                start_sh = self.secure_app_dir / 'start.sh'

                # Only attempt to copy start scripts if dist directory is a folder (one-folder mode)
                if app_dist_dir.is_dir():
                    if start_bat.exists():
                        shutil.copy2(start_bat, app_dist_dir / 'start.bat')
                        logger.info("Copied start.bat to distribution")

                    if start_sh.exists():
                        shutil.copy2(start_sh, app_dist_dir / 'start.sh')
                        os.chmod(app_dist_dir / 'start.sh', 0o755)
                        logger.info("Copied start.sh to distribution")
                else:
                    logger.info("Single-file bundle produced; skipping start script copy")

                return True
            else:
                logger.error(f"PyInstaller failed: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Failed to build executable: {e}")
            return False

    def create_distribution_package(self):
        """Create final distribution package"""
        try:
            logger.info("Creating distribution package...")

            # In one-file mode, app_dist_dir is a file, not a folder. Handle both.
            app_dist_dir = self.dist_dir / self.build_config["app_name"]
            if not app_dist_dir.exists():
                logger.error("Distribution output not found")
                return False

            # If a single-file binary was produced, create a folder to host README and env example
            if app_dist_dir.is_file():
                bundle_dir = self.dist_dir / f"{self.build_config['app_name']}_bundle"
                bundle_dir.mkdir(parents=True, exist_ok=True)
                # Move/copy the binary into the bundle folder
                bin_dest = bundle_dir / self.build_config["app_name"]
                try:
                    shutil.copy2(app_dist_dir, bin_dest)
                except Exception:
                    pass
                app_dist_dir = bundle_dir

            # Ensure launch scripts ship alongside the binary for every platform
            start_sh = self.secure_app_dir / 'start.sh'
            if start_sh.exists():
                shutil.copy2(start_sh, app_dist_dir / 'start.sh')
                os.chmod(app_dist_dir / 'start.sh', 0o755)
                logger.info("Added start.sh to distribution")
            start_bat = self.secure_app_dir / 'start.bat'
            if start_bat.exists():
                shutil.copy2(start_bat, app_dist_dir / 'start.bat')
                logger.info("Added start.bat to distribution")

            # Bring over the prepared runtime assets (Python/Node environments, device data)
            runtime_src = self.temp_dir / self.runtime_root_name
            if runtime_src.exists():
                runtime_dst = app_dist_dir / self.runtime_root_name
                if runtime_dst.exists():
                    shutil.rmtree(runtime_dst)
                shutil.copytree(runtime_src, runtime_dst, dirs_exist_ok=True)
                logger.info("Included runtime assets in distribution")

            # Include sanitized database snapshots so the app boots offline
            db_src = self.temp_dir / 'db-data'
            if db_src.exists():
                db_dst = app_dist_dir / 'db-data'
                shutil.copytree(db_src, db_dst, dirs_exist_ok=True)
                logger.info("Included sanitized db-data directory in distribution")

            # Create README for distribution
            readme_content = f'''# {self.build_config["app_name"]} v{self.build_config["version"]}

{self.build_config["description"]}

## Features
{chr(10).join(f"- {feature}" for feature in self.build_config["features"])}

## Quick Start

### Windows
1. Double-click `start.bat` or run `{self.build_config["app_name"]}.exe`
2. The web interface will open automatically in your browser
3. Log in with your Supabase credentials

### macOS/Linux
1. Run `./start.sh` or `./{self.build_config["app_name"]}`
2. The web interface will open automatically in your browser
3. Log in with your Supabase credentials

## Configuration

1. Copy `.env.example` to `.env`
2. Configure your Supabase credentials:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

## Platform Support

- **Windows**: Android automation only
- **macOS**: Both iOS and Android automation
- **Linux**: Android automation only

## Build Information

- Version: {self.build_config["version"]}
- Build Date: {self.build_config["build_date"]}
- Author: {self.build_config["author"]}

## Support

For support and documentation, please refer to the project repository.
'''

            with open(app_dist_dir / 'README.md', 'w') as f:
                f.write(readme_content)

            # Copy .env.example if it exists
            env_example = self.secure_app_dir / '.env.example'
            if env_example.exists():
                shutil.copy2(env_example, app_dist_dir / '.env.example')

            logger.info("✅ Distribution package created successfully")
            logger.info(f"📦 Distribution location: {app_dist_dir}")

            return True

        except Exception as e:
            logger.error(f"Failed to create distribution package: {e}")
            return False

    def build(self):
        """Main build process"""
        try:
            logger.info(f"🚀 Starting build process for {self.build_config['app_name']} v{self.build_config['version']}")

            # Check dependencies
            if not self.check_dependencies():
                return False

            # Prepare source files
            if not self.prepare_source_files():
                return False

            # Obfuscate code using requested strategy
            obfuscation_done = False
            if self.use_pyarmor and self.pyarmor_available:
                if self.obfuscate_with_pyarmor():
                    obfuscation_done = True
                else:
                    logger.warning("PyArmor obfuscation failed; falling back to python-minifier")

            if not obfuscation_done:
                if not self.obfuscate_code_minify():
                    return False

            # Create PyInstaller spec
            spec_file = self.create_pyinstaller_spec()
            if not spec_file:
                return False

            # Build executable
            if not self.build_executable(spec_file):
                return False

            # Create distribution package
            if not self.create_distribution_package():
                return False

            logger.info("🎉 Build completed successfully!")
            logger.info(f"📦 Distribution package: {self.dist_dir / self.build_config['app_name']}")

            return True

        except Exception as e:
            logger.error(f"Build process failed: {e}")
            return False

def parse_args():
    parser = argparse.ArgumentParser(description='Build the secure mobile automation distribution package.')
    parser.add_argument('--pyarmor', action='store_true', help='Use PyArmor for obfuscation when available.')
    return parser.parse_args()


def main():
    """Main entry point"""
    try:
        args = parse_args()
        builder = FinalSecureAppBuilder(use_pyarmor=args.pyarmor)
        success = builder.build()

        if success:
            print("\n" + "="*60)
            print("🎉 BUILD COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"📦 Distribution: {builder.dist_dir / builder.build_config['app_name']}")
            print("🚀 Ready for deployment!")
            print("="*60)
            return 0
        else:
            print("\n" + "="*60)
            print("❌ BUILD FAILED!")
            print("="*60)
            print("Check the logs above for error details.")
            print("="*60)
            return 1

    except Exception as e:
        logger.error(f"Critical build error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
