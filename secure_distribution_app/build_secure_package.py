#!/usr/bin/env python3
"""
Secure Package Builder with PyArmor Obfuscation
Creates obfuscated, license-protected distribution packages
"""

import os
import sys
import shutil
import subprocess
import platform
import json
from pathlib import Path
from datetime import datetime, timedelta
import argparse

class SecurePackageBuilder:
    """Build secure, obfuscated packages with license protection"""
    
    def __init__(self, source_dir=None, output_dir=None):
        self.source_dir = Path(source_dir or Path(__file__).parent.parent)
        self.output_dir = Path(output_dir or self.source_dir / "dist")
        self.temp_dir = self.output_dir / "temp"
        self.obfuscated_dir = self.output_dir / "obfuscated"
        
        # Package configuration
        self.package_name = "MobileAppAutomation"
        self.version = "1.0.0"
        self.platforms = ["windows", "macos", "linux"]
        
        # Files to obfuscate (critical source files)
        self.critical_files = [
            "secure_distribution_app/auth/license_manager.py",
            "secure_distribution_app/auth/session_manager.py", 
            "secure_distribution_app/web_server/app.py",
            "app/app.py",
            "run.py",
            "run_python.py"
        ]
        
        # Files to include without obfuscation
        self.include_files = [
            "requirements.txt",
            "README.md",
            "LICENSE",
            "config.py"
        ]
        
        # Directories to include
        self.include_dirs = [
            "app/templates",
            "app/static", 
            "secure_distribution_app/templates",
            "utils",
            "tests"
        ]
        
    def setup_directories(self):
        """Create necessary directories"""
        print("🗂️  Setting up build directories...")
        
        # Clean and create output directories
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        
        self.output_dir.mkdir(parents=True)
        self.temp_dir.mkdir(parents=True)
        self.obfuscated_dir.mkdir(parents=True)
        
        print(f"✅ Build directories created: {self.output_dir}")
    
    def install_pyarmor(self):
        """Install PyArmor if not available"""
        print("🔧 Checking PyArmor installation...")
        
        try:
            subprocess.run(["pyarmor", "--version"], check=True, capture_output=True)
            print("✅ PyArmor is already installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("📦 Installing PyArmor...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyarmor"], check=True)
            print("✅ PyArmor installed successfully")
    
    def create_license_config(self):
        """Create PyArmor license configuration"""
        print("🔑 Creating license configuration...")
        
        # Create license configuration for runtime validation
        license_config = {
            "license_type": "runtime",
            "expired_date": (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d"),
            "bind_hardware": True,
            "bind_network": False,
            "check_interval": 300,  # 5 minutes
            "grace_period": 7,      # 7 days
            "features": [
                "ios_automation",
                "android_automation", 
                "web_automation",
                "api_testing"
            ]
        }
        
        config_file = self.temp_dir / "license_config.json"
        with open(config_file, 'w') as f:
            json.dump(license_config, f, indent=2)
        
        print(f"✅ License configuration created: {config_file}")
        return config_file
    
    def obfuscate_critical_files(self):
        """Obfuscate critical Python files using PyArmor"""
        print("🔒 Obfuscating critical source files...")
        
        # Create PyArmor project
        project_dir = self.temp_dir / "pyarmor_project"
        project_dir.mkdir(exist_ok=True)
        
        # Initialize PyArmor project
        subprocess.run([
            "pyarmor", "init", 
            "--src", str(self.source_dir),
            "--entry", "run.py",
            str(project_dir)
        ], check=True)
        
        # Configure advanced obfuscation options
        pyarmor_config = {
            "obf_mod": 1,           # Obfuscate module
            "obf_code": 1,          # Obfuscate code object
            "wrap_mode": 1,         # Wrap mode
            "obf_name": 1,          # Obfuscate function/class names
            "advanced": 2,          # Advanced mode
            "restrict_mode": 4,     # Restrict mode (highest security)
            "bootstrap_code": 2,    # Bootstrap protection
            "cross_protection": 1   # Cross protection
        }
        
        # Create configuration file
        config_file = project_dir / ".pyarmor_config"
        with open(config_file, 'w') as f:
            for key, value in pyarmor_config.items():
                f.write(f"{key} = {value}\n")
        
        # Obfuscate each critical file
        for file_path in self.critical_files:
            source_file = self.source_dir / file_path
            if source_file.exists():
                print(f"  🔐 Obfuscating: {file_path}")
                
                # Create output directory structure
                output_file = self.obfuscated_dir / file_path
                output_file.parent.mkdir(parents=True, exist_ok=True)
                
                # Obfuscate the file
                subprocess.run([
                    "pyarmor", "obfuscate",
                    "--output", str(output_file.parent),
                    "--exact",
                    "--advanced", "2",
                    "--restrict", "4",
                    str(source_file)
                ], check=True)
                
                print(f"    ✅ Obfuscated: {output_file}")
            else:
                print(f"    ⚠️  File not found: {file_path}")
        
        print("✅ Critical files obfuscated successfully")
    
    def copy_supporting_files(self):
        """Copy supporting files and directories"""
        print("📁 Copying supporting files...")
        
        # Copy individual files
        for file_path in self.include_files:
            source_file = self.source_dir / file_path
            if source_file.exists():
                dest_file = self.obfuscated_dir / file_path
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source_file, dest_file)
                print(f"  📄 Copied: {file_path}")
        
        # Copy directories
        for dir_path in self.include_dirs:
            source_dir = self.source_dir / dir_path
            if source_dir.exists():
                dest_dir = self.obfuscated_dir / dir_path
                shutil.copytree(source_dir, dest_dir, dirs_exist_ok=True)
                print(f"  📁 Copied directory: {dir_path}")
        
        print("✅ Supporting files copied successfully")
    
    def create_runtime_protection(self):
        """Create runtime license protection"""
        print("🛡️  Creating runtime protection...")
        
        # Create protected launcher script
        launcher_content = '''#!/usr/bin/env python3
"""
Protected Launcher for Mobile App Automation Tool
Includes runtime license validation and tamper protection
"""

import sys
import os
import hashlib
import platform
from pathlib import Path

# Runtime license validation
def validate_runtime_license():
    """Validate license at runtime"""
    try:
        # Import obfuscated license manager
        from secure_distribution_app.auth.license_manager import LicenseManager
        
        # Initialize license manager
        license_manager = LicenseManager()
        
        # Generate hardware fingerprint
        fingerprint = license_manager.generate_hardware_fingerprint()
        
        # Validate license (this will be obfuscated)
        validation_result = license_manager.validate_license("runtime_check")
        
        if not validation_result.get('valid'):
            print("❌ License validation failed:", validation_result.get('error'))
            sys.exit(1)
        
        return True
        
    except Exception as e:
        print(f"❌ Runtime protection error: {e}")
        sys.exit(1)

# Tamper detection
def check_file_integrity():
    """Check critical file integrity"""
    critical_files = [
        "secure_distribution_app/auth/license_manager.py",
        "secure_distribution_app/web_server/app.py"
    ]
    
    for file_path in critical_files:
        if not Path(file_path).exists():
            print(f"❌ Critical file missing: {file_path}")
            sys.exit(1)
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Mobile App Automation Tool...")
    
    # Runtime validations
    validate_runtime_license()
    check_file_integrity()
    
    # Import and run main application
    if len(sys.argv) > 1 and sys.argv[1] == "--android":
        from run_python import main
    else:
        from run import main
    
    main()
'''
        
        launcher_file = self.obfuscated_dir / "secure_launcher.py"
        with open(launcher_file, 'w') as f:
            f.write(launcher_content)
        
        # Obfuscate the launcher
        subprocess.run([
            "pyarmor", "obfuscate",
            "--output", str(self.obfuscated_dir),
            "--exact",
            "--advanced", "2",
            "--restrict", "4",
            str(launcher_file)
        ], check=True)
        
        print("✅ Runtime protection created")
    
    def create_installation_script(self):
        """Create installation and setup scripts"""
        print("📦 Creating installation scripts...")
        
        # Create setup script
        setup_content = '''#!/usr/bin/env python3
"""
Setup Script for Mobile App Automation Tool
Handles dependency installation and initial configuration
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "-r", str(requirements_file)
        ], check=True)
    
    print("✅ Dependencies installed")

def setup_environment():
    """Setup environment and configuration"""
    print("⚙️  Setting up environment...")
    
    # Create necessary directories
    directories = ["logs", "screenshots", "test_results"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("✅ Environment setup complete")

if __name__ == "__main__":
    print("🚀 Mobile App Automation Tool Setup")
    print("=" * 50)
    
    try:
        install_dependencies()
        setup_environment()
        
        print("\\n✅ Setup completed successfully!")
        print("\\n🎯 To start the application:")
        print("   python secure_launcher.py")
        
    except Exception as e:
        print(f"\\n❌ Setup failed: {e}")
        sys.exit(1)
'''
        
        setup_file = self.obfuscated_dir / "setup.py"
        with open(setup_file, 'w') as f:
            f.write(setup_content)
        
        print("✅ Installation scripts created")
    
    def build_platform_packages(self):
        """Build platform-specific packages"""
        print("📦 Building platform-specific packages...")
        
        for platform_name in self.platforms:
            print(f"  🏗️  Building {platform_name} package...")
            
            platform_dir = self.output_dir / f"{self.package_name}_{platform_name}"
            platform_dir.mkdir(exist_ok=True)
            
            # Copy obfuscated files
            shutil.copytree(self.obfuscated_dir, platform_dir / "app", dirs_exist_ok=True)
            
            # Create platform-specific launcher
            if platform_name == "windows":
                launcher_ext = ".bat"
                launcher_content = f'''@echo off
echo Starting {self.package_name}...
python app/secure_launcher.py %*
'''
            else:
                launcher_ext = ".sh"
                launcher_content = f'''#!/bin/bash
echo "Starting {self.package_name}..."
python3 app/secure_launcher.py "$@"
'''
            
            launcher_file = platform_dir / f"start{launcher_ext}"
            with open(launcher_file, 'w') as f:
                f.write(launcher_content)
            
            # Make executable on Unix systems
            if platform_name != "windows":
                os.chmod(launcher_file, 0o755)
            
            # Create archive
            archive_name = f"{self.package_name}_{platform_name}_v{self.version}"
            shutil.make_archive(
                str(self.output_dir / archive_name),
                'zip',
                str(platform_dir)
            )
            
            print(f"    ✅ {platform_name} package: {archive_name}.zip")
        
        print("✅ Platform packages built successfully")
    
    def build(self):
        """Build complete secure package"""
        print(f"🚀 Building secure package: {self.package_name} v{self.version}")
        print("=" * 60)
        
        try:
            self.setup_directories()
            self.install_pyarmor()
            self.create_license_config()
            self.obfuscate_critical_files()
            self.copy_supporting_files()
            self.create_runtime_protection()
            self.create_installation_script()
            self.build_platform_packages()
            
            print("\n🎉 Secure package build completed successfully!")
            print(f"📁 Output directory: {self.output_dir}")
            print("\n📦 Generated packages:")
            for platform_name in self.platforms:
                package_file = f"{self.package_name}_{platform_name}_v{self.version}.zip"
                print(f"  • {package_file}")
            
        except Exception as e:
            print(f"\n❌ Build failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Build secure, obfuscated packages")
    parser.add_argument("--source", help="Source directory", default=None)
    parser.add_argument("--output", help="Output directory", default=None)
    parser.add_argument("--platforms", nargs="+", help="Target platforms", 
                       choices=["windows", "macos", "linux"], default=["windows", "macos", "linux"])
    
    args = parser.parse_args()
    
    builder = SecurePackageBuilder(args.source, args.output)
    builder.platforms = args.platforms
    builder.build()

if __name__ == "__main__":
    main()
