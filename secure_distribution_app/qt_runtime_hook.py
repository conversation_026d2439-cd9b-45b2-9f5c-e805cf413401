# Runtime hook to configure Qt plugins and Playwright browsers in frozen app
import os
import sys
from pathlib import Path

try:
    base = Path(getattr(sys, '_MEIPASS', Path(__file__).parent))
except Exception:
    base = Path(__file__).parent

# Set Qt plugin path if bundled
for qt_dir in ['PyQt5/Qt/plugins', 'PyQt6/Qt/plugins']:
    plugins_path = base / qt_dir
    if plugins_path.exists():
        os.environ['QT_PLUGIN_PATH'] = str(plugins_path)
        # Common: ensure platform plugin path is present
        plat_dir = plugins_path / 'platforms'
        if plat_dir.exists():
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(plat_dir)
        break

# Set Playwright browsers path if bundled
pw_dir = base / 'playwright-browsers'
if pw_dir.exists():
    os.environ['PLAYWRIGHT_BROWSERS_PATH'] = str(pw_dir)

