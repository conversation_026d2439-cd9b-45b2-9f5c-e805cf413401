#!/usr/bin/env python3
"""
Helper Function Extractor

Extracts helper functions and utility code from app.py into organized modules.
"""

import re
from pathlib import Path
from typing import Dict, List

class HelperExtractor:
    """Extracts helper functions and organizes them into modules"""
    
    # Helper function patterns to identify
    HELPER_PATTERNS = {
        'config_helpers': [
            'get_configured_directory',
            'load_config',
            'save_config',
            'validate_folder_configuration',
            'initialize_report_directory',
            'encode_image_to_base64',
        ],
        'directory_helpers': [
            'get_reports_directory',
            'get_temp_directory',
        ],
    }
    
    def __init__(self, source_file):
        self.source_file = Path(source_file)
        with open(self.source_file, 'r') as f:
            self.content = f.read()
        self.lines = self.content.split('\n')
    
    def find_function(self, func_name: str) -> tuple:
        """
        Find a function by name and return its start and end lines
        Returns: (start_line, end_line) or None if not found
        """
        for i, line in enumerate(self.lines):
            if re.match(rf'^def\s+{func_name}\s*\(', line):
                # Found the function, now find its end
                end_line = self._find_function_end(i)
                return (i, end_line)
        return None
    
    def _find_function_end(self, start_line: int) -> int:
        """Find the end line of a function"""
        func_line = self.lines[start_line]
        func_indent = len(func_line) - len(func_line.lstrip())
        
        i = start_line + 1
        while i < len(self.lines):
            line = self.lines[i]
            
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                i += 1
                continue
            
            # Check indentation
            line_indent = len(line) - len(line.lstrip())
            
            # If we find a line with same or less indentation, function has ended
            if line_indent <= func_indent and line.strip():
                return i - 1
            
            i += 1
        
        return len(self.lines) - 1
    
    def extract_function_code(self, start_line: int, end_line: int) -> str:
        """Extract the code for a function"""
        return '\n'.join(self.lines[start_line:end_line + 1])
    
    def extract_all_helpers(self) -> Dict[str, str]:
        """
        Extract all helper functions and organize them into modules
        Returns: Dict of {module_name: module_content}
        """
        modules = {}
        
        for module_name, func_names in self.HELPER_PATTERNS.items():
            functions = []
            
            for func_name in func_names:
                result = self.find_function(func_name)
                if result:
                    start, end = result
                    code = self.extract_function_code(start, end)
                    functions.append({
                        'name': func_name,
                        'code': code,
                        'start': start,
                        'end': end
                    })
            
            if functions:
                module_content = self._generate_module_content(
                    module_name, functions
                )
                modules[module_name] = module_content
        
        return modules
    
    def _generate_module_content(self, module_name: str, functions: List[Dict]) -> str:
        """Generate the content for a helper module"""
        content = []
        
        # Header
        content.append('"""')
        content.append(f'{module_name.replace("_", " ").title()}')
        content.append('')
        content.append(f'This module contains helper functions for {module_name.replace("_", " ")}.')
        content.append('"""')
        content.append('')
        
        # Imports
        content.append('import os')
        content.append('import json')
        content.append('import logging')
        content.append('from pathlib import Path')
        content.append('from datetime import datetime')
        content.append('')
        content.append('# Set up logger')
        content.append('logger = logging.getLogger(__name__)')
        content.append('')
        
        # Add imports from app_android if needed
        if 'config' in module_name or 'directory' in module_name:
            content.append('# Import configuration')
            content.append('try:')
            content.append('    import config_android as config')
            content.append('    from app_android.utils.directory_paths_db import directory_paths_db')
            content.append('except ImportError:')
            content.append('    config = None')
            content.append('    directory_paths_db = None')
            content.append('')
        
        content.append(f'# {"="*76}')
        content.append(f'# HELPER FUNCTIONS')
        content.append(f'# {"="*76}')
        content.append('')
        
        # Add all functions
        for func in functions:
            content.append(func['code'])
            content.append('')
            content.append('')
        
        return '\n'.join(content)

if __name__ == '__main__':
    # Test the extractor
    extractor = HelperExtractor('app_android/app.py')
    helpers = extractor.extract_all_helpers()
    
    print(f"Found {len(helpers)} helper modules:")
    for module_name, content in helpers.items():
        lines = len(content.split('\n'))
        print(f"  {module_name}: {lines} lines")

