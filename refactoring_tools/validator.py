#!/usr/bin/env python3
"""
Refactoring Validator

Validates that the refactored code maintains functionality and meets requirements.
"""

import os
import ast
import sys
from pathlib import Path
from typing import Dict, List

class RefactoringValidator:
    """Validates refactored code"""
    
    MAX_LINES_PER_FILE = 2000
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_syntax(self, file_path: Path) -> bool:
        """Validate Python syntax of a file"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            ast.parse(code)
            return True
        except SyntaxError as e:
            self.errors.append(f"Syntax error in {file_path}: {e}")
            return False
        except Exception as e:
            self.errors.append(f"Error parsing {file_path}: {e}")
            return False
    
    def validate_line_count(self, file_path: Path) -> bool:
        """Validate that file doesn't exceed maximum line count"""
        try:
            with open(file_path, 'r') as f:
                lines = len(f.readlines())
            
            if lines > self.MAX_LINES_PER_FILE:
                self.warnings.append(
                    f"{file_path} has {lines} lines (max: {self.MAX_LINES_PER_FILE})"
                )
                return False
            return True
        except Exception as e:
            self.errors.append(f"Error counting lines in {file_path}: {e}")
            return False
    
    def validate_imports(self, file_path: Path) -> bool:
        """Validate that all imports can be resolved"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        # Check if module exists
                        module_name = alias.name
                        # Skip validation for now - would need full environment
                        pass
                elif isinstance(node, ast.ImportFrom):
                    # Check if module exists
                    module_name = node.module
                    # Skip validation for now - would need full environment
                    pass
            
            return True
        except Exception as e:
            self.errors.append(f"Error validating imports in {file_path}: {e}")
            return False
    
    def validate_directory(self, directory: Path) -> Dict[str, bool]:
        """Validate all Python files in a directory"""
        results = {
            'syntax': True,
            'imports': True,
            'line_counts': True,
            'errors': [],
            'warnings': []
        }
        
        # Find all Python files
        python_files = list(directory.rglob('*.py'))
        
        for file_path in python_files:
            # Skip __pycache__ and other generated files
            if '__pycache__' in str(file_path):
                continue
            
            # Validate syntax
            if not self.validate_syntax(file_path):
                results['syntax'] = False
            
            # Validate imports
            if not self.validate_imports(file_path):
                results['imports'] = False
            
            # Validate line count
            if not self.validate_line_count(file_path):
                results['line_counts'] = False
        
        results['errors'] = self.errors
        results['warnings'] = self.warnings
        
        return results
    
    def validate_all(self, app_dir: Path) -> Dict[str, bool]:
        """Validate the entire refactored application"""
        self.errors = []
        self.warnings = []
        
        return self.validate_directory(app_dir)
    
    def check_backward_compatibility(self, original_file: Path, new_file: Path) -> bool:
        """
        Check that the new file maintains backward compatibility
        by ensuring all exported symbols are still available
        """
        try:
            # Parse original file
            with open(original_file, 'r') as f:
                original_code = f.read()
            original_tree = ast.parse(original_code)
            
            # Parse new file
            with open(new_file, 'r') as f:
                new_code = f.read()
            new_tree = ast.parse(new_code)
            
            # Extract function and class names from original
            original_symbols = set()
            for node in ast.walk(original_tree):
                if isinstance(node, ast.FunctionDef):
                    original_symbols.add(node.name)
                elif isinstance(node, ast.ClassDef):
                    original_symbols.add(node.name)
            
            # Extract function and class names from new
            new_symbols = set()
            for node in ast.walk(new_tree):
                if isinstance(node, ast.FunctionDef):
                    new_symbols.add(node.name)
                elif isinstance(node, ast.ClassDef):
                    new_symbols.add(node.name)
            
            # Check for missing symbols
            missing = original_symbols - new_symbols
            if missing:
                self.warnings.append(
                    f"Missing symbols in new file: {', '.join(missing)}"
                )
                return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"Error checking backward compatibility: {e}")
            return False

if __name__ == '__main__':
    # Test the validator
    validator = RefactoringValidator()
    
    # Validate a directory
    results = validator.validate_directory(Path('app_android'))
    
    print("Validation Results:")
    print(f"  Syntax: {'✓ PASS' if results['syntax'] else '✗ FAIL'}")
    print(f"  Imports: {'✓ PASS' if results['imports'] else '✗ FAIL'}")
    print(f"  Line Counts: {'✓ PASS' if results['line_counts'] else '✗ FAIL'}")
    
    if results['errors']:
        print("\nErrors:")
        for error in results['errors']:
            print(f"  ✗ {error}")
    
    if results['warnings']:
        print("\nWarnings:")
        for warning in results['warnings']:
            print(f"  ⚠ {warning}")

