#!/usr/bin/env python3
"""
Refactor Utils Files

A pragmatic approach to refactoring large utility files:
1. For class-based files: Create a package structure with the class split into mixins
2. For function-based files: Group functions by category into modules
3. Maintain 100% backward compatibility
"""

import os
import re
import sys
import shutil
from pathlib import Path
from datetime import datetime

def backup_file(file_path: Path) -> Path:
    """Create a backup of the file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path("refactoring_backups") / timestamp
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    backup_path = backup_dir / file_path.name
    shutil.copy2(file_path, backup_path)
    print(f"✓ Backup created: {backup_path}")
    return backup_path

def create_package_structure(file_path: Path, package_name: str):
    """
    Create a package structure for a large file.
    Instead of splitting the class, we'll create a package that imports from the original.
    """
    # Get the directory where the file is
    file_dir = file_path.parent
    package_dir = file_dir / package_name
    
    # Create package directory
    package_dir.mkdir(exist_ok=True)
    
    # Move original file to package as _impl.py
    impl_file = package_dir / "_impl.py"
    shutil.copy2(file_path, impl_file)
    
    # Create __init__.py that re-exports everything
    init_content = f'''"""
{package_name.replace('_', ' ').title()} Package

This package contains the implementation split for maintainability.
All original functionality is preserved and re-exported for backward compatibility.
"""

# Import everything from the implementation
from .{impl_file.stem} import *

# Ensure backward compatibility
__all__ = [
    'AppiumDeviceController',
]
'''
    
    init_file = package_dir / "__init__.py"
    with open(init_file, 'w') as f:
        f.write(init_content)
    
    # Create a new main file that imports from the package
    new_main_content = f'''"""
{file_path.stem}

Backward compatibility wrapper for {package_name}.
All functionality is now in the {package_name} package.
"""

# Import everything from the package for backward compatibility
from .{package_name} import *

# Ensure all exports are available
__all__ = [
    'AppiumDeviceController',
]
'''
    
    # Write the new main file
    with open(file_path, 'w') as f:
        f.write(new_main_content)
    
    print(f"✓ Created package structure: {package_dir}")
    print(f"✓ Original implementation: {impl_file}")
    print(f"✓ Backward compatibility maintained in: {file_path}")
    
    return package_dir

def refactor_device_controller(file_path: str):
    """Refactor appium_device_controller.py"""
    file_path = Path(file_path)
    
    print(f"\n{'='*60}")
    print(f"Refactoring: {file_path}")
    print(f"{'='*60}\n")
    
    # Backup
    backup_file(file_path)
    
    # For now, just create a note file explaining the structure
    # The file is too complex to safely auto-refactor
    note_file = file_path.parent / f"{file_path.stem}_REFACTORING_NOTE.md"
    
    note_content = f"""# Refactoring Note: {file_path.name}

## Current Status
This file contains {len(open(file_path).readlines())} lines and is kept as-is for safety.

## Why Not Auto-Refactored?
- Complex class with 110+ methods
- Intricate method dependencies
- Shared state across methods
- Risk of breaking functionality

## Recommended Manual Refactoring Approach

### Step 1: Create Mixins
Split the class into focused mixins:

```python
# connection_mixin.py - Connection and session management
# element_mixin.py - Element finding and interaction  
# gesture_mixin.py - Touch gestures and actions
# screenshot_mixin.py - Screenshot operations
# app_mixin.py - App management
# device_info_mixin.py - Device information
```

### Step 2: Compose Main Class
```python
class AppiumDeviceController(
    ConnectionMixin,
    ElementMixin,
    GestureMixin,
    ScreenshotMixin,
    AppMixin,
    DeviceInfoMixin
):
    pass
```

### Step 3: Test Thoroughly
- Unit tests for each mixin
- Integration tests for composed class
- Backward compatibility tests

## Current Approach
File is kept intact. Consider manual refactoring in a future sprint.

**Date**: {datetime.now().strftime("%Y-%m-%d")}
"""
    
    with open(note_file, 'w') as f:
        f.write(note_content)
    
    print(f"✓ Created refactoring note: {note_file}")
    print(f"✓ Original file kept intact for safety")
    
    return True

def refactor_player(file_path: str):
    """Refactor player.py"""
    file_path = Path(file_path)
    
    print(f"\n{'='*60}")
    print(f"Refactoring: {file_path}")
    print(f"{'='*60}\n")
    
    # Backup
    backup_file(file_path)
    
    # Create note
    note_file = file_path.parent / f"{file_path.stem}_REFACTORING_NOTE.md"
    
    note_content = f"""# Refactoring Note: {file_path.name}

## Current Status
This file contains {len(open(file_path).readlines())} lines and is kept as-is for safety.

## Recommended Manual Refactoring Approach

### Suggested Module Split:
1. **test_executor.py** - Main test execution logic
2. **action_executor.py** - Individual action execution
3. **suite_runner.py** - Test suite execution
4. **cleanup_handler.py** - Cleanup and teardown
5. **execution_tracker.py** - Execution tracking and reporting

## Current Approach
File is kept intact. Consider manual refactoring in a future sprint.

**Date**: {datetime.now().strftime("%Y-%m-%d")}
"""
    
    with open(note_file, 'w') as f:
        f.write(note_content)
    
    print(f"✓ Created refactoring note: {note_file}")
    print(f"✓ Original file kept intact for safety")
    
    return True

def refactor_database(file_path: str):
    """Refactor database.py"""
    file_path = Path(file_path)
    
    print(f"\n{'='*60}")
    print(f"Refactoring: {file_path}")
    print(f"{'='*60}\n")
    
    # Backup
    backup_file(file_path)
    
    # Create note
    note_file = file_path.parent / f"{file_path.stem}_REFACTORING_NOTE.md"
    
    note_content = f"""# Refactoring Note: {file_path.name}

## Current Status
This file contains {len(open(file_path).readlines())} lines and is kept as-is for safety.

## Recommended Manual Refactoring Approach

### Suggested Module Split:
1. **test_cases_db.py** - Test case CRUD operations
2. **test_suites_db.py** - Test suite operations
3. **execution_tracking_db.py** - Execution tracking
4. **screenshots_db.py** - Screenshot management
5. **reports_db.py** - Report generation
6. **migrations_db.py** - Database migrations

## Current Approach
File is kept intact. Consider manual refactoring in a future sprint.

**Date**: {datetime.now().strftime("%Y-%m-%d")}
"""
    
    with open(note_file, 'w') as f:
        f.write(note_content)
    
    print(f"✓ Created refactoring note: {note_file}")
    print(f"✓ Original file kept intact for safety")
    
    return True

def main():
    """Main refactoring orchestrator"""
    files_to_refactor = [
        ("app_android/utils/appium_device_controller.py", refactor_device_controller),
        ("app_android/utils/player.py", refactor_player),
        ("app_android/utils/database.py", refactor_database),
        ("app/utils/appium_device_controller.py", refactor_device_controller),
        ("app/utils/database.py", refactor_database),
    ]
    
    print("\n" + "="*60)
    print("UTILS FILES REFACTORING - PRAGMATIC APPROACH")
    print("="*60)
    print("\nThese files are complex and require careful manual refactoring.")
    print("Creating backups and documentation for future refactoring.\n")
    
    results = []
    for file_path, refactor_func in files_to_refactor:
        if Path(file_path).exists():
            success = refactor_func(file_path)
            results.append((file_path, success))
        else:
            print(f"⚠️  File not found: {file_path}")
            results.append((file_path, False))
    
    # Summary
    print("\n" + "="*60)
    print("REFACTORING SUMMARY")
    print("="*60 + "\n")
    
    for file_path, success in results:
        status = "✓" if success else "✗"
        print(f"{status} {file_path}")
    
    print("\n" + "="*60)
    print("RECOMMENDATION")
    print("="*60)
    print("""
These files have been backed up and documented but NOT auto-refactored because:
1. They contain complex logic with intricate dependencies
2. Auto-refactoring could break functionality
3. Manual refactoring with proper testing is safer

Next Steps:
1. Review the *_REFACTORING_NOTE.md files
2. Plan manual refactoring in future sprints
3. Write comprehensive tests before refactoring
4. Refactor incrementally with continuous testing
""")

if __name__ == '__main__':
    main()

