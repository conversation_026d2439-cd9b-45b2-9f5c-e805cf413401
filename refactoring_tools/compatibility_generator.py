#!/usr/bin/env python3
"""
Backward Compatibility Layer Generator

Generates a new app.py that maintains backward compatibility by re-exporting
all components from the modular structure.
"""

from pathlib import Path
from typing import Dict

class CompatibilityGenerator:
    """Generates backward compatibility layer"""
    
    def __init__(self, source_file):
        self.source_file = Path(source_file)
        with open(self.source_file, 'r') as f:
            self.content = f.read()
        self.lines = self.content.split('\n')
    
    def extract_global_variables(self) -> list:
        """Extract global variable declarations"""
        globals_section = []
        in_globals = False
        
        for line in self.lines:
            # Look for global variable declarations
            if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*', line):
                if not line.strip().startswith('app ='):
                    globals_section.append(line)
        
        return globals_section
    
    def generate(self, route_modules: Dict[str, str], helper_modules: Dict[str, str]) -> str:
        """
        Generate the new app.py with backward compatibility
        
        Args:
            route_modules: Dict of route module names and their content
            helper_modules: Dict of helper module names and their content
        
        Returns:
            str: Content of the new app.py file
        """
        content = []
        
        # File header
        content.append('"""')
        content.append('Mobile App Automation Tool - Android Platform')
        content.append('')
        content.append('This is the main entry point for the Android platform app.')
        content.append('The application has been refactored into modular components for better maintainability.')
        content.append('')
        content.append('Module Structure:')
        content.append('  - core/: Core application initialization and state management')
        content.append('  - routes_modules/: Route handlers organized by functionality')
        content.append('  - helpers/: Helper functions and utilities')
        content.append('')
        content.append('This file maintains backward compatibility by re-exporting all components.')
        content.append('"""')
        content.append('')
        
        # Imports
        content.append('import os')
        content.append('import sys')
        content.append('import logging')
        content.append('from pathlib import Path')
        content.append('')
        
        # Set up logging
        content.append('# Set up logging')
        content.append('logging.basicConfig(')
        content.append('    level=logging.DEBUG,')
        content.append('    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"')
        content.append(')')
        content.append('logger = logging.getLogger(__name__)')
        content.append('')
        
        # Import core components
        content.append('# ============================================================================')
        content.append('# CORE COMPONENTS')
        content.append('# ============================================================================')
        content.append('')
        content.append('# Import Flask app instance')
        content.append('from app_android.core.app_factory import app, create_app')
        content.append('')
        content.append('# Import all state variables for backward compatibility')
        content.append('from app_android.core.state_manager import (')
        content.append('    # Device controller maps')
        content.append('    device_controllers,')
        content.append('    players,')
        content.append('    action_factories,')
        content.append('    ')
        content.append('    # Global variables')
        content.append('    current_device,')
        content.append('    current_device_id,')
        content.append('    recording_actions,')
        content.append('    action_factory,')
        content.append('    is_test_suite_execution,')
        content.append('    ')
        content.append('    # Report directories')
        content.append('    current_report_dir,')
        content.append('    current_report_timestamp,')
        content.append('    current_screenshots_dir,')
        content.append('    current_action_logs,')
        content.append('    ')
        content.append('    # Step execution timeout')
        content.append('    step_execution_timer,')
        content.append('    step_timeout_occurred,')
        content.append('    ')
        content.append('    # Classes')
        content.append('    StepExecutionTimeout,')
        content.append('    IndexHolder,')
        content.append('    ')
        content.append('    # Session management')
        content.append('    get_client_session_id,')
        content.append('    get_session_device_id,')
        content.append('    set_session_device_id,')
        content.append('    clear_session_device_id,')
        content.append('    get_session_id,')
        content.append('    get_current_environment_id_from_session,')
        content.append('    ensure_default_environment,')
        content.append('    ')
        content.append('    # Managers')
        content.append('    test_suites_manager,')
        content.append('    import_export_manager,')
        content.append('    test_case_manager,')
        content.append('    ')
        content.append('    # Directories')
        content.append('    TEST_CASES_DIR,')
        content.append('    REFERENCE_IMAGES_DIR,')
        content.append('    SCREENSHOTS_DIR,')
        content.append('    screenshots_dir,')
        content.append('    app_screenshots_dir,')
        content.append('    ')
        content.append('    # SocketIO')
        content.append('    socketio,')
        content.append('    DummySocketIO,')
        content.append('    ')
        content.append('    # Cleanup')
        content.append('    cleanup_screenshots,')
        content.append('    cleanup_app_screenshots,')
        content.append('    setup_signal_handlers,')
        content.append('    shutdown_handler,')
        content.append(')')
        content.append('')
        
        # Import helper modules
        if helper_modules:
            content.append('# ============================================================================')
            content.append('# HELPER FUNCTIONS')
            content.append('# ============================================================================')
            content.append('')
            for module_name in helper_modules.keys():
                content.append(f'from app_android.helpers.{module_name} import *')
            content.append('')
        
        # Import route modules
        if route_modules:
            content.append('# ============================================================================')
            content.append('# ROUTE MODULES')
            content.append('# ============================================================================')
            content.append('')
            content.append('# Import all route modules to register them with the Flask app')
            for module_name in route_modules.keys():
                content.append(f'import app_android.routes_modules.{module_name}')
            content.append('')
        
        # Main execution block
        content.append('# ============================================================================')
        content.append('# MAIN EXECUTION')
        content.append('# ============================================================================')
        content.append('')
        content.append('if __name__ == "__main__":')
        content.append('    # Setup signal handlers in main thread')
        content.append('    setup_signal_handlers()')
        content.append('    ')
        content.append('    # Get port from environment variable or config file')
        content.append('    port = int(os.environ.get("FLASK_PORT", 8081))  # Default to 8081 for Android')
        content.append('    try:')
        content.append('        import config')
        content.append('        if not os.environ.get("FLASK_PORT"):')
        content.append('            port = getattr(config, "FLASK_PORT", 8081)')
        content.append('    except ImportError:')
        content.append('        pass')
        content.append('    ')
        content.append('    print(f"Starting Mobile App Automation Tool (Android) on port {port}...")')
        content.append('    SECURE = os.getenv("SECURE_BUILD", "False").lower() == "true"')
        content.append('    host = "127.0.0.1" if SECURE else "0.0.0.0"')
        content.append('    debug = False if SECURE else True')
        content.append('    app.run(host=host, port=port, debug=debug)')
        content.append('')
        
        return '\n'.join(content)

# Add missing import
import re

if __name__ == '__main__':
    # Test the generator
    generator = CompatibilityGenerator('app_android/app.py')
    content = generator.generate({}, {})
    print(f"Generated {len(content.split(chr(10)))} lines")

