# Refactoring Tools

Automated tools for refactoring large Python files into modular components while maintaining 100% backward compatibility.

## Overview

This toolkit helps break down monolithic Python files (10,000+ lines) into smaller, maintainable modules organized by functionality. It's specifically designed for the Mobile App Automation Tool project but can be adapted for other Flask applications.

## Features

- ✅ **Automated Route Extraction**: Categorizes and extracts Flask routes into logical modules
- ✅ **Helper Function Organization**: Extracts utility functions into helper modules
- ✅ **Backward Compatibility**: Generates compatibility layer to maintain existing imports
- ✅ **Validation**: Validates syntax, imports, and line counts
- ✅ **Safe Execution**: Dry-run mode and automatic backups
- ✅ **Rollback Support**: Easy rollback if issues occur

## Quick Start

### 1. Dry Run (Recommended First Step)

```bash
python refactoring_tools/main_refactor.py --file app_android/app.py --dry-run
```

This will:
- Analyze the file structure
- Show what would be extracted
- Display route distribution
- **NOT modify any files**

### 2. Execute Refactoring

```bash
python refactoring_tools/main_refactor.py --file app_android/app.py --execute
```

This will:
- Create timestamped backups
- Extract routes into `routes_modules/`
- Extract helpers into `helpers/`
- Generate new `app_new.py` with backward compatibility
- Run validation tests

### 3. Review and Test

After execution:

1. **Review the generated files**:
   ```bash
   ls -la app_android/routes_modules/
   ls -la app_android/helpers/
   cat app_android/app_new.py
   ```

2. **Run syntax validation**:
   ```bash
   python -m py_compile app_android/app_new.py
   python -m py_compile app_android/routes_modules/*.py
   python -m py_compile app_android/helpers/*.py
   ```

3. **Test the application**:
   ```bash
   # Temporarily rename files to test
   mv app_android/app.py app_android/app_old.py
   mv app_android/app_new.py app_android/app.py
   
   # Start the application
   python run.py
   
   # Test functionality:
   # - Connect to a device
   # - Run a test case
   # - Generate a report
   ```

4. **If everything works**:
   ```bash
   # Keep the new structure
   rm app_android/app_old.py
   ```

5. **If issues occur**:
   ```bash
   # Rollback
   mv app_android/app.py app_android/app_new.py
   mv app_android/app_old.py app_android/app.py
   ```

## Module Structure

After refactoring, the structure will be:

```
app_android/
├── app.py                    # Main entry point (500 lines)
├── core/
│   ├── __init__.py
│   ├── app_factory.py        # Flask initialization
│   └── state_manager.py      # Global state variables
├── routes_modules/
│   ├── __init__.py
│   ├── device_routes.py      # Device-related endpoints
│   ├── test_case_routes.py   # Test case operations
│   ├── test_suite_routes.py  # Test suite operations
│   ├── report_routes.py      # Report generation
│   ├── environment_routes.py # Environment management
│   ├── action_routes.py      # Action execution
│   ├── reference_image_routes.py  # Reference images
│   └── misc_routes.py        # Miscellaneous endpoints
└── helpers/
    ├── __init__.py
    ├── config_helpers.py     # Configuration utilities
    └── directory_helpers.py  # Directory management
```

## Route Categories

Routes are automatically categorized based on URL patterns:

- **device_routes**: `/api/devices`, `/api/device/*`, `/api/session/*`
- **test_case_routes**: `/api/test_cases/*`, `/api/execute_test_case`
- **test_suite_routes**: `/api/test_suites/*`
- **report_routes**: `/api/reports/*`, `/api/execution/*`
- **environment_routes**: `/api/environments`, `/api/settings`
- **action_routes**: `/api/action/*`, `/api/screenshot`
- **reference_image_routes**: `/api/reference_images`
- **misc_routes**: `/health`, `/`, and other miscellaneous routes

## Backward Compatibility

The refactoring maintains 100% backward compatibility:

```python
# These imports will continue to work:
from app_android.app import app
from app_android.app import current_test_idx, current_step_idx
from app_android.app import device_controllers, players
from app_android.app import socketio
```

All global variables, functions, and classes are re-exported from the main `app.py`.

## Validation

The validator checks:

1. **Syntax**: All Python files have valid syntax
2. **Imports**: All imports can be resolved
3. **Line Counts**: No file exceeds 2,000 lines
4. **Backward Compatibility**: All symbols are still exported

## Rollback

If you need to rollback:

1. **Find your backup**:
   ```bash
   ls -la refactoring_backups/
   ```

2. **Restore from backup**:
   ```bash
   # Example: restore from backup created on 2024-01-15 at 14:30:00
   cp refactoring_backups/20240115_143000/app.py app_android/app.py
   ```

3. **Remove generated modules** (if needed):
   ```bash
   rm -rf app_android/routes_modules/
   rm -rf app_android/helpers/
   ```

## Advanced Usage

### Custom Route Categories

Edit `route_extractor.py` to customize route categories:

```python
ROUTE_CATEGORIES = {
    'my_custom_routes': [
        r'/api/custom/',
        r'/api/special/',
    ],
    # ... other categories
}
```

### Custom Helper Patterns

Edit `helper_extractor.py` to customize helper extraction:

```python
HELPER_PATTERNS = {
    'my_helpers': [
        'my_helper_function',
        'another_helper',
    ],
    # ... other patterns
}
```

## Troubleshooting

### Issue: Import errors after refactoring

**Solution**: Check that all imports in the new modules are correct. The route modules should import from `app_android.core`:

```python
from app_android.core import app
from app_android.core.state_manager import device_controllers, players
```

### Issue: Missing routes

**Solution**: Check the uncategorized routes in the dry-run output. Add patterns to `ROUTE_CATEGORIES` to categorize them.

### Issue: File still too large

**Solution**: Further split large route modules. For example, split `test_case_routes.py` into:
- `test_case_crud_routes.py` (create, read, update, delete)
- `test_case_execution_routes.py` (execution-related)

## Next Steps

After successfully refactoring `app_android/app.py`, apply the same process to:

1. `app/app.py` (10,461 lines)
2. `app_android/utils/appium_device_controller.py` (8,167 lines)
3. `app_android/utils/player.py` (6,926 lines)
4. `app_android/utils/database.py` (6,437 lines)
5. `app/utils/appium_device_controller.py` (5,674 lines)
6. `app/utils/database.py` (5,511 lines)

## Support

For issues or questions, refer to the main project documentation or create an issue in the project repository.

