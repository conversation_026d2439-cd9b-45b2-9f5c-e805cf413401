#!/usr/bin/env python3
"""
Route Extractor

Extracts Flask route handlers from a large app.py file and organizes them
into categorized modules.
"""

import re
import ast
import logging
from pathlib import Path
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

class RouteExtractor:
    """Extracts and categorizes Flask routes"""

    # Route categories based on URL patterns
    ROUTE_CATEGORIES = {
        'device_routes': [
            r'/api/devices',
            r'/api/device/',
            r'/api/session/',
            r'/api/check_inspector',
            r'/api/page_source',
            r'/api/session_info',
        ],
        'test_case_routes': [
            r'/api/test_cases/',
            r'/api/execute_test_case',
            r'/api/delete_test_case',
            r'/api/recording/',
            r'/api/test_cases_for_multi_step',
        ],
        'test_suite_routes': [
            r'/api/test_suites/',
        ],
        'report_routes': [
            r'/api/reports/',
            r'/api/generate_report',
            r'/api/report/',
            r'/reports/',
            r'/api/execution/',
            r'/api/get_execution_context',
            r'/api/set_execution_context',
            r'/api/get_test_execution_status',
            r'/api/executions',
        ],
        'environment_routes': [
            r'/api/environments',
            r'/api/environment_variables',
            r'/api/settings',
            r'/api/directory_paths',
        ],
        'action_routes': [
            r'/api/action/',
            r'/api/capture_image',
            r'/api/screenshot',
            r'/api/text_detection',
            r'/get_element_at_position',
            r'/appium/inspect_element',
            r'/api/element/at_position',
        ],
        'reference_image_routes': [
            r'/api/reference_images',
            r'/api/reference_image',
            r'/api/validate_screenshot_name',
        ],
        'misc_routes': [
            r'/health',
            r'/api/debug/',
            r'/api/temp/',
            r'/api/logs/',
            r'/api/screenshots/',
            r'/api/upload_media',
            r'/api/test_debug',
            r'/api/test-execution/',
            r'/api/database/clear_screenshots',
            r'/api/ios/set_clipboard',
            r'/api/ios/paste_clipboard',
            r'/screenshot$',
            r'/screenshots/',
            r'/$',  # index route
            r'/action-templates',
            r'/test_suites$',
            r'/multi_device_test',
            r'/ios_app',
        ],
    }
    
    def __init__(self, source_file):
        self.source_file = Path(source_file)
        with open(self.source_file, 'r') as f:
            self.content = f.read()
        self.lines = self.content.split('\n')
    
    def extract_imports(self) -> str:
        """Extract all import statements"""
        imports = []
        for line in self.lines:
            stripped = line.strip()
            if (stripped.startswith('import ') or 
                stripped.startswith('from ') or
                stripped == '' or
                stripped.startswith('#')):
                imports.append(line)
            elif stripped and not stripped.startswith('#'):
                break
        return '\n'.join(imports)
    
    def find_route_functions(self) -> List[Tuple[str, str, int, int]]:
        """
        Find all route functions with their decorators
        Returns: List of (route_url, function_name, start_line, end_line)
        """
        routes = []
        i = 0
        
        while i < len(self.lines):
            line = self.lines[i]
            
            # Check if this is a route decorator
            if '@app.route(' in line:
                # Extract the route URL
                match = re.search(r"@app\.route\(['\"]([^'\"]+)['\"]", line)
                if match:
                    route_url = match.group(1)
                    
                    # Find the function definition
                    j = i + 1
                    while j < len(self.lines) and not self.lines[j].strip().startswith('def '):
                        j += 1
                    
                    if j < len(self.lines):
                        func_match = re.search(r'def\s+(\w+)\s*\(', self.lines[j])
                        if func_match:
                            func_name = func_match.group(1)
                            
                            # Find the end of the function
                            end_line = self._find_function_end(j)
                            
                            routes.append((route_url, func_name, i, end_line))
                            i = end_line
                            continue
            
            i += 1
        
        return routes
    
    def _find_function_end(self, start_line: int) -> int:
        """Find the end line of a function starting at start_line"""
        # Get the indentation of the function definition
        func_line = self.lines[start_line]
        func_indent = len(func_line) - len(func_line.lstrip())
        
        i = start_line + 1
        while i < len(self.lines):
            line = self.lines[i]
            
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                i += 1
                continue
            
            # Check indentation
            line_indent = len(line) - len(line.lstrip())
            
            # If we find a line with same or less indentation, function has ended
            if line_indent <= func_indent and line.strip():
                return i - 1
            
            i += 1
        
        return len(self.lines) - 1
    
    def categorize_route(self, route_url: str) -> str:
        """Categorize a route based on its URL"""
        for category, patterns in self.ROUTE_CATEGORIES.items():
            for pattern in patterns:
                if re.search(pattern, route_url):
                    return category
        return 'misc_routes'
    
    def extract_route_function_code(self, start_line: int, end_line: int) -> str:
        """Extract the complete code for a route function"""
        return '\n'.join(self.lines[start_line:end_line + 1])
    
    def extract_all_routes(self) -> Dict[str, str]:
        """
        Extract all routes and organize them into modules
        Returns: Dict of {module_name: module_content}
        """
        # Find all routes
        routes = self.find_route_functions()
        
        # Categorize routes
        categorized = {category: [] for category in self.ROUTE_CATEGORIES.keys()}
        
        for route_url, func_name, start_line, end_line in routes:
            category = self.categorize_route(route_url)
            code = self.extract_route_function_code(start_line, end_line)
            categorized[category].append({
                'url': route_url,
                'name': func_name,
                'code': code,
                'start': start_line,
                'end': end_line
            })
        
        # Generate module files
        modules = {}
        imports = self.extract_imports()
        
        for category, route_list in categorized.items():
            if not route_list:
                continue
            
            module_content = self._generate_module_content(
                category, route_list, imports
            )
            modules[category] = module_content
        
        return modules
    
    def _generate_module_content(self, category: str, routes: List[Dict], imports: str) -> str:
        """Generate the content for a route module"""
        content = []

        # Header
        content.append('"""')
        content.append(f'{category.replace("_", " ").title()}')
        content.append('')
        content.append(f'This module contains all {category.replace("_", " ")} for the application.')
        content.append('"""')
        content.append('')

        # Standard imports
        content.append('import os')
        content.append('import json')
        content.append('import base64')
        content.append('import time')
        content.append('import logging')
        content.append('import glob')
        content.append('import sys')
        content.append('import traceback')
        content.append('import shutil')
        content.append('import threading')
        content.append('import signal')
        content.append('import uuid')
        content.append('from pathlib import Path')
        content.append('from datetime import datetime')
        content.append('from functools import wraps')
        content.append('')
        content.append('# Flask imports')
        content.append('from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session, Response')
        content.append('from werkzeug.utils import secure_filename')
        content.append('')
        content.append('# PIL imports')
        content.append('import PIL.Image')
        content.append('import PIL.ImageDraw')
        content.append('import io')
        content.append('')
        content.append('# Other imports')
        content.append('import xml.etree.ElementTree as ET')
        content.append('import requests')
        content.append('import re')
        content.append('')
        content.append('# Import app instance from core')
        content.append('from app_android.core import app')
        content.append('')
        content.append('# Import state variables')
        content.append('from app_android.core.state_manager import (')
        content.append('    device_controllers, players, action_factories,')
        content.append('    current_device, current_device_id, recording_actions,')
        content.append('    test_case_manager, test_suites_manager,')
        content.append('    get_session_device_id, get_session_id, get_client_session_id,')
        content.append('    socketio,')
        content.append(')')
        content.append('')
        content.append('# Import utilities')
        content.append('from app_android.utils.appium_device_controller import AppiumDeviceController')
        content.append('from app_android.utils.recorder import Recorder')
        content.append('from app_android.utils.player import Player')
        content.append('from app_android.actions.action_factory import ActionFactory')
        content.append('from app_android.utils.reportGenerator import generateReport')
        content.append('from app_android.utils.custom_report_generator import generate_custom_report')
        content.append('from app_android.utils.screenshot_manager import screenshot_manager')
        content.append('from app_android.utils.directory_paths_db import directory_paths_db')
        content.append('from app_android.utils.environment_resolver import resolve_text_with_env_variables, get_resolved_variable_value')
        content.append('')
        content.append('# Set up logger')
        content.append('logger = logging.getLogger(__name__)')
        content.append('')
        content.append(f'# {"="*76}')
        content.append(f'# ROUTE HANDLERS')
        content.append(f'# {"="*76}')
        content.append('')
        
        # Add all routes
        for route in routes:
            content.append(route['code'])
            content.append('')
            content.append('')
        
        return '\n'.join(content)

if __name__ == '__main__':
    # Test the extractor
    extractor = RouteExtractor('app_android/app.py')
    routes = extractor.find_route_functions()
    print(f"Found {len(routes)} routes")
    
    for url, name, start, end in routes[:5]:
        print(f"  {url} -> {name}() [lines {start}-{end}]")

