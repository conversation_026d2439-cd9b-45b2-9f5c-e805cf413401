"""
Refactoring Tools Package

This package contains automated tools for refactoring large Python files
into modular components while maintaining 100% backward compatibility.

Modules:
    - main_refactor: Main orchestrator for the refactoring process
    - route_extractor: Extracts Flask routes into categorized modules
    - helper_extractor: Extracts helper functions into utility modules
    - compatibility_generator: Generates backward compatibility layer
    - validator: Validates refactored code

Usage:
    python refactoring_tools/main_refactor.py --file app_android/app.py --dry-run
    python refactoring_tools/main_refactor.py --file app_android/app.py --execute
"""

__version__ = '1.0.0'
__author__ = 'Mobile App Automation Tool Team'

from .route_extractor import RouteExtractor
from .helper_extractor import HelperExtractor
from .compatibility_generator import CompatibilityGenerator
from .validator import RefactoringValidator

__all__ = [
    'RouteExtractor',
    'HelperExtractor',
    'CompatibilityGenerator',
    'RefactoringValidator',
]

