#!/usr/bin/env python3
"""
Main Refactoring Orchestrator

This script orchestrates the complete refactoring of large Python files
into modular components while maintaining 100% backward compatibility.

Usage:
    python refactoring_tools/main_refactor.py --file app_android/app.py --dry-run
    python refactoring_tools/main_refactor.py --file app_android/app.py --execute
"""

import os
import sys
import argparse
import shutil
from pathlib import Path
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

from route_extractor import RouteExtractor
from helper_extractor import HelperExtractor
from compatibility_generator import CompatibilityGenerator
from validator import RefactoringValidator

class RefactoringOrchestrator:
    """Orchestrates the complete refactoring process"""
    
    def __init__(self, source_file, dry_run=True):
        self.source_file = Path(source_file)
        self.dry_run = dry_run
        self.backup_dir = Path('refactoring_backups') / datetime.now().strftime('%Y%m%d_%H%M%S')
        self.app_dir = self.source_file.parent
        
        # Create output directories
        self.routes_dir = self.app_dir / 'routes_modules'
        self.helpers_dir = self.app_dir / 'helpers'
        self.core_dir = self.app_dir / 'core'
        
        # Initialize extractors
        self.route_extractor = RouteExtractor(source_file)
        self.helper_extractor = HelperExtractor(source_file)
        self.compatibility_generator = CompatibilityGenerator(source_file)
        self.validator = RefactoringValidator()
        
    def create_backup(self):
        """Create a timestamped backup of the original file"""
        print(f"\n{'='*80}")
        print("STEP 1: Creating Backup")
        print(f"{'='*80}")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        backup_file = self.backup_dir / self.source_file.name
        
        shutil.copy2(self.source_file, backup_file)
        print(f"✓ Backup created: {backup_file}")
        
        # Also create a backup of the entire directory
        app_backup = self.backup_dir / self.app_dir.name
        if not app_backup.exists():
            shutil.copytree(self.app_dir, app_backup, 
                          ignore=shutil.ignore_patterns('__pycache__', '*.pyc', '.git'))
            print(f"✓ Full directory backup: {app_backup}")
        
        return backup_file
    
    def create_directory_structure(self):
        """Create the modular directory structure"""
        print(f"\n{'='*80}")
        print("STEP 2: Creating Directory Structure")
        print(f"{'='*80}")
        
        directories = [
            self.routes_dir,
            self.helpers_dir,
            self.core_dir,
        ]
        
        for directory in directories:
            if not self.dry_run:
                directory.mkdir(parents=True, exist_ok=True)
            print(f"{'[DRY RUN] ' if self.dry_run else ''}✓ Created: {directory}")
    
    def extract_routes(self):
        """Extract routes into separate modules"""
        print(f"\n{'='*80}")
        print("STEP 3: Extracting Routes")
        print(f"{'='*80}")
        
        route_modules = self.route_extractor.extract_all_routes()
        
        print(f"\nExtracted {len(route_modules)} route modules:")
        for module_name, content in route_modules.items():
            lines = len(content.split('\n'))
            print(f"  - {module_name}: {lines} lines")
            
            if not self.dry_run:
                output_file = self.routes_dir / f"{module_name}.py"
                with open(output_file, 'w') as f:
                    f.write(content)
                print(f"    ✓ Written to: {output_file}")
        
        return route_modules
    
    def extract_helpers(self):
        """Extract helper functions into separate modules"""
        print(f"\n{'='*80}")
        print("STEP 4: Extracting Helper Functions")
        print(f"{'='*80}")
        
        helper_modules = self.helper_extractor.extract_all_helpers()
        
        print(f"\nExtracted {len(helper_modules)} helper modules:")
        for module_name, content in helper_modules.items():
            lines = len(content.split('\n'))
            print(f"  - {module_name}: {lines} lines")
            
            if not self.dry_run:
                output_file = self.helpers_dir / f"{module_name}.py"
                with open(output_file, 'w') as f:
                    f.write(content)
                print(f"    ✓ Written to: {output_file}")
        
        return helper_modules
    
    def generate_compatibility_layer(self, route_modules, helper_modules):
        """Generate the backward compatibility layer"""
        print(f"\n{'='*80}")
        print("STEP 5: Generating Backward Compatibility Layer")
        print(f"{'='*80}")
        
        new_app_content = self.compatibility_generator.generate(
            route_modules, helper_modules
        )
        
        lines = len(new_app_content.split('\n'))
        print(f"\nGenerated new app.py: {lines} lines")
        
        if not self.dry_run:
            # Save the new app.py
            new_app_file = self.app_dir / 'app_new.py'
            with open(new_app_file, 'w') as f:
                f.write(new_app_content)
            print(f"✓ Written to: {new_app_file}")
            print(f"  (Review this file before replacing the original)")
        
        return new_app_content
    
    def validate_refactoring(self):
        """Validate the refactored code"""
        print(f"\n{'='*80}")
        print("STEP 6: Validation")
        print(f"{'='*80}")
        
        if self.dry_run:
            print("[DRY RUN] Skipping validation")
            return True
        
        # Run validation tests
        results = self.validator.validate_all(self.app_dir)
        
        print("\nValidation Results:")
        print(f"  Syntax Check: {'✓ PASS' if results['syntax'] else '✗ FAIL'}")
        print(f"  Import Check: {'✓ PASS' if results['imports'] else '✗ FAIL'}")
        print(f"  Line Count Check: {'✓ PASS' if results['line_counts'] else '✗ FAIL'}")
        
        if results['errors']:
            print("\nErrors found:")
            for error in results['errors']:
                print(f"  ✗ {error}")
        
        return all([results['syntax'], results['imports'], results['line_counts']])
    
    def generate_report(self):
        """Generate a refactoring report"""
        print(f"\n{'='*80}")
        print("STEP 7: Generating Report")
        print(f"{'='*80}")
        
        report_file = self.backup_dir / 'refactoring_report.txt'
        
        with open(report_file, 'w') as f:
            f.write("REFACTORING REPORT\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Source File: {self.source_file}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Dry Run: {self.dry_run}\n")
            f.write(f"Backup Directory: {self.backup_dir}\n\n")
            f.write("=" * 80 + "\n")
        
        print(f"✓ Report saved: {report_file}")
    
    def run(self):
        """Run the complete refactoring process"""
        print(f"\n{'#'*80}")
        print(f"# REFACTORING ORCHESTRATOR")
        print(f"# File: {self.source_file}")
        print(f"# Mode: {'DRY RUN' if self.dry_run else 'EXECUTE'}")
        print(f"{'#'*80}")
        
        try:
            # Step 1: Backup
            self.create_backup()
            
            # Step 2: Create directories
            self.create_directory_structure()
            
            # Step 3: Extract routes
            route_modules = self.extract_routes()
            
            # Step 4: Extract helpers
            helper_modules = self.extract_helpers()
            
            # Step 5: Generate compatibility layer
            self.generate_compatibility_layer(route_modules, helper_modules)
            
            # Step 6: Validate
            if not self.dry_run:
                validation_passed = self.validate_refactoring()
                if not validation_passed:
                    print("\n⚠ WARNING: Validation failed!")
                    print("Review the errors above before proceeding.")
                    return False
            
            # Step 7: Generate report
            self.generate_report()
            
            print(f"\n{'='*80}")
            print("REFACTORING COMPLETE!")
            print(f"{'='*80}")
            
            if self.dry_run:
                print("\nThis was a DRY RUN. No files were modified.")
                print("Run with --execute to apply changes.")
            else:
                print("\nNext steps:")
                print("1. Review app_new.py")
                print("2. Run tests to verify functionality")
                print("3. If everything works, replace app.py with app_new.py")
                print(f"4. Backup is available at: {self.backup_dir}")
            
            return True
            
        except Exception as e:
            print(f"\n✗ ERROR: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    parser = argparse.ArgumentParser(description='Refactor large Python files into modular components')
    parser.add_argument('--file', required=True, help='Source file to refactor')
    parser.add_argument('--execute', action='store_true', help='Execute refactoring (default is dry-run)')
    parser.add_argument('--dry-run', action='store_true', default=True, help='Perform dry run without modifying files')
    
    args = parser.parse_args()
    
    # If --execute is specified, disable dry-run
    dry_run = not args.execute
    
    orchestrator = RefactoringOrchestrator(args.file, dry_run=dry_run)
    success = orchestrator.run()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()

