#!/usr/bin/env python3
"""
Class Method Extractor

Extracts methods from a large class and organizes them into smaller,
focused classes based on functionality.
"""

import re
import ast
from pathlib import Path
from typing import Dict, List, Tuple

class ClassMethodExtractor:
    """Extracts and categorizes methods from a large class"""
    
    # Method categories for AppiumDeviceController
    METHOD_CATEGORIES = {
        'connection_manager': [
            '__init__', 'connect_device', 'disconnect', 'reconnect',
            '_try_device_manager_connection', '_start_connection_monitoring',
            '_connection_monitor', '_perform_health_check', '_update_activity_time',
            'is_appium_server_running', '_attempt_connection_recovery',
            '_simple_session_recovery', '_enhanced_session_recovery',
            '_validate_session_health', 'handle_session_termination',
            '_validate_session_simple', '_simple_session_ping', 'is_session_healthy',
            'test_airtest_connection', '_kill_existing_processes',
            '_stop_tracked_appium_process', '_pids_listening_on_port',
            'check_appium_server', 'restart_appium_server',
        ],
        'element_operations': [
            'find_element', 'find_elements', 'wait_for_element',
            'is_element_present', 'get_element_text', 'get_element_attribute',
            'click_element', 'send_keys_to_element', 'clear_element',
            'get_element_location', 'get_element_size', 'is_element_displayed',
            'is_element_enabled', 'get_element_screenshot',
        ],
        'gesture_handler': [
            'tap', 'double_tap', 'long_press', 'swipe', 'scroll',
            'drag_and_drop', 'pinch', 'zoom', 'rotate',
            'perform_touch_action', 'perform_multi_touch',
        ],
        'screenshot_manager': [
            'take_screenshot', 'save_screenshot', 'get_screenshot_as_base64',
            'get_screenshot_as_png', 'compare_screenshots',
            'capture_element_screenshot',
        ],
        'session_manager': [
            'get_session_id', 'get_session_capabilities', 'get_session_status',
            '_update_session_metrics', 'get_session_diagnostics',
            'get_current_activity', 'get_current_package',
        ],
        'device_info': [
            'get_device_info', 'get_device_time', 'get_device_orientation',
            'set_device_orientation', 'get_screen_size', 'get_device_model',
            'get_platform_version', 'is_device_locked', 'unlock_device',
            'lock_device', 'get_battery_info',
        ],
        'app_management': [
            'install_app', 'remove_app', 'launch_app', 'close_app',
            'activate_app', 'terminate_app', 'is_app_installed',
            'get_app_state', 'reset_app', 'background_app',
        ],
    }
    
    def __init__(self, source_file):
        self.source_file = Path(source_file)
        with open(self.source_file, 'r') as f:
            self.content = f.read()
        self.lines = self.content.split('\n')
        self.class_name = None
        self.class_start_line = None
    
    def find_class(self, class_name: str) -> Tuple[int, int]:
        """Find the start and end lines of a class"""
        for i, line in enumerate(self.lines):
            if re.match(rf'^class\s+{class_name}\s*[:(]', line):
                self.class_name = class_name
                self.class_start_line = i
                # Find the end of the class (next class or end of file)
                for j in range(i + 1, len(self.lines)):
                    if re.match(r'^class\s+\w+', self.lines[j]):
                        return (i, j - 1)
                return (i, len(self.lines) - 1)
        return None
    
    def find_methods(self) -> List[Tuple[str, int, int]]:
        """
        Find all methods in the class
        Returns: List of (method_name, start_line, end_line)
        """
        if not self.class_start_line:
            return []
        
        methods = []
        i = self.class_start_line + 1
        
        while i < len(self.lines):
            line = self.lines[i]
            
            # Check if this is a method definition
            match = re.match(r'^\s{4}def\s+(\w+)\s*\(', line)
            if match:
                method_name = match.group(1)
                start_line = i
                
                # Find the end of the method
                end_line = self._find_method_end(i)
                
                methods.append((method_name, start_line, end_line))
                i = end_line + 1
            else:
                i += 1
        
        return methods
    
    def _find_method_end(self, start_line: int) -> int:
        """Find the end line of a method"""
        # Get the indentation of the method definition
        method_line = self.lines[start_line]
        method_indent = len(method_line) - len(method_line.lstrip())
        
        i = start_line + 1
        while i < len(self.lines):
            line = self.lines[i]
            
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                i += 1
                continue
            
            # Check indentation
            line_indent = len(line) - len(line.lstrip())
            
            # If we find a line with same or less indentation, method has ended
            if line_indent <= method_indent and line.strip():
                return i - 1
            
            i += 1
        
        return len(self.lines) - 1
    
    def categorize_method(self, method_name: str) -> str:
        """Categorize a method based on its name"""
        for category, methods in self.METHOD_CATEGORIES.items():
            if method_name in methods:
                return category
        
        # Try to categorize by name patterns
        if any(x in method_name.lower() for x in ['connect', 'session', 'health', 'monitor']):
            return 'connection_manager'
        elif any(x in method_name.lower() for x in ['element', 'find', 'wait', 'click']):
            return 'element_operations'
        elif any(x in method_name.lower() for x in ['tap', 'swipe', 'scroll', 'gesture']):
            return 'gesture_handler'
        elif any(x in method_name.lower() for x in ['screenshot', 'capture', 'image']):
            return 'screenshot_manager'
        elif any(x in method_name.lower() for x in ['app', 'install', 'launch', 'package']):
            return 'app_management'
        elif any(x in method_name.lower() for x in ['device', 'info', 'battery', 'orientation']):
            return 'device_info'
        else:
            return 'session_manager'
    
    def extract_method_code(self, start_line: int, end_line: int) -> str:
        """Extract the code for a method"""
        return '\n'.join(self.lines[start_line:end_line + 1])
    
    def generate_report(self, class_name: str) -> Dict:
        """Generate a report of methods by category"""
        class_info = self.find_class(class_name)
        if not class_info:
            return {'error': f'Class {class_name} not found'}
        
        methods = self.find_methods()
        
        # Categorize methods
        categorized = {}
        for method_name, start, end in methods:
            category = self.categorize_method(method_name)
            if category not in categorized:
                categorized[category] = []
            
            lines = end - start + 1
            categorized[category].append({
                'name': method_name,
                'start': start,
                'end': end,
                'lines': lines
            })
        
        # Calculate statistics
        report = {
            'class_name': class_name,
            'total_methods': len(methods),
            'categories': {}
        }
        
        for category, method_list in categorized.items():
            total_lines = sum(m['lines'] for m in method_list)
            report['categories'][category] = {
                'method_count': len(method_list),
                'total_lines': total_lines,
                'methods': method_list
            }
        
        return report

if __name__ == '__main__':
    # Test the extractor
    import sys
    import json
    
    if len(sys.argv) < 3:
        print("Usage: python class_method_extractor.py <file> <class_name>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    class_name = sys.argv[2]
    
    extractor = ClassMethodExtractor(file_path)
    report = extractor.generate_report(class_name)
    
    print(json.dumps(report, indent=2))

