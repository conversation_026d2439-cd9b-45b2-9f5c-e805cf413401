#!/usr/bin/env python
"""
Documentation Generator for MobileAppAutomation

This script analyzes the project codebase and generates comprehensive documentation
using OpenAI. It creates structured documentation organized into sections for:
- Project overview
- Architecture
- Modules/Components
- API reference
- Usage guides
- And more

Usage:
    python generate_documentation.py
"""

import os
import sys
import json
import re
import glob
import time
import logging
import shutil
from pathlib import Path
import openai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY environment variable is not set. Please set it before running this script.")
    sys.exit(1)

openai.api_key = OPENAI_API_KEY

# Project directories to document
DIRECTORIES = [
    "app",
    "app/utils",
    "app/actions",
    "app/routes",
    "app/templates",
    "app/static",
    "test_cases",
    "image_comparison",
    "scripts"
]

# Root directory for documentation output
DOCS_ROOT = "docs"

# Create documentation directories if they don't exist
DOCS_STRUCTURE = [
    "overview",
    "architecture",
    "api",
    "modules",
    "guides",
    "examples",
    "troubleshooting"
]

for dir_name in DOCS_STRUCTURE:
    os.makedirs(os.path.join(DOCS_ROOT, dir_name), exist_ok=True)

def read_file_content(file_path):
    """Read the content of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def get_file_extension(file_path):
    """Get the extension of a file."""
    return os.path.splitext(file_path)[1].lower()

def is_code_file(file_path):
    """Check if a file is a code file based on its extension."""
    code_extensions = [
        '.py', '.js', '.html', '.css', '.json', '.md', '.sh', '.bat'
    ]
    return get_file_extension(file_path) in code_extensions

def analyze_directory(directory):
    """Analyze a directory and return information about its files."""
    files_info = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            if is_code_file(file_path):
                relative_path = os.path.relpath(file_path, start=os.getcwd())
                files_info.append({
                    "path": relative_path,
                    "extension": get_file_extension(file_path),
                    "size": os.path.getsize(file_path)
                })
    
    return files_info

def get_file_summary(file_path, max_tokens=8000):
    """Generate a summary of a file using OpenAI."""
    content = read_file_content(file_path)
    if not content:
        return f"Failed to read file: {file_path}"
    
    # Simple truncation for very large files to avoid token limits
    if len(content) > max_tokens * 4:  # Rough estimation of characters to tokens
        content = content[:max_tokens * 4] + "...[content truncated]"
    
    file_extension = get_file_extension(file_path)
    prompt = f"""
    You are an expert software documentation writer. Please analyze the following {file_extension} file and provide:
    
    1. A brief description of what this file does
    2. Its main functionality and purpose in the project
    3. Key classes and functions (if applicable)
    4. Any dependencies or important interactions with other parts of the system
    
    Be concise but comprehensive.
    
    File path: {file_path}
    
    ```{file_extension}
    {content}
    ```
    """
    
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "system", "content": "You are an expert software documentation assistant."},
                      {"role": "user", "content": prompt}],
            max_tokens=1500
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"Error getting summary for {file_path}: {e}")
        return f"Failed to generate summary for: {file_path}\nError: {str(e)}"

def generate_module_documentation(module_path, output_path):
    """Generate documentation for a module."""
    logger.info(f"Generating documentation for module: {module_path}")
    
    # Get files in the module
    files_info = analyze_directory(module_path)
    
    # Prepare module overview prompt
    module_name = os.path.basename(module_path)
    files_list = "\n".join([f"- {f['path']}" for f in files_info])
    
    module_overview_prompt = f"""
    You are an expert software documentation writer. Please analyze the following module information and provide:
    
    1. A comprehensive overview of the {module_name} module
    2. Its purpose in the project
    3. How it interacts with other modules
    4. Key components and their functions
    
    Module: {module_name}
    Files:
    {files_list}
    """
    
    try:
        # Generate module overview
        module_overview_response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "system", "content": "You are an expert software documentation assistant."},
                      {"role": "user", "content": module_overview_prompt}],
            max_tokens=2000
        )
        module_overview = module_overview_response.choices[0].message.content
        
        # Create module documentation
        module_doc = f"# {module_name.capitalize()} Module\n\n{module_overview}\n\n"
        
        # Add file summaries
        for file_info in files_info:
            if file_info['size'] > 1000:  # Only document substantial files
                file_summary = get_file_summary(file_info['path'])
                module_doc += f"\n## {os.path.basename(file_info['path'])}\n\n{file_summary}\n\n"
                # Add a small delay to avoid API rate limits
                time.sleep(0.5)
        
        # Write module documentation to file
        output_file = os.path.join(output_path, f"{module_name.lower()}.md")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(module_doc)
        
        logger.info(f"Successfully generated documentation for {module_name} at {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error generating documentation for module {module_name}: {e}")
        return False

def generate_architecture_documentation():
    """Generate architecture documentation for the project."""
    logger.info("Generating architecture documentation")
    
    # Read important structural files
    run_py = read_file_content("run.py")
    app_py = read_file_content("app/app.py")
    config_py = read_file_content("config.py")
    readme = read_file_content("README.md")
    
    architecture_prompt = f"""
    You are an expert software architect. Please analyze the following information about the MobileAppAutomation project and provide a comprehensive architecture document that includes:
    
    1. High-level architecture overview
    2. System components and their relationships
    3. Data flow through the system
    4. Key design patterns used
    5. Integration points with external systems (Appium, mobile devices, etc.)
    6. Deployment architecture
    
    Create detailed diagrams in Mermaid format where appropriate to visualize the architecture.
    
    Here are key files that define the architecture:
    
    run.py:
    ```python
    {run_py[:5000] if run_py else "File not found"}
    ```
    
    app/app.py (first 5000 chars):
    ```python
    {app_py[:5000] if app_py else "File not found"}
    ```
    
    config.py:
    ```python
    {config_py[:5000] if config_py else "File not found"}
    ```
    
    README.md excerpt:
    ```markdown
    {readme[:5000] if readme else "File not found"}
    ```
    """
    
    try:
        # Generate architecture document
        architecture_response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "system", "content": "You are an expert software architecture documentation writer."},
                     {"role": "user", "content": architecture_prompt}],
            max_tokens=4000
        )
        architecture_doc = architecture_response.choices[0].message.content
        
        # Write architecture documentation to file
        output_file = os.path.join(DOCS_ROOT, "architecture", "overview.md")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# System Architecture\n\n")
            f.write(architecture_doc)
        
        logger.info(f"Successfully generated architecture documentation at {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error generating architecture documentation: {e}")
        return False

def generate_project_overview():
    """Generate project overview documentation."""
    logger.info("Generating project overview documentation")
    
    # Read README and other important files
    readme = read_file_content("README.md")
    
    overview_prompt = f"""
    You are an expert technical writer. Please analyze the README of the MobileAppAutomation project and create a comprehensive project overview document that includes:
    
    1. Project purpose and goals
    2. Key features and capabilities
    3. Technologies used
    4. Project structure overview
    5. Getting started guide (installation, configuration, basic usage)
    
    Format the document in clear, well-structured Markdown with proper headings, lists, and code blocks where appropriate.
    
    README.md:
    ```markdown
    {readme if readme else "File not found"}
    ```
    """
    
    try:
        # Generate project overview
        overview_response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "system", "content": "You are an expert technical documentation writer."},
                     {"role": "user", "content": overview_prompt}],
            max_tokens=3000
        )
        overview_doc = overview_response.choices[0].message.content
        
        # Write overview documentation to file
        output_file = os.path.join(DOCS_ROOT, "overview", "project_overview.md")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# MobileAppAutomation: Project Overview\n\n")
            f.write(overview_doc)
        
        logger.info(f"Successfully generated project overview at {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error generating project overview: {e}")
        return False

def generate_api_documentation():
    """Generate API reference documentation."""
    logger.info("Generating API reference documentation")
    
    # Find all routes in app.py
    app_py = read_file_content("app/app.py")
    if not app_py:
        logger.error("Could not read app/app.py")
        return False
    
    # Extract routes using regex
    route_pattern = r'@app\.route\([\'"]([^\'"]+)[\'"](?:,\s*methods=\[([^\]]+)\])?\)'
    routes = re.findall(route_pattern, app_py)
    
    if not routes:
        logger.error("No routes found in app/app.py")
        return False
    
    # Prepare API documentation prompt
    routes_formatted = []
    for route, methods in routes:
        methods_str = methods if methods else "'GET'"  # Default to GET if methods not specified
        routes_formatted.append(f"- {route} [{methods_str}]")
    
    routes_list = "\n".join(routes_formatted)
    
    api_prompt = f"""
    You are an API documentation expert. Please analyze the following Flask routes from the MobileAppAutomation project and create comprehensive API reference documentation that includes:
    
    1. Endpoint purpose and functionality
    2. Request parameters and format
    3. Response format and possible status codes
    4. Example usage (where appropriate)
    
    Group related endpoints together and organize them in a logical way.
    Format the documentation in clear, well-structured Markdown with proper headings, tables, and code blocks.
    
    Routes:
    {routes_list}
    
    Here is some context about the API from app.py (first 5000 chars):
    ```python
    {app_py[:5000]}
    ```
    """
    
    try:
        # Generate API documentation
        api_response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "system", "content": "You are an API documentation specialist."},
                     {"role": "user", "content": api_prompt}],
            max_tokens=4000
        )
        api_doc = api_response.choices[0].message.content
        
        # Write API documentation to file
        output_file = os.path.join(DOCS_ROOT, "api", "endpoints.md")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# API Reference\n\n")
            f.write(api_doc)
        
        logger.info(f"Successfully generated API documentation at {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error generating API documentation: {e}")
        return False

def generate_usage_guides():
    """Generate usage guides for the project."""
    logger.info("Generating usage guides")
    
    # Read README and other informative files
    readme = read_file_content("README.md")
    
    guides = [
        {
            "name": "getting_started",
            "title": "Getting Started Guide",
            "prompt": f"""
            Create a comprehensive getting started guide for the MobileAppAutomation project. Include:
            
            1. Prerequisites and system requirements
            2. Installation steps
            3. Initial configuration
            4. First test automation example
            5. Common issues and troubleshooting
            
            Base your guide on this README:
            ```markdown
            {readme[:10000] if readme else "File not found"}
            ```
            """
        },
        {
            "name": "android_testing",
            "title": "Android Testing Guide",
            "prompt": f"""
            Create a detailed guide for automating Android app testing with MobileAppAutomation. Include:
            
            1. Android-specific setup requirements
            2. Connecting to Android devices
            3. Recording interactions on Android
            4. Special considerations for Android automation
            5. Troubleshooting Android-specific issues
            
            Base your guide on this README:
            ```markdown
            {readme[:10000] if readme else "File not found"}
            ```
            """
        },
        {
            "name": "ios_testing",
            "title": "iOS Testing Guide",
            "prompt": f"""
            Create a detailed guide for automating iOS app testing with MobileAppAutomation. Include:
            
            1. iOS-specific setup requirements (Xcode, WebDriverAgent, etc.)
            2. Connecting to iOS devices
            3. Recording interactions on iOS
            4. Special considerations for iOS automation
            5. Troubleshooting iOS-specific issues
            
            Base your guide on this README:
            ```markdown
            {readme[:10000] if readme else "File not found"}
            ```
            """
        },
        {
            "name": "visual_testing",
            "title": "Visual Testing Guide",
            "prompt": f"""
            Create a detailed guide for visual testing with MobileAppAutomation. Include:
            
            1. Setting up baseline images
            2. Capturing screenshots during test runs
            3. Comparing images between test runs
            4. Analyzing visual differences
            5. Common issues and best practices
            
            Base your guide on this README:
            ```markdown
            {readme[:10000] if readme else "File not found"}
            ```
            """
        }
    ]
    
    for guide in guides:
        try:
            # Generate guide
            guide_response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[{"role": "system", "content": "You are an expert technical documentation writer."},
                         {"role": "user", "content": guide["prompt"]}],
                max_tokens=3000
            )
            guide_doc = guide_response.choices[0].message.content
            
            # Write guide to file
            output_file = os.path.join(DOCS_ROOT, "guides", f"{guide['name']}.md")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# {guide['title']}\n\n")
                f.write(guide_doc)
            
            logger.info(f"Successfully generated guide: {guide['title']}")
            
            # Add a small delay to avoid API rate limits
            time.sleep(1)
            
        except Exception as e:
            logger.error(f"Error generating guide {guide['title']}: {e}")
    
    return True

def generate_main_index():
    """Generate the main index.md file for the documentation."""
    logger.info("Generating main documentation index")
    
    main_index = """# MobileAppAutomation Documentation

## Overview

MobileAppAutomation is a Python-based mobile app automation tool with record and playback capabilities, built using Flask, OpenCV, Appium, and ADB. The tool provides features for element detection and text recognition for both Android and iOS platforms.

## Documentation Sections

### [Project Overview](./overview/project_overview.md)
Get a high-level understanding of the project, its goals, and features.

### [Architecture](./architecture/overview.md)
Understand the system architecture, components, and their relationships.

### [API Reference](./api/endpoints.md)
Detailed documentation of the application's API endpoints.

### [Modules](./modules/)
Detailed documentation of each module in the codebase.

### Guides
Step-by-step guides for various tasks:
- [Getting Started Guide](./guides/getting_started.md)
- [Android Testing Guide](./guides/android_testing.md)
- [iOS Testing Guide](./guides/ios_testing.md)
- [Visual Testing Guide](./guides/visual_testing.md)

### [Troubleshooting](./troubleshooting/common_issues.md)
Solutions to common issues and problems.

## How to Use This Documentation

This documentation is organized to help you quickly find the information you need:

1. Start with the **Project Overview** to understand what the system does
2. Explore the **Architecture** to understand how the system is designed
3. Use the **Guides** for step-by-step instructions on specific tasks
4. Refer to the **API Reference** for details on API endpoints
5. Dive into the **Modules** section when you need to understand specific parts of the codebase
6. Check the **Troubleshooting** section if you encounter issues

"""
    
    # Write main index to file
    output_file = os.path.join(DOCS_ROOT, "index.md")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(main_index)
    
    logger.info(f"Successfully generated main documentation index at {output_file}")
    return True

def generate_troubleshooting_guide():
    """Generate a troubleshooting guide."""
    logger.info("Generating troubleshooting guide")
    
    # Read README for troubleshooting information
    readme = read_file_content("README.md")
    
    troubleshooting_prompt = f"""
    You are an expert support engineer. Please analyze the README of the MobileAppAutomation project and create a comprehensive troubleshooting guide that includes:
    
    1. Common issues and their solutions, organized by category
    2. Step-by-step diagnosis procedures
    3. How to identify and resolve connection issues with devices
    4. How to fix installation problems
    5. Solutions for test recording and playback issues
    6. How to troubleshoot visual testing problems
    
    Format the guide as a well-structured Markdown document with clear headings, steps, and code examples where appropriate.
    
    README.md:
    ```markdown
    {readme if readme else "File not found"}
    ```
    """
    
    try:
        # Generate troubleshooting guide
        troubleshooting_response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[{"role": "system", "content": "You are an expert technical support documentation writer."},
                     {"role": "user", "content": troubleshooting_prompt}],
            max_tokens=3000
        )
        troubleshooting_doc = troubleshooting_response.choices[0].message.content
        
        # Write troubleshooting guide to file
        output_file = os.path.join(DOCS_ROOT, "troubleshooting", "common_issues.md")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Troubleshooting Guide\n\n")
            f.write(troubleshooting_doc)
        
        logger.info(f"Successfully generated troubleshooting guide at {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error generating troubleshooting guide: {e}")
        return False

def main():
    """Main function to generate all documentation."""
    logger.info("Starting documentation generation")
    
    # Create a clean docs directory
    if os.path.exists(DOCS_ROOT):
        shutil.rmtree(DOCS_ROOT)
    for dir_name in DOCS_STRUCTURE:
        os.makedirs(os.path.join(DOCS_ROOT, dir_name), exist_ok=True)
    
    # Generate project overview
    generate_project_overview()
    
    # Generate architecture documentation
    generate_architecture_documentation()
    
    # Generate API documentation
    generate_api_documentation()
    
    # Generate module documentation
    for directory in DIRECTORIES:
        if os.path.exists(directory):
            generate_module_documentation(directory, os.path.join(DOCS_ROOT, "modules"))
    
    # Generate usage guides
    generate_usage_guides()
    
    # Generate troubleshooting guide
    generate_troubleshooting_guide()
    
    # Generate main index
    generate_main_index()
    
    logger.info(f"Documentation generation complete. Results are in the {DOCS_ROOT} directory.")

if __name__ == "__main__":
    main() 