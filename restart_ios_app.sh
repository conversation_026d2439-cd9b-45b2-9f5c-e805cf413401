#!/bin/bash

# <PERSON>ript to restart iOS app with proper port isolation
# This script cleans up existing processes and restarts the iOS app

set -e

echo "🔄 Restarting iOS app with port isolation..."

# Kill existing Appium processes
echo "🔍 Killing existing Appium processes..."
pkill -f appium || true
sleep 2

# Kill existing iproxy processes for iOS
echo "🔍 Killing existing iproxy processes..."
pkill -f iproxy || true
sleep 1

# Kill existing tidevice processes
echo "🔍 Killing existing tidevice processes..."
pkill -f tidevice || true
sleep 1

# Kill existing Python processes running the iOS app
echo "🔍 Killing existing iOS app processes..."
pkill -f "python.*run.py" || true
pkill -f "python.*app/app.py" || true
sleep 2

# Clean up any stale ports
echo "🔧 Cleaning up stale ports..."
# Kill any processes using port 4723 (Appium)
lsof -ti:4723 | xargs kill -9 2>/dev/null || true
# Kill any processes using port 8200 (WDA)
lsof -ti:8200 | xargs kill -9 2>/dev/null || true
# Kill any processes using port 8080 (Flask)
lsof -ti:8080 | xargs kill -9 2>/dev/null || true

sleep 2

# Verify ports are free
echo "✅ Verifying ports are free..."
if lsof -ti:4723; then
    echo "❌ Port 4723 is still in use"
    exit 1
fi

if lsof -ti:8200; then
    echo "❌ Port 8200 is still in use"
    exit 1
fi

if lsof -ti:8080; then
    echo "❌ Port 8080 is still in use"
    exit 1
fi

echo "✅ All ports are free"

# Start the iOS app with proper port configuration
echo "🚀 Starting iOS app..."
cd "$(dirname "$0")"

# Set environment variables for port isolation
export FLASK_PORT=8080
export APPIUM_PORT=4723
export WDA_PORT=8200

# Start Appium server for iOS
echo "📱 Starting Appium server for iOS on port 4723..."
nohup appium --port 4723 --address 127.0.0.1 > logs/appium_ios.log 2>&1 &
APP_PID=$!
echo "Appium iOS started with PID: $APP_PID"
sleep 5

# Start the iOS Flask app
echo "🌐 Starting iOS Flask app on port 8080..."
python run.py --flask-port 8080 --appium-port 4723 --wda-port 8200 > logs/ios_app.log 2>&1 &
FLASK_PID=$!
echo "iOS Flask app started with PID: $FLASK_PID"

# Save PIDs for later cleanup
echo $APP_PID > logs/ios_appium.pid
echo $FLASK_PID > logs/ios_flask.pid

echo "✅ iOS app restarted successfully!"
echo "📊 Appium server: http://localhost:4723"
echo "🌐 iOS app: http://localhost:8080"
echo "📱 WDA port: 8200"
echo ""
echo "📋 Logs:"
echo "   Appium: logs/appium_ios.log"
echo "   Flask: logs/ios_app.log"
echo ""
echo "🔄 To stop: ./stop_ios_app.sh"