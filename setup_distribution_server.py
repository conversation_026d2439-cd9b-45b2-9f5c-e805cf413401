#!/usr/bin/env python3
"""
Distribution Server Setup Script

This script sets up the distribution server with initial data, app packages,
and user permissions for the secure distribution system.
"""

import os
import sys
import json
import logging
import requests
from pathlib import Path
import time
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DistributionServerSetup:
    """Sets up the distribution server with initial configuration"""
    
    def __init__(self, server_url='http://localhost:8080'):
        self.server_url = server_url
        self.admin_token = None
        self.project_root = Path(__file__).parent
        self.packages_dir = self.project_root / 'distribution_packages'
        
    def wait_for_server(self, timeout=60):
        """Wait for the server to be ready"""
        logger.info("Waiting for server to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.server_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("Server is ready!")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(2)
        
        logger.error("Server did not become ready within timeout")
        return False
    
    def admin_login(self, email=None, password=None):
        """Login as admin user"""
        try:
            # Use environment variables or defaults
            admin_email = email or os.environ.get('ADMIN_EMAIL', '<EMAIL>')
            admin_password = password or os.environ.get('ADMIN_PASSWORD', 'admin123')
            
            login_data = {
                'email': admin_email,
                'password': admin_password
            }
            
            response = requests.post(f"{self.server_url}/api/auth/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data['access_token']
                logger.info(f"Admin login successful: {admin_email}")
                return True
            else:
                logger.error(f"Admin login failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Admin login error: {e}")
            return False
    
    def get_auth_headers(self):
        """Get authentication headers"""
        if not self.admin_token:
            raise Exception("Admin not logged in")
        
        return {
            'Authorization': f'Bearer {self.admin_token}',
            'Content-Type': 'application/json'
        }
    
    def create_encryption_keys(self, packages):
        """Create encryption keys for packages"""
        try:
            logger.info("Creating encryption keys...")
            
            # For this demo, we'll create a simple key mapping
            # In production, you'd want more sophisticated key management
            encryption_keys = {}
            
            for package in packages:
                key_data = package.get('encryption_key', {})
                
                # Create encryption key record
                key_record = {
                    'id': str(uuid.uuid4()),
                    'key_data': key_data.get('key_data', ''),
                    'salt': key_data.get('salt', ''),
                    'is_active': True
                }
                
                encryption_keys[package['id']] = key_record
                logger.info(f"Created encryption key for package: {package['name']}")
            
            return encryption_keys
            
        except Exception as e:
            logger.error(f"Failed to create encryption keys: {e}")
            return {}
    
    def setup_apps_from_packages(self):
        """Set up apps from package manifest"""
        try:
            manifest_file = self.packages_dir / 'package_manifest.json'
            
            if not manifest_file.exists():
                logger.error("Package manifest not found. Run prepare_app_packages.py first.")
                return False
            
            with open(manifest_file, 'r') as f:
                manifest = json.load(f)
            
            packages = manifest.get('packages', [])
            logger.info(f"Found {len(packages)} packages to set up")
            
            # Create encryption keys
            encryption_keys = self.create_encryption_keys(packages)
            
            # Set up each app
            for package in packages:
                success = self.create_app_from_package(package, encryption_keys)
                if not success:
                    logger.warning(f"Failed to create app: {package['name']}")
            
            logger.info("App setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set up apps: {e}")
            return False
    
    def create_app_from_package(self, package, encryption_keys):
        """Create an app record from package data"""
        try:
            # Get encryption key ID
            encryption_key_id = encryption_keys.get(package['id'], {}).get('id')
            
            if not encryption_key_id:
                logger.error(f"No encryption key found for package: {package['id']}")
                return False
            
            # Prepare app data
            app_data = {
                'name': package['name'],
                'description': package['description'],
                'platform': package['platform'],
                'version': package['version'],
                'file_path': package['file_path'],
                'file_hash': package['file_hash'],
                'encryption_key_id': encryption_key_id,
                'is_active': True
            }
            
            # Note: In a real implementation, you'd have admin endpoints for this
            # For now, we'll simulate the database operations
            logger.info(f"Would create app: {app_data['name']} ({app_data['platform']})")
            
            # Store encryption key data (in production, this would go to the database)
            key_data = encryption_keys[package['id']]
            logger.info(f"Encryption key created with ID: {encryption_key_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create app from package: {e}")
            return False
    
    def create_demo_users(self):
        """Create demo users for testing"""
        try:
            logger.info("Creating demo users...")
            
            demo_users = [
                {
                    'email': '<EMAIL>',
                    'password': 'demo123',
                    'first_name': 'Demo',
                    'last_name': 'User'
                },
                {
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name': 'Test',
                    'last_name': 'User'
                }
            ]
            
            created_users = []
            
            for user_data in demo_users:
                response = requests.post(
                    f"{self.server_url}/api/auth/register",
                    json=user_data
                )
                
                if response.status_code == 201:
                    user_info = response.json()['user']
                    created_users.append(user_info)
                    logger.info(f"Created demo user: {user_data['email']}")
                else:
                    logger.warning(f"Failed to create user {user_data['email']}: {response.status_code}")
            
            return created_users
            
        except Exception as e:
            logger.error(f"Failed to create demo users: {e}")
            return []
    
    def setup_user_permissions(self, users, apps):
        """Set up user permissions for apps"""
        try:
            logger.info("Setting up user permissions...")
            
            # For demo purposes, grant all users access to all apps
            for user in users:
                for app in apps:
                    # In production, you'd use admin API endpoints
                    logger.info(f"Would grant user {user['email']} access to {app['name']}")
            
            logger.info("User permissions setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set up user permissions: {e}")
            return False
    
    def verify_setup(self):
        """Verify the setup is working correctly"""
        try:
            logger.info("Verifying setup...")
            
            # Test health endpoint
            response = requests.get(f"{self.server_url}/health")
            if response.status_code != 200:
                logger.error("Health check failed")
                return False
            
            # Test authentication
            if not self.admin_token:
                logger.error("Admin authentication failed")
                return False
            
            # Test app listing (would need to implement this endpoint)
            # response = requests.get(f"{self.server_url}/api/apps/list", 
            #                        headers=self.get_auth_headers())
            
            logger.info("Setup verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Setup verification failed: {e}")
            return False
    
    def run_setup(self):
        """Run the complete setup process"""
        try:
            logger.info("Starting distribution server setup...")
            
            # Wait for server to be ready
            if not self.wait_for_server():
                return False
            
            # Login as admin
            if not self.admin_login():
                return False
            
            # Set up apps from packages
            if not self.setup_apps_from_packages():
                return False
            
            # Create demo users
            demo_users = self.create_demo_users()
            
            # Set up user permissions (placeholder)
            # self.setup_user_permissions(demo_users, apps)
            
            # Verify setup
            if not self.verify_setup():
                return False
            
            logger.info("Distribution server setup completed successfully!")
            
            # Print summary
            self.print_setup_summary(demo_users)
            
            return True
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            return False
    
    def print_setup_summary(self, demo_users):
        """Print setup summary"""
        print("\\n" + "="*60)
        print("🎉 DISTRIBUTION SERVER SETUP COMPLETE!")
        print("="*60)
        print(f"\\n📡 Server URL: {self.server_url}")
        print(f"📁 Packages Directory: {self.packages_dir}")
        
        print("\\n👥 Demo Users Created:")
        for user in demo_users:
            print(f"   📧 {user['email']} - Password: demo123/test123")
        
        print("\\n📱 Available Apps:")
        manifest_file = self.packages_dir / 'package_manifest.json'
        if manifest_file.exists():
            with open(manifest_file, 'r') as f:
                manifest = json.load(f)
            
            for package in manifest.get('packages', []):
                print(f"   📦 {package['name']} ({package['platform']})")
        
        print("\\n🔧 Next Steps:")
        print("1. Test the secure distribution client")
        print("2. Create production user accounts")
        print("3. Configure SSL/HTTPS for production")
        print("4. Set up monitoring and backups")
        print("\\n" + "="*60)

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Set up distribution server')
    parser.add_argument('--server-url', default='http://localhost:8080',
                       help='Server URL (default: http://localhost:8080)')
    parser.add_argument('--admin-email', help='Admin email')
    parser.add_argument('--admin-password', help='Admin password')
    
    args = parser.parse_args()
    
    setup = DistributionServerSetup(args.server_url)
    
    # Override admin credentials if provided
    if args.admin_email and args.admin_password:
        success = setup.admin_login(args.admin_email, args.admin_password)
        if not success:
            logger.error("Failed to login with provided admin credentials")
            sys.exit(1)
    
    success = setup.run_setup()
    
    if not success:
        logger.error("Setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
