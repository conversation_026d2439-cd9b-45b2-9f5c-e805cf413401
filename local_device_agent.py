#!/usr/bin/env python3
"""
Mobile Testing Local Device Agent

This agent runs on subscriber's local machines to:
1. Discover and manage connected mobile devices
2. Execute tests received from the SaaS platform
3. Stream results back to the cloud platform
4. Maintain secure communication with the SaaS backend

Author: Mobile Testing SaaS Platform
Version: 1.0.0
"""

import asyncio
import json
import logging
import os
import platform
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import aiohttp
import socketio
from cryptography.fernet import Fernet
from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.options.ios import XCUITestOptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('agent.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DeviceManager:
    """Manages discovery and communication with mobile devices"""
    
    def __init__(self):
        self.devices = {}
        self.platform_system = platform.system().lower()
        
    async def discover_devices(self) -> List[Dict[str, Any]]:
        """Discover all connected mobile devices"""
        devices = []
        
        # Discover Android devices
        android_devices = await self._discover_android_devices()
        devices.extend(android_devices)
        
        # Discover iOS devices (macOS only)
        if self.platform_system == 'darwin':
            ios_devices = await self._discover_ios_devices()
            devices.extend(ios_devices)
            
        self.devices = {device['id']: device for device in devices}
        logger.info(f"Discovered {len(devices)} devices: {[d['id'] for d in devices]}")
        return devices
    
    async def _discover_android_devices(self) -> List[Dict[str, Any]]:
        """Discover Android devices using ADB"""
        devices = []
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if '\t' in line:
                        device_id, status = line.split('\t')
                        if status == 'device':
                            device_info = await self._get_android_device_info(device_id)
                            if device_info:
                                devices.append(device_info)
        except FileNotFoundError:
            logger.warning("ADB not found. Android device discovery disabled.")
        except Exception as e:
            logger.error(f"Error discovering Android devices: {e}")
            
        return devices
    
    async def _discover_ios_devices(self) -> List[Dict[str, Any]]:
        """Discover iOS devices using idevice tools"""
        devices = []
        try:
            result = subprocess.run(['idevice_id', '-l'], capture_output=True, text=True)
            if result.returncode == 0:
                device_ids = result.stdout.strip().split('\n')
                for device_id in device_ids:
                    if device_id.strip():
                        device_info = await self._get_ios_device_info(device_id.strip())
                        if device_info:
                            devices.append(device_info)
        except FileNotFoundError:
            logger.warning("idevice tools not found. iOS device discovery disabled.")
        except Exception as e:
            logger.error(f"Error discovering iOS devices: {e}")
            
        return devices
    
    async def _get_android_device_info(self, device_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about an Android device"""
        try:
            # Get device properties
            props_cmd = ['adb', '-s', device_id, 'shell', 'getprop']
            result = subprocess.run(props_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                props = {}
                for line in result.stdout.split('\n'):
                    if ': [' in line:
                        key, value = line.split(': [', 1)
                        key = key.strip('[]')
                        value = value.rstrip(']')
                        props[key] = value
                
                return {
                    'id': device_id,
                    'platform': 'android',
                    'name': props.get('ro.product.model', 'Unknown Android Device'),
                    'version': props.get('ro.build.version.release', 'Unknown'),
                    'api_level': props.get('ro.build.version.sdk', 'Unknown'),
                    'manufacturer': props.get('ro.product.manufacturer', 'Unknown'),
                    'status': 'available',
                    'capabilities': {
                        'automationName': 'UiAutomator2',
                        'platformName': 'Android',
                        'platformVersion': props.get('ro.build.version.release', 'Unknown'),
                        'deviceName': props.get('ro.product.model', 'Unknown Android Device'),
                        'udid': device_id
                    }
                }
        except Exception as e:
            logger.error(f"Error getting Android device info for {device_id}: {e}")
            
        return None
    
    async def _get_ios_device_info(self, device_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about an iOS device"""
        try:
            logger.info(f"Getting iOS device info for {device_id}")
            
            # Get device name
            name_cmd = ['ideviceinfo', '-u', device_id, '-k', 'DeviceName']
            name_result = subprocess.run(name_cmd, capture_output=True, text=True)
            logger.info(f"Device name command result: {name_result.returncode}, stdout: '{name_result.stdout.strip()}', stderr: '{name_result.stderr.strip()}'")
            
            # Get iOS version
            version_cmd = ['ideviceinfo', '-u', device_id, '-k', 'ProductVersion']
            version_result = subprocess.run(version_cmd, capture_output=True, text=True)
            logger.info(f"Device version command result: {version_result.returncode}, stdout: '{version_result.stdout.strip()}', stderr: '{version_result.stderr.strip()}'")
            
            # Get device model
            model_cmd = ['ideviceinfo', '-u', device_id, '-k', 'ProductType']
            model_result = subprocess.run(model_cmd, capture_output=True, text=True)
            logger.info(f"Device model command result: {model_result.returncode}, stdout: '{model_result.stdout.strip()}', stderr: '{model_result.stderr.strip()}'")
            
            if all(r.returncode == 0 for r in [name_result, version_result, model_result]):
                device_info = {
                    'id': device_id,
                    'platform': 'ios',
                    'name': name_result.stdout.strip(),
                    'version': version_result.stdout.strip(),
                    'model': model_result.stdout.strip(),
                    'status': 'available',
                    'capabilities': {
                        'automationName': 'XCUITest',
                        'platformName': 'iOS',
                        'platformVersion': version_result.stdout.strip(),
                        'deviceName': name_result.stdout.strip(),
                        'udid': device_id
                    }
                }
                logger.info(f"Successfully created device info: {device_info}")
                return device_info
            else:
                logger.error(f"One or more ideviceinfo commands failed for device {device_id}")
        except Exception as e:
            logger.error(f"Error getting iOS device info for {device_id}: {e}")
            
        return None

class TestExecutor:
    """Executes tests on mobile devices"""
    
    def __init__(self, device_manager: DeviceManager):
        self.device_manager = device_manager
        self.active_sessions = {}
        
    async def execute_test(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a test on the specified device"""
        device_id = test_data.get('device_id')
        test_steps = test_data.get('test_steps', [])
        test_id = test_data.get('test_id')
        
        if device_id not in self.device_manager.devices:
            return {
                'test_id': test_id,
                'status': 'error',
                'message': f'Device {device_id} not found'
            }
        
        device = self.device_manager.devices[device_id]
        
        try:
            # Start Appium session
            driver = await self._create_driver(device)
            self.active_sessions[test_id] = driver
            
            results = []
            for i, step in enumerate(test_steps):
                step_result = await self._execute_step(driver, step, i)
                results.append(step_result)
                
                # If step failed and test should stop on failure
                if not step_result['success'] and test_data.get('stop_on_failure', True):
                    break
            
            # Clean up session
            await self._cleanup_session(test_id)
            
            return {
                'test_id': test_id,
                'status': 'completed',
                'results': results,
                'device_id': device_id,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing test {test_id}: {e}")
            await self._cleanup_session(test_id)
            return {
                'test_id': test_id,
                'status': 'error',
                'message': str(e),
                'device_id': device_id,
                'timestamp': datetime.now().isoformat()
            }
    
    async def _create_driver(self, device: Dict[str, Any]):
        """Create Appium WebDriver for the device"""
        capabilities = device['capabilities'].copy()
        
        if device['platform'] == 'android':
            options = UiAutomator2Options()
            options.load_capabilities(capabilities)
            driver = webdriver.Remote('http://localhost:4723', options=options)
        elif device['platform'] == 'ios':
            options = XCUITestOptions()
            options.load_capabilities(capabilities)
            driver = webdriver.Remote('http://localhost:4723', options=options)
        else:
            raise ValueError(f"Unsupported platform: {device['platform']}")
            
        return driver
    
    async def _execute_step(self, driver, step: Dict[str, Any], step_index: int) -> Dict[str, Any]:
        """Execute a single test step"""
        try:
            action = step.get('action')
            params = step.get('params', {})
            
            if action == 'tap':
                element = driver.find_element(params['by'], params['value'])
                element.click()
            elif action == 'type':
                element = driver.find_element(params['by'], params['value'])
                element.send_keys(params['text'])
            elif action == 'swipe':
                driver.swipe(
                    params['start_x'], params['start_y'],
                    params['end_x'], params['end_y'],
                    params.get('duration', 1000)
                )
            elif action == 'wait':
                await asyncio.sleep(params.get('duration', 1))
            elif action == 'screenshot':
                screenshot = driver.get_screenshot_as_base64()
                return {
                    'step_index': step_index,
                    'action': action,
                    'success': True,
                    'screenshot': screenshot,
                    'timestamp': datetime.now().isoformat()
                }
            
            return {
                'step_index': step_index,
                'action': action,
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing step {step_index}: {e}")
            return {
                'step_index': step_index,
                'action': action,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _cleanup_session(self, test_id: str):
        """Clean up Appium session"""
        if test_id in self.active_sessions:
            try:
                self.active_sessions[test_id].quit()
            except Exception as e:
                logger.error(f"Error cleaning up session {test_id}: {e}")
            finally:
                del self.active_sessions[test_id]

class SaaSAgent:
    """Main agent class that communicates with the SaaS platform"""
    
    def __init__(self, config_path: str = 'config/agent.conf'):
        self.config = self._load_config(config_path)
        self.device_manager = DeviceManager()
        self.test_executor = TestExecutor(self.device_manager)
        self.sio = socketio.AsyncClient()
        self.agent_id = self.config.get('agent_id')
        self.api_key = self.config.get('api_key')
        self.platform_url = self.config.get('platform_url')
        
        # Setup SocketIO event handlers
        self._setup_socketio_handlers()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load agent configuration"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {config_path}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return {}
    
    def _setup_socketio_handlers(self):
        """Setup SocketIO event handlers"""
        
        @self.sio.event
        async def connect():
            logger.info("Connected to SaaS platform")
            await self._register_agent()
            await self._send_device_list()
        
        @self.sio.event
        async def disconnect():
            logger.info("Disconnected from SaaS platform")
        
        @self.sio.event
        async def execute_test(data):
            logger.info(f"Received test execution request: {data.get('test_id')}")
            result = await self.test_executor.execute_test(data)
            await self.sio.emit('test_result', result)
        
        @self.sio.event
        async def refresh_devices(data):
            logger.info("Refreshing device list")
            await self._send_device_list()
        
        @self.sio.event
        async def agent_update(data):
            logger.info(f"Received agent update: {data}")
            # Handle agent updates (restart, configuration changes, etc.)
    
    async def _register_agent(self):
        """Register this agent with the SaaS platform"""
        registration_data = {
            'agent_id': self.agent_id,
            'api_key': self.api_key,
            'platform': platform.system(),
            'version': '1.0.0',
            'capabilities': {
                'android': True,
                'ios': platform.system().lower() == 'darwin'
            }
        }
        await self.sio.emit('register_agent', registration_data)
    
    async def _send_device_list(self):
        """Send current device list to the SaaS platform"""
        devices = await self.device_manager.discover_devices()
        await self.sio.emit('device_list', {
            'agent_id': self.agent_id,
            'devices': devices
        })
    
    async def start(self):
        """Start the agent"""
        logger.info(f"Starting Mobile Testing Agent {self.agent_id}")
        
        # Initial device discovery
        await self.device_manager.discover_devices()
        
        # Connect to SaaS platform
        try:
            await self.sio.connect(f"{self.platform_url}/socket.io/")
            
            # Keep the agent running
            while True:
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
                await self._send_device_list()  # Refresh device list
                
        except KeyboardInterrupt:
            logger.info("Agent stopped by user")
        except Exception as e:
            logger.error(f"Agent error: {e}")
        finally:
            await self.sio.disconnect()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Mobile Testing Local Device Agent')
    parser.add_argument('--config', default='config/agent.conf', help='Configuration file path')
    parser.add_argument('--log-level', default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))
    
    # Create and start agent
    agent = SaaSAgent(args.config)
    
    try:
        asyncio.run(agent.start())
    except KeyboardInterrupt:
        logger.info("Agent stopped")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()