#!/usr/bin/env python
"""
Documentation Generator for MobileAppAutomation

This script generates comprehensive documentation for the MobileAppAutomation project
by analyzing the codebase structure and creating organized documentation in both
Markdown and HTML formats.

Usage:
    python generate_docs.py
"""

import os
import sys
import subprocess
import logging
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_script_exists(script_path):
    """Check if a script exists and is executable."""
    return os.path.isfile(script_path) and os.access(script_path, os.X_OK)

def run_script(script_path, description):
    """Run a Python script and log the result."""
    if not check_script_exists(script_path):
        # Try to make it executable if it exists but isn't executable
        if os.path.isfile(script_path):
            try:
                os.chmod(script_path, 0o755)  # Make executable
                logger.info(f"Made {script_path} executable")
            except Exception as e:
                logger.error(f"Failed to make {script_path} executable: {e}")
                return False
        else:
            logger.error(f"{script_path} not found")
            return False
    
    logger.info(f"Running {description}...")
    start_time = time.time()
    
    try:
        result = subprocess.run(
            [sys.executable, script_path],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        duration = time.time() - start_time
        logger.info(f"{description} completed successfully in {duration:.2f} seconds")
        return True
    
    except subprocess.CalledProcessError as e:
        logger.error(f"{description} failed with exit code {e.returncode}")
        logger.error(f"Error output: {e.stderr}")
        return False
    
    except Exception as e:
        logger.error(f"Error running {description}: {e}")
        return False

def main():
    """Main function to coordinate documentation generation."""
    logger.info("Starting documentation generation process")
    
    # Create docs directory if it doesn't exist
    os.makedirs("docs", exist_ok=True)
    
    # Step 1: Analyze codebase
    if not run_script("analyze_codebase.py", "codebase analysis"):
        logger.error("Codebase analysis failed, stopping documentation generation")
        return
    
    # Step 2: Generate HTML documentation
    run_script("generate_html_docs.py", "HTML documentation generation")
    
    logger.info("Documentation generation complete")
    logger.info("You can view the documentation in:")
    logger.info("  - Markdown format: docs/")
    logger.info("  - HTML format: docs/html/")
    logger.info("\nTo view the HTML documentation in your browser, open:")
    logger.info(f"  {os.path.abspath(os.path.join('docs', 'html', 'index.html'))}")

if __name__ == "__main__":
    main() 