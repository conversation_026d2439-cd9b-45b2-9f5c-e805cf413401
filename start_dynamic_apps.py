#!/usr/bin/env python3
"""
Dynamic Multi-Platform Mobile Automation Launcher

This script starts iOS and Android automation apps with dynamic port allocation,
ensuring no port conflicts when running multiple instances.
"""

import os
import sys
import time
import signal
import subprocess
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add utils to path
sys.path.insert(0, str(Path(__file__).parent / 'utils'))

class DynamicAppLauncher:
    """Manages dynamic launching of iOS and Android automation apps"""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.allocated_ports: Dict[str, Dict[str, int]] = {}
        self.dashboard_process: Optional[subprocess.Popen] = None
        
    def cleanup_on_exit(self, signum=None, frame=None):
        """Clean up processes and ports on exit"""
        logger.info("Cleaning up processes and ports...")
        
        # Terminate all processes
        for platform, process in self.processes.items():
            if process and process.poll() is None:
                logger.info(f"Terminating {platform} app...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning(f"Force killing {platform} app...")
                    process.kill()
                    
        if self.dashboard_process and self.dashboard_process.poll() is None:
            logger.info("Terminating dashboard...")
            self.dashboard_process.terminate()
            try:
                self.dashboard_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning("Force killing dashboard...")
                self.dashboard_process.kill()
        
        # Clean up allocated ports
        try:
            from dynamic_port_init import cleanup_platform_ports
            for platform in self.allocated_ports.keys():
                cleanup_platform_ports(platform)
                logger.info(f"Cleaned up {platform} ports")
        except Exception as e:
            logger.error(f"Failed to cleanup ports: {e}")
            
        sys.exit(0)
        
    def start_platform_app(self, platform: str, custom_ports: Optional[Dict[str, int]] = None) -> bool:
        """Start a platform-specific app with dynamic port allocation"""
        try:
            logger.info(f"Starting {platform} app with dynamic ports...")
            
            # Initialize dynamic ports
            from dynamic_port_init import initialize_platform_ports
            allocated_ports = initialize_platform_ports(platform, custom_ports)
            self.allocated_ports[platform] = allocated_ports
            
            # Determine script to run
            if platform == 'ios':
                script_name = 'run.py'
            elif platform == 'android':
                script_name = 'run_android.py'
            else:
                raise ValueError(f"Unsupported platform: {platform}")
                
            # Build command
            cmd = [
                sys.executable,
                script_name,
                '--flask-port', str(allocated_ports['flask']),
                '--appium-port', str(allocated_ports['appium']),
                '--wda-port', str(allocated_ports['wda'])
            ]
            
            # Start the process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes[platform] = process
            
            logger.info(f"{platform.upper()} app started successfully:")
            logger.info(f"  - Flask: http://localhost:{allocated_ports['flask']}")
            logger.info(f"  - Appium: {allocated_ports['appium']}")
            logger.info(f"  - WDA: {allocated_ports['wda']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start {platform} app: {e}")
            return False
            
    def start_dashboard(self, port: Optional[int] = None) -> bool:
        """Start the dashboard with appropriate port"""
        try:
            logger.info("Starting dashboard...")
            
            # Set environment for dashboard
            env = os.environ.copy()
            
            # Set dashboard port
            if port:
                env['DASHBOARD_PORT'] = str(port)
            else:
                # Use port 8080 for secure builds, 8090 for development
                env['SECURE_BUILD'] = 'true'  # This will make dashboard use port 8080
                
            # Set service URLs based on allocated ports
            if 'ios' in self.allocated_ports:
                env['IOS_APP_URL'] = f"http://localhost:{self.allocated_ports['ios']['flask']}"
            if 'android' in self.allocated_ports:
                env['ANDROID_APP_URL'] = f"http://localhost:{self.allocated_ports['android']['flask']}"
                
            # Start dashboard
            cmd = [sys.executable, 'dashboard_app.py']
            self.dashboard_process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            dashboard_port = port or 8080
            logger.info(f"Dashboard started: http://localhost:{dashboard_port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start dashboard: {e}")
            return False
            
    def monitor_processes(self):
        """Monitor running processes and restart if needed"""
        logger.info("Monitoring processes... Press Ctrl+C to stop all services.")
        
        try:
            while True:
                # Check if any process has died
                for platform, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.warning(f"{platform} app has stopped unexpectedly")
                        # Could implement restart logic here
                        
                if self.dashboard_process and self.dashboard_process.poll() is not None:
                    logger.warning("Dashboard has stopped unexpectedly")
                    
                time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
            self.cleanup_on_exit()
            
def main():
    parser = argparse.ArgumentParser(description='Dynamic Multi-Platform Mobile Automation Launcher')
    parser.add_argument('--platforms', nargs='+', choices=['ios', 'android'], 
                       default=['ios', 'android'], help='Platforms to start')
    parser.add_argument('--dashboard', action='store_true', default=True, 
                       help='Start dashboard (default: True)')
    parser.add_argument('--dashboard-port', type=int, 
                       help='Dashboard port (default: 8080 for secure builds, 8090 for dev)')
    parser.add_argument('--ios-flask-port', type=int, help='Custom iOS Flask port')
    parser.add_argument('--ios-appium-port', type=int, help='Custom iOS Appium port')
    parser.add_argument('--ios-wda-port', type=int, help='Custom iOS WDA port')
    parser.add_argument('--android-flask-port', type=int, help='Custom Android Flask port')
    parser.add_argument('--android-appium-port', type=int, help='Custom Android Appium port')
    parser.add_argument('--android-wda-port', type=int, help='Custom Android WDA port')
    
    args = parser.parse_args()
    
    launcher = DynamicAppLauncher()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, launcher.cleanup_on_exit)
    signal.signal(signal.SIGTERM, launcher.cleanup_on_exit)
    
    # Start requested platforms
    success_count = 0
    
    for platform in args.platforms:
        custom_ports = {}
        
        if platform == 'ios':
            if args.ios_flask_port:
                custom_ports['flask'] = args.ios_flask_port
            if args.ios_appium_port:
                custom_ports['appium'] = args.ios_appium_port
            if args.ios_wda_port:
                custom_ports['wda'] = args.ios_wda_port
        elif platform == 'android':
            if args.android_flask_port:
                custom_ports['flask'] = args.android_flask_port
            if args.android_appium_port:
                custom_ports['appium'] = args.android_appium_port
            if args.android_wda_port:
                custom_ports['wda'] = args.android_wda_port
                
        if launcher.start_platform_app(platform, custom_ports if custom_ports else None):
            success_count += 1
            time.sleep(2)  # Give each app time to start
            
    if success_count == 0:
        logger.error("Failed to start any platform apps")
        return 1
        
    # Start dashboard if requested
    if args.dashboard:
        launcher.start_dashboard(args.dashboard_port)
        
    # Monitor processes
    launcher.monitor_processes()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())