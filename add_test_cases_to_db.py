#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the locator verification test cases to the Android database.
"""

import sys
import os
import json
import sqlite3
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_android.utils.database import get_db_path

def add_test_case_to_db(test_case_file, suite_id):
    """Add a test case to the database."""
    # Read the test case JSON file
    with open(test_case_file, 'r') as f:
        test_case_data = json.load(f)
    
    # Connect to database
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    test_case_id = test_case_data.get('test_case_id')
    name = test_case_data.get('name')
    description = test_case_data.get('description', '')
    platform = test_case_data.get('platform', 'android')
    status = test_case_data.get('status', 'active')
    json_payload = json.dumps(test_case_data)
    
    # Check if test case already exists
    cursor.execute('SELECT test_case_id FROM test_cases WHERE test_case_id = ?', (test_case_id,))
    existing = cursor.fetchone()
    
    if existing:
        # Update existing test case
        cursor.execute('''
            UPDATE test_cases 
            SET name = ?, description = ?, platform = ?, status = ?, json_payload = ?, suite_id = ?
            WHERE test_case_id = ?
        ''', (name, description, platform, status, json_payload, suite_id, test_case_id))
        print(f"✅ Updated test case: {name} ({test_case_id})")
    else:
        # Insert new test case
        cursor.execute('''
            INSERT INTO test_cases (test_case_id, name, description, platform, status, json_payload, suite_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (test_case_id, name, description, platform, status, json_payload, suite_id))
        print(f"✅ Inserted test case: {name} ({test_case_id})")
    
    conn.commit()
    conn.close()

def add_test_suite_to_db(suite_file):
    """Add a test suite to the database."""
    # Read the test suite JSON file
    with open(suite_file, 'r') as f:
        suite_data = json.load(f)
    
    # Connect to database
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    suite_id = suite_data.get('suite_id')
    name = suite_data.get('name')
    description = suite_data.get('description', '')
    platform = suite_data.get('platform', 'android')
    status = suite_data.get('status', 'active')
    
    # Check if suite already exists
    cursor.execute('SELECT suite_id FROM test_suites WHERE suite_id = ?', (suite_id,))
    existing = cursor.fetchone()
    
    if existing:
        # Update existing suite
        cursor.execute('''
            UPDATE test_suites 
            SET name = ?, description = ?, platform = ?, status = ?
            WHERE suite_id = ?
        ''', (name, description, platform, status, suite_id))
        print(f"✅ Updated test suite: {name} ({suite_id})")
    else:
        # Insert new suite
        cursor.execute('''
            INSERT INTO test_suites (suite_id, name, description, platform, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (suite_id, name, description, platform, status))
        print(f"✅ Inserted test suite: {name} ({suite_id})")
    
    conn.commit()
    conn.close()
    
    return suite_id

def main():
    """Main function."""
    print("=" * 80)
    print("ADDING LOCATOR VERIFICATION TEST CASES TO DATABASE")
    print("=" * 80)
    
    # Add test suite
    suite_file = 'test_suites/locator_verification_suite.json'
    suite_id = add_test_suite_to_db(suite_file)
    
    # Add test cases
    test_cases = [
        'test_cases/locator_test_xpath.json',
        'test_cases/locator_test_accessibility.json',
        'test_cases/locator_test_mixed.json'
    ]
    
    for test_case_file in test_cases:
        add_test_case_to_db(test_case_file, suite_id)
    
    print("=" * 80)
    print("✅ ALL TEST CASES ADDED SUCCESSFULLY")
    print("=" * 80)

if __name__ == '__main__':
    main()

