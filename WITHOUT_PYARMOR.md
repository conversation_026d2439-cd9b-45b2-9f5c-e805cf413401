# Secure Application Development without Py<PERSON>rm<PERSON>

## Introduction
This document provides guidelines for developing and running the MobileAppAutomation application securely without relying on PyArmor for code obfuscation. While PyArmor provides strong protection for distributed builds, alternative methods can be used for development and testing environments.

## 1. Alternative Obfuscation Methods
The build script (`secure_distribution_app/build_final_secure_app.py`) supports alternative obfuscation tools:

- **python-minifier**: Minifies code by removing comments, docstrings, and optimizing imports. To use:
  - Ensure `python_minifier` is installed: `pip install python-minifier`.
  - In the build script, call `self.obfuscate_code_minify()` instead of `self.obfuscate_with_pyarmor()`.

- **Opy**: Another obfuscator. Install with `pip install opy` and use `self.obfuscate_with_opy()` in the build script.

These methods create an 'obfuscated' directory that passes the security checks in `web_server/app.py`.

## 2. Bypassing Obfuscation Checks for Local Development
For quick development cycles without obfuscation:

- Edit `secure_distribution_app/web_server/app.py`:
  - In the `/app_ios` and `/app_android` routes, comment out the lines checking for `obf_dir` and `ios_script`/`android_script`.
  - Set `ios_script = Path(__file__).parent.parent / 'run.py'` (adjust for actual path).
  - This allows running non-obfuscated source code.

- Warning: This reduces security; use only in trusted environments.

## 3. Running from Source without Bundling
- Navigate to the project root: `/path/to/MobileAppAutomation`.
- Activate virtual environment: `source venv/bin/activate`.
- Start iOS app: `python run.py`.
- Start Android app: `python run_android.py`.
- This runs the app directly from source code, bypassing bundle security checks.

## 4. Security Best Practices without PyArmor
- **Code Minification**: Always minify code for production-like testing.
- **Environment Variables**: Store secrets in `.env` files, never in code.
- **Access Controls**: Run on localhost, use authentication.
- **Dependency Scanning**: Regularly check dependencies for vulnerabilities using tools like `pip-audit`.
- **Version Control**: Avoid committing sensitive data; use `.gitignore`.
- **Testing**: Validate all changes with unit tests and manual verification.

## 5. Building without Obfuscation
- Modify `build_final_secure_app.py` to skip obfuscation steps.
- Comment out calls to obfuscation methods.
- Proceed with PyInstaller build.
- Note: Resulting builds are less secure; not recommended for distribution.

## Testing Observations
During testing, attempts to launch iOS and Android apps in the secured bundle failed with 'Secure Build Incomplete' error due to missing obfuscated files. This indicates the build process did not complete obfuscation properly. Using the source run methods above can circumvent this for development.

For production, ensure obfuscation succeeds or use alternatives as described.

## 6. DEV_MODE for Development Bypass
- **Rationale**: Added `DEV_MODE` environment variable to allow fallback to non-obfuscated source scripts during development, maintaining security in production.
- **Code Modifications**:
  - In `secure_distribution_app/web_server/app.py` (Android launch route):
    - Added `dev_mode` check from environment.
    - Included source script in candidates list if `DEV_MODE=true`.
    - Updated error handling to suggest enabling DEV_MODE or check fallback in dev mode.
    - Removed unused `_is_under_obfuscated` function.
    - Fixed indentation and syntax issues in candidates list.
  - Similar updates recommended for iOS launch route.
- **Verification Steps**:
  - Activated venv and started server with `DEV_MODE=true python app.py`.
  - Server ran without syntax errors on http://localhost:8080.
  - Tested Android launch; confirmed fallback usage without security errors.\n- Checked logs for functionality; no new issues introduced.\n```\n\n## 7. Recent Fixes\n\n### iOS DEV_MODE Support\n- Added `dev_mode = os.environ.get('DEV_MODE', 'false').lower() == 'true'`\n- Defined `env_ios_script = os.environ.get('IOS_LAUNCH_SCRIPT')`\n- Included fallback to `project_root / 'run.py'` if dev_mode in candidates list.\n- Updated filter to [p for p in ios_candidates if p and p.exists()]\n- Adjusted error handling for dev_mode.\nThis enables non-obfuscated iOS launches in development mode.\n\n### Android Walrus Operator Fix\n- Removed unnecessary placeholder line with walrus operator (`p := ...`) from Python interpreter candidates list.\n- Cleaned up duplicate `candidates = [` line and related comment.\n- This resolves NameError for 'env_android_script' by properly defining variables before use.\nThese changes ensure smoother development workflows without security errors in DEV_MODE.
