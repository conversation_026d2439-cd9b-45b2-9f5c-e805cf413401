
"""
Enhanced Environment Variable Loader for Mobile App Automation

This module provides comprehensive environment variable loading and validation
for the mobile automation framework, including AI healing configuration.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def load_environment_variables():
    """Load environment variables from .env file with enhanced validation"""
    try:
        from dotenv import load_dotenv

        # Get the project root directory
        project_root = Path(__file__).parent
        env_file = project_root / ".env"

        if env_file.exists():
            load_dotenv(env_file)
            logger.info(f"✅ Loaded environment variables from {env_file}")

            # Validate AI healing configuration
            validation_result = validate_ai_configuration()

            if validation_result['valid']:
                logger.info("✅ AI healing configuration validated successfully")
                return True
            else:
                logger.warning(f"⚠️ AI configuration issues: {validation_result['issues']}")
                return False
        else:
            logger.error(f"❌ .env file not found at {env_file}")
            return False

    except ImportError:
        logger.error("❌ python-dotenv not installed. Install with: pip install python-dotenv")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to load environment variables: {e}")
        return False

def validate_ai_configuration() -> Dict[str, Any]:
    """Validate AI healing configuration"""
    issues = []

    # Check Together AI API key
    api_key = os.getenv('TOGETHER_API_KEY')
    if not api_key:
        issues.append("TOGETHER_API_KEY not set")
    elif api_key == 'your_together_ai_api_key_here':
        issues.append("TOGETHER_API_KEY still has placeholder value")
    elif len(api_key) < 10:
        issues.append("TOGETHER_API_KEY appears to be invalid (too short)")

    # Check AI healing settings
    ai_enabled = os.getenv('AI_HEALING_ENABLED', 'true').lower()
    if ai_enabled not in ['true', 'false']:
        issues.append("AI_HEALING_ENABLED must be 'true' or 'false'")

    # Validate numeric settings
    try:
        max_attempts = int(os.getenv('AI_HEALING_MAX_ATTEMPTS', '3'))
        if max_attempts < 1 or max_attempts > 10:
            issues.append("AI_HEALING_MAX_ATTEMPTS should be between 1 and 10")
    except ValueError:
        issues.append("AI_HEALING_MAX_ATTEMPTS must be a valid integer")

    try:
        confidence_threshold = float(os.getenv('AI_HEALING_CONFIDENCE_THRESHOLD', '0.6'))
        if confidence_threshold < 0.0 or confidence_threshold > 1.0:
            issues.append("AI_HEALING_CONFIDENCE_THRESHOLD should be between 0.0 and 1.0")
    except ValueError:
        issues.append("AI_HEALING_CONFIDENCE_THRESHOLD must be a valid float")

    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'api_key_present': bool(api_key),
        'ai_enabled': ai_enabled == 'true'
    }

def get_ai_configuration() -> Dict[str, Any]:
    """Get AI configuration settings"""
    return {
        'enabled': os.getenv('AI_HEALING_ENABLED', 'true').lower() == 'true',
        'api_key': os.getenv('TOGETHER_API_KEY'),
        'base_url': os.getenv('TOGETHER_BASE_URL', 'https://api.together.xyz/v1'),
        'model_name': os.getenv('AI_MODEL_NAME', 'meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo'),
        'max_attempts': int(os.getenv('AI_HEALING_MAX_ATTEMPTS', '3')),
        'confidence_threshold': float(os.getenv('AI_HEALING_CONFIDENCE_THRESHOLD', '0.6')),
        'screenshot_limit': float(os.getenv('SCREENSHOT_FREQUENCY_LIMIT', '5.0')),
        'disable_screenshots': os.getenv('DISABLE_SCREENSHOTS', 'false').lower() == 'true'
    }

def ensure_ai_directories():
    """Ensure AI healing output directories exist"""
    try:
        base_dir = Path.cwd()

        # Create AI healing output directories
        ai_dirs = [
            base_dir / "ai_healing_outputs",
            base_dir / "ai_healing_outputs" / "ios",
            base_dir / "ai_healing_outputs" / "android",
            base_dir / "ai_healing_outputs" / "contexts"
        ]

        for directory in ai_dirs:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {directory}")

        logger.info("✅ AI healing directories created successfully")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to create AI healing directories: {e}")
        return False

def initialize_ai_environment():
    """Initialize complete AI environment"""
    logger.info("🚀 Initializing AI environment...")

    # Load environment variables
    if not load_environment_variables():
        logger.error("❌ Failed to load environment variables")
        return False

    # Ensure directories exist
    if not ensure_ai_directories():
        logger.error("❌ Failed to create AI directories")
        return False

    # Get and log configuration
    config = get_ai_configuration()
    logger.info(f"AI Healing Enabled: {config['enabled']}")
    logger.info(f"Max Attempts: {config['max_attempts']}")
    logger.info(f"Confidence Threshold: {config['confidence_threshold']}")

    if config['api_key']:
        logger.info(f"API Key: {config['api_key'][:10]}...")
    else:
        logger.warning("⚠️ No API key configured")

    logger.info("✅ AI environment initialized successfully")
    return True

# Auto-initialize when module is imported
if __name__ != "__main__":
    try:
        initialize_ai_environment()
    except Exception as e:
        logger.warning(f"Auto-initialization failed: {e}")

if __name__ == "__main__":
    # Test the environment loader
    print("Testing AI Environment Loader...")
    success = initialize_ai_environment()
    if success:
        print("🎉 Environment loader test passed!")
    else:
        print("❌ Environment loader test failed!")
            
    except ImportError:
        print("❌ python-dotenv not available")
        return False
    except Exception as e:
        print(f"❌ Error loading environment: {e}")
        return False

# Auto-load when imported
if __name__ != "__main__":
    load_environment_variables()
