# HTML Report User Guide

## 🎯 Overview

The HTML report provides an interactive, browser-based view of your test execution results with expand/collapse screenshot functionality.

---

## 📂 Opening the Report

### **Option 1: From Test Executions Tab**
1. Navigate to **Test Executions** tab
2. Find your execution in the list
3. Click the **Download** button
4. Extract the ZIP file
5. Open the HTML file in your browser

### **Option 2: Direct Access**
```bash
# Navigate to the report directory
cd reports_android/testsuite_execution_YYYYMMDD_HHMMSS/

# Open the HTML report
open test_report_YYYYMMDD_HHMMSS.html
```

---

## 🎨 Report Layout

### **1. Header Section**
```
┌─────────────────────────────────────────────────────┐
│  Test Execution Report                              │
│  UI Execution 03/10/2025, 19:33:57                 │
│  Generated: 2025-10-03 20:16:50                    │
└─────────────────────────────────────────────────────┘
```

### **2. Execution Summary**
```
┌──────────────────────────────────────────────────────┐
│  Execution Summary                                   │
│                                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐  │
│  │ Total Tests │ │   Passed    │ │   Failed    │  │
│  │      2      │ │      2      │ │      0      │  │
│  └─────────────┘ └─────────────┘ └─────────────┘  │
│                                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐  │
│  │ Total Steps │ │ Passed Steps│ │Failed Steps │  │
│  │     10      │ │     10      │ │      0      │  │
│  └─────────────┘ └─────────────┘ └─────────────┘  │
└──────────────────────────────────────────────────────┘
```

### **3. Test Cases**
```
┌──────────────────────────────────────────────────────┐
│  Test Case 1: Calc-AndroidTest          [PASSED]    │
├──────────────────────────────────────────────────────┤
│                                                      │
│  Step 1: Restart app: com.coloros.calculator        │
│  [PASSED] 416ms  ID: CqEO79EYle                     │
│  [▶ View Screenshot]  ← Click to expand             │
│                                                      │
│  Step 2: Tap on element with xpath: //android...    │
│  [PASSED] 542ms  ID: f3GnddxkRi                     │
│  [▶ View Screenshot]  ← Click to expand             │
│                                                      │
│  ...                                                 │
└──────────────────────────────────────────────────────┘
```

---

## 🖱️ Using Expand/Collapse

### **Default State: All Screenshots Collapsed**
```
Step 1: Restart app: com.coloros.calculator
[PASSED] 416ms  ID: CqEO79EYle
[▶ View Screenshot]  ← Button to expand
```

### **Click to Expand a Screenshot**
```
Step 1: Restart app: com.coloros.calculator
[PASSED] 416ms  ID: CqEO79EYle
[▼ Hide Screenshot]  ← Button to collapse

┌─────────────────────────────────────────┐
│                                         │
│  [Screenshot of the app screen]        │
│                                         │
│  (Shows the state when this step       │
│   was executed)                         │
│                                         │
└─────────────────────────────────────────┘
```

### **Click Again to Collapse**
```
Step 1: Restart app: com.coloros.calculator
[PASSED] 416ms  ID: CqEO79EYle
[▶ View Screenshot]  ← Back to collapsed state
```

---

## 🎯 Key Features

### **1. Independent Expand/Collapse**
- Each step has its own expand/collapse button
- Expanding one step doesn't affect others
- You can have multiple screenshots expanded at once
- Or keep all collapsed for a clean overview

### **2. Visual Indicators**
- **▶** icon = Screenshot is collapsed (hidden)
- **▼** icon = Screenshot is expanded (visible)
- Button text changes: "View Screenshot" ↔ "Hide Screenshot"

### **3. Status Color Coding**

**Test Case Headers:**
- 🟢 **Green** = All steps passed
- 🔴 **Red** = One or more steps failed
- 🟡 **Yellow** = Test was skipped

**Step Status Badges:**
- 🟢 **PASSED** = Step executed successfully
- 🔴 **FAILED** = Step encountered an error
- 🟡 **SKIPPED** = Step was not executed

### **4. Step Information**

Each step displays:
```
Step 1: Restart app: com.coloros.calculator
[PASSED] 416ms  ID: CqEO79EYle
```

- **Step Number:** Sequential number (1, 2, 3...)
- **Step Name:** Descriptive action name
- **Status Badge:** PASSED/FAILED/SKIPPED
- **Duration:** Time taken to execute (e.g., "416ms")
- **Action ID:** Unique identifier for the step

---

## 📱 Responsive Design

### **Desktop View**
- Full-width layout (max 1200px)
- Side-by-side stat cards
- Large screenshots

### **Tablet View**
- Adjusted grid layout
- Stacked stat cards
- Medium-sized screenshots

### **Mobile View**
- Single column layout
- Stacked elements
- Optimized screenshot size

---

## 🔍 Browser Features

### **Search**
Use your browser's find function (Cmd+F / Ctrl+F) to search for:
- Test case names
- Step descriptions
- Action IDs
- Status values

### **Zoom**
- Zoom in/out using browser controls
- Screenshots scale proportionally
- Layout remains responsive

### **Print**
- Use browser's print function
- Screenshots will be included
- Consider expanding relevant screenshots before printing

---

## 📊 Understanding the Report

### **Execution Summary Statistics**

**Test Case Level:**
- **Total Test Cases:** Number of test cases executed
- **Passed:** Test cases where all steps passed
- **Failed:** Test cases with at least one failed step
- **Skipped:** Test cases that were not executed

**Step Level:**
- **Total Steps:** Total number of steps across all test cases
- **Passed Steps:** Steps that executed successfully
- **Failed Steps:** Steps that encountered errors

### **Test Case Details**

Each test case shows:
1. **Test Case Number:** Sequential numbering (1, 2, 3...)
2. **Test Case Name:** Name from your test suite
3. **Overall Status:** PASSED/FAILED/SKIPPED
4. **All Steps:** Complete list of steps with details

---

## 🐛 Troubleshooting

### **Screenshot Not Showing**
If you see "No screenshot available":
- The screenshot file doesn't exist in the screenshots/ directory
- The screenshot was not captured during test execution
- This is normal for some steps that don't require screenshots

### **Expand/Collapse Not Working**
- Make sure JavaScript is enabled in your browser
- Try refreshing the page
- Check browser console for errors (F12 → Console tab)

### **Layout Issues**
- Try a different browser (Chrome, Safari, Firefox)
- Clear browser cache
- Ensure you're viewing the latest version of the report

---

## 💡 Tips & Best Practices

### **1. Efficient Navigation**
- Keep screenshots collapsed for quick overview
- Expand only the screenshots you need to review
- Use browser's find function to locate specific steps

### **2. Reviewing Failed Tests**
- Look for red status badges
- Expand screenshots of failed steps
- Check the step name for error details

### **3. Sharing Reports**
- ZIP file contains everything needed
- Recipients can open HTML directly in browser
- No special software required

### **4. Archiving**
- Keep ZIP files for historical records
- HTML reports are self-contained
- Screenshots are embedded in the report directory

---

## 📋 Quick Reference

| Action | How To |
|--------|--------|
| Open Report | Double-click HTML file or use browser's "Open File" |
| Expand Screenshot | Click "▶ View Screenshot" button |
| Collapse Screenshot | Click "▼ Hide Screenshot" button |
| Search Content | Cmd+F (Mac) or Ctrl+F (Windows/Linux) |
| Zoom In/Out | Cmd/Ctrl + Plus/Minus |
| Print Report | Cmd/Ctrl + P |
| View Full Screenshot | Click on screenshot image (if supported) |

---

## 🎯 Example Workflow

1. **Open the HTML report** in your browser
2. **Review the Execution Summary** to see overall results
3. **Scan test case headers** for failed tests (red)
4. **Expand screenshots** for failed steps to investigate
5. **Use browser search** to find specific actions
6. **Keep successful test screenshots collapsed** for clean view
7. **Share the ZIP file** with team members if needed

---

**The HTML report provides a clean, interactive way to review your test execution results with full control over screenshot visibility!**

