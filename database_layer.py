#!/usr/bin/env python3
"""
Database Layer Implementation

This module provides a unified database interface to replace file-based storage
with the new consolidated database structure (ios.db and android.db).

Features:
- Unified CRUD operations for all tables
- Binary image storage and retrieval
- Connection pooling and error handling
- Platform-specific database routing
- Migration from file-based storage
"""

import os
import sqlite3
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from contextlib import contextmanager
import json
import uuid

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Unified database manager for iOS and Android platforms"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.db_data_dir = self.project_root / 'db-data'
        self.db_data_dir.mkdir(exist_ok=True)
        
        # Database file paths
        self.ios_db_path = self.db_data_dir / 'ios.db'
        self.android_db_path = self.db_data_dir / 'android.db'
        
        # Connection pools
        self._ios_connection = None
        self._android_connection = None
        self._lock = threading.Lock()
        
        # Initialize databases
        self._initialize_databases()
        
    def _initialize_databases(self):
        """Initialize database schemas if they don't exist"""
        for platform in ['ios', 'android']:
            self._create_schema(platform)
            
    def _create_schema(self, platform: str):
        """Create database schema for the specified platform"""
        db_path = self.ios_db_path if platform == 'ios' else self.android_db_path
        
        with self.get_connection(platform) as conn:
            # Enable foreign keys
            conn.execute('PRAGMA foreign_keys = ON')
            
            # Create tables
            self._create_test_suites_table(conn)
            self._create_test_cases_table(conn)
            self._create_environments_table(conn)
            self._create_environment_variables_table(conn)
            self._create_global_values_table(conn)
            self._create_execution_tracking_table(conn)
            self._create_execution_settings_table(conn)
            self._create_locator_repository_table(conn)
            self._create_reference_images_table(conn)
            self._create_execution_reports_table(conn)
            # Back-compat tables referenced by some code paths
            self._create_test_steps_table(conn)
            self._create_screenshots_table(conn)

            conn.commit()

    def _create_test_suites_table(self, conn):
        """Create test_suites table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS test_suites (
                suite_id TEXT PRIMARY KEY,
                platform TEXT NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                file_path TEXT,
                test_case_count INTEGER DEFAULT 0,
                step_count INTEGER DEFAULT 0,
                json_payload TEXT,
                status TEXT DEFAULT 'active',
                version TEXT DEFAULT '1.0',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                last_modified_by TEXT
            )
        """)
        
        # Create indexes
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_suites_platform ON test_suites(platform)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_suites_status ON test_suites(status)')
        
    def _create_test_cases_table(self, conn):
        """Create test_cases table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS test_cases (
                test_case_id TEXT PRIMARY KEY,
                suite_id TEXT NOT NULL,
                platform TEXT NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                file_path TEXT,
                step_count INTEGER DEFAULT 0,
                json_payload TEXT,
                test_idx INTEGER,
                status TEXT DEFAULT 'active',
                version TEXT DEFAULT '1.0',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                last_modified_by TEXT,
                FOREIGN KEY (suite_id) REFERENCES test_suites(suite_id) ON DELETE CASCADE
            )
        """)
        
        # Create indexes
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_suite_id ON test_cases(suite_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_platform ON test_cases(platform)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_status ON test_cases(status)')
        
    def _create_environments_table(self, conn):
        """Create environments table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS environments (
                environment_id TEXT PRIMARY KEY,
                platform TEXT NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                is_active INTEGER DEFAULT 0,
                port_specific TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.execute('CREATE INDEX IF NOT EXISTS idx_environments_platform ON environments(platform)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_environments_active ON environments(is_active)')
        
    def _create_environment_variables_table(self, conn):
        """Create environment_variables table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS environment_variables (
                environment_id TEXT NOT NULL,
                name TEXT NOT NULL,
                initial_value TEXT,
                current_value TEXT,
                type TEXT DEFAULT 'default',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (environment_id, name),
                FOREIGN KEY (environment_id) REFERENCES environments(environment_id) ON DELETE CASCADE
            )
        """)
        
        conn.execute('CREATE INDEX IF NOT EXISTS idx_env_vars_environment_id ON environment_variables(environment_id)')
        
    def _create_global_values_table(self, conn):
        """Create global_values table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS global_values (
                name TEXT PRIMARY KEY,
                value TEXT,
                type TEXT
            )
        """)
        
    def _create_execution_tracking_table(self, conn):
        """Create execution_tracking table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS execution_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                filename TEXT,
                action_type TEXT,
                action_params TEXT,
                action_id TEXT,
                status TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                last_error TEXT,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                in_progress INTEGER DEFAULT 0,
                execution_result TEXT,
                test_case_id TEXT,
                test_execution_id TEXT
            )
        """)
        
        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_tracking_suite_id ON execution_tracking(suite_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_tracking_test_case_id ON execution_tracking(test_case_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_tracking_status ON execution_tracking(status)')
        
    def _create_execution_settings_table(self, conn):
        """Create execution_settings table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS execution_settings (
                setting_name TEXT PRIMARY KEY,
                setting_value TEXT,
                description TEXT,
                category TEXT,
                is_default INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_settings_category ON execution_settings(category)')
        
    def _create_locator_repository_table(self, conn):
        """Create locator_repository table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS locator_repository (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case_name TEXT,
                test_case_id TEXT,
                action_id TEXT,
                locator_type TEXT,
                locator_value TEXT,
                platform TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used_date TIMESTAMP
            )
        """)
        
        conn.execute('CREATE INDEX IF NOT EXISTS idx_locator_repository_test_case_id ON locator_repository(test_case_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_locator_repository_platform ON locator_repository(platform)')
        
    def _create_reference_images_table(self, conn):
        """Create reference_images table for binary image storage"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS reference_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_name TEXT UNIQUE NOT NULL,
                image_data BLOB NOT NULL,
                image_format TEXT NOT NULL,
                file_size INTEGER,
                checksum TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.execute('CREATE INDEX IF NOT EXISTS idx_reference_images_name ON reference_images(image_name)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_reference_images_format ON reference_images(image_format)')
        
    def _create_execution_reports_table(self, conn):
        """Create execution_reports table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS execution_reports (
                report_id TEXT PRIMARY KEY,
                test_execution_id TEXT,
                suite_id TEXT,
                test_case_id TEXT,
                platform TEXT,
                status TEXT,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration INTEGER,
                error_message TEXT,
                screenshot_data BLOB,
                report_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_suite_id ON execution_reports(suite_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_case_id ON execution_reports(test_case_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_status ON execution_reports(status)')

    def _create_test_steps_table(self, conn):
        """Create legacy-compatible test_steps table used by some code paths"""
        conn.execute(
            """
            CREATE TABLE IF NOT EXISTS test_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                name TEXT,
                action_type TEXT,
                action_id TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                screenshot_path TEXT,
                error TEXT,
                enabled INTEGER DEFAULT 1
            )
            """
        )
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_steps_suite ON test_steps(suite_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_test_steps_action ON test_steps(action_id)')

    def _create_screenshots_table(self, conn):
        """Create screenshots table used by step rendering paths"""
        conn.execute(
            """
            CREATE TABLE IF NOT EXISTS screenshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                filename TEXT,
                path TEXT,
                timestamp TEXT,
                action_id TEXT
            )
            """
        )
        conn.execute('CREATE INDEX IF NOT EXISTS idx_screenshots_suite ON screenshots(suite_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_screenshots_action ON screenshots(action_id)')

    @contextmanager
    def get_connection(self, platform: str):
        """Get database connection for the specified platform"""
        db_path = self.ios_db_path if platform == 'ios' else self.android_db_path
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        conn.execute('PRAGMA foreign_keys = ON')
        
        try:
            yield conn
        finally:
            conn.close()
            
    # Test Suites CRUD Operations
    def create_test_suite(self, platform: str, suite_data: Dict[str, Any]) -> str:
        """Create a new test suite"""
        suite_id = suite_data.get('suite_id', str(uuid.uuid4()))
        
        with self.get_connection(platform) as conn:
            conn.execute("""
                INSERT INTO test_suites 
                (suite_id, platform, name, description, file_path, test_case_count, 
                 step_count, json_payload, status, version, created_by, last_modified_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                suite_id, platform, suite_data['name'], suite_data.get('description'),
                suite_data.get('file_path'), suite_data.get('test_case_count', 0),
                suite_data.get('step_count', 0), json.dumps(suite_data.get('json_payload', {})),
                suite_data.get('status', 'active'), suite_data.get('version', '1.0'),
                suite_data.get('created_by'), suite_data.get('last_modified_by')
            ))
            conn.commit()
            
        return suite_id
        
    def get_test_suite(self, platform: str, suite_id: str) -> Optional[Dict[str, Any]]:
        """Get a test suite by ID"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("SELECT * FROM test_suites WHERE suite_id = ?", (suite_id,))
            row = cursor.fetchone()
            
            if row:
                result = dict(row)
                if result['json_payload']:
                    result['json_payload'] = json.loads(result['json_payload'])
                return result
                
        return None
        
    def get_all_test_suites(self, platform: str) -> List[Dict[str, Any]]:
        """Get all test suites for a platform"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("SELECT * FROM test_suites WHERE platform = ? ORDER BY name", (platform,))
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = dict(row)
                if result['json_payload']:
                    result['json_payload'] = json.loads(result['json_payload'])
                results.append(result)
                
            return results
            
    def update_test_suite(self, platform: str, suite_id: str, updates: Dict[str, Any]) -> bool:
        """Update a test suite"""
        if not updates:
            return False
            
        # Prepare update query
        set_clauses = []
        values = []
        
        for key, value in updates.items():
            if key == 'json_payload' and isinstance(value, (dict, list)):
                value = json.dumps(value)
            set_clauses.append(f"{key} = ?")
            values.append(value)
            
        set_clauses.append("updated_at = CURRENT_TIMESTAMP")
        values.append(suite_id)
        
        query = f"UPDATE test_suites SET {', '.join(set_clauses)} WHERE suite_id = ?"
        
        with self.get_connection(platform) as conn:
            cursor = conn.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0
            
    def delete_test_suite(self, platform: str, suite_id: str) -> bool:
        """Delete a test suite and its associated test cases"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
            conn.commit()
            return cursor.rowcount > 0
            
    # Test Cases CRUD Operations
    def create_test_case(self, platform: str, case_data: Dict[str, Any]) -> str:
        """Create a new test case"""
        test_case_id = case_data.get('test_case_id', str(uuid.uuid4()))
        
        with self.get_connection(platform) as conn:
            conn.execute("""
                INSERT INTO test_cases 
                (test_case_id, suite_id, platform, name, description, file_path, 
                 step_count, json_payload, test_idx, status, version, created_by, last_modified_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_case_id, case_data['suite_id'], platform, case_data['name'],
                case_data.get('description'), case_data.get('file_path'),
                case_data.get('step_count', 0), json.dumps(case_data.get('json_payload', {})),
                case_data.get('test_idx'), case_data.get('status', 'active'),
                case_data.get('version', '1.0'), case_data.get('created_by'),
                case_data.get('last_modified_by')
            ))
            conn.commit()
            
        return test_case_id
        
    def get_test_cases_by_suite(self, platform: str, suite_id: str) -> List[Dict[str, Any]]:
        """Get all test cases for a suite"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("""
                SELECT * FROM test_cases WHERE suite_id = ? ORDER BY test_idx, name
            """, (suite_id,))
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = dict(row)
                if result['json_payload']:
                    result['json_payload'] = json.loads(result['json_payload'])
                results.append(result)
                
            return results
            
    # Reference Images Operations
    def store_reference_image(self, platform: str, image_name: str, image_data: bytes, image_format: str) -> bool:
        """Store a reference image as binary data"""
        import hashlib
        
        file_size = len(image_data)
        checksum = hashlib.md5(image_data).hexdigest()
        
        with self.get_connection(platform) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO reference_images 
                (image_name, image_data, image_format, file_size, checksum, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (image_name, image_data, image_format, file_size, checksum))
            conn.commit()
            
        return True
        
    def get_reference_image(self, platform: str, image_name: str) -> Optional[Dict[str, Any]]:
        """Retrieve a reference image"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("""
                SELECT image_name, image_data, image_format, file_size, checksum, created_at
                FROM reference_images WHERE image_name = ?
            """, (image_name,))
            row = cursor.fetchone()
            
            if row:
                return dict(row)
                
        return None
        
    def list_reference_images(self, platform: str) -> List[Dict[str, Any]]:
        """List all reference images (metadata only)"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("""
                SELECT image_name, image_format, file_size, checksum, created_at, updated_at
                FROM reference_images ORDER BY image_name
            """)
            rows = cursor.fetchall()
            
            return [dict(row) for row in rows]
            
    def delete_reference_image(self, platform: str, image_name: str) -> bool:
        """Delete a reference image"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("DELETE FROM reference_images WHERE image_name = ?", (image_name,))
            conn.commit()
            return cursor.rowcount > 0
            
    # Global Values Operations
    def set_global_value(self, platform: str, name: str, value: str, value_type: str = None) -> bool:
        """Set a global value"""
        with self.get_connection(platform) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO global_values (name, value, type)
                VALUES (?, ?, ?)
            """, (name, value, value_type))
            conn.commit()
            
        return True
        
    def get_global_value(self, platform: str, name: str) -> Optional[str]:
        """Get a global value"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("SELECT value FROM global_values WHERE name = ?", (name,))
            row = cursor.fetchone()
            
            if row:
                return row['value']
                
        return None
        
    def get_all_global_values(self, platform: str) -> Dict[str, str]:
        """Get all global values as a dictionary"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("SELECT name, value FROM global_values")
            rows = cursor.fetchall()
            
            return {row['name']: row['value'] for row in rows}
            
    # Environment Operations
    def create_environment(self, platform: str, env_data: Dict[str, Any]) -> str:
        """Create a new environment"""
        env_id = env_data.get('environment_id', str(uuid.uuid4()))
        
        with self.get_connection(platform) as conn:
            conn.execute("""
                INSERT INTO environments 
                (environment_id, platform, name, description, is_active, port_specific)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                env_id, platform, env_data['name'], env_data.get('description'),
                env_data.get('is_active', 0), env_data.get('port_specific')
            ))
            conn.commit()
            
        return env_id
        
    def get_environments(self, platform: str) -> List[Dict[str, Any]]:
        """Get all environments for a platform"""
        with self.get_connection(platform) as conn:
            cursor = conn.execute("""
                SELECT * FROM environments WHERE platform = ? ORDER BY name
            """, (platform,))
            rows = cursor.fetchall()
            
            return [dict(row) for row in rows]
            
    # Execution Reports Operations
    def create_execution_report(self, platform: str, report_data: Dict[str, Any]) -> str:
        """Create a new execution report"""
        report_id = report_data.get('report_id', str(uuid.uuid4()))
        
        with self.get_connection(platform) as conn:
            conn.execute("""
                INSERT INTO execution_reports 
                (report_id, test_execution_id, suite_id, test_case_id, platform, status,
                 start_time, end_time, duration, error_message, screenshot_data, report_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                report_id, report_data.get('test_execution_id'), report_data.get('suite_id'),
                report_data.get('test_case_id'), platform, report_data.get('status'),
                report_data.get('start_time'), report_data.get('end_time'),
                report_data.get('duration'), report_data.get('error_message'),
                report_data.get('screenshot_data'), json.dumps(report_data.get('report_data', {}))
            ))
            conn.commit()
            
        return report_id
        
    def get_execution_reports(self, platform: str, suite_id: str = None, test_case_id: str = None) -> List[Dict[str, Any]]:
        """Get execution reports with optional filtering"""
        query = "SELECT * FROM execution_reports WHERE platform = ?"
        params = [platform]
        
        if suite_id:
            query += " AND suite_id = ?"
            params.append(suite_id)
            
        if test_case_id:
            query += " AND test_case_id = ?"
            params.append(test_case_id)
            
        query += " ORDER BY created_at DESC"
        
        with self.get_connection(platform) as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = dict(row)
                if result['report_data']:
                    result['report_data'] = json.loads(result['report_data'])
                results.append(result)
                
            return results
            
    # Utility Methods
    def get_database_stats(self, platform: str) -> Dict[str, int]:
        """Get database statistics"""
        stats = {}
        
        tables = [
            'test_suites', 'test_cases', 'environments', 'environment_variables',
            'global_values', 'execution_tracking', 'execution_settings',
            'locator_repository', 'reference_images', 'execution_reports'
        ]
        
        with self.get_connection(platform) as conn:
            for table in tables:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                stats[table] = cursor.fetchone()[0]
                
        return stats
        
    def backup_database(self, platform: str, backup_path: str = None) -> str:
        """Create a backup of the database"""
        if not backup_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.db_data_dir / f'{platform}_backup_{timestamp}.db'
            
        source_db = self.ios_db_path if platform == 'ios' else self.android_db_path
        
        import shutil
        shutil.copy2(source_db, backup_path)
        
        logger.info(f"Database backup created: {backup_path}")
        return str(backup_path)
        
# Global database manager instance
_db_manager = None

def get_database_manager(project_root: str = None) -> DatabaseManager:
    """Get the global database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(project_root)
    return _db_manager

# Convenience functions for backward compatibility
def get_test_suites(platform: str) -> List[Dict[str, Any]]:
    """Get all test suites for a platform"""
    return get_database_manager().get_all_test_suites(platform)
    
def get_test_cases(platform: str, suite_id: str) -> List[Dict[str, Any]]:
    """Get test cases for a suite"""
    return get_database_manager().get_test_cases_by_suite(platform, suite_id)
    
def store_image(platform: str, image_name: str, image_data: bytes, image_format: str) -> bool:
    """Store a reference image"""
    return get_database_manager().store_reference_image(platform, image_name, image_data, image_format)
    
def get_image(platform: str, image_name: str) -> Optional[Dict[str, Any]]:
    """Get a reference image"""
    return get_database_manager().get_reference_image(platform, image_name)