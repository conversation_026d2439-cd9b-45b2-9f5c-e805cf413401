#!/usr/bin/env bash

set -euo pipefail

if [[ "$(uname)" != "Darwin" ]]; then
  echo "[package_macos] This script must be executed on macOS." >&2
  exit 1
fi

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DIST_DIR="${PROJECT_ROOT}/secure_distribution_app/dist"
BUILD_DIR="${PROJECT_ROOT}/secure_distribution_app/temp_build_final"

APP_NAME="SecureMobileAppAutomation"
VERSION="${SECURE_BUILD_VERSION:-2.0.0}"
BUILD_NUMBER="${SECURE_BUILD_NUMBER:-$(date +%Y%m%d%H%M)}"
LAUNCHER_PATH="${DIST_DIR}/${APP_NAME}"
DEFAULT_MIN_OS="${SECURE_BUILD_MIN_OS:-11.0}"

if [[ ! -d "${LAUNCHER_PATH}" && ! -f "${LAUNCHER_PATH}" ]]; then
  echo "[package_macos] Expected launcher artifact '${LAUNCHER_PATH}' not found." >&2
  echo "Run 'python3 secure_distribution_app/build_final_secure_app.py' first." >&2
  exit 1
fi

RELEASE_ROOT="${PROJECT_ROOT}/release/macos"
APP_BUNDLE="${RELEASE_ROOT}/${APP_NAME}.app"
DMG_PATH="${RELEASE_ROOT}/${APP_NAME}-${VERSION}.dmg"

echo "[package_macos] Preparing release directory: ${RELEASE_ROOT}"
rm -rf "${RELEASE_ROOT}"
mkdir -p "${APP_BUNDLE}/Contents/MacOS"
mkdir -p "${APP_BUNDLE}/Contents/Resources"

# Copy launcher payload into bundle
echo "[package_macos] Copying launcher payload into .app bundle"

if [[ -d "${LAUNCHER_PATH}" ]]; then
  rsync -a --delete "${LAUNCHER_PATH}/" "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}/"
  if [[ -x "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}/SecureMobileAppAutomation" ]]; then
    ln -sf "${APP_NAME}/SecureMobileAppAutomation" "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}"
  fi
else
  mkdir -p "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}"
  cp "${LAUNCHER_PATH}" "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}/SecureMobileAppAutomation"
  chmod +x "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}/SecureMobileAppAutomation"
  ln -sf "${APP_NAME}/SecureMobileAppAutomation" "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}"
fi

# Copy optional icon if available
ICON_SRC="${PROJECT_ROOT}/static/app.icns"
if [[ -f "${ICON_SRC}" ]]; then
  cp "${ICON_SRC}" "${APP_BUNDLE}/Contents/Resources/app.icns"
else
  echo "[package_macos] Icon not found at ${ICON_SRC}; skipping."
fi

# Generate Info.plist
TEMPLATE="${PROJECT_ROOT}/scripts/macos/Info.plist.template"
if [[ ! -f "${TEMPLATE}" ]]; then
  echo "[package_macos] Info.plist template missing at ${TEMPLATE}" >&2
  exit 1
fi

INFO_PLIST="${APP_BUNDLE}/Contents/Info.plist"
MACOS_VERSION=$(sw_vers -productVersion 2>/dev/null || echo "11.0")
MACOS_VERSION_SHORT=$(echo "${MACOS_VERSION}" | awk -F. '{print $1 "." $2}')

# pick the lower of current os vs requested min (simple numeric compare)
MIN_OS="${DEFAULT_MIN_OS}"
if [[ "$(printf '%s\n%s\n' "${MIN_OS}" "${MACOS_VERSION_SHORT}" | sort -V | head -n1)" != "${MIN_OS}" ]]; then
  MIN_OS="${MACOS_VERSION_SHORT}"
fi

sed \
  -e "s/__VERSION__/${VERSION}/g" \
  -e "s/__BUILD__/${BUILD_NUMBER}/g" \
  -e "s/__MIN_OS__/${MIN_OS}/g" \
  "${TEMPLATE}" > "${INFO_PLIST}"

# Copy runtime assets (python_env, node_env, etc.)
if [[ -d "${BUILD_DIR}/runtime" ]]; then
  echo "[package_macos] Embedding runtime assets"
  rsync -a "${BUILD_DIR}/runtime/" "${APP_BUNDLE}/Contents/Resources/runtime/"
fi

# Provide config sources for fallback imports
mkdir -p "${APP_BUNDLE}/Contents/Resources/runtime/config"
for cfg in "${BUILD_DIR}/obfuscated/config.py" "${BUILD_DIR}/obfuscated/config_android.py"; do
  if [[ -f "${cfg}" ]]; then
    cp "${cfg}" "${APP_BUNDLE}/Contents/Resources/runtime/config/$(basename "${cfg}")"
  fi
done

# Copy sanitized databases for reference
if [[ -d "${BUILD_DIR}/db-data" ]]; then
  rsync -a "${BUILD_DIR}/db-data/" "${APP_BUNDLE}/Contents/Resources/db-data/"
fi

echo "[package_macos] App bundle created at ${APP_BUNDLE}"

# Create DMG for distribution
echo "[package_macos] Building DMG image"
hdiutil create -volname "${APP_NAME}" -srcfolder "${APP_BUNDLE}" -ov -format UDZO "${DMG_PATH}" >/dev/null

echo "[package_macos] DMG created at ${DMG_PATH}"
echo "[package_macos] Next steps:"
echo "  1. codesign --force --options runtime --sign 'Developer ID Application: <Team>' ${APP_BUNDLE}"
echo "  2. codesign --force --sign 'Developer ID Application: <Team>' ${DMG_PATH}"
echo "  3. xcrun notarytool submit ${DMG_PATH} --team-id <TeamID> --apple-id <AppleID> --password <AppSpecificPassword>"
echo "  4. stapler staple ${DMG_PATH}"
# Optional documentation from bundle
if [[ -d "${DIST_DIR}/${APP_NAME}_bundle" ]]; then
  if [[ -f "${DIST_DIR}/${APP_NAME}_bundle/README.md" ]]; then
    mkdir -p "${APP_BUNDLE}/Contents/Resources/docs"
    cp "${DIST_DIR}/${APP_NAME}_bundle/README.md" "${APP_BUNDLE}/Contents/Resources/docs/README.md"
  fi
fi
DEFAULT_MIN_OS="${SECURE_BUILD_MIN_OS:-11.0}"
