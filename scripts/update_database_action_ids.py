#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update database records with action IDs from test case files

This script:
1. Updates the database schema to include action_id columns
2. Loads all test cases from the test_cases directory
3. For each test case, extracts the action IDs
4. Updates the database records with the action IDs
"""

import os
import json
import sys
import logging
import sqlite3
from pathlib import Path

# Add the parent directory to the path so we can import app modules
parent_dir = Path(__file__).resolve().parent.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

from app.utils.database_update import update_database_schema
from app.utils.database import DB_PATH, update_action_id

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_case(file_path):
    """
    Load a test case from a file
    
    Args:
        file_path (str): Path to the test case file
        
    Returns:
        dict: Test case data or None if error
    """
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading test case {file_path}: {str(e)}")
        return None

def get_test_steps_with_action_ids(test_cases_dir):
    """
    Get all test steps with action IDs from all test cases
    
    Args:
        test_cases_dir (str): Path to the test cases directory
        
    Returns:
        list: List of tuples (test_case_name, step_idx, action_id, action_type)
    """
    steps_with_action_ids = []
    
    # Get all JSON files in the test cases directory
    test_case_files = [
        os.path.join(test_cases_dir, f) 
        for f in os.listdir(test_cases_dir) 
        if f.endswith('.json') and not f.endswith('.bak')
    ]
    
    logger.info(f"Found {len(test_case_files)} test case files")
    
    # Process each test case
    for file_path in test_case_files:
        test_case = load_test_case(file_path)
        if not test_case:
            continue
        
        test_case_name = test_case.get('name', os.path.basename(file_path))
        
        # Process each action in the test case
        for step_idx, action in enumerate(test_case.get('actions', [])):
            action_id = action.get('action_id')
            action_type = action.get('type')
            
            if action_id:
                steps_with_action_ids.append((test_case_name, step_idx, action_id, action_type))
                
                # If this is a multiStep action, process its steps too
                if action_type == 'multiStep' and 'test_case_steps' in action:
                    for sub_step_idx, sub_action in enumerate(action['test_case_steps']):
                        sub_action_id = sub_action.get('action_id')
                        sub_action_type = sub_action.get('type')
                        
                        if sub_action_id:
                            # Use a special format to identify sub-steps
                            steps_with_action_ids.append((
                                f"{test_case_name}:multiStep:{step_idx}", 
                                sub_step_idx, 
                                sub_action_id, 
                                sub_action_type
                            ))
    
    logger.info(f"Found {len(steps_with_action_ids)} steps with action IDs")
    return steps_with_action_ids

def update_database_with_action_ids(steps_with_action_ids):
    """
    Update database records with action IDs
    
    Args:
        steps_with_action_ids (list): List of tuples (test_case_name, step_idx, action_id, action_type)
        
    Returns:
        int: Number of records updated
    """
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get all test suites
        cursor.execute("SELECT suite_id, name FROM test_suites")
        test_suites = cursor.fetchall()
        
        logger.info(f"Found {len(test_suites)} test suites in database")
        
        # For each test suite, get all test cases
        updated_count = 0
        for suite_id, suite_name in test_suites:
            cursor.execute(
                "SELECT test_idx, name FROM test_cases WHERE suite_id = ?",
                (suite_id,)
            )
            test_cases = cursor.fetchall()
            
            logger.info(f"Found {len(test_cases)} test cases in suite {suite_name}")
            
            # For each test case, update action IDs
            for test_idx, test_name in test_cases:
                # Find matching steps with action IDs
                for tc_name, step_idx, action_id, action_type in steps_with_action_ids:
                    if tc_name == test_name:
                        # Update the action ID in the database
                        update_action_id(suite_id, test_idx, step_idx, action_id)
                        updated_count += 1
        
        conn.close()
        return updated_count
    except Exception as e:
        logger.error(f"Error updating database with action IDs: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return 0

def main():
    """Main function to update database records with action IDs"""
    # DB-only enforcement: disable JSON scanning unless explicitly allowed
    if os.getenv('ALLOW_JSON_SCANNING', '').lower() != 'true':
        logger.info('Skipping: JSON scanning is disabled by default (DB-only mode). Set ALLOW_JSON_SCANNING=true to enable.')
        return

    # Update the database schema to include action_id columns
    if not update_database_schema():
        logger.error("Failed to update database schema")
        return

    # Get the test cases directory
    test_cases_dir = os.path.join(parent_dir, 'test_cases')

    if not os.path.exists(test_cases_dir):
        logger.error(f"Test cases directory not found: {test_cases_dir}")
        return

    # Get all test steps with action IDs
    steps_with_action_ids = get_test_steps_with_action_ids(test_cases_dir)

    # Update database records with action IDs
    updated_count = update_database_with_action_ids(steps_with_action_ids)

    logger.info(f"Updated {updated_count} database records with action IDs")

if __name__ == "__main__":
    main()
