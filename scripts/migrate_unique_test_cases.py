#!/usr/bin/env python3
"""
Safe migration to enforce uniqueness for test_cases at the database level.
- Creates a timestamped backup of the DB first
- De-duplicates existing rows by case-insensitive file_path (preferred) and by name when file_path is null/empty
- Adds DB-level safeguards:
  * UNIQUE index on lower(file_path) with a partial predicate (if supported)
  * UNIQUE index on test_case_id
  * Fallback BEFORE INSERT/UPDATE triggers to prevent duplicates when expression/partial indexes are unsupported

Usage:
  python scripts/migrate_unique_test_cases.py --platform ios --dry-run
  python scripts/migrate_unique_test_cases.py --platform android --dry-run
  python scripts/migrate_unique_test_cases.py --platform ios --apply-to-live

Notes:
- --dry-run works on a copy of the DB only (backup file) and does not modify the live DB
- --apply-to-live will also apply the dedupe + constraints to the live DB after a successful backup migration
"""

import argparse
import os
import shutil
import sqlite3
import sys
from datetime import datetime
from pathlib import Path

REPO_ROOT = Path(__file__).resolve().parents[1]

PLATFORM_DB = {
    'ios': REPO_ROOT / 'app' / 'data' / 'ios_test_suites.db',
    'android': REPO_ROOT / 'app_android' / 'data' / 'android_test_suites.db',
}


def backup_db(db_path: Path) -> Path:
    if not db_path.exists():
        raise FileNotFoundError(f"Database not found: {db_path}")
    ts = datetime.now().strftime('%Y%m%d-%H%M%S')
    backup_path = db_path.with_name(db_path.stem + f".bak-{ts}" + db_path.suffix)
    shutil.copy2(db_path, backup_path)
    print(f"[INFO] Backup created: {backup_path}")
    return backup_path


def get_columns(cursor, table: str):
    cursor.execute(f"PRAGMA table_info({table})")
    return [row[1] for row in cursor.fetchall()]


def ensure_columns(conn):
    cursor = conn.cursor()
    cols = set(get_columns(cursor, 'test_cases'))
    needed = {
        'test_case_id': 'TEXT',
        'name': 'TEXT',
        'file_path': 'TEXT',
        'status': 'TEXT',
        'updated_at': 'TEXT'
    }
    for col, typ in needed.items():
        if col not in cols:
            try:
                cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {col} {typ}")
                print(f"[INFO] Added column {col} {typ}")
            except sqlite3.OperationalError as e:
                print(f"[WARN] Could not add column {col}: {e}")
    conn.commit()


def dedupe_by_key(conn, key_expr: str, where_clause: str = '') -> int:
    """Delete duplicate rows keeping the most recent (by updated_at then id). Returns rows removed."""
    cursor = conn.cursor()
    where = f"WHERE {where_clause}" if where_clause else ''
    query_dups = f"""
        WITH tc AS (
          SELECT id, {key_expr} AS k, updated_at,
                 ROW_NUMBER() OVER (PARTITION BY {key_expr} ORDER BY 
                                    CASE WHEN updated_at IS NULL OR updated_at = '' THEN 0 ELSE 1 END DESC,
                                    updated_at DESC,
                                    id DESC) AS rn
          FROM test_cases
          {where}
        )
        SELECT id FROM tc WHERE rn > 1
    """
    try:
        cursor.execute(query_dups)
        ids = [row[0] for row in cursor.fetchall()]
    except sqlite3.OperationalError as e:
        # Older SQLite might not support window functions; fallback approach
        print(f"[WARN] Window function not available, falling back for key={key_expr}: {e}")
        cursor.execute(f"SELECT {key_expr} AS k, COUNT(*) c FROM test_cases {where} GROUP BY {key_expr} HAVING c>1")
        keys = [row[0] for row in cursor.fetchall()]
        ids = []
        for k in keys:
            cursor.execute(
                f"SELECT id FROM test_cases WHERE {key_expr}=? {('AND ' + where_clause) if where_clause else ''} ORDER BY updated_at DESC, id DESC",
                (k,)
            )
            keep_first = True
            for row in cursor.fetchall():
                if keep_first:
                    keep_first = False
                    continue
                ids.append(row[0])

    removed = 0
    if ids:
        cursor.execute(
            f"DELETE FROM test_cases WHERE id IN ({','.join('?' for _ in ids)})",
            ids,
        )
        removed = cursor.rowcount
        conn.commit()
        print(f"[INFO] Removed {removed} duplicate rows for key {key_expr}")
    else:
        print(f"[INFO] No duplicates found for key {key_expr}")
    return removed


def create_indexes_and_triggers(conn):
    cursor = conn.cursor()
    # Try expression/partial unique index on lower(file_path)
    try:
        cursor.execute(
            """
            CREATE UNIQUE INDEX IF NOT EXISTS ux_test_cases_file_path_ci
            ON test_cases(lower(file_path))
            WHERE file_path IS NOT NULL AND file_path <> ''
            """
        )
        print("[INFO] Created unique index ux_test_cases_file_path_ci")
    except sqlite3.OperationalError as e:
        print(f"[WARN] Could not create expression/partial index: {e}. Adding triggers as fallback.")
        # Fallback triggers to enforce uniqueness by lower(file_path)
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS trg_tc_fp_unique_insert
            BEFORE INSERT ON test_cases
            WHEN NEW.file_path IS NOT NULL AND NEW.file_path <> '' AND EXISTS(
                SELECT 1 FROM test_cases WHERE lower(file_path) = lower(NEW.file_path)
            )
            BEGIN
                SELECT RAISE(ABORT, 'Duplicate test case file_path');
            END;
            """
        )
        cursor.execute(
            """
            CREATE TRIGGER IF NOT EXISTS trg_tc_fp_unique_update
            BEFORE UPDATE OF file_path ON test_cases
            WHEN NEW.file_path IS NOT NULL AND NEW.file_path <> '' AND EXISTS(
                SELECT 1 FROM test_cases WHERE lower(file_path) = lower(NEW.file_path) AND id <> OLD.id
            )
            BEGIN
                SELECT RAISE(ABORT, 'Duplicate test case file_path (update)');
            END;
            """
        )
        print("[INFO] Installed BEFORE INSERT/UPDATE triggers for file_path uniqueness")

    # Always ensure test_case_id uniqueness as well
    try:
        cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS ux_test_cases_id ON test_cases(test_case_id)")
        print("[INFO] Ensured unique index ux_test_cases_id on test_case_id")
    except sqlite3.OperationalError as e:
        print(f"[WARN] Could not create unique index on test_case_id: {e}")

    conn.commit()


def migrate(db_path: Path):
    # Work on a backup first
    backup_path = backup_db(db_path)

    # 1) Run migration on backup
    with sqlite3.connect(str(backup_path)) as conn:
        print(f"[INFO] Migrating backup DB: {backup_path}")
        ensure_columns(conn)
        # De-dupe by case-insensitive file_path
        removed_fp = dedupe_by_key(conn, "lower(file_path)", "file_path IS NOT NULL AND file_path <> ''")
        # De-dupe by case-insensitive name where file_path is NULL/empty
        removed_name = dedupe_by_key(conn, "lower(name)", "(file_path IS NULL OR file_path = '') AND name IS NOT NULL AND name <> ''")
        create_indexes_and_triggers(conn)
        print(f"[INFO] Backup migration complete. Removed duplicates: file_path={removed_fp}, name={removed_name}")

    return backup_path


def apply_to_live(db_path: Path):
    with sqlite3.connect(str(db_path)) as conn:
        print(f"[INFO] Applying migration to LIVE DB: {db_path}")
        ensure_columns(conn)
        removed_fp = dedupe_by_key(conn, "lower(file_path)", "file_path IS NOT NULL AND file_path <> ''")
        removed_name = dedupe_by_key(conn, "lower(name)", "(file_path IS NULL OR file_path = '') AND name IS NOT NULL AND name <> ''")
        create_indexes_and_triggers(conn)
        print(f"[INFO] Live migration complete. Removed duplicates: file_path={removed_fp}, name={removed_name}")


def main():
    parser = argparse.ArgumentParser(description='Add DB-level dedupe/uniqueness for test_cases')
    parser.add_argument('--platform', choices=['ios', 'android'], required=True)
    parser.add_argument('--dry-run', action='store_true', help='Migrate the BACKUP only (recommended first)')
    parser.add_argument('--apply-to-live', action='store_true', help='Also apply the migration to the live DB after successful backup migration')
    args = parser.parse_args()

    db_path = PLATFORM_DB[args.platform]
    print(f"[INFO] Platform: {args.platform}\n[INFO] DB Path: {db_path}")

    try:
        backup_path = migrate(db_path)
        print(f"[INFO] Backup migration succeeded: {backup_path}")
        if args.apply_to_live and not args.dry_run:
            apply_to_live(db_path)
        else:
            print("[INFO] Live DB not modified (use --apply-to-live to apply after backup migration)")
        return 0
    except Exception as e:
        print(f"[ERROR] Migration failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())

