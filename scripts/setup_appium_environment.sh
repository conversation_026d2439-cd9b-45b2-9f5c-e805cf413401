#!/usr/bin/env bash
set -euo pipefail

APP_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
NODE_BIN_DIR=${NODE_BIN_DIR:-"$HOME/.nvm/versions/node/v20.19.5/bin"}
APPIUM_HOME=${APPIUM_HOME:-"$APP_ROOT/.appium-python"}
APPIUM_VERSION=${APPIUM_VERSION:-"3.0.2"}
APPIUM_DRIVERS=${APPIUM_DRIVERS:-"appium-uiautomator2-driver@4.2.9 appium-xcuitest-driver@10.1.2"}
APPIUM_PLUGINS=${APPIUM_PLUGINS:-"appium-inspector-plugin@2025.8.2"}

export PATH="$NODE_BIN_DIR:$PATH"

command -v node >/dev/null 2>&1 || { echo "Node.js not found on PATH. Set NODE_BIN_DIR or install Node." >&2; exit 1; }
command -v npm >/dev/null 2>&1 || { echo "npm not found on PATH." >&2; exit 1; }

echo "Installing Appium $APPIUM_VERSION ..."
GLOBAL_NODE_ROOT=$(npm root -g 2>/dev/null || printf '')
if [ -n "$GLOBAL_NODE_ROOT" ]; then
  rm -rf "$GLOBAL_NODE_ROOT/appium"
fi
npm uninstall -g appium >/dev/null 2>&1 || true
npm install -g "appium@$APPIUM_VERSION"

rm -rf "$APPIUM_HOME"
mkdir -p "$APPIUM_HOME"
export APPIUM_HOME

echo "Provisioning drivers in $APPIUM_HOME ..."
for driver in $APPIUM_DRIVERS; do
  echo "  -> $driver"
  appium driver install --source=npm "$driver"
done

if [ -n "$APPIUM_PLUGINS" ]; then
  echo "Provisioning plugins ..."
  for plugin in $APPIUM_PLUGINS; do
    echo "  -> $plugin"
    appium plugin install --source=npm "$plugin"
  done
fi

INSPECTOR_FILE="$APPIUM_HOME/node_modules/appium-inspector-plugin/index.mjs"
if [ -f "$INSPECTOR_FILE" ]; then
  echo "Applying Appium Inspector compatibility patch ..."
  python3 - "$INSPECTOR_FILE" <<'PY'
from pathlib import Path
import sys

inspector_path = Path(sys.argv[1])
source = inspector_path.read_text()

route_line = "    expressApp.all(['/inspector', '/inspector/*'], AppiumInspectorPlugin.openInspector);\n"
patched_routes = (
"    expressApp.get('/inspector', AppiumInspectorPlugin.openInspector);\n"
"    expressApp.get('/inspector/:resource(*)', AppiumInspectorPlugin.openInspector);\n"
)

if route_line in source:
    source = source.replace(route_line, patched_routes)

open_lines = (
"    const reqPath =\n"
"      req.path === PLUGIN_ROOT_PATH ? INDEX_HTML : req.path.substring(PLUGIN_ROOT_PATH.length);\n"
"    res.sendFile(reqPath, {root: ROOT_DIR});\n"
)
open_patch = (
"    const resource = (req.params && req.params.resource) || '';\n"
"    const sanitized = resource.startsWith('/') ? resource.slice(1) : resource;\n"
"    const reqPath = sanitized ? sanitized : INDEX_HTML;\n"
"    res.sendFile(reqPath, {root: ROOT_DIR});\n"
)

if open_lines in source:
    source = source.replace(open_lines, open_patch)

if not source.endswith('\n'):
    source += '\n'

inspector_path.write_text(source)
PY
fi

echo "Appium setup complete. Remember to export PATH=$NODE_BIN_DIR:\$PATH"
echo "  export PATH=$NODE_BIN_DIR:\$PATH"
echo "  export APPIUM_HOME=$APPIUM_HOME"
