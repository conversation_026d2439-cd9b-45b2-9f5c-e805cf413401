#!/usr/bin/env bash

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DIST_DIR="${PROJECT_ROOT}/secure_distribution_app/dist"
BUILD_DIR="${PROJECT_ROOT}/secure_distribution_app/temp_build_final"
APP_NAME="SecureMobileAppAutomation"

OS_NAME="${SECURE_BUILD_OS:-}" 
if [[ -z "${OS_NAME}" ]]; then
  case "${OSTYPE}" in
    darwin*) OS_NAME="macos" ;;
    linux*) OS_NAME="linux" ;;
    msys*|mingw*|cygwin*) OS_NAME="windows" ;;
    *) OS_NAME="macos" ;;
  esac
fi

VERSION="${SECURE_BUILD_VERSION:-2.0.0}"
OUTPUT_ROOT="${PROJECT_ROOT}/release/portable"
PACKAGE_ROOT="${OUTPUT_ROOT}/${APP_NAME}_${OS_NAME}_${VERSION}"

rm -rf "${PACKAGE_ROOT}"
mkdir -p "${PACKAGE_ROOT}"

if [[ ! -f "${DIST_DIR}/${APP_NAME}" ]]; then
  echo "[package_portable] Missing build artifact '${DIST_DIR}/${APP_NAME}'." >&2
  echo "Run 'python3 secure_distribution_app/build_final_secure_app.py' first." >&2
  exit 1
fi

cp "${DIST_DIR}/${APP_NAME}" "${PACKAGE_ROOT}/${APP_NAME}"
chmod +x "${PACKAGE_ROOT}/${APP_NAME}"

if [[ -d "${BUILD_DIR}/runtime" ]]; then
  rsync -a "${BUILD_DIR}/runtime" "${PACKAGE_ROOT}/"
fi

# Bundle Node executables for offline usage
mkdir -p "${PACKAGE_ROOT}/runtime/node_env/bin"
NODE_BIN=$(command -v node || true)
if [[ -n "${NODE_BIN}" ]]; then
  cp "${NODE_BIN}" "${PACKAGE_ROOT}/runtime/node_env/bin/node"
  chmod +x "${PACKAGE_ROOT}/runtime/node_env/bin/node"
fi
for tool in npm npx appium; do
  TOOL_PATH=$(command -v "${tool}" || true)
  if [[ -n "${TOOL_PATH}" ]]; then
    cp "${TOOL_PATH}" "${PACKAGE_ROOT}/runtime/node_env/bin/${tool}"
    chmod +x "${PACKAGE_ROOT}/runtime/node_env/bin/${tool}"
  fi
done

if [[ -d "${BUILD_DIR}/db-data" ]]; then
  rsync -a "${BUILD_DIR}/db-data" "${PACKAGE_ROOT}/"
fi

if [[ -f "${DIST_DIR}/${APP_NAME}_bundle/README.md" ]]; then
  cp "${DIST_DIR}/${APP_NAME}_bundle/README.md" "${PACKAGE_ROOT}/README.md"
fi

# Copy shared python configuration files for fallback imports
mkdir -p "${PACKAGE_ROOT}/runtime/config"
for cfg in "${BUILD_DIR}/obfuscated/config.py" "${BUILD_DIR}/obfuscated/config_android.py"; do
  if [[ -f "${cfg}" ]]; then
    cp "${cfg}" "${PACKAGE_ROOT}/runtime/config/$(basename "${cfg}")"
  fi
done

case "${OS_NAME}" in
  macos|linux)
    cat > "${PACKAGE_ROOT}/launch.sh" <<'EOF'
#!/usr/bin/env bash
DIR="$(cd "$(dirname "$0")" && pwd)"
export SECURE_BUILD="True"
export AUTOMATION_WORKSPACE="$DIR/runtime"
export PYTHONPATH="$DIR/runtime/config:${PYTHONPATH:-}"
export PATH="$DIR/runtime/node_env/bin:$DIR/runtime/node_env/node_modules/.bin:$PATH"
export NODE_PATH="$DIR/runtime/node_env/node_modules:${NODE_PATH:-}"
"$DIR/SecureMobileAppAutomation" "$@"
EOF
    chmod +x "${PACKAGE_ROOT}/launch.sh"
    ;;
  windows)
    cat > "${PACKAGE_ROOT}/launch.bat" <<'EOF'
@echo off
set SCRIPT_DIR=%~dp0
set SECURE_BUILD=True
set AUTOMATION_WORKSPACE=%SCRIPT_DIR%\runtime
set PYTHONPATH=%SCRIPT_DIR%\runtime\config;%PYTHONPATH%
set PATH=%SCRIPT_DIR%\runtime\node_env\bin;%SCRIPT_DIR%\runtime\node_env\node_modules\.bin;%PATH%
set NODE_PATH=%SCRIPT_DIR%\runtime\node_env\node_modules;%NODE_PATH%
start "SecureMobileAppAutomation" "%SCRIPT_DIR%\SecureMobileAppAutomation" %*
EOF
    ;;
esac

ARCHIVE_PATH="${PACKAGE_ROOT}.zip"
rm -f "${ARCHIVE_PATH}"
(
  cd "${OUTPUT_ROOT}"
  zip -r "$(basename "${ARCHIVE_PATH}")" "$(basename "${PACKAGE_ROOT}")" >/dev/null
)

echo "[package_portable] Portable bundle created at ${ARCHIVE_PATH}"
echo "Contents:"
ls -1 "${PACKAGE_ROOT}"
