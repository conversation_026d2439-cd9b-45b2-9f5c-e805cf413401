#!/usr/bin/env python3
"""
Simple License Validation System for Protected Executables
Provides basic license checking for distributed automation tools
"""

import os
import sys
import json
import hashlib
import base64
from datetime import datetime, timedelta
from pathlib import Path

class LicenseValidator:
    def __init__(self):
        self.license_key = "mobile_automation_2024"
        self.valid_licenses = {
            # Demo licenses for testing
            "<EMAIL>": {
                "token": "demo_license_token_2024",
                "expiry": "2025-12-31",
                "features": ["ios", "android", "basic"]
            },
            "<EMAIL>": {
                "token": "test_license_token_2024", 
                "expiry": "2025-12-31",
                "features": ["ios", "android", "premium"]
            }
        }
    
    def generate_license_hash(self, email, token):
        """Generate a hash for license validation"""
        combined = f"{email}:{token}:{self.license_key}"
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def validate_license(self, email=None, token=None):
        """Validate license credentials"""
        try:
            # Get credentials from environment variables if not provided
            if not email:
                email = os.environ.get('LICENSE_EMAIL')
            if not token:
                token = os.environ.get('LICENSE_TOKEN')
            
            # For development/demo mode, allow bypass
            if os.environ.get('DEVELOPMENT_MODE', '').lower() == 'true':
                print("🔓 Development mode - license validation bypassed")
                return True
            
            # Check if credentials are provided
            if not email or not token:
                print("❌ License credentials not provided")
                print("Set LICENSE_EMAIL and LICENSE_TOKEN environment variables")
                return False
            
            # Check against valid licenses
            if email in self.valid_licenses:
                license_info = self.valid_licenses[email]
                
                # Check token
                if license_info["token"] != token:
                    print("❌ Invalid license token")
                    return False
                
                # Check expiry
                expiry_date = datetime.strptime(license_info["expiry"], "%Y-%m-%d")
                if datetime.now() > expiry_date:
                    print("❌ License has expired")
                    return False
                
                print(f"✅ License validated for {email}")
                return True
            else:
                print("❌ License not found for this email")
                return False
                
        except Exception as e:
            print(f"❌ License validation error: {e}")
            return False
    
    def get_license_info(self, email):
        """Get license information for a user"""
        if email in self.valid_licenses:
            return self.valid_licenses[email]
        return None
    
    def check_feature_access(self, email, feature):
        """Check if user has access to a specific feature"""
        license_info = self.get_license_info(email)
        if license_info:
            return feature in license_info.get("features", [])
        return False

def check_license_before_startup():
    """Check license before application startup"""
    print("🔐 Checking license...")
    
    validator = LicenseValidator()
    
    if validator.validate_license():
        print("✅ License validation successful")
        return True
    else:
        print("❌ License validation failed")
        print("\nTo run this application, you need a valid license.")
        print("Please contact your vendor for licensing information.")
        print("\nFor demo purposes, you can set:")
        print("export LICENSE_EMAIL='<EMAIL>'")
        print("export LICENSE_TOKEN='demo_license_token_2024'")
        print("\nOr enable development mode:")
        print("export DEVELOPMENT_MODE='true'")
        return False

def main():
    """Test the license validator"""
    print("🧪 Testing License Validator")
    print("=" * 40)
    
    validator = LicenseValidator()
    
    # Test cases
    test_cases = [
        ("<EMAIL>", "demo_license_token_2024"),
        ("<EMAIL>", "test_license_token_2024"),
        ("<EMAIL>", "invalid_token"),
        ("<EMAIL>", "wrong_token")
    ]
    
    for email, token in test_cases:
        print(f"\nTesting: {email}")
        result = validator.validate_license(email, token)
        print(f"Result: {'✅ Valid' if result else '❌ Invalid'}")
        
        if result:
            license_info = validator.get_license_info(email)
            print(f"Features: {license_info['features']}")
            print(f"Expiry: {license_info['expiry']}")

if __name__ == "__main__":
    main()
