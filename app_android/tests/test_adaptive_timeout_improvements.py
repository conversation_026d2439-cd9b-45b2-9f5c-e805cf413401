"""
Test script for validating Android Adaptive Timeout Improvements

This script tests the fixes implemented for Issue #3: Element Finding Timeout Inefficiencies
including adaptive timeout strategies, cross-platform locator validation, and performance optimization.
"""

import time
import logging
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the app_android directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from utils.adaptive_timeout_manager import AdaptiveTimeoutManager, ElementContext, LocatorComplexity
from utils.enhanced_element_finder import EnhancedElementFinder


class TestAdaptiveTimeoutManager(unittest.TestCase):
    """Test cases for the AdaptiveTimeoutManager"""
    
    def setUp(self):
        self.manager = AdaptiveTimeoutManager()
        
    def test_ios_locator_detection(self):
        """Test detection of iOS locators on Android"""
        # Test iOS XCUIElementType detection
        is_valid, message, suggestion = self.manager.validate_locator_for_android(
            'xpath', '//XCUIElementTypeButton[@name="Login"]'
        )
        self.assertFalse(is_valid)
        self.assertIn('iOS locator', message)
        self.assertIsNotNone(suggestion)
        self.assertIn('android.widget.Button', suggestion)
        
        # Test iOS @name pattern detection
        is_valid, message, suggestion = self.manager.validate_locator_for_android(
            'xpath', '//*[@name="Password"]'
        )
        self.assertFalse(is_valid)
        self.assertIn('iOS locator', message)
        self.assertIsNotNone(suggestion)
        self.assertIn('@content-desc=', suggestion)
        
    def test_valid_android_locators(self):
        """Test validation of valid Android locators"""
        # Test valid Android XPath
        is_valid, message, suggestion = self.manager.validate_locator_for_android(
            'xpath', '//android.widget.Button[@text="Login"]'
        )
        self.assertTrue(is_valid)
        
        # Test valid resource ID
        is_valid, message, suggestion = self.manager.validate_locator_for_android(
            'id', 'com.example.app:id/login_button'
        )
        self.assertTrue(is_valid)
        
        # Test valid UISelector
        is_valid, message, suggestion = self.manager.validate_locator_for_android(
            'uiselector', 'new UiSelector().text("Login")'
        )
        self.assertTrue(is_valid)
        
    def test_context_determination(self):
        """Test element context determination"""
        # Test conditional actions
        context = self.manager.determine_element_context('tapIfLocatorExists')
        self.assertEqual(context, ElementContext.CONDITIONAL)
        
        context = self.manager.determine_element_context('checkIfExists')
        self.assertEqual(context, ElementContext.CONDITIONAL)
        
        # Test navigation actions
        context = self.manager.determine_element_context('launchApp')
        self.assertEqual(context, ElementContext.NAVIGATION)
        
        # Test verification actions
        context = self.manager.determine_element_context('waitTill')
        self.assertEqual(context, ElementContext.VERIFICATION)
        
        # Test regular actions
        context = self.manager.determine_element_context('tap')
        self.assertEqual(context, ElementContext.ACTION)
        
    def test_locator_complexity_determination(self):
        """Test locator complexity determination"""
        # Test simple locators
        complexity = self.manager.determine_locator_complexity('id', 'login_button')
        self.assertEqual(complexity, LocatorComplexity.SIMPLE)
        
        complexity = self.manager.determine_locator_complexity('accessibility_id', 'Login Button')
        self.assertEqual(complexity, LocatorComplexity.SIMPLE)
        
        # Test moderate locators
        complexity = self.manager.determine_locator_complexity('class_name', 'android.widget.Button')
        self.assertEqual(complexity, LocatorComplexity.MODERATE)
        
        # Test complex locators
        complexity = self.manager.determine_locator_complexity('xpath', '//android.widget.Button[contains(@text, "Login")]')
        self.assertEqual(complexity, LocatorComplexity.COMPLEX)
        
        complexity = self.manager.determine_locator_complexity('uiselector', 'new UiSelector().text("Login")')
        self.assertEqual(complexity, LocatorComplexity.COMPLEX)
        
    def test_adaptive_timeout_calculation(self):
        """Test adaptive timeout calculation"""
        # Test conditional action with simple locator
        config = self.manager.calculate_adaptive_timeout(
            'tapIfLocatorExists', 'id', 'login_button', 10
        )
        self.assertLessEqual(config['timeout'], 10)  # Should respect conditional timeout limit
        self.assertEqual(config['context'], 'conditional')
        self.assertTrue(config['early_termination'])
        
        # Test action with complex locator
        config = self.manager.calculate_adaptive_timeout(
            'tap', 'xpath', '//android.widget.Button[contains(@text, "Login")]', 30
        )
        self.assertGreater(config['timeout'], 10)  # Should be higher for complex locators
        self.assertEqual(config['context'], 'action')
        self.assertFalse(config['early_termination'])
        
        # Test navigation action
        config = self.manager.calculate_adaptive_timeout(
            'launchApp', 'id', 'app_icon', None
        )
        self.assertGreaterEqual(config['timeout'], 15)  # Navigation should have higher base timeout
        self.assertEqual(config['context'], 'navigation')
        
    def test_fast_fail_logic(self):
        """Test fast-fail logic"""
        # Test fast-fail for invalid locator
        should_fail = self.manager.should_fast_fail('xpath', '//XCUIElementTypeButton[@name="Login"]')
        self.assertTrue(should_fail)
        
        # Test valid locator should not fast-fail
        should_fail = self.manager.should_fast_fail('id', 'login_button')
        self.assertFalse(should_fail)
        
    def test_performance_tracking(self):
        """Test performance tracking functionality"""
        # Record some performance data
        self.manager.record_performance('id', 'login_button', True, 2.5, 10.0)
        self.manager.record_performance('id', 'login_button', True, 1.8, 10.0)
        self.manager.record_performance('id', 'login_button', False, 10.0, 10.0)
        
        # Check performance history
        locator_key = 'id:login_button'
        self.assertIn(locator_key, self.manager.performance_history)
        
        history = self.manager.performance_history[locator_key]
        self.assertEqual(history['attempt_count'], 3)
        self.assertEqual(history['success_count'], 2)
        self.assertAlmostEqual(history['success_rate'], 2/3, places=2)
        
        # Test performance insights
        insights = self.manager.get_performance_insights()
        self.assertEqual(insights['total_locators_tracked'], 1)
        self.assertIsInstance(insights['recommendations'], list)


class TestEnhancedElementFinderIntegration(unittest.TestCase):
    """Test cases for Enhanced Element Finder integration with Adaptive Timeout Manager"""
    
    def setUp(self):
        # Mock the controller and driver
        self.mock_controller = Mock()
        self.mock_driver = Mock()
        self.mock_controller.driver = self.mock_driver
        
        self.finder = EnhancedElementFinder(self.mock_controller)
        
    @patch('app_android.utils.enhanced_element_finder.WebDriverWait')
    @patch('app_android.utils.enhanced_element_finder.EC')
    def test_adaptive_timeout_integration(self, mock_ec, mock_wait):
        """Test that enhanced element finder uses adaptive timeouts"""
        # Mock successful element finding
        mock_element = Mock()
        mock_wait_instance = Mock()
        mock_wait_instance.until.return_value = mock_element
        mock_wait.return_value = mock_wait_instance
        
        # Test with conditional action
        element = self.finder.find_element_intelligently(
            'id', 'login_button', 30, 'conditional', 'tapIfLocatorExists'
        )
        
        self.assertIsNotNone(element)
        # Verify WebDriverWait was called with adaptive timeout
        mock_wait.assert_called()
        
    def test_ios_locator_conversion(self):
        """Test that iOS locators are converted to Android equivalents"""
        with patch('app_android.utils.enhanced_element_finder.WebDriverWait') as mock_wait:
            mock_element = Mock()
            mock_wait_instance = Mock()
            mock_wait_instance.until.return_value = mock_element
            mock_wait.return_value = mock_wait_instance
            
            # Test iOS locator conversion
            element = self.finder.find_element_intelligently(
                'xpath', '//XCUIElementTypeButton[@name="Login"]', 10, 'action', 'tap'
            )
            
            # Should still find element after conversion
            self.assertIsNotNone(element)


class TestTimeoutEfficiencyImprovements(unittest.TestCase):
    """Integration tests for timeout efficiency improvements"""
    
    def test_timeout_reduction_for_conditional_actions(self):
        """Test that conditional actions use reduced timeouts"""
        manager = AdaptiveTimeoutManager()
        
        # Test conditional action timeout
        config = manager.calculate_adaptive_timeout(
            'tapIfLocatorExists', 'id', 'button', 60  # Request 60s
        )
        
        # Should be capped at much lower value for conditional actions
        self.assertLessEqual(config['timeout'], 10)
        self.assertTrue(config['early_termination'])
        
    def test_complex_locator_timeout_adjustment(self):
        """Test that complex locators get appropriate timeout adjustments"""
        manager = AdaptiveTimeoutManager()
        
        # Simple locator should get base timeout
        simple_config = manager.calculate_adaptive_timeout(
            'tap', 'id', 'button', None
        )
        
        # Complex locator should get higher timeout
        complex_config = manager.calculate_adaptive_timeout(
            'tap', 'xpath', '//android.widget.Button[contains(@text, "Login") and @enabled="true"]', None
        )
        
        self.assertGreater(complex_config['timeout'], simple_config['timeout'])


if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(level=logging.DEBUG)
    
    # Run the tests
    unittest.main(verbosity=2)
