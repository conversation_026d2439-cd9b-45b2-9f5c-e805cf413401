#!/usr/bin/env python3
"""
Consolidated Configuration for Android App

This configuration module uses the consolidated database instead of separate database files.
It provides the same interface as the original config files but reads from the consolidated database.
"""

import os
import logging
from pathlib import Path
from app_android.utils.consolidated_db_adapter import get_consolidated_db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize consolidated database
try:
    db = get_consolidated_db()
    logger.info("Connected to consolidated database")
except Exception as e:
    logger.error(f"Failed to connect to consolidated database: {e}")
    raise

# Platform detection
PLATFORM = 'android'
logger.info(f"Platform: {PLATFORM}")

# Port configuration
PORT = int(os.environ.get('FLASK_PORT', 8081))
APPIUM_PORT = int(os.environ.get('APPIUM_PORT', 4724))
WDA_PORT = int(os.environ.get('WDA_PORT', 8300))  # Android doesn't use WDA but keeping for compatibility

logger.info(f"Port: {PORT}")
logger.info(f"Appium Port: {APPIUM_PORT}")
logger.info(f"WDA Port: {WDA_PORT}")

# Get directory paths from consolidated database
def get_directory_paths():
    """Get directory paths from consolidated database with fallbacks."""
    paths = {}
    
    # Default paths relative to project root
    project_root = Path(__file__).resolve().parent.parent
    android_data_root = project_root.parent / 'android_data'
    
    # Directory mappings with fallbacks
    directory_mappings = {
        'TEST_CASES': android_data_root / 'test_cases',
        'REPORTS': project_root / 'reports',
        'SCREENSHOTS': project_root / 'screenshots', 
        'REFERENCE_IMAGES': android_data_root / 'reference_images',
        'TEST_SUITES': android_data_root / 'test_suites',
        'RESULTS': project_root / 'reports' / 'suites',
        'RECORDINGS': project_root / 'recordings',
        'TEMP_FILES': project_root / 'temp'
    }
    
    for name, default_path in directory_mappings.items():
        # Try to get from database first
        db_path = db.get_directory_path(name, PLATFORM)
        if db_path:
            paths[name] = Path(db_path)
            logger.info(f"Loaded from DB: {name} -> {db_path}")
        else:
            # Fall back to default
            paths[name] = default_path
            logger.info(f"Using default: {name} -> {default_path}")
        
        # Ensure directory exists
        paths[name].mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured directory exists: {name} -> {paths[name]}")
    
    return paths

# Load directory paths
DIRECTORIES = get_directory_paths()

# Get global values from consolidated database
def get_global_values():
    """Get global values from consolidated database with fallbacks."""
    values = {}
    
    # Try to get platform-specific values first, then shared
    for platform in [PLATFORM, 'shared']:
        platform_values = db.get_all_global_values(platform)
        values.update(platform_values)
        if platform_values:
            logger.info(f"Loaded {len(platform_values)} global values for platform: {platform}")
    
    return values

# Load global values
GLOBAL_VALUES = get_global_values()

# Export commonly used paths for backward compatibility
TEST_CASES_DIR = DIRECTORIES['TEST_CASES']
REPORTS_DIR = DIRECTORIES['REPORTS']
SCREENSHOTS_DIR = DIRECTORIES['SCREENSHOTS']
REFERENCE_IMAGES_DIR = DIRECTORIES['REFERENCE_IMAGES']
TEST_SUITES_DIR = DIRECTORIES['TEST_SUITES']
RESULTS_DIR = DIRECTORIES['RESULTS']
RECORDINGS_DIR = DIRECTORIES['RECORDINGS']
TEMP_FILES_DIR = DIRECTORIES['TEMP_FILES']

logger.info("Configuration loaded successfully with consolidated database")
logger.info(f"Platform: {PLATFORM}")
logger.info(f"Port: {PORT}")
logger.info(f"Directories: {DIRECTORIES}")