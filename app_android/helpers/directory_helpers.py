"""
Directory Helpers

This module contains helper functions for directory helpers.
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime

# Set up logger
logger = logging.getLogger(__name__)

# Import configuration
try:
    import config_android as config
    from app_android.utils.directory_paths_db import directory_paths_db
except ImportError:
    config = None
    directory_paths_db = None

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def get_temp_directory():
    """Get the configured temp directory for this platform"""
    try:
        import sqlite3
        import os

        # Determine platform based on file location
        platform = 'android' if 'app_android' in __file__ else 'ios'
        db_path = f'data/settings_{platform}.db'

        if not os.path.exists(db_path):
            # Fallback to default temp directory
            temp_dir = f'temp_{platform}'
            os.makedirs(temp_dir, exist_ok=True)
            return temp_dir

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT path FROM directory_paths WHERE name = 'TEMP_FILES'")
        result = cursor.fetchone()
        conn.close()

        if result:
            temp_path = result[0]
            os.makedirs(temp_path, exist_ok=True)
            return temp_path
        else:
            # Fallback to default temp directory
            temp_dir = f'temp_{platform}'
            os.makedirs(temp_dir, exist_ok=True)
            return temp_dir

    except Exception as e:
        # Fallback to current directory temp folder
        platform = 'android' if 'app_android' in __file__ else 'ios'
        temp_dir = f'temp_{platform}'
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir

# Reference Image Management endpoints (metadata + delete)

