"""
Database-Only API Endpoints

Add these endpoints to app_android/app.py to enable database-only features:

1. View HTML report from database
2. Get execution data from database
3. Delete execution with cascade deletion
4. List all executions from database

To integrate, add this to app.py:

    from api_endpoints_database_only import register_database_only_endpoints
    register_database_only_endpoints(app)
"""

from flask import jsonify, Response
import logging

logger = logging.getLogger(__name__)


def register_database_only_endpoints(app):
    """
    Register database-only API endpoints with Flask app
    
    Args:
        app: Flask application instance
    """
    
    @app.route('/api/executions/<execution_id>/report', methods=['GET'])
    def generate_report_from_database(execution_id):
        """
        Generate HTML report from database
        
        GET /api/executions/<execution_id>/report
        
        Returns:
            HTML content with embedded screenshots
        """
        try:
            from utils.report_generator_db import ReportGeneratorDB
            
            logger.info(f"📊 Generating HTML report from database: {execution_id}")
            
            generator = ReportGeneratorDB()
            html_content = generator.generate_html_from_database(execution_id)
            
            if html_content:
                logger.info(f"✅ HTML report generated: {len(html_content)} characters")
                return Response(html_content, mimetype='text/html')
            else:
                logger.warning(f"⚠️  Report not found: {execution_id}")
                return jsonify({'error': 'Report not found'}), 404
                
        except Exception as e:
            logger.error(f"❌ Error generating report: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500
    
    
    @app.route('/api/executions/<execution_id>', methods=['GET'])
    def get_execution_data(execution_id):
        """
        Get execution data from database
        
        GET /api/executions/<execution_id>
        
        Returns:
            JSON with execution data including test cases and steps
        """
        try:
            from utils.database_execution_tracker import DatabaseExecutionTracker
            
            logger.info(f"📊 Getting execution data from database: {execution_id}")
            
            tracker = DatabaseExecutionTracker()
            execution_data = tracker.get_execution_data(execution_id)
            
            if execution_data:
                logger.info(f"✅ Execution data retrieved")
                return jsonify(execution_data)
            else:
                logger.warning(f"⚠️  Execution not found: {execution_id}")
                return jsonify({'error': 'Execution not found'}), 404
                
        except Exception as e:
            logger.error(f"❌ Error getting execution data: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500
    
    
    @app.route('/api/executions/<execution_id>', methods=['DELETE'])
    def delete_execution(execution_id):
        """
        Delete execution with cascade deletion
        
        DELETE /api/executions/<execution_id>
        
        Deletes:
        - Screenshots from database
        - Execution tracking records
        - Execution report record
        
        Returns:
            JSON with deletion counts
        """
        try:
            from utils.screenshot_manager_db import ScreenshotManagerDB
            from utils.database import get_db_path
            import sqlite3
            
            logger.info(f"🗑️  Deleting execution from database: {execution_id}")
            
            screenshot_manager = ScreenshotManagerDB()
            
            # Get database connection
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()
            
            # Delete screenshots
            screenshots_deleted = screenshot_manager.delete_screenshots_for_execution(execution_id)
            logger.info(f"   Deleted {screenshots_deleted} screenshots")
            
            # Delete execution_tracking records
            cursor.execute('DELETE FROM execution_tracking WHERE test_execution_id = ?', (execution_id,))
            steps_deleted = cursor.rowcount
            logger.info(f"   Deleted {steps_deleted} execution tracking records")
            
            # Delete execution_reports record
            cursor.execute('DELETE FROM execution_reports WHERE test_execution_id = ?', (execution_id,))
            reports_deleted = cursor.rowcount
            logger.info(f"   Deleted {reports_deleted} execution reports")
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Execution deleted successfully")
            
            return jsonify({
                'success': True,
                'execution_id': execution_id,
                'deleted': {
                    'screenshots': screenshots_deleted,
                    'steps': steps_deleted,
                    'reports': reports_deleted
                }
            })
            
        except Exception as e:
            logger.error(f"❌ Error deleting execution: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500
    
    
    @app.route('/api/executions', methods=['GET'])
    def list_executions():
        """
        List all executions from database
        
        GET /api/executions?limit=50&offset=0
        
        Query parameters:
        - limit: Number of executions to return (default: 50)
        - offset: Offset for pagination (default: 0)
        
        Returns:
            JSON with list of executions
        """
        try:
            from utils.database import get_db_path
            from flask import request
            import sqlite3
            
            # Get query parameters
            limit = request.args.get('limit', 50, type=int)
            offset = request.args.get('offset', 0, type=int)
            
            logger.info(f"📊 Listing executions from database (limit={limit}, offset={offset})")
            
            # Get database connection
            conn = sqlite3.connect(get_db_path())
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get total count
            cursor.execute('SELECT COUNT(*) as count FROM execution_reports')
            total_count = cursor.fetchone()['count']
            
            # Get executions
            cursor.execute('''
                SELECT 
                    test_execution_id,
                    suite_id,
                    test_case_id,
                    platform,
                    status,
                    start_time,
                    end_time,
                    duration,
                    error_message,
                    created_at
                FROM execution_reports
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (limit, offset))
            
            executions = [dict(row) for row in cursor.fetchall()]
            
            # Get step counts for each execution
            for execution in executions:
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_steps,
                        SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed_steps,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_steps
                    FROM execution_tracking
                    WHERE test_execution_id = ?
                ''', (execution['test_execution_id'],))
                
                step_counts = cursor.fetchone()
                execution['total_steps'] = step_counts['total_steps'] or 0
                execution['passed_steps'] = step_counts['passed_steps'] or 0
                execution['failed_steps'] = step_counts['failed_steps'] or 0
            
            conn.close()
            
            logger.info(f"✅ Found {len(executions)} executions (total: {total_count})")
            
            return jsonify({
                'success': True,
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'executions': executions
            })
            
        except Exception as e:
            logger.error(f"❌ Error listing executions: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500
    
    
    @app.route('/api/executions/<execution_id>/screenshot/<screenshot_filename>', methods=['GET'])
    def get_screenshot(execution_id, screenshot_filename):
        """
        Get screenshot from database
        
        GET /api/executions/<execution_id>/screenshot/<screenshot_filename>
        
        Returns:
            Image data (PNG or JPEG)
        """
        try:
            from utils.screenshot_manager_db import ScreenshotManagerDB
            
            logger.info(f"📸 Getting screenshot from database: {screenshot_filename}")
            
            screenshot_manager = ScreenshotManagerDB()
            screenshot_data = screenshot_manager.get_screenshot_from_db(screenshot_filename)
            
            if screenshot_data:
                logger.info(f"✅ Screenshot retrieved: {len(screenshot_data)} bytes")
                # Determine MIME type from filename
                mime_type = 'image/jpeg' if screenshot_filename.endswith('.jpg') or screenshot_filename.endswith('.jpeg') else 'image/png'
                return Response(screenshot_data, mimetype=mime_type)
            else:
                logger.warning(f"⚠️  Screenshot not found: {screenshot_filename}")
                return jsonify({'error': 'Screenshot not found'}), 404
                
        except Exception as e:
            logger.error(f"❌ Error getting screenshot: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500
    
    
    logger.info("✅ Database-only API endpoints registered")
    logger.info("   - GET /api/executions/<execution_id>/report")
    logger.info("   - GET /api/executions/<execution_id>")
    logger.info("   - DELETE /api/executions/<execution_id>")
    logger.info("   - GET /api/executions")
    logger.info("   - GET /api/executions/<execution_id>/screenshot/<screenshot_filename>")

