"""
Execution Logging Routes

API endpoints for execution logging, past runs retrieval, test retry, and report generation

Author: Mobile App Automation Tool
Date: 2025-01-07
"""

from flask import Blueprint, request, jsonify, send_file
import logging
from pathlib import Path
import io
import json

# Create blueprint
execution_logging_bp = Blueprint('execution_logging', __name__)

# Initialize services
from app_android.utils.execution_logging_service import ExecutionLoggingService
from app_android.utils.past_runs_service import PastRunsService
from app_android.utils.test_retry_service import TestRetryService
from app_android.utils.comprehensive_report_service import ComprehensiveReportService

logger = logging.getLogger(__name__)

# Initialize services with default database paths
execution_logging_service = ExecutionLoggingService()
past_runs_service = PastRunsService()
test_retry_service = TestRetryService()
report_service = ComprehensiveReportService()


@execution_logging_bp.route('/api/executions/list', methods=['GET'])
def list_executions():
    """
    List all executions with optional filtering
    
    Query Parameters:
        platform: Filter by platform ('ios' or 'android')
        status: Filter by status ('running', 'completed', 'failed', 'cancelled')
        start_date: Filter by start date (ISO format)
        end_date: Filter by end date (ISO format)
        suite_id: Filter by suite ID
        limit: Maximum number of results (default: 50)
        offset: Offset for pagination (default: 0)
    """
    try:
        platform = request.args.get('platform')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        suite_id = request.args.get('suite_id')
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))
        
        executions = past_runs_service.get_all_executions(
            platform=platform,
            status=status,
            start_date=start_date,
            end_date=end_date,
            suite_id=suite_id,
            limit=limit,
            offset=offset
        )
        
        return jsonify({
            'status': 'success',
            'executions': executions,
            'count': len(executions)
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing executions: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/<execution_id>/details', methods=['GET'])
def get_execution_details(execution_id):
    """Get detailed execution data including summary and test cases"""
    try:
        details = past_runs_service.get_execution_details(execution_id)
        
        if not details:
            return jsonify({
                'status': 'error',
                'message': f'Execution not found: {execution_id}'
            }), 404
        
        return jsonify({
            'status': 'success',
            'execution': details
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting execution details: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/<execution_id>/logs', methods=['GET'])
def get_execution_logs(execution_id):
    """Get step-by-step execution logs"""
    try:
        logs = past_runs_service.get_execution_logs(execution_id)
        
        return jsonify({
            'status': 'success',
            'logs': logs,
            'count': len(logs)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting execution logs: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/<execution_id>/failed-tests', methods=['GET'])
def get_failed_tests(execution_id):
    """Get all failed tests for an execution"""
    try:
        failed_tests = past_runs_service.get_failed_tests(execution_id)
        
        return jsonify({
            'status': 'success',
            'failed_tests': failed_tests,
            'count': len(failed_tests)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting failed tests: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/<execution_id>/statistics', methods=['GET'])
def get_execution_statistics(execution_id):
    """Get detailed statistics for an execution"""
    try:
        stats = past_runs_service.get_execution_statistics(execution_id)
        
        if not stats:
            return jsonify({
                'status': 'error',
                'message': f'Execution not found: {execution_id}'
            }), 404
        
        return jsonify({
            'status': 'success',
            'statistics': stats
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting execution statistics: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/search', methods=['POST'])
def search_executions():
    """
    Search executions by execution ID or suite name
    
    Request Body:
        search_term: Search term
        filters: Additional filters (platform, status, etc.)
    """
    try:
        data = request.get_json() or {}
        search_term = data.get('search_term', '')
        filters = data.get('filters', {})
        
        results = past_runs_service.search_executions(search_term, filters)
        
        return jsonify({
            'status': 'success',
            'results': results,
            'count': len(results)
        }), 200
        
    except Exception as e:
        logger.error(f"Error searching executions: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/<execution_id>/retry-test/<test_case_id>', methods=['POST'])
def retry_single_test(execution_id, test_case_id):
    """
    Retry a single failed test
    
    This endpoint returns the test case information needed to retry the test.
    The actual retry execution should be triggered by the existing test execution endpoint.
    """
    try:
        # Get failed test info
        failed_test_info = test_retry_service.get_failed_test_info(execution_id, test_case_id)
        
        if not failed_test_info:
            return jsonify({
                'status': 'error',
                'message': f'Failed test not found: {test_case_id} in execution {execution_id}'
            }), 404
        
        # Get test case filename
        filename = test_retry_service.get_test_case_filename(execution_id, test_case_id)
        
        if not filename:
            return jsonify({
                'status': 'error',
                'message': f'Test case filename not found for: {test_case_id}'
            }), 404
        
        # Get suite info
        suite_info = test_retry_service.get_suite_info(execution_id)
        
        return jsonify({
            'status': 'success',
            'message': 'Test case ready for retry',
            'test_case': {
                'test_case_id': test_case_id,
                'filename': filename,
                'original_execution_id': execution_id,
                'failure_reason': failed_test_info.get('failure_reason'),
                'failed_step_idx': failed_test_info.get('step_idx'),
                'failed_action_id': failed_test_info.get('action_id')
            },
            'suite_info': suite_info
        }), 200
        
    except Exception as e:
        logger.error(f"Error preparing test retry: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/executions/<execution_id>/retry-all-failed', methods=['POST'])
def retry_all_failed_tests(execution_id):
    """
    Get all failed tests for retry
    
    This endpoint returns all failed test cases that need to be retried.
    The actual retry execution should be triggered by the existing test execution endpoint.
    """
    try:
        # Get all failed tests
        failed_tests = test_retry_service.get_all_failed_tests(execution_id)
        
        if not failed_tests:
            return jsonify({
                'status': 'success',
                'message': 'No failed tests found',
                'failed_tests': []
            }), 200
        
        # Get suite info
        suite_info = test_retry_service.get_suite_info(execution_id)
        
        # Prepare test case list for retry
        test_cases_to_retry = []
        for failed_test in failed_tests:
            filename = failed_test.get('test_case_filename')
            if not filename:
                filename = test_retry_service.get_test_case_filename(
                    execution_id,
                    failed_test.get('test_case_id')
                )
            
            if filename:
                test_cases_to_retry.append({
                    'test_case_id': failed_test.get('test_case_id'),
                    'filename': filename,
                    'failure_reason': failed_test.get('failure_reason'),
                    'failed_step_idx': failed_test.get('step_idx'),
                    'failed_action_id': failed_test.get('action_id')
                })
        
        return jsonify({
            'status': 'success',
            'message': f'Found {len(test_cases_to_retry)} failed tests to retry',
            'failed_tests': test_cases_to_retry,
            'suite_info': suite_info,
            'original_execution_id': execution_id
        }), 200
        
    except Exception as e:
        logger.error(f"Error preparing failed tests retry: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/reports/execution/<execution_id>', methods=['GET'])
def generate_execution_report(execution_id):
    """
    Generate report for a specific execution
    
    Query Parameters:
        format: Report format ('html', 'json', 'pdf') - default: 'html'
    """
    try:
        format = request.args.get('format', 'html')
        
        report_content = report_service.generate_execution_report(execution_id, format)
        
        if not report_content:
            return jsonify({
                'status': 'error',
                'message': f'Could not generate report for execution: {execution_id}'
            }), 404
        
        if format == 'json':
            return jsonify({
                'status': 'success',
                'report': json.loads(report_content)
            }), 200
        elif format == 'html' or format == 'pdf':
            # Return HTML content
            return report_content, 200, {'Content-Type': 'text/html'}
        else:
            return jsonify({
                'status': 'error',
                'message': f'Unsupported format: {format}'
            }), 400
        
    except Exception as e:
        logger.error(f"Error generating execution report: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@execution_logging_bp.route('/api/reports/date-range', methods=['GET'])
def generate_date_range_report():
    """
    Generate report for executions within a date range
    
    Query Parameters:
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
        format: Report format ('html', 'json') - default: 'html'
    """
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        format = request.args.get('format', 'html')
        
        if not start_date or not end_date:
            return jsonify({
                'status': 'error',
                'message': 'start_date and end_date are required'
            }), 400
        
        report_content = report_service.generate_date_range_report(start_date, end_date, format)
        
        if not report_content:
            return jsonify({
                'status': 'error',
                'message': 'Could not generate date range report'
            }), 404
        
        if format == 'json':
            return jsonify({
                'status': 'success',
                'report': json.loads(report_content)
            }), 200
        elif format == 'html':
            return report_content, 200, {'Content-Type': 'text/html'}
        else:
            return jsonify({
                'status': 'error',
                'message': f'Unsupported format: {format}'
            }), 400
        
    except Exception as e:
        logger.error(f"Error generating date range report: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

