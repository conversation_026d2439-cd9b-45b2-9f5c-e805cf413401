#!/usr/bin/env python3
"""
Run database migrations for Android automation app.
This script applies all necessary schema changes for database-only execution tracking.
"""

import sys
import os
import logging

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.database import run_database_migrations, verify_database_schema, get_db_path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """Run database migrations"""
    print("=" * 80)
    print("Android Automation - Database Migrations")
    print("=" * 80)
    print()
    
    # Get database path
    db_path = get_db_path('execution_tracker')
    print(f"Database path: {db_path}")
    print()
    
    # Check if database exists
    if not os.path.exists(db_path):
        print(f"⚠️  Database does not exist: {db_path}")
        print("The database will be created when the app starts.")
        print("Please run the app first, then run this migration script.")
        return 1
    
    # Verify current schema
    print("Checking current database schema...")
    is_up_to_date = verify_database_schema(db_path)
    print()
    
    if is_up_to_date:
        print("✅ Database schema is already up to date!")
        print("No migrations needed.")
        return 0
    
    # Run migrations
    print("Running database migrations...")
    print()
    
    success = run_database_migrations(db_path)
    print()
    
    if success:
        print("=" * 80)
        print("✅ Database migrations completed successfully!")
        print("=" * 80)
        print()
        print("The following changes have been applied:")
        print()
        print("1. screenshots table:")
        print("   - Added screenshot_blob (BLOB) column")
        print("   - Added screenshot_mime (TEXT) column")
        print("   - Added test_execution_id (TEXT) column")
        print("   - Added test_case_id (TEXT) column")
        print("   - Added compressed_size (INTEGER) column")
        print("   - Added original_size (INTEGER) column")
        print()
        print("2. execution_reports table:")
        print("   - Added test_case_id (TEXT) column")
        print("   - Added platform (TEXT) column")
        print("   - Added start_time (TIMESTAMP) column")
        print("   - Added end_time (TIMESTAMP) column")
        print("   - Added duration (INTEGER) column")
        print("   - Added error_message (TEXT) column")
        print("   - Added report_data (TEXT) column")
        print()
        print("3. execution_tracking table:")
        print("   - Added screenshot_filename (VARCHAR) column")
        print()
        print("4. Database indexes:")
        print("   - Created indexes for better query performance")
        print()
        print("=" * 80)
        print()
        
        # Verify migrations
        print("Verifying migrations...")
        is_verified = verify_database_schema(db_path)
        print()
        
        if is_verified:
            print("✅ All migrations verified successfully!")
            return 0
        else:
            print("⚠️  Some migrations may not have been applied correctly.")
            print("Please check the logs for details.")
            return 1
    else:
        print("=" * 80)
        print("❌ Database migrations failed!")
        print("=" * 80)
        print()
        print("Please check the logs for error details.")
        print("You may need to:")
        print("1. Check database file permissions")
        print("2. Ensure the database is not locked by another process")
        print("3. Backup your database and try again")
        print()
        return 1


if __name__ == '__main__':
    sys.exit(main())

