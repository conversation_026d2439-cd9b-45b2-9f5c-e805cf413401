"""
App Factory for app_android

This module creates and configures the Flask application instance.
"""

import os
import sys
import logging
import re
from pathlib import Path
from flask import Flask
from logging.config import dictConfig

# Set up logger
logger = logging.getLogger(__name__)

# Add app directory to path if not already in it
current_dir = Path(__file__).resolve().parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

def create_app():
    """Create and configure the Flask application"""

    # Get the app_android directory (parent of core)
    app_android_dir = Path(__file__).resolve().parent.parent

    # Initialize Flask app with correct template and static folders
    app = Flask(
        __name__,
        template_folder=str(app_android_dir / 'templates'),
        static_folder=str(app_android_dir / 'static')
    )
    app.config.from_object('config_android.FlaskConfig')
    app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'a_very_secret_key_that_should_be_set_in_env')
    
    # Security: scrub sensitive query parameters from logs
    class _QueryStringScrubber(logging.Filter):
        def filter(self, record: logging.LogRecord) -> bool:
            try:
                msg = record.getMessage()
                scrubbed = re.sub(r'(session_token=)[^&\s]+', r'\1[REDACTED]', msg)
                if scrubbed != msg:
                    record.msg = scrubbed
                    record.args = ()
            except Exception:
                pass
            return True
    
    logging.getLogger('werkzeug').addFilter(_QueryStringScrubber())
    logging.getLogger().addFilter(_QueryStringScrubber())
    
    # Configure logging
    dictConfig({
        'version': 1,
        'formatters': {'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        }},
        'handlers': {'wsgi': {
            'class': 'logging.StreamHandler',
            'stream': 'ext://flask.logging.wsgi_errors_stream',
            'formatter': 'default'
        }},
        'root': {
            'level': 'INFO',
            'handlers': ['wsgi']
        },
        'loggers': {
            'appium_device_controller': {
                'level': 'DEBUG',
                'handlers': ['wsgi'],
                'propagate': False
            },
            'utils.parameter_utils': {
                'level': 'DEBUG',
                'handlers': ['wsgi'],
                'propagate': False
            },
            'actions': {
                'level': 'DEBUG',
                'handlers': ['wsgi'],
                'propagate': False
            }
        }
    })
    
    # Set additional Flask app config
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'default-secret-key')
    app.config['SCREENSHOTS_FOLDER'] = os.path.join(app.root_path, 'static', 'screenshots')
    
    # Initialize directories from config
    _initialize_directories()
    
    # Initialize managers
    _initialize_managers()
    
    # Register blueprints
    _register_blueprints(app)
    
    return app

def _initialize_directories():
    """Initialize directory paths from config"""
    from . import state_manager
    
    try:
        from config_android import DIRECTORIES
        state_manager.TEST_CASES_DIR = DIRECTORIES['TEST_CASES']
        state_manager.REFERENCE_IMAGES_DIR = DIRECTORIES['REFERENCE_IMAGES']
        state_manager.SCREENSHOTS_DIR = DIRECTORIES['SCREENSHOTS']
        logger.info(f"Using directories from config_android.py:")
        logger.info(f"  - TEST_CASES_DIR: {state_manager.TEST_CASES_DIR}")
        logger.info(f"  - REFERENCE_IMAGES_DIR: {state_manager.REFERENCE_IMAGES_DIR}")
        logger.info(f"  - SCREENSHOTS_DIR: {state_manager.SCREENSHOTS_DIR}")
    except ImportError:
        logger.error("Failed to import config_android.py - this is required for correct operation")
        from app_android.utils.directory_utils import get_test_cases_directory
        state_manager.TEST_CASES_DIR = Path(get_test_cases_directory())
        state_manager.REFERENCE_IMAGES_DIR = Path(__file__).resolve().parent.parent.parent / 'reference_images'
        state_manager.SCREENSHOTS_DIR = Path(__file__).resolve().parent.parent / 'static' / 'screenshots'
        logger.warning(f"Using fallback directories:")
        logger.warning(f"  - TEST_CASES_DIR: {state_manager.TEST_CASES_DIR}")
        logger.warning(f"  - REFERENCE_IMAGES_DIR: {state_manager.REFERENCE_IMAGES_DIR}")
        logger.warning(f"  - SCREENSHOTS_DIR: {state_manager.SCREENSHOTS_DIR}")
    
    # Set screenshots directories
    state_manager.screenshots_dir = str(state_manager.SCREENSHOTS_DIR)
    state_manager.app_screenshots_dir = os.path.join(str(Path(__file__).resolve().parent.parent), 'static', 'screenshots')

def _initialize_managers():
    """Initialize manager instances"""
    from . import state_manager
    from app_android.utils.test_case_manager import TestCaseManager
    from app_android.test_suites_manager import TestSuitesManager
    from app_android.utils.import_export_manager import ImportExportManager
    
    try:
        from config_android import DIRECTORIES
    except ImportError:
        from pathlib import Path
        BASE_DIR = Path(__file__).resolve().parent.parent.parent
        DIRECTORIES = {
            'TEST_CASES': BASE_DIR / 'test_cases',
            'TEST_SUITES': BASE_DIR / 'test_suites'
        }
    
    # Initialize managers
    state_manager.test_case_manager = TestCaseManager(state_manager.TEST_CASES_DIR)
    state_manager.test_suites_manager = TestSuitesManager()
    state_manager.import_export_manager = ImportExportManager(
        DIRECTORIES['TEST_CASES'],
        DIRECTORIES['TEST_SUITES']
    )

def _register_blueprints(app):
    """Register Flask blueprints"""
    from app_android.routes.random_data_routes import random_data_bp
    from app_android.routes.devices import devices_bp
    from app_android.routes.tools import tools_bp
    
    app.register_blueprint(random_data_bp)
    app.register_blueprint(devices_bp)
    app.register_blueprint(tools_bp)
    
    logger.info("Blueprints registered successfully")

# Create the app instance
app = create_app()

# Export for backward compatibility
__all__ = ['create_app', 'app']

