"""Expose shared Android config from the root project without ambiguous imports."""

from __future__ import annotations

import importlib.util
import sys
from pathlib import Path


def _load_root_config_module():
    """Load the top-level config_android.py file in a robust way."""
    candidates = []

    # When bundled (e.g. PyInstaller), files may reside in _MEIPASS
    meipass = getattr(sys, "_MEIPASS", None)
    if meipass:
        candidates.append(Path(meipass) / "config_android.py")

    # Standard project layout: project root contains config_android.py
    candidates.append(Path(__file__).resolve().parents[1] / "config_android.py")
    candidates.append(Path.cwd() / "config_android.py")

    for candidate in candidates:
        if not candidate.exists():
            continue

        spec = importlib.util.spec_from_file_location("_root_config_android", candidate)
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return module

    raise ModuleNotFoundError("Unable to locate root config_android.py")


_ROOT_CONFIG = _load_root_config_module()

# Re-export public attributes from the root module.
for _name in dir(_ROOT_CONFIG):
    if _name.startswith('_'):
        continue
    globals()[_name] = getattr(_ROOT_CONFIG, _name)

__all__ = [name for name in globals() if not name.startswith('_')]
