#!/usr/bin/env python3
"""
Test Execution with Database-Only Tracking

This script demonstrates how to use the database-only execution tracking
during test execution.

It simulates a test suite execution and shows how database records are created.
"""

import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.execution_tracker_wrapper import ExecutionTrackerWrapper
from utils.database import get_db_path
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def simulate_test_execution():
    """Simulate a test suite execution with database-only tracking"""
    
    print("\n" + "=" * 80)
    print("SIMULATING TEST EXECUTION WITH DATABASE-ONLY TRACKING")
    print("=" * 80)
    
    # Initialize execution tracker wrapper
    wrapper = ExecutionTrackerWrapper()
    
    if not wrapper.enabled:
        print("\n❌ Database-only tracking is not enabled!")
        return False
    
    # Step 1: Start execution
    print("\n1. Starting execution...")
    execution_id = wrapper.start_execution(
        suite_id='test_suite_demo',
        suite_name='Demo Test Suite',
        platform='Android'
    )
    
    if not execution_id:
        print("❌ Failed to start execution")
        return False
    
    print(f"✅ Execution started: {execution_id}")
    
    # Step 2: Simulate test case execution with steps
    print("\n2. Executing test case with 5 steps...")
    
    test_case_id = 'test_case_demo_001'
    test_case_filename = 'demo_test_case.json'
    suite_id = 'test_suite_demo'
    
    for step_idx in range(5):
        action_id = f'action_{step_idx}'
        action_type = ['tap', 'input', 'wait', 'swipe', 'tap'][step_idx]
        status = 'passed' if step_idx < 4 else 'failed'  # Last step fails
        
        print(f"   Step {step_idx}: {action_type} - {status}")
        
        # Track step
        step_id = wrapper.track_step(
            execution_id=execution_id,
            test_case_id=test_case_id,
            step_idx=step_idx,
            action_type=action_type,
            status=status,
            screenshot_filename=f'screenshot_{step_idx}.png',
            action_id=action_id,
            suite_id=suite_id,
            filename=test_case_filename,
            error_message='Element not found' if status == 'failed' else None
        )
        
        if step_id:
            print(f"      ✅ Step tracked: ID={step_id}")
        else:
            print(f"      ❌ Step tracking failed")
        
        # Small delay to simulate execution time
        time.sleep(0.1)
    
    # Step 3: Complete execution
    print("\n3. Completing execution...")
    report_data = wrapper.complete_execution(
        execution_id=execution_id,
        status='completed'
    )
    
    if report_data:
        print("✅ Execution completed successfully")
        print(f"   Report data keys: {list(report_data.keys())}")
        summary = report_data.get('summary', {})
        print(f"   Total test cases: {summary.get('total_tests', 0)}")
        print(f"   Total steps: {summary.get('total_steps', 0)}")
        print(f"   Passed: {summary.get('passed', 0)}")
        print(f"   Failed: {summary.get('failed', 0)}")
    else:
        print("❌ Execution completion failed")
        return False
    
    # Step 4: Verify database records
    print("\n4. Verifying database records...")
    verify_database_records(execution_id)
    
    # Step 5: Generate HTML report
    print("\n5. Generating HTML report...")
    html_content = wrapper.generate_html_report(execution_id)
    
    if html_content:
        print(f"✅ HTML report generated: {len(html_content)} characters")
        
        # Save to file for inspection
        report_file = f'/tmp/test_report_{execution_id}.html'
        with open(report_file, 'w') as f:
            f.write(html_content)
        print(f"   Report saved to: {report_file}")
    else:
        print("❌ HTML report generation failed")
    
    print("\n" + "=" * 80)
    print("✅ TEST EXECUTION SIMULATION COMPLETE")
    print("=" * 80)
    print(f"\nExecution ID: {execution_id}")
    print(f"Database: {get_db_path()}")
    print("\nYou can now:")
    print(f"1. View the report: open {report_file}")
    print(f"2. Query the database: sqlite3 {get_db_path()}")
    print(f"3. Use API endpoints: GET /api/executions/{execution_id}/report")
    print("=" * 80)
    
    return True


def verify_database_records(execution_id):
    """Verify that database records were created"""
    import sqlite3
    
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check execution_reports
    cursor.execute('SELECT COUNT(*) FROM execution_reports WHERE test_execution_id = ?', (execution_id,))
    report_count = cursor.fetchone()[0]
    print(f"   execution_reports: {report_count} record(s)")
    
    # Check execution_tracking
    cursor.execute('SELECT COUNT(*) FROM execution_tracking WHERE test_execution_id = ?', (execution_id,))
    tracking_count = cursor.fetchone()[0]
    print(f"   execution_tracking: {tracking_count} record(s)")
    
    # Check screenshots
    cursor.execute('SELECT COUNT(*) FROM screenshots WHERE test_execution_id = ?', (execution_id,))
    screenshot_count = cursor.fetchone()[0]
    print(f"   screenshots: {screenshot_count} record(s)")
    
    conn.close()
    
    if report_count > 0 and tracking_count > 0:
        print("   ✅ Database records verified")
    else:
        print("   ⚠️  Some database records missing")


def main():
    """Main entry point"""
    print("\n" + "=" * 80)
    print("DATABASE-ONLY EXECUTION TRACKING - DEMONSTRATION")
    print("=" * 80)
    
    # Check database exists
    db_path = get_db_path()
    print(f"\nDatabase path: {db_path}")
    print(f"Database exists: {os.path.exists(db_path)}")
    
    if not os.path.exists(db_path):
        print("\n⚠️  Database does not exist. Please run the app first to create it.")
        return 1
    
    # Run simulation
    success = simulate_test_execution()
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())

