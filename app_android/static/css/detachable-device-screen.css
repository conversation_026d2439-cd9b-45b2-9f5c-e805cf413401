/* Detachable Device Screen Styles */

/* Detach button styling */
#detachDeviceScreenBtn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.3s ease;
}

#detachDeviceScreenBtn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-1px);
}

#detachDeviceScreenBtn:active {
    transform: translateY(0);
}

/* Device screen container transition for detachment */
.device-screen-container {
    transition: opacity 0.3s ease, pointer-events 0.3s ease;
}

.device-screen-container.detached {
    opacity: 0.3;
    pointer-events: none;
}

/* Detached window specific styles (applied via JavaScript) */
.detached-window-body {
    margin: 0;
    padding: 0;
    background: #1a1a1a;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

.detached-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 15px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    position: relative;
}

.detached-header.android {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.platform-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    margin-left: 10px;
}

.close-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255,255,255,0.3);
}

.detached-device-container {
    position: relative;
    width: 100%;
    height: calc(100vh - 44px);
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2a2a2a;
}

.detached-device-screen {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
    background: #000;
}

.detached-loading-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #888;
    font-size: 14px;
    text-align: center;
}

.status-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-indicator.error-state {
    background: #f44336 !important;
}

/* Responsive adjustments for detached window */
@media (max-width: 480px) {
    .detached-header {
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .platform-badge {
        padding: 2px 8px;
        font-size: 10px;
        margin-left: 5px;
    }
    
    .close-btn {
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
}
