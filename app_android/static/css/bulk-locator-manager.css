/* Bulk Locator Search and Replace Styles */

/* Search Results Table */
#searchResultsTable {
    font-size: 0.9rem;
}

#searchResultsTable th {
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#searchResultsTable td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

#searchResultsTable .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

#searchResultsTable code {
    font-size: 0.8rem;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    word-break: break-all;
}

/* Progress Bar Enhancements */
.progress {
    height: 0.75rem;
    background-color: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Button Group Styling */
.btn-group .btn {
    position: relative;
    z-index: 1;
}

.btn-group .btn:hover {
    z-index: 2;
}

/* Input Group Enhancements */
.input-group .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.input-group .btn-outline-secondary:hover,
.input-group .btn-outline-info:hover {
    z-index: 3;
}

/* Modal Enhancements */
.modal-header.bg-warning {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.modal-header.bg-secondary {
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

/* Affected Files List */
#affectedFilesList {
    max-height: 200px;
    overflow-y: auto;
}

#affectedFilesList li {
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#affectedFilesList li:last-child {
    border-bottom: none;
}

#affectedFilesList i {
    margin-right: 0.5rem;
    color: #6c757d;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header.bg-warning {
    background-color: #fff3cd !important;
    color: #664d03 !important;
}

/* Form Enhancements */
.form-label.fw-bold {
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Loading States */
.btn .spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #searchResultsTable {
        font-size: 0.8rem;
    }
    
    #searchResultsTable th,
    #searchResultsTable td {
        padding: 0.5rem 0.25rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.375rem !important;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Hover Effects */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Badge Styling */
.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000 !important;
}

/* Scrollbar Styling for Webkit Browsers */
.table-responsive::-webkit-scrollbar,
#affectedFilesList::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track,
#affectedFilesList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb,
#affectedFilesList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover,
#affectedFilesList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for Search Results */
#searchResultsContainer {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status Badge */
#searchStatusBadge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
