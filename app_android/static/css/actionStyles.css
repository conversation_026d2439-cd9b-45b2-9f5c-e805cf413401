/* Styles for action execution visual indicators */
.list-group-item.executing {
    background-color: #e9f5ff !important;
    border-left: 4px solid #0d6efd !important;
    animation: pulse-blue 2s infinite;
}
.list-group-item.success:not(.error) {
    background-color: #e8f7ee !important;
    border-left: 4px solid #198754 !important;
}
.list-group-item.error:not(.success) {
    background-color: #feeceb !important;
    border-left: 4px solid #dc3545 !important;
}
/* When both classes are present, success should take precedence */
.list-group-item.success.error {
    background-color: #e8f7ee !important;
    border-left: 4px solid #198754 !important;
}
@keyframes pulse-blue {
    0% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.2); }
    70% { box-shadow: 0 0 0 6px rgba(13, 110, 253, 0); }
    100% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0); }
}

/* Disabled action styles */
.list-group-item.action-disabled {
    background-color: #f8f9fa !important;
    opacity: 0.6;
    border-left: 4px solid #6c757d !important;
}

.list-group-item.action-disabled .action-content {
    text-decoration: line-through;
    color: #6c757d;
}

.list-group-item.action-disabled .badge {
    opacity: 0.7;
}

/* Multi-step action styles */
.multi-step-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

.multi-step-item {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    background-color: white;
    margin-bottom: 0.25rem;
}

.multi-step-item:last-child {
    margin-bottom: 0;
}

.multi-step-content {
    flex: 1;
}

.multi-step-status {
    min-width: 20px;
}

/* Cleanup checkbox styles */
.cleanup-checkbox-container {
    display: flex;
    align-items: center;
    margin-left: auto;
    margin-right: 0.5rem;
}

.cleanup-step-checkbox {
    margin-right: 0.25rem !important;
}

.cleanup-checkbox-container .form-check-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
    cursor: pointer;
    user-select: none;
}

.cleanup-checkbox-container .form-check-label:hover {
    color: #495057;
}

.cleanup-step-checkbox:checked + .form-check-label {
    color: #856404;
    font-weight: 500;
}

/* Cleanup badge styles */
.cleanup-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

/* Multi-step item with cleanup step styling */
.multi-step-item:has(.cleanup-step-checkbox:checked) {
    border-left: 3px solid #ffc107;
    background-color: #fffbf0;
}

/* Fallback for browsers that don't support :has() */
.multi-step-item.has-cleanup-step {
    border-left: 3px solid #ffc107;
    background-color: #fffbf0;
}