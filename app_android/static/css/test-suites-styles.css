/* Styles for the Test Suites Tab */

/* Header styling - ensure white text */
.card-header.bg-primary h5,
.card-header.bg-primary h5 i {
    color: white !important;
}

/* Available Test Cases List Styling */
#availableTestCases .list-group-item {
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

#availableTestCases .list-group-item:hover {
    background-color: #e9ecef;
}

#availableTestCases .list-group-item.selected {
    background-color: #cfe2ff;
    border-color: #b6d4fe;
    color: #0a58ca;
}

/* Existing Test Suites List Styling */
#testSuitesList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    margin-bottom: 0.5rem;
    background-color: #fff;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.test-suite-info {
    flex-grow: 1;
    margin-right: 1rem;
}

.test-suite-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #343a40;
    margin-bottom: 0.25rem;
}

.test-suite-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.test-suite-actions .btn {
    margin-left: 0.5rem;
}

/* Create Test Suite Button Background */
#createTestSuiteBtn {
    background-color: #e9ecef; /* Light gray background */
    border-color: #ced4da;
    color: #495057;
}

#createTestSuiteBtn:hover {
    background-color: #dee2e6;
    border-color: #adb5bd;
}

/* Checkbox Styling (General for the tab) */
.form-check-input {
    border: 1px solid #adb5bd; /* Add a clearer border */
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Styling for checkboxes within the modal */
#createTestSuiteModal .form-check-input,
#editTestSuiteModal .form-check-input { /* Assuming an edit modal exists */
    border: 1px solid #ced4da;
}

/* Styling for selected test cases list in modal */
#selectedTestCases .list-group-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: move;
    transition: background-color 0.2s ease, transform 0.1s ease;
    border-left: 3px solid transparent;
}

#selectedTestCases .list-group-item:hover {
    background-color: #f0f8ff;
    border-left-color: #0d6efd;
}

/* Drag handle styling */
#selectedTestCases .drag-handle {
    color: #6c757d;
    cursor: grab;
}

#selectedTestCases .list-group-item:hover .drag-handle {
    color: #0d6efd;
}

/* Sortable ghost (the dragging item) */
.sortable-ghost {
    opacity: 0.5;
    background-color: #e9ecef !important;
}

/* Sortable chosen (the item being dragged) */
.sortable-chosen {
    background-color: #f8f9fa !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
    transform: scale(1.02);
}

/* Sortable drag (the clone that follows the cursor) */
.sortable-drag {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
}

/* Improved Create Test Suite Modal Styling */
#createTestSuiteModal .modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: none;
}

#createTestSuiteModal .modal-header {
    border-bottom: 2px solid #dee2e6;
}

#createTestSuiteModal .modal-body {
    padding: 1.5rem;
}

/* Improved form field styling */
#createTestSuiteModal .form-control {
    border: 2px solid #ced4da;
    border-radius: 6px;
    padding: 0.625rem 0.75rem;
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    background-color: #fff;
}

#createTestSuiteModal .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: 0;
}

#createTestSuiteModal .form-label {
    font-weight: 500;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

#createTestSuiteModal .mb-3 {
    margin-bottom: 1.5rem !important;
}

/* Styling for the Selected Test Cases section */
#createTestSuiteModal #selectedTestCases {
    border: 2px solid #ced4da;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

#createTestSuiteModal #selectedTestCases:empty {
    padding: 1.5rem;
    text-align: center;
    color: #6c757d;
}

#createTestSuiteModal #selectedTestCases:empty:before {
    content: "No test cases selected";
    font-style: italic;
}

/* Improved button styling */
#createTestSuiteModal .modal-footer .btn {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
}

/* Test Case Selector Styling */
#testCaseSelector {
    border: 2px solid #ced4da;
    border-radius: 6px 0 0 6px;
    padding: 0.625rem 0.75rem;
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    background-color: #fff;
}

#testCaseSelector:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: 0;
}

#addTestCaseBtn {
    border: 2px solid #ced4da;
    border-left: none;
    border-radius: 0 6px 6px 0;
    padding: 0.625rem 1rem;
    background-color: #f8f9fa;
    color: #495057;
    transition: all 0.2s ease-in-out;
}

#addTestCaseBtn:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}