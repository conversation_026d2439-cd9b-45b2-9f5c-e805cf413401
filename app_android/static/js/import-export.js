class ImportExportManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Export functionality
        const exportTestCaseBtn = document.getElementById('exportTestCaseBtn');
        if (exportTestCaseBtn) {
            exportTestCaseBtn.addEventListener('click', () => this.exportTestCase());
        }

        const exportTestSuiteBtn = document.getElementById('exportTestSuiteBtn');
        if (exportTestSuiteBtn) {
            exportTestSuiteBtn.addEventListener('click', () => this.exportTestSuite());
        }

        // Import functionality
        const importTestCaseBtn = document.getElementById('importTestCaseBtn');
        if (importTestCaseBtn) {
            importTestCaseBtn.addEventListener('click', () => this.importTestCase());
        }

        const importTestSuiteBtn = document.getElementById('importTestSuiteBtn');
        if (importTestSuiteBtn) {
            importTestSuiteBtn.addEventListener('click', () => this.importTestSuite());
        }

        // File input handlers
        const testCaseFileInput = document.getElementById('testCaseFileInput');
        if (testCaseFileInput) {
            testCaseFileInput.addEventListener('change', (e) => this.handleTestCaseFileSelect(e));
        }

        const testSuiteFileInput = document.getElementById('testSuiteFileInput');
        if (testSuiteFileInput) {
            testSuiteFileInput.addEventListener('change', (e) => this.handleTestSuiteFileSelect(e));
        }
    }

    async exportTestCase() {
        try {
            const testCaseSelect = document.getElementById('testCaseSelect');
            if (!testCaseSelect || !testCaseSelect.value) {
                this.showNotification('Please select a test case to export', 'error');
                return;
            }

            const response = await fetch(`/api/test_cases/${testCaseSelect.value}/export`);
            if (!response.ok) {
                throw new Error('Export failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${testCaseSelect.value}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showNotification('Test case exported successfully', 'success');
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification('Export failed: ' + error.message, 'error');
        }
    }

    async exportTestSuite() {
        try {
            const testSuiteSelect = document.getElementById('testSuiteSelect');
            if (!testSuiteSelect || !testSuiteSelect.value) {
                this.showNotification('Please select a test suite to export', 'error');
                return;
            }

            const response = await fetch(`/api/test_suites/${testSuiteSelect.value}/export`);
            if (!response.ok) {
                throw new Error('Export failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${testSuiteSelect.value}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showNotification('Test suite exported successfully', 'success');
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification('Export failed: ' + error.message, 'error');
        }
    }

    importTestCase() {
        const fileInput = document.getElementById('testCaseFileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    importTestSuite() {
        const fileInput = document.getElementById('testSuiteFileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    async handleTestCaseFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/test_cases/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.showNotification('Test case imported successfully', 'success');
                // Refresh the page or update the test case list
                if (typeof loadTestCases === 'function') {
                    loadTestCases();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification('Import failed: ' + error.message, 'error');
        } finally {
            // Clear the file input
            event.target.value = '';
        }
    }

    async handleTestSuiteFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/test_suites/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.showNotification('Test suite imported successfully', 'success');
                // Refresh the page or update the test suite list
                if (typeof loadTestSuites === 'function') {
                    loadTestSuites();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification('Import failed: ' + error.message, 'error');
        } finally {
            // Clear the file input
            event.target.value = '';
        }
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available, otherwise use alert
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.importExportManager = new ImportExportManager();
});
