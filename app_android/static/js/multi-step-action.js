// multi-step-action.js - Module for handling Multi Step actions

class MultiStepAction {
    constructor(appInstance) {
        this.app = appInstance;
        this.testCases = [];
        this.testCaseLookup = new Map();
        this.testCaseLookupNormalized = new Map();
        this.testCasesLoaded = false;
        this.testCasesLoadingPromise = null;
        this.init();
    }

    init() {
        // Initialize event listeners
        this.initEventListeners();

        // Load test cases for the dropdown
        this.loadTestCases();
    }

    initEventListeners() {
        // Add event listener for the refresh button
        const refreshButton = document.getElementById('refreshMultiStepTestCases');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.loadTestCases();
            });
        }

        // Add event listener for the test case dropdown
        const testCaseSelect = document.getElementById('multiStepTestCase');
        if (testCaseSelect) {
            testCaseSelect.addEventListener('change', () => {
                this.handleTestCaseSelection();
            });
        }
    }

    loadTestCases(dropdownId = 'multiStepTestCase') {
        // Show loading indicator
        const testCaseSelect = document.getElementById(dropdownId);
        if (testCaseSelect) {
            testCaseSelect.innerHTML = '<option value="">Loading test cases...</option>';
        }

        // Fetch test cases from the server
        this.testCasesLoaded = false;
        this.testCasesLoadingPromise = fetch('/api/test_cases_for_multi_step')
            .then(response => response.json())
            .then(data => {
                this.testCases = data.test_cases || [];
                this._rebuildTestCaseLookup();
                this.populateTestCaseDropdown(dropdownId);
            })
            .catch(error => {
                console.error('Error loading test cases:', error);
                this.app.logAction('error', 'Failed to load test cases for Multi Step action');

                // Reset dropdown with error message
                if (testCaseSelect) {
                    testCaseSelect.innerHTML = '<option value="">Error loading test cases</option>';
                }
            })
            .finally(() => {
                this.testCasesLoaded = true;
                this.testCasesLoadingPromise = null;
            });

        return this.testCasesLoadingPromise;
    }

    populateTestCaseDropdown(dropdownId = 'multiStepTestCase') {
        const testCaseSelect = document.getElementById(dropdownId);
        if (!testCaseSelect) return;

        // Clear the dropdown
        testCaseSelect.innerHTML = '<option value="">-- Select Test Case --</option>';

        // Add test cases to the dropdown
        this.testCases.forEach(testCase => {
            const option = document.createElement('option');
            const identifier = testCase.test_case_id || testCase.id || testCase.filename;
            option.value = identifier;
            option.textContent = `${testCase.name} (${testCase.steps_count} steps)`;
            option.dataset.name = testCase.name;
            option.dataset.stepsCount = testCase.steps_count;
            option.dataset.description = testCase.description || '';
            option.dataset.filename = testCase.filename || '';
            option.dataset.testCaseId = identifier;
            testCaseSelect.appendChild(option);
        });
    }

    _rebuildTestCaseLookup() {
        this.testCaseLookup = new Map();
        this.testCaseLookupNormalized = new Map();
        this.testCases.forEach(testCase => {
            const normalizedMeta = {
                id: testCase.id || testCase.test_case_id || testCase.filename,
                test_case_id: testCase.test_case_id || testCase.id || '',
                filename: testCase.filename || '',
                name: testCase.name || '',
                steps_count: testCase.steps_count || 0,
                description: testCase.description || '',
            };

            this._storeMeta(
                normalizedMeta,
                testCase.test_case_id,
                testCase.id,
                testCase.filename,
                testCase.file_path,
                testCase.name,
            );
        });
    }

    _rememberTestCaseMeta(meta, ...keys) {
        if (!meta) return;
        const normalizedMeta = {
            id: meta.test_case_id || meta.id || meta.filename || '',
            test_case_id: meta.test_case_id || meta.id || '',
            filename: meta.filename || '',
            name: meta.name || '',
            steps_count: meta.steps_count || meta.actions?.length || 0,
            description: meta.description || '',
        };

        this._storeMeta(
            normalizedMeta,
            ...keys,
            normalizedMeta.test_case_id,
            normalizedMeta.filename,
            normalizedMeta.id,
            normalizedMeta.name,
        );
    }

    handleTestCaseSelection() {
        const testCaseSelect = document.getElementById('multiStepTestCase');
        const testCaseInfo = document.getElementById('multiStepTestCaseInfo');
        const testCaseName = document.getElementById('multiStepTestCaseName');
        const testCaseSteps = document.getElementById('multiStepTestCaseSteps');
        const testCaseDescription = document.getElementById('multiStepTestCaseDescription');

        if (!testCaseSelect || !testCaseInfo || !testCaseName || !testCaseSteps || !testCaseDescription) return;

        const selectedOption = testCaseSelect.options[testCaseSelect.selectedIndex];

        if (selectedOption && selectedOption.value) {
            // Show the test case info
            testCaseInfo.classList.remove('d-none');

            // Set the test case details
            testCaseName.textContent = selectedOption.dataset.name || '';
            testCaseSteps.textContent = selectedOption.dataset.stepsCount || '0';
            testCaseDescription.textContent = selectedOption.dataset.description || 'No description available';

            // Pre-load the test case steps
            this.preLoadTestCaseSteps(selectedOption.value, selectedOption.dataset.filename || '');
        } else {
            // Hide the test case info
            testCaseInfo.classList.add('d-none');

            // Clear any stored steps
            delete testCaseSelect.dataset.loadedSteps;
        }
    }

    // Pre-load test case steps for better performance
    preLoadTestCaseSteps(testCaseId, fallbackFilename = null) {
        const testCaseSelect = document.getElementById('multiStepTestCase');
        if (!testCaseSelect) return;

        const selectedOption = testCaseSelect.options[testCaseSelect.selectedIndex];

        // Show loading indicator
        const loadingIndicator = document.getElementById('multiStepLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('d-none');
        }

        // Load the test case steps
        this.loadTestCaseSteps(testCaseId, fallbackFilename || selectedOption?.dataset?.filename || null)
            .then(result => {
                const actions = result.actions || [];
                // Store the steps and identifier in a data attribute for later use
                testCaseSelect.dataset.loadedSteps = JSON.stringify({
                    actions,
                    identifier: result.identifier,
                    filename: result.filename,
                    name: result.name || (selectedOption?.dataset?.name || ''),
                });
                if (result.identifier) {
                    testCaseSelect.dataset.loadedIdentifier = result.identifier;
                    if (selectedOption && selectedOption.value !== result.identifier) {
                        selectedOption.value = result.identifier;
                        selectedOption.dataset.testCaseId = result.identifier;
                        testCaseSelect.value = result.identifier;
                    }
                }
                if (selectedOption) {
                    selectedOption.dataset.stepsCount = String(actions.length);
                    if (result.filename) {
                        selectedOption.dataset.filename = result.filename;
                    }
                    if (result.identifier) {
                        selectedOption.dataset.testCaseId = result.identifier;
                    }
                }
                console.log(`Pre-loaded ${actions.length} steps for test case ${result.identifier || testCaseId}`);

                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.classList.add('d-none');
                }
            })
            .catch(error => {
                console.error('Error pre-loading test case steps:', error);

                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.classList.add('d-none');
                }
            });
    }

    // Method to load test case steps for execution
    async loadTestCaseSteps(testCaseId, fallbackFilename = null) {
        await this._ensureTestCasesLoaded();

        const identifiers = this._buildIdentifierCandidates(testCaseId, fallbackFilename);
        if (identifiers.length === 0) {
            throw new Error('No identifiers provided to load test case steps');
        }

        let lastError = null;

        for (const identifier of identifiers) {
            try {
                const response = await Promise.race([
                    fetch(`/api/test_cases/load/${encodeURIComponent(identifier)}`),
                    this._createTimeoutPromise(),
                ]);

                if (!response.ok) {
                    if (response.status === 404) {
                        throw new Error(`HTTP 404: ${identifier}`);
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.status === 'success' && data.test_case) {
                    const actions = data.test_case.actions || [];
                    const resolvedIdentifier = data.test_case.test_case_id || identifier;
                    const resolvedFilename = data.test_case.filename || fallbackFilename || identifier;

                    this._rememberTestCaseMeta({
                        id: resolvedIdentifier,
                        test_case_id: resolvedIdentifier,
                        filename: resolvedFilename,
                        name: data.test_case.name || '',
                        steps_count: actions.length,
                        description: data.test_case.description || '',
                    }, identifier);

                    return {
                        actions,
                        identifier: resolvedIdentifier,
                        filename: resolvedFilename,
                        name: data.test_case.name || '',
                    };
                }

                throw new Error(data.error || 'Failed to load test case steps');
            } catch (error) {
                lastError = error;
                const isNotFound = error && typeof error.message === 'string' && error.message.startsWith('HTTP 404');
                const isLastAttempt = identifier === identifiers[identifiers.length - 1];

                if (isNotFound && !isLastAttempt) {
                    console.warn(`Identifier ${identifier} not found, trying alternative identifier...`);
                } else {
                    console.error(`Error loading test case steps for ${identifier}:`, error);
                }
            }
        }

        throw lastError || new Error('Failed to load test case steps');
    }

    _buildIdentifierCandidates(primaryId, fallbackFilename) {
        const identifiers = [];
        const meta = this._lookupMeta(primaryId) || this._lookupMeta(fallbackFilename);

        if (primaryId) {
            identifiers.push(primaryId);
            if (primaryId.endsWith('.json')) {
                identifiers.push(primaryId.slice(0, -5));
            }
            identifiers.push(this._normalizeKey(primaryId));
            if (primaryId.includes('-') && !primaryId.includes('_')) {
                identifiers.push(primaryId.replace(/-/g, '_'));
            }
        }

        if (meta) {
            const canonicalPriority = [
                meta.test_case_id,
                meta.filename,
                meta.id,
                meta.name,
            ];

            canonicalPriority.forEach(candidate => {
                if (candidate && !identifiers.includes(candidate)) {
                    identifiers.unshift(candidate);
                }
            });

            const normalizedMetaKey = this._normalizeKey(meta.name || meta.filename);
            if (normalizedMetaKey && !identifiers.includes(normalizedMetaKey)) {
                identifiers.push(normalizedMetaKey);
            }
        }

        if (fallbackFilename && !identifiers.includes(fallbackFilename)) {
            identifiers.push(fallbackFilename);
            if (fallbackFilename.endsWith('.json')) {
                identifiers.push(fallbackFilename.slice(0, -5));
            }
            identifiers.push(this._normalizeKey(fallbackFilename));
            if (fallbackFilename.includes('-') && !fallbackFilename.includes('_')) {
                identifiers.push(fallbackFilename.replace(/-/g, '_'));
            }
        }

        const seen = new Set();
        return identifiers.filter(identifier => {
            if (!identifier) {
                return false;
            }
            if (seen.has(identifier)) {
                return false;
            }
            seen.add(identifier);
            return true;
        });
    }

    _createTimeoutPromise(duration = 30000, message = 'Request timeout after 30 seconds') {
        return new Promise((_, reject) => {
            setTimeout(() => reject(new Error(message)), duration);
        });
    }

    async _ensureTestCasesLoaded() {
        if (this.testCasesLoaded) {
            return;
        }
        if (this.testCasesLoadingPromise) {
            await this.testCasesLoadingPromise;
            return;
        }
        await this.loadTestCases();
    }

    _storeMeta(meta, ...keys) {
        keys
            .filter(Boolean)
            .forEach(key => {
                this.testCaseLookup.set(key, meta);
                const normalized = this._normalizeKey(key);
                if (normalized) {
                    this.testCaseLookupNormalized.set(normalized, meta);
                }
            });
    }

    _lookupMeta(identifier) {
        if (!identifier) {
            return null;
        }
        return (
            this.testCaseLookup.get(identifier) ||
            this.testCaseLookupNormalized.get(this._normalizeKey(identifier)) ||
            this._findMetaByTestCases(identifier)
        );
    }

    _findMetaByTestCases(identifier) {
        const normalized = this._normalizeKey(identifier);
        if (!normalized) {
            return null;
        }

        const match = this.testCases.find(testCase => {
            const candidates = [
                testCase.test_case_id,
                testCase.id,
                testCase.filename,
                testCase.file_path,
                testCase.name,
                this._normalizeKey(testCase.filename),
                this._normalizeKey(testCase.name),
            ].filter(Boolean);
            return candidates.some(candidate => this._normalizeKey(candidate) === normalized);
        });

        if (!match) {
            return null;
        }

        const meta = {
            id: match.id || match.test_case_id || match.filename,
            test_case_id: match.test_case_id || match.id || '',
            filename: match.filename || '',
            name: match.name || '',
            steps_count: match.steps_count || 0,
            description: match.description || '',
        };

        this._storeMeta(
            meta,
            match.test_case_id,
            match.id,
            match.filename,
            match.file_path,
            match.name,
        );

        return meta;
    }

    _normalizeKey(value) {
        if (!value || typeof value !== 'string') {
            return null;
        }

        return value
            .trim()
            .toLowerCase()
            .replace(/\.json$/i, '')
            .replace(/[^a-z0-9]+/g, '_')
            .replace(/^_+|_+$/g, '');
    }

    // Generate a unique alphanumeric ID for an action
    generateActionId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 10; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        return result;
    }

    // Method to create a Multi Step action
    createMultiStepAction(testCaseId, testCaseName, stepsCount, steps = null) {
        let meta = this._lookupMeta(testCaseId);

        const action = {
            type: 'multiStep',
            timestamp: Date.now(),
            test_case_id: meta?.test_case_id || testCaseId,
            test_case_name: testCaseName,
            test_case_steps_count: stepsCount,
            expanded: true, // Always start expanded for multi-step actions that execute other test case files
            action_id: this.generateActionId() // Add unique action ID for the multi-step action
        };

        if (meta?.filename) {
            action.test_case_filename = meta.filename;
        } else if (typeof testCaseId === 'string' && testCaseId.endsWith('.json')) {
            action.test_case_filename = testCaseId;
        }

        // Always include steps in the action - either provided steps or load them
        if (steps && Array.isArray(steps)) {
            // Preserve existing action IDs in the steps
            action.test_case_steps = steps;
            action.steps_loaded = true;
            action.test_case_steps_count = steps.length;
        } else if (steps && typeof steps === 'object') {
            const providedSteps = Array.isArray(steps.actions) ? steps.actions : [];
            action.test_case_steps = providedSteps;
            action.steps_loaded = providedSteps.length > 0;
            action.test_case_steps_count = providedSteps.length;
            if (steps.identifier) {
                action.test_case_id = steps.identifier;
            }
            if (steps.filename) {
                action.test_case_filename = steps.filename;
            }
        } else {
            // If steps are not provided, load them immediately
            this.loadTestCaseSteps(testCaseId, action.test_case_filename)
                .then(result => {
                    const loadedSteps = result?.actions || [];
                    // Preserve existing action IDs in the loaded steps
                    action.test_case_steps = loadedSteps;
                    action.steps_loaded = true;
                    action.test_case_steps_count = loadedSteps.length;
                    if (result?.identifier) {
                        action.test_case_id = result.identifier;
                    }
                    if (result?.filename) {
                        action.test_case_filename = result.filename;
                    }
                    console.log(`Loaded ${loadedSteps.length} steps for test case ${action.test_case_id}`);
                })
                .catch(error => {
                    console.error(`Error loading steps for test case ${testCaseId}:`, error);
                    // Set empty array to avoid null reference errors
                    action.test_case_steps = [];
                    action.steps_loaded = false;
                });
        }

        return action;
    }
}

// Export the class
window.MultiStepAction = MultiStepAction;
