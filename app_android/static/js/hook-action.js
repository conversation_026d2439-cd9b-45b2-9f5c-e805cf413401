// hook-action.js - Module for handling Hook Actions

class HookAction {
    constructor(appInstance) {
        this.app = appInstance;
        this.hookActionType = document.getElementById('hookActionType');
        this.hookActionFormContainer = document.getElementById('hookActionFormContainer');

        // Initialize event listeners
        this.initEventListeners();
    }

    initEventListeners() {
        // Listen for changes to the hook action type
        if (this.hookActionType) {
            this.hookActionType.addEventListener('change', () => this.handleHookActionTypeChange());
        }
    }

    handleHookActionTypeChange() {
        // Clear the form container
        if (this.hookActionFormContainer) {
            this.hookActionFormContainer.innerHTML = '';
        }

        const selectedType = this.hookActionType.value;
        if (!selectedType) return;

        // Add a header to indicate this is for a hook action
        const header = document.createElement('div');
        header.className = 'alert alert-secondary mb-3';
        header.innerHTML = `<i class="bi bi-tools"></i> Configure the ${selectedType} action to be executed when a step fails`;
        this.hookActionFormContainer.appendChild(header);

        // We only support tap action for hook actions
        if (selectedType === 'tap') {
            // Create a simplified form for tap action
            const formContent = document.createElement('div');

            // Create a method selector
            const methodGroup = document.createElement('div');
            methodGroup.className = 'form-group mb-3';
            methodGroup.innerHTML = `
                <label for="hookTapMethod">Tap Method</label>
                <select id="hookTapMethod" class="form-control">
                    <option value="coordinates">Coordinates</option>
                    <option value="image">Image</option>
                    <option value="locator">Locator</option>
                </select>
            `;
            formContent.appendChild(methodGroup);

            // Create coordinates section
            const coordsSection = document.createElement('div');
            coordsSection.id = 'hookTapCoordinatesSection';
            coordsSection.className = 'mt-3';
            coordsSection.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="form-group">
                            <label for="hookTapX">X Coordinate</label>
                            <input type="number" id="hookTapX" class="form-control" value="0" min="0">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="hookTapY">Y Coordinate</label>
                            <input type="number" id="hookTapY" class="form-control" value="0" min="0">
                        </div>
                    </div>
                </div>
                <button class="btn btn-outline-primary btn-sm mt-2" id="hookPickTapCoordinates">
                    <i class="bi bi-cursor"></i> Pick from Screen
                </button>
            `;
            formContent.appendChild(coordsSection);

            // Create image section (initially hidden)
            const imageSection = document.createElement('div');
            imageSection.id = 'hookTapImageSection';
            imageSection.className = 'mt-3';
            imageSection.style.display = 'none';
            imageSection.innerHTML = `
                <div class="form-group">
                    <label for="hookTapImageFilename">Reference Image:</label>
                    <div class="input-group mb-2">
                        <select class="form-select" id="hookTapImageFilename">
                            <option value="" selected disabled>Select an image...</option>
                        </select>
                        <button class="btn btn-outline-secondary" type="button" id="hookRefreshTapImages">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label for="hookTapThreshold">Similarity Threshold:</label>
                            <input type="number" class="form-control mb-2" id="hookTapThreshold" value="0.7" min="0" max="1" step="0.05">
                        </div>
                        <div class="col-6">
                            <label for="hookTapTimeout">Timeout (seconds):</label>
                            <input type="number" class="form-control mb-2" id="hookTapTimeout" value="20" min="1">
                        </div>
                    </div>
                </div>
            `;
            formContent.appendChild(imageSection);

            // Create locator section (initially hidden)
            const locatorSection = document.createElement('div');
            locatorSection.id = 'hookTapLocatorSection';
            locatorSection.className = 'mt-3';
            locatorSection.style.display = 'none';
            locatorSection.innerHTML = `
                <div class="form-group mb-3">
                    <label for="hookTapLocatorType">Locator Type</label>
                    <select id="hookTapLocatorType" class="form-control">
                        <option value="id">ID</option>
                        <option value="xpath">XPath</option>
                        <option value="accessibility_id">Accessibility ID</option>
                        <option value="text">Text</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="hookTapLocatorValue">Locator Value</label>
                    <input type="text" id="hookTapLocatorValue" class="form-control" placeholder="Enter locator value">
                </div>
                <div class="form-group mb-3">
                    <label for="hookTapLocatorTimeout">Timeout (seconds)</label>
                    <input type="number" id="hookTapLocatorTimeout" class="form-control" value="10" min="1" step="1">
                </div>
                <div class="form-group mb-3">
                    <label for="hookTapLocatorInterval">Interval (seconds)</label>
                    <input type="number" id="hookTapLocatorInterval" class="form-control" value="0.5" min="0.1" step="0.1">
                </div>
            `;
            formContent.appendChild(locatorSection);

            // Add the form content to the container
            this.hookActionFormContainer.appendChild(formContent);

            // Add event listener for method selector
            const methodSelector = document.getElementById('hookTapMethod');
            if (methodSelector) {
                methodSelector.addEventListener('change', () => {
                    const method = methodSelector.value;

                    // Hide all sections
                    document.getElementById('hookTapCoordinatesSection').style.display = 'none';
                    document.getElementById('hookTapImageSection').style.display = 'none';
                    document.getElementById('hookTapLocatorSection').style.display = 'none';

                    // Show the selected section
                    if (method === 'coordinates') {
                        document.getElementById('hookTapCoordinatesSection').style.display = 'block';
                    } else if (method === 'image') {
                        document.getElementById('hookTapImageSection').style.display = 'block';
                    } else if (method === 'locator') {
                        document.getElementById('hookTapLocatorSection').style.display = 'block';
                    }
                });
            }

            // Add event listener for pick coordinates button
            const pickCoordsBtn = document.getElementById('hookPickTapCoordinates');
            if (pickCoordsBtn && this.app && typeof this.app.setupCoordinatePicker === 'function') {
                pickCoordsBtn.addEventListener('click', () => {
                    this.app.setupCoordinatePicker('hookTapX', 'hookTapY');
                });
            }

            // Load reference images for the image section
            if (this.app && typeof this.app.loadReferenceImages === 'function') {
                this.app.loadReferenceImages('tap', 'hookTapImageFilename');

                // Add event listener for the refresh button
                const refreshBtn = document.getElementById('hookRefreshTapImages');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', () => {
                        this.app.loadReferenceImages('tap', 'hookTapImageFilename');
                    });
                }
            }
        } else {
            // If the action type is not supported, show an error
            this.hookActionFormContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Only tap action is supported for hook actions.
                </div>
            `;
        }
    }

    // Helper method to update all IDs in a cloned form
    updateClonedFormIds(formElement, prefix) {
        if (!formElement) return;

        // Update IDs of all input elements, selects, and buttons
        const elements = formElement.querySelectorAll('[id]');
        elements.forEach(el => {
            // Skip elements that already have the prefix
            if (!el.id.startsWith(prefix)) {
                el.id = prefix + el.id.charAt(0).toUpperCase() + el.id.slice(1);
            }
        });

        // Update for attributes in labels
        const labels = formElement.querySelectorAll('label[for]');
        labels.forEach(label => {
            const forAttr = label.getAttribute('for');
            if (forAttr && !forAttr.startsWith(prefix)) {
                label.setAttribute('for', prefix + forAttr.charAt(0).toUpperCase() + forAttr.slice(1));
            }
        });
    }

    getHookActionData() {
        const selectedType = this.hookActionType.value;
        if (!selectedType) return null;

        // Create a basic hook action object
        const hookAction = {
            type: 'hookAction',
            hook_type: selectedType,
            hook_data: {}
        };

        // Get the data for the selected action type
        switch (selectedType) {
            case 'tapIfImageExists':
                hookAction.hook_data.image_filename = document.getElementById('hookTapIfImageExistsFilename')?.value || '';
                hookAction.hook_data.threshold = parseFloat(document.getElementById('hookTapIfImageExistsThreshold')?.value) || 0.7;
                hookAction.hook_data.timeout = parseInt(document.getElementById('hookTapIfImageExistsTimeout')?.value) || 5;
                break;
            case 'tap':
                // Get the selected method from the dropdown
                const methodSelector = document.getElementById('hookTapMethod');
                if (!methodSelector) {
                    console.error('Hook tap method selector not found');
                    return null;
                }

                const method = methodSelector.value;
                hookAction.hook_data.method = method;

                // Get data based on the selected method
                if (method === 'locator') {
                    // Using locator
                    hookAction.hook_data.locator_type = document.getElementById('hookTapLocatorType')?.value;
                    hookAction.hook_data.locator_value = document.getElementById('hookTapLocatorValue')?.value;
                    hookAction.hook_data.timeout = parseInt(document.getElementById('hookTapLocatorTimeout')?.value) || 10;
                    hookAction.hook_data.interval = parseFloat(document.getElementById('hookTapLocatorInterval')?.value) || 0.5;

                    // Add detailed log information
                    hookAction.hook_data.log_details = `Locator: ${hookAction.hook_data.locator_type}="${hookAction.hook_data.locator_value}"`;
                } else if (method === 'image') {
                    // Using image
                    hookAction.hook_data.image_filename = document.getElementById('hookTapImageFilename')?.value;
                    hookAction.hook_data.threshold = parseFloat(document.getElementById('hookTapThreshold')?.value) || 0.7;
                    hookAction.hook_data.timeout = parseInt(document.getElementById('hookTapTimeout')?.value) || 20;

                    // Add detailed log information
                    hookAction.hook_data.log_details = `Image: ${hookAction.hook_data.image_filename}`;
                } else {
                    // Using coordinates (default)
                    hookAction.hook_data.x = parseInt(document.getElementById('hookTapX')?.value) || 0;
                    hookAction.hook_data.y = parseInt(document.getElementById('hookTapY')?.value) || 0;

                    // Add detailed log information
                    hookAction.hook_data.log_details = `Coordinates: (${hookAction.hook_data.x}, ${hookAction.hook_data.y})`;
                }
                break;

            case 'wait':
                const waitTimeInput = document.getElementById('hookWaitTime');
                if (waitTimeInput) {
                    const waitTime = parseInt(waitTimeInput.value);
                    if (!isNaN(waitTime) && waitTime > 0) {
                        hookAction.hook_data.duration = waitTime;
                    } else {
                        hookAction.hook_data.duration = 5; // Default to 5 seconds if invalid
                    }
                } else {
                    hookAction.hook_data.duration = 5; // Default to 5 seconds if input not found
                }
                break;

            case 'text':
                hookAction.hook_data.text = document.getElementById('hookInputText')?.value || '';
                break;

            case 'hideKeyboard':
                // No additional data needed
                break;

            case 'deviceBack':
                // No additional data needed
                break;

            case 'launchApp':
                hookAction.hook_data.package_id = document.getElementById('hookAppPackage')?.value || '';
                break;

            case 'restartApp':
                hookAction.hook_data.package_id = document.getElementById('hookRestartPackage')?.value || '';
                break;

            case 'terminateApp':
                hookAction.hook_data.package_id = document.getElementById('hookTerminatePackage')?.value || '';
                break;

            case 'iosFunctions':
                // Get the iOS function select element
                const iosFunctionSelect = document.getElementById('hookIosFunction');
                if (!iosFunctionSelect) {
                    console.error('iOS Function select element not found');
                    return null;
                }

                hookAction.hook_data.function_name = iosFunctionSelect.value;

                // Add function-specific parameters
                if (hookAction.hook_data.function_name === 'press') {
                    const pressKeyInput = document.getElementById('hookIosPressKey');
                    if (pressKeyInput) {
                        hookAction.hook_data.key = pressKeyInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'alert_click') {
                    const buttonTextInput = document.getElementById('hookIosAlertButtonText');
                    if (buttonTextInput) {
                        hookAction.hook_data.button_text = buttonTextInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'alert_wait') {
                    const timeoutInput = document.getElementById('hookIosAlertTimeout');
                    if (timeoutInput) {
                        hookAction.hook_data.timeout = parseInt(timeoutInput.value) || 2;
                    }
                } else if (hookAction.hook_data.function_name === 'set_clipboard') {
                    const contentInput = document.getElementById('hookIosClipboardContent');
                    if (contentInput) {
                        hookAction.hook_data.content = contentInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'text') {
                    const textInput = document.getElementById('hookIosTextContent');
                    const enterCheckbox = document.getElementById('hookIosTextEnter');
                    if (textInput) {
                        hookAction.hook_data.text = textInput.value;
                    }
                    if (enterCheckbox) {
                        hookAction.hook_data.enter = enterCheckbox.checked;
                    }
                } else if (hookAction.hook_data.function_name === 'paste') {
                    // No additional parameters needed for paste
                }
                break;

            case 'androidFunctions':
                // Get the Android function select element
                const androidFunctionSelect = document.getElementById('hookAndroidFunction');
                if (!androidFunctionSelect) {
                    console.error('Android Function select element not found');
                    return null;
                }

                hookAction.hook_data.function_name = androidFunctionSelect.value;

                // Add function-specific parameters
                if (hookAction.hook_data.function_name === 'send_key_event') {
                    const keyEventInput = document.getElementById('hookAndroidKeyEvent');
                    if (keyEventInput) {
                        hookAction.hook_data.key_event = keyEventInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'switch_context') {
                    const contextInput = document.getElementById('hookAndroidContext');
                    if (contextInput) {
                        hookAction.hook_data.context = contextInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'clear_app') {
                    const packageNameInput = document.getElementById('hookAndroidPackageName');
                    if (packageNameInput) {
                        hookAction.hook_data.package_name = packageNameInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'set_connectivity') {
                    const connectivityTypeInput = document.getElementById('hookAndroidConnectivityType');
                    const connectivityStateInput = document.getElementById('hookAndroidConnectivityState');
                    if (connectivityTypeInput) {
                        hookAction.hook_data.connectivity_type = connectivityTypeInput.value;
                    }
                    if (connectivityStateInput) {
                        hookAction.hook_data.state = connectivityStateInput.value === 'true';
                    }
                } else if (hookAction.hook_data.function_name === 'set_clipboard') {
                    const contentInput = document.getElementById('hookAndroidClipboardContent');
                    if (contentInput) {
                        hookAction.hook_data.content = contentInput.value;
                    }
                } else if (hookAction.hook_data.function_name === 'inject_camera_image') {
                    const imagePathInput = document.getElementById('hookAndroidImagePath');
                    if (imagePathInput) {
                        hookAction.hook_data.image_path = imagePathInput.value;
                    }
                }
                break;

            // Add more cases for other action types as needed
        }

        return hookAction;
    }

    validateHookAction() {
        const selectedType = this.hookActionType.value;
        if (!selectedType) {
            this.app.logAction('error', 'Please select a hook action type');
            return false;
        }

        // Validate based on the selected action type
        switch (selectedType) {
            case 'tapIfImageExists':
                const imageFilename = document.getElementById('hookTapIfImageExistsFilename')?.value;
                if (!imageFilename) {
                    this.app.logAction('error', 'Reference image is required for Tap If Image Exists hook action');
                    return false;
                }
                break;
            case 'tap':
                // Get the selected method from the dropdown
                const methodSelector = document.getElementById('hookTapMethod');
                if (!methodSelector) {
                    this.app.logAction('error', 'Hook tap method selector not found');
                    return false;
                }

                const method = methodSelector.value;

                // Validate based on the selected method
                if (method === 'locator') {
                    // Validate locator
                    const locatorType = document.getElementById('hookTapLocatorType')?.value;
                    const locatorValue = document.getElementById('hookTapLocatorValue')?.value;
                    if (!locatorType || !locatorValue) {
                        this.app.logAction('error', 'Locator type and value are required for Tap hook action');
                        return false;
                    }
                } else if (method === 'image') {
                    // Validate image
                    const imageFilename = document.getElementById('hookTapImageFilename')?.value;
                    if (!imageFilename) {
                        this.app.logAction('error', 'Image filename is required for Tap hook action');
                        return false;
                    }
                }
                // No validation needed for coordinates - default values are fine
                break;

            case 'wait':
                // Get the wait time input element
                const waitTimeInput = document.getElementById('hookWaitTime');

                // If the hookWaitTime input doesn't exist, try to find the waitTime input
                if (!waitTimeInput) {
                    const waitTimeForm = document.querySelector('#waitActionForm');
                    if (waitTimeForm) {
                        // Find the wait time input in the form
                        const originalWaitTimeInput = waitTimeForm.querySelector('#waitTime');
                        if (originalWaitTimeInput) {
                            // Create a new input with the hook-specific ID
                            const hookWaitTimeInput = document.createElement('input');
                            hookWaitTimeInput.id = 'hookWaitTime';
                            hookWaitTimeInput.type = 'number';
                            hookWaitTimeInput.className = 'form-control';
                            hookWaitTimeInput.value = '5'; // Default value

                            // Add the new input to the hook action form container
                            const hookActionFormContainer = document.getElementById('hookActionFormContainer');
                            if (hookActionFormContainer) {
                                // Create a form group for the wait time input
                                const formGroup = document.createElement('div');
                                formGroup.className = 'form-group mb-3';

                                // Create a label for the wait time input
                                const label = document.createElement('label');
                                label.htmlFor = 'hookWaitTime';
                                label.textContent = 'Wait time (seconds)';

                                // Add the label and input to the form group
                                formGroup.appendChild(label);
                                formGroup.appendChild(hookWaitTimeInput);

                                // Add the form group to the hook action form container
                                hookActionFormContainer.appendChild(formGroup);

                                // Validate the wait time
                                const waitTime = parseInt(hookWaitTimeInput.value);
                                if (isNaN(waitTime) || waitTime <= 0) {
                                    this.app.logAction('error', 'Wait time must be a positive number');
                                    return false;
                                }
                            } else {
                                this.app.logAction('error', 'Hook action form container not found');
                                return false;
                            }
                        } else {
                            this.app.logAction('error', 'Wait time input not found in wait action form');
                            return false;
                        }
                    } else {
                        // If we can't find the wait action form, create a default wait time input
                        const hookWaitTimeInput = document.createElement('input');
                        hookWaitTimeInput.id = 'hookWaitTime';
                        hookWaitTimeInput.type = 'number';
                        hookWaitTimeInput.className = 'form-control';
                        hookWaitTimeInput.value = '5'; // Default value

                        // Add the new input to the hook action form container
                        const hookActionFormContainer = document.getElementById('hookActionFormContainer');
                        if (hookActionFormContainer) {
                            // Create a form group for the wait time input
                            const formGroup = document.createElement('div');
                            formGroup.className = 'form-group mb-3';

                            // Create a label for the wait time input
                            const label = document.createElement('label');
                            label.htmlFor = 'hookWaitTime';
                            label.textContent = 'Wait time (seconds)';

                            // Add the label and input to the form group
                            formGroup.appendChild(label);
                            formGroup.appendChild(hookWaitTimeInput);

                            // Add the form group to the hook action form container
                            hookActionFormContainer.appendChild(formGroup);
                        } else {
                            this.app.logAction('error', 'Hook action form container not found');
                            return false;
                        }
                    }
                } else {
                    // Use the value from the existing input
                    const waitTime = parseInt(waitTimeInput.value);
                    if (isNaN(waitTime) || waitTime <= 0) {
                        this.app.logAction('error', 'Wait time must be a positive number');
                        return false;
                    }
                }
                break;

            case 'text':
                const text = document.getElementById('hookInputText')?.value;
                if (!text) {
                    this.app.logAction('error', 'Text is required for Text hook action');
                    return false;
                }
                break;

            case 'launchApp':
            case 'restartApp':
            case 'terminateApp':
                const packageId = document.getElementById(`hook${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)}Package`)?.value;
                if (!packageId) {
                    this.app.logAction('error', `Package ID is required for ${selectedType} hook action`);
                    return false;
                }
                break;

            case 'iosFunctions':
                // Get the iOS function select element
                const iosFunctionSelect = document.getElementById('hookIosFunction');
                if (!iosFunctionSelect) {
                    this.app.logAction('error', 'iOS Function select element not found');
                    return false;
                }

                const functionName = iosFunctionSelect.value;
                if (!functionName) {
                    this.app.logAction('error', 'Function name is required for iOS Functions hook action');
                    return false;
                }

                // Validate function-specific parameters
                if (functionName === 'press') {
                    const pressKeyInput = document.getElementById('hookIosPressKey');
                    if (!pressKeyInput || !pressKeyInput.value) {
                        this.app.logAction('error', 'Key is required for iOS press function');
                        return false;
                    }
                } else if (functionName === 'alert_click') {
                    const buttonTextInput = document.getElementById('hookIosAlertButtonText');
                    if (!buttonTextInput || !buttonTextInput.value) {
                        this.app.logAction('error', 'Button text is required for iOS alert_click function');
                        return false;
                    }
                } else if (functionName === 'set_clipboard') {
                    const contentInput = document.getElementById('hookIosClipboardContent');
                    if (!contentInput || !contentInput.value) {
                        this.app.logAction('error', 'Content is required for iOS set_clipboard function');
                        return false;
                    }
                } else if (functionName === 'text') {
                    const textInput = document.getElementById('hookIosTextContent');
                    if (!textInput || !textInput.value) {
                        this.app.logAction('error', 'Text content is required for iOS text function');
                        return false;
                    }
                } else if (functionName === 'paste' || functionName === 'alert_wait') {
                    // These functions don't require additional validation
                } else {
                    // For any other iOS function, just make sure we have a function name
                    console.log(`Validating iOS function: ${functionName}`);
                }
                break;

            case 'androidFunctions':
                // Get the Android function select element
                const androidFunctionSelect = document.getElementById('hookAndroidFunction');
                if (!androidFunctionSelect) {
                    this.app.logAction('error', 'Android Function select element not found');
                    return false;
                }

                const androidFunctionName = androidFunctionSelect.value;
                if (!androidFunctionName) {
                    this.app.logAction('error', 'Function name is required for Android Functions hook action');
                    return false;
                }

                // Validate function-specific parameters
                if (androidFunctionName === 'send_key_event') {
                    const keyEventInput = document.getElementById('hookAndroidKeyEvent');
                    if (!keyEventInput || !keyEventInput.value) {
                        this.app.logAction('error', 'Key event is required for Android send_key_event function');
                        return false;
                    }
                } else if (androidFunctionName === 'switch_context') {
                    const contextInput = document.getElementById('hookAndroidContext');
                    if (!contextInput || !contextInput.value) {
                        this.app.logAction('error', 'Context is required for Android switch_context function');
                        return false;
                    }
                } else if (androidFunctionName === 'set_connectivity') {
                    const connectivityTypeInput = document.getElementById('hookAndroidConnectivityType');
                    const connectivityStateInput = document.getElementById('hookAndroidConnectivityState');
                    if (!connectivityTypeInput || !connectivityTypeInput.value) {
                        this.app.logAction('error', 'Connectivity type is required for Android set_connectivity function');
                        return false;
                    }
                    if (!connectivityStateInput || connectivityStateInput.value === '') {
                        this.app.logAction('error', 'Connectivity state is required for Android set_connectivity function');
                        return false;
                    }
                } else if (androidFunctionName === 'set_clipboard') {
                    const contentInput = document.getElementById('hookAndroidClipboardContent');
                    if (!contentInput || !contentInput.value) {
                        this.app.logAction('error', 'Content is required for Android set_clipboard function');
                        return false;
                    }
                } else if (androidFunctionName === 'inject_camera_image') {
                    const imagePathInput = document.getElementById('hookAndroidImagePath');
                    if (!imagePathInput || !imagePathInput.value) {
                        this.app.logAction('error', 'Image path is required for Android inject_camera_image function');
                        return false;
                    }
                } else if (['accept_alert', 'dismiss_alert', 'get_clipboard', 'clear_app'].includes(androidFunctionName)) {
                    // These functions don't require additional validation
                } else {
                    // For any other Android function, just make sure we have a function name
                    console.log(`Validating Android function: ${androidFunctionName}`);
                }
                break;
        }

        return true;
    }
}

// Make the class available globally
window.HookAction = HookAction;
