// Add this status manager to handle execution status updates
const ExecutionStatusManager = {
  panel: null,
  progressBar: null,
  currentLabel: null,
  progressText: null,
  logContainer: null,
  totalActions: 0,
  currentAction: 0,
  isRunning: false,

  initialize() {
    this.panel = document.getElementById('execution-status-panel');
    this.progressBar = document.getElementById('execution-progress-bar');
    this.currentLabel = document.getElementById('current-action-label');
    this.progressText = document.getElementById('progress-text');
    this.logContainer = document.getElementById('execution-log');

    // Set up minimize button
    document.getElementById('minimize-status-panel').addEventListener('click', () => {
      this.panel.classList.toggle('minimized');
    });
  },

  start(totalActions) {
    this.totalActions = totalActions;
    this.currentAction = 0;
    this.isRunning = true;
    this.panel.style.display = 'block';
    this.panel.classList.remove('minimized');
    this.progressBar.style.width = '0%';
    this.currentLabel.textContent = 'Starting execution...';
    this.progressText.textContent = `0/${totalActions}`;
    this.logContainer.innerHTML = '';
    this.addLogEntry('Execution started', 'info');
  },

  update(actionIndex, status, message) {
    if (!this.isRunning) return;

    this.currentAction = actionIndex + 1;
    const percent = Math.round((this.currentAction / this.totalActions) * 100);

    this.progressBar.style.width = `${percent}%`;
    this.currentLabel.textContent = message || `Executing action ${this.currentAction}`;
    this.progressText.textContent = `${this.currentAction}/${this.totalActions}`;

    this.addLogEntry(`Action ${actionIndex + 1}: ${message || status}`, status);

    // Auto-scroll log to bottom
    this.logContainer.scrollTop = this.logContainer.scrollHeight;
  },

  complete(success, message) {
    this.isRunning = false;
    this.progressBar.style.width = '100%';
    this.currentLabel.textContent = success ? 'Execution completed' : 'Execution failed';
    this.addLogEntry(message || (success ? 'All actions completed successfully' : 'Execution failed'),
                     success ? 'success' : 'error');

    // Keep panel visible for 10 seconds after completion
    setTimeout(() => {
      if (!this.isRunning) {
        this.panel.classList.add('minimized');
      }
    }, 10000);
  },

  addLogEntry(message, type) {
    const entry = document.createElement('div');
    entry.className = `log-entry ${type || 'info'}`;
    entry.innerHTML = `<span class="log-time">[${new Date().toLocaleTimeString()}]</span> ${message}`;
    this.logContainer.appendChild(entry);
  }
};

/**
 * Enhance tab navigation with modern interactions
 */
function enhanceTabNavigation() {
  // Get all tab navigation elements
  const tabNavigations = document.querySelectorAll('.nav-tabs');

  tabNavigations.forEach(navTabs => {
    const tabId = navTabs.id;
    const tabButtons = navTabs.querySelectorAll('.nav-link');

    // Create a function to update the sliding indicator
    const updateSliderPosition = (activeTab) => {
      // Get position of active tab
      const rect = activeTab.getBoundingClientRect();
      const navRect = navTabs.getBoundingClientRect();

      // Calculate positions relative to the nav container
      const left = rect.left - navRect.left;
      const width = rect.width;

      // Style the indicator
      navTabs.style.setProperty('--slider-left', `${left}px`);
      navTabs.style.setProperty('--slider-width', `${width}px`);

      // Adding a custom style element for this specific nav
      if (!document.getElementById(`slider-style-${tabId}`)) {
        const style = document.createElement('style');
        style.id = `slider-style-${tabId}`;
        style.innerHTML = `
          #${tabId}::after {
            transform: translateX(var(--slider-left, 0));
            width: var(--slider-width, 0);
          }
        `;
        document.head.appendChild(style);
      }
    };

    // Add hover effects and active indicator
    tabButtons.forEach(button => {
      // Add ripple effect on click
      button.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();

        ripple.className = 'tab-ripple';
        ripple.style.left = `${e.clientX - rect.left}px`;
        ripple.style.top = `${e.clientY - rect.top}px`;

        button.appendChild(ripple);

        // Remove ripple after animation completes
        setTimeout(() => {
          ripple.remove();
        }, 600);

        // Update sliding indicator position
        updateSliderPosition(button);

        // Track current active tab in localStorage for persistence
        if (tabId) {
          localStorage.setItem(`last-active-tab-${tabId}`, button.id);
        }
      });
    });

    // Restore last active tab on page load
    if (tabId) {
      const lastActiveTabId = localStorage.getItem(`last-active-tab-${tabId}`);
      if (lastActiveTabId) {
        const tabToActivate = document.getElementById(lastActiveTabId);
        if (tabToActivate) {
          // Use Bootstrap's tab API to activate the tab
          const tabInstance = new bootstrap.Tab(tabToActivate);
          tabInstance.show();

          // Update slider position after tab is shown
          setTimeout(() => {
            updateSliderPosition(tabToActivate);
          }, 150);
        }
      } else {
        // Find the active tab and position the indicator
        const activeTab = navTabs.querySelector('.nav-link.active');
        if (activeTab) {
          setTimeout(() => {
            updateSliderPosition(activeTab);
          }, 150);
        }
      }
    }

    // Handle window resize to reposition the indicator
    window.addEventListener('resize', () => {
      const activeTab = navTabs.querySelector('.nav-link.active');
      if (activeTab) {
        updateSliderPosition(activeTab);
      }
    });
  });

  // Add CSS for ripple effect if not already present
  if (!document.getElementById('tab-ripple-style')) {
    const style = document.createElement('style');
    style.id = 'tab-ripple-style';
    style.innerHTML = `
      .tab-ripple {
        position: absolute;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: tab-ripple 0.6s linear;
        pointer-events: none;
      }

      @keyframes tab-ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }

      .nav-link {
        position: relative;
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
  }

  console.log('Tab navigation enhanced with modern interactions');
}

// Initialize event handlers when document is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('App.js loaded, initializing app components...');

  // Initialize execution status manager
  ExecutionStatusManager.initialize();

  // Enhance tab navigation
  enhanceTabNavigation();

  // Only set up socket handlers if socket exists
  if (typeof socket !== 'undefined' && socket) {
    // Update the socket.io handler for action results
    socket.on('action_result', function(data) {
      // Only update the UI, don't modify row classes
      if (data.status === 'started') {
        ExecutionStatusManager.start(data.total_actions || 0);
      }
      else if (data.status === 'running') {
        ExecutionStatusManager.update(
          data.action_index || 0,
          'info',
          data.message || `Executing action ${data.action_index + 1}`
        );
        // Log action status for running actions
        if (data.action_id && window.mainApp && window.mainApp.logActionStatus) {
          window.mainApp.logActionStatus(data.action_id, 'running');
        }
      }
      else if (data.status === 'success') {
        if (data.completed) {
          ExecutionStatusManager.complete(true, data.message);
        } else {
          // Special handling for hook recovery success
          if (data.hook_recovery_success) {
            // Find the action item and explicitly update its classes
            const actionItem = document.querySelector(`.action-item[data-action-index="${data.action_index}"]`);
            if (actionItem) {
              // First remove error classes
              actionItem.classList.remove('error', 'action-item-failed', 'action-item-recovery', 'action-item-retrying');
              // Then add success class
              actionItem.classList.add('success');
              console.log(`Hook recovery success: Updated classes for action ${data.action_index}`);
            }
          }

          // Store screenshot_filename in the action data if available
          if (data.screenshot_filename) {
            console.log(`Received screenshot_filename: ${data.screenshot_filename} for action ${data.action_index}`);
            // Find the action item
            const actionItem = document.querySelector(`.action-item[data-action-index="${data.action_index}"]`);
            if (actionItem) {
              // Store the screenshot_filename as a data attribute
              actionItem.setAttribute('data-screenshot-filename', data.screenshot_filename);
              console.log(`Stored screenshot_filename for action ${data.action_index}`);
            }

            // Also store in the current actions array if it exists
            if (window.app && window.app.currentActions && window.app.currentActions[data.action_index]) {
              window.app.currentActions[data.action_index].screenshot_filename = data.screenshot_filename;
              console.log(`Stored screenshot_filename in currentActions[${data.action_index}]`);
            }
          }

          ExecutionStatusManager.update(
            data.action_index || 0,
            'success',
            data.message || 'Action completed successfully'
          );
          // Log action status for successful actions
          if (data.action_id && window.mainApp && window.mainApp.logActionStatus) {
            window.mainApp.logActionStatus(data.action_id, 'pass');
          }
        }
      }
      else if (data.status === 'error' || data.status === 'failed') {
        if (data.completed) {
          ExecutionStatusManager.complete(false, data.message);
        } else {
          ExecutionStatusManager.update(
            data.action_index || 0,
            'error',
            data.message || 'Action failed'
          );
          // Log action status for failed actions
          if (data.action_id && window.mainApp && window.mainApp.logActionStatus) {
            window.mainApp.logActionStatus(data.action_id, 'fail');
          }

          // Handle cleanup execution when action fails
          handleActionFailureCleanup(data);
        }
      }
      else if (data.status === 'stopped') {
        ExecutionStatusManager.complete(false, data.message || 'Execution stopped');
      }

      // Always refresh the screenshot if available
      if (data.screenshot) {
        refreshScreenshot();
      }
    });

    // Handle pong response
    socket.on('pong', function(data) {
      // Connection is alive
      if (SocketHealthMonitor.connectionLost) {
        console.log('Connection restored');
        SocketHealthMonitor.connectionLost = false;
      }
    });
  } else {
    console.error('Socket not available in app.js');
  }

  // Initialize socket listeners if socket exists
  if (typeof socket !== 'undefined') {
    setupAppSocketListeners(socket);
  } else {
    console.error('Socket.IO connection not available');
  }

  // Initialize execution manager
  if (typeof window.initExecutionManager === 'function') {
    window.initExecutionManager(socket);
  }

  // Setup device screen functionality
  setupDeviceScreen();

  console.log('App initialization complete');
});

/**
 * Set up Socket.IO listeners specific to this app module
 */
function setupAppSocketListeners(socket) {
  // Add app-specific socket event listeners here

  // Listen for cleanup step progress updates (SocketIO)
  socket.on('cleanup_step_result', function(data) {
    console.log('Cleanup step result:', data);

    // Show cleanup progress in the UI
    showCleanupStepProgress(data);
  });

  // Listen for multiStep cleanup action status updates (SocketIO)
  socket.on('multistep_cleanup_status', function(data) {
    console.log('MultiStep cleanup status:', data);

    // Update the multiStep cleanup action row status
    updateMultiStepCleanupActionStatus(data);
  });

  // Fallback: Poll for cleanup status if SocketIO is not available
  if (!socket || !socket.connected) {
    console.log('SocketIO not available, using polling for cleanup status');
    startCleanupStatusPolling();
  }

  // Example: Listen for server notifications
  socket.on('server_notification', function(data) {
    if (typeof showToast === 'function') {
      showToast('Server', data.message, data.type || 'info');
    } else {
      console.log('Server notification:', data.message);
    }
  });

  console.log('App socket listeners initialized');
}

/**
 * Show cleanup step progress in the UI
 */
function showCleanupStepProgress(data) {
  console.log('Showing cleanup step progress:', data);

  // Create or update the cleanup progress modal
  let cleanupModal = document.getElementById('cleanup-progress-modal');
  if (!cleanupModal) {
    cleanupModal = createCleanupProgressModal();
  }

  // Show the modal
  const modal = new bootstrap.Modal(cleanupModal);
  modal.show();

  // Update the progress
  updateCleanupProgress(data);

  // If all steps are complete, auto-hide after a delay
  if (data.status === 'complete') {
    setTimeout(() => {
      modal.hide();
    }, 2000);
  }
}

/**
 * Create the cleanup progress modal
 */
function createCleanupProgressModal() {
  const modalHtml = `
    <div class="modal fade" id="cleanup-progress-modal" tabindex="-1" aria-labelledby="cleanup-progress-title" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-warning text-dark">
            <h5 class="modal-title" id="cleanup-progress-title">
              <i class="fas fa-broom me-2"></i>Executing Cleanup Steps
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="cleanup-progress-container">
              <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning"
                     role="progressbar" style="width: 0%" id="cleanup-progress-bar">
                  <span id="cleanup-progress-text">0%</span>
                </div>
              </div>
              <div class="cleanup-steps-list" id="cleanup-steps-list">
                <!-- Steps will be populated here -->
              </div>
              <div class="cleanup-status-message mt-3" id="cleanup-status-message">
                <div class="alert alert-info">
                  <i class="fas fa-info-circle me-2"></i>
                  Cleanup steps are running to restore the app to a clean state...
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <small class="text-muted">
              <i class="fas fa-shield-alt me-1"></i>
              Cleanup steps ensure your app is in a consistent state for the next test
            </small>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add the modal to the document
  document.body.insertAdjacentHTML('beforeend', modalHtml);
  return document.getElementById('cleanup-progress-modal');
}

/**
 * Start polling for cleanup status when SocketIO is not available
 */
function startCleanupStatusPolling() {
  console.log('Starting cleanup status polling...');

  // Check every 500ms for cleanup activity
  window.cleanupPollingInterval = setInterval(function() {
    // Look for any multiStep cleanup actions and check if they should be running
    checkForActiveCleanupActions();
  }, 500);
}

/**
 * Check for active cleanup actions and update their status
 */
function checkForActiveCleanupActions() {
  // This function will be called by the action execution logic
  // when cleanup starts, to trigger the UI updates
}

/**
 * Manually trigger cleanup action status update (called from action execution)
 */
function triggerCleanupActionStatus(status, message) {
  console.log('Triggering cleanup action status:', status, message);

  // Find and update multiStep cleanup actions
  const actionsList = document.getElementById('actionsList');
  if (!actionsList) return;

  const multiStepActions = actionsList.querySelectorAll('.action-item[data-action-type="multiStep"]');

  for (const actionItem of multiStepActions) {
    const actionDataStr = actionItem.dataset.actionData;
    if (actionDataStr) {
      try {
        const actionData = JSON.parse(actionDataStr);
        if (actionData && actionData.cleanup === true) {
          updateActionItemStatus(actionItem, status, message);
          break;
        }
      } catch (e) {
        // Try alternative approach - look for cleanup in content
        const actionContent = actionItem.querySelector('.action-content');
        if (actionContent && actionContent.textContent.toLowerCase().includes('cleanup')) {
          updateActionItemStatus(actionItem, status, message);
          break;
        }
      }
    }
  }
}

/**
 * Detect and handle cleanup execution when an action fails
 */
function handleActionFailureCleanup(actionResult) {
  console.log('Handling action failure cleanup:', actionResult);

  // Check if this action failure should trigger cleanup
  if (actionResult && !actionResult.success) {
    // Look for multiStep cleanup actions and show them as running
    setTimeout(() => {
      triggerCleanupActionStatus('running', 'Executing cleanup steps...');

      // After a delay, show as completed (this is a simplified approach)
      setTimeout(() => {
        triggerCleanupActionStatus('success', 'Cleanup steps completed');
      }, 3000); // Assume cleanup takes 3 seconds
    }, 500); // Small delay to let the failed action UI update first
  }
}

/**
 * Update a specific action item's status
 */
function updateActionItemStatus(actionItem, status, message) {
  console.log('Updating action item status:', status);

  // Remove existing status classes and icons
  actionItem.classList.remove(
    'list-group-item-info', 'bg-opacity-25',
    'list-group-item-success', 'list-group-item-warning',
    'list-group-item-danger', 'executing'
  );

  // Remove existing status icons
  const existingStatusIcon = actionItem.querySelector('.action-status');
  if (existingStatusIcon) {
    existingStatusIcon.remove();
  }

  const existingSpinner = actionItem.querySelector('.spinner-border');
  if (existingSpinner) {
    existingSpinner.remove();
  }

  // Add new status
  let statusHtml = '';
  switch (status) {
    case 'running':
      actionItem.classList.add('list-group-item-info', 'bg-opacity-25', 'executing');
      statusHtml = '<div class="action-status"><span class="spinner-border spinner-border-sm" role="status"></span></div>';
      break;
    case 'success':
      actionItem.classList.add('list-group-item-success', 'bg-opacity-25');
      statusHtml = '<div class="action-status"><i class="bi bi-check-circle-fill text-success"></i></div>';
      break;
    case 'warning':
      actionItem.classList.add('list-group-item-warning', 'bg-opacity-25');
      statusHtml = '<div class="action-status"><i class="bi bi-exclamation-triangle-fill text-warning"></i></div>';
      break;
    case 'error':
      actionItem.classList.add('list-group-item-danger', 'bg-opacity-25');
      statusHtml = '<div class="action-status"><i class="bi bi-x-circle-fill text-danger"></i></div>';
      break;
  }

  // Add the status icon
  const actionContent = actionItem.querySelector('.action-content');
  if (actionContent && statusHtml) {
    actionContent.insertAdjacentHTML('afterbegin', statusHtml);
  }

  // Add tooltip
  if (message) {
    actionItem.setAttribute('title', message);
    actionItem.setAttribute('data-bs-toggle', 'tooltip');
    actionItem.setAttribute('data-bs-placement', 'top');
  }

  // Scroll to the action if it's running
  if (status === 'running') {
    actionItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
}

/**
 * Update multiStep cleanup action status in the UI
 */
function updateMultiStepCleanupActionStatus(data) {
  console.log('Updating multiStep cleanup action status:', data);

  // Find the multiStep cleanup action in the actions list
  const actionsList = document.getElementById('actionsList');
  if (!actionsList) {
    console.error('Actions list not found');
    return;
  }

  // Look for multiStep actions with cleanup=true
  const multiStepActions = actionsList.querySelectorAll('.action-item[data-action-type="multiStep"]');
  console.log(`Found ${multiStepActions.length} multiStep actions`);

  let foundCleanupAction = false;

  for (const actionItem of multiStepActions) {
    // Check if this is a cleanup multiStep action
    const actionDataStr = actionItem.dataset.actionData;
    console.log('Checking action data:', actionDataStr);

    if (actionDataStr) {
      try {
        const actionData = JSON.parse(actionDataStr);
        console.log('Parsed action data:', actionData);

        if (actionData && actionData.cleanup === true) {
          console.log('Found multiStep cleanup action, updating status to:', data.status);
          foundCleanupAction = true;

          // Remove existing status classes and icons
          actionItem.classList.remove(
            'list-group-item-info', 'bg-opacity-25',
            'list-group-item-success', 'list-group-item-warning',
            'list-group-item-danger', 'executing'
          );

          // Remove existing status icons
          const existingStatusIcon = actionItem.querySelector('.action-status');
          if (existingStatusIcon) {
            existingStatusIcon.remove();
          }

          // Remove existing spinners
          const existingSpinner = actionItem.querySelector('.spinner-border');
          if (existingSpinner) {
            existingSpinner.remove();
          }

          // Add new status based on the event data
          let statusHtml = '';
          switch (data.status) {
            case 'running':
              actionItem.classList.add('list-group-item-info', 'bg-opacity-25', 'executing');
              statusHtml = '<div class="action-status"><span class="spinner-border spinner-border-sm" role="status"></span></div>';
              break;
            case 'success':
              actionItem.classList.add('list-group-item-success', 'bg-opacity-25');
              statusHtml = '<div class="action-status"><i class="bi bi-check-circle-fill text-success"></i></div>';
              break;
            case 'warning':
              actionItem.classList.add('list-group-item-warning', 'bg-opacity-25');
              statusHtml = '<div class="action-status"><i class="bi bi-exclamation-triangle-fill text-warning"></i></div>';
              break;
            case 'error':
              actionItem.classList.add('list-group-item-danger', 'bg-opacity-25');
              statusHtml = '<div class="action-status"><i class="bi bi-x-circle-fill text-danger"></i></div>';
              break;
          }

          // Add the status icon to the action content
          const actionContent = actionItem.querySelector('.action-content');
          if (actionContent && statusHtml) {
            actionContent.insertAdjacentHTML('afterbegin', statusHtml);
          }

          // Add tooltip with message
          if (data.message) {
            actionItem.setAttribute('title', data.message);
            actionItem.setAttribute('data-bs-toggle', 'tooltip');
            actionItem.setAttribute('data-bs-placement', 'top');
          }

          // Scroll to the action if it's running
          if (data.status === 'running') {
            actionItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }

          break; // Found and updated the cleanup action, no need to continue
        }
      } catch (e) {
        console.error('Error parsing action data:', e);
      }
    }
  }

  if (!foundCleanupAction) {
    console.warn('No multiStep cleanup action found to update');

    // Alternative approach: look for any multiStep action with "cleanup" in the title or content
    const allActions = actionsList.querySelectorAll('.action-item');
    for (const actionItem of allActions) {
      const actionContent = actionItem.querySelector('.action-content');
      if (actionContent && actionContent.textContent.toLowerCase().includes('cleanup')) {
        console.log('Found potential cleanup action by content search, updating status');

        // Apply the same status update logic
        actionItem.classList.remove(
          'list-group-item-info', 'bg-opacity-25',
          'list-group-item-success', 'list-group-item-warning',
          'list-group-item-danger', 'executing'
        );

        const existingStatusIcon = actionItem.querySelector('.action-status');
        if (existingStatusIcon) {
          existingStatusIcon.remove();
        }

        const existingSpinner = actionItem.querySelector('.spinner-border');
        if (existingSpinner) {
          existingSpinner.remove();
        }

        let statusHtml = '';
        switch (data.status) {
          case 'running':
            actionItem.classList.add('list-group-item-info', 'bg-opacity-25', 'executing');
            statusHtml = '<div class="action-status"><span class="spinner-border spinner-border-sm" role="status"></span></div>';
            break;
          case 'success':
            actionItem.classList.add('list-group-item-success', 'bg-opacity-25');
            statusHtml = '<div class="action-status"><i class="bi bi-check-circle-fill text-success"></i></div>';
            break;
          case 'warning':
            actionItem.classList.add('list-group-item-warning', 'bg-opacity-25');
            statusHtml = '<div class="action-status"><i class="bi bi-exclamation-triangle-fill text-warning"></i></div>';
            break;
        }

        const actionContent2 = actionItem.querySelector('.action-content');
        if (actionContent2 && statusHtml) {
          actionContent2.insertAdjacentHTML('afterbegin', statusHtml);
        }

        if (data.status === 'running') {
          actionItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        break;
      }
    }
  }
}

/**
 * Update cleanup progress based on step data
 */
function updateCleanupProgress(data) {
  const progressBar = document.getElementById('cleanup-progress-bar');
  const progressText = document.getElementById('cleanup-progress-text');
  const stepsList = document.getElementById('cleanup-steps-list');
  const statusMessage = document.getElementById('cleanup-status-message');

  if (!progressBar || !progressText || !stepsList || !statusMessage) {
    console.error('Cleanup progress elements not found');
    return;
  }

  // Calculate progress percentage
  const progress = Math.round(((data.step_index + 1) / data.total_steps) * 100);
  progressBar.style.width = `${progress}%`;
  progressText.textContent = `${progress}%`;

  // Create or update the step item
  let stepItem = document.querySelector(`[data-cleanup-step="${data.step_index}"]`);
  if (!stepItem) {
    stepItem = document.createElement('div');
    stepItem.className = 'cleanup-step-item d-flex align-items-center mb-2 p-2 border rounded';
    stepItem.setAttribute('data-cleanup-step', data.step_index);
    stepsList.appendChild(stepItem);
  }

  // Update step content based on status
  let statusIcon, statusClass, statusText;
  switch (data.status) {
    case 'running':
      statusIcon = '<i class="fas fa-spinner fa-spin text-primary"></i>';
      statusClass = 'border-primary bg-light';
      statusText = 'Running...';
      break;
    case 'success':
      statusIcon = '<i class="fas fa-check-circle text-success"></i>';
      statusClass = 'border-success bg-light';
      statusText = 'Completed';
      break;
    case 'error':
      statusIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
      statusClass = 'border-danger bg-light';
      statusText = 'Failed (continuing...)';
      break;
    case 'warning':
      statusIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
      statusClass = 'border-warning bg-light';
      statusText = 'Warning';
      break;
    default:
      statusIcon = '<i class="fas fa-circle text-muted"></i>';
      statusClass = 'border-muted';
      statusText = 'Pending';
  }

  stepItem.className = `cleanup-step-item d-flex align-items-center mb-2 p-2 border rounded ${statusClass}`;
  stepItem.innerHTML = `
    <div class="me-3">${statusIcon}</div>
    <div class="flex-grow-1">
      <div class="fw-bold">Step ${data.step_index + 1}/${data.total_steps}: ${data.action_type || 'Unknown'}</div>
      <div class="text-muted small">${data.message || statusText}</div>
    </div>
    <div class="ms-2">
      <span class="badge ${data.status === 'success' ? 'bg-success' : data.status === 'error' ? 'bg-danger' : data.status === 'warning' ? 'bg-warning' : 'bg-primary'}">${statusText}</span>
    </div>
  `;

  // Update status message
  if (data.status === 'running') {
    statusMessage.innerHTML = `
      <div class="alert alert-info">
        <i class="fas fa-cogs me-2"></i>
        Executing cleanup step ${data.step_index + 1} of ${data.total_steps}...
      </div>
    `;
  } else if (progress === 100) {
    statusMessage.innerHTML = `
      <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        All cleanup steps completed! The app is now in a clean state.
      </div>
    `;
  }

  // Scroll to the current step
  stepItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

/**
 * Create a cleanup steps container for visual feedback
 */
function createCleanupStepsContainer(actionElement, testCaseId, totalSteps) {
  console.log(`Creating cleanup steps container for ${totalSteps} steps`);

  // Create the container
  const container = document.createElement('div');
  container.className = 'cleanup-steps-container mt-2';
  container.style.display = 'block';

  // Add header
  const header = document.createElement('div');
  header.className = 'cleanup-steps-header d-flex justify-content-between align-items-center mb-2';
  header.innerHTML = `
    <span class="badge bg-warning">Cleanup Steps</span>
    <small class="text-muted">Test Case: ${testCaseId}</small>
  `;
  container.appendChild(header);

  // Create steps list
  const stepsList = document.createElement('div');
  stepsList.className = 'cleanup-steps-list';

  // Create placeholder step items (will be populated as steps execute)
  for (let i = 0; i < totalSteps; i++) {
    const stepItem = document.createElement('div');
    stepItem.className = 'cleanup-step-item d-flex justify-content-between align-items-center mb-1 p-2 border rounded';
    stepItem.dataset.stepIndex = i;
    stepItem.innerHTML = `
      <div class="cleanup-step-content">
        <span class="badge bg-secondary me-2">${i + 1}</span>
        <span class="cleanup-step-description">Waiting...</span>
      </div>
      <div class="cleanup-step-status">
        <i class="bi bi-clock text-muted"></i>
      </div>
    `;
    stepsList.appendChild(stepItem);
  }

  container.appendChild(stepsList);

  // Append to the action element
  actionElement.appendChild(container);

  return container;
}

/**
 * Update the status of a specific cleanup step
 */
function updateCleanupStepStatus(container, stepIndex, status, message) {
  const stepItem = container.querySelector(`.cleanup-step-item[data-step-index="${stepIndex}"]`);
  if (!stepItem) {
    console.warn(`Cleanup step item not found for index ${stepIndex}`);
    return;
  }

  const stepDescription = stepItem.querySelector('.cleanup-step-description');
  const stepStatus = stepItem.querySelector('.cleanup-step-status');

  // Update description
  if (stepDescription) {
    stepDescription.textContent = message || `Step ${stepIndex + 1}`;
  }

  // Update status icon and styling
  if (stepStatus) {
    // Remove previous status classes
    stepItem.classList.remove('cleanup-step-executing', 'cleanup-step-success', 'cleanup-step-error', 'cleanup-step-warning');

    switch (status) {
      case 'running':
        stepStatus.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>';
        stepItem.classList.add('cleanup-step-executing');
        break;
      case 'success':
        stepStatus.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
        stepItem.classList.add('cleanup-step-success');
        break;
      case 'error':
        stepStatus.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i>';
        stepItem.classList.add('cleanup-step-error');
        break;
      case 'warning':
        stepStatus.innerHTML = '<i class="bi bi-exclamation-triangle-fill text-warning"></i>';
        stepItem.classList.add('cleanup-step-warning');
        break;
      default:
        stepStatus.innerHTML = '<i class="bi bi-clock text-muted"></i>';
    }
  }
}

/**
 * Set up functionality for the device screen
 */
function setupDeviceScreen() {
  const deviceScreen = document.getElementById('deviceScreen');
  const refreshBtn = document.getElementById('refreshScreenBtn');

  if (refreshBtn) {
    refreshBtn.addEventListener('click', function() {
      if (typeof socket !== 'undefined') {
        console.log('Requesting screenshot refresh');
        socket.emit('refresh_screenshot');

        if (typeof showLoading === 'function') {
          showLoading(true, 'Refreshing screenshot...');
        }
      }
    });
  }

  console.log('Device screen functionality initialized');
}

// Make functions available globally
window.setupAppSocketListeners = setupAppSocketListeners;
window.setupDeviceScreen = setupDeviceScreen;

// WebSocket health monitor
const SocketHealthMonitor = {
  pingInterval: null,
  connectionLost: false,

  start() {
    // Clear any existing interval
    this.stop();

    // Only start if socket exists
    if (typeof socket === 'undefined' || !socket) {
      console.error('Cannot start health monitor - socket not available');
      return;
    }

    // Reduced frequency: ping every 15 seconds (was 5 seconds) to reduce background noise
    this.pingInterval = setInterval(() => {
      socket.emit('ping', {}, (response) => {
        if (this.connectionLost) {
          console.log('Connection restored');
          this.connectionLost = false;

          // If execution was in progress, request status update
          if (ExecutionStatusManager.isRunning) {
            socket.emit('check_execution_status');
          }
        }
      });

      // Set a timeout to detect missed pongs (increased to 10 seconds)
      setTimeout(() => {
        if (!this.connectionLost) {
          console.log('WebSocket connection may be lost');
          this.connectionLost = true;

          // Show reconnection message
          if (ExecutionStatusManager.isRunning) {
            ExecutionStatusManager.addLogEntry('Connection lost, attempting to reconnect...', 'warning');
          }
        }
      }, 10000);
    }, 15000);
  },

  stop() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }
};

// Initialize the socket health monitor when document is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Only start health monitor if socket exists
  if (typeof socket !== 'undefined' && socket) {
    SocketHealthMonitor.start();
  } else {
    console.error('Cannot start health monitor - socket not available');
  }
});

// Revised version for safer socket usage
function executeAction(index) {
  // Check if socket exists before using it
  if (typeof socket !== 'undefined' && socket) {
    // Execute the action without modifying UI row status
    socket.emit('execute_action', { index: index });
  } else {
    console.error('Cannot execute action - socket not available');
  }
}

// Add this functionality to handle action log updates
socket.on('action_log', function(data) {
    var logContainer = document.getElementById('action-log');
    if (!logContainer) return;

    var entry = document.createElement('div');
    entry.className = 'log-entry ' + (data.type || 'info');

    var time = new Date().toLocaleTimeString();
    entry.innerHTML = '<span class="log-time">[' + time + ']</span> ' + data.message;

    logContainer.appendChild(entry);

    // Auto-scroll to bottom
    logContainer.scrollTop = logContainer.scrollHeight;
});

// Helper function to get descriptions for each action
function get_action_description(action) {
    switch (action.type) {
        case 'tap':
            return `Tapping at (${action.x}, ${action.y})`;
        case 'swipe':
            return `Swiping from (${action.start_x}, ${action.start_y}) to (${action.end_x}, ${action.end_y})`;
        case 'text':
            return `Entering text: "${action.text.substring(0, 15)}${action.text.length > 15 ? '...' : ''}"`;
        case 'wait':
            return `Waiting for ${action.duration} seconds`;
        case 'key':
            return `Pressing key: ${action.key}`;
        case 'hideKeyboard':
            return `Hiding keyboard`;
        case 'airplaneMode':
            return `${action.enabled ? 'Enable' : 'Disable'} Airplane Mode`;
        case 'addMedia':
            const mediaName = action.file_path?.split('/').pop() || 'file';
            return `Add Media: ${mediaName}`;
        case 'deviceBack':
            return `Press Android Back`;
        case 'getValue':
            return `Get Value: ${action.locator_type}`;
        case 'compareValue':
            return `Compare Value: ${action.locator_type}`;
        default:
            return `${action.type} action`;
    }
}

// Make sure socket.io is initialized with the correct base URL
const socket = io.connect(window.location.origin, {
  path: '/socket.io',
  transports: ['websocket', 'polling']
});