/**
 * Test script to verify coordinate calculation functionality
 * This can be run in the browser console to test the coordinate calculation
 */

function testCoordinateCalculation() {
    console.log('🧪 Testing Coordinate Calculation...');
    
    // Mock device screen element
    const mockDeviceScreen = {
        getBoundingClientRect: () => ({
            left: 100,
            top: 50,
            width: 540,  // Half size of 1080
            height: 1170, // Half size of 2340
        }),
        naturalWidth: 1080,
        naturalHeight: 2340
    };
    
    // Mock device info
    const mockDeviceInfo = {
        width: 1080,
        height: 2340
    };
    
    // Mock event at center of image
    const mockEvent = {
        clientX: 100 + 270, // left + half width
        clientY: 50 + 585   // top + half height
    };
    
    // Mock app instance
    const mockApp = {
        deviceScreen: mockDeviceScreen,
        deviceInfo: mockDeviceInfo,
        isConnected: true,
        
        async fetchApi(endpoint) {
            return {
                success: true,
                dimensions: {
                    width: 1080,
                    height: 2340
                }
            };
        },
        
        logAction(type, message) {
            console.log(`[${type.toUpperCase()}] ${message}`);
        },
        
        // Copy the calculateAccurateDeviceCoordinates method
        async calculateAccurateDeviceCoordinates(event) {
            try {
                // Get click coordinates relative to the image ELEMENT
                const rect = this.deviceScreen.getBoundingClientRect();
                const clickXRelative = event.clientX - rect.left;
                const clickYRelative = event.clientY - rect.top;

                // Validate click is within image bounds
                if (clickXRelative < 0 || clickYRelative < 0 || 
                    clickXRelative > rect.width || clickYRelative > rect.height) {
                    console.warn('Click outside image bounds');
                    return null;
                }

                let deviceWidth, deviceHeight;
                let method = 'unknown';

                // Method 1: Try to get dimensions from cached deviceInfo
                if (this.deviceInfo &&
                    ((this.deviceInfo.width && this.deviceInfo.height) ||
                     (this.deviceInfo.dimensions && this.deviceInfo.dimensions.width && this.deviceInfo.dimensions.height))) {

                    if (this.deviceInfo.dimensions) {
                        deviceWidth = this.deviceInfo.dimensions.width;
                        deviceHeight = this.deviceInfo.dimensions.height;
                    } else {
                        deviceWidth = this.deviceInfo.width;
                        deviceHeight = this.deviceInfo.height;
                    }
                    method = 'cached_device_info';
                    console.log(`Using cached device dimensions: ${deviceWidth}x${deviceHeight}`);
                } 
                // Method 2: Fetch dimensions from the API
                else {
                    try {
                        console.log('Device dimensions not cached, fetching from /api/device/dimensions...');
                        const dimensionsResult = await this.fetchApi('device/dimensions');
                        if (dimensionsResult.success && dimensionsResult.dimensions) {
                            deviceWidth = dimensionsResult.dimensions.width;
                            deviceHeight = dimensionsResult.dimensions.height;
                            method = 'api_fetch';
                            
                            // Cache them for next time
                            this.deviceInfo = this.deviceInfo || {};
                            this.deviceInfo.width = deviceWidth;
                            this.deviceInfo.height = deviceHeight;
                            console.log(`Fetched device dimensions: ${deviceWidth}x${deviceHeight}`);
                        } else {
                            throw new Error('API returned invalid dimensions');
                        }
                    } catch (apiError) {
                        console.warn('API fetch failed, using image natural dimensions:', apiError);
                        // Method 3: Fallback to image natural dimensions
                        deviceWidth = this.deviceScreen.naturalWidth;
                        deviceHeight = this.deviceScreen.naturalHeight;
                        method = 'image_natural_dimensions';
                        
                        if (!deviceWidth || !deviceHeight) {
                            // Method 4: Final fallback to default Android dimensions
                            deviceWidth = 1080;
                            deviceHeight = 1920;
                            method = 'default_android_dimensions';
                            console.warn('Using default Android dimensions as final fallback');
                        }
                    }
                }

                // Validate device dimensions
                if (!deviceWidth || !deviceHeight || deviceWidth <= 0 || deviceHeight <= 0) {
                    console.error('Invalid device dimensions:', deviceWidth, deviceHeight);
                    return null;
                }

                // Enhanced coordinate calculation with precision handling
                const imageDisplayWidth = rect.width;
                const imageDisplayHeight = rect.height;
                
                // Calculate scaling factors with high precision
                const scaleX = deviceWidth / imageDisplayWidth;
                const scaleY = deviceHeight / imageDisplayHeight;

                // Calculate device coordinates with proper rounding
                const x = Math.round(clickXRelative * scaleX);
                const y = Math.round(clickYRelative * scaleY);

                // Validate calculated coordinates are within device bounds
                const clampedX = Math.max(0, Math.min(x, deviceWidth - 1));
                const clampedY = Math.max(0, Math.min(y, deviceHeight - 1));

                const debugInfo = {
                    method: method,
                    clickRelative: { x: clickXRelative.toFixed(2), y: clickYRelative.toFixed(2) },
                    imageDisplay: { width: imageDisplayWidth, height: imageDisplayHeight },
                    deviceDimensions: { width: deviceWidth, height: deviceHeight },
                    scale: { x: scaleX.toFixed(4), y: scaleY.toFixed(4) },
                    calculated: { x, y },
                    final: { x: clampedX, y: clampedY },
                    clamped: x !== clampedX || y !== clampedY
                };

                if (debugInfo.clamped) {
                    console.warn('Coordinates were clamped to device bounds:', debugInfo);
                }

                return {
                    x: clampedX,
                    y: clampedY,
                    debugInfo: debugInfo
                };

            } catch (error) {
                console.error('Error calculating device coordinates:', error);
                return null;
            }
        }
    };
    
    // Run the test
    mockApp.calculateAccurateDeviceCoordinates(mockEvent).then(result => {
        if (result) {
            console.log('✅ Coordinate calculation test PASSED!');
            console.log('📍 Calculated coordinates:', result.x, result.y);
            console.log('🔍 Debug info:', result.debugInfo);
            
            // Verify the result is reasonable (should be center of device)
            const expectedX = 540; // Center of 1080
            const expectedY = 1170; // Center of 2340
            
            if (Math.abs(result.x - expectedX) <= 1 && Math.abs(result.y - expectedY) <= 1) {
                console.log('✅ Coordinate accuracy test PASSED!');
                console.log(`Expected: (${expectedX}, ${expectedY}), Got: (${result.x}, ${result.y})`);
            } else {
                console.log('❌ Coordinate accuracy test FAILED!');
                console.log(`Expected: (${expectedX}, ${expectedY}), Got: (${result.x}, ${result.y})`);
            }
        } else {
            console.log('❌ Coordinate calculation test FAILED!');
        }
    }).catch(error => {
        console.log('❌ Coordinate calculation test ERROR:', error);
    });
}

// Auto-run test if in browser
if (typeof window !== 'undefined') {
    console.log('🚀 Coordinate calculation test script loaded. Run testCoordinateCalculation() to test.');
}

// Export for Node.js testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testCoordinateCalculation };
}
