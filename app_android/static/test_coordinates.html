<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coordinate Calculation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-area {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8f9fa;
        }
        .device-screen {
            width: 270px;
            height: 585px;
            border: 2px solid #333;
            border-radius: 20px;
            background: linear-gradient(45deg, #e3f2fd, #bbdefb);
            margin: 20px auto;
            position: relative;
            cursor: crosshair;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #333;
        }
        .coordinates-display {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
        .error-display {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            color: #d32f2f;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Coordinate Calculation Test</h1>
        <p>This page tests the enhanced coordinate calculation functionality for the "Tap on Coordinates" feature.</p>
        
        <div class="test-area">
            <h3>📱 Mock Device Screen</h3>
            <p>Click anywhere on the mock device screen below to test coordinate calculation:</p>
            
            <div id="deviceScreen" class="device-screen">
                Click anywhere to test<br>
                coordinates calculation
            </div>
            
            <div id="coordinatesDisplay" class="coordinates-display" style="display: none;">
                <strong>Calculated Coordinates:</strong><br>
                <span id="coordinatesText"></span>
            </div>
            
            <div id="errorDisplay" class="error-display" style="display: none;">
                <strong>Error:</strong><br>
                <span id="errorText"></span>
            </div>
        </div>
        
        <div class="test-area">
            <h3>🔧 Test Controls</h3>
            <button class="test-button" onclick="runAutomatedTests()">Run Automated Tests</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            <button class="test-button" onclick="testCornerCases()">Test Corner Cases</button>
        </div>
        
        <div class="test-area">
            <h3>📋 Test Log</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // Mock device dimensions (simulating 1080x2340 device displayed at 270x585)
        const DEVICE_WIDTH = 1080;
        const DEVICE_HEIGHT = 2340;
        const DISPLAY_WIDTH = 270;
        const DISPLAY_HEIGHT = 585;
        
        let testCount = 0;
        let passCount = 0;
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#d32f2f' : type === 'success' ? '#2e7d32' : '#333';
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
            testCount = 0;
            passCount = 0;
        }
        
        // Enhanced coordinate calculation function (copied from main.js)
        async function calculateAccurateDeviceCoordinates(event) {
            try {
                const rect = document.getElementById('deviceScreen').getBoundingClientRect();
                const clickXRelative = event.clientX - rect.left;
                const clickYRelative = event.clientY - rect.top;

                // Validate click is within image bounds
                if (clickXRelative < 0 || clickYRelative < 0 || 
                    clickXRelative > rect.width || clickYRelative > rect.height) {
                    throw new Error('Click outside image bounds');
                }

                const deviceWidth = DEVICE_WIDTH;
                const deviceHeight = DEVICE_HEIGHT;
                const method = 'test_mock_data';

                // Enhanced coordinate calculation with precision handling
                const imageDisplayWidth = rect.width;
                const imageDisplayHeight = rect.height;
                
                // Calculate scaling factors with high precision
                const scaleX = deviceWidth / imageDisplayWidth;
                const scaleY = deviceHeight / imageDisplayHeight;

                // Calculate device coordinates with proper rounding
                const x = Math.round(clickXRelative * scaleX);
                const y = Math.round(clickYRelative * scaleY);

                // Validate calculated coordinates are within device bounds
                const clampedX = Math.max(0, Math.min(x, deviceWidth - 1));
                const clampedY = Math.max(0, Math.min(y, deviceHeight - 1));

                const debugInfo = {
                    method: method,
                    clickRelative: { x: clickXRelative.toFixed(2), y: clickYRelative.toFixed(2) },
                    imageDisplay: { width: imageDisplayWidth, height: imageDisplayHeight },
                    deviceDimensions: { width: deviceWidth, height: deviceHeight },
                    scale: { x: scaleX.toFixed(4), y: scaleY.toFixed(4) },
                    calculated: { x, y },
                    final: { x: clampedX, y: clampedY },
                    clamped: x !== clampedX || y !== clampedY
                };

                return {
                    x: clampedX,
                    y: clampedY,
                    debugInfo: debugInfo
                };

            } catch (error) {
                throw error;
            }
        }
        
        // Handle clicks on the mock device screen
        document.getElementById('deviceScreen').addEventListener('click', async function(event) {
            try {
                const result = await calculateAccurateDeviceCoordinates(event);
                
                // Display results
                const coordsDisplay = document.getElementById('coordinatesDisplay');
                const coordsText = document.getElementById('coordinatesText');
                const errorDisplay = document.getElementById('errorDisplay');
                
                coordsText.innerHTML = `
                    Device Coordinates: (${result.x}, ${result.y})<br>
                    Click Position: (${result.debugInfo.clickRelative.x}, ${result.debugInfo.clickRelative.y})<br>
                    Scale Factors: X=${result.debugInfo.scale.x}, Y=${result.debugInfo.scale.y}<br>
                    Method: ${result.debugInfo.method}
                `;
                
                coordsDisplay.style.display = 'block';
                errorDisplay.style.display = 'none';
                
                log(`✅ Coordinates calculated: (${result.x}, ${result.y}) from click (${result.debugInfo.clickRelative.x}, ${result.debugInfo.clickRelative.y})`, 'success');
                
            } catch (error) {
                const errorDisplay = document.getElementById('errorDisplay');
                const errorText = document.getElementById('errorText');
                const coordsDisplay = document.getElementById('coordinatesDisplay');
                
                errorText.textContent = error.message;
                errorDisplay.style.display = 'block';
                coordsDisplay.style.display = 'none';
                
                log(`❌ Error calculating coordinates: ${error.message}`, 'error');
            }
        });
        
        // Automated test functions
        function runAutomatedTests() {
            log('🚀 Starting automated coordinate calculation tests...');
            testCount = 0;
            passCount = 0;
            
            // Test center coordinates
            testCoordinates(DISPLAY_WIDTH / 2, DISPLAY_HEIGHT / 2, DEVICE_WIDTH / 2, DEVICE_HEIGHT / 2, 'Center coordinates');
            
            // Test corners
            testCoordinates(0, 0, 0, 0, 'Top-left corner');
            testCoordinates(DISPLAY_WIDTH - 1, 0, DEVICE_WIDTH - 2, 0, 'Top-right corner');
            testCoordinates(0, DISPLAY_HEIGHT - 1, 0, DEVICE_HEIGHT - 2, 'Bottom-left corner');
            testCoordinates(DISPLAY_WIDTH - 1, DISPLAY_HEIGHT - 1, DEVICE_WIDTH - 2, DEVICE_HEIGHT - 2, 'Bottom-right corner');
            
            // Test quarter points
            testCoordinates(DISPLAY_WIDTH / 4, DISPLAY_HEIGHT / 4, DEVICE_WIDTH / 4, DEVICE_HEIGHT / 4, 'Quarter point');
            testCoordinates(3 * DISPLAY_WIDTH / 4, 3 * DISPLAY_HEIGHT / 4, 3 * DEVICE_WIDTH / 4, 3 * DEVICE_HEIGHT / 4, 'Three-quarter point');
            
            setTimeout(() => {
                log(`📊 Test Summary: ${passCount}/${testCount} tests passed (${((passCount/testCount)*100).toFixed(1)}%)`, 
                    passCount === testCount ? 'success' : 'error');
            }, 100);
        }
        
        function testCoordinates(displayX, displayY, expectedDeviceX, expectedDeviceY, description) {
            testCount++;
            
            // Create mock event
            const rect = document.getElementById('deviceScreen').getBoundingClientRect();
            const mockEvent = {
                clientX: rect.left + displayX,
                clientY: rect.top + displayY
            };
            
            calculateAccurateDeviceCoordinates(mockEvent).then(result => {
                const tolerance = 2; // Allow small rounding differences
                const xMatch = Math.abs(result.x - expectedDeviceX) <= tolerance;
                const yMatch = Math.abs(result.y - expectedDeviceY) <= tolerance;
                
                if (xMatch && yMatch) {
                    passCount++;
                    log(`✅ ${description}: Expected (${expectedDeviceX}, ${expectedDeviceY}), Got (${result.x}, ${result.y})`, 'success');
                } else {
                    log(`❌ ${description}: Expected (${expectedDeviceX}, ${expectedDeviceY}), Got (${result.x}, ${result.y})`, 'error');
                }
            }).catch(error => {
                log(`❌ ${description}: Error - ${error.message}`, 'error');
            });
        }
        
        function testCornerCases() {
            log('🔍 Testing corner cases and edge conditions...');
            
            // Test coordinates outside bounds (should be clamped)
            testCoordinates(-10, -10, 0, 0, 'Negative coordinates (should clamp to 0,0)');
            testCoordinates(DISPLAY_WIDTH + 10, DISPLAY_HEIGHT + 10, DEVICE_WIDTH - 1, DEVICE_HEIGHT - 1, 'Beyond bounds (should clamp to max)');
            
            // Test fractional coordinates
            testCoordinates(100.5, 200.7, Math.round(100.5 * (DEVICE_WIDTH / DISPLAY_WIDTH)), Math.round(200.7 * (DEVICE_HEIGHT / DISPLAY_HEIGHT)), 'Fractional coordinates');
        }
        
        // Initialize
        log('🧪 Coordinate calculation test page loaded. Click on the device screen or run automated tests.');
    </script>
</body>
</html>
