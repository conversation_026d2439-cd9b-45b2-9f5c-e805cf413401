import logging
import importlib
import os


# DATABASE-FIRST: Ensure every action is tracked regardless of return paths
def db_track_action_factory(func):
    def wrapper(self, action_type, params, *args, **kwargs):
        result = None
        screenshot_path = None
        try:
            self.logger.info(f"🔵 DATABASE-FIRST: AF Wrapper invoked for execute_action: {action_type}")
            self.logger.info(f"🔵 AF Params: {params}")
            result = func(self, action_type, params, *args, **kwargs)
            # Prefer explicit screenshot path in result if present
            if isinstance(result, dict):
                screenshot_path = result.get('screenshot_path') or result.get('screenshot')
            self.logger.info(f"🔵 AF Result: {result}")
            return result
        finally:
            try:
                self.logger.info(f"🔵 AF Finally block: Attempting to track execution")
                # Track execution in DB
                if hasattr(self, '_track_action_execution_in_database'):
                    self.logger.info(f"🔵 AF Calling existing _track_action_execution_in_database")
                    self._track_action_execution_in_database(action_type, params, result, screenshot_path)
                else:
                    # Lazy-add and call inline to avoid missing tracking
                    try:
                        self.logger.info(f"🔵 AF Binding _track_action_execution_in_database")
                        self._track_action_execution_in_database = _action_factory_track_helper.__get__(self, self.__class__)
                        self._track_action_execution_in_database(action_type, params, result, screenshot_path)
                    except Exception as bind_err:
                        self.logger.error(f"❌ DATABASE-FIRST: Failed to bind tracking helper: {bind_err}")
            except Exception as track_err:
                self.logger.error(f"❌ DATABASE-FIRST: AF tracking error: {track_err}")
    return wrapper

# Helper bound method for ActionFactory instances
def _action_factory_track_helper(self, action_type, params, result, screenshot_path=None):
    try:
        self.logger.info("=" * 80)
        self.logger.info("DATABASE-FIRST: AF Tracking action execution")
        # DATABASE-FIRST: Log DB path and input context for diagnostics
        try:
            from app_android.utils.database import get_db_path as _adb_get_db_path
            import os as _os
            _dbp = _adb_get_db_path()
            self.logger.info(f"DATABASE-FIRST: AF Using DB file: {_dbp} (exists={_os.path.exists(_dbp)})")
        except Exception as _dbp_err:
            self.logger.error(f"DATABASE-FIRST: AF Failed to get DB path: {_dbp_err}")
        self.logger.info(f"DATABASE-FIRST: AF Context — action_type={action_type}, params_keys={list((params or {}).keys())}")

        from app_android.utils.database import track_test_execution, save_screenshot_info
        from app_android.app import current_test_idx, current_step_idx, current_suite_id, app
        import os, glob, time

        action_id = (params or {}).get('action_id') or ''
        filename = (params or {}).get('filename') or getattr(app, 'current_test_case_name', None) or 'unknown'
        success = False
        message = ''
        if isinstance(result, dict):
            success = result.get('status') == 'success'
            message = result.get('message', '')

        # Determine indices and IDs
        local_test_idx = (params or {}).get('test_idx')
        if local_test_idx is None:
            local_test_idx = getattr(current_test_idx, 'value', 0)
        suite_id = getattr(app, 'current_suite_id', None) or getattr(current_suite_id, 'value', None) or 'unknown'
        test_execution_id = getattr(app, 'current_execution_id', None)
        test_case_id = getattr(app, 'current_test_case_id_mapping', {}).get(local_test_idx) if hasattr(app, 'current_test_case_id_mapping') else None
        step_idx = getattr(current_step_idx, 'value', None)

        # Persist tracking row
        final_status = 'passed' if success else 'failed'
        self.logger.info(f"🔵 AF track_test_execution: suite_id={suite_id}, test_idx={local_test_idx}, step_idx={step_idx}, action_type={action_type}, action_id={action_id}, status={final_status}")
        self.logger.info(f"🔵 AF About to call track_test_execution with status: {final_status}")

        track_result = track_test_execution(
            suite_id=suite_id,
            test_idx=local_test_idx,
            filename=filename,
            status=final_status,
            retry_count=int((params or {}).get('retry_count', 0)),
            max_retries=int((params or {}).get('max_retries', 0)),
            error=None if success else message,
            in_progress=False,
            step_idx=step_idx,
            action_type=action_type,
            action_params=params,
            action_id=action_id,
            execution_result=result,
            test_case_id=test_case_id,
            test_execution_id=test_execution_id,
        )

        if track_result:
            self.logger.info(f"✅ AF track_test_execution SUCCESS for action_id: {action_id}")
        else:
            self.logger.error(f"❌ AF track_test_execution FAILED for action_id: {action_id}")

        # Determine screenshot path if not provided: try current report screenshots dir
        def _find_latest_screenshot_dir():
            try:
                from app_android.utils.directory_paths_db import directory_paths_db
                reports_dir = directory_paths_db.get_path('REPORTS')
                exec_id = getattr(app, 'current_execution_id', '')
                if reports_dir and exec_id:
                    shots_dir = os.path.join(reports_dir, exec_id, 'screenshots')
                    return shots_dir if os.path.isdir(shots_dir) else None
            except Exception:
                return None
            return None
        # Log candidate screenshots when inferring
        self.logger.info(f"DATABASE-FIRST: AF Initial screenshot_path={screenshot_path}")


        if not screenshot_path:
            shots_dir = _find_latest_screenshot_dir()
            if shots_dir:
                candidates = sorted(glob.glob(os.path.join(shots_dir, 'screenshot_*.png')), key=os.path.getmtime)
                if not candidates:
                    # fallback include latest.png
                    candidates = sorted(glob.glob(os.path.join(shots_dir, '*.png')), key=os.path.getmtime)
                self.logger.info(f"DATABASE-FIRST: AF candidates in {shots_dir}: {len(candidates)}")

                if candidates:
                    latest = candidates[-1]
                    # Only attach if very recent (within 5s) to reduce mismatches
                    try:
                        self.logger.info(f"DATABASE-FIRST: AF latest candidate: {latest}")

                        if time.time() - os.path.getmtime(latest) <= 5:
                            screenshot_path = latest
                    except Exception:
                        pass

        if screenshot_path and os.path.exists(screenshot_path):
            self.logger.info(f"AF save_screenshot_info: path={screenshot_path}")
            save_screenshot_info(
                suite_id=suite_id,
                test_idx=local_test_idx,
                step_idx=step_idx,
                filename=f"{action_id}.png" if action_id else os.path.basename(screenshot_path),
                path=screenshot_path,
                action_id=action_id,
                test_execution_id=test_execution_id,
                test_case_id=test_case_id,
            )
            self.logger.info("✅ AF Screenshot saved to database with BLOB")
        else:
            self.logger.info(f"AF No screenshot to save (path: {screenshot_path})")

        self.logger.info("✅ DATABASE-FIRST: AF Tracking completed")
        self.logger.info("=" * 80)
    except Exception as e:
        self.logger.error(f"❌ AF tracking exception: {e}")
        import traceback; self.logger.error(traceback.format_exc())

class ActionFactory:
    """Factory for creating and executing actions"""

    def __init__(self, controller=None):
        """
        Initialize the action factory

        Args:
            controller: The device controller to use for actions
        """
        self.controller = controller
        self.logger = logging.getLogger("ActionFactory")
        self.action_handlers = {}
        self._discover_action_handlers()

        # Ensure doubleTap action is registered
        self._ensure_double_tap_registered()

        # Debug: Print all registered action types with more detail
        self.logger.info(f"Registered action types: {list(self.action_handlers.keys())}")

        # Add more detailed logging for each handler
        for action_type, handler in self.action_handlers.items():
            self.logger.info(f"Handler for '{action_type}': {handler.__class__.__name__}")

    def _discover_action_handlers(self):
        """Dynamically discover and register all action handlers"""
        import sys
        import os

        # Path to the actions directory
        actions_dir = os.path.dirname(os.path.abspath(__file__))

        # Add the actions directory to sys.path if not already there
        if actions_dir not in sys.path:
            sys.path.insert(0, actions_dir)

        # Get all Python files in the actions directory
        action_files = [f[:-3] for f in os.listdir(actions_dir)
                       if f.endswith('.py')
                       and not f.startswith('__')
                       and f not in ['base_action.py', 'action_factory.py']]

        # Add the current directory to sys.path for direct imports
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        # Temporarily register a few basic actions manually to test functionality
        self._register_basic_actions()

        for action_file in action_files:
            try:
                # Import the module using proper package path with fallback
                # Prioritize Android-specific actions over iOS actions
                module = None
                import_attempts = [
                    f"app_android.actions.{action_file}",  # Try Android version first
                    f"actions.{action_file}",              # Fall back to iOS version
                    action_file                            # Direct import as last resort
                ]

                for module_name in import_attempts:
                    try:
                        module = importlib.import_module(module_name)
                        break
                    except ImportError:
                        continue

                if module is None:
                    self.logger.error(f"Failed to import module for {action_file}")
                    continue

                # Extract the action type from the filename
                # For example, text_action.py -> text
                action_type = action_file.replace('_action', '')

                # Special case for hook_action.py - register as hookAction
                if action_file == 'hook_action':
                    action_type = 'hookAction'
                # Special case for double_tap_action.py - register as doubleTap
                elif action_file == 'double_tap_action':
                    action_type = 'doubleTap'
                    self.logger.info(f"Special case: Registering double_tap_action.py as 'doubleTap'")
                # Special case for repeat_steps_action.py - register as repeatSteps
                elif action_file == 'repeat_steps_action':
                    action_type = 'repeatSteps'
                    self.logger.info(f"Special case: Registering repeat_steps_action.py as 'repeatSteps'")
                # Special case for tap_on_text_action.py - register as tapOnText
                elif action_file == 'tap_on_text_action':
                    action_type = 'tapOnText'
                    self.logger.info(f"Special case: Registering tap_on_text_action.py as 'tapOnText'")
                # Special case for take_screenshot_action.py - register as takeScreenshot
                elif action_file == 'take_screenshot_action':
                    action_type = 'takeScreenshot'
                    self.logger.info(f"Special case: Registering take_screenshot_action.py as 'takeScreenshot'")
                # Special case for tap_if_image_exists_action.py - register as tapIfImageExists
                elif action_file == 'tap_if_image_exists_action':
                    action_type = 'tapIfImageExists'
                    self.logger.info(f"Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'")
                # Special case for tap_if_locator_exists_action.py - register as tapIfLocatorExists
                elif action_file == 'tap_if_locator_exists_action':
                    action_type = 'tapIfLocatorExists'
                    self.logger.info(f"Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'")
                # Special case for tap_if_text_exists_action.py - register as tapIfTextExists
                elif action_file == 'tap_if_text_exists_action':
                    action_type = 'tapIfTextExists'
                    self.logger.info(f"Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'")
                # Special case for check_if_exists_action.py - register as exists
                elif action_file == 'check_if_exists_action':
                    action_type = 'exists'
                    self.logger.info(f"Special case: Registering check_if_exists_action.py as 'exists'")

                # Special case for info_action.py - register as info
                elif action_file == 'info_action':
                    action_type = 'info'
                    self.logger.info(f"Special case: Registering info_action.py as 'info'")
                # Special case for android_functions_action.py - register as androidFunctions
                elif action_file == 'android_functions_action':
                    action_type = 'androidFunctions'
                    self.logger.info(f"Special case: Registering android_functions_action.py as 'androidFunctions'")
                # Special case for if_then_steps_action.py - register as ifThenSteps
                elif action_file == 'if_then_steps_action':
                    action_type = 'ifThenSteps'
                    self.logger.info(f"Special case: Registering if_then_steps_action.py as 'ifThenSteps'")
                # Special case for cleanup_steps_action.py - register as cleanupSteps
                elif action_file == 'cleanup_steps_action':
                    action_type = 'cleanupSteps'
                    self.logger.info(f"Special case: Registering cleanup_steps_action.py as 'cleanupSteps'")
                # Convert snake_case to camelCase for action type (e.g., get_value -> getValue)
                elif '_' in action_type:
                    parts = action_type.split('_')
                    action_type = parts[0] + ''.join(part.capitalize() for part in parts[1:])

                # Get the class name (expected to be CamelCase of the filename)
                class_name = ''.join(word.capitalize() for word in action_file.split('_'))

                # Get the class from the module
                if hasattr(module, class_name):
                    action_class = getattr(module, class_name)
                    # Create an instance and register it
                    self.action_handlers[action_type] = action_class(self.controller)
                    self.logger.info(f"Registered action handler for '{action_type}'")
            except Exception as e:
                self.logger.error(f"Error loading action handler from {action_file}: {e}")

    def _register_basic_actions(self):
        """Manually register basic actions to test functionality"""
        try:
            # Create simple action handlers for testing
            class BasicTapAction:
                def __init__(self, controller):
                    self.controller = controller
                    self.logger = logging.getLogger(__name__)

                def set_controller(self, controller):
                    self.controller = controller

                def execute(self, params):
                    self.logger.info(f"BasicTapAction executed with params: {params}")
                    # Try to perform a basic tap if coordinates are provided
                    if self.controller and 'x' in params and 'y' in params:
                        try:
                            x, y = int(params['x']), int(params['y'])
                            self.controller.tap(x, y)
                            self.logger.info(f"Performed tap at ({x}, {y})")
                        except Exception as e:
                            self.logger.error(f"Error performing tap: {e}")
                    return {"status": "success", "message": "Basic tap action completed"}

            class BasicAddLogAction:
                def __init__(self, controller):
                    self.controller = controller
                    self.logger = logging.getLogger(__name__)

                def set_controller(self, controller):
                    self.controller = controller

                def execute(self, params):
                    message = params.get('message', 'Log message')
                    self.logger.info(f"BasicAddLogAction executed: {message}")
                    return {"status": "success", "message": f"Logged: {message}"}

            class BasicWaitAction:
                def __init__(self, controller):
                    self.controller = controller
                    self.logger = logging.getLogger(__name__)

                def set_controller(self, controller):
                    self.controller = controller

                def execute(self, params):
                    import time
                    wait_time = float(params.get('time', 1))
                    self.logger.info(f"BasicWaitAction: waiting {wait_time} seconds")
                    time.sleep(wait_time)
                    return {"status": "success", "message": f"Waited {wait_time} seconds"}

            # Register the basic actions
            self.action_handlers['tap'] = BasicTapAction(self.controller)
            self.action_handlers['wait'] = BasicWaitAction(self.controller)
            self.logger.info("Registered basic actions: tap, wait")

        except Exception as e:
            self.logger.error(f"Error registering basic actions: {e}")

    @db_track_action_factory
    def execute_action(self, action_type, params):
        """
        Execute an action by type with the given parameters

        Args:
            action_type: The type of action to execute (e.g., 'text', 'tap')
            params: Dictionary of parameters for the action

        Returns:
            dict: Result of the action with status and message
        """
        self.logger.info(f"Requested action type: '{action_type}', Available types: {sorted(list(self.action_handlers.keys()))}")
        self.logger.info(f"Action parameters before env resolution: {params}")

        # Apply enhanced environment variable resolution to all string parameters
        try:
            from app_android.utils.environment_resolver import resolve_text_with_env_variables, get_active_environment_id

            # Try multiple methods to get the current environment ID
            current_env_id = None
            try:
                # Try Flask session first
                from flask import session
                current_env_id = session.get('current_environment_id')
                self.logger.debug(f"Retrieved environment ID from Flask session: {current_env_id}")
            except ImportError:
                self.logger.debug("Flask not available, trying database fallback")
            except RuntimeError as re:
                # This happens when we're outside of Flask app context
                self.logger.debug(f"Flask app context not available: {re}, trying database fallback")
            except Exception as env_err:
                self.logger.debug(f"Could not get current environment ID from session: {env_err}, trying database fallback")

            # Fallback to database active environment if Flask session failed
            if current_env_id is None:
                try:
                    current_env_id = get_active_environment_id()
                    self.logger.debug(f"Retrieved environment ID from database: {current_env_id}")
                except Exception as db_err:
                    self.logger.debug(f"Could not get active environment ID from database: {db_err}")

            if current_env_id is not None:
                self.logger.info(f"Applying enhanced environment variable resolution for env ID {current_env_id} to action params")
                substitution_count = 0
                for key, value in params.items():
                    if isinstance(value, str):
                        original_value = value
                        try:
                            resolved_value = resolve_text_with_env_variables(value, current_env_id)
                            if resolved_value != original_value:
                                params[key] = resolved_value
                                substitution_count += 1
                                self.logger.info(f"  ✓ Resolved env var in param '{key}': '{original_value}' -> '{resolved_value}'")
                            else:
                                self.logger.debug(f"  - No substitution needed for param '{key}': '{original_value}'")
                        except NameError as ne:
                            self.logger.warning(f"NameError during environment variable resolution for param '{key}': {ne}")
                            self.logger.warning(f"Keeping original value for param '{key}': '{original_value}'")
                        except Exception as ve:
                            self.logger.warning(f"Error resolving environment variable for param '{key}': {ve}")
                            self.logger.warning(f"Keeping original value for param '{key}': '{original_value}'")

                self.logger.info(f"Environment variable resolution completed: {substitution_count} substitutions made")
                self.logger.info(f"Action parameters after env resolution: {params}")
            else:
                self.logger.warning("No active environment found (checked Flask session and database). Skipping env var resolution for action params.")
        except Exception as e:
            self.logger.error(f"Critical error in environment variable resolution system: {e}")
            self.logger.warning(f"Continuing with original parameters: {params}")
            # Continue execution even if env resolution fails

        # Special case for hookAction - try both hookAction and hook
        if action_type == 'hookAction' and action_type not in self.action_handlers and 'hook' in self.action_handlers:
            self.logger.info(f"Using 'hook' handler for 'hookAction' type")
            action_type = 'hook'

        # Special case for doubleTap - try both doubleTap and double_tap
        if action_type == 'doubleTap' and action_type not in self.action_handlers:
            # Try alternative names
            alternatives = ['double_tap', 'doubleTap', 'doubleClick', 'double_click']
            for alt in alternatives:
                if alt in self.action_handlers:
                    self.logger.info(f"Using '{alt}' handler for 'doubleTap' type")
                    action_type = alt
                    break

        # Special case for repeatSteps - try both repeatSteps and repeat_steps
        if action_type == 'repeatSteps' and action_type not in self.action_handlers:
            # Try alternative names
            alternatives = ['repeat_steps', 'repeatSteps']
            for alt in alternatives:
                if alt in self.action_handlers:
                    self.logger.info(f"Using '{alt}' handler for 'repeatSteps' type")
                    action_type = alt
                    break

        # Special case for tapOnText - try both tapOnText and tap_on_text
        if action_type == 'tapOnText' and action_type not in self.action_handlers:
            # Try alternative names
            alternatives = ['tap_on_text', 'tapOnText', 'tapText']
            for alt in alternatives:
                if alt in self.action_handlers:
                    self.logger.info(f"Using '{alt}' handler for 'tapOnText' type")
                    action_type = alt
                    break

        # Special case for iosFunctions - try both iosFunctions and ios_functions
        if action_type == 'iosFunctions' and action_type not in self.action_handlers:
            # Try alternative names
            alternatives = ['ios_functions', 'iosFunctions', 'iosFunction']
            for alt in alternatives:
                if alt in self.action_handlers:
                    self.logger.info(f"Using '{alt}' handler for 'iosFunctions' type")
                    action_type = alt
                    break

        # Special case for exists - try both exists and checkIfExists
        if action_type in ['exists', 'checkIfExists'] and action_type not in self.action_handlers:
            # Try alternative names
            alternatives = ['exists', 'checkIfExists', 'check_if_exists']
            for alt in alternatives:
                if alt in self.action_handlers:
                    self.logger.info(f"Using '{alt}' handler for '{action_type}' type")
                    action_type = alt
                    break

        if action_type in self.action_handlers:
            # Make sure the handler has the latest controller
            self.action_handlers[action_type].set_controller(self.controller)

            # Execute the action
            try:
                return self.action_handlers[action_type].execute(params)
            except Exception as e:
                self.logger.error(f"Error executing {action_type} action: {e}")
                return {"status": "error", "message": f"Action execution failed: {str(e)}"}
        else:
            # Additional logging to help diagnose missing action types
            self.logger.error(f"No handler found for action type: '{action_type}'")
            self.logger.info(f"Available action types: {sorted(list(self.action_handlers.keys()))}")
            self.logger.info("Re-scanning action handlers directory...")
            self._discover_action_handlers()
            self.logger.info(f"After re-scan, available action types: {sorted(list(self.action_handlers.keys()))}")

            # Check again after re-scan
            if action_type in self.action_handlers:
                self.logger.info(f"Action type '{action_type}' found after re-scan, executing...")
                self.action_handlers[action_type].set_controller(self.controller)
                try:
                    return self.action_handlers[action_type].execute(params)
                except Exception as e:
                    self.logger.error(f"Error executing {action_type} action after re-scan: {e}")
                    return {"status": "error", "message": f"Action execution failed after re-scan: {str(e)}"}
            else:
                return {"status": "error", "message": f"Unsupported action type: {action_type}"}

    def _ensure_double_tap_registered(self):
        """Ensure that the doubleTap action is properly registered"""
        if 'doubleTap' not in self.action_handlers:
            try:
                import sys
                import os

                # Get the current directory (actions folder)
                current_dir = os.path.dirname(os.path.abspath(__file__))

                # Add the actions directory to sys.path if not already there
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)

                # Try to import the DoubleTapAction class directly
                from double_tap_action import DoubleTapAction
                self.logger.info("Manually registering DoubleTapAction as 'doubleTap'")
                self.action_handlers['doubleTap'] = DoubleTapAction(self.controller)
            except Exception as e:
                self.logger.error(f"Error manually registering DoubleTapAction: {e}")

                # If direct import fails, try to find it in existing handlers
                for action_type, handler in list(self.action_handlers.items()):
                    if handler.__class__.__name__ == 'DoubleTapAction':
                        self.logger.info(f"Found DoubleTapAction registered as '{action_type}', adding alias 'doubleTap'")
                        self.action_handlers['doubleTap'] = handler
                        break
                    elif 'double' in action_type.lower() or 'tap' in action_type.lower():
                        self.logger.info(f"Found potential double tap handler '{action_type}', adding alias 'doubleTap'")
                        self.action_handlers['doubleTap'] = handler
                        break

    def set_controller(self, controller):
        """
        Update the controller for all action handlers

        Args:
            controller: The device controller to use
        """
        self.controller = controller
        # Update controller for all registered handlers
        for handler in self.action_handlers.values():
            handler.set_controller(controller)