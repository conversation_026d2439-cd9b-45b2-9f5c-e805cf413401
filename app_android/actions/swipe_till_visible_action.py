from .base_action import BaseAction
import time
import os
import logging
from pathlib import Path
import sys

# Import text detection utility with fallback
try:
    from app_android.utils.text_detection import detect_text_in_image, scale_coordinates
except ImportError:
    try:
        from ..utils.text_detection import detect_text_in_image, scale_coordinates
    except ImportError:
        # Create dummy text_detection functions
        def detect_text_in_image(*args, **kwargs):
            return False, None
        def scale_coordinates(*args, **kwargs):
            return None

class SwipeTillVisibleAction(BaseAction):
    """Handler for swipe till visible actions"""
    
    def execute(self, params):
        """
        Execute swipe till visible action
        
        Args:
            params: Dictionary containing:
                - vector_start: Starting vector as percentage [x, y] (0-1 range)
                - vector_end: Ending vector as percentage [x, y] (0-1 range)
                - duration: Duration of swipe in milliseconds
                - count: Number of swipes to perform
                - interval: Time interval between swipes in seconds
                - locator_type: Type of locator (id, xpath, etc.)
                - locator_value: Value of the locator
                - image_filename: (Optional) Reference image to look for
                - threshold: (Optional) Similarity threshold for image matching
                - timeout: (Optional) Timeout for finding the element
                - text_to_find: (Optional) Text to find on screen
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        # Get parameters with default values
        vector_start = params.get('vector_start', [0.5, 0.5])
        vector_end = params.get('vector_end', [0.5, 0.7])
        duration = params.get('duration', 300)  # Default 300ms
        count = int(params.get('count', 5))  # Default 5 swipes
        interval = float(params.get('interval', 0.5))  # Default 0.5s between swipes
        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        image_filename = params.get('image_filename')
        threshold = float(params.get('threshold', 0.7))  # Default threshold 0.7
        timeout = int(params.get('timeout', 3))  # Default timeout 3s for faster swipe execution
        text_to_find = params.get('text_to_find')
        
        # Log the parameters for debugging
        self.logger.info(f"SwipeTillVisible params - locator_type: '{locator_type}', locator_value: '{locator_value}', image_filename: '{image_filename}', text_to_find: '{text_to_find}'")
        
        try:
            # Ensure vectors are in 0-1 range (convert if they're percentages)
            if vector_start[0] > 1 or vector_start[1] > 1:
                self.logger.info(f"Converting vector_start from percentage to relative: {vector_start}")
                vector_start = [val/100 if val <= 100 else val for val in vector_start]
            
            if vector_end[0] > 1 or vector_end[1] > 1:
                self.logger.info(f"Converting vector_end from percentage to relative: {vector_end}")
                vector_end = [val/100 if val <= 100 else val for val in vector_end]
            
            # Get screen dimensions
            screen_size = self.controller.get_device_dimensions()
            if not screen_size:
                screen_size = (1080, 1920)  # Default fallback size
                self.logger.warning(f"Could not get device dimensions, using default: {screen_size}")
            
            # Convert relative coordinates to absolute
            start_x = int(vector_start[0] * screen_size[0])
            start_y = int(vector_start[1] * screen_size[1])
            end_x = int(vector_end[0] * screen_size[0])
            end_y = int(vector_end[1] * screen_size[1])
            
            self.logger.info(f"Swiping from ({start_x}, {start_y}) to ({end_x}, {end_y}), duration={duration}ms, count={count}, interval={interval}s")
            
            # Convert duration from ms to seconds for Airtest API
            duration_sec = duration / 1000.0
            
            # Perform swipes and check for element after each swipe
            for i in range(count):
                self.logger.info(f"Swipe {i+1}/{count}")
                
                # Check if element is already visible before swiping
                if i == 0:
                    self.logger.info(f"Checking if element is already visible before first swipe...")
                    if self._is_element_visible(locator_type, locator_value, image_filename, text_to_find, threshold, timeout):
                        self.logger.info(f"Element already visible, no swipe needed")
                        return {"status": "success", "message": f"Element already visible, no swipe needed"}
                
                # Execute the swipe
                if hasattr(self.controller, '_ensure_airtest_connected') and self.controller._ensure_airtest_connected():
                    # Using Airtest API directly
                    try:
                        import airtest.core.api as airtest_api
                        airtest_api.swipe(vector_start, vector_end, duration=duration_sec)
                        self.logger.info(f"Swiped from {vector_start} to {vector_end} using Airtest (swipe {i+1}/{count})")
                    except Exception as e:
                        self.logger.error(f"Airtest swipe failed: {e}, falling back to regular swipe")
                        result = self.controller.swipe(start_x, start_y, end_x, end_y, duration)
                else:
                    # Use regular device controller
                    result = self.controller.swipe(start_x, start_y, end_x, end_y, duration)
                
                # Wait for the specified interval
                time.sleep(interval)
                
                # Check if element is visible after swiping
                self.logger.info(f"Checking if element is visible after swipe {i+1}/{count}...")
                if self._is_element_visible(locator_type, locator_value, image_filename, text_to_find, threshold, timeout):
                    self.logger.info(f"Element found after swipe {i+1}/{count}!")
                    return {"status": "success", "message": f"Element visible after {i+1} swipe(s)"}
                else:
                    self.logger.info(f"Element not found after swipe {i+1}/{count}, continuing...")
            
            # If we get here, element was not found after all swipes
            return {"status": "error", "message": f"Element not visible after {count} swipe(s)"}
                
        except Exception as e:
            self.logger.error(f"Error executing swipe till visible action: {e}")
            return {"status": "error", "message": f"Swipe till visible action failed: {str(e)}"}
    
    def _is_element_visible(self, locator_type, locator_value, image_filename=None, text_to_find=None, threshold=0.7, timeout=2):
        """
        Check if an element is visible on the screen
        
        Args:
            locator_type: Type of locator (id, xpath, etc.)
            locator_value: Value of the locator
            image_filename: (Optional) Reference image to look for
            text_to_find: (Optional) Text to find on screen
            threshold: Similarity threshold for image matching
            timeout: Timeout for finding the element
            
        Returns:
            bool: True if element is visible, False otherwise
        """
        try:
            # Priority 1: Check for image if provided
            if image_filename:
                self.logger.info(f"Checking for image: {image_filename}")
                try:
                    # Use Airtest's wait() function with a short timeout
                    from airtest.core.api import wait, Template, TargetNotFoundError
                    
                    # Resolve the image path
                    image_path = image_filename
                    
                    # Make sure image has a file extension
                    if not os.path.splitext(image_path)[1]:
                        self.logger.warning(f"Image parameter '{image_path}' has no file extension, trying to add .png")
                        image_path = f"{image_path}.png"
                    
                    # Try multiple paths to find the image
                    potential_paths = [
                        image_path,  # Direct path as provided
                        os.path.abspath(image_path),  # Absolute path
                        os.path.join('reference_images', image_path),  # In reference_images folder
                        os.path.join('app', 'reference_images', image_path),  # In app/reference_images folder
                    ]
                    
                    template = None
                    for path in potential_paths:
                        if os.path.exists(path):
                            self.logger.info(f"Found image at path: {path}")
                            template = Template(path, threshold=threshold)
                            break
                    
                    if not template:
                        self.logger.error(f"Could not find image at any of these paths: {potential_paths}")
                        return False
                    
                    # Try to find the image with a short timeout
                    try:
                        position = wait(template, timeout=timeout)
                        self.logger.info(f"Image found at position: {position}")
                        return True
                    except TargetNotFoundError:
                        self.logger.info(f"Image not found on screen")
                        return False
                    
                except Exception as e:
                    self.logger.error(f"Airtest image check failed: {e}, attempting OpenCV fallback")
                    
                    # OpenCV fallback for image detection
                    try:
                        import cv2
                        import numpy as np
                        from PIL import Image
                        import io
                        import base64
                        
                        # Take screenshot using Appium driver
                        screenshot_b64 = self.controller.driver.get_screenshot_as_base64()
                        screenshot_data = base64.b64decode(screenshot_b64)
                        screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                        screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)
                        
                        # Get device dimensions
                        window_size = self.controller.driver.get_window_size()
                        device_width = window_size['width']
                        device_height = window_size['height']
                        
                        self.logger.info(f"Screenshot dimensions: {screenshot_cv.shape[1]}x{screenshot_cv.shape[0]}")
                        self.logger.info(f"Device dimensions: {device_width}x{device_height}")
                        
                        # Find template image path
                        template_path = None
                        image_path = image_filename
                        if not os.path.splitext(image_path)[1]:
                            image_path = f"{image_path}.png"
                        
                        potential_paths = [
                            image_path,
                            os.path.abspath(image_path),
                            os.path.join('reference_images', image_path),
                            os.path.join('app_android', 'reference_images', image_path),
                        ]
                        
                        for path in potential_paths:
                            if os.path.exists(path):
                                template_path = path
                                break
                        
                        if not template_path:
                            self.logger.error(f"Template image not found at any path: {potential_paths}")
                            return False
                        
                        # Load template image
                        template_cv = cv2.imread(template_path)
                        if template_cv is None:
                            self.logger.error(f"Failed to load template image: {template_path}")
                            return False
                        
                        self.logger.info(f"Template dimensions: {template_cv.shape[1]}x{template_cv.shape[0]}")
                        
                        # Perform template matching with multiple methods
                        methods = [cv2.TM_CCOEFF_NORMED, cv2.TM_CCORR_NORMED, cv2.TM_SQDIFF_NORMED]
                        best_match_val = -1
                        best_match_loc = None
                        best_method = None
                        
                        for method in methods:
                            result = cv2.matchTemplate(screenshot_cv, template_cv, method)
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                            
                            if method == cv2.TM_SQDIFF_NORMED:
                                match_val = 1 - min_val
                                match_loc = min_loc
                            else:
                                match_val = max_val
                                match_loc = max_loc
                            
                            self.logger.info(f"Method {method}: match_val = {match_val}")
                            
                            if match_val > best_match_val:
                                best_match_val = match_val
                                best_match_loc = match_loc
                                best_method = method
                        
                        # Dynamic threshold adjustment
                        adjusted_threshold = max(0.6, threshold - 0.1)
                        self.logger.info(f"Best match: {best_match_val} (threshold: {adjusted_threshold}) using method {best_method}")
                        
                        if best_match_val >= adjusted_threshold:
                            # Calculate center coordinates
                            template_h, template_w = template_cv.shape[:2]
                            center_x = best_match_loc[0] + template_w // 2
                            center_y = best_match_loc[1] + template_h // 2
                            
                            self.logger.info(f"OpenCV: Image found at center coordinates: ({center_x}, {center_y})")
                            
                            # Save debug image
                            debug_image = screenshot_cv.copy()
                            cv2.rectangle(debug_image, best_match_loc, 
                                         (best_match_loc[0] + template_w, best_match_loc[1] + template_h), 
                                         (0, 255, 0), 2)
                            debug_path = f"debug_images/swipe_opencv_match_{int(time.time())}.png"
                            os.makedirs(os.path.dirname(debug_path), exist_ok=True)
                            cv2.imwrite(debug_path, debug_image)
                            self.logger.info(f"Debug image saved: {debug_path}")
                            
                            return True
                        else:
                            self.logger.info(f"OpenCV: Image not found (best match: {best_match_val} < threshold: {adjusted_threshold})")
                            return False
                            
                    except Exception as opencv_e:
                        self.logger.error(f"OpenCV fallback failed: {opencv_e}")
                        return False
            
            # Priority 2: Check for text if provided
            elif text_to_find:
                self.logger.info(f"Checking for text: {text_to_find}")
                try:
                    # Take a screenshot
                    screenshot_path = self.controller.take_screenshot()
                    if not screenshot_path:
                        self.logger.error("Failed to take screenshot for text detection")
                        return False
                    
                    # Use text detection to find the text
                    text_found, text_coords = detect_text_in_image(screenshot_path, text_to_find)
                    if text_found:
                        self.logger.info(f"Text '{text_to_find}' found at {text_coords}")
                        return True
                    else:
                        self.logger.info(f"Text '{text_to_find}' not found on screen")
                        return False
                    
                except Exception as e:
                    self.logger.error(f"Error checking for text: {e}")
                    return False
            
            # Priority 3: Check for element using locator
            elif locator_type and locator_value:
                self.logger.info(f"Checking for element with {locator_type}: {locator_value} (timeout: {timeout}s)")
                start_time = time.time()
                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                elapsed_time = time.time() - start_time
                if element is not None:
                    self.logger.info(f"Element found in {elapsed_time:.2f}s")
                    return True
                else:
                    self.logger.info(f"Element not found after {elapsed_time:.2f}s")
                    return False
            
            # If no valid check method is provided
            else:
                self.logger.error("No valid check method provided (image, text, or locator)")
                return False
            
        except Exception as e:
            self.logger.error(f"Error in _is_element_visible: {e}")
            return False
