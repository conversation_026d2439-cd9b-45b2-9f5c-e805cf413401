from .base_action import BaseAction

class ClickElementAction(BaseAction):
    """Handler for clicking on UI elements identified by locator"""

    def execute(self, params):
        """
        Execute click element action

        Args:
            params: Dictionary containing:
                - locator_type: Type of locator (id, xpath, accessibility_id, etc.)
                - locator_value: Value of the locator
                - fallback_locators: (Optional) List of fallback locators to try if the primary locator fails
                  Format: [{"locator_type": "id", "locator_value": "value1"},
                           {"locator_type": "xpath", "locator_value": "//some/xpath"}]
                - timeout: (Optional) Maximum time to wait before click in seconds
                - wait_visibility: (Optional) Whether to wait for element to be visible before clicking

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        fallback_locators = params.get('fallback_locators', [])
        # Get timeout from global settings or use provided value
        default_timeout = self.get_global_timeout()
        timeout = params.get('timeout', default_timeout)
        wait_visibility = params.get('wait_visibility', True)  # Default to waiting

        # Check if we have multiple locators to try
        has_fallbacks = isinstance(fallback_locators, list) and len(fallback_locators) > 0

        # If no primary locator is provided but we have fallbacks, that's okay
        if (not locator_type or not locator_value) and not has_fallbacks:
            return {"status": "error", "message": "Missing required parameters: locator_type and locator_value or fallback_locators"}

        try:
            # Prepare the list of locators to try
            locators_to_try = []

            # Add primary locator if provided
            if locator_type and locator_value:
                locators_to_try.append({
                    'locator_type': locator_type,
                    'locator_value': locator_value
                })

            # Add fallback locators if provided
            if has_fallbacks:
                locators_to_try.extend(fallback_locators)

            # Log the locators we're going to try
            self.logger.info(f"Will try {len(locators_to_try)} locators for element click")

            # Check if controller has find_element_with_fallback method
            if hasattr(self.controller, 'find_element_with_fallback') and len(locators_to_try) > 1:
                self.logger.info("Using find_element_with_fallback method")
                element = self.controller.find_element_with_fallback(locators_to_try, timeout=timeout)

                if element:
                    try:
                        element.click()
                        # Find which locator succeeded for the message
                        success_locator = locators_to_try[0]  # Default to first one for message
                        return {
                            "status": "success",
                            "message": f"Element clicked using fallback locator system"
                        }
                    except Exception as click_e:
                        self.logger.error(f"Error clicking element found with fallback locators: {click_e}")
                        return {
                            "status": "error",
                            "message": f"Failed to click element found with fallback locators: {click_e}"
                        }
                else:
                    return {
                        "status": "error",
                        "message": f"Element could not be located with any of the {len(locators_to_try)} provided locators"
                    }

            # Try using universal locator finding method first (includes UISelector support)
            elif hasattr(self.controller, 'driver') and self.controller.driver:
                # Try each locator in sequence using universal method
                for i, locator_info in enumerate(locators_to_try):
                    current_locator_type = locator_info.get('locator_type')
                    current_locator_value = locator_info.get('locator_value')

                    self.logger.info(f"Trying locator {i+1}/{len(locators_to_try)}: {current_locator_type}='{current_locator_value}'")

                    # Try universal locator finding method
                    element = self.find_element_with_locator(
                        current_locator_type,
                        current_locator_value,
                        timeout // len(locators_to_try)
                    )

                    if element:
                        try:
                            element.click()
                            return {
                                "status": "success",
                                "message": f"Element clicked successfully using {current_locator_type}='{current_locator_value}'"
                            }
                        except Exception as click_e:
                            self.logger.warning(f"Click failed for {current_locator_type}='{current_locator_value}': {click_e}")
                            continue  # Try next locator

                # If universal method didn't work, try controller's click_element method
                if hasattr(self.controller, 'click_element'):
                    for i, locator_info in enumerate(locators_to_try):
                        current_locator_type = locator_info.get('locator_type')
                        current_locator_value = locator_info.get('locator_value')

                        self.logger.info(f"Fallback: Trying locator {i+1}/{len(locators_to_try)}: {current_locator_type}='{current_locator_value}'")

                        result = self.controller.click_element(
                            locator_type=current_locator_type,
                            locator_value=current_locator_value,
                            timeout=timeout // len(locators_to_try),  # Divide timeout among locators
                            wait_visibility=wait_visibility
                        )

                    # Handle different return types
                    if isinstance(result, dict) and result.get('status') == 'success':
                        return result
                    elif isinstance(result, bool) and result:
                        return {
                            "status": "success",
                            "message": f"Element clicked: {current_locator_type}='{current_locator_value}'"
                        }
                    elif result is not None and not (isinstance(result, bool) and not result) and not (isinstance(result, dict) and result.get('status') != 'success'):
                        # Assume success if any non-None/False value is returned
                        return {
                            "status": "success",
                            "message": f"Element clicked: {current_locator_type}='{current_locator_value}'"
                        }

                    # If we get here, this locator failed, try the next one
                    self.logger.info(f"Locator {i+1} failed, trying next locator if available")

                # If we get here, all locators failed
                return {
                    "status": "error",
                    "message": f"Failed to click element with any of the {len(locators_to_try)} provided locators"
                }

            # If the controller has an Appium driver directly available
            elif hasattr(self.controller, 'driver') and self.controller.driver:
                driver = self.controller.driver
                element = None

                # Try each locator in sequence
                for i, locator_info in enumerate(locators_to_try):
                    current_locator_type = locator_info.get('locator_type')
                    current_locator_value = locator_info.get('locator_value')

                    self.logger.info(f"Trying locator {i+1}/{len(locators_to_try)}: {current_locator_type}='{current_locator_value}'")

                    # First, find the element (with waiting if enabled)
                    if wait_visibility and self._has_selenium_support():
                        try:
                            from selenium.webdriver.support.ui import WebDriverWait
                            from selenium.webdriver.support import expected_conditions as EC
                            from selenium.webdriver.common.by import By
                            from selenium.common.exceptions import TimeoutException

                            # Map locator types to Selenium/Appium By types
                            locator_map = {
                                'id': By.ID,
                                'xpath': By.XPATH,
                                'name': By.NAME,
                                'class': By.CLASS_NAME,
                                'accessibility_id': 'accessibility id',  # Appium specific
                                'ios_predicate': '-ios predicate string',  # iOS specific
                                'android_uiautomator': '-android uiautomator',  # Android specific
                                'ios_class_chain': '-ios class chain'  # iOS specific
                            }

                            by_type = locator_map.get(current_locator_type.lower())
                            if not by_type:
                                by_type = current_locator_type  # Use as-is if not in mapping

                            # Wait for element to be clickable
                            locator_timeout = timeout // len(locators_to_try)  # Divide timeout among locators
                            element = WebDriverWait(driver, locator_timeout).until(
                                EC.element_to_be_clickable((by_type, current_locator_value))
                            )

                            # If we found the element, break the loop
                            if element:
                                self.logger.info(f"Element found with locator {i+1}: {current_locator_type}='{current_locator_value}'")
                                break

                        except TimeoutException:
                            self.logger.info(f"Element not found with locator {i+1}: {current_locator_type}='{current_locator_value}'")
                            # Continue to next locator
                            continue
                        except Exception as inner_e:
                            self.logger.error(f"Error waiting for element with locator {i+1}: {inner_e}")
                            # Continue to next locator
                            continue
                    else:
                        # Try to find element without explicit wait
                        try:
                            # Handle Appium-specific locator types
                            if current_locator_type.lower() == 'accessibility_id':
                                element = driver.find_element_by_accessibility_id(current_locator_value)
                            elif current_locator_type.lower() == 'ios_predicate':
                                element = driver.find_element_by_ios_predicate(current_locator_value)
                            elif current_locator_type.lower() == 'android_uiautomator':
                                element = driver.find_element_by_android_uiautomator(current_locator_value)
                            elif current_locator_type.lower() == 'ios_class_chain':
                                element = driver.find_element_by_ios_class_chain(current_locator_value)
                            # Standard Selenium locators
                            elif current_locator_type.lower() == 'id':
                                element = driver.find_element_by_id(current_locator_value)
                            elif current_locator_type.lower() == 'xpath':
                                element = driver.find_element_by_xpath(current_locator_value)
                            elif current_locator_type.lower() == 'name':
                                element = driver.find_element_by_name(current_locator_value)
                            elif current_locator_type.lower() == 'class':
                                element = driver.find_element_by_class_name(current_locator_value)
                            else:
                                # Try modern syntax if available
                                try:
                                    from selenium.webdriver.common.by import By
                                    # Map locator type to By enum
                                    locator_map = {
                                        'id': By.ID,
                                        'xpath': By.XPATH,
                                        'name': By.NAME,
                                        'class': By.CLASS_NAME,
                                        'tag': By.TAG_NAME,
                                        'css': By.CSS_SELECTOR,
                                        'link_text': By.LINK_TEXT,
                                        'partial_link_text': By.PARTIAL_LINK_TEXT
                                    }
                                    by_type = locator_map.get(current_locator_type.lower())
                                    if by_type:
                                        element = driver.find_element(by_type, current_locator_value)
                                    else:
                                        # Try to use the locator type as-is
                                        element = driver.find_element(current_locator_type, current_locator_value)
                                except Exception as find_e:
                                    self.logger.error(f"Error finding element with locator {i+1}: {find_e}")
                                    # Continue to next locator
                                    continue

                            # If we found the element, break the loop
                            if element:
                                self.logger.info(f"Element found with locator {i+1}: {current_locator_type}='{current_locator_value}'")
                                break

                        except Exception as e:
                            self.logger.error(f"Error finding element with locator {i+1}: {e}")
                            # Continue to next locator
                            continue

                # Click the element if found
                if element:
                    try:
                        element.click()
                        return {
                            "status": "success",
                            "message": f"Element clicked successfully using one of the provided locators"
                        }
                    except Exception as click_e:
                        self.logger.error(f"Error clicking element: {click_e}")
                        return {
                            "status": "error",
                            "message": f"Failed to click element: {click_e}"
                        }
                else:
                    return {
                        "status": "error",
                        "message": f"Element could not be located with any of the {len(locators_to_try)} provided locators"
                    }
            else:
                return {
                    "status": "error",
                    "message": "Device controller doesn't support element click operations"
                }

        except Exception as e:
            self.logger.error(f"Error executing click element action: {e}")
            return {"status": "error", "message": f"Click element action failed: {str(e)}"}

    def _has_selenium_support(self):
        """Check if Selenium WebDriverWait is available"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            return True
        except ImportError:
            return False