import os
import traceback
import time
import threading
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class TapIfLocatorExistsAction(BaseAction):
    def __init__(self, controller=None):
        super().__init__(controller)
        self.action_type = 'tapIfLocatorExists'  # Set action type for timeout handling

    def execute(self, params):
        """
        Execute the Tap If Locator Exists action.
        This action will tap on an element if it exists on the screen using locators, and do nothing if it doesn't.

        Args:
            params (dict): Parameters for the action
                - locator_type (str): Type of locator (id, xpath, accessibility_id, class_name, etc.)
                - locator_value (str): Value of the locator
                - timeout (int, optional): Maximum time to wait for the element to appear in seconds. Default is 10

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
        """
        try:
            # Check if controller is available
            if not self.controller:
                # Always return success for "if exists" behavior
                return {"status": "success", "message": "Device controller not available - step passed (if exists behavior)"}

            # Get parameters with intelligent timeout
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            user_timeout = int(params.get('timeout', 10))

            # Use intelligent timeout based on element criticality
            timeout = self.get_intelligent_timeout(locator_value, self.action_type, user_timeout)

            if not locator_type or not locator_value:
                # Always return success for "if exists" behavior
                return {"status": "success", "message": "Locator type and value are required - step passed (if exists behavior)"}

            self.logger.info(f"Intelligent tap if locator exists: {locator_type}={locator_value} (timeout: {timeout}s, user_timeout: {user_timeout}s)")

            # STRICT TIMEOUT ENFORCEMENT using threading
            def find_element_with_timeout():
                """Find element in a separate thread"""
                try:
                    # Use fast element finding with intelligent timeout (includes 30-second override for critical elements)
                    if hasattr(self.controller, 'find_element_fast'):
                        return self.controller.find_element_fast(locator_type, locator_value, timeout=timeout)
                    elif hasattr(self.controller, '_find_element_with_timeout'):
                        return self.controller._find_element_with_timeout(locator_type, locator_value, timeout=timeout)
                    else:
                        # Fallback to regular method with intelligent timeout
                        return self.controller.find_element(locator_type, locator_value, timeout=timeout)
                except Exception:
                    return None

            # Execute element finding in a thread with strict timeout
            start_time = time.time()
            result = [None]  # Use list to allow modification in thread

            def thread_target():
                result[0] = find_element_with_timeout()

            # Create and start thread
            thread = threading.Thread(target=thread_target)
            thread.daemon = True
            thread.start()

            # Wait for thread to complete or timeout
            thread.join(timeout=timeout)

            elapsed_time = time.time() - start_time
            element = result[0]

            # Check if thread completed within timeout
            if thread.is_alive():
                self.logger.info(f"THREAD TIMEOUT ENFORCED: {elapsed_time:.1f}s >= {timeout}s - element search terminated")
                return {"status": "success", "message": f"Element not found within {timeout}s - step passed (if exists behavior)"}

            # Check for timeout even if thread completed
            if elapsed_time >= timeout:
                self.logger.info(f"ELAPSED TIMEOUT ENFORCED: {elapsed_time:.1f}s >= {timeout}s - element search terminated")
                return {"status": "success", "message": f"Element not found within {timeout}s - step passed (if exists behavior)"}
                
            if element:
                self.logger.info(f"Element found: {locator_type}={locator_value}, attempting to tap...")

                # Simple tap attempt with minimal fallbacks
                try:
                    # Try controller's tap_element method first
                    if hasattr(self.controller, 'tap_element'):
                        tap_result = self.controller.tap_element(locator_type, locator_value, timeout=5)
                        if isinstance(tap_result, dict) and tap_result.get('status') == 'success':
                            return {"status": "success", "message": f"Tapped on element: {locator_type}={locator_value}"}
                        else:
                            return {"status": "success", "message": f"Element found but tap failed - step passed (if exists behavior)"}

                    # Fallback: try to click the element directly
                    elif hasattr(element, 'click'):
                        element.click()
                        return {"status": "success", "message": f"Tapped on element: {locator_type}={locator_value}"}

                    else:
                        return {"status": "success", "message": f"Element found but no tap method - step passed (if exists behavior)"}

                except Exception as tap_err:
                    self.logger.warning(f"Tap failed: {tap_err}")
                    return {"status": "success", "message": f"Element found but tap error - step passed (if exists behavior)"}
            else:
                # Element not found - expected behavior for "if exists"
                self.logger.info(f"Element not found: {locator_type}={locator_value} - step passed (if exists behavior)")
                return {"status": "success", "message": f"Element not found - step passed (if exists behavior)"}
        except Exception as e:
            self.logger.error(f"Error in tap if locator exists action: {e}")
            return {"status": "success", "message": f"Action error - step passed (if exists behavior): {str(e)}"}
