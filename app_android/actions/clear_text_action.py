from .base_action import BaseAction
import logging
from selenium.webdriver.common.keys import Keys

class ClearTextAction(BaseAction):
    """Handler for clearing text in input fields using CTRL+A + DELETE sequence"""

    def execute(self, params):
        """
        Execute clear text action using CTRL+A + DELETE sequence

        Args:
            params: Dictionary containing:
                - method: (Optional) Method to use ('uiautomator', 'appium', 'adb')

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"[CLEAR_TEXT] ClearTextAction.execute called with params: {params}")
        
        if not self.controller:
            self.logger.error("[CLEAR_TEXT] No device controller available")
            return {"status": "error", "message": "No device controller available"}

        # Get the method preference
        method = params.get('method', 'auto')
        
        self.logger.info(f"Clearing text using method: {method}")
        
        try:
            # Update activity time for connection monitoring
            if hasattr(self.controller, '_update_activity_time'):
                self.controller._update_activity_time()
            
            # Try different methods based on preference and availability
            if method == 'auto' or method == 'uiautomator':
                # Method 1: Try UIAutomator2 approach first
                if self._clear_text_uiautomator():
                    return {"status": "success", "message": "Text cleared successfully using UIAutomator2"}
            
            if method == 'auto' or method == 'appium':
                # Method 2: Try Appium driver approach
                if self._clear_text_appium():
                    return {"status": "success", "message": "Text cleared successfully using Appium driver"}
            
            if method == 'auto' or method == 'adb':
                # Method 3: Try ADB shell approach as fallback
                if self._clear_text_adb():
                    return {"status": "success", "message": "Text cleared successfully using ADB shell"}
            
            return {"status": "error", "message": "All clear text methods failed"}
            
        except Exception as e:
            self.logger.error(f"Error clearing text: {str(e)}")
            return {"status": "error", "message": f"Error clearing text: {str(e)}"}

    def _clear_text_uiautomator(self):
        """Clear text using UIAutomator2 approach"""
        try:
            if not hasattr(self.controller, 'driver') or not self.controller.driver:
                return False
            
            driver = self.controller.driver
            
            # Method 1: Try to find the currently focused element and clear it
            try:
                # Get the currently focused element
                focused_element = driver.switch_to.active_element
                if focused_element:
                    # Clear the element directly
                    focused_element.clear()
                    self.logger.info("Successfully cleared text using focused element.clear()")
                    return True
            except Exception as focused_error:
                self.logger.debug(f"Focused element clear failed: {focused_error}")
            
            # Method 2: Try using key combinations
            try:
                # Send CTRL+A to select all text
                driver.press_keycode(29)  # CTRL key
                driver.press_keycode(29, 29)  # CTRL+A (select all)
                
                # Send DELETE key
                driver.press_keycode(67)  # DEL key
                
                self.logger.info("Successfully cleared text using key combinations")
                return True
            except Exception as key_error:
                self.logger.debug(f"Key combination clear failed: {key_error}")
            
            return False
            
        except Exception as e:
            self.logger.debug(f"UIAutomator clear text failed: {e}")
            return False

    def _clear_text_appium(self):
        """Clear text using Appium driver approach"""
        try:
            if not hasattr(self.controller, 'driver') or not self.controller.driver:
                return False
            
            driver = self.controller.driver
            
            # Method 1: Try using Appium's built-in clear functionality
            try:
                # Get the currently active element
                active_element = driver.switch_to.active_element
                if active_element:
                    active_element.clear()
                    self.logger.info("Successfully cleared text using Appium active element clear")
                    return True
            except Exception as active_error:
                self.logger.debug(f"Appium active element clear failed: {active_error}")
            
            # Method 2: Try using send_keys with CTRL+A and DELETE
            try:
                # Send CTRL+A to select all
                driver.execute_script('mobile: shell', {
                    'command': 'input',
                    'args': ['keyevent', 'KEYCODE_CTRL_LEFT', 'KEYCODE_A']
                })
                
                # Send DELETE key
                driver.execute_script('mobile: shell', {
                    'command': 'input',
                    'args': ['keyevent', 'KEYCODE_DEL']
                })
                
                self.logger.info("Successfully cleared text using Appium shell commands")
                return True
            except Exception as shell_error:
                self.logger.debug(f"Appium shell command clear failed: {shell_error}")
            
            # Method 3: Try using Actions API
            try:
                from selenium.webdriver.common.action_chains import ActionChains
                
                actions = ActionChains(driver)
                actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).perform()
                actions.send_keys(Keys.DELETE).perform()
                
                self.logger.info("Successfully cleared text using Appium Actions API")
                return True
            except Exception as actions_error:
                self.logger.debug(f"Appium Actions API clear failed: {actions_error}")
            
            return False
            
        except Exception as e:
            self.logger.debug(f"Appium clear text failed: {e}")
            return False

    def _clear_text_adb(self):
        """Clear text using ADB shell commands as fallback"""
        try:
            if not hasattr(self.controller, 'device_id') or not self.controller.device_id:
                return False
            
            device_id = self.controller.device_id
            
            # Method 1: Try using ADB input keyevent for CTRL+A and DELETE
            try:
                import subprocess
                
                # Send CTRL+A (select all)
                result1 = subprocess.run([
                    'adb', '-s', device_id, 'shell', 'input', 'keyevent', 
                    'KEYCODE_CTRL_LEFT KEYCODE_A'
                ], capture_output=True, text=True, timeout=5)
                
                # Send DELETE key
                result2 = subprocess.run([
                    'adb', '-s', device_id, 'shell', 'input', 'keyevent', 'KEYCODE_DEL'
                ], capture_output=True, text=True, timeout=5)
                
                if result1.returncode == 0 and result2.returncode == 0:
                    self.logger.info("Successfully cleared text using ADB keyevent commands")
                    return True
            except Exception as keyevent_error:
                self.logger.debug(f"ADB keyevent clear failed: {keyevent_error}")
            
            # Method 2: Try alternative ADB approach
            try:
                # Send individual key events
                subprocess.run([
                    'adb', '-s', device_id, 'shell', 'input', 'keyevent', '113'  # CTRL
                ], capture_output=True, text=True, timeout=3)
                
                subprocess.run([
                    'adb', '-s', device_id, 'shell', 'input', 'keyevent', '29'   # A
                ], capture_output=True, text=True, timeout=3)
                
                subprocess.run([
                    'adb', '-s', device_id, 'shell', 'input', 'keyevent', '67'   # DEL
                ], capture_output=True, text=True, timeout=3)
                
                self.logger.info("Successfully cleared text using ADB individual keyevents")
                return True
            except Exception as individual_error:
                self.logger.debug(f"ADB individual keyevent clear failed: {individual_error}")
            
            return False
            
        except Exception as e:
            self.logger.debug(f"ADB clear text failed: {e}")
            return False
