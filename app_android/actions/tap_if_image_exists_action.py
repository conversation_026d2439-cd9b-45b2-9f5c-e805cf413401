from .base_action import BaseAction
from airtest.core.api import Template, wait
from airtest.core.error import TargetNotFoundError
import os
import time
import traceback

class TapIfImageExistsAction(BaseAction):
    """Handler for tap if image exists actions - EXACT copy of TapAction logic"""

    def __init__(self, controller=None):
        super().__init__(controller)
        self.action_type = 'tapIfImageExists'  # Identify as conditional action

    def scale_ios_coordinates(self, coordinates):
        """
        Scale coordinates for iOS devices if needed (EXACT same as TapAction)

        Args:
            coordinates: Tuple of (x, y) coordinates

        Returns:
            tuple: Scaled coordinates
        """
        try:
            x, y = coordinates

            # Get device dimensions
            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                device_width = self.controller.device_dimensions.get('width')
                device_height = self.controller.device_dimensions.get('height')

                if device_width and device_height:
                    # Check if we need to scale (if image dimensions don't match device dimensions)
                    if hasattr(self.controller, 'airtest_device') and self.controller.airtest_device:
                        try:
                            # Get the screen resolution from Airtest device
                            airtest_resolution = self.controller.airtest_device.get_current_resolution()
                            if airtest_resolution and len(airtest_resolution) == 2:
                                airtest_width, airtest_height = airtest_resolution

                                # Only scale if dimensions are different
                                if airtest_width != device_width or airtest_height != device_height:
                                    scale_x = device_width / airtest_width
                                    scale_y = device_height / airtest_height

                                    self.logger.info(f"Scaling coordinates by factors: x={scale_x}, y={scale_y}")
                                    return (int(x * scale_x), int(y * scale_y))
                        except Exception as e:
                            self.logger.warning(f"Error getting Airtest resolution: {e}")

            # Return original coordinates if no scaling needed or if scaling failed
            return coordinates
        except Exception as e:
            self.logger.warning(f"Error in scale_ios_coordinates: {e}")
            return coordinates
    def execute(self, params):
        """
        Execute tap if image exists action - EXACT copy of TapAction image method logic
        The only difference: returns success when image is not found instead of error

        Args:
            params: Dictionary containing:
                - image_filename: Reference image to tap on
                - threshold: (Optional) Matching threshold (default: 0.7)
                - timeout: (Optional) Maximum time to wait in seconds (default: 5)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "No device controller available - step passed (if exists behavior)"}

        # Extract parameters with optimized timeout (max 30 seconds)
        image_filename = params.get('image_filename')
        threshold = float(params.get('threshold', 0.7))
        timeout = min(int(params.get('timeout', 30)), 30)  # Max 30 seconds

        if not image_filename:
            # Always return success for "if exists" behavior
            return {"status": "success", "message": "Image filename is required - step passed (if exists behavior)"}

        self.logger.info(f"Optimized tap if image exists: {image_filename} (threshold: {threshold}, timeout: {timeout}s)")

        # Simple image path resolution
        abs_path = image_filename
        if not os.path.exists(image_filename):
            # Try reference_images folder
            ref_path = os.path.join('reference_images', os.path.basename(image_filename))
            if os.path.exists(ref_path):
                abs_path = ref_path
            else:
                self.logger.info(f"Reference image not found: {image_filename} - step passed (if exists behavior)")
                return {"status": "success", "message": f"Reference image not found - step passed (if exists behavior)"}

        # Simple, fast image detection with timeout (iOS-style approach)
        try:
            from airtest.core.api import connect_device, wait, touch, Template, exists
            from airtest.core.error import TargetNotFoundError
            from airtest.core.helper import G

            # Ensure Airtest device is initialized (same as TapAction)
            if hasattr(self.controller, '_ensure_airtest_connected'):
                self.logger.info("Ensuring Airtest device is connected...")
                airtest_connected = self.controller._ensure_airtest_connected()
                if not airtest_connected:
                    self.logger.error("Failed to connect Airtest device, trying fallback methods")
                    # Don't return error here, continue to fallback methods
            else:
                self.logger.warning("Controller doesn't have _ensure_airtest_connected method")

            # Try to initialize Airtest device if not already initialized (same as TapAction)
            if hasattr(self.controller, '_init_airtest'):
                self.logger.info("Initializing Airtest device...")
                airtest_initialized = self.controller._init_airtest()
                if not airtest_initialized:
                    self.logger.error("Failed to initialize Airtest device, trying fallback methods")
                    # Don't return error here, continue to fallback methods

            # Log the device info to help with debugging (same as TapAction)
            if hasattr(G, 'DEVICE') and G.DEVICE:
                self.logger.info(f"Current Airtest device: {G.DEVICE}")
            else:
                self.logger.warning("No Airtest device available in G.DEVICE")

            template_image = Template(abs_path, threshold=threshold)

            # Use simple wait with timeout
            start_time = time.time()
            try:
                wait_result = wait(template_image, timeout=timeout)
                if wait_result:
                    self.logger.info(f"Image found: {image_filename}, attempting to tap...")
                    # Simple tap using Airtest
                    from airtest.core.api import touch
                    touch(template_image)
                    return {"status": "success", "message": f"Tapped on image: {image_filename}"}

            except TargetNotFoundError:
                # Image not found - expected behavior for "if exists"
                elapsed_time = time.time() - start_time
                self.logger.info(f"Image not found within {timeout}s - step passed (if exists behavior)")
                return {"status": "success", "message": f"Image not found within {timeout}s - step passed (if exists behavior)"}

        except Exception as e:
            self.logger.error(f"Failed to initialize Airtest device, trying fallback methods")
            
            # For ALL errors (including Airtest initialization failures), try OpenCV fallback method
            try:
                self.logger.info(f"Trying OpenCV approach for image recognition: {abs_path}")
                import cv2
                import numpy as np
                from PIL import Image
                import io
                import base64

                # Take a screenshot using Appium
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # Get device dimensions first
                    device_width = None
                    device_height = None
                    if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                        device_width = self.controller.device_dimensions.get('width')
                        device_height = self.controller.device_dimensions.get('height')
                        self.logger.info(f"Device dimensions: {device_width}x{device_height}")

                    # Get screenshot as base64
                    screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                    screenshot_data = base64.b64decode(screenshot_base64)

                    # Convert to PIL Image first
                    screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                    original_size = screenshot_pil.size
                    self.logger.info(f"Screenshot dimensions: {original_size[0]}x{original_size[1]}")

                    # Convert PIL to OpenCV format
                    screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                    # Load template image
                    template = cv2.imread(abs_path)
                    if template is None:
                        self.logger.error(f"Could not load template image: {abs_path}")
                        raise Exception(f"Could not load template image: {abs_path}")

                    h, w = template.shape[:2]
                    self.logger.info(f"Template dimensions: {w}x{h}")

                    # Try multiple template matching methods
                    methods = [
                        (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
                        (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED"),
                        (cv2.TM_SQDIFF_NORMED, "TM_SQDIFF_NORMED")
                    ]

                    best_val = 0
                    best_loc = None
                    best_method = None

                    # Use a lower threshold for OpenCV matching
                    opencv_threshold = max(0.5, threshold - 0.2)  # Lower threshold by 0.2 but not below 0.5
                    self.logger.info(f"Using OpenCV threshold: {opencv_threshold} (original: {threshold})")

                    for method, method_name in methods:
                        # Perform template matching
                        result = cv2.matchTemplate(screenshot_cv, template, method)

                        # Different handling for SQDIFF (lower is better) vs others (higher is better)
                        if method == cv2.TM_SQDIFF_NORMED:
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                            curr_val = 1.0 - min_val  # Convert to same scale as other methods
                            curr_loc = min_loc
                        else:
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                            curr_val = max_val
                            curr_loc = max_loc

                        self.logger.info(f"Template matching with {method_name}: {curr_val}")

                        if curr_val > best_val:
                            best_val = curr_val
                            best_loc = curr_loc
                            best_method = method_name

                    self.logger.info(f"Best template matching result: {best_val} with method {best_method} (threshold: {opencv_threshold})")

                    if best_val >= opencv_threshold:
                        # Match found, calculate center coordinates
                        x = best_loc[0] + w // 2
                        y = best_loc[1] + h // 2

                        self.logger.info(f"Found image at ({x}, {y}) in original screenshot")

                        # Validate coordinates against device dimensions
                        if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                            device_width = self.controller.device_dimensions.get('width')
                            device_height = self.controller.device_dimensions.get('height')

                            # Check if coordinates are within device bounds
                            if device_width and device_height:
                                if x >= original_size[0] or y >= original_size[1]:
                                    self.logger.warning(f"Coordinates ({x}, {y}) are outside screenshot bounds {original_size[0]}x{original_size[1]}")

                                    # Clamp coordinates to screenshot bounds
                                    x = min(x, original_size[0] - 1)
                                    y = min(y, original_size[1] - 1)
                                    self.logger.info(f"Clamped coordinates to ({x}, {y})")

                                # Only scale if dimensions are different and scaling is needed
                                if device_width != original_size[0] or device_height != original_size[1]:
                                    # Calculate scaling factors
                                    scale_x = device_width / original_size[0]
                                    scale_y = device_height / original_size[1]

                                    # Apply scaling
                                    original_x, original_y = x, y
                                    x = int(x * scale_x)
                                    y = int(y * scale_y)

                                    # Ensure coordinates are within device bounds after scaling
                                    x = min(x, device_width - 1)
                                    y = min(y, device_height - 1)

                                    self.logger.info(f"Scaled coordinates from ({original_x}, {original_y}) to ({x}, {y}) for device dimensions {device_width}x{device_height}")

                        # Create a debug image showing the match
                        debug_match_path = os.path.join(os.path.dirname(abs_path), 'debug_match.png')
                        debug_img = screenshot_cv.copy()
                        cv2.rectangle(debug_img, best_loc, (best_loc[0] + w, best_loc[1] + h), (0, 255, 0), 2)
                        cv2.circle(debug_img, (x, y), 5, (0, 0, 255), -1)
                        cv2.imwrite(debug_match_path, debug_img)
                        self.logger.info(f"Saved debug match image to {debug_match_path}")

                        self.logger.info(f"Tapping at ({x}, {y}) using OpenCV image recognition")

                        # Tap at the center using Appium
                        self.controller.driver.tap([(int(x), int(y))])
                        return {"status": "success", "message": f"Tapped at ({x}, {y}) using OpenCV image recognition"}
                    else:
                        self.logger.warning(f"Image not found with OpenCV (best_val: {best_val}, threshold: {opencv_threshold})")
            except ImportError as opencv_e:
                self.logger.warning(f"OpenCV not available: {opencv_e}")
            except Exception as opencv_e:
                self.logger.error(f"Error using OpenCV for image recognition: {opencv_e}")
            
            return {"status": "success", "message": f"Image detection error - step passed (if exists behavior): {str(e)}"}


