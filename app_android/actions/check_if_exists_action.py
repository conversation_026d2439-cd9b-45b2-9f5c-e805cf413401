import os
import traceback
import time
import math
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class CheckIfExistsAction(BaseAction):
    def execute(self, params):
        """
        Execute the Check if Exists action.
        This action checks if an element exists on the screen using various locator types.

        Args:
            params (dict): Parameters for the action
                - locator_type (str): Type of locator (id, xpath, accessibility_id, class, name, text, image)
                - locator_value (str): Value of the locator
                - threshold (float, optional): Similarity threshold for image matching (0.0-1.0). Default is 0.7
                - timeout (int, optional): Maximum time to wait for the element in seconds. Default is 10
                - use_env_var (bool, optional): Whether locator_value is an environment variable name
                - env_var_name (str, optional): Environment variable name if use_env_var is True

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
                - found (bool): Whether the element was found
        """
        try:
            # Check if controller is available
            if not self.controller:
                return {"status": "error", "message": "Device controller not available"}

            # Get parameters
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            threshold = float(params.get('threshold', 0.7))
            timeout = int(params.get('timeout', 10))
            use_env_var = params.get('use_env_var', False)
            env_var_name = params.get('env_var_name')

            if not locator_type:
                return {"status": "error", "message": "Locator type is required"}

            # Handle environment variables
            if use_env_var and env_var_name:
                # Resolve environment variable
                env_value = os.environ.get(env_var_name)
                if env_value is None:
                    return {"status": "error", "message": f"Environment variable '{env_var_name}' not found"}
                locator_value = env_value
                self.logger.info(f"Resolved environment variable '{env_var_name}' to '{locator_value}'")

            if not locator_value:
                return {"status": "error", "message": "Locator value is required"}

            self.logger.info(f"Checking if element exists with {locator_type}: {locator_value}, timeout={timeout}s")

            # Optimized for conditional actions - suspend health checks
            try:
                self._suspend_health_checks_for_operation()

                # Handle image-based existence check
                if locator_type == 'image':
                    return self._check_image_exists(locator_value, threshold, timeout)

                # Handle text-based existence check
                elif locator_type == 'text':
                    return self._check_text_exists(locator_value, timeout)

                # Handle UISelector locator type
                elif locator_type.lower() == 'uiselector':
                    return self._check_uiselector_exists(locator_value, timeout)

                # Handle other locator types (id, xpath, accessibility_id, etc.)
                else:
                    return self._check_element_exists(locator_type, locator_value, timeout)

            finally:
                # Always resume health checks after conditional operation
                self._resume_health_checks_for_operation()

        except Exception as e:
            self.logger.error(f"Error executing Check if Exists action: {e}")
            traceback.print_exc()
            return {"status": "error", "message": f"Check if Exists action failed: {str(e)}"}

    def _check_image_exists(self, image_filename, threshold, timeout):
        """Check if image exists on screen using the EXACT same logic as Tap (Image) action"""
        try:
            self.logger.info(f"Checking if image exists: '{image_filename}' with threshold={threshold}, timeout={timeout}s")

            # Resolve the image path properly (EXACT same logic as Tap Image action)
            if not os.path.exists(image_filename):
                # Try to resolve from reference_images directory
                try:
                    from config_android import DIRECTORIES
                    reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                    if reference_dir:
                        full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                        if os.path.exists(full_path):
                            image_filename = full_path
                            self.logger.info(f"Resolved image path to: {image_filename}")
                        else:
                            # Try directly in reference_images folder
                            ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                            if os.path.exists(ref_path):
                                image_filename = ref_path
                                self.logger.info(f"Resolved image path to: {image_filename}")
                except (ImportError, Exception) as e:
                    self.logger.warning(f"Could not resolve reference image directory: {e}")

            # If still doesn't exist, return error
            if not os.path.exists(image_filename):
                return {
                    "status": "success",
                    "message": f"Reference image not found: {image_filename}",
                    "found": False
                }

            # Get absolute path for more reliable loading (same as Tap Image action)
            abs_path = os.path.abspath(image_filename)
            self.logger.info(f"Using absolute image path: {abs_path}")

            # METHOD 1: Try Airtest Template and exists() - This is the PRIMARY method used by Tap Image
            try:
                from airtest.core.api import exists
                from airtest.core.cv import Template
                from airtest.core.error import TargetNotFoundError

                self.logger.info(f"Using Airtest Template and exists() method (primary method)")

                # Create template and check if it exists (same as Tap Image action)
                template_image = Template(abs_path, threshold=threshold)
                self.logger.info(f"Created Template object: {template_image}")

                # Use exists() to find the image (same as Tap Image action)
                match_pos = exists(template_image)

                if match_pos:
                    # Found the image, extract coordinates
                    center_x, center_y = match_pos
                    self.logger.info(f"Image found at: ({center_x}, {center_y}) using Airtest exists()")

                    # Validate coordinates to prevent infinity or NaN values
                    valid_coords = validate_coordinates(match_pos, None, None)

                    if valid_coords:
                        x, y = valid_coords
                        return {
                            "status": "success",
                            "message": f"Image {image_filename} found at position ({x}, {y})",
                            "found": True,
                            "position": (x, y)
                        }
                    else:
                        self.logger.error(f"Image found but coordinates are invalid: {match_pos}")
                        return {
                            "status": "success",
                            "message": f"Image {image_filename} found but coordinates are invalid",
                            "found": False
                        }
                else:
                    self.logger.info(f"Image not found with Airtest exists(): {image_filename}")
                    # Continue to fallback methods (same as Tap Image action)

            except ImportError:
                self.logger.warning("Airtest not available, trying fallback methods")
            except Exception as airtest_err:
                self.logger.warning(f"Airtest method failed: {airtest_err}, trying fallback methods")

            # METHOD 2: Try controller's find_image method (fallback method from Tap Image action)
            if hasattr(self.controller, 'find_image'):
                try:
                    self.logger.info(f"Trying controller's find_image method (fallback)")
                    match_pos = self.controller.find_image(abs_path, threshold=threshold, timeout=timeout)

                    if match_pos:
                        # Validate coordinates
                        valid_coords = validate_coordinates(match_pos, None, None)

                        if valid_coords:
                            x, y = valid_coords
                            self.logger.info(f"Image found at valid position: ({x}, {y}) using controller's find_image")
                            return {
                                "status": "success",
                                "message": f"Image {image_filename} found at position ({x}, {y})",
                                "found": True,
                                "position": (x, y)
                            }
                        else:
                            self.logger.error(f"Image found but coordinates are invalid: {match_pos}")

                except Exception as controller_err:
                    self.logger.warning(f"Controller's find_image method failed: {controller_err}")

            # METHOD 3: Try Appium's find_image_occurrence (another fallback from Tap Image action)
            if hasattr(self.controller, 'driver') and self.controller.driver:
                try:
                    if hasattr(self.controller.driver, 'find_image_occurrence'):
                        self.logger.info("Trying Appium's find_image_occurrence method (fallback)")

                        import base64
                        # Read the image file as base64
                        with open(abs_path, 'rb') as img_file:
                            img_base64 = base64.b64encode(img_file.read()).decode('utf-8')

                        # Take a screenshot to use as the base image
                        screenshot = self.controller.driver.get_screenshot_as_base64()

                        # Find the image on screen
                        match = self.controller.driver.find_image_occurrence(
                            screenshot,
                            img_base64,
                            threshold=threshold,
                            visualize=False
                        )

                        if match and 'rect' in match:
                            rect = match['rect']
                            x = rect['x'] + rect['width'] / 2
                            y = rect['y'] + rect['height'] / 2

                            self.logger.info(f"Image found at: ({x}, {y}) using Appium's find_image_occurrence")
                            return {
                                "status": "success",
                                "message": f"Image {image_filename} found at position ({x}, {y})",
                                "found": True,
                                "position": (x, y)
                            }

                except Exception as appium_err:
                    self.logger.warning(f"Appium's find_image_occurrence failed: {appium_err}")

            # METHOD 4: Try OpenCV approach (MAIN fallback method used by Click Image action)
            try:
                self.logger.info(f"Trying OpenCV approach for image recognition: {abs_path}")
                import cv2
                import numpy as np
                from PIL import Image
                import io
                import base64

                # Take a screenshot using Appium
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # Get device dimensions first
                    device_width = None
                    device_height = None
                    if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                        device_width = self.controller.device_dimensions.get('width')
                        device_height = self.controller.device_dimensions.get('height')
                        self.logger.info(f"Device dimensions: {device_width}x{device_height}")

                    # Get screenshot as base64
                    screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                    screenshot_data = base64.b64decode(screenshot_base64)

                    # Convert to PIL Image first
                    screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                    original_size = screenshot_pil.size
                    self.logger.info(f"Original screenshot size: {original_size[0]}x{original_size[1]}")

                    # DO NOT resize the screenshot - use original dimensions
                    self.logger.info(f"Using original screenshot dimensions: {original_size[0]}x{original_size[1]}")
                    # Update device dimensions to match the actual screenshot
                    device_width = original_size[0]
                    device_height = original_size[1]

                    # Convert to OpenCV format
                    screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                    # Load the template image
                    template = cv2.imread(abs_path)
                    if template is None:
                        self.logger.error(f"Failed to load template image: {abs_path}")
                    else:
                        # Get template dimensions
                        h, w = template.shape[:2]
                        self.logger.info(f"Template dimensions: {w}x{h}")

                        # Try multiple template matching methods
                        methods = [
                            (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
                            (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED"),
                            (cv2.TM_SQDIFF_NORMED, "TM_SQDIFF_NORMED")
                        ]

                        best_val = 0
                        best_loc = None
                        best_method = None

                        # Use a lower threshold for OpenCV matching
                        opencv_threshold = max(0.5, threshold - 0.2)  # Lower threshold by 0.2 but not below 0.5
                        self.logger.info(f"Using OpenCV threshold: {opencv_threshold} (original: {threshold})")

                        for method, method_name in methods:
                            # Perform template matching
                            result = cv2.matchTemplate(screenshot_cv, template, method)

                            # Different handling for SQDIFF (lower is better) vs others (higher is better)
                            if method == cv2.TM_SQDIFF_NORMED:
                                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                curr_val = 1.0 - min_val  # Convert to same scale as other methods
                                curr_loc = min_loc
                            else:
                                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                curr_val = max_val
                                curr_loc = max_loc

                            self.logger.info(f"Template matching with {method_name}: {curr_val}")

                            if curr_val > best_val:
                                best_val = curr_val
                                best_loc = curr_loc
                                best_method = method_name

                        self.logger.info(f"Best template matching result: {best_val} with method {best_method} (threshold: {opencv_threshold})")

                        if best_val >= opencv_threshold:
                            # Match found, calculate center coordinates
                            x = best_loc[0] + w // 2
                            y = best_loc[1] + h // 2

                            self.logger.info(f"Found image at ({x}, {y}) in original screenshot using OpenCV")

                            # Validate coordinates against device dimensions
                            if device_width and device_height:
                                if x >= original_size[0] or y >= original_size[1]:
                                    self.logger.warning(f"Coordinates ({x}, {y}) are outside screenshot bounds {original_size[0]}x{original_size[1]}")

                                    # Clamp coordinates to screenshot bounds
                                    x = min(x, original_size[0] - 1)
                                    y = min(y, original_size[1] - 1)
                                    self.logger.info(f"Clamped coordinates to ({x}, {y})")

                                # Only scale if dimensions are different and scaling is needed
                                if device_width != original_size[0] or device_height != original_size[1]:
                                    # Calculate scaling factors
                                    scale_x = device_width / original_size[0]
                                    scale_y = device_height / original_size[1]

                                    # Apply scaling
                                    original_x, original_y = x, y
                                    x = int(x * scale_x)
                                    y = int(y * scale_y)

                                    # Ensure coordinates are within device bounds after scaling
                                    x = min(x, device_width - 1)
                                    y = min(y, device_height - 1)

                                    self.logger.info(f"Scaled coordinates from ({original_x}, {original_y}) to ({x}, {y}) for device dimensions {device_width}x{device_height}")

                            return {
                                "status": "success",
                                "message": f"Image {image_filename} found at position ({x}, {y}) using OpenCV",
                                "found": True,
                                "position": (x, y)
                            }
                        else:
                            self.logger.info(f"Image not found with OpenCV (best match: {best_val}, threshold: {opencv_threshold})")
                else:
                    self.logger.warning("No Appium driver available for OpenCV screenshot")

            except ImportError as e:
                self.logger.warning(f"OpenCV not available: {e}")
            except Exception as e:
                self.logger.warning(f"Error using OpenCV for image recognition: {e}")

            # If all methods failed, image not found
            self.logger.info(f"Image not found using any method: {image_filename}")
            return {
                "status": "success",
                "message": f"Image {image_filename} not found on screen",
                "found": False
            }

        except Exception as e:
            self.logger.error(f"Error checking if image exists: {e}")
            return {"status": "error", "message": f"Error checking if image exists: {str(e)}"}

    def _check_text_exists(self, text_to_find, timeout):
        """Check if text exists on screen using OCR-based text detection"""
        try:
            self.logger.info(f"Checking if text exists on screen: '{text_to_find}' with timeout={timeout}s")

            # Method 1: Use OCR-based text detection (same as TapOnTextAction)
            try:
                # Take a screenshot first
                screenshot_result = self.controller.take_screenshot()
                if screenshot_result['status'] != 'success' or not screenshot_result['path']:
                    self.logger.warning("Failed to take screenshot for OCR text detection, falling back to XPath")
                else:
                    screenshot_path = screenshot_result['path']

                    # Import the text detection function
                    try:
                        from app_android.utils.text_detection import detect_text_in_image

                        # Create a directory for debug images
                        debug_dir = os.path.join(os.path.dirname(screenshot_path), 'text_detection')
                        os.makedirs(debug_dir, exist_ok=True)

                        # Try to find the text using OCR with timeout
                        start_time = time.time()
                        text_found = False

                        while time.time() - start_time < timeout and not text_found:
                            try:
                                # Find the text in the screenshot using OCR
                                result = detect_text_in_image(
                                    screenshot_path,
                                    text_to_find,
                                    output_dir=debug_dir
                                )

                                if result:
                                    text_found = True
                                    self.logger.info(f"Text '{text_to_find}' found on screen using OCR at coordinates: {result['coordinates']}")
                                    return {
                                        "status": "success",
                                        "message": f"Text '{text_to_find}' found on screen using OCR",
                                        "found": True,
                                        "coordinates": result['coordinates']
                                    }

                                # If not found and we have time left, take a new screenshot and try again
                                if time.time() - start_time < timeout - 1:  # Leave 1 second buffer
                                    time.sleep(1)  # Wait 1 second before retrying
                                    screenshot_result = self.controller.take_screenshot()
                                    if screenshot_result['status'] == 'success' and screenshot_result['path']:
                                        screenshot_path = screenshot_result['path']
                                else:
                                    break

                            except Exception as ocr_error:
                                self.logger.warning(f"OCR text detection failed: {ocr_error}")
                                break

                        if not text_found:
                            self.logger.info(f"Text '{text_to_find}' not found using OCR, trying XPath fallback")

                    except ImportError:
                        self.logger.warning("Text detection module not available, falling back to XPath")

            except Exception as screenshot_error:
                self.logger.warning(f"Screenshot-based text detection failed: {screenshot_error}, falling back to XPath")

            # Method 2: Fallback to XPath-based search
            if hasattr(self.controller, 'driver') and self.controller.driver:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.common.exceptions import TimeoutException
                from appium.webdriver.common.appiumby import AppiumBy

                driver = self.controller.driver

                # Create comprehensive XPath to search for text in various attributes
                xpath_patterns = [
                    f"//*[@text='{text_to_find}']",  # Exact text match
                    f"//*[contains(@text, '{text_to_find}')]",  # Contains text
                    f"//*[@content-desc='{text_to_find}']",  # Exact content description
                    f"//*[contains(@content-desc, '{text_to_find}')]",  # Contains content description
                    f"//*[@label='{text_to_find}']",  # Exact label (iOS)
                    f"//*[contains(@label, '{text_to_find}')]",  # Contains label (iOS)
                    f"//*[@name='{text_to_find}']",  # Exact name
                    f"//*[contains(@name, '{text_to_find}')]",  # Contains name
                    f"//*[@value='{text_to_find}']",  # Exact value
                    f"//*[contains(@value, '{text_to_find}')]"  # Contains value
                ]

                # Try each XPath pattern
                for i, xpath in enumerate(xpath_patterns):
                    try:
                        self.logger.debug(f"Trying XPath pattern {i+1}: {xpath}")
                        element = WebDriverWait(driver, 2).until(  # Short timeout per pattern
                            EC.presence_of_element_located((AppiumBy.XPATH, xpath))
                        )
                        self.logger.info(f"Text '{text_to_find}' found on screen using XPath pattern {i+1}")
                        return {
                            "status": "success",
                            "message": f"Text '{text_to_find}' found on screen using XPath",
                            "found": True
                        }
                    except TimeoutException:
                        continue  # Try next pattern
                    except Exception as xpath_error:
                        self.logger.debug(f"XPath pattern {i+1} failed: {xpath_error}")
                        continue

                # If no XPath patterns worked
                self.logger.info(f"Text '{text_to_find}' not found on screen using any method")
                return {
                    "status": "success",
                    "message": f"Text '{text_to_find}' not found on screen",
                    "found": False
                }
            else:
                return {
                    "status": "error",
                    "message": "No driver available for text search"
                }

        except Exception as e:
            self.logger.error(f"Error checking if text exists: {e}")
            return {"status": "error", "message": f"Error checking if text exists: {str(e)}"}

    def _check_element_exists(self, locator_type, locator_value, timeout):
        """Check if element exists on screen using locator type and value"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Make sure we have a driver in device controller
            if not hasattr(self.controller, 'driver') or not self.controller.driver:
                return {"status": "error", "message": "No Appium driver available for element checking"}

            driver = self.controller.driver

            # Set up locator strategy
            locator = None
            if locator_type == 'id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ID, locator_value)
                self.logger.info(f"Checking for element with ID: {locator_value}")
            elif locator_type == 'xpath':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.XPATH, locator_value)
                self.logger.info(f"Checking for element with XPath: {locator_value}")
            elif locator_type == 'accessibility_id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ACCESSIBILITY_ID, locator_value)
                self.logger.info(f"Checking for element with Accessibility ID: {locator_value}")
            elif locator_type == 'class':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.CLASS_NAME, locator_value)
                self.logger.info(f"Checking for element with Class: {locator_value}")
            elif locator_type == 'name':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.NAME, locator_value)
                self.logger.info(f"Checking for element with Name: {locator_value}")
            else:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            try:
                # Try to find the element with the specified timeout
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located(locator)
                )

                # Element found
                element_text = None
                try:
                    element_text = element.text
                except:
                    pass

                self.logger.info(f"Element found: {locator_type}='{locator_value}'" +
                                 (f" with text '{element_text}'" if element_text else ""))

                return {
                    "status": "success",
                    "message": f"Element found: {locator_type}='{locator_value}'",
                    "found": True,
                    "element_text": element_text
                }
            except TimeoutException:
                # Element not found within timeout
                self.logger.info(f"Element not found: {locator_type}='{locator_value}' within {timeout} seconds")
                return {
                    "status": "success",
                    "message": f"Element not found: {locator_type}='{locator_value}'",
                    "found": False
                }
            except Exception as find_err:
                self.logger.error(f"Error finding element: {str(find_err)}")
                return {
                    "status": "error",
                    "message": f"Error checking for element: {str(find_err)}"
                }
        except Exception as e:
            self.logger.error(f"Failed to execute exists check: {str(e)}")
            return {"status": "error", "message": f"Failed to execute exists check: {str(e)}"}

    def _check_uiselector_exists(self, uiselector_value, timeout):
        """Check if element exists using UISelector"""
        try:
            self.logger.info(f"Checking if UISelector element exists: {uiselector_value}")

            # Use the universal locator finding method
            element = self.find_element_with_locator('uiselector', uiselector_value, timeout)

            if element:
                self.logger.info(f"UISelector element found: {uiselector_value}")
                return {
                    "status": "success",
                    "message": f"UISelector element found: {uiselector_value}",
                    "found": True
                }
            else:
                self.logger.info(f"UISelector element not found: {uiselector_value}")
                return {
                    "status": "success",
                    "message": f"UISelector element not found: {uiselector_value}",
                    "found": False
                }

        except Exception as e:
            self.logger.error(f"Error checking UISelector element existence: {e}")
            return {
                "status": "success",
                "message": f"UISelector element not found: {uiselector_value}",
                "found": False
            }
