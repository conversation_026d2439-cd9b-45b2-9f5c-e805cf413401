from .base_action import BaseAction
import logging
import traceback
import json
import os
from airtest.core.api import Template, wait, exists
from airtest.core.error import TargetNotFoundError
from airtest.core.helper import G

class IfThenStepsAction(BaseAction):
    """Handler for if-then conditional actions - COMPLETELY REWRITTEN to use exact TAP action logic"""

    def execute(self, params):
        """
        Execute a conditional action - if condition is true, execute then action

        Args:
            params: Dictionary containing:
                - condition_type: Type of condition (exists, not_exists, visible, contains_text, value_equals, has_attribute, etc.)
                - condition: Condition parameters
                - then_action: Action to execute if condition is true

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "success", "message": "No device controller available - step passed (if-then behavior)"}

        condition_type = params.get('condition_type')
        condition = params.get('condition', {})
        then_action = params.get('then_action', {})

        # Log the conditional action
        self.logger.info(f"Executing If-Then Steps with condition type: {condition_type}")
        self.logger.info(f"Condition parameters: {condition}")
        self.logger.info(f"Then action: {then_action}")

        # Check condition based on type
        condition_met = False
        found_element_coordinates = None  # Store coordinates of found condition element

        try:
            if condition_type in ['exists', 'not_exists']:
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value:
                    return {"status": "success", "message": "Missing locator parameters for condition - step passed (if-then behavior)"}

                self.logger.info(f"Checking if element exists with {locator_type}: {locator_value}, timeout={timeout}s")

                # Use EXACT same logic as working TAP actions with proper timeout handling
                element_found = None
                coordinates = None

                try:
                    if locator_type == 'image':
                        # Use EXACT same image finding logic as TAP Image action
                        threshold = float(condition.get('threshold', 0.7))
                        element_found, coordinates = self._find_image_exact_tap_logic(locator_value, timeout, threshold)
                        if element_found:
                            found_element_coordinates = coordinates
                            self.logger.info(f"Found image at coordinates: {found_element_coordinates}")

                    elif locator_type == 'text':
                        # Use EXACT same text finding logic as TAP Text action
                        element_found, coordinates = self._find_text_exact_tap_logic(locator_value, timeout)
                        if element_found:
                            found_element_coordinates = coordinates
                            self.logger.info(f"Found text at coordinates: {found_element_coordinates}")

                    else:
                        # Use EXACT same locator finding logic as TAP Element action
                        element_found, coordinates = self._find_element_exact_tap_logic(locator_type, locator_value, timeout)
                        if element_found:
                            found_element_coordinates = coordinates
                            self.logger.info(f"Found element at coordinates: {found_element_coordinates}")

                except Exception as condition_error:
                    self.logger.warning(f"Error during condition check: {condition_error}")
                    element_found, coordinates = None, None

                # For 'exists', condition is met if element is found
                # For 'not_exists', condition is met if element is NOT found
                if condition_type == 'exists':
                    condition_met = element_found is not None
                    self.logger.info(f"Condition check result: Element exists = {condition_met}")
                else:  # not_exists
                    condition_met = element_found is None
                    found_element_coordinates = None  # No coordinates for not_exists
                    self.logger.info(f"Condition check result: Element does not exist = {condition_met}")

            else:
                # Handle other condition types (visible, contains_text, etc.)
                return {"status": "success", "message": f"Condition type '{condition_type}' not yet implemented - step passed (if-then behavior)"}

        except Exception as e:
            self.logger.error(f"Error checking condition: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error checking condition but step passed (if-then behavior): {str(e)}"}

        # Execute then action if condition is met
        if condition_met:
            return self._execute_then_action(then_action, found_element_coordinates)
        else:
            self.logger.info("Condition not met, no action taken")
            return {"status": "success", "message": "Condition not met, no action taken"}

    def _execute_then_action(self, then_action, found_coordinates):
        """Execute the then action - directly use the TAP action for tap actions (Android version)"""
        if not then_action or not then_action.get('type'):
            self.logger.info("Condition met, but no Then action was provided")
            return {"status": "success", "message": "Condition met, no action taken (No Action selected)"}

        self.logger.info(f"Condition met, executing then action: {then_action.get('type')}")

        # Enhanced debugging
        self.logger.info(f"DEBUG: found_coordinates = {found_coordinates}")
        self.logger.info(f"DEBUG: found_coordinates type = {type(found_coordinates)}")
        self.logger.info(f"DEBUG: then_action type = {then_action.get('type')}")
        self.logger.info(f"DEBUG: original then_action = {then_action}")

        # For TAP actions, use the TAP action directly instead of replicating logic
        if then_action.get('type') == 'tap':
            return self._execute_tap_action_directly(then_action, found_coordinates)

        # For TAP ON TEXT actions, use the Tap on Text action directly
        if then_action.get('type') == 'tapOnText':
            return self._execute_tap_on_text_action_directly(then_action)

        # For other action types, use the action factory
        try:
            from .action_factory import ActionFactory
            action_factory = ActionFactory(self.controller)
            self.logger.info(f"DEBUG: Executing non-tap action with parameters: {then_action}")
            result = action_factory.execute_action(then_action.get('type'), then_action)
            self.logger.info(f"Then action result: {result}")
            return result
        except Exception as e:
            self.logger.error(f"Error executing then action: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error executing then action but step passed (if-then behavior): {str(e)}"}

    def _execute_tap_action_directly(self, then_action, found_coordinates):
        """Execute TAP action directly using the TAP action class (Android version)"""
        try:
            # Import and create the TAP action directly
            from .tap_action import TapAction
            tap_action = TapAction(self.controller)

            # Prepare the tap action parameters
            tap_params = then_action.copy()

            self.logger.info(f"DEBUG: TAP action method = {tap_params.get('method', 'not specified')}")

            # Handle different TAP action methods
            method = tap_params.get('method', 'coordinates')

            if method == 'coordinates':
                # For coordinate-based tapping, use the provided coordinates or found coordinates
                if 'x' in tap_params and 'y' in tap_params:
                    # Use coordinates from the form
                    self.logger.info(f"Using coordinates from form: ({tap_params['x']}, {tap_params['y']})")
                elif found_coordinates and isinstance(found_coordinates, (tuple, list)) and len(found_coordinates) >= 2:
                    # Use coordinates from condition detection
                    try:
                        x = int(found_coordinates[0])
                        y = int(found_coordinates[1])
                        if x > 0 and y > 0:
                            tap_params['x'] = x
                            tap_params['y'] = y
                            self.logger.info(f"Using coordinates from condition detection: ({x}, {y})")
                        else:
                            self.logger.warning(f"Invalid coordinates from condition: ({x}, {y})")
                            return {"status": "success", "message": f"Invalid coordinates from condition but step passed (if-then behavior): ({x}, {y})"}
                    except (ValueError, TypeError) as coord_error:
                        self.logger.error(f"Error processing coordinates: {coord_error}")
                        return {"status": "success", "message": f"Error processing coordinates but step passed (if-then behavior): {coord_error}"}
                else:
                    self.logger.error("No valid coordinates available for tap action")
                    return {"status": "success", "message": "No valid coordinates available for tap action but step passed (if-then behavior)"}

            elif method == 'image':
                # For image-based tapping, ensure required parameters are present
                if not tap_params.get('image_filename'):
                    self.logger.error("Image filename is required for image-based tap")
                    return {"status": "success", "message": "Image filename is required for image-based tap but step passed (if-then behavior)"}
                self.logger.info(f"Using image-based tap with image: {tap_params['image_filename']}")

            elif method == 'locator':
                # For locator-based tapping, ensure required parameters are present
                if not tap_params.get('locator_type') or not tap_params.get('locator_value'):
                    self.logger.error("Locator type and value are required for locator-based tap")
                    return {"status": "success", "message": "Locator type and value are required for locator-based tap but step passed (if-then behavior)"}
                self.logger.info(f"Using locator-based tap with {tap_params['locator_type']}='{tap_params['locator_value']}'")

            elif method == 'text':
                # For text-based tapping, ensure required parameters are present
                if not tap_params.get('text_to_find'):
                    self.logger.error("Text to find is required for text-based tap")
                    return {"status": "success", "message": "Text to find is required for text-based tap but step passed (if-then behavior)"}
                self.logger.info(f"Using text-based tap with text: '{tap_params['text_to_find']}'")

            # Execute the TAP action directly
            self.logger.info(f"DEBUG: Executing TAP action directly with parameters: {tap_params}")
            result = tap_action.execute(tap_params)
            self.logger.info(f"TAP action result: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Error executing TAP action directly: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error executing TAP action directly but step passed (if-then behavior): {str(e)}"}

    def _execute_tap_on_text_action_directly(self, then_action):
        """Execute Tap on Text action directly using the Tap on Text action class (Android version)"""
        try:
            # Import and create the Tap on Text action directly
            from .tap_on_text_action import TapOnTextAction
            tap_on_text_action = TapOnTextAction(self.controller)

            # Prepare the tap on text action parameters
            tap_on_text_params = then_action.copy()

            self.logger.info(f"DEBUG: Tap on Text action parameters = {tap_on_text_params}")

            # Validate required parameters
            if not tap_on_text_params.get('text_to_find'):
                self.logger.error("Text to find is required for Tap on Text action")
                return {"status": "success", "message": "Text to find is required for Tap on Text action but step passed (if-then behavior)"}

            # Set default values if not provided
            if 'timeout' not in tap_on_text_params:
                tap_on_text_params['timeout'] = 30
            if 'double_tap' not in tap_on_text_params:
                tap_on_text_params['double_tap'] = False

            self.logger.info(f"Executing Tap on Text action with text: '{tap_on_text_params['text_to_find']}'")

            # Execute the Tap on Text action directly
            result = tap_on_text_action.execute(tap_on_text_params)
            self.logger.info(f"Tap on Text action result: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Error executing Tap on Text action directly: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return {"status": "success", "message": f"Error executing Tap on Text action directly but step passed (if-then behavior): {str(e)}"}

    def _find_image_exact_tap_logic(self, image_filename, timeout, threshold):
        """
        Find image using EXACT same logic as TAP Image action
        Returns: (element_found, coordinates)
        """
        try:
            # Get absolute path for the image
            if hasattr(self.controller, 'get_screenshot_folder'):
                screenshot_folder = self.controller.get_screenshot_folder()
                abs_path = os.path.join(screenshot_folder, image_filename)
            else:
                abs_path = os.path.abspath(image_filename)

            if not os.path.exists(abs_path):
                self.logger.error(f"Image file not found: {abs_path}")
                return False, None

            self.logger.info(f"Looking for image: {abs_path} with threshold: {threshold}")

            # EXACT COPY of TAP Image action logic for Android
            # Ensure Airtest device connection
            if hasattr(self.controller, '_ensure_airtest_connected'):
                self.logger.info("Ensuring Airtest device is connected...")
                airtest_connected = self.controller._ensure_airtest_connected()
                if not airtest_connected:
                    self.logger.error("Failed to connect Airtest device")
                    return False, None

            # Initialize Airtest device if needed
            if hasattr(self.controller, '_init_airtest'):
                self.logger.info("Initializing Airtest device...")
                airtest_initialized = self.controller._init_airtest()
                if not airtest_initialized:
                    self.logger.error("Failed to initialize Airtest device")
                    return False, None

            # Log device info
            if hasattr(G, 'DEVICE') and G.DEVICE:
                self.logger.info(f"Current Airtest device: {G.DEVICE}")
            else:
                self.logger.warning("No Airtest device available in G.DEVICE")

            # Use Airtest for image recognition (EXACT same as TAP Image)
            self.logger.info(f"Using Airtest for image recognition")
            template_image = Template(abs_path, threshold=threshold)
            match_pos = wait(template_image, timeout=timeout)
            self.logger.info(f"Found image at position: {match_pos}")

            return True, match_pos

        except TargetNotFoundError:
            self.logger.info(f"Image not found: {image_filename}")
            return False, None
        except Exception as e:
            self.logger.error(f"Error finding image: {e}")
            return False, None

    def _find_text_exact_tap_logic(self, text_to_find, timeout):
        """
        Find text using EXACT same logic as TAP Text action with proper timeout handling
        Returns: (element_found, coordinates)
        """
        import time

        start_time = time.time()
        text_found = False
        result = None

        self.logger.info(f"Looking for text: '{text_to_find}' with timeout: {timeout}s")

        # Take initial screenshot
        screenshot_result = self.controller.take_screenshot()
        if screenshot_result['status'] != 'success' or not screenshot_result['path']:
            self.logger.error(f"Failed to take initial screenshot: {screenshot_result.get('message', 'Unknown error')}")
            return False, None

        screenshot_path = screenshot_result['path']

        # Use timeout loop exactly like TAP Text action
        while time.time() - start_time < timeout and not text_found:
            try:
                # Use the exact same text detection logic as TAP Text action
                from ..utils.text_detection import detect_text_in_image

                self.logger.info(f"Searching for text in screenshot: {screenshot_path}")

                # Find the text in the screenshot using enhanced text detection
                result = detect_text_in_image(screenshot_path, text_to_find)

                if result and result.get('coordinates'):
                    coordinates = result['coordinates']
                    center_x = coordinates.get('center_x')
                    center_y = coordinates.get('center_y')

                    if center_x is not None and center_y is not None:
                        self.logger.info(f"Found text '{text_to_find}' at coordinates: ({center_x}, {center_y})")
                        return True, (center_x, center_y)

                # If not found, wait a bit and try again (same as TAP Text action)
                elapsed = time.time() - start_time
                if elapsed < timeout:
                    self.logger.info(f"Text '{text_to_find}' not found, retrying... (elapsed: {elapsed:.1f}s)")
                    time.sleep(1)

                    # Take a new screenshot
                    screenshot_result = self.controller.take_screenshot()
                    if screenshot_result['status'] != 'success' or not screenshot_result['path']:
                        self.logger.warning(f"Failed to take new screenshot: {screenshot_result.get('message', 'Unknown error')}")
                        time.sleep(1)
                        continue

                    screenshot_path = screenshot_result['path']
                else:
                    break

            except Exception as e:
                self.logger.error(f"Error finding text: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                break

        self.logger.info(f"Text '{text_to_find}' not found within timeout ({timeout}s)")
        return False, None

    def _find_element_exact_tap_logic(self, locator_type, locator_value, timeout):
        """
        Find element using EXACT same logic as TAP Element action with proper timeout handling
        Returns: (element_found, coordinates)
        """
        import time

        start_time = time.time()
        element_found = False

        self.logger.info(f"Finding element with {locator_type}='{locator_value}', timeout={timeout}s")

        # Use timeout loop for element finding
        while time.time() - start_time < timeout and not element_found:
            try:
                # EXACT COPY of TAP action logic - try UIAutomator2 first for specific locator types
                if hasattr(self.controller, 'uiautomator2_helper') and self.controller.uiautomator2_helper:
                    ui2_helper = self.controller.uiautomator2_helper

                    # Try UIAutomator2 for content-desc based locators (EXACT same as TAP action)
                    if locator_type.lower() in ['content-desc', 'contentdesc', 'accessibility_id']:
                        self.logger.info(f"Using UIAutomator2 for content-desc locator: {locator_value}")
                        try:
                            element_info = ui2_helper.find_element_by_content_desc(locator_value)
                            if element_info and 'bounds' in element_info:
                                # Calculate center coordinates from bounds (EXACT same as TAP action)
                                bounds = element_info['bounds']
                                center_x = (bounds['left'] + bounds['right']) // 2
                                center_y = (bounds['top'] + bounds['bottom']) // 2

                                self.logger.info(f"Found element with UIAutomator2 content-desc at coordinates: ({center_x}, {center_y})")
                                return True, (center_x, center_y)
                        except Exception as ui2_desc_error:
                            self.logger.warning(f"UIAutomator2 content-desc locator failed: {ui2_desc_error}")

                    # Try UIAutomator2 for text-based locators (EXACT same as TAP action)
                    elif locator_type.lower() in ['text', 'textcontains']:
                        self.logger.info(f"Using UIAutomator2 for text-based locator: {locator_value}")
                        try:
                            exact_match = locator_type.lower() == 'text'
                            element_info = ui2_helper.find_element_by_text(locator_value, exact_match)
                            if element_info and 'bounds' in element_info:
                                # Calculate center coordinates from bounds (EXACT same as TAP action)
                                bounds = element_info['bounds']
                                center_x = (bounds['left'] + bounds['right']) // 2
                                center_y = (bounds['top'] + bounds['bottom']) // 2

                                self.logger.info(f"Found element with UIAutomator2 text at coordinates: ({center_x}, {center_y})")
                                return True, (center_x, center_y)
                        except Exception as ui2_text_error:
                            self.logger.warning(f"UIAutomator2 text locator failed: {ui2_text_error}")

                # Fallback to standard Appium method (EXACT same as TAP action)
                self.logger.info("Falling back to standard element finding methods")

                # Use the universal locator method with shorter timeout for retry loop
                element = None
                try:
                    # Use shorter timeout for individual attempts within the retry loop
                    remaining_timeout = timeout - (time.time() - start_time)
                    attempt_timeout = min(2, remaining_timeout)  # Max 2 seconds per attempt

                    if attempt_timeout > 0:
                        element = self.find_element_with_locator(locator_type, locator_value, attempt_timeout)
                        self.logger.info(f"Universal locator method found element: {element is not None}")
                except Exception as e:
                    self.logger.warning(f"Universal locator method failed: {e}")

                # Fallback to controller's method if universal method didn't work
                if not element and hasattr(self.controller, 'find_element'):
                    try:
                        remaining_timeout = timeout - (time.time() - start_time)
                        attempt_timeout = min(2, remaining_timeout)  # Max 2 seconds per attempt

                        if attempt_timeout > 0:
                            element = self.controller.find_element(locator_type, locator_value, timeout=attempt_timeout)
                            self.logger.info(f"Controller find_element method found element: {element is not None}")
                    except Exception as e:
                        self.logger.warning(f"Controller find_element method failed: {e}")

                if element:
                    self.logger.info(f"Element found with {locator_type}='{locator_value}', extracting coordinates...")

                    # Extract coordinates using the same logic as TAP Element
                    if hasattr(element, 'location') and hasattr(element, 'size'):
                        location = element.location
                        size = element.size

                        self.logger.info(f"Element location: {location}, size: {size}")

                        # Calculate center coordinates (same as TAP Element)
                        center_x = location['x'] + size['width'] // 2
                        center_y = location['y'] + size['height'] // 2

                        self.logger.info(f"Found element with {locator_type}='{locator_value}' at coordinates: ({center_x}, {center_y})")
                        return True, (center_x, center_y)
                    else:
                        self.logger.warning(f"Element found but couldn't extract coordinates - missing location or size attributes")
                        self.logger.warning(f"Element attributes: {dir(element)}")
                        return True, None

                # If not found in this iteration, wait and retry
                elapsed = time.time() - start_time
                if elapsed < timeout:
                    self.logger.info(f"Element '{locator_type}={locator_value}' not found, retrying... (elapsed: {elapsed:.1f}s)")
                    time.sleep(1)
                else:
                    break

            except Exception as e:
                self.logger.error(f"Error finding element: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                break

        self.logger.info(f"Element '{locator_type}={locator_value}' not found within timeout ({timeout}s)")
        return False, None
