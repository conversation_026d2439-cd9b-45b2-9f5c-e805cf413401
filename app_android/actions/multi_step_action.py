from .base_action import BaseAction
import logging
import traceback
import time
from pathlib import Path

class MultiStepAction(BaseAction):
    """Handler for executing a test case as a multi-step action"""

    def execute(self, params):
        """
        Execute a test case as a multi-step action

        Args:
            params: Dictionary containing:
                - test_case_id: ID of the test case to execute
                - test_case_steps: Steps of the test case to execute

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the test case ID from params
        test_case_id = params.get('test_case_id')
        test_case_steps = params.get('test_case_steps', [])

        if not test_case_id:
            return {"status": "error", "message": "No test case ID provided"}

        # If no steps are provided directly, try to load them from the test case file
        if not test_case_steps:
            self.logger.info(f"No test case steps provided directly, attempting to load from file: {test_case_id}")
            try:
                # Import test_case_manager utilities with fallback
                try:
                    from ..utils.test_case_manager import TestCaseManager
                except ImportError:
                    try:
                        from app_android.utils.test_case_manager import TestCaseManager
                    except ImportError:
                        try:
                            from app_android.utils.test_case_manager import TestCaseManager
                        except ImportError:
                            # Fallback function if import fails
                            def TestCaseManager(*args, **kwargs):
                                return args[0] if args else None

                # Get the test cases directory
                try:
                    from config_android import DIRECTORIES
                    test_cases_dir = DIRECTORIES['TEST_CASES']
                except ImportError:
                    # Fallback to default directory
                    import os
                    try:
                        from config_android import DIRECTORIES as ANDROID_DIRECTORIES
                        test_cases_dir = str(ANDROID_DIRECTORIES['TEST_CASES'])
                    except Exception:
                        test_cases_dir = str(Path.home() / 'MobileAutomationWorkspace' / 'android' / 'test_cases')

                # Create a test case manager instance
                test_case_manager = TestCaseManager(test_cases_dir)

                # Load the test case
                test_case = test_case_manager.load_test_case(test_case_id)

                if test_case and 'actions' in test_case:
                    test_case_steps = test_case.get('actions', [])
                    self.logger.info(f"Loaded {len(test_case_steps)} steps from test case file: {test_case_id}")
                else:
                    self.logger.error(f"Failed to load test case or no actions found: {test_case_id}")
                    return {"status": "error", "message": f"Failed to load test case or no actions found: {test_case_id}"}
            except Exception as e:
                self.logger.error(f"Error loading test case: {e}")
                return {"status": "error", "message": f"Error loading test case: {str(e)}"}

        if not test_case_steps:
            return {"status": "error", "message": "No test case steps provided or loaded"}

        self.logger.info(f"Executing multi-step action for test case: {test_case_id}")

        # Import the action factory
        try:
            from .action_factory import ActionFactory
        except ImportError:
            try:
                from action_factory import ActionFactory
            except ImportError:
                try:
                    from app_android.actions.action_factory import ActionFactory
                except ImportError:
                    # Last resort - try importing from actions module
                    from app_android.actions.action_factory import ActionFactory

        # Create a new action factory with the same controller
        action_factory = ActionFactory(self.controller)

        # Separate regular steps from cleanup steps
        regular_steps = []
        cleanup_steps = []

        for i, step in enumerate(test_case_steps):
            if step.get('is_cleanup_step', False):
                cleanup_steps.append((i, step))  # Store with original index
                self.logger.info(f"Step {i + 1} marked as cleanup step: {step.get('type', 'unknown')}")
            else:
                regular_steps.append((i, step))  # Store with original index

        self.logger.info(f"MultiStep execution plan: {len(regular_steps)} regular steps, {len(cleanup_steps)} cleanup steps")

        # Execute each step in the test case
        results = []
        success_count = 0

        # Find all hook actions in the test case steps
        # Look for both 'action_type' and 'type' being 'hookAction'
        hook_actions = [step for step in test_case_steps if step.get('action_type') == 'hookAction' or step.get('type') == 'hookAction']
        if hook_actions:
            self.logger.info(f"Found {len(hook_actions)} hook actions within multi-step")
            # Log the hook actions for debugging
            for i, hook in enumerate(hook_actions):
                self.logger.info(f"Hook action {i+1}: {hook}")

        # Execute regular steps first
        self.logger.info("=== EXECUTING REGULAR STEPS ===")
        regular_success_count = self._execute_steps_batch(regular_steps, action_factory, results, "regular")
        success_count += regular_success_count

        # Execute cleanup steps (always execute, regardless of regular step failures)
        cleanup_success_count = 0
        if cleanup_steps:
            self.logger.info("=== EXECUTING CLEANUP STEPS ===")
            cleanup_success_count = self._execute_steps_batch(cleanup_steps, action_factory, results, "cleanup")
            success_count += cleanup_success_count

        # Calculate total steps for status reporting
        total_steps = len(regular_steps) + len(cleanup_steps)

        # Determine overall status
        if success_count == total_steps:
            overall_status = "success"
            message = f"All {success_count} steps executed successfully ({len(regular_steps)} regular, {len(cleanup_steps)} cleanup)"
        elif success_count > 0:
            overall_status = "partial"
            message = f"{success_count}/{total_steps} steps executed successfully ({regular_success_count}/{len(regular_steps)} regular, {cleanup_success_count}/{len(cleanup_steps)} cleanup)"
        else:
            overall_status = "error"
            message = "All steps failed to execute"

        return {
            "status": overall_status,
            "message": message,
            "results": results,
            "success_count": success_count,
            "total_steps": total_steps,
            "regular_steps_count": len(regular_steps),
            "cleanup_steps_count": len(cleanup_steps)
        }

    def _execute_steps_batch(self, steps_with_indices, action_factory, results, batch_type="regular"):
        """
        Execute a batch of steps (either regular or cleanup)

        Args:
            steps_with_indices: List of tuples (original_index, step)
            action_factory: Action factory instance
            results: Results list to append to
            batch_type: "regular" or "cleanup" for logging

        Returns:
            int: Number of successful steps
        """
        success_count = 0
        step_retry_count = {}
        max_retries_per_step = 3

        for batch_index, (original_index, step) in enumerate(steps_with_indices):
            # Initialize retry count for this step
            if original_index not in step_retry_count:
                step_retry_count[original_index] = 0

            retry_step = True
            while retry_step:
                retry_step = False  # Default to not retry

                try:
                    # Get action type
                    action_type = step.get('type') or step.get('action_type')

                    self.logger.info(f"Executing {batch_type} step {batch_index + 1}/{len(steps_with_indices)} (original step {original_index + 1}): {action_type or 'unknown'}")

                    if not action_type:
                        self.logger.warning(f"{batch_type.title()} step {original_index + 1} has no action_type, skipping")
                        results.append({
                            "status": "warning",
                            "message": f"Step {original_index + 1} has no action_type, skipping",
                            "step_index": original_index,
                            "batch_type": batch_type
                        })
                        break  # Move to next step

                    # Skip hook actions during normal execution (they're handled separately)
                    if action_type == 'hookAction' or step.get('type') == 'hookAction':
                        self.logger.info(f"Skipping hook action during {batch_type} execution (step {original_index + 1})")
                        results.append({
                            "status": "success",
                            "message": "Hook Action skipped (will only be executed when a step fails)",
                            "step_index": original_index,
                            "batch_type": batch_type
                        })
                        success_count += 1
                        break  # Move to next step

                    # Special handling for swipe actions with locator parameters
                    if action_type == 'swipe':
                        if step.get('locator_type') or step.get('locator_value') or step.get('image_filename') or step.get('text_to_find'):
                            self.logger.info(f"MultiStep {batch_type} step {original_index + 1}: Swipe action has locator parameters, converting to swipeTillVisible")
                            action_type = 'swipeTillVisible'

                            # Optimize timeouts for cleanup steps
                            if batch_type == "cleanup" and ('timeout' not in step or step.get('timeout', 20) > 15):
                                step = step.copy()
                                step['timeout'] = 15
                                self.logger.info(f"MultiStep {batch_type} step {original_index + 1}: Reduced timeout to 15 seconds")

                    # Execute the action
                    try:
                        result = action_factory.execute_action(action_type, step)
                    except Exception as action_error:
                        self.logger.error(f"Failed to execute {batch_type} step {original_index + 1} (action_type: {action_type}): {action_error}")
                        result = {
                            "status": "error",
                            "message": f"Failed to execute action {action_type}: {str(action_error)}"
                        }

                    # Process result
                    if isinstance(result, dict):
                        result['step_index'] = original_index
                        result['batch_type'] = batch_type
                        results.append(result)

                        if result.get('status') == 'success':
                            success_count += 1
                            step_retry_count[original_index] = 0  # Reset retry count
                            self.logger.info(f"{batch_type.title()} step {original_index + 1} completed successfully")
                        else:
                            # For cleanup steps, continue execution even on failure
                            if batch_type == "cleanup":
                                self.logger.warning(f"Cleanup step {original_index + 1} failed but continuing: {result.get('message')}")
                            else:
                                # For regular steps, handle retries and hook actions
                                self.logger.error(f"Regular step {original_index + 1} failed: {result.get('message')}")
                                # Note: Hook action handling could be added here if needed
                    else:
                        self.logger.warning(f"{batch_type.title()} step {original_index + 1} returned non-dict result: {result}")
                        results.append({
                            "status": "warning",
                            "message": f"Step {original_index + 1} returned non-dict result: {result}",
                            "step_index": original_index,
                            "batch_type": batch_type
                        })

                except Exception as e:
                    self.logger.error(f"Error executing {batch_type} step {original_index + 1}: {e}")
                    self.logger.error(traceback.format_exc())
                    results.append({
                        "status": "error",
                        "message": f"Error executing step {original_index + 1}: {str(e)}",
                        "step_index": original_index,
                        "batch_type": batch_type
                    })

                    # For cleanup steps, continue even on exception
                    if batch_type == "cleanup":
                        self.logger.warning(f"Cleanup step {original_index + 1} had exception but continuing")

        return success_count
