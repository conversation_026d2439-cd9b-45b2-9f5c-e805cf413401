import logging
from app_android.actions.base_action import BaseAction


class HideKeyboardAction(BaseAction):
    """
    Action to hide the on-screen keyboard
    This action ensures the keyboard is hidden and doesn't interfere with other UI interactions
    """

    def __init__(self, controller=None):
        super().__init__(controller)
        self.logger = logging.getLogger(__name__)

    def execute(self, **kwargs):
        """
        Execute the hide keyboard action
        
        Returns:
            dict: Result with status and message
        """
        try:
            self.logger.info("Executing hide keyboard action")
            
            if not self.controller:
                self.logger.error("No controller available for hide keyboard action")
                return {
                    "status": "error",
                    "message": "No controller available for hide keyboard action"
                }
            
            # Try multiple methods to hide the keyboard
            success_methods = []
            
            # Method 1: Use Appium's hide_keyboard method
            if hasattr(self.controller, 'driver') and self.controller.driver:
                try:
                    self.controller.driver.hide_keyboard()
                    success_methods.append("Appium hide_keyboard")
                    self.logger.info("Successfully hid keyboard using Appium hide_keyboard method")
                except Exception as e:
                    self.logger.debug(f"Appium hide_keyboard method failed: {e}")
            
            # Method 2: Use Android back key to dismiss keyboard
            if hasattr(self.controller, 'platform_name') and self.controller.platform_name and self.controller.platform_name.lower() == 'android':
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        self.controller.driver.press_keycode(4)  # Android BACK key
                        success_methods.append("Android BACK key")
                        self.logger.info("Successfully hid keyboard using Android BACK key")
                except Exception as e:
                    self.logger.debug(f"Android BACK key method failed: {e}")
                
                # Method 3: Use ADB shell input keyevent
                try:
                    if hasattr(self.controller, 'device_id') and self.controller.device_id:
                        import subprocess
                        subprocess.run([
                            'adb', '-s', self.controller.device_id, 'shell',
                            'input', 'keyevent', 'KEYCODE_BACK'
                        ], capture_output=True, timeout=5)
                        success_methods.append("ADB shell BACK keyevent")
                        self.logger.info("Successfully hid keyboard using ADB shell BACK keyevent")
                except Exception as e:
                    self.logger.debug(f"ADB shell BACK keyevent method failed: {e}")
            
            # Method 4: For iOS, try to tap outside keyboard area
            elif hasattr(self.controller, 'platform_name') and self.controller.platform_name and self.controller.platform_name.lower() == 'ios':
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Get screen size
                        screen_size = self.controller.driver.get_window_size()
                        # Tap in the upper area of the screen (away from keyboard)
                        self.controller.driver.tap([(screen_size['width'] // 2, screen_size['height'] // 4)])
                        success_methods.append("iOS tap outside keyboard")
                        self.logger.info("Successfully hid keyboard by tapping outside keyboard area on iOS")
                except Exception as e:
                    self.logger.debug(f"iOS tap outside keyboard method failed: {e}")
            
            # Method 5: Use Airtest if available
            if hasattr(self.controller, 'airtest_device') and self.controller.airtest_device:
                try:
                    # Try to use Airtest's keyevent for back key
                    self.controller.airtest_device.keyevent("BACK")
                    success_methods.append("Airtest BACK keyevent")
                    self.logger.info("Successfully hid keyboard using Airtest BACK keyevent")
                except Exception as e:
                    self.logger.debug(f"Airtest BACK keyevent method failed: {e}")
            
            if success_methods:
                return {
                    "status": "success",
                    "message": f"Keyboard hidden successfully using: {', '.join(success_methods)}"
                }
            else:
                self.logger.warning("All keyboard hiding methods failed, but continuing")
                return {
                    "status": "success",
                    "message": "Keyboard hide attempted (all methods failed, but this is often normal if keyboard was not visible)"
                }
                
        except Exception as e:
            self.logger.error(f"Error in hide keyboard action: {e}")
            return {
                "status": "error",
                "message": f"Error in hide keyboard action: {e}"
            }