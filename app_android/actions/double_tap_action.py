from .base_action import BaseAction
from airtest.core.api import Template, wait, touch, exists
import os
import traceback
import time
from airtest.core.error import TargetNotFoundError
from airtest.core.helper import G

class DoubleTapAction(BaseAction):
    """Handler for double tap actions"""

    def execute(self, params):
        """
        Execute double tap action (tap twice at the same location)

        Args:
            params: Dictionary containing:
                - x: The x coordinate to tap
                - y: The y coordinate to tap
                - image_filename: (Optional) Reference image to tap on
                - method: (Optional) Method to use for tapping ('coordinates', 'image', or 'locator')
                - locator_type: (Optional) Type of locator to use (id, xpath, accessibility_id, etc.)
                - locator_value: (Optional) Value of the locator
                - fallback_locators: (Optional) List of fallback locators to try if primary fails

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Check if we're using locator-based tapping
        method = params.get('method', 'coordinates')
        if method == 'locator':
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            timeout = int(params.get('timeout', 15))
            interval = float(params.get('interval', 0.5))
            fallback_locators = params.get('fallback_locators', [])

            # If we have no primary locator but have fallbacks, that's okay
            if (not locator_type or not locator_value) and not fallback_locators:
                return {"status": "error", "message": "Missing locator type or value, and no fallback locators provided"}

            # Try primary locator first if provided
            if locator_type and locator_value:
                try:
                    self.logger.info(f"Attempting to double-tap using primary locator: {locator_type}={locator_value}")

                    # Use the controller's double_tap_element method if available
                    if hasattr(self.controller, 'double_tap_element'):
                        result = self.controller.double_tap_element(locator_type, locator_value, timeout, interval)
                    else:
                        # Fallback to tap_element twice
                        self.logger.info("No double_tap_element method available, using tap_element twice")
                        result1 = self.controller.tap_element(locator_type, locator_value, timeout, interval)

                        # If first tap succeeded, do the second tap
                        if isinstance(result1, dict) and result1.get('status') == 'success':
                            time.sleep(0.1)  # Small delay between taps
                            result2 = self.controller.tap_element(locator_type, locator_value, timeout, interval)

                            # If both taps succeeded, return success
                            if isinstance(result2, dict) and result2.get('status') == 'success':
                                return {
                                    "status": "success",
                                    "message": f"Double-tapped element with {locator_type}='{locator_value}'"
                                }
                            else:
                                # Second tap failed
                                return result2
                        else:
                            # First tap failed
                            return result1

                    # If we have a result from double_tap_element, return it
                    if isinstance(result, dict):
                        return result

                except Exception as e:
                    self.logger.warning(f"Error with primary locator: {e}")
                    # Continue to fallbacks if we have them
                    if not fallback_locators:
                        return {"status": "error", "message": f"Double tap with locator failed: {str(e)}"}

            # Try fallback locators in sequence
            if fallback_locators:
                self.logger.info(f"Trying {len(fallback_locators)} fallback locators for double tap")

                # Calculate timeout per locator
                locator_count = len(fallback_locators) + (1 if locator_type and locator_value else 0)
                timeout_per_locator = max(5, timeout // locator_count)  # Minimum 5 seconds per locator

                for i, fallback in enumerate(fallback_locators):
                    fallback_type = fallback.get('locator_type')
                    fallback_value = fallback.get('locator_value')

                    if not fallback_type or not fallback_value:
                        self.logger.warning(f"Skipping invalid fallback locator at index {i}")
                        continue

                    try:
                        self.logger.info(f"Trying fallback locator {i+1}/{len(fallback_locators)}: {fallback_type}={fallback_value}")

                        # Handle different locator types
                        if fallback_type == 'image':
                            # Use image-based double tap
                            self.logger.info(f"Using image-based double tap for fallback locator {i+1}")

                            # Create new params for image-based double tap
                            image_params = {
                                'image_filename': fallback_value,
                                'threshold': params.get('threshold', 0.7),
                                'timeout': timeout_per_locator
                            }

                            # Call ourselves recursively with the new params
                            result = self.execute(image_params)

                            # If successful, return the result with updated message
                            if isinstance(result, dict) and result.get('status') == 'success':
                                result['message'] = f"Double-tapped using image fallback locator {i+1}: {fallback_value}"
                                return result

                            # Log the failure and continue to next fallback
                            if isinstance(result, dict):
                                self.logger.warning(f"Image fallback locator {i+1} failed: {result.get('message')}")

                        elif fallback_type == 'text':
                            # Use text-based double tap
                            self.logger.info(f"Using text-based double tap for fallback locator {i+1}")

                            # Use the controller's tap_on_text method twice if available
                            if hasattr(self.controller, 'tap_on_text'):
                                result1 = self.controller.tap_on_text(fallback_value, timeout=timeout_per_locator)

                                # If first tap succeeded, do the second tap
                                if isinstance(result1, dict) and result1.get('status') == 'success':
                                    time.sleep(0.1)  # Small delay between taps
                                    result2 = self.controller.tap_on_text(fallback_value, timeout=timeout_per_locator)

                                    # If both taps succeeded, return success
                                    if isinstance(result2, dict) and result2.get('status') == 'success':
                                        return {
                                            "status": "success",
                                            "message": f"Double-tapped using text fallback locator {i+1}: {fallback_value}"
                                        }
                                    else:
                                        # Second tap failed, but continue to next fallback
                                        self.logger.warning(f"Text fallback locator {i+1} second tap failed: {result2.get('message')}")
                                        continue
                                else:
                                    # First tap failed, continue to next fallback
                                    self.logger.warning(f"Text fallback locator {i+1} first tap failed: {result1.get('message')}")
                                    continue
                            else:
                                self.logger.warning(f"Controller does not support tap_on_text, skipping text fallback locator {i+1}")
                                continue

                        else:
                            # Use element-based double tap
                            if hasattr(self.controller, 'double_tap_element'):
                                result = self.controller.double_tap_element(fallback_type, fallback_value, timeout_per_locator, interval)

                                # If successful, return the result with updated message
                                if isinstance(result, dict) and result.get('status') == 'success':
                                    result['message'] = f"Double-tapped using fallback locator {i+1}: {fallback_type}={fallback_value}"
                                    return result

                                # Log the failure and continue to next fallback
                                if isinstance(result, dict):
                                    self.logger.warning(f"Fallback locator {i+1} failed: {result.get('message')}")
                            else:
                                # Fallback to tap_element twice
                                self.logger.info(f"No double_tap_element method available, using tap_element twice for fallback {i+1}")
                                result1 = self.controller.tap_element(fallback_type, fallback_value, timeout_per_locator, interval)

                                # If first tap succeeded, do the second tap
                                if isinstance(result1, dict) and result1.get('status') == 'success':
                                    time.sleep(0.1)  # Small delay between taps
                                    result2 = self.controller.tap_element(fallback_type, fallback_value, timeout_per_locator, interval)

                                    # If both taps succeeded, return success
                                    if isinstance(result2, dict) and result2.get('status') == 'success':
                                        return {
                                            "status": "success",
                                            "message": f"Double-tapped using fallback locator {i+1}: {fallback_type}={fallback_value}"
                                        }
                                    else:
                                        # Second tap failed, continue to next fallback
                                        self.logger.warning(f"Fallback locator {i+1} second tap failed: {result2.get('message')}")
                                        continue
                                else:
                                    # First tap failed, continue to next fallback
                                    self.logger.warning(f"Fallback locator {i+1} first tap failed: {result1.get('message')}")
                                    continue

                    except Exception as e:
                        self.logger.warning(f"Error with fallback locator {i+1}: {e}")
                        self.logger.warning(traceback.format_exc())

                # If we get here, all fallbacks failed
                return {"status": "error", "message": f"All locators failed (primary + {len(fallback_locators)} fallbacks)"}

            # If we get here with no fallbacks, the primary locator failed
            return {"status": "error", "message": "Primary locator failed and no fallbacks provided"}

        # Check if we have an image filename
        image_filename = params.get('image_filename')
        if image_filename:
            # Use airtest to find and tap on the image
            threshold = float(params.get('threshold', 0.7))
            timeout = int(params.get('timeout', 20))

            try:
                # Resolve the image path properly
                if not os.path.exists(image_filename):
                    # Try to resolve from reference_images directory
                    try:
                        from config_android import DIRECTORIES
                        reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                        if reference_dir:
                            full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                            if os.path.exists(full_path):
                                image_filename = full_path
                                self.logger.info(f"Resolved image path to: {image_filename}")
                            else:
                                # Try directly in reference_images folder
                                ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                                if os.path.exists(ref_path):
                                    image_filename = ref_path
                                    self.logger.info(f"Resolved image path to: {image_filename}")
                    except (ImportError, Exception) as e:
                        self.logger.warning(f"Could not resolve reference image directory: {e}")

                # If still doesn't exist, return error
                if not os.path.exists(image_filename):
                    return {"status": "error", "message": f"Image file not found: {image_filename}"}

                # Get absolute path for more reliable loading
                abs_path = os.path.abspath(image_filename)
                self.logger.info(f"Using absolute image path: {abs_path}")

                # Ensure Airtest device is initialized
                if hasattr(self.controller, '_ensure_airtest_connected'):
                    airtest_connected = self.controller._ensure_airtest_connected()
                    if not airtest_connected:
                        return {"status": "error", "message": "Failed to connect Airtest device"}

                # Log the device info to help with debugging
                if hasattr(G, 'DEVICE') and G.DEVICE:
                    self.logger.info(f"Current Airtest device: {G.DEVICE}")

                # Use Airtest ONLY for finding the image coordinates
                self.logger.info(f"Using Airtest only for image recognition, not for tapping")

                # Create template and find its position
                template_image = Template(abs_path, threshold=threshold)
                match_pos = wait(template_image, timeout=timeout)
                self.logger.info(f"Found image at position: {match_pos}, now using Appium to double-tap")

                # Skip all Airtest touch methods and go directly to Appium
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # Scale coordinates to device dimensions if needed
                    original_pos = match_pos

                    # Get device dimensions
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        try:
                            # Get actual device dimensions
                            device_width = None
                            device_height = None
                            if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                                device_width = self.controller.device_dimensions.get('width')
                                device_height = self.controller.device_dimensions.get('height')

                            # Get screenshot dimensions
                            if hasattr(self.controller, 'take_screenshot'):
                                screenshot_result = self.controller.take_screenshot()
                                if screenshot_result.get('status') == 'success' and screenshot_result.get('path'):
                                    import cv2
                                    screenshot = cv2.imread(screenshot_result.get('path'))
                                    if screenshot is not None:
                                        screenshot_height, screenshot_width = screenshot.shape[:2]

                                        # Scale coordinates if dimensions differ
                                        if device_width and device_height and screenshot_width != device_width:
                                            scale_x = device_width / screenshot_width
                                            scale_y = device_height / screenshot_height

                                            x, y = match_pos
                                            x = int(x * scale_x)
                                            y = int(y * scale_y)
                                            match_pos = (x, y)

                                            self.logger.info(f"Scaled coordinates from {original_pos} to {match_pos} for device dimensions {device_width}x{device_height}")
                        except Exception as e:
                            self.logger.warning(f"Error scaling coordinates: {e}")

                    self.logger.info(f"Using coordinates for double tap: {match_pos}")

                    x, y = match_pos
                    try:
                        # Try Appium's tap method twice for double-tap
                        self.logger.info(f"Double-tapping at coordinates {match_pos} using Appium driver")
                        self.controller.driver.tap([(int(x), int(y))])
                        time.sleep(0.1)
                        self.controller.driver.tap([(int(x), int(y))])
                        return {"status": "success", "message": f"Double-tapped at {match_pos} using Appium after finding image with Airtest"}
                    except Exception as tap_err:
                        self.logger.warning(f"Appium tap failed: {tap_err}, trying touch actions")

                        try:
                            # Try using Appium's TouchAction for double tap
                            from appium.webdriver.common.touch_action import TouchAction
                            actions = TouchAction(self.controller.driver)
                            actions.tap(x=int(x), y=int(y)).wait(100).tap(x=int(x), y=int(y)).perform()
                            return {"status": "success", "message": f"Double-tapped at {match_pos} using Appium TouchAction after finding image with Airtest"}
                        except Exception as touch_action_err:
                            self.logger.warning(f"Appium TouchAction failed: {touch_action_err}")

                            # Try two separate TouchActions
                            try:
                                from appium.webdriver.common.touch_action import TouchAction
                                TouchAction(self.controller.driver).tap(x=int(x), y=int(y)).perform()
                                time.sleep(0.1)
                                TouchAction(self.controller.driver).tap(x=int(x), y=int(y)).perform()
                                return {"status": "success", "message": f"Double-tapped at {match_pos} using separate Appium TouchActions after finding image with Airtest"}
                            except Exception as separate_err:
                                self.logger.warning(f"Separate TouchActions failed: {separate_err}")

                                # One last attempt with W3C actions if available
                                try:
                                    self.logger.info("Trying W3C Actions API for double-tap")
                                    from selenium.webdriver.common.action_chains import ActionChains
                                    from selenium.webdriver.common.actions.pointer_input import PointerInput
                                    from selenium.webdriver.common.actions.action_builder import ActionBuilder

                                    # Create pointer input
                                    pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                                    # Create action chains
                                    actions = ActionBuilder(self.controller.driver, mouse=pointer)
                                    # First tap
                                    actions.pointer_action.move_to_location(int(x), int(y))
                                    actions.pointer_action.click()
                                    actions.perform()

                                    # Wait and do second tap
                                    time.sleep(0.1)
                                    actions = ActionBuilder(self.controller.driver, mouse=pointer)
                                    actions.pointer_action.move_to_location(int(x), int(y))
                                    actions.pointer_action.click()
                                    actions.perform()

                                    return {"status": "success", "message": f"Double-tapped at {match_pos} using W3C Actions after finding image with Airtest"}
                                except Exception as w3c_err:
                                    self.logger.error(f"All Appium double-tap methods failed: {w3c_err}")

                                    # Only now try the Airtest touch as a last resort
                                    self.logger.warning("All Appium methods failed, falling back to Airtest touch as last resort")
                                    touch(template_image)
                                    time.sleep(0.1)
                                    touch(template_image)
                                    return {"status": "success", "message": f"Double-tapped on image using Airtest touch as fallback"}
                else:
                    self.logger.warning("No Appium driver available, using Airtest touch")
                    touch(template_image)
                    time.sleep(0.1)
                    touch(template_image)
                    return {"status": "success", "message": f"Double-tapped on image using Airtest touch (no Appium driver)"}

            except TargetNotFoundError:
                self.logger.error(f"Image '{os.path.basename(abs_path)}' not found within {timeout} seconds with threshold {threshold}")
                # Try exists() to see if we can find it with a lower threshold
                try:
                    match_result = exists(template_image)
                    if match_result:
                        self.logger.info(f"Found with exists() but below threshold: {match_result}")
                    else:
                        self.logger.info("Image not found with exists() either")
                except Exception as e_exists:
                    self.logger.warning(f"Error checking with exists(): {e_exists}")

                return {"status": "error", "message": f"Image not found within {timeout} seconds"}

            except Exception as e:
                self.logger.error(f"Error executing double tap on image: {e}")
                traceback.print_exc()
                return {"status": "error", "message": f"Double tap on image failed: {str(e)}"}

        # Regular coordinate-based double tap
        x = params.get('x')
        y = params.get('y')

        if x is None or y is None:
            return {"status": "error", "message": "Missing x or y coordinates"}

        try:
            # Execute double tap using the controller if available
            if hasattr(self.controller, 'double_tap'):
                result = self.controller.double_tap(x, y)

                # Check if the result is a dict or a boolean
                if isinstance(result, dict):
                    # Controller returned a dict with status and message
                    return result
                elif result is True:
                    # Controller returned a boolean success
                    return {"status": "success", "message": f"Double tapped at ({x}, {y})"}
                else:
                    # Controller returned a boolean failure
                    return {"status": "error", "message": f"Failed to double tap at ({x}, {y})"}

            # If controller doesn't have double_tap, use fallback with two regular taps
            self.logger.info(f"Using fallback double tap at ({x}, {y})")

            # Execute first tap
            tap1 = self.controller.tap(x, y)
            if not tap1:
                return {"status": "error", "message": f"Failed to execute first tap at ({x}, {y})"}

            # Small delay between taps (100ms)
            time.sleep(0.1)

            # Execute second tap
            tap2 = self.controller.tap(x, y)
            if not tap2:
                return {"status": "error", "message": f"Failed to execute second tap at ({x}, {y})"}

            return {"status": "success", "message": f"Double tapped at ({x}, {y}) using consecutive taps"}

        except Exception as e:
            self.logger.error(f"Error executing double tap action: {e}")
            return {"status": "error", "message": f"Double tap action failed: {str(e)}"}