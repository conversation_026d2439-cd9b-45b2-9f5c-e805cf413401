from .base_action import BaseAction
import logging
import traceback

class AndroidFunctionsAction(BaseAction):
    """Handler for Android-specific functions using UiAutomator2 and ADB"""

    def execute(self, params):
        """
        Execute Android-specific functions using UiAutomator2 and ADB

        Args:
            params: Dictionary containing:
                - function_name: Name of the Android function to execute
                - Additional parameters based on the function

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the function name from params
        function_name = params.get('function_name')
        if not function_name:
            return {"status": "error", "message": "No function name provided"}

        self.logger.info(f"Executing Android function: {function_name}")

        try:
            # Send Key Events
            if function_name == 'send_key_event':
                key_event = params.get('key_event')
                if not key_event:
                    return {"status": "error", "message": "No key event provided"}
                
                # Map key event names to Android key codes
                key_codes = {
                    # Navigation keys
                    'BACK': 4,
                    'HOME': 3,
                    'MENU': 82,
                    'RECENT_APPS': 187,
                    
                    # Input keys
                    'ENTER': 66,
                    'TAB': 61,
                    'SPACE': 62,
                    'DELETE': 67,
                    'BACKSPACE': 67,
                    
                    # Number keys
                    '0': 7, '1': 8, '2': 9, '3': 10, '4': 11,
                    '5': 12, '6': 13, '7': 14, '8': 15, '9': 16,
                    
                    # Volume keys
                    'VOLUME_UP': 24,
                    'VOLUME_DOWN': 25,
                    'VOLUME_MUTE': 164,
                    
                    # Power and system keys
                    'POWER': 26,
                    'SLEEP': 223,
                    'WAKEUP': 224,
                    
                    # Media keys
                    'MEDIA_PLAY': 126,
                    'MEDIA_PAUSE': 127,
                    'MEDIA_STOP': 86,
                    'MEDIA_NEXT': 87,
                    'MEDIA_PREVIOUS': 88,
                    'MEDIA_PLAY_PAUSE': 85,
                    
                    # Additional common keys
                    'ESCAPE': 111,
                    'SEARCH': 84,
                    'CAMERA': 27,
                    'CALL': 5,
                    'ENDCALL': 6,
                    'NOTIFICATION': 83,
                    'SETTINGS': 176
                }
                
                key_code = key_codes.get(key_event)
                if key_code is None:
                    return {"status": "error", "message": f"Unsupported key event: {key_event}"}
                
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(key_code)
                    return {"status": "success", "message": f"Sent key event: {key_event}"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', str(key_code)])
                    return {"status": "success", "message": f"Sent key event via ADB: {key_event}"}

            # Accept Alert
            elif function_name == 'accept_alert':
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Try to find and click OK/Accept button
                        try:
                            accept_button = self.controller.driver.find_element('xpath', '//*[@text="OK" or @text="Accept" or @text="Allow" or @text="Yes"]')
                            accept_button.click()
                            return {"status": "success", "message": "Accepted alert"}
                        except:
                            # Fallback: press ENTER key
                            self.controller.driver.press_keycode(66)  # KEYCODE_ENTER
                            return {"status": "success", "message": "Accepted alert using ENTER key"}
                    else:
                        # Use ADB fallback
                        result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '66'])
                        return {"status": "success", "message": "Accepted alert via ADB"}
                except Exception as e:
                    return {"status": "warning", "message": f"Could not find alert to accept: {str(e)}"}

            # Dismiss Alert
            elif function_name == 'dismiss_alert':
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Try to find and click Cancel/Dismiss button
                        try:
                            dismiss_button = self.controller.driver.find_element('xpath', '//*[@text="Cancel" or @text="Dismiss" or @text="No" or @text="Deny"]')
                            dismiss_button.click()
                            return {"status": "success", "message": "Dismissed alert"}
                        except:
                            # Fallback: press BACK key
                            self.controller.driver.press_keycode(4)  # KEYCODE_BACK
                            return {"status": "success", "message": "Dismissed alert using BACK key"}
                    else:
                        # Use ADB fallback
                        result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '4'])
                        return {"status": "success", "message": "Dismissed alert via ADB"}
                except Exception as e:
                    return {"status": "warning", "message": f"Could not find alert to dismiss: {str(e)}"}

            # Switch Context
            elif function_name == 'switch_context':
                context = params.get('context')
                if not context:
                    return {"status": "error", "message": "No context provided"}
                
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    try:
                        if context.lower() == 'native':
                            # Switch to native context
                            contexts = self.controller.driver.contexts
                            native_context = None
                            for ctx in contexts:
                                if 'NATIVE_APP' in ctx:
                                    native_context = ctx
                                    break
                            
                            if native_context:
                                self.controller.driver.switch_to.context(native_context)
                                return {"status": "success", "message": f"Switched to native context: {native_context}"}
                            else:
                                return {"status": "error", "message": "Native context not found"}
                                
                        elif context.lower() == 'web':
                            # Switch to webview context
                            contexts = self.controller.driver.contexts
                            web_context = None
                            for ctx in contexts:
                                if 'WEBVIEW' in ctx:
                                    web_context = ctx
                                    break
                            
                            if web_context:
                                self.controller.driver.switch_to.context(web_context)
                                return {"status": "success", "message": f"Switched to web context: {web_context}"}
                            else:
                                return {"status": "error", "message": "Web context not found"}
                        else:
                            return {"status": "error", "message": f"Invalid context: {context}. Use 'native' or 'web'"}
                    except Exception as e:
                        return {"status": "error", "message": f"Failed to switch context: {str(e)}"}
                else:
                    return {"status": "error", "message": "Driver not available for context switching"}

            # Clear App
            elif function_name == 'clear_app':
                package_name = params.get('package_name', '')
                if not package_name:
                    # Try to get current app package
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        try:
                            package_name = self.controller.driver.current_package
                        except:
                            return {"status": "error", "message": "No package name provided and could not detect current app"}
                    else:
                        return {"status": "error", "message": "No package name provided"}
                
                try:
                    if hasattr(self.controller, '_run_adb_command'):
                        result = self.controller._run_adb_command(['shell', 'pm', 'clear', package_name])
                        return {"status": "success", "message": f"Cleared app data for {package_name}"}
                    else:
                        return {"status": "error", "message": "ADB not available for clearing app data"}
                except Exception as e:
                    return {"status": "error", "message": f"Failed to clear app data: {str(e)}"}

            # Set Connectivity
            elif function_name == 'set_connectivity':
                connectivity_type = params.get('connectivity_type')
                state = params.get('state')
                
                if not connectivity_type or state is None:
                    return {"status": "error", "message": "Connectivity type and state are required"}
                
                try:
                    if connectivity_type.lower() == 'wifi':
                        if state:
                            result = self.controller._run_adb_command(['shell', 'svc', 'wifi', 'enable'])
                            return {"status": "success", "message": "Enabled WiFi"}
                        else:
                            result = self.controller._run_adb_command(['shell', 'svc', 'wifi', 'disable'])
                            return {"status": "success", "message": "Disabled WiFi"}
                    elif connectivity_type.lower() == 'data':
                        if state:
                            result = self.controller._run_adb_command(['shell', 'svc', 'data', 'enable'])
                            return {"status": "success", "message": "Enabled mobile data"}
                        else:
                            result = self.controller._run_adb_command(['shell', 'svc', 'data', 'disable'])
                            return {"status": "success", "message": "Disabled mobile data"}
                    elif connectivity_type.lower() == 'airplane':
                        # Toggle airplane mode
                        if state:
                            result = self.controller._run_adb_command(['shell', 'settings', 'put', 'global', 'airplane_mode_on', '1'])
                            self.controller._run_adb_command(['shell', 'am', 'broadcast', '-a', 'android.intent.action.AIRPLANE_MODE', '--ez', 'state', 'true'])
                            return {"status": "success", "message": "Enabled airplane mode"}
                        else:
                            result = self.controller._run_adb_command(['shell', 'settings', 'put', 'global', 'airplane_mode_on', '0'])
                            self.controller._run_adb_command(['shell', 'am', 'broadcast', '-a', 'android.intent.action.AIRPLANE_MODE', '--ez', 'state', 'false'])
                            return {"status": "success", "message": "Disabled airplane mode"}
                    else:
                        return {"status": "error", "message": f"Unsupported connectivity type: {connectivity_type}"}
                except Exception as e:
                    return {"status": "error", "message": f"Failed to set connectivity: {str(e)}"}

            # Get Clipboard
            elif function_name == 'get_clipboard':
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        content = self.controller.driver.get_clipboard()
                        return {"status": "success", "message": f"Clipboard content: {content}", "content": content}
                    else:
                        # Use ADB fallback - this might not work on all devices
                        try:
                            result = self.controller._run_adb_command(['shell', 'am', 'broadcast', '-a', 'clipper.get'])
                            return {"status": "success", "message": "Retrieved clipboard via ADB", "content": result}
                        except:
                            return {"status": "warning", "message": "Could not retrieve clipboard content via ADB"}
                except Exception as e:
                    return {"status": "error", "message": f"Failed to get clipboard: {str(e)}"}

            # Set Clipboard
            elif function_name == 'set_clipboard':
                content = params.get('content', '')
                if not content:
                    return {"status": "error", "message": "No content provided for clipboard"}
                
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        self.controller.driver.set_clipboard(content)
                        return {"status": "success", "message": f"Set clipboard content to: {content}"}
                    else:
                        # Use ADB fallback
                        try:
                            result = self.controller._run_adb_command(['shell', 'am', 'broadcast', '-a', 'clipper.set', '-e', 'text', content])
                            return {"status": "success", "message": f"Set clipboard content via ADB: {content}"}
                        except:
                            return {"status": "warning", "message": "Could not set clipboard content via ADB"}
                except Exception as e:
                    return {"status": "error", "message": f"Failed to set clipboard: {str(e)}"}

            # Inject Emulator Camera Image
            elif function_name == 'inject_camera_image':
                image_path = params.get('image_path', '')
                if not image_path:
                    return {"status": "error", "message": "No image path provided"}
                
                try:
                    # This feature is only available for emulators
                    # Push image to emulator and set as camera image
                    import os
                    if not os.path.exists(image_path):
                        return {"status": "error", "message": f"Image file not found: {image_path}"}
                    
                    # Push image to device
                    device_path = '/sdcard/camera_inject.jpg'
                    result = self.controller._run_adb_command(['push', image_path, device_path])
                    
                    # Use emulator console command to inject camera image
                    # Note: This requires emulator console access which might not be available
                    return {"status": "warning", "message": "Camera image injection requires emulator console access. Image pushed to device at " + device_path}
                    
                except Exception as e:
                    return {"status": "error", "message": f"Failed to inject camera image: {str(e)}"}

            # Tap on Element using UISelector
            elif function_name == 'tap_uiselector':
                uiselector = params.get('uiselector')
                if not uiselector:
                    return {"status": "error", "message": "No UISelector provided"}

                self.logger.info(f"Attempting to tap element using UISelector: {uiselector}")

                try:
                    # Use Appium's UIAutomator2 driver to find and tap element using UISelector
                    # This is the proper way according to BrowserStack documentation
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        try:
                            # Import AppiumBy for proper UIAutomator2 usage (newer Appium versions)
                            try:
                                from appium.webdriver.common.appiumby import AppiumBy
                                locator_type = AppiumBy.ANDROID_UIAUTOMATOR
                            except ImportError:
                                # Fallback to MobileBy for older Appium versions
                                from appium.webdriver.common.mobileby import MobileBy
                                locator_type = MobileBy.ANDROID_UIAUTOMATOR

                            # Use AppiumBy.ANDROID_UIAUTOMATOR with the UISelector string
                            # This is the correct approach as per Appium documentation
                            element = self.controller.driver.find_element(locator_type, uiselector)
                            if element:
                                element.click()
                                return {"status": "success", "message": f"Tapped element using UISelector: {uiselector}"}
                            else:
                                return {"status": "error", "message": f"Element not found with UISelector: {uiselector}"}
                        except Exception as appium_error:
                            self.logger.warning(f"Appium UISelector failed: {appium_error}")

                            # Provide helpful error message based on the error type
                            error_message = str(appium_error).lower()

                            if "no such element" in error_message:
                                return {
                                    "status": "error",
                                    "message": f"Element not found with UISelector: {uiselector}. "
                                              f"Suggestions: 1) Verify the UISelector syntax is correct, "
                                              f"2) Check if element is visible on screen, "
                                              f"3) Try alternative locators like resourceId, text, or className, "
                                              f"4) Use coordinate-based actions if element position is known."
                                }
                            elif "invalid selector" in error_message or "syntax" in error_message:
                                return {
                                    "status": "error",
                                    "message": f"Invalid UISelector syntax: {uiselector}. "
                                              f"Please check the UISelector format. Examples: "
                                              f"'new UiSelector().text(\"Login\")', "
                                              f"'new UiSelector().resourceId(\"com.app:id/button\")', "
                                              f"'new UiSelector().description(\"Login button\")'"
                                }
                            else:
                                return {
                                    "status": "error",
                                    "message": f"UISelector execution failed: {appium_error}. "
                                              f"This may be due to device restrictions or UIAutomator2 limitations. "
                                              f"Try using alternative locator strategies or coordinate-based actions."
                                }
                    else:
                        return {"status": "error", "message": "Appium driver not available for UISelector"}

                except Exception as e:
                    return {"status": "error", "message": f"Failed to tap element with UISelector: {str(e)}"}

            # Clear Text - CTRL+A followed by DELETE
            elif function_name == 'clear_text':
                try:
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Method 1: Use key combinations (CTRL+A then DELETE)
                        try:
                            # Send CTRL+A to select all text
                            self.controller.driver.press_keycode(1)  # KEYCODE_CTRL_LEFT
                            self.controller.driver.press_keycode(29)  # KEYCODE_A
                            # Send DELETE to clear selected text
                            self.controller.driver.press_keycode(67)  # KEYCODE_DEL
                            return {"status": "success", "message": "Cleared text using key combination"}
                        except:
                            # Method 2: Use ADB input commands as fallback
                            try:
                                # Select all text using key events
                                result1 = self.controller._run_adb_command(['shell', 'input', 'keyevent', '113'])  # CTRL_LEFT
                                result2 = self.controller._run_adb_command(['shell', 'input', 'keyevent', '29'])   # A
                                result3 = self.controller._run_adb_command(['shell', 'input', 'keyevent', '67'])   # DEL
                                return {"status": "success", "message": "Cleared text using ADB key events"}
                            except:
                                # Method 3: Try to find focused element and clear it
                                try:
                                    # Get the currently focused element
                                    focused_element = self.controller.driver.switch_to.active_element
                                    if focused_element:
                                        focused_element.clear()
                                        return {"status": "success", "message": "Cleared text using element.clear()"}
                                    else:
                                        return {"status": "warning", "message": "No focused element found to clear"}
                                except:
                                    return {"status": "warning", "message": "Could not clear text using any method"}
                    else:
                        # Use ADB fallback when no driver available
                        try:
                            # Send CTRL+A then DELETE using ADB
                            result1 = self.controller._run_adb_command(['shell', 'input', 'keyevent', '113'])  # CTRL_LEFT
                            result2 = self.controller._run_adb_command(['shell', 'input', 'keyevent', '29'])   # A
                            result3 = self.controller._run_adb_command(['shell', 'input', 'keyevent', '67'])   # DEL
                            return {"status": "success", "message": "Cleared text via ADB"}
                        except Exception as adb_error:
                            return {"status": "error", "message": f"Failed to clear text via ADB: {str(adb_error)}"}
                except Exception as e:
                    return {"status": "error", "message": f"Failed to clear text: {str(e)}"}

            # Legacy functions for backward compatibility
            elif function_name == 'home':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'HOME'})
            elif function_name == 'back':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'BACK'})
            elif function_name == 'recent_apps':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'RECENT_APPS'})
            elif function_name == 'menu':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'MENU'})
            elif function_name == 'power':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'POWER'})
            elif function_name == 'volume_up':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'VOLUME_UP'})
            elif function_name == 'volume_down':
                return self.execute({'function_name': 'send_key_event', 'key_event': 'VOLUME_DOWN'})

            else:
                return {"status": "error", "message": f"Unsupported Android function: {function_name}"}

        except Exception as e:
            self.logger.error(f"Error executing Android function {function_name}: {e}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Android function execution failed: {str(e)}"}

    def _parse_uiselector(self, uiselector):
        """
        Parse UISelector string to extract attributes

        Args:
            uiselector (str): UISelector string like 'new UiSelector().text("Button").className("android.widget.Button")'

        Returns:
            dict: Dictionary of attributes and their values
        """
        import re

        attributes = {}

        # Common UISelector methods and their corresponding XML attributes
        method_mappings = {
            'text': 'text',
            'textContains': 'text',  # Will use contains logic
            'textMatches': 'text',   # Will use regex logic
            'textStartsWith': 'text', # Will use startswith logic
            'description': 'content-desc',
            'descriptionContains': 'content-desc',
            'descriptionMatches': 'content-desc',
            'descriptionStartsWith': 'content-desc',
            'className': 'class',
            'classNameMatches': 'class',
            'resourceId': 'resource-id',
            'resourceIdMatches': 'resource-id',
            'packageName': 'package',
            'packageNameMatches': 'package',
            'checkable': 'checkable',
            'checked': 'checked',
            'clickable': 'clickable',
            'enabled': 'enabled',
            'focusable': 'focusable',
            'focused': 'focused',
            'longClickable': 'long-clickable',
            'scrollable': 'scrollable',
            'selected': 'selected'
        }

        try:
            # Extract method calls from UISelector string
            # Enhanced pattern to handle various quote types and nested parentheses
            pattern = r'\.(\w+)\(([^)]*(?:\([^)]*\)[^)]*)*)\)'
            matches = re.findall(pattern, uiselector)

            self.logger.debug(f"UISelector regex matches: {matches}")

            for method, value in matches:
                if method in method_mappings:
                    xml_attr = method_mappings[method]

                    # Clean up the value (remove quotes and handle different quote types)
                    clean_value = value.strip()

                    # Handle different value formats
                    if clean_value.startswith('"') and clean_value.endswith('"'):
                        clean_value = clean_value[1:-1]  # Remove double quotes
                    elif clean_value.startswith("'") and clean_value.endswith("'"):
                        clean_value = clean_value[1:-1]  # Remove single quotes
                    elif clean_value.lower() in ['true', 'false']:
                        # Keep boolean values as strings for comparison
                        clean_value = clean_value.lower()

                    # Store the method type and value for special handling
                    attributes[xml_attr] = {
                        'value': clean_value,
                        'method': method
                    }

                    self.logger.debug(f"Parsed UISelector method: {method} -> {xml_attr} = {clean_value}")
                else:
                    self.logger.warning(f"Unknown UISelector method: {method}")

            self.logger.info(f"Successfully parsed UISelector with {len(attributes)} attributes")
            return attributes

        except Exception as e:
            self.logger.error(f"Error parsing UISelector '{uiselector}': {e}")
            return {}

    def _find_element_in_xml(self, root, selector_attrs):
        """
        Find element in XML tree that matches UISelector attributes

        Args:
            root: XML root element
            selector_attrs (dict): Parsed UISelector attributes

        Returns:
            Element or None: Matching XML element
        """
        import re

        def matches_element(element, attrs):
            """Check if element matches all selector attributes"""
            self.logger.debug(f"Checking element: {element.tag} with attributes: {element.attrib}")

            for xml_attr, attr_info in attrs.items():
                element_value = element.get(xml_attr, '')
                expected_value = attr_info['value']
                method = attr_info['method']

                self.logger.debug(f"Comparing {xml_attr}: '{element_value}' vs '{expected_value}' (method: {method})")

                # Handle different matching methods
                if method in ['text', 'description', 'className', 'resourceId', 'packageName']:
                    # Exact match
                    if element_value != expected_value:
                        self.logger.debug(f"Exact match failed for {xml_attr}")
                        return False
                elif method in ['textContains', 'descriptionContains']:
                    # Contains match
                    if expected_value not in element_value:
                        self.logger.debug(f"Contains match failed for {xml_attr}")
                        return False
                elif method in ['textMatches', 'descriptionMatches', 'classNameMatches', 'resourceIdMatches', 'packageNameMatches']:
                    # Regex match
                    try:
                        if not re.search(expected_value, element_value):
                            self.logger.debug(f"Regex match failed for {xml_attr}")
                            return False
                    except re.error as regex_err:
                        self.logger.warning(f"Invalid regex pattern '{expected_value}': {regex_err}")
                        return False
                elif method in ['textStartsWith', 'descriptionStartsWith']:
                    # Starts with match
                    if not element_value.startswith(expected_value):
                        self.logger.debug(f"StartsWith match failed for {xml_attr}")
                        return False
                elif method in ['checkable', 'checked', 'clickable', 'enabled', 'focusable', 'focused', 'longClickable', 'scrollable', 'selected']:
                    # Boolean attributes
                    expected_bool = expected_value.lower() in ['true', '1', 'yes']
                    element_bool = element_value.lower() in ['true', '1', 'yes']
                    if element_bool != expected_bool:
                        self.logger.debug(f"Boolean match failed for {xml_attr}: expected {expected_bool}, got {element_bool}")
                        return False

                self.logger.debug(f"Match successful for {xml_attr}")

            return True

        # Search through all elements in the XML tree
        elements_checked = 0
        for element in root.iter():
            elements_checked += 1
            if matches_element(element, selector_attrs):
                self.logger.info(f"Found matching element after checking {elements_checked} elements")
                return element

        self.logger.warning(f"No matching element found after checking {elements_checked} elements")

        # Debug: Log some sample elements for troubleshooting
        sample_elements = []
        for i, element in enumerate(root.iter()):
            if i < 5:  # Log first 5 elements
                sample_elements.append({
                    'tag': element.tag,
                    'attrib': dict(element.attrib)
                })
        self.logger.debug(f"Sample elements from XML: {sample_elements}")

        return None
