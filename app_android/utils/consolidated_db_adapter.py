#!/usr/bin/env python3
"""
Consolidated Database Adapter

This module provides an adapter to use the consolidated database
instead of the separate database files used by the original application.
"""

import sqlite3
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class ConsolidatedDBAdapter:
    """
    Adapter class to interface with the consolidated database.
    Provides methods that match the original database interfaces.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the consolidated database adapter.
        
        Args:
            db_path: Path to the consolidated database file.
                    If None, uses default location: database_consolidation/consolidated.db
        """
        if db_path:
            self.db_path = Path(db_path)
        else:
            # Default location in database_consolidation directory
            project_root = Path(__file__).resolve().parent.parent.parent
            self.db_path = project_root / 'database_consolidation' / 'consolidated.db'
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"Consolidated database not found at: {self.db_path}")
        
        logger.info(f"Using consolidated database at: {self.db_path}")
    
    def get_connection(self):
        """Get a database connection."""
        return sqlite3.connect(str(self.db_path))
    
    # Global Values methods (compatible with GlobalValuesDB)
    def get_global_value(self, name: str, platform: str = 'shared') -> Optional[str]:
        """
        Get a global value by name and platform.
        
        Args:
            name: The name of the global value
            platform: The platform ('ios', 'android', 'shared')
            
        Returns:
            The value as a string, or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT value FROM globals WHERE name = ? AND platform = ?",
                (name, platform)
            )
            result = cursor.fetchone()
            return result[0] if result else None
    
    def set_global_value(self, name: str, value: str, platform: str = 'shared', 
                        category: str = 'variable', value_type: str = 'string') -> bool:
        """
        Set a global value.
        
        Args:
            name: The name of the global value
            value: The value to set
            platform: The platform ('ios', 'android', 'shared')
            category: The category ('constant', 'variable', 'config', 'cache')
            value_type: The value type ('string', 'number', 'boolean', 'json', 'encrypted')
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """INSERT OR REPLACE INTO globals 
                       (name, value, platform, category, value_type, created_at, updated_at)
                       VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))""",
                    (name, value, platform, category, value_type)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error setting global value {name}: {e}")
            return False
    
    def get_all_global_values(self, platform: str = 'shared') -> Dict[str, str]:
        """
        Get all global values for a platform.
        
        Args:
            platform: The platform to get values for
            
        Returns:
            Dictionary of name -> value mappings
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT name, value FROM globals WHERE platform = ?",
                (platform,)
            )
            return dict(cursor.fetchall())
    
    # Settings methods (compatible with SettingsDB)
    def get_setting(self, key: str, platform: str = 'shared') -> Optional[str]:
        """
        Get a setting value by key and platform.
        
        Args:
            key: The setting key
            platform: The platform ('ios', 'android', 'shared')
            
        Returns:
            The setting value as a string, or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT value FROM settings WHERE key = ? AND platform = ?",
                (key, platform)
            )
            result = cursor.fetchone()
            return result[0] if result else None
    
    def set_setting(self, key: str, value: str, platform: str = 'shared', 
                   category: str = 'general') -> bool:
        """
        Set a setting value.
        
        Args:
            key: The setting key
            value: The setting value
            platform: The platform ('ios', 'android', 'shared')
            category: The setting category
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """INSERT OR REPLACE INTO settings 
                       (key, value, platform, category, created_at, updated_at)
                       VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))""",
                    (key, value, platform, category)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error setting setting {key}: {e}")
            return False
    
    def get_all_settings(self, platform: str = 'shared') -> Dict[str, str]:
        """
        Get all settings for a platform.
        
        Args:
            platform: The platform to get settings for
            
        Returns:
            Dictionary of key -> value mappings
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT key, value FROM settings WHERE platform = ?",
                (platform,)
            )
            return dict(cursor.fetchall())
    
    # Directory paths methods
    def get_directory_path(self, name: str, platform: str = 'shared') -> Optional[str]:
        """
        Get a directory path by name and platform.
        
        Args:
            name: The directory name (e.g., 'TEST_CASES', 'REPORTS')
            platform: The platform ('ios', 'android', 'shared')
            
        Returns:
            The directory path as a string, or None if not found
        """
        # First try platform-specific, then fall back to shared
        for p in [platform, 'shared'] if platform != 'shared' else ['shared']:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT path FROM directory_paths WHERE name = ? AND platform = ?",
                    (name, p)
                )
                result = cursor.fetchone()
                if result:
                    return result[0]
        return None
    
    def set_directory_path(self, name: str, path: str, platform: str = 'shared') -> bool:
        """
        Set a directory path.
        
        Args:
            name: The directory name
            path: The directory path
            platform: The platform ('ios', 'android', 'shared')
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """INSERT OR REPLACE INTO directory_paths 
                       (name, path, platform, created_at, updated_at)
                       VALUES (?, ?, ?, datetime('now'), datetime('now'))""",
                    (name, path, platform)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error setting directory path {name}: {e}")
            return False
    
    def get_all_directory_paths(self, platform: str = 'shared') -> Dict[str, str]:
        """
        Get all directory paths for a platform.
        
        Args:
            platform: The platform to get paths for
            
        Returns:
            Dictionary of name -> path mappings
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT name, path FROM directory_paths WHERE platform = ?",
                (platform,)
            )
            return dict(cursor.fetchall())
    
    # Environment methods
    def get_environment(self, name: str, platform: str = 'shared') -> Optional[Dict[str, Any]]:
        """
        Get an environment configuration by name and platform.
        
        Args:
            name: The environment name
            platform: The platform ('ios', 'android', 'shared')
            
        Returns:
            Environment configuration as a dictionary, or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT config FROM environments WHERE name = ? AND platform = ?",
                (name, platform)
            )
            result = cursor.fetchone()
            if result:
                try:
                    return json.loads(result[0])
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON in environment {name}")
                    return None
            return None
    
    # Test suite methods
    def get_test_suite(self, suite_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a test suite by ID.
        
        Args:
            suite_id: The test suite ID
            
        Returns:
            Test suite data as a dictionary, or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM test_suites WHERE id = ?",
                (suite_id,)
            )
            result = cursor.fetchone()
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))
            return None
    
    def get_all_test_suites(self, platform: str = None) -> List[Dict[str, Any]]:
        """
        Get all test suites, optionally filtered by platform.
        
        Args:
            platform: Optional platform filter ('ios', 'android', 'shared')
            
        Returns:
            List of test suite dictionaries
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if platform:
                cursor.execute(
                    "SELECT * FROM test_suites WHERE platform = ?",
                    (platform,)
                )
            else:
                cursor.execute("SELECT * FROM test_suites")
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    # Execution tracking methods
    def log_execution(self, suite_id: str, test_case_id: str, status: str, 
                     platform: str, details: Optional[str] = None) -> bool:
        """
        Log a test execution.
        
        Args:
            suite_id: The test suite ID
            test_case_id: The test case ID
            status: The execution status
            platform: The platform
            details: Optional execution details
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """INSERT INTO execution_tracker 
                       (suite_id, test_case_id, status, platform, details, executed_at)
                       VALUES (?, ?, ?, ?, ?, datetime('now'))""",
                    (suite_id, test_case_id, status, platform, details)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error logging execution: {e}")
            return False


# Global instance for easy access
_consolidated_db = None

def get_consolidated_db(db_path: Optional[str] = None) -> ConsolidatedDBAdapter:
    """
    Get the global consolidated database adapter instance.
    
    Args:
        db_path: Optional path to the database file
        
    Returns:
        ConsolidatedDBAdapter instance
    """
    global _consolidated_db
    if _consolidated_db is None:
        _consolidated_db = ConsolidatedDBAdapter(db_path)
    return _consolidated_db