# Optimized Session Manager for Android Performance
# Implements Step 3 optimizations from ANDROID_PERFORMANCE_RECOMMENDATIONS.md

import time
import threading
import logging
from typing import Optional, Dict, Any, Callable
from contextlib import contextmanager

from app_android.config.performance_config import performance_config

class OptimizedSessionManager:
    """
    Optimized session manager with intelligent health checks and session validation
    """
    
    def __init__(self, device_controller):
        self.device_controller = device_controller
        self.logger = logging.getLogger(__name__)
        
        # Session state tracking
        self.session_healthy = True
        self.consecutive_failures = 0
        self.last_validation_time = 0
        self.last_health_check_time = 0
        
        # Performance metrics
        self.health_check_count = 0
        self.health_check_failures = 0
        self.session_recoveries = 0
        self.validation_skips = 0
        
        # Thread safety
        self._session_lock = threading.Lock()
        self._health_checks_suspended = False
        
        self.logger.info("OptimizedSessionManager initialized")
    
    @contextmanager
    def suspend_health_checks(self):
        """
        Context manager to suspend health checks during critical operations
        """
        self._health_checks_suspended = True
        self.logger.debug("Health checks suspended")
        try:
            yield
        finally:
            self._health_checks_suspended = False
            self.logger.debug("Health checks resumed")
    
    def should_validate_session(self) -> bool:
        """
        Determine if session validation should be performed
        
        Returns:
            bool: True if validation should be performed
        """
        current_time = time.time()
        
        # Skip if health checks are suspended
        if self._health_checks_suspended:
            self.validation_skips += 1
            return False
        
        # Check if enough time has passed since last validation
        time_since_validation = current_time - self.last_validation_time
        if time_since_validation < performance_config.session_validation_frequency:
            self.validation_skips += 1
            return False
        
        return True
    
    def should_perform_health_check(self) -> bool:
        """
        Determine if health check should be performed
        
        Returns:
            bool: True if health check should be performed
        """
        # Skip if health checks are suspended
        if self._health_checks_suspended:
            return False
        
        # Use performance config to determine timing
        return performance_config.should_perform_health_check()
    
    def perform_optimized_health_check(self) -> bool:
        """
        Perform an optimized health check with reduced overhead
        
        Returns:
            bool: True if session is healthy
        """
        if not self.should_perform_health_check():
            return self.session_healthy  # Return cached state
        
        start_time = time.time()
        
        try:
            with self._session_lock:
                self.health_check_count += 1
                
                # Perform lightweight health check
                is_healthy = self._lightweight_health_check()
                
                # Update state
                self.session_healthy = is_healthy
                performance_config.update_health_check_time()
                self.last_health_check_time = time.time()
                
                if is_healthy:
                    self.consecutive_failures = 0
                else:
                    self.consecutive_failures += 1
                    self.health_check_failures += 1
                
                # Log performance
                duration = time.time() - start_time
                if duration > performance_config.performance_warning_threshold:
                    self.logger.warning(f"Health check took {duration:.2f}s (threshold: {performance_config.performance_warning_threshold}s)")
                
                self.logger.debug(f"Health check result: {is_healthy} (took {duration:.2f}s)")
                return is_healthy
                
        except Exception as e:
            self.logger.error(f"Error during health check: {e}")
            self.health_check_failures += 1
            self.consecutive_failures += 1
            self.session_healthy = False
            return False
    
    def _lightweight_health_check(self) -> bool:
        """
        Perform a lightweight health check with minimal overhead
        
        Returns:
            bool: True if session appears healthy
        """
        try:
            # Check if device controller and driver exist
            if not self.device_controller or not hasattr(self.device_controller, 'driver'):
                return False
            
            driver = self.device_controller.driver
            if not driver:
                return False
            
            # Simple session check with timeout
            try:
                # Use a very lightweight operation
                session_id = driver.session_id
                if not session_id:
                    return False
                
                # Quick capability check (cached by driver)
                capabilities = driver.capabilities
                if not capabilities:
                    return False
                
                return True
                
            except Exception as e:
                error_msg = str(e).lower()
                # Check for session termination indicators
                if any(err in error_msg for err in [
                    "nosuchdriver", "no such session", "session is either terminated",
                    "session not found", "invalid session id", "session has been terminated"
                ]):
                    self.logger.warning(f"Session terminated detected: {e}")
                    return False
                else:
                    # Minor errors don't necessarily mean session is dead
                    self.logger.debug(f"Minor health check error: {e}")
                    return True
                    
        except Exception as e:
            self.logger.debug(f"Health check exception: {e}")
            return False
    
    def validate_session_if_needed(self) -> bool:
        """
        Validate session only if needed based on frequency settings
        
        Returns:
            bool: True if session is valid or validation was skipped
        """
        if not self.should_validate_session():
            return self.session_healthy  # Return cached state
        
        start_time = time.time()
        
        try:
            with self._session_lock:
                self.last_validation_time = time.time()
                
                # Perform validation
                is_valid = self._validate_session_state()
                
                # Update state
                self.session_healthy = is_valid
                
                if not is_valid:
                    self.consecutive_failures += 1
                else:
                    self.consecutive_failures = 0
                
                # Log performance
                duration = time.time() - start_time
                if duration > performance_config.performance_warning_threshold:
                    self.logger.warning(f"Session validation took {duration:.2f}s (threshold: {performance_config.performance_warning_threshold}s)")
                
                self.logger.debug(f"Session validation result: {is_valid} (took {duration:.2f}s)")
                return is_valid
                
        except Exception as e:
            self.logger.error(f"Error during session validation: {e}")
            self.consecutive_failures += 1
            self.session_healthy = False
            return False
    
    def _validate_session_state(self) -> bool:
        """
        Validate the current session state
        
        Returns:
            bool: True if session is valid
        """
        try:
            if not self.device_controller or not hasattr(self.device_controller, 'driver'):
                return False
            
            driver = self.device_controller.driver
            if not driver:
                return False
            
            # More thorough validation than health check
            try:
                # Check session ID
                session_id = driver.session_id
                if not session_id:
                    return False
                
                # Try a simple operation
                current_activity = driver.current_activity
                
                return True
                
            except Exception as e:
                error_msg = str(e).lower()
                if any(err in error_msg for err in [
                    "nosuchdriver", "no such session", "session is either terminated",
                    "session not found", "invalid session id", "session has been terminated"
                ]):
                    return False
                else:
                    # Some operations might fail but session could still be valid
                    return True
                    
        except Exception as e:
            self.logger.debug(f"Session validation exception: {e}")
            return False
    
    def needs_session_recovery(self) -> bool:
        """
        Determine if session recovery is needed
        
        Returns:
            bool: True if recovery is needed
        """
        return (self.consecutive_failures >= performance_config.session_refresh_threshold or
                not self.session_healthy)
    
    def attempt_session_recovery(self) -> bool:
        """
        Attempt to recover the session
        
        Returns:
            bool: True if recovery was successful
        """
        start_time = time.time()
        
        try:
            with self._session_lock:
                self.logger.info("Attempting optimized session recovery...")
                self.session_recoveries += 1
                
                # Use device controller's recovery mechanism
                if hasattr(self.device_controller, '_simple_session_recovery'):
                    success = self.device_controller._simple_session_recovery()
                elif hasattr(self.device_controller, 'reconnect_device'):
                    success = self.device_controller.reconnect_device()
                else:
                    self.logger.warning("No recovery method available on device controller")
                    success = False
                
                if success:
                    self.session_healthy = True
                    self.consecutive_failures = 0
                    self.logger.info("Session recovery successful")
                else:
                    self.logger.error("Session recovery failed")
                
                # Log performance
                duration = time.time() - start_time
                self.logger.info(f"Session recovery took {duration:.2f}s, success: {success}")
                
                return success
                
        except Exception as e:
            self.logger.error(f"Error during session recovery: {e}")
            return False
    
    def execute_with_session_management(self, operation: Callable, *args, **kwargs) -> Any:
        """
        Execute an operation with automatic session management
        
        Args:
            operation: Function to execute
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Any: Result of the operation
        """
        # Suspend health checks during critical operations
        with self.suspend_health_checks():
            try:
                # Validate session if needed before operation
                if not self.validate_session_if_needed():
                    if self.needs_session_recovery():
                        if not self.attempt_session_recovery():
                            raise Exception("Session recovery failed before operation")
                
                # Execute the operation
                result = operation(*args, **kwargs)
                
                # Mark as successful
                self.session_healthy = True
                
                return result
                
            except Exception as e:
                self.logger.error(f"Operation failed: {e}")
                self.consecutive_failures += 1
                self.session_healthy = False
                raise
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for session management
        
        Returns:
            dict: Performance statistics
        """
        health_check_success_rate = 0
        if self.health_check_count > 0:
            health_check_success_rate = ((self.health_check_count - self.health_check_failures) / self.health_check_count) * 100
        
        return {
            'session_healthy': self.session_healthy,
            'consecutive_failures': self.consecutive_failures,
            'health_check_count': self.health_check_count,
            'health_check_failures': self.health_check_failures,
            'health_check_success_rate_percent': round(health_check_success_rate, 2),
            'session_recoveries': self.session_recoveries,
            'validation_skips': self.validation_skips,
            'last_validation_time': self.last_validation_time,
            'last_health_check_time': self.last_health_check_time
        }
    
    def cleanup(self):
        """
        Cleanup resources and log final statistics
        """
        stats = self.get_performance_stats()
        self.logger.info(f"OptimizedSessionManager cleanup - Stats: {stats}")