"""
Step Rerun Executor for Mobile Automation Framework

This module provides functionality to rerun specific failed steps from previous
test executions while maintaining test context and preserving performance optimizations.
"""

import logging
import json
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


class StepRerunExecutor:
    """
    Executor for rerunning failed steps while preserving test context
    
    Features:
    - Rerun specific failed steps without full test case execution
    - Context preservation and restoration
    - Integration with existing action factory and device controller
    - Performance optimization preservation
    - Detailed rerun reporting
    """
    
    def __init__(self, device_controller=None, action_factory=None):
        """
        Initialize the Step Rerun Executor
        
        Args:
            device_controller: Device controller for action execution
            action_factory: Action factory for creating action instances
        """
        self.logger = logging.getLogger(__name__)
        self.device_controller = device_controller
        self.action_factory = action_factory
        self.step_retry_manager = None
        
        # Initialize step retry manager
        try:
            from .step_retry_manager import StepRetryManager
            self.step_retry_manager = StepRetryManager()
        except ImportError:
            self.logger.error("Step retry manager not available for rerun functionality")
        
        self.rerun_statistics = {
            'total_reruns': 0,
            'successful_reruns': 0,
            'failed_reruns': 0,
            'rerun_by_action_type': {}
        }
    
    def get_failed_steps_for_rerun(self, test_case_name: str = None) -> List[Dict[str, Any]]:
        """
        Get list of failed steps available for rerun
        
        Args:
            test_case_name: Optional filter by test case name
            
        Returns:
            List of failed step metadata dictionaries
        """
        if not self.step_retry_manager:
            return []
        
        failed_steps = self.step_retry_manager.get_failed_steps(test_case_name)
        
        # Convert to dictionaries for easier handling
        return [
            {
                'step_index': step.step_index,
                'action_type': step.action_type,
                'parameters': step.parameters,
                'failure_reason': step.failure_reason,
                'failure_type': step.failure_type.value,
                'timestamp': step.timestamp,
                'retry_count': step.retry_count,
                'max_retries': step.max_retries,
                'test_case_name': step.test_case_name,
                'suite_id': step.suite_id,
                'execution_id': step.execution_id,
                'context_data': step.context_data
            }
            for step in failed_steps
        ]
    
    def rerun_failed_step(self, step_metadata: Dict[str, Any], preserve_context: bool = True) -> Dict[str, Any]:
        """
        Rerun a specific failed step
        
        Args:
            step_metadata: Metadata of the failed step to rerun
            preserve_context: Whether to restore test context before rerun
            
        Returns:
            Dict containing rerun result and statistics
        """
        if not self.device_controller or not self.action_factory:
            return {
                'status': 'error',
                'message': 'Device controller or action factory not available',
                'rerun_successful': False
            }
        
        action_type = step_metadata.get('action_type')
        parameters = step_metadata.get('parameters', {})
        step_index = step_metadata.get('step_index')
        test_case_name = step_metadata.get('test_case_name')
        context_data = step_metadata.get('context_data', {})
        
        self.logger.info(f"Rerunning failed step: {action_type} (index: {step_index}) from test case: {test_case_name}")
        
        try:
            # Restore test context if requested
            if preserve_context and context_data:
                self._restore_test_context(context_data)
            
            # Get action handler from factory
            action_handler = self.action_factory.get_action_handler(action_type)
            if not action_handler:
                return {
                    'status': 'error',
                    'message': f'No action handler found for type: {action_type}',
                    'rerun_successful': False
                }
            
            # Set the device controller
            action_handler.set_controller(self.device_controller)
            
            # Execute the action with step retry enabled
            start_time = time.time()
            
            # Create execution context for the rerun
            execution_context = {
                'suite_id': step_metadata.get('suite_id'),
                'execution_id': f"rerun_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'rerun': True,
                'original_step_index': step_index,
                'original_failure': step_metadata.get('failure_reason')
            }
            
            # Execute with step retry mechanism
            if hasattr(action_handler, 'execute_with_step_retry'):
                result = action_handler.execute_with_step_retry(
                    parameters, step_index, test_case_name, execution_context
                )
            else:
                # Fall back to regular execution
                result = action_handler.execute(parameters)
            
            execution_time = time.time() - start_time
            
            # Determine if rerun was successful
            rerun_successful = isinstance(result, dict) and result.get('status') == 'success'
            
            # Update statistics
            self._update_rerun_statistics(action_type, rerun_successful)
            
            # Prepare rerun result
            rerun_result = {
                'status': result.get('status', 'unknown') if isinstance(result, dict) else 'error',
                'message': result.get('message', str(result)) if isinstance(result, dict) else str(result),
                'rerun_successful': rerun_successful,
                'execution_time': execution_time,
                'original_failure': step_metadata.get('failure_reason'),
                'step_index': step_index,
                'action_type': action_type,
                'test_case_name': test_case_name,
                'retry_info': result.get('retry_info', {}) if isinstance(result, dict) else {}
            }
            
            if rerun_successful:
                self.logger.info(f"Step rerun successful: {action_type} (index: {step_index})")
            else:
                self.logger.warning(f"Step rerun failed: {action_type} (index: {step_index})")
            
            return rerun_result
            
        except Exception as e:
            self.logger.error(f"Exception during step rerun: {e}")
            self._update_rerun_statistics(action_type, False)
            
            return {
                'status': 'error',
                'message': f'Exception during rerun: {str(e)}',
                'rerun_successful': False,
                'step_index': step_index,
                'action_type': action_type,
                'test_case_name': test_case_name
            }
    
    def rerun_multiple_failed_steps(self, step_metadata_list: List[Dict[str, Any]], 
                                   preserve_context: bool = True, 
                                   stop_on_failure: bool = False) -> Dict[str, Any]:
        """
        Rerun multiple failed steps
        
        Args:
            step_metadata_list: List of failed step metadata to rerun
            preserve_context: Whether to restore test context before each rerun
            stop_on_failure: Whether to stop on first failure
            
        Returns:
            Dict containing overall rerun results and statistics
        """
        results = []
        successful_reruns = 0
        failed_reruns = 0
        
        self.logger.info(f"Starting rerun of {len(step_metadata_list)} failed steps")
        
        for i, step_metadata in enumerate(step_metadata_list):
            self.logger.info(f"Rerunning step {i+1}/{len(step_metadata_list)}")
            
            result = self.rerun_failed_step(step_metadata, preserve_context)
            results.append(result)
            
            if result.get('rerun_successful', False):
                successful_reruns += 1
            else:
                failed_reruns += 1
                if stop_on_failure:
                    self.logger.warning(f"Stopping rerun due to failure at step {i+1}")
                    break
        
        overall_result = {
            'status': 'success' if failed_reruns == 0 else 'partial' if successful_reruns > 0 else 'failed',
            'message': f'Rerun completed: {successful_reruns} successful, {failed_reruns} failed',
            'total_steps': len(step_metadata_list),
            'successful_reruns': successful_reruns,
            'failed_reruns': failed_reruns,
            'results': results,
            'statistics': self.get_rerun_statistics()
        }
        
        self.logger.info(f"Rerun completed: {successful_reruns}/{len(step_metadata_list)} successful")
        return overall_result
    
    def _restore_test_context(self, context_data: Dict[str, Any]):
        """
        Restore test context for step rerun
        
        Args:
            context_data: Context data to restore
        """
        try:
            # Set context in step retry manager
            if self.step_retry_manager:
                self.step_retry_manager.set_test_context(context_data)
            
            # Additional context restoration logic can be added here
            # For example: app state, navigation state, etc.
            
            self.logger.debug("Test context restored for step rerun")
            
        except Exception as e:
            self.logger.warning(f"Failed to restore test context: {e}")
    
    def _update_rerun_statistics(self, action_type: str, success: bool):
        """Update rerun statistics"""
        self.rerun_statistics['total_reruns'] += 1
        
        if success:
            self.rerun_statistics['successful_reruns'] += 1
        else:
            self.rerun_statistics['failed_reruns'] += 1
        
        # Track by action type
        if action_type not in self.rerun_statistics['rerun_by_action_type']:
            self.rerun_statistics['rerun_by_action_type'][action_type] = {
                'total': 0, 'successful': 0, 'failed': 0
            }
        
        self.rerun_statistics['rerun_by_action_type'][action_type]['total'] += 1
        if success:
            self.rerun_statistics['rerun_by_action_type'][action_type]['successful'] += 1
        else:
            self.rerun_statistics['rerun_by_action_type'][action_type]['failed'] += 1
    
    def get_rerun_statistics(self) -> Dict[str, Any]:
        """Get current rerun statistics"""
        return self.rerun_statistics.copy()
    
    def clear_rerun_statistics(self):
        """Clear rerun statistics"""
        self.rerun_statistics = {
            'total_reruns': 0,
            'successful_reruns': 0,
            'failed_reruns': 0,
            'rerun_by_action_type': {}
        }
    
    def clear_failed_steps(self, test_case_name: str = None):
        """Clear failed steps metadata"""
        if self.step_retry_manager:
            self.step_retry_manager.clear_failed_steps(test_case_name)
            self.logger.info(f"Cleared failed steps for test case: {test_case_name or 'all'}")
