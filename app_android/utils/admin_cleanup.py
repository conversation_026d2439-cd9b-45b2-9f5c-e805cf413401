#!/usr/bin/env python3
"""
Admin cleanup utilities for Android backend (app_android/)
- Clears test data while preserving system configuration
- Robust error handling with transactions and rollback
"""
import os
import sqlite3
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

from .database import get_db_path

PRESERVE_TABLES = {
    'settings': ['settings'],
    'environments': ['environments'],
    'globals': ['global_values']
}

CLEAR_TABLES = {
    'test_suites': ['test_suites', 'test_cases', 'test_steps'],
    'execution_tracker': ['execution_tracking', 'screenshots']
}

PLATFORM_SETTINGS_CATEGORY = 'directory_path'


def reset_user_data() -> Dict[str, Any]:
    results = {"success": True, "details": []}
    try:
        _clear_tables('test_suites', CLEAR_TABLES['test_suites'], results)
        _clear_tables('execution_tracker', CLEAR_TABLES['execution_tracker'], results)
        _clear_platform_paths('settings', results)
        results["message"] = "All user data cleared (Android)."
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        results["success"] = False
        results["error"] = str(e)
    return results


def _clear_tables(db_type: str, tables: list, results: Dict[str, Any]):
    db_path = get_db_path(db_type)
    if not os.path.exists(db_path):
        results["details"].append({"db": db_type, "status": "skipped", "reason": "db not found"})
        return
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        conn.execute('PRAGMA foreign_keys = ON')
        cur = conn.cursor()
        conn.execute('BEGIN')
        for table in tables:
            try:
                cur.execute(f'DELETE FROM {table}')
                results["details"].append({"db": db_type, "table": table, "status": "cleared"})
            except sqlite3.OperationalError as oe:
                results["details"].append({"db": db_type, "table": table, "status": "missing", "error": str(oe)})
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error clearing {db_type}: {e}")
        raise
    finally:
        if conn:
            conn.close()


def _clear_platform_paths(db_type: str, results: Dict[str, Any]):
    db_path = get_db_path('settings')
    if not os.path.exists(db_path):
        results["details"].append({"db": db_type, "status": "skipped", "reason": "settings db not found"})
        return
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cur = conn.cursor()
        conn.execute('BEGIN')
        try:
            cur.execute("DELETE FROM settings WHERE platform = 'android' AND category = ?", (PLATFORM_SETTINGS_CATEGORY,))
            results["details"].append({"db": db_type, "table": "settings", "status": "directory_paths_cleared"})
        except sqlite3.OperationalError as oe:
            results["details"].append({"db": db_type, "table": "settings", "status": "missing", "error": str(oe)})
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error clearing platform paths in settings: {e}")
        raise
    finally:
        if conn:
            conn.close()

