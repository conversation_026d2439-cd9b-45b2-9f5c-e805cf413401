"""
Test Case Manager for Android Mobile Automation
Handles loading, saving, and managing test case files
"""

import os
import json
import logging
import uuid
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class TestCaseManager:
    """Manages test case files and operations"""
    
    def __init__(self, test_cases_dir: str):
        """
        Initialize TestCaseManager
        
        Args:
            test_cases_dir: Directory path where test case files are stored
        """
        self.test_cases_dir = Path(test_cases_dir)
        self.logger = logging.getLogger(__name__)
        
        # Ensure test cases directory exists
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"TestCaseManager initialized with directory: {self.test_cases_dir}")
        
        # Initialize database connection
        self.db_path = None
        self._init_database_connection()
    
    def _init_database_connection(self):
        """Initialize database connection for hybrid operations"""
        try:
            from app_android.utils.database import get_db_path
            self.db_path = get_db_path('test_suites')
            self.logger.info(f"Database connection initialized: {self.db_path}")

            # Verify the database file exists
            if not os.path.exists(self.db_path):
                self.logger.warning(f"Database file not found: {self.db_path}")
                self.db_path = None
        except Exception as e:
            self.logger.warning(f"Database connection failed: {e}")
            self.db_path = None
    
    def get_test_cases(self) -> List[Dict[str, Any]]:
        """Get list of all test cases with metadata from centralized database only."""
        test_cases: List[Dict[str, Any]] = []
        try:
            if not self.db_path:
                self.logger.warning("Centralized DB path not available for test cases")
                return []
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT test_case_id, name, description, file_path, step_count,
                       created_at, updated_at, status, platform
                FROM test_cases
                WHERE status = 'active' AND platform = 'android'
                ORDER BY name
                """
            )
            for row in cursor.fetchall():
                # Create filename from test_case_id if file_path is not available
                filename = os.path.basename(row['file_path']) if row['file_path'] else f"{row['test_case_id']}.json"

                test_cases.append({
                    'test_case_id': row['test_case_id'],
                    'filename': filename,
                    'name': row['name'],
                    'description': row['description'] or '',
                    'created_at': row['created_at'] or '',
                    'modified_at': row['updated_at'] or '',
                    'actions_count': row['step_count'] or 0,
                    'step_count': row['step_count'] or 0,  # Include both for compatibility
                    'device_id': '',
                    'tags': [],
                    'source': 'database',
                    'platform': row['platform'] or 'android'
                })
            conn.close()
            self.logger.info(f"Loaded {len(test_cases)} Android test cases from centralized DB")
            return test_cases
        except Exception as e:
            self.logger.error(f"Error getting Android test cases from centralized DB: {e}")
            return []

    def get_test_cases_hybrid(self) -> List[Dict[str, Any]]:
        """
        Get list of all test cases with metadata (hybrid: database + files)
        
        Returns:
            List of test case metadata dictionaries
        """
        test_cases = []
        db_file_paths = set()
        
        # First, try to load from database
        if self.db_path:
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT test_case_id, name, description, file_path, step_count, 
                           created_at, updated_at, status, platform
                    FROM test_cases 
                    WHERE status = 'active'
                    ORDER BY name
                """)
                
                db_results = cursor.fetchall()
                conn.close()
                
                for row in db_results:
                    test_case_id, name, description, file_path, step_count, created_at, updated_at, status, platform = row
                    
                    metadata = {
                        'test_case_id': test_case_id,
                        'filename': os.path.basename(file_path) if file_path else f"{test_case_id}.json",
                        'name': name,
                        'description': description or '',
                        'created_at': created_at or '',
                        'modified_at': updated_at or '',
                        'actions_count': step_count or 0,
                        'device_id': '',
                        'tags': [],
                        'source': 'database'
                    }
                    
                    test_cases.append(metadata)
                    if file_path:
                        db_file_paths.add(file_path)
                        
                self.logger.info(f"Loaded {len(test_cases)} test cases from database")
                
            except Exception as e:
                self.logger.warning(f"Error loading test cases from database: {e}")
        
        # Then, load from files that are not in database
        try:
            json_files = list(self.test_cases_dir.glob("*.json"))
            
            for json_file in json_files:
                file_path_str = str(json_file)
                if file_path_str in db_file_paths:
                    continue  # Skip files already loaded from database
                    
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        test_case_data = json.load(f)
                    
                    # Extract metadata
                    metadata = {
                        'filename': json_file.name,
                        'name': test_case_data.get('name', json_file.stem),
                        'description': test_case_data.get('description', ''),
                        'created_at': test_case_data.get('created_at', ''),
                        'modified_at': test_case_data.get('modified_at', ''),
                        'actions_count': len(test_case_data.get('actions', [])),
                        'device_id': test_case_data.get('device_id', ''),
                        'tags': test_case_data.get('tags', []),
                        'source': 'file'
                    }
                    
                    test_cases.append(metadata)
                    
                except Exception as e:
                    self.logger.warning(f"Error reading test case file {json_file}: {e}")
                    continue
            
            # Sort by name
            test_cases.sort(key=lambda x: x['name'].lower())
            
            self.logger.info(f"Found {len(test_cases)} total test cases (database + files)")
            return test_cases
            
        except Exception as e:
            self.logger.error(f"Error getting test cases: {e}")
            return test_cases  # Return what we have from database
    
    def load_test_case(self, identifier: str) -> Optional[Dict[str, Any]]:
        """
        Load a specific test case by test_case_id or filename from centralized DB only.
        """
        result = self._load_from_database(identifier)
        if result:
            self.logger.info(f"Successfully loaded test case from database: {identifier}")
        else:
            self.logger.warning(f"Test case not found in database: {identifier}")
        return result

    def load_test_case_hybrid(self, identifier: str) -> Optional[Dict[str, Any]]:
        """
        Load test case using hybrid approach (database first, then file)
        
        Args:
            identifier: test_case_id or filename
            
        Returns:
            Test case data dictionary or None if not found
        """
        # Try loading from database first
        if self.db_path:
            result = self._load_from_database(identifier)
            if result:
                return result
        
        # Fallback to file loading
        return self._load_from_file(identifier)
    
    def _load_from_database(self, identifier: str) -> Optional[Dict[str, Any]]:
        """
        Load test case from database by test_case_id or file_path with fuzzy matching

        Args:
            identifier: test_case_id or filename

        Returns:
            Test case data dictionary or None if not found
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Try by test_case_id first
            cursor.execute(
                "SELECT json_payload FROM test_cases WHERE test_case_id = ? AND status = 'active'",
                (identifier,)
            )
            result = cursor.fetchone()

            # If not found, try by exact file_path match
            if not result:
                filename = identifier if identifier.endswith('.json') else f"{identifier}.json"
                file_path = str(self.test_cases_dir / filename)
                cursor.execute(
                    "SELECT json_payload FROM test_cases WHERE file_path = ? AND status = 'active'",
                    (file_path,)
                )
                result = cursor.fetchone()

            # If still not found, try by filename only (basename match with exact filename)
            if not result:
                filename = identifier if identifier.endswith('.json') else f"{identifier}.json"
                cursor.execute(
                    "SELECT json_payload FROM test_cases WHERE file_path LIKE ? AND status = 'active'",
                    (f'%/{filename}',)
                )
                result = cursor.fetchone()
                if result:
                    self.logger.info(f"Found test case by exact filename match: {filename}")

            # If still not found, try fuzzy match by replacing hyphens with underscores
            if not result:
                filename = identifier if identifier.endswith('.json') else f"{identifier}.json"
                fuzzy_filename = filename.replace('-', '_')
                cursor.execute(
                    "SELECT json_payload FROM test_cases WHERE file_path LIKE ? AND status = 'active'",
                    (f'%/{fuzzy_filename}',)
                )
                result = cursor.fetchone()
                if result:
                    self.logger.info(f"Found test case by fuzzy filename match: {filename} -> {fuzzy_filename}")

            # If still not found, try the reverse: underscores to hyphens
            if not result:
                filename = identifier if identifier.endswith('.json') else f"{identifier}.json"
                fuzzy_filename = filename.replace('_', '-')
                cursor.execute(
                    "SELECT json_payload FROM test_cases WHERE file_path LIKE ? AND status = 'active'",
                    (f'%/{fuzzy_filename}',)
                )
                result = cursor.fetchone()
                if result:
                    self.logger.info(f"Found test case by reverse fuzzy filename match: {filename} -> {fuzzy_filename}")

            # If still not found, try matching by base name without timestamp
            # Remove timestamp pattern like _20250712145003 from the filename
            if not result:
                filename = identifier if identifier.endswith('.json') else f"{identifier}.json"
                # Remove timestamp pattern: _YYYYMMDDHHMMSS
                import re
                base_filename = re.sub(r'_\d{14}', '', filename)
                if base_filename != filename:
                    cursor.execute(
                        "SELECT json_payload FROM test_cases WHERE file_path LIKE ? AND status = 'active'",
                        (f'%/{base_filename}',)
                    )
                    result = cursor.fetchone()
                    if result:
                        self.logger.info(f"Found test case by base filename match (removed timestamp): {filename} -> {base_filename}")

            # If still not found, try partial name matching (most flexible)
            # Extract the core name without timestamp and try to find similar files
            if not result:
                filename = identifier if identifier.endswith('.json') else f"{identifier}.json"
                # Remove .json extension and timestamp
                import re
                core_name = filename.replace('.json', '')
                core_name = re.sub(r'_\d{14}', '', core_name)
                # Try to find any file that contains this core name
                cursor.execute(
                    "SELECT json_payload FROM test_cases WHERE file_path LIKE ? AND status = 'active' LIMIT 1",
                    (f'%{core_name}%',)
                )
                result = cursor.fetchone()
                if result:
                    self.logger.info(f"Found test case by partial name match: {filename} -> {core_name}")

            conn.close()

            if result:
                test_case_data = json.loads(result[0])
                self.logger.info(f"Loaded test case from database: {identifier}")
                return test_case_data

        except Exception as e:
            self.logger.warning(f"Error loading test case from database: {e}")

        return None
    
    def _load_from_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        Load test case from file system
        
        Args:
            filename: Name of the test case file
            
        Returns:
            Test case data dictionary or None if not found
        """
        try:
            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = self.test_cases_dir / filename
            
            if not file_path.exists():
                self.logger.warning(f"Test case file not found: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                test_case_data = json.load(f)
            
            self.logger.info(f"Loaded test case from file: {filename}")
            return test_case_data
            
        except Exception as e:
            self.logger.error(f"Error loading test case {filename}: {e}")
            return None
    
    def sync_to_database(self, test_case_data: Dict[str, Any], file_path: str) -> bool:
        """
        Sync test case data to database
        
        Args:
            test_case_data: Test case data dictionary
            file_path: Full path to the test case file
            
        Returns:
            True if successful, False otherwise
        """
        if not self.db_path:
            return False
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Generate test_case_id if not present
            test_case_id = test_case_data.get('test_case_id')
            if not test_case_id:
                test_case_id = str(uuid.uuid4())[:8].upper()
                test_case_data['test_case_id'] = test_case_id
            
            # Prepare data for database
            name = test_case_data.get('name', os.path.basename(file_path).replace('.json', ''))
            description = test_case_data.get('description', '')
            step_count = len(test_case_data.get('actions', []))
            json_payload = json.dumps(test_case_data)
            platform = 'android'  # Default platform
            
            # Normalize file path to avoid duplicates due to path variations
            normalized_path = os.path.abspath(file_path)

            # First, try to find by exact normalized file_path
            cursor.execute("""
                SELECT test_case_id, created_at FROM test_cases
                WHERE file_path = ? AND status = 'active'
                LIMIT 1
            """, (normalized_path,))
            existing = cursor.fetchone()

            # If not found, try matching by filename or name as a fallback (prevents duplicates)
            if not existing:
                filename_only = os.path.basename(normalized_path)
                base_name = os.path.splitext(filename_only)[0]
                cursor.execute("""
                    SELECT test_case_id, created_at FROM test_cases
                    WHERE (file_path LIKE ? OR name = ?) AND status = 'active'
                    LIMIT 1
                """, (f"%/{filename_only}", base_name))
                existing = cursor.fetchone()

            if existing:
                # Update existing test case, keep original test_case_id and created_at
                existing_test_case_id, _existing_created_at = existing
                cursor.execute("""
                    UPDATE test_cases
                    SET name = ?, description = ?, step_count = ?, json_payload = ?,
                        file_path = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE test_case_id = ? AND status = 'active'
                """, (
                    name, description, step_count, json_payload, normalized_path, existing_test_case_id
                ))
                self.logger.info(f"Updated existing test case: {existing_test_case_id} for {normalized_path}")
            else:
                # Insert new test case
                cursor.execute("""
                    INSERT INTO test_cases
                    (test_case_id, suite_id, platform, name, description, file_path,
                     step_count, json_payload, test_idx, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (
                    test_case_id, 'standalone', platform, name, description, normalized_path,
                    step_count, json_payload, 0, 'active'
                ))
                self.logger.info(f"Inserted new test case: {test_case_id} for {normalized_path}")

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            self.logger.error(f"Error syncing test case to database: {e}")
            return False
    
    def save_test_case(self, test_case_data: Dict[str, Any], filename: str = None, is_save_as: bool = False) -> Optional[str]:
        """
        Save test case data to file
        
        Args:
            test_case_data: Test case data dictionary
            filename: Optional filename (if None, will generate from test case name)
            is_save_as: Whether this is a "Save As" operation
            
        Returns:
            Saved filename or None if failed
        """
        try:
            # Generate filename if not provided
            if not filename:
                test_case_name = test_case_data.get('name', f'test_case_{uuid.uuid4().hex[:8]}')
                # Sanitize filename
                safe_name = "".join(c for c in test_case_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_name = safe_name.replace(' ', '_')
                filename = f"{safe_name}.json"
            
            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Update timestamps
            current_time = datetime.now().isoformat()
            if 'created_at' not in test_case_data:
                test_case_data['created_at'] = current_time
            test_case_data['modified_at'] = current_time
            
            # Do NOT write to filesystem; use centralized DB only
            file_path = self.test_cases_dir / filename  # logical path reference only

            # Sync to database
            self.sync_to_database(test_case_data, str(file_path))

            self.logger.info(f"Saved Android test case to centralized DB: {test_case_data.get('test_case_id')}")
            return filename

        except Exception as e:
            self.logger.error(f"Error saving test case: {e}")
            return None
    
    def delete_test_case(self, filename: str) -> bool:
        """
        Delete a test case file
        
        Args:
            filename: Name of the test case file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = self.test_cases_dir / filename
            
            if not file_path.exists():
                self.logger.warning(f"Test case file not found for deletion: {file_path}")
                return False
            
            file_path.unlink()
            self.logger.info(f"Deleted test case: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting test case {filename}: {e}")
            return False
    
    def rename_test_case(self, filename: str, new_name: str) -> Optional[str]:
        """
        Rename a test case file
        
        Args:
            filename: Current filename
            new_name: New name for the test case
            
        Returns:
            New filename or None if failed
        """
        try:
            # Load existing test case
            test_case_data = self.load_test_case(filename)
            if not test_case_data:
                return None
            
            # Update name in data
            test_case_data['name'] = new_name
            
            # Generate new filename
            safe_name = "".join(c for c in new_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_')
            new_filename = f"{safe_name}.json"
            
            # Save with new filename
            saved_filename = self.save_test_case(test_case_data, new_filename)
            
            if saved_filename:
                # Delete old file
                self.delete_test_case(filename)
                self.logger.info(f"Renamed test case from {filename} to {saved_filename}")
                return saved_filename
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error renaming test case {filename}: {e}")
            return None
    
    def duplicate_test_case(self, filename: str) -> Optional[str]:
        """
        Duplicate a test case file
        
        Args:
            filename: Name of the test case file to duplicate
            
        Returns:
            New filename or None if failed
        """
        try:
            # Load existing test case
            test_case_data = self.load_test_case(filename)
            if not test_case_data:
                return None
            
            # Update name and remove timestamps
            original_name = test_case_data.get('name', filename.replace('.json', ''))
            test_case_data['name'] = f"{original_name}_copy"
            test_case_data.pop('created_at', None)
            test_case_data.pop('modified_at', None)
            
            # Save as new file
            saved_filename = self.save_test_case(test_case_data, is_save_as=True)
            
            if saved_filename:
                self.logger.info(f"Duplicated test case {filename} as {saved_filename}")
                return saved_filename
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error duplicating test case {filename}: {e}")
            return None
    
    def clean_duplicates(self) -> int:
        """
        Clean up duplicate test case files
        
        Returns:
            Number of duplicates removed
        """
        try:
            test_cases = self.get_test_cases()
            names_seen = set()
            duplicates_removed = 0
            
            for test_case in test_cases:
                name = test_case['name']
                if name in names_seen:
                    # This is a duplicate
                    if self.delete_test_case(test_case['filename']):
                        duplicates_removed += 1
                        self.logger.info(f"Removed duplicate test case: {test_case['filename']}")
                else:
                    names_seen.add(name)
            
            self.logger.info(f"Cleaned up {duplicates_removed} duplicate test cases")
            return duplicates_removed
            
        except Exception as e:
            self.logger.error(f"Error cleaning duplicates: {e}")
            return 0

    def get_all_action_types(self) -> List[str]:
        """
        Get all unique action types from existing test cases

        Returns:
            List of unique action types
        """
        action_types = set()

        try:
            # Get all JSON files in the test cases directory
            json_files = list(self.test_cases_dir.glob("*.json"))

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        test_case_data = json.load(f)

                    # Extract action types from actions
                    actions = test_case_data.get('actions', [])
                    for action in actions:
                        action_type = action.get('type', action.get('action_type'))
                        if action_type:
                            action_types.add(action_type)

                except Exception as e:
                    self.logger.warning(f"Error reading test case file {json_file}: {e}")
                    continue

            # Convert to sorted list
            result = sorted(list(action_types))
            self.logger.info(f"Found {len(result)} unique action types")
            return result

        except Exception as e:
            self.logger.error(f"Error getting action types: {e}")
            return []

    def get_all_locator_types(self) -> List[str]:
        """
        Get all unique locator types from existing test cases

        Returns:
            List of unique locator types
        """
        locator_types = set()

        try:
            # Get all JSON files in the test cases directory
            json_files = list(self.test_cases_dir.glob("*.json"))

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        test_case_data = json.load(f)

                    # Extract locator types from actions
                    actions = test_case_data.get('actions', [])
                    for action in actions:
                        locator_type = action.get('locator_type')
                        if locator_type:
                            locator_types.add(locator_type)

                except Exception as e:
                    self.logger.warning(f"Error reading test case file {json_file}: {e}")
                    continue

            # Convert to sorted list
            result = sorted(list(locator_types))
            self.logger.info(f"Found {len(result)} unique locator types")
            return result

        except Exception as e:
            self.logger.error(f"Error getting locator types: {e}")
            return []
