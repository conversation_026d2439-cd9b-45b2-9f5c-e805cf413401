"""
Android Timeout Performance Monitor

This utility monitors and measures the performance improvements from timeout optimizations
in the Android automation framework.
"""

import time
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class TimeoutMetrics:
    """Data class to store timeout performance metrics"""
    action_type: str
    locator_type: str
    locator_value: str
    timeout_requested: float
    timeout_actual: float
    element_found: bool
    retry_count: int
    context_switches: int
    start_time: datetime
    end_time: datetime
    
    @property
    def duration(self) -> float:
        """Calculate the total duration in seconds"""
        return (self.end_time - self.start_time).total_seconds()
    
    @property
    def efficiency_ratio(self) -> float:
        """Calculate efficiency ratio (actual time / requested timeout)"""
        if self.timeout_requested > 0:
            return self.duration / self.timeout_requested
        return 0.0


class TimeoutPerformanceMonitor:
    """Monitor and analyze timeout performance in Android automation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics: List[TimeoutMetrics] = []
        self.current_action_start: Optional[datetime] = None
        self.current_action_data: Dict = {}
        
    def start_action_monitoring(self, action_type: str, locator_type: str, 
                              locator_value: str, timeout: float):
        """Start monitoring an action's timeout performance"""
        self.current_action_start = datetime.now()
        self.current_action_data = {
            'action_type': action_type,
            'locator_type': locator_type,
            'locator_value': locator_value,
            'timeout_requested': timeout,
            'retry_count': 0,
            'context_switches': 0
        }
        self.logger.debug(f"Started monitoring: {action_type} with {timeout}s timeout")
    
    def record_retry_attempt(self):
        """Record a retry attempt"""
        if self.current_action_data:
            self.current_action_data['retry_count'] += 1
            self.logger.debug(f"Retry attempt #{self.current_action_data['retry_count']}")
    
    def record_context_switch(self):
        """Record a context switch"""
        if self.current_action_data:
            self.current_action_data['context_switches'] += 1
            self.logger.debug(f"Context switch #{self.current_action_data['context_switches']}")
    
    def end_action_monitoring(self, element_found: bool, actual_timeout: float = None):
        """End monitoring and record the metrics"""
        if not self.current_action_start or not self.current_action_data:
            return
        
        end_time = datetime.now()
        duration = (end_time - self.current_action_start).total_seconds()
        
        metrics = TimeoutMetrics(
            action_type=self.current_action_data['action_type'],
            locator_type=self.current_action_data['locator_type'],
            locator_value=self.current_action_data['locator_value'],
            timeout_requested=self.current_action_data['timeout_requested'],
            timeout_actual=actual_timeout or duration,
            element_found=element_found,
            retry_count=self.current_action_data['retry_count'],
            context_switches=self.current_action_data['context_switches'],
            start_time=self.current_action_start,
            end_time=end_time
        )
        
        self.metrics.append(metrics)
        self.logger.info(f"Action completed: {metrics.action_type} - "
                        f"Duration: {duration:.2f}s, "
                        f"Efficiency: {metrics.efficiency_ratio:.2f}, "
                        f"Found: {element_found}, "
                        f"Retries: {metrics.retry_count}")
        
        # Reset current action data
        self.current_action_start = None
        self.current_action_data = {}
    
    def get_performance_summary(self) -> Dict:
        """Get a summary of timeout performance metrics"""
        if not self.metrics:
            return {"message": "No metrics collected yet"}
        
        total_actions = len(self.metrics)
        successful_actions = sum(1 for m in self.metrics if m.element_found)
        total_duration = sum(m.duration for m in self.metrics)
        total_retries = sum(m.retry_count for m in self.metrics)
        total_context_switches = sum(m.context_switches for m in self.metrics)
        
        avg_efficiency = sum(m.efficiency_ratio for m in self.metrics) / total_actions
        avg_duration = total_duration / total_actions
        
        # Performance by action type
        action_types = {}
        for metric in self.metrics:
            action_type = metric.action_type
            if action_type not in action_types:
                action_types[action_type] = {
                    'count': 0,
                    'success_rate': 0,
                    'avg_duration': 0,
                    'avg_efficiency': 0,
                    'total_retries': 0
                }
            
            action_types[action_type]['count'] += 1
            action_types[action_type]['total_retries'] += metric.retry_count
            
        # Calculate averages for each action type
        for action_type, data in action_types.items():
            type_metrics = [m for m in self.metrics if m.action_type == action_type]
            data['success_rate'] = sum(1 for m in type_metrics if m.element_found) / len(type_metrics)
            data['avg_duration'] = sum(m.duration for m in type_metrics) / len(type_metrics)
            data['avg_efficiency'] = sum(m.efficiency_ratio for m in type_metrics) / len(type_metrics)
        
        return {
            'total_actions': total_actions,
            'success_rate': successful_actions / total_actions,
            'avg_duration': avg_duration,
            'avg_efficiency_ratio': avg_efficiency,
            'total_retries': total_retries,
            'total_context_switches': total_context_switches,
            'avg_retries_per_action': total_retries / total_actions,
            'performance_by_action_type': action_types,
            'optimization_indicators': {
                'fast_actions': sum(1 for m in self.metrics if m.duration < 3),
                'slow_actions': sum(1 for m in self.metrics if m.duration > 10),
                'efficient_actions': sum(1 for m in self.metrics if m.efficiency_ratio < 0.5),
                'inefficient_actions': sum(1 for m in self.metrics if m.efficiency_ratio > 0.8)
            }
        }
    
    def log_performance_report(self):
        """Log a detailed performance report"""
        summary = self.get_performance_summary()
        
        if "message" in summary:
            self.logger.info(summary["message"])
            return
        
        self.logger.info("=== TIMEOUT PERFORMANCE REPORT ===")
        self.logger.info(f"Total Actions: {summary['total_actions']}")
        self.logger.info(f"Success Rate: {summary['success_rate']:.2%}")
        self.logger.info(f"Average Duration: {summary['avg_duration']:.2f}s")
        self.logger.info(f"Average Efficiency Ratio: {summary['avg_efficiency_ratio']:.2f}")
        self.logger.info(f"Total Retries: {summary['total_retries']}")
        self.logger.info(f"Average Retries per Action: {summary['avg_retries_per_action']:.2f}")
        
        opt_indicators = summary['optimization_indicators']
        self.logger.info(f"Fast Actions (<3s): {opt_indicators['fast_actions']}")
        self.logger.info(f"Slow Actions (>10s): {opt_indicators['slow_actions']}")
        self.logger.info(f"Efficient Actions (<50% timeout used): {opt_indicators['efficient_actions']}")
        self.logger.info(f"Inefficient Actions (>80% timeout used): {opt_indicators['inefficient_actions']}")
        
        self.logger.info("=== PERFORMANCE BY ACTION TYPE ===")
        for action_type, data in summary['performance_by_action_type'].items():
            self.logger.info(f"{action_type}: "
                           f"Count={data['count']}, "
                           f"Success={data['success_rate']:.2%}, "
                           f"AvgDuration={data['avg_duration']:.2f}s, "
                           f"Efficiency={data['avg_efficiency']:.2f}")


# Global instance for easy access
timeout_monitor = TimeoutPerformanceMonitor()
