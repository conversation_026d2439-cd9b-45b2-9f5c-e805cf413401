#!/usr/bin/env python3
"""
Simple AI Wrapper for Android App Player

This module provides a simple way to add AI assistance to the existing
Player class without major refactoring.
"""

import os
import sys
import logging
from typing import Dict, Any, Optional, Callable

# Add parent directory to path to import simple_ai_helper
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from simple_ai_helper import SimpleAIHelper
except ImportError:
    # Fallback if simple_ai_helper is not available
    class SimpleAIHelper:
        def __init__(self, *args, **kwargs):
            self.enabled = False
        
        def suggest_fix(self, *args, **kwargs):
            return {"suggestion": "AI helper not available", "confidence": 0.0, "alternative_actions": []}
        
        def quick_element_suggestion(self, *args, **kwargs):
            return ["Check element locators", "Verify app state"]


class AIEnhancedPlayerWrapper:
    """
    Simple wrapper that adds AI assistance to any existing Player instance.
    
    This wrapper can be used to enhance the existing Player class without
    modifying its code directly.
    """
    
    def __init__(self, player_instance, enable_ai: bool = True):
        """
        Initialize the AI wrapper around an existing Player instance.
        
        Args:
            player_instance: Existing Player instance to wrap
            enable_ai: Whether to enable AI assistance (default: True)
        """
        self.player = player_instance
        self.logger = logging.getLogger(__name__)
        
        # Initialize AI helper
        if enable_ai:
            try:
                self.ai_helper = SimpleAIHelper()
                self.ai_enabled = self.ai_helper.enabled
            except Exception as e:
                self.logger.warning(f"Could not initialize AI helper: {e}")
                self.ai_helper = SimpleAIHelper()  # Fallback
                self.ai_enabled = False
        else:
            self.ai_helper = SimpleAIHelper()
            self.ai_enabled = False
        
        # Track failures for learning
        self.failure_history = []
    
    def __getattr__(self, name):
        """
        Delegate all attribute access to the wrapped player instance.
        This allows the wrapper to act as a drop-in replacement.
        """
        return getattr(self.player, name)
    
    def execute_action_with_ai(self, action_method: str, *args, **kwargs) -> Dict[str, Any]:
        """
        Execute any player action with AI assistance on failure.
        
        Args:
            action_method: Name of the method to call on the player
            *args, **kwargs: Arguments to pass to the method
            
        Returns:
            Result dictionary with success status and AI suggestions if failed
        """
        try:
            # Get the method from the player
            method = getattr(self.player, action_method)
            
            # Execute the original action
            result = method(*args, **kwargs)
            
            # Log success
            self.logger.info(f"✅ Action succeeded: {action_method}")
            
            return {
                "success": True,
                "result": result,
                "action": action_method,
                "ai_suggestions": None
            }
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"❌ Action failed: {action_method} - {error_msg}")
            
            # Get AI suggestions if enabled
            ai_suggestions = None
            if self.ai_enabled:
                try:
                    action_description = f"Execute {action_method} with args: {args[:2]}"  # Limit args for privacy
                    ai_suggestions = self.ai_helper.suggest_fix(error_msg, action_description)
                    
                    self.logger.info(f"🤖 AI suggestion: {ai_suggestions['suggestion']}")
                    
                except Exception as ai_error:
                    self.logger.warning(f"AI suggestion failed: {ai_error}")
            
            # Store failure for learning
            self.failure_history.append({
                "action": action_method,
                "error": error_msg,
                "args": str(args)[:100],  # Truncate for storage
                "ai_suggestions": ai_suggestions
            })
            
            return {
                "success": False,
                "error": error_msg,
                "action": action_method,
                "ai_suggestions": ai_suggestions,
                "original_exception": e
            }
    
    def click_with_ai(self, *args, **kwargs):
        """
        Enhanced click action with AI assistance.
        """
        return self.execute_action_with_ai('click_element', *args, **kwargs)
    
    def type_with_ai(self, *args, **kwargs):
        """
        Enhanced type action with AI assistance.
        """
        return self.execute_action_with_ai('input_text', *args, **kwargs)
    
    def tap_with_ai(self, *args, **kwargs):
        """
        Enhanced tap action with AI assistance.
        """
        return self.execute_action_with_ai('tap', *args, **kwargs)
    
    def get_failure_summary(self) -> Dict[str, Any]:
        """
        Get a summary of failures and AI suggestions.
        
        Returns:
            Dictionary with failure statistics and common suggestions
        """
        if not self.failure_history:
            return {"total_failures": 0, "common_suggestions": []}
        
        # Analyze failure patterns
        common_errors = {}
        all_suggestions = []
        
        for failure in self.failure_history:
            error = failure["error"]
            if error in common_errors:
                common_errors[error] += 1
            else:
                common_errors[error] = 1
            
            if failure["ai_suggestions"] and failure["ai_suggestions"].get("alternative_actions"):
                all_suggestions.extend(failure["ai_suggestions"]["alternative_actions"])
        
        # Get most common suggestions
        suggestion_counts = {}
        for suggestion in all_suggestions:
            if suggestion in suggestion_counts:
                suggestion_counts[suggestion] += 1
            else:
                suggestion_counts[suggestion] = 1
        
        common_suggestions = sorted(suggestion_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_failures": len(self.failure_history),
            "common_errors": dict(sorted(common_errors.items(), key=lambda x: x[1], reverse=True)[:3]),
            "common_suggestions": [suggestion for suggestion, count in common_suggestions],
            "ai_enabled": self.ai_enabled
        }
    
    def clear_failure_history(self):
        """
        Clear the failure history.
        """
        self.failure_history = []
        self.logger.info("Failure history cleared")


def enhance_player_with_ai(player_instance, enable_ai: bool = True) -> AIEnhancedPlayerWrapper:
    """
    Simple function to enhance any existing Player instance with AI capabilities.
    
    Args:
        player_instance: Existing Player instance
        enable_ai: Whether to enable AI assistance
        
    Returns:
        AI-enhanced wrapper around the player
    """
    return AIEnhancedPlayerWrapper(player_instance, enable_ai)


# Example usage
def demo_usage():
    """
    Demonstrate how to use the AI wrapper with existing Player.
    """
    print("=== AI Enhanced Player Demo ===")
    
    # Simulate existing player (in real usage, this would be your actual Player instance)
    class MockPlayer:
        def click_element(self, locator):
            raise Exception(f"Element not found: {locator}")
        
        def input_text(self, locator, text):
            raise Exception(f"Cannot type into element: {locator}")
    
    # Create mock player
    original_player = MockPlayer()
    
    # Enhance with AI (one line!)
    ai_player = enhance_player_with_ai(original_player)
    
    # Use enhanced player
    print("\n1. Testing click with AI assistance...")
    result = ai_player.click_with_ai("//button[@text='Login']")
    
    if not result["success"]:
        print(f"   Failed: {result['error']}")
        if result["ai_suggestions"]:
            print(f"   AI suggests: {result['ai_suggestions']['suggestion']}")
    
    print("\n2. Testing type with AI assistance...")
    result = ai_player.type_with_ai("//input[@id='username']", "testuser")
    
    if not result["success"]:
        print(f"   Failed: {result['error']}")
        if result["ai_suggestions"]:
            print(f"   AI suggests: {result['ai_suggestions']['suggestion']}")
    
    # Get failure summary
    print("\n3. Failure summary:")
    summary = ai_player.get_failure_summary()
    print(f"   Total failures: {summary['total_failures']}")
    print(f"   AI enabled: {summary['ai_enabled']}")
    if summary['common_suggestions']:
        print("   Common AI suggestions:")
        for suggestion in summary['common_suggestions']:
            print(f"   - {suggestion}")


if __name__ == "__main__":
    demo_usage()