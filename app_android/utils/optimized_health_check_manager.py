"""
Optimized Health Check Manager for Mobile App Automation

This module provides intelligent health check management to reduce the excessive
health check overhead identified in the performance analysis (56 suspend/resume cycles).
"""

import time
import threading
import logging
from typing import Dict, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class HealthCheckState(Enum):
    """Health check states"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    DISABLED = "disabled"


@dataclass
class HealthCheckMetrics:
    """Health check performance metrics"""
    total_checks: int = 0
    successful_checks: int = 0
    failed_checks: int = 0
    suspensions: int = 0
    total_suspension_time: float = 0.0
    average_check_duration: float = 0.0


class OptimizedHealthCheckManager:
    """
    Intelligent health check manager that reduces overhead while maintaining reliability
    """
    
    def __init__(self, device_controller):
        """
        Initialize the optimized health check manager
        
        Args:
            device_controller: The device controller instance
        """
        self.device_controller = device_controller
        self.logger = logger
        
        # Load performance configuration
        try:
            from ..config.performance_config import performance_config
            self.config = performance_config
        except ImportError:
            self.config = None
            self.logger.warning("Performance config not available, using defaults")
        
        # Health check state
        self.state = HealthCheckState.ACTIVE
        self.last_check_time = 0
        self.last_successful_check = 0
        self.suspension_start_time = None
        self.active_operations: Set[str] = set()
        
        # Performance metrics
        self.metrics = HealthCheckMetrics()
        
        # Threading
        self._lock = threading.Lock()
        self._health_check_thread = None
        self._stop_event = threading.Event()
        
        # Configuration
        self.check_interval = 30.0  # Default 30 seconds (optimized from analysis)
        self.check_timeout = 3.0    # Quick timeout for faster failures
        self.max_suspension_time = 60.0  # Maximum suspension time
        self.batch_check_enabled = True
        
        if self.config:
            self.check_interval = self.config.health_check_interval
            self.check_timeout = self.config.health_check_timeout
    
    def start_monitoring(self):
        """Start the health check monitoring thread"""
        if self._health_check_thread and self._health_check_thread.is_alive():
            return
        
        self._stop_event.clear()
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop,
            name="OptimizedHealthCheck",
            daemon=True
        )
        self._health_check_thread.start()
        self.logger.info("Optimized health check monitoring started")
    
    def stop_monitoring(self):
        """Stop the health check monitoring thread"""
        self._stop_event.set()
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5.0)
        self.logger.info("Optimized health check monitoring stopped")
    
    def suspend_for_operation(self, operation_type: str, estimated_duration: float = None):
        """
        Intelligently suspend health checks for an operation
        
        Args:
            operation_type: Type of operation being performed
            estimated_duration: Estimated duration of the operation
        """
        with self._lock:
            # Check if suspension is needed using smart logic
            if self.config and hasattr(self.config, 'should_suspend_health_checks'):
                should_suspend = self.config.should_suspend_health_checks(operation_type)
            else:
                # Fallback logic
                should_suspend = operation_type in [
                    'session_creation', 'app_launch', 'driver_initialization',
                    'screenshot_capture', 'element_search'
                ]
            
            if not should_suspend:
                self.logger.debug(f"Health check suspension not needed for: {operation_type}")
                return
            
            self.active_operations.add(operation_type)
            
            if self.state == HealthCheckState.ACTIVE:
                self.state = HealthCheckState.SUSPENDED
                self.suspension_start_time = time.time()
                self.metrics.suspensions += 1
                
                self.logger.debug(f"Health checks suspended for operation: {operation_type}")
    
    def resume_after_operation(self, operation_type: str):
        """
        Resume health checks after an operation completes
        
        Args:
            operation_type: Type of operation that completed
        """
        with self._lock:
            self.active_operations.discard(operation_type)
            
            # Only resume if no other operations are active
            if not self.active_operations and self.state == HealthCheckState.SUSPENDED:
                self._resume_health_checks()
    
    def _resume_health_checks(self):
        """Internal method to resume health checks"""
        if self.suspension_start_time:
            suspension_duration = time.time() - self.suspension_start_time
            self.metrics.total_suspension_time += suspension_duration
            
            # Prevent excessive suspension times
            if suspension_duration > self.max_suspension_time:
                self.logger.warning(f"Health check suspension exceeded maximum time: {suspension_duration:.2f}s")
        
        self.state = HealthCheckState.ACTIVE
        self.suspension_start_time = None
        self.logger.debug("Health checks resumed")
    
    def force_resume(self):
        """Force resume health checks (emergency use)"""
        with self._lock:
            self.active_operations.clear()
            self._resume_health_checks()
            self.logger.warning("Health checks force resumed")
    
    def perform_health_check(self) -> bool:
        """
        Perform an optimized health check
        
        Returns:
            bool: True if healthy, False otherwise
        """
        if self.state != HealthCheckState.ACTIVE:
            return True  # Assume healthy when suspended
        
        start_time = time.time()
        
        try:
            self.metrics.total_checks += 1
            
            # Quick health check - just verify driver is responsive
            if hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                # Use a very lightweight check
                try:
                    # Just check if we can get the current activity (Android) or page source size (iOS)
                    if hasattr(self.device_controller.driver, 'current_activity'):
                        _ = self.device_controller.driver.current_activity
                    else:
                        # For iOS or when current_activity is not available
                        source = self.device_controller.driver.page_source
                        if len(source) < 100:  # Suspiciously small page source
                            return False
                    
                    self.metrics.successful_checks += 1
                    self.last_successful_check = time.time()
                    return True
                    
                except Exception as e:
                    self.logger.debug(f"Health check failed: {e}")
                    self.metrics.failed_checks += 1
                    return False
            else:
                self.metrics.failed_checks += 1
                return False
                
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            self.metrics.failed_checks += 1
            return False
        finally:
            # Update metrics
            duration = time.time() - start_time
            if self.metrics.total_checks > 0:
                self.metrics.average_check_duration = (
                    (self.metrics.average_check_duration * (self.metrics.total_checks - 1) + duration) /
                    self.metrics.total_checks
                )
            self.last_check_time = time.time()
    
    def _health_check_loop(self):
        """Main health check loop with optimized timing"""
        while not self._stop_event.is_set():
            try:
                # Only perform check if active and interval has passed
                current_time = time.time()
                if (self.state == HealthCheckState.ACTIVE and 
                    current_time - self.last_check_time >= self.check_interval):
                    
                    is_healthy = self.perform_health_check()
                    
                    if not is_healthy:
                        self.logger.warning("Device health check failed")
                        # Could trigger recovery actions here
                
                # Sleep for a short interval to avoid busy waiting
                self._stop_event.wait(min(5.0, self.check_interval / 6))
                
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                self._stop_event.wait(5.0)
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get health check performance metrics
        
        Returns:
            dict: Performance metrics
        """
        with self._lock:
            success_rate = 0.0
            if self.metrics.total_checks > 0:
                success_rate = self.metrics.successful_checks / self.metrics.total_checks
            
            return {
                'state': self.state.value,
                'total_checks': self.metrics.total_checks,
                'successful_checks': self.metrics.successful_checks,
                'failed_checks': self.metrics.failed_checks,
                'success_rate': success_rate,
                'suspensions': self.metrics.suspensions,
                'total_suspension_time': self.metrics.total_suspension_time,
                'average_check_duration': self.metrics.average_check_duration,
                'last_check_time': self.last_check_time,
                'last_successful_check': self.last_successful_check,
                'active_operations': list(self.active_operations),
                'check_interval': self.check_interval
            }
    
    def is_healthy(self) -> bool:
        """
        Check if the system is currently healthy
        
        Returns:
            bool: True if healthy or recently checked successfully
        """
        if self.state == HealthCheckState.SUSPENDED:
            return True  # Assume healthy during suspension
        
        # Consider healthy if last successful check was recent
        time_since_success = time.time() - self.last_successful_check
        return time_since_success < (self.check_interval * 2)


# Context manager for automatic operation suspension/resume
class HealthCheckSuspension:
    """Context manager for automatic health check suspension during operations"""
    
    def __init__(self, health_manager: OptimizedHealthCheckManager, operation_type: str):
        self.health_manager = health_manager
        self.operation_type = operation_type
    
    def __enter__(self):
        self.health_manager.suspend_for_operation(self.operation_type)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.health_manager.resume_after_operation(self.operation_type)
