import os
import sqlite3
import logging
import json
import re
import glob
from datetime import datetime

logger = logging.getLogger(__name__)

# Create a dedicated logger for execution tracking
execution_logger = logging.getLogger('execution_tracking_android')
execution_handler = logging.FileHandler('execution_tracking_android.log')
execution_formatter = logging.Formatter('%(asctime)s - EXECUTION_TRACKING_ANDROID - %(message)s')
execution_handler.setFormatter(execution_formatter)
execution_logger.addHandler(execution_handler)
execution_logger.setLevel(logging.INFO)

# Cross-database helpers: detect whether execution/suite exists in android.db or ios.db

def _get_platform_db_paths():
    """Return absolute paths for both android and ios centralized DBs."""
    from pathlib import Path
    base_dir = Path(__file__).resolve().parents[2]
    return {
        'android': str(base_dir / 'db-data' / 'android.db'),
        'ios': str(base_dir / 'db-data' / 'ios.db'),
    }


def _detect_db_for_execution(execution_id: str) -> str:
    """Detect which platform DB contains rows for a given execution identifier."""
    dbs = _get_platform_db_paths()
    for name, db_path in dbs.items():
        try:
            if not os.path.exists(db_path):
                continue
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            # Exact match on test_execution_id
            cur.execute("SELECT 1 FROM execution_tracking WHERE test_execution_id = ? LIMIT 1", (execution_id,))
            if cur.fetchone():
                conn.close()
                logger.debug(f"Execution {execution_id} found in {name}.db by exact match")
                return db_path
            # LIKE match for short exec_ ids
            if execution_id and execution_id.startswith('exec_'):
                short = execution_id.replace('exec_', '')
                cur.execute("SELECT 1 FROM execution_tracking WHERE test_execution_id LIKE ? LIMIT 1", (f"%{short}%",))
                if cur.fetchone():
                    conn.close()
                    logger.debug(f"Execution {execution_id} found in {name}.db by LIKE match")
                    return db_path
            # Direct suite_id equality (sometimes execution_id == suite_id)
            cur.execute("SELECT 1 FROM execution_tracking WHERE suite_id = ? LIMIT 1", (execution_id,))
            if cur.fetchone():
                conn.close()
                logger.debug(f"Execution {execution_id} found in {name}.db by suite_id match")
                return db_path
            conn.close()
        except Exception as e:
            logger.debug(f"DB detect failed on {name}.db: {e}")
            try:
                conn.close()
            except Exception:
                pass
            continue
    # Default to android if nothing found (preserve previous behavior)
    return _get_platform_db_paths()['android']


def _detect_db_for_suite(suite_id: str) -> str:
    """Detect which DB contains rows for a given suite_id."""
    dbs = _get_platform_db_paths()
    for name, db_path in dbs.items():
        try:
            if not os.path.exists(db_path):
                continue
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            cur.execute("SELECT 1 FROM execution_tracking WHERE suite_id = ? LIMIT 1", (suite_id,))
            if cur.fetchone():
                conn.close()
                logger.debug(f"Suite {suite_id} found in {name}.db")
                return db_path
            conn.close()
        except Exception as e:
            logger.debug(f"Suite detect failed on {name}.db: {e}")
            try:
                conn.close()
            except Exception:
                pass
            continue
    return _get_platform_db_paths()['android']

def run_database_migrations(db_path: str = None) -> bool:
    """
    Run all database migrations for database-only execution tracking.

    Args:
        db_path: Optional database path. If None, uses default execution_tracker DB.

    Returns:
        success: Boolean indicating if migrations succeeded
    """
    try:
        if db_path is None:
            db_path = get_db_path('execution_tracker')

        logger.info(f"Running database migrations for: {db_path}")

        from utils.database_migrations import DatabaseMigrations
        migrations = DatabaseMigrations(db_path)

        success = migrations.run_all_migrations()

        if success:
            logger.info("✅ Database migrations completed successfully")
        else:
            logger.error("❌ Database migrations failed")

        return success

    except Exception as e:
        logger.error(f"Error running database migrations: {e}")
        return False


def verify_database_schema(db_path: str = None) -> bool:
    """
    Verify that all required schema changes have been applied.

    Args:
        db_path: Optional database path. If None, uses default execution_tracker DB.

    Returns:
        success: Boolean indicating if schema is up to date
    """
    try:
        if db_path is None:
            db_path = get_db_path('execution_tracker')

        from utils.database_migrations import DatabaseMigrations
        migrations = DatabaseMigrations(db_path)

        success, missing_items = migrations.verify_schema_migration()

        if success:
            logger.info("✅ Database schema is up to date")
        else:
            logger.warning(f"⚠️ Database schema is missing items: {', '.join(missing_items)}")

        return success

    except Exception as e:
        logger.error(f"Error verifying database schema: {e}")
        return False


def log_execution_tracking_change(test_execution_id, test_suite_id, test_case_id, action_id, old_status, new_status, retry_count, timestamp, operation_type="UPDATE"):
    """
    Log execution tracking changes for debugging and audit purposes

    Args:
        test_execution_id (str): Test execution ID
        test_suite_id (str): Test suite ID
        test_case_id (str): Test case ID
        action_id (str): Action ID
        old_status (str): Previous status
        new_status (str): New status
        retry_count (int): Current retry count
        timestamp (str): Timestamp of change
        operation_type (str): Type of operation (UPDATE, INSERT)
    """
    log_message = f"OPERATION={operation_type} | test_execution_id={test_execution_id} | test_suite_id={test_suite_id} | test_case_id={test_case_id} | action_id={action_id} | old_status={old_status} | new_status={new_status} | retry_count={retry_count} | timestamp={timestamp}"

    # Log to both the main logger and dedicated execution logger
    logger.info(log_message)
    execution_logger.info(log_message)

def sync_execution_data_to_json(test_execution_id, reports_dir):
    """
    Synchronize database execution data to data.json file

    Args:
        test_execution_id (str): Test execution ID to sync
        reports_dir (str): Reports directory path

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(_detect_db_for_execution(test_execution_id))
        cursor = conn.cursor()

        # Get all execution data for this test execution ID
        cursor.execute('''
        SELECT suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
               status, retry_count, last_error, start_time, end_time, execution_result,
               test_case_id, test_execution_id
        FROM execution_tracking
        WHERE test_execution_id = ?
        ORDER BY test_idx, step_idx
        ''', (test_execution_id,))

        execution_data = cursor.fetchall()
        conn.close()

        if not execution_data:
            logger.warning(f"No execution data found for test_execution_id: {test_execution_id}")
            return False

        # Group data by test case
        test_cases = {}
        suite_id = None

        for row in execution_data:
            (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
             status, retry_count, last_error, start_time, end_time, execution_result,
             test_case_id, exec_id) = row

            if test_case_id not in test_cases:
                test_cases[test_case_id] = {
                    'name': filename.replace('.json', ''),
                    'test_case_id': test_case_id,
                    'filename': filename,
                    'status': 'unknown',
                    'steps': []
                }

            if step_idx is not None:
                # This is a step/action
                step_data = {
                    'action_id': action_id,
                    'action_type': action_type,
                    'status': status,
                    'retry_count': retry_count,
                    'description': action_params,
                    'timestamp': end_time,
                    'test_execution_id': exec_id
                }
                test_cases[test_case_id]['steps'].append(step_data)

        # Determine final status for each test case
        for test_case_id, test_case in test_cases.items():
            steps = test_case['steps']
            if not steps:
                test_case['status'] = 'unknown'
            else:
                # Group steps by action_id to get final status for each action
                action_statuses = {}
                for step in steps:
                    action_id = step['action_id']
                    if action_id not in action_statuses:
                        action_statuses[action_id] = []
                    action_statuses[action_id].append(step)

                # Get final status for each action (last retry)
                final_action_statuses = []
                for action_id, action_steps in action_statuses.items():
                    # Sort by retry_count to get the latest attempt
                    action_steps.sort(key=lambda x: x['retry_count'])
                    final_status = action_steps[-1]['status']
                    final_action_statuses.append(final_status)

                # Test case passes only if all actions pass
                if all(status == 'passed' for status in final_action_statuses):
                    test_case['status'] = 'passed'
                else:
                    test_case['status'] = 'failed'

        # Create the data.json structure
        json_data = {
            'name': f'Test Suite {suite_id}',
            'test_execution_id': test_execution_id,
            'test_suite_id': suite_id,
            'timestamp': datetime.now().isoformat(),
            'status': 'passed' if all(tc['status'] == 'passed' for tc in test_cases.values()) else 'failed',
            'testCases': list(test_cases.values())
        }

        # Find the execution directory
        execution_dirs = [d for d in os.listdir(reports_dir) if test_execution_id in d]
        if execution_dirs:
            execution_dir = os.path.join(reports_dir, execution_dirs[0])
            data_json_path = os.path.join(execution_dir, 'data.json')

            # Write the synchronized data
            with open(data_json_path, 'w') as f:
                json.dump(json_data, f, indent=2)

            logger.info(f"Successfully synchronized execution data to {data_json_path}")
            return True
        else:
            logger.warning(f"No execution directory found for test_execution_id: {test_execution_id}")
            return False

    except Exception as e:
        logger.error(f"Error synchronizing execution data to JSON: {str(e)}")
        return False

# Database file path - centralized: always use db-data/android.db
def get_db_path(db_type='test_suites'):
    """
    Centralized database path for Android (ignores db_type).
    """
    from pathlib import Path
    override_root = os.environ.get('AUTOMATION_DB_ROOT')
    if override_root:
        base_dir = Path(override_root)
    else:
        base_dir = Path(__file__).resolve().parents[2]
    return str(base_dir / 'db-data' / 'android.db')

# Use the function to get the path - default to test_suites database
DB_PATH = get_db_path('test_suites')

def ensure_db_directory(db_type='test_suites'):
    """Ensure the directory for the database exists"""
    # Check if we're in migration mode to prevent unauthorized database creation
    if os.environ.get('ANDROID_MIGRATION_MODE') == 'true':
        logger.info("Android migration mode - skipping directory creation")
        return

    db_path = get_db_path(db_type)
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"Created directory for database: {db_dir}")

def init_db():
    """Initialize the database with required tables (Deprecated under centralized DB)."""
    logger.info("Centralized DB mode (Android): skipping legacy init_db")
    return

    # Legacy logic below is intentionally unreachable
    # ensure_db_directory()

    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create test_suites table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_suites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        name TEXT,
        status TEXT,
        passed INTEGER,
        failed INTEGER,
        skipped INTEGER,
        timestamp TEXT,
        report_dir TEXT,
        error TEXT
    )
    ''')

    # Create test_cases table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_cases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        name TEXT,
        status TEXT,
        duration TEXT,
        timestamp TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 0,
        error TEXT
    )
    ''')

    # Create test_steps table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_steps (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        name TEXT,
        action_type TEXT,
        action_id TEXT,
        status TEXT,
        duration TEXT,
        timestamp TEXT,
        screenshot_path TEXT,
        error TEXT,
        enabled INTEGER DEFAULT 1
    )
    ''')

    # Create test_case_json_backups table for JSON editor functionality
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_case_json_backups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        test_case_filename TEXT NOT NULL,
        test_case_id TEXT,
        json_data BLOB NOT NULL,
        backup_timestamp TEXT NOT NULL,
        session_id TEXT,
        created_by TEXT DEFAULT 'json_editor'
    )
    ''')

    # Create screenshots table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS screenshots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        path TEXT,
        timestamp TEXT,
        action_id TEXT,
        custom_screenshot_name TEXT,
        custom_screenshot_filename TEXT,
        custom_screenshot_path TEXT
    )
    ''')

    # Create environment_variables table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS environment_variables (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create execution_tracking table for detailed execution status
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_tracking (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        action_type TEXT,
        action_params TEXT,
        action_id TEXT,
        status TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 0,
        last_error TEXT,
        start_time TEXT,
        end_time TEXT,
        in_progress BOOLEAN DEFAULT 0,
        execution_result TEXT,
        test_case_id TEXT,
        test_execution_id TEXT
    )
    ''')

    # Add execution_result column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN execution_result TEXT')
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Add test_case_id column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN test_case_id TEXT')
        logger.info("Added test_case_id column to execution_tracking table")
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Add test_execution_id column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN test_execution_id TEXT')
        logger.info("Added test_execution_id column to execution_tracking table")
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Add test_execution_id column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN test_execution_id TEXT')
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Add test_case_id column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN test_case_id TEXT')
    except sqlite3.OperationalError:
        # Column already exists
        pass

    # Create indexes for performance if they don't exist
    try:
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_execution_id ON execution_tracking(test_execution_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_test_case_id ON execution_tracking(test_case_id)')
    except sqlite3.OperationalError:
        # Indexes already exist
        pass

    # Create execution_reports table with standardized schema
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_reports (
        report_id TEXT PRIMARY KEY,
        test_execution_id TEXT,
        suite_id TEXT,
        test_case_id TEXT,
        platform TEXT,
        status TEXT,
        start_time TIMESTAMP,
        end_time TIMESTAMP,
        duration INTEGER,
        error_message TEXT,
        report_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Add indexes for performance
    try:
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_suite_id ON execution_reports(suite_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_case_id ON execution_reports(test_case_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_status ON execution_reports(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_execution_id ON execution_reports(test_execution_id)')
    except sqlite3.OperationalError:
        # Indexes already exist
        pass

    # Create execution_settings table for test suite execution preferences
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Insert default execution settings if they don't exist
    cursor.execute('''
    INSERT OR IGNORE INTO execution_settings (setting_name, setting_value, description)
    VALUES
        ('record_execution_default', 'NO', 'Default setting for recording test suite execution'),
        ('retry_failed_tests_default', '0', 'Default number of retries for failed test cases'),
        ('capture_step_screenshots', 'true', 'Capture and store screenshots for every step in execution tracking (default true)')
    ''')

    # Create locators_repository table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS locators_repository (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        test_case_name TEXT NOT NULL,
        test_case_id TEXT,
        action_id TEXT NOT NULL,
        locator_type TEXT NOT NULL,
        locator_value TEXT NOT NULL,
        platform TEXT NOT NULL,
        created_date TEXT DEFAULT CURRENT_TIMESTAMP,
        last_used_date TEXT DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(test_case_name, action_id, locator_type, locator_value)
    )
    ''')

    conn.commit()
    conn.close()

    # Update schemas for new features
    update_test_steps_schema()
    update_screenshots_schema()
    update_execution_tracking_schema()
    update_execution_reports_schema()

    logger.info("Database initialized successfully")



def update_screenshots_schema():
    """
    Update or create the screenshots table schema to include custom screenshot fields.

    - Creates the screenshots table with the full modern schema if it does not exist
    - Ensures custom_screenshot_* columns exist on older schemas

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING SCREENSHOTS TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Ensure screenshots table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='screenshots'")
        if not cursor.fetchone():
            logger.info("Screenshots table not found. Creating with full schema.")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS screenshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                filename TEXT,
                path TEXT,
                timestamp TEXT,
                action_id TEXT,
                custom_screenshot_name TEXT,
                custom_screenshot_filename TEXT,
                custom_screenshot_path TEXT
            )
            ''')

        # Check existing columns to add any missing ones for backward compatibility
        cursor.execute("PRAGMA table_info(screenshots)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add custom screenshot columns if they don't exist
        if 'custom_screenshot_name' not in columns:
            logger.info("Adding custom_screenshot_name column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_name TEXT")

        if 'custom_screenshot_filename' not in columns:
            logger.info("Adding custom_screenshot_filename column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_filename TEXT")

        if 'custom_screenshot_path' not in columns:
            logger.info("Adding custom_screenshot_path column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_path TEXT")

        # Add BLOB columns for database-only screenshot storage
        if 'screenshot_blob' not in columns:
            logger.info("Adding screenshot_blob column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_blob BLOB")

        if 'screenshot_thumb_blob' not in columns:
            logger.info("Adding screenshot_thumb_blob column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_thumb_blob BLOB")

        if 'screenshot_mime' not in columns:
            logger.info("Adding screenshot_mime column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")

        if 'test_execution_id' not in columns:
            logger.info("Adding test_execution_id column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN test_execution_id TEXT")

        if 'test_case_id' not in columns:
            logger.info("Adding test_case_id column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN test_case_id TEXT")

        if 'original_size' not in columns:
            logger.info("Adding original_size column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN original_size INTEGER")

        if 'compressed_size' not in columns:
            logger.info("Adding compressed_size column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN compressed_size INTEGER")

        conn.commit()
        conn.close()

        logger.info("Screenshots table schema updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating screenshots table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_test_steps_schema():
    """
    Update the test_steps table schema to include enabled column for step enable/disable functionality

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING TEST_STEPS TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if enabled column exists
        cursor.execute("PRAGMA table_info(test_steps)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add enabled column if it doesn't exist (default to 1 for enabled)
        if 'enabled' not in columns:
            logger.info("Adding enabled column to test_steps table")
            cursor.execute("ALTER TABLE test_steps ADD COLUMN enabled INTEGER DEFAULT 1")

        conn.commit()
        conn.close()

        logger.info("Test_steps table schema updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating test_steps table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_execution_tracking_schema():
    """
    Update the execution_tracking table schema to include step_idx, action_type, action_params, and action_id columns

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING EXECUTION_TRACKING TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if execution_tracking table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
        if cursor.fetchone() is None:
            logger.info("execution_tracking table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS execution_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                filename TEXT,
                action_type TEXT,
                action_params TEXT,
                action_id TEXT,
                status TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                last_error TEXT,
                start_time TEXT,
                end_time TEXT,
                in_progress BOOLEAN DEFAULT 0
            )
            ''')

            logger.info("execution_tracking table created successfully")
        else:
            # Check if step_idx column exists
            try:
                cursor.execute("SELECT step_idx FROM execution_tracking LIMIT 1")
                logger.info("step_idx column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding step_idx column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN step_idx INTEGER")
                logger.info("step_idx column added successfully")

            # Check if action_type column exists
            try:
                cursor.execute("SELECT action_type FROM execution_tracking LIMIT 1")
                logger.info("action_type column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_type column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_type TEXT")
                logger.info("action_type column added successfully")

            # Check if action_params column exists
            try:
                cursor.execute("SELECT action_params FROM execution_tracking LIMIT 1")
                logger.info("action_params column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_params column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_params TEXT")
                logger.info("action_params column added successfully")

            # Check if action_id column exists
            try:
                cursor.execute("SELECT action_id FROM execution_tracking LIMIT 1")
                logger.info("action_id column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_id column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_id TEXT")
                logger.info("action_id column added successfully")

            # Add screenshot-related columns if they don't exist
            try:
                cursor.execute("SELECT screenshot_blob FROM execution_tracking LIMIT 1")
                logger.info("screenshot_blob column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_blob column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_blob BLOB")
                logger.info("screenshot_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_thumb_blob FROM execution_tracking LIMIT 1")
                logger.info("screenshot_thumb_blob column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_thumb_blob column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_thumb_blob BLOB")
                logger.info("screenshot_thumb_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_mime FROM execution_tracking LIMIT 1")
                logger.info("screenshot_mime column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_mime column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")
                logger.info("screenshot_mime column added successfully")

            try:
                cursor.execute("SELECT screenshot_filename FROM execution_tracking LIMIT 1")
                logger.info("screenshot_filename column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_filename column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN screenshot_filename TEXT")
                logger.info("screenshot_filename column added successfully")

            try:
                cursor.execute("SELECT timestamp FROM execution_tracking LIMIT 1")
                logger.info("timestamp column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding timestamp column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
                logger.info("timestamp column added successfully")

            # Ensure screenshots table has all required BLOB columns
            try:
                cursor.execute("SELECT screenshot_blob FROM screenshots LIMIT 1")
                logger.info("screenshot_blob column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_blob column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_blob BLOB")
                logger.info("screenshot_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_thumb_blob FROM screenshots LIMIT 1")
                logger.info("screenshot_thumb_blob column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_thumb_blob column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_thumb_blob BLOB")
                logger.info("screenshot_thumb_blob column added successfully")

            try:
                cursor.execute("SELECT screenshot_mime FROM screenshots LIMIT 1")
                logger.info("screenshot_mime column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding screenshot_mime column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN screenshot_mime TEXT DEFAULT 'image/png'")
                logger.info("screenshot_mime column added successfully")

            try:
                cursor.execute("SELECT test_execution_id FROM screenshots LIMIT 1")
                logger.info("test_execution_id column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding test_execution_id column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN test_execution_id TEXT")
                logger.info("test_execution_id column added successfully")

            try:
                cursor.execute("SELECT test_case_id FROM screenshots LIMIT 1")
                logger.info("test_case_id column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding test_case_id column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN test_case_id TEXT")
                logger.info("test_case_id column added successfully")

            try:
                cursor.execute("SELECT original_size FROM screenshots LIMIT 1")
                logger.info("original_size column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding original_size column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN original_size INTEGER")
                logger.info("original_size column added successfully")

            try:
                cursor.execute("SELECT compressed_size FROM screenshots LIMIT 1")
                logger.info("compressed_size column already exists in screenshots table")
            except sqlite3.OperationalError:
                logger.info("Adding compressed_size column to screenshots table...")
                cursor.execute("ALTER TABLE screenshots ADD COLUMN compressed_size INTEGER")
                logger.info("compressed_size column added successfully")

        # Commit and close
        conn.commit()
        conn.close()

        logger.info("Successfully updated execution_tracking table schema")
        return True
    except Exception as e:
        logger.error(f"Error updating execution_tracking table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_execution_reports_schema():
    """Update execution_reports table schema to new standardized format"""
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if execution_reports table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_reports'")
        if cursor.fetchone() is None:
            logger.info("execution_reports table does not exist, will be created by init_db")
            conn.close()
            return

        # Check existing columns
        cursor.execute("PRAGMA table_info(execution_reports)")
        columns = [col[1] for col in cursor.fetchall()]

        # If we have the old schema (execution_id, data_json), migrate to new schema
        if 'execution_id' in columns and 'report_id' not in columns:
            logger.info("Migrating execution_reports table to new schema...")

            # Create new table with correct schema
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS execution_reports_new (
                report_id TEXT PRIMARY KEY,
                test_execution_id TEXT,
                suite_id TEXT,
                test_case_id TEXT,
                platform TEXT,
                status TEXT,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration INTEGER,
                error_message TEXT,
                report_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Migrate existing data
            cursor.execute('''
            INSERT INTO execution_reports_new
            (report_id, test_execution_id, suite_id, status, report_data, created_at)
            SELECT
                execution_id || '_' || datetime('now'),
                execution_id,
                suite_id,
                status,
                CASE
                    WHEN data_json IS NOT NULL THEN data_json
                    ELSE NULL
                END,
                created_at
            FROM execution_reports
            ''')

            # Drop old table and rename new one
            cursor.execute('DROP TABLE execution_reports')
            cursor.execute('ALTER TABLE execution_reports_new RENAME TO execution_reports')

            # Add indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_suite_id ON execution_reports(suite_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_case_id ON execution_reports(test_case_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_status ON execution_reports(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_execution_reports_test_execution_id ON execution_reports(test_execution_id)')

            logger.info("Successfully migrated execution_reports table to new schema")

        conn.commit()
        conn.close()
        logger.info("execution_reports schema updated successfully")

    except Exception as e:
        logger.error(f"Error updating execution_reports schema: {e}")
        import traceback
        traceback.print_exc()


def clear_test_tables():
    """
    Clear the test_suites and test_cases tables instead of dropping them

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING TEST TABLES (test_suites, test_cases) ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if tables exist before clearing
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_suites'")
        test_suites_exists = cursor.fetchone() is not None

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
        test_cases_exists = cursor.fetchone() is not None

        # Clear tables if they exist
        if test_suites_exists:
            logger.info("Clearing test_suites table...")
            cursor.execute("DELETE FROM test_suites")
            suites_cleared = cursor.rowcount
            logger.info(f"test_suites table cleared successfully, removed {suites_cleared} rows")
        else:
            logger.info("test_suites table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                name TEXT,
                status TEXT,
                passed INTEGER,
                failed INTEGER,
                skipped INTEGER,
                timestamp TEXT,
                report_dir TEXT,
                error TEXT
            )
            ''')
            logger.info("test_suites table created successfully")

        if test_cases_exists:
            logger.info("Clearing test_cases table...")
            cursor.execute("DELETE FROM test_cases")
            cases_cleared = cursor.rowcount
            logger.info(f"test_cases table cleared successfully, removed {cases_cleared} rows")
        else:
            logger.info("test_cases table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                name TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                error TEXT
            )
            ''')
            logger.info("test_cases table created successfully")

        # Commit and close
        conn.commit()
        conn.close()

        logger.info("Successfully cleared test tables")
        return True
    except Exception as e:
        logger.error(f"Error clearing test tables: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def track_test_execution(suite_id, test_idx, filename, status, retry_count=0, max_retries=0, error=None, in_progress=False, step_idx=None, action_type=None, action_params=None, action_id=None, execution_result=None, test_case_id=None, test_execution_id=None, screenshot_filename=None):
    """
    Track test execution status in the database

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        filename (str): Test case filename
        status (str): Test execution status (started, running, passed, failed, retrying)
        retry_count (int): Current retry count
        max_retries (int): Maximum number of retries
        error (str): Error message if any
        in_progress (bool): Whether the test is currently in progress
        step_idx (int, optional): Step index within the test case
        action_type (str, optional): Type of action being executed
        action_params (dict, optional): Parameters for the action
        screenshot_filename (str, optional): Screenshot filename for database-only tracking

    Returns:
        bool: Success status
    """
    # COMPREHENSIVE LOGGING - Log EVERY call to this function
    logger.info("=" * 80)
    logger.info("track_test_execution() CALLED")
    logger.info(f"  suite_id: {suite_id}")
    logger.info(f"  test_idx: {test_idx}")
    logger.info(f"  step_idx: {step_idx}")
    logger.info(f"  filename: {filename}")
    logger.info(f"  status: {status}")
    logger.info(f"  action_type: {action_type}")
    logger.info(f"  action_id: {action_id}")
    logger.info(f"  test_case_id: {test_case_id}")
    logger.info(f"  test_execution_id: {test_execution_id}")
    logger.info(f"  in_progress: {in_progress}")
    logger.info("=" * 80)
    # DB path diagnostic
    try:
        import os as _os
        logger.info(f"DATABASE-FIRST: track_test_execution DB file: {DB_PATH} (exists={_os.path.exists(DB_PATH)})")
    except Exception as _e:
        logger.error(f"DATABASE-FIRST: track_test_execution unable to log DB_PATH: {_e}")


    try:
        # Allow step records for all meaningful statuses so we can stage a row
        # when a step starts and update it once it finishes.
        allowed_statuses = {'passed', 'failed', 'running', 'started', 'skipped'}
        if status not in allowed_statuses:
            logger.info(
                "SKIPPING database insert for status '%s' - allowed statuses: %s",
                status,
                sorted(allowed_statuses),
            )
            return True  # Treat as success to avoid breaking execution flow

        # RELAXED VALIDATION: Allow inserts even without test_case_id (will use filename as fallback)
        if not suite_id or suite_id.strip() == '':
            logger.warning(f"❌ SKIPPING database insert - missing or empty suite_id: '{suite_id}'")
            return False

        # If test_case_id is missing, use filename as fallback identifier
        if not test_case_id or test_case_id.strip() == '':
            if filename and filename != 'unknown':
                logger.info(f"✅ Using filename as test_case_id fallback: '{filename}'")
                test_case_id = filename
            else:
                logger.warning(f"❌ SKIPPING database insert - missing both test_case_id and valid filename")
                return False

        logger.info(f"✅ VALIDATION PASSED - Proceeding with database insert")

        logger.info(f"========== TRACKING TEST EXECUTION ==========")
        logger.info(f"DEBUG: RECEIVED test_idx: {test_idx} (type: {type(test_idx)})")
        logger.info(f"DEBUG: suite_id: {suite_id}, test_idx: {test_idx}, step_idx: {step_idx}")
        logger.info(f"DEBUG: filename: {filename}, action_type: {action_type}")
        logger.info(f"DEBUG: status: {status}, retry: {retry_count}/{max_retries}, in_progress: {in_progress}")
        logger.info(f"DEBUG: STATUS FILTER PASSED - inserting record with status: {status}")
        logger.info(f"DEBUG: VALIDATION PASSED - suite_id: {suite_id}, test_case_id: {test_case_id}")

        # WRITE-TIME SAFEGUARDS: normalize and enforce IDs before any DB access
        # 1) Prevent accidentally using execution_id as suite_id and repair if possible
        try:
            if suite_id and isinstance(suite_id, str) and suite_id.startswith('exec_'):
                if not test_execution_id:
                    test_execution_id = suite_id
                    logger.warning(f"Recovered test_execution_id from suite_id value: {test_execution_id}")
                fixed_suite_id = None
                try:
                    if filename:
                        fixed_suite_id = find_suite_id_for_test_case(filename)
                except Exception as _e:
                    logger.warning(f"Failed to resolve suite_id from filename '{filename}': {_e}")
                if fixed_suite_id and not fixed_suite_id.startswith('exec_'):
                    logger.warning(f"Correcting suite_id (was execution_id): {suite_id} -> {fixed_suite_id}")
                    suite_id = fixed_suite_id
        except Exception as guard_err:
            logger.warning(f"Suite/Test execution ID guard encountered an error: {guard_err}")

        # 2) Align/repair test_case_id using suite mapping when available
        try:
            if suite_id and filename:
                mapping = get_test_case_id_mapping_for_suite(suite_id)
                if mapping:
                    clean_fn = os.path.basename(filename)
                    if not clean_fn.endswith('.json'):
                        clean_fn += '.json'
                    base_no_ext = os.path.splitext(clean_fn)[0]
                    stripped = re.sub(r'([_-][0-9]{8,})+$', '', base_no_ext)
                    candidates = [
                        filename,
                        clean_fn,
                        stripped + '.json',
                        clean_fn.replace('_', ' '),
                        clean_fn.replace(' ', '_')
                    ]
                    mapped_id = None
                    for key in candidates:
                        if key in mapping:
                            mapped_id = mapping[key]
                            break
                    if mapped_id and (not test_case_id or test_case_id != mapped_id):
                        logger.warning(f"Correcting test_case_id based on suite mapping: {test_case_id} -> {mapped_id} for filename '{filename}'")
                        test_case_id = mapped_id
        except Exception as map_err:
            logger.warning(f"Failed to align test_case_id from mapping: {map_err}")

        # Get stack trace to see where this function is being called from
        import traceback
        stack_trace = traceback.format_stack()
        logger.info(f"DEBUG: Called from: {stack_trace[-2]}")

        # Log error if present
        if error:
            logger.info(f"DEBUG: error: {error[:100]}..." if len(str(error)) > 100 else f"DEBUG: error: {error}")

        # Validate parameters
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        # Convert test_idx to int if it's not already
        try:
            # Make sure test_idx is an integer and not None
            if test_idx is None:
                logger.warning("DEBUG: test_idx is None, using 0 instead")
                test_idx = 0
            else:
                # Force conversion to int to ensure we're using the correct type
                test_idx = int(test_idx)
                logger.info(f"DEBUG: Converted test_idx to int: {test_idx}")
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        # Convert step_idx to int if it's not None and not already an int
        if step_idx is not None:
            try:
                step_idx = int(step_idx)
            except (ValueError, TypeError):
                logger.warning(f"DEBUG: Invalid step_idx value: {step_idx}, using None instead")
                step_idx = None

        # Convert action_params to JSON string if it's a dict
        action_params_str = None
        if action_params:
            if isinstance(action_params, dict):
                action_params_str = json.dumps(action_params)
            else:
                action_params_str = str(action_params)
            logger.info(f"DEBUG: action_params: {action_params_str[:100]}..." if len(str(action_params_str)) > 100 else f"DEBUG: action_params: {action_params_str}")

        # Convert execution_result to JSON string if it's a dict
        execution_result_str = None
        if execution_result:
            if isinstance(execution_result, dict):
                execution_result_str = json.dumps(execution_result)
            else:
                execution_result_str = str(execution_result)
            logger.info(f"DEBUG: execution_result: {execution_result_str[:100]}..." if len(str(execution_result_str)) > 100 else f"DEBUG: execution_result: {execution_result_str}")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if there's an existing entry - prioritize unique ID matching for retries
        existing = None
        existing_id = None

        # For retry scenarios, check by test_execution_id, test_case_id, and action_id first
        if test_execution_id and test_case_id and action_id:
            query = '''SELECT id, retry_count, status FROM execution_tracking
                      WHERE test_execution_id = ? AND test_case_id = ? AND action_id = ?'''
            logger.info(f"DEBUG: Checking for retry entry with unique IDs: test_execution_id={test_execution_id}, test_case_id={test_case_id}, action_id={action_id}")
            cursor.execute(query, (test_execution_id, test_case_id, action_id))
            existing = cursor.fetchone()
            if existing:
                existing_id, current_retry_count, current_status = existing
                logger.info(f"DEBUG: Found existing entry for retry - ID: {existing_id}, current retry: {current_retry_count}, current status: {current_status}")
                # Increment retry count for retries
                retry_count = current_retry_count + 1
                logger.info(f"DEBUG: Incremented retry count to: {retry_count}")

        # If no unique ID match found, fall back to traditional matching
        if not existing:
            if step_idx is not None:
                query = 'SELECT id, retry_count, status FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ?'
                logger.info(f"DEBUG: Checking for existing step entry with query: {query} and params: ({suite_id}, {test_idx}, {step_idx}, {filename})")
                cursor.execute(query, (suite_id, test_idx, step_idx, filename))
            else:
                query = 'SELECT id, retry_count, status FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND filename = ? AND step_idx IS NULL'
                logger.info(f"DEBUG: Checking for existing test case entry with query: {query} and params: ({suite_id}, {test_idx}, {filename})")
                cursor.execute(query, (suite_id, test_idx, filename))
            existing = cursor.fetchone()
            if existing:
                existing_id, current_retry_count, current_status = existing
                logger.info(f"DEBUG: Found existing entry via traditional matching - ID: {existing_id}")

        logger.info(f"DEBUG: Existing entry found: {existing is not None}")

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if existing:
            # Log the status change for comprehensive tracking
            old_status = current_status if 'current_status' in locals() else 'unknown'
            log_execution_tracking_change(
                test_execution_id=test_execution_id,
                test_suite_id=suite_id,
                test_case_id=test_case_id,
                action_id=action_id,
                old_status=old_status,
                new_status=status,
                retry_count=retry_count,
                timestamp=timestamp,
                operation_type="UPDATE"
            )

            # Update existing entry
            cursor.execute('''
            UPDATE execution_tracking
            SET status = ?, retry_count = ?, max_retries = ?, last_error = ?,
                end_time = ?, in_progress = ?, action_type = ?, action_params = ?, action_id = ?, execution_result = ?,
                test_case_id = ?, test_execution_id = ?, screenshot_filename = ?
            WHERE id = ?
            ''', (
                status, retry_count, max_retries, error or '',
                timestamp, 1 if in_progress else 0, action_type, action_params_str, action_id, execution_result_str,
                test_case_id, test_execution_id, screenshot_filename, existing_id
            ))
            logger.info(f"Updated execution tracking for test {filename} (idx: {test_idx}, step: {step_idx}): status={status}, retry={retry_count}/{max_retries}")
        else:
            # Log the new entry creation
            log_execution_tracking_change(
                test_execution_id=test_execution_id,
                test_suite_id=suite_id,
                test_case_id=test_case_id,
                action_id=action_id,
                old_status="none",
                new_status=status,
                retry_count=retry_count,
                timestamp=timestamp,
                operation_type="INSERT"
            )

            # Insert new entry
            logger.info(f"🔵 INSERTING NEW RECORD INTO execution_tracking table")
            logger.info(f"   Values: suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}")
            logger.info(f"   filename={filename}, action_type={action_type}, action_id={action_id}")
            logger.info(f"   status={status}, test_case_id={test_case_id}, test_execution_id={test_execution_id}")

            cursor.execute('''
            INSERT INTO execution_tracking
            (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id, status, retry_count, max_retries, last_error, start_time, end_time, in_progress, execution_result, test_case_id, test_execution_id, screenshot_filename)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id, test_idx, step_idx, filename, action_type, action_params_str, action_id, status, retry_count, max_retries, error or '',
                timestamp, timestamp, 1 if in_progress else 0, execution_result_str, test_case_id, test_execution_id, screenshot_filename
            ))

            # Get the inserted row ID
            inserted_id = cursor.lastrowid
            logger.info(f"✅ SUCCESS: Created execution tracking for test {filename} (idx: {test_idx}, step: {step_idx}): status={status}, retry={retry_count}/{max_retries}")
            logger.info(f"   Inserted row ID: {inserted_id}")

            # Verify the insert by querying the total count
            cursor.execute("SELECT COUNT(*) FROM execution_tracking WHERE suite_id = ?", (suite_id,))
            total_count = cursor.fetchone()[0]
            logger.info(f"📊 Total execution_tracking entries for suite {suite_id}: {total_count}")

        # Save step information to test_steps table if step_idx is provided
        if step_idx is not None and action_type is not None:
            try:
                # Extract step name from action_params if available
                step_name = f"Step {step_idx}: {action_type}"
                if action_params and isinstance(action_params, dict):
                    if 'description' in action_params:
                        step_name = action_params['description']
                    elif 'name' in action_params:
                        step_name = action_params['name']

                # Use provided action_id or extract from action_params if available
                if action_id is None:
                    if action_params and isinstance(action_params, dict) and 'action_id' in action_params:
                        action_id = action_params['action_id']
                        logger.info(f"Extracted action_id from action_params: {action_id}")

                # Check if step already exists
                cursor.execute(
                    'SELECT id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
                    (suite_id, test_idx, step_idx)
                )
                existing_step = cursor.fetchone()

                if existing_step:
                    # Update existing step
                    cursor.execute('''
                    UPDATE test_steps
                    SET name = ?, action_type = ?, status = ?, timestamp = ?, error = ?, action_id = ?
                    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                    ''', (
                        step_name, action_type, status, timestamp, error or '', action_id,
                        suite_id, test_idx, step_idx
                    ))
                    logger.info(f"Updated step information in test_steps table: {step_name}")
                else:
                    # Insert new step
                    cursor.execute('''
                    INSERT INTO test_steps
                    (suite_id, test_idx, step_idx, name, action_type, status, timestamp, error, action_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        suite_id, test_idx, step_idx, step_name, action_type, status, timestamp, error or '', action_id
                    ))
                    logger.info(f"Inserted step information into test_steps table: {step_name}")
            except Exception as step_error:
                logger.error(f"Error saving step information to test_steps table: {str(step_error)}")

        logger.info(f"🔵 COMMITTING transaction to database...")
        conn.commit()
        logger.info(f"✅ COMMIT SUCCESSFUL")

        conn.close()
        logger.info(f"✅ Database connection closed")

        # Trigger data synchronization if we have test_execution_id
        if test_execution_id:
            try:
                from app_android.utils.directory_utils import get_reports_directory
                reports_dir = get_reports_directory()
                sync_execution_data_to_json(test_execution_id, reports_dir)
                logger.info(f"✅ Synced execution data to JSON")
            except Exception as sync_error:
                logger.warning(f"⚠️ Failed to sync execution data to JSON: {str(sync_error)}")

        logger.info(f"✅ track_test_execution() COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        return True
    except Exception as e:
        logger.error(f"❌ ERROR in track_test_execution: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"❌ Traceback:\n{traceback.format_exc()}")
        logger.error("=" * 80)
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def find_suite_id_for_test_case(filename):
    """
    Find the suite_id for a given test case filename by looking up which test suite contains it

    Args:
        filename (str): Test case filename (e.g., 'health2.json')

    Returns:
        str: The suite_id if found, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        # Clean filename - remove path and ensure .json extension
        clean_filename = os.path.basename(filename)
        if not clean_filename.endswith('.json'):
            clean_filename += '.json'

        logger.info(f"Looking for suite_id for test case: {clean_filename}")

        # First try: Look for test case in test_cases table
        cursor.execute('''
            SELECT suite_id, test_case_id, name
            FROM test_cases
            WHERE file_path LIKE ? OR name = ?
            AND status = 'active'
            LIMIT 1
        ''', (f'%{clean_filename}', clean_filename.replace('.json', '')))

        result = cursor.fetchone()
        if result:
            suite_id, test_case_id, name = result
            logger.info(f"Found test case {clean_filename} in suite {suite_id} with test_case_id {test_case_id}")
            conn.close()
            return suite_id

        # Second try: Look in test_suites table for JSON data containing the filename
        cursor.execute('SELECT suite_id, name, json_payload FROM test_suites WHERE status = "active"')
        suites = cursor.fetchall()

        for suite_id, suite_name, json_payload in suites:
            if json_payload:
                try:
                    suite_data = json.loads(json_payload)
                    test_cases = suite_data.get('test_cases', [])
                    if clean_filename in test_cases:
                        logger.info(f"Found test case {clean_filename} in suite {suite_id} ({suite_name})")
                        conn.close()
                        return suite_id
                except json.JSONDecodeError:
                    continue

        conn.close()
        logger.warning(f"Could not find suite_id for test case: {clean_filename}")
        return None

    except Exception as e:
        logger.error(f"Error finding suite_id for test case {filename}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def find_test_case_id_for_filename(filename):
    """
    Find the correct test_case_id for a given filename from the database

    Args:
        filename (str): Test case filename (e.g., 'health2.json')

    Returns:
        str: The test_case_id if found, None otherwise
    """
    try:
        # Clean filename - remove path and ensure .json extension
        clean_filename = os.path.basename(filename)
        if not clean_filename.endswith('.json'):
            clean_filename += '.json'

        logger.info(f"Looking for test_case_id for filename: {clean_filename}")

        # Try both platform DBs to be platform-aware
        db_paths = _get_platform_db_paths()
        for platform, db_path in db_paths.items():
            if not os.path.exists(db_path):
                continue
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT test_case_id, suite_id, name
                    FROM test_cases
                    WHERE (file_path LIKE ? OR name = ?)
                    AND status = 'active'
                    LIMIT 1
                ''', (f'%{clean_filename}', clean_filename.replace('.json', '')))
                result = cursor.fetchone()
                conn.close()
                if result:
                    test_case_id, suite_id, name = result
                    logger.info(f"Found test_case_id {test_case_id} for filename {clean_filename} in suite {suite_id} ({platform})")
                    return test_case_id
            except Exception as inner_e:
                logger.debug(f"Lookup error in {platform} db ({db_path}): {inner_e}")
                try:
                    if 'conn' in locals():
                        conn.close()
                except Exception:
                    pass
                continue

        logger.warning(f"Could not find test_case_id for filename: {clean_filename}")
        return None

    except Exception as e:
        logger.error(f"Error finding test_case_id for filename {filename}: {str(e)}")
        try:
            if 'conn' in locals():
                conn.close()
        except Exception:
            pass
        return None
    except Exception as e:
        logger.error(f"Error finding test_case_id for filename {filename}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_case_id_mapping_for_suite(suite_id):
    """
    Get a mapping of test case filenames to test_case_ids for a given test suite.

    This function implements the database lookup process:
    1. Extract test case filenames from test suite json_payload
    2. Map filenames to test case IDs from test_cases table
    3. Return mapping for use in execution tracking

    Args:
        suite_id (str): The test suite ID (e.g., 'vuYvvC')

    Returns:
        dict: Mapping of filename -> test_case_id (e.g., {'health2.json': 'CSUVQZ'})
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        logger.info(f"Getting test case ID mapping for suite: {suite_id}")

        # Step 1: Extract test case filenames from test suite json_payload
        cursor.execute('''
            SELECT json_payload, name
            FROM test_suites
            WHERE suite_id = ? AND status = 'active'
            LIMIT 1
        ''', (suite_id,))

        result = cursor.fetchone()
        if not result:
            logger.warning(f"Test suite {suite_id} not found")
            conn.close()
            return {}

        json_payload, suite_name = result
        if not json_payload:
            logger.warning(f"No json_payload found for suite {suite_id}")
            conn.close()
            return {}

        # Parse the json_payload to extract test_cases array
        try:
            suite_data = json.loads(json_payload)
            test_case_filenames = suite_data.get('test_cases', [])
            logger.info(f"Found {len(test_case_filenames)} test cases in suite {suite_name}: {test_case_filenames}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse json_payload for suite {suite_id}: {e}")
            conn.close()
            return {}

        # Step 2: Map filenames to test case IDs
        filename_to_id_mapping = {}

        for filename in test_case_filenames:
            # Clean filename for consistent matching
            clean_filename = os.path.basename(filename)
            if not clean_filename.endswith('.json'):
                clean_filename += '.json'

            # Query test_cases table to find matching test_case_id
            cursor.execute('''
                SELECT test_case_id, name, file_path
                FROM test_cases
                WHERE (file_path LIKE ? OR name = ?) AND status = 'active'
                LIMIT 1
            ''', (f'%{clean_filename}', clean_filename.replace('.json', '')))

            test_case_result = cursor.fetchone()
            if test_case_result:
                test_case_id, test_case_name, file_path = test_case_result
                filename_to_id_mapping[filename] = test_case_id
                logger.info(f"Mapped {filename} -> {test_case_id} ({test_case_name})")
            else:
                logger.warning(f"No test_case_id found for filename: {filename}")
                # Use filename as fallback to avoid None values
                filename_to_id_mapping[filename] = filename.replace('.json', '')

        conn.close()
        logger.info(f"Created test case ID mapping for suite {suite_id}: {filename_to_id_mapping}")
        return filename_to_id_mapping

    except Exception as e:
        logger.error(f"Error getting test case ID mapping for suite {suite_id}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return {}

def clear_execution_tracking():
    """
    DEPRECATED: This function is no longer used in database-first architecture.
    Execution tracking records persist indefinitely (30-day retention policy applies).

    Returns:
        bool: True (no-op for backward compatibility)
    """
    logger.warning("clear_execution_tracking() called but is deprecated in database-first architecture")
    return True

def get_execution_tracking_by_id(execution_id):
    """
    Get all execution tracking records for a specific execution (DATABASE-FIRST)

    Args:
        execution_id (str): Unique execution identifier (test_execution_id)

    Returns:
        list: List of execution tracking records as dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM execution_tracking
            WHERE test_execution_id = ?
            ORDER BY test_idx, step_idx
        ''', (execution_id,))

        records = [dict(row) for row in cursor.fetchall()]
        conn.close()

        logger.info(f"Retrieved {len(records)} execution tracking records for execution {execution_id}")
        return records

    except Exception as e:
        logger.error(f"Error getting execution tracking by ID {execution_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return []

def get_screenshots_by_execution_id(execution_id):
    """
    Get all screenshots for a specific execution (DATABASE-FIRST)

    Args:
        execution_id (str): Unique execution identifier (test_execution_id)

    Returns:
        list: List of screenshot records as dictionaries (includes BLOBs)
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM screenshots
            WHERE test_execution_id = ?
            ORDER BY test_idx, step_idx
        ''', (execution_id,))

        records = [dict(row) for row in cursor.fetchall()]
        conn.close()

        logger.info(f"Retrieved {len(records)} screenshot records for execution {execution_id}")
        return records

    except Exception as e:
        logger.error(f"Error getting screenshots by execution ID {execution_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return []

def get_execution_summary(execution_id):
    """
    Get summary information for a specific execution (DATABASE-FIRST)

    Args:
        execution_id (str): Unique execution identifier (test_execution_id)

    Returns:
        dict: Execution summary with metadata and statistics
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                suite_id,
                MIN(start_time) as execution_start_time,
                MAX(end_time) as execution_end_time,
                COUNT(*) as total_steps,
                SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed_steps,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_steps,
                SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_steps,
                COUNT(DISTINCT test_case_id) as total_test_cases,
                COUNT(DISTINCT filename) as total_files
            FROM execution_tracking
            WHERE test_execution_id = ?
        ''', (execution_id,))

        row = cursor.fetchone()
        summary = dict(row) if row else {}

        # Get overall status
        if summary:
            cursor.execute('''
                SELECT status FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY id DESC LIMIT 1
            ''', (execution_id,))
            last_status = cursor.fetchone()
            summary['overall_status'] = last_status[0] if last_status else 'unknown'

        conn.close()

        logger.info(f"Retrieved execution summary for {execution_id}: {summary}")
        return summary

    except Exception as e:
        logger.error(f"Error getting execution summary for {execution_id}: {e}")
        if 'conn' in locals():
            conn.close()
        return {}

def get_screenshots_for_suite(suite_id):
    """
    Get all screenshots for a specific test suite

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of screenshot dictionaries with filename, path, test_idx, step_idx, and action_id
    """
    try:
        conn = sqlite3.connect(_detect_db_for_suite(actual_suite_id))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all screenshots for this suite
        cursor.execute(
            'SELECT * FROM screenshots WHERE suite_id = ?',
            (suite_id,)
        )
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        screenshots = []
        for row in rows:
            screenshot = {
                'filename': row['filename'],
                'path': row['path'],
                'test_idx': row['test_idx'],
                'step_idx': row['step_idx'],
                'action_id': row['action_id'],
                'timestamp': row['timestamp']
            }
            screenshots.append(screenshot)

        logger.info(f"Found {len(screenshots)} screenshots for suite {suite_id}")
        conn.close()
        return screenshots
    except Exception as e:
        logger.error(f"Error getting screenshots for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_test_steps_for_suite(suite_id):
    """
    Get all test steps for a specific test suite from the database

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of step dictionaries with all step information
    """
    try:
        conn = sqlite3.connect(_detect_db_for_suite(actual_suite_id))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # First, get all execution tracking entries for this suite
        # This will give us all the steps that were executed
        cursor.execute(
            '''
            SELECT * FROM execution_tracking
            WHERE suite_id = ? AND in_progress = 0
            ORDER BY test_idx, step_idx
            ''',
            (suite_id,)
        )
        execution_rows = cursor.fetchall()

        # Convert execution rows to dictionaries
        steps = []
        for row in execution_rows:
            # Convert row to dict
            step = dict(row)

            # Parse action_params if it's a JSON string
            if step['action_params'] and isinstance(step['action_params'], str):
                try:
                    action_params = json.loads(step['action_params'])
                    # Add relevant fields from action_params
                    if isinstance(action_params, dict):
                        # Add description or message as step name if available
                        if 'description' in action_params:
                            step['name'] = action_params['description']
                        elif 'message' in action_params:
                            step['name'] = action_params['message']
                        else:
                            step['name'] = f"Step {step['step_idx']}: {step['action_type']}"
                except:
                    # If JSON parsing fails, use a default name
                    step['name'] = f"Step {step['step_idx']}: {step['action_type']}"
            else:
                # Default name if no action_params
                step['name'] = f"Step {step['step_idx']}: {step['action_type']}"

            # Now get the screenshot for this step if available
            cursor.execute(
                '''
                SELECT * FROM screenshots
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''',
                (suite_id, step['test_idx'], step['step_idx'])
            )
            screenshot_row = cursor.fetchone()

            if screenshot_row:
                screenshot = dict(screenshot_row)
                # Add screenshot information
                step['screenshot'] = screenshot['path']
                step['screenshot_filename'] = screenshot['filename']
                step['report_screenshot'] = screenshot['filename']
                step['resolved_screenshot'] = f"screenshots/{screenshot['filename']}"

                # Use action_id from screenshot if available
                if screenshot['action_id']:
                    step['action_id'] = screenshot['action_id']

            # If we have an action_id in the step, use it for screenshot paths
            if step['action_id']:
                # Standardize screenshot paths based on action_id
                step['screenshot_filename'] = f"{step['action_id']}.png"
                step['report_screenshot'] = f"{step['action_id']}.png"
                step['resolved_screenshot'] = f"screenshots/{step['action_id']}.png"

            # Add duration field for report compatibility
            step['duration'] = '0ms'  # We don't track duration currently

            steps.append(step)

        logger.info(f"Found {len(steps)} steps for suite {suite_id}")

        # If we didn't find any steps in execution_tracking, try the test_steps table as fallback
        if not steps:
            logger.info(f"No steps found in execution_tracking, trying test_steps table")
            cursor.execute(
                '''
                SELECT ts.*, s.filename as screenshot_filename, s.path as screenshot_path, s.action_id as screenshot_action_id
                FROM test_steps ts
                LEFT JOIN screenshots s ON ts.suite_id = s.suite_id AND ts.test_idx = s.test_idx AND ts.step_idx = s.step_idx
                WHERE ts.suite_id = ?
                ORDER BY ts.test_idx, ts.step_idx
                ''',
                (suite_id,)
            )
            rows = cursor.fetchall()

            # Convert rows to dictionaries
            for row in rows:
                step = dict(row)

                # Add screenshot information if available
                if step.get('screenshot_filename'):
                    step['screenshot'] = step['screenshot_path']
                    step['report_screenshot'] = step['screenshot_filename']
                    step['resolved_screenshot'] = f"screenshots/{step['screenshot_filename']}"

                # Use action_id from step or screenshot
                if step.get('action_id'):
                    # Use step's action_id
                    pass
                elif step.get('screenshot_action_id'):
                    # Use screenshot's action_id
                    step['action_id'] = step['screenshot_action_id']

                # If we have an action_id, standardize screenshot paths
                if step.get('action_id'):
                    step['screenshot_filename'] = f"{step['action_id']}.png"
                    step['report_screenshot'] = f"{step['action_id']}.png"
                    step['resolved_screenshot'] = f"screenshots/{step['action_id']}.png"

                steps.append(step)

            logger.info(f"Found {len(steps)} steps in test_steps table for suite {suite_id}")

        conn.close()
        return steps
    except Exception as e:
        logger.error(f"Error getting steps for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def resolve_execution_id_to_suite_id(execution_id):
    """
    Resolve a timestamp-based execution ID to the actual test suite UUID.
    Enhanced to handle retry scenarios and imported test sessions.
    Prioritizes exact test_execution_id match over fuzzy fallbacks.

    Args:
        execution_id (str): Execution ID (could be timestamp-based like 'testsuite_execution_20250627_181306',
                           short exec ID like 'exec_595837249263', or actual suite UUID)

    Returns:
        str: The actual test suite UUID, or the original execution_id if no mapping found
    """
    try:
        conn = sqlite3.connect(_detect_db_for_execution(execution_id))
        cursor = conn.cursor()

        # Strategy 1: Direct test_execution_id match (highest priority - exact match)
        cursor.execute('''
            SELECT DISTINCT suite_id, test_execution_id, action_type
            FROM execution_tracking
            WHERE test_execution_id = ?
            ORDER BY CASE WHEN action_type = 'retry_update' THEN 0 ELSE 1 END, id DESC
            LIMIT 1
        ''', (execution_id,))

        result = cursor.fetchone()
        if result:
            actual_suite_id = result[0]
            logger.info(f"Resolved execution ID {execution_id} to suite ID {actual_suite_id} via exact test_execution_id match")
            conn.close()
            return actual_suite_id

        # Strategy 2: Check if this is already a valid suite_id in the database
        cursor.execute('SELECT DISTINCT suite_id FROM execution_tracking WHERE suite_id = ? LIMIT 1', (execution_id,))
        if cursor.fetchone():
            conn.close()
            logger.info(f"Execution ID {execution_id} is already a valid suite_id")
            return execution_id

        # Strategy 3: Pattern matching for timestamp-based IDs (only if exact match fails)
        if 'testsuite_execution_' in execution_id:
            # Extract timestamp from execution_id (e.g., '20250627_181306' from 'testsuite_execution_20250627_181306')
            timestamp_part = execution_id.replace('testsuite_execution_', '')

            # Look for execution tracking entries where test_execution_id contains this timestamp
            cursor.execute('''
                SELECT DISTINCT suite_id, test_execution_id, action_type
                FROM execution_tracking
                WHERE test_execution_id LIKE ? OR test_execution_id LIKE ?
                ORDER BY CASE WHEN action_type = 'retry_update' THEN 0 ELSE 1 END, id DESC
                LIMIT 1
            ''', (f'%{timestamp_part}%', f'%{execution_id}%'))

            result = cursor.fetchone()
            if result:
                actual_suite_id = result[0]
                logger.info(f"Resolved execution ID {execution_id} to suite ID {actual_suite_id} via timestamp pattern")
                conn.close()
                return actual_suite_id

        # Strategy 4: Handle short exec_* identifiers by fuzzy LIKE match (conservative fallback)
        if execution_id.startswith('exec_'):
            short = execution_id.replace('exec_', '')
            # Only use fuzzy match if the short ID is sufficiently long to avoid false positives
            if len(short) >= 10:
                cursor.execute('''
                    SELECT DISTINCT suite_id
                    FROM execution_tracking
                    WHERE test_execution_id LIKE ?
                    ORDER BY id DESC
                    LIMIT 1
                ''', (f'%{short}%',))
                result = cursor.fetchone()
                if result:
                    actual_suite_id = result[0]
                    logger.info(f"Resolved short exec ID {execution_id} to suite ID {actual_suite_id} via conservative LIKE match")
                    conn.close()
                    return actual_suite_id

        # Strategy 5: Check if execution_id is used as suite_id directly (for imported sessions)
        cursor.execute('''
            SELECT COUNT(*) as count, suite_id
            FROM execution_tracking
            WHERE suite_id = ?
            GROUP BY suite_id
        ''', (execution_id,))

        result = cursor.fetchone()
        if result and result[0] > 0:
            logger.info(f"Execution ID {execution_id} is being used as suite_id directly")
            conn.close()
            return execution_id

        # Remove previous fallback to 'active' suite from test_suites to avoid incorrect mappings
        logger.warning(f"Could not resolve execution ID {execution_id} to a suite ID using any strategy")
        conn.close()
        return execution_id

    except Exception as e:
        logger.error(f"Error resolving execution ID to suite ID: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return execution_id

def get_execution_tracking_for_suite(suite_id):
    """
    Get all execution tracking entries for a specific test suite from the database.
    Automatically resolves timestamp-based execution IDs to actual suite UUIDs.

    Args:
        suite_id (str): Test suite ID (can be timestamp-based execution ID or actual UUID)

    Returns:
        list: List of execution tracking dictionaries with all execution information
    """
    try:
        # Resolve the execution ID to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(suite_id)
        logger.info(f"Querying execution tracking for resolved suite_id: {actual_suite_id}")

        conn = sqlite3.connect(_detect_db_for_suite(actual_suite_id))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all execution tracking entries for this suite with custom screenshot information
        # Order by end_time DESC, then by id DESC to get the most recent execution status for each step
        # The id DESC ensures that if end_time is the same, we get the latest inserted record
        cursor.execute(
            '''
            SELECT et.*, s.custom_screenshot_name, s.custom_screenshot_filename, s.custom_screenshot_path
            FROM execution_tracking et
            LEFT JOIN screenshots s ON et.suite_id = s.suite_id
                AND et.test_idx = s.test_idx
                AND et.step_idx = s.step_idx
            WHERE et.suite_id = ?
            ORDER BY et.test_idx, et.step_idx, et.end_time DESC, et.id DESC
            ''',
            (actual_suite_id,)
        )
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        execution_entries = []
        for row in rows:
            entry = dict(row)

            # Convert action_params from JSON string to dict if it exists
            if entry.get('action_params'):
                try:
                    entry['action_params'] = json.loads(entry['action_params'])
                except:
                    # If JSON parsing fails, keep it as a string
                    pass

            # Log the action_id for debugging
            if entry.get('action_id'):
                logger.info(f"Found action_id {entry['action_id']} for step {entry.get('step_idx')} in test {entry.get('test_idx')}")
            else:
                logger.warning(f"No action_id found for step {entry.get('step_idx')} in test {entry.get('test_idx')}")

            # If action_params contains action_id, use it if entry doesn't already have one
            if not entry.get('action_id') and entry.get('action_params') and isinstance(entry['action_params'], dict) and entry['action_params'].get('action_id'):
                entry['action_id'] = entry['action_params']['action_id']
                logger.info(f"Using action_id {entry['action_id']} from action_params for step {entry.get('step_idx')} in test {entry.get('test_idx')}")

            # Add timestamp for sorting
            entry['timestamp'] = entry.get('start_time') or entry.get('end_time') or datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Ensure status is properly mapped for report generation
            db_status = entry.get('status', 'unknown')
            if db_status in ['success', 'passed']:
                entry['status'] = 'passed'
            elif db_status in ['error', 'failed']:
                entry['status'] = 'failed'
            elif db_status == 'unknown' and entry.get('last_error') and entry.get('last_error').strip():
                # If status is unknown but there's an error, mark as failed
                entry['status'] = 'failed'
            elif db_status == 'unknown' and not entry.get('last_error'):
                # If status is unknown and no error, mark as passed
                entry['status'] = 'passed'

            logger.debug(f"Mapped status '{db_status}' to '{entry['status']}' for action_id {entry.get('action_id', 'N/A')}")

            execution_entries.append(entry)

        logger.info(f"Found {len(execution_entries)} execution tracking entries for suite {suite_id}")
        conn.close()
        return execution_entries
    except Exception as e:
        logger.error(f"Error getting execution tracking for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_action_id_for_step(suite_id, test_idx, step_idx):
    """
    Get the action_id for a specific step in a test case

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index

    Returns:
        str: Action ID if found, None otherwise
    """
    try:
        conn = sqlite3.connect(_detect_db_for_suite(suite_id))
        cursor = conn.cursor()

        # First try to find the action_id in the test_steps table
        cursor.execute(
            'SELECT action_id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in test_steps table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If not found in test_steps, try the screenshots table
        cursor.execute(
            'SELECT action_id FROM screenshots WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in screenshots table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If not found in screenshots, try the execution_tracking table
        cursor.execute(
            'SELECT action_id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in execution_tracking table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If we still don't have an action_id, return None
        logger.warning(f"No action_id found for step {step_idx} in test {test_idx}")
        conn.close()
        return None
    except Exception as e:
        logger.error(f"Error getting action_id for step: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_execution_status(suite_id, test_idx=None, filename=None, test_case_id=None):
    """
    Get test execution status from the database

    Args:
        suite_id (str): Test suite ID
        test_idx (int, optional): Test case index
        filename (str, optional): Test case filename

    Returns:
        dict or list: Test execution status(es)
    """
    try:
        conn = sqlite3.connect(_detect_db_for_suite(suite_id))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if test_idx is not None and filename is not None:
            # Get specific test execution - prioritize test_case_id if available
            if test_case_id:
                cursor.execute(
                    'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND test_case_id = ? ORDER BY retry_count DESC LIMIT 1',
                    (suite_id, test_idx, test_case_id)
                )
            else:
                cursor.execute(
                    'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND filename = ? ORDER BY retry_count DESC LIMIT 1',
                    (suite_id, test_idx, filename)
                )
            row = cursor.fetchone()
            conn.close()

            if row:
                return dict(row)
            return None
        else:
            # Get all test executions for the suite
            cursor.execute(
                'SELECT * FROM execution_tracking WHERE suite_id = ? ORDER BY test_idx',
                (suite_id,)
            )
            rows = cursor.fetchall()
            conn.close()

            return [dict(row) for row in rows]
    except Exception as e:
        logger.error(f"Error getting test execution status: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_execution_data(execution_id):
    """
    Get test execution data for a specific execution ID

    Args:
        execution_id (str): Execution ID (can be timestamp-based or actual suite UUID)

    Returns:
        list: List of execution data dictionaries containing filename, test_idx, test_case_id, etc.
    """
    try:
        # Resolve the execution ID to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(execution_id)
        logger.info(f"Getting test execution data for resolved suite_id: {actual_suite_id}")

        conn = sqlite3.connect(_detect_db_for_suite(actual_suite_id))
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all execution tracking entries for this suite
        cursor.execute('''
            SELECT DISTINCT filename, test_idx, test_case_id, suite_id
            FROM execution_tracking
            WHERE suite_id = ?
            ORDER BY test_idx
        ''', (actual_suite_id,))

        rows = cursor.fetchall()
        conn.close()

        if not rows:
            logger.warning(f"No execution data found for execution_id: {execution_id} (resolved to: {actual_suite_id})")
            return []

        # Convert to list of dictionaries
        execution_data = []
        for row in rows:
            execution_data.append({
                'filename': row['filename'],
                'test_idx': row['test_idx'],
                'test_case_id': row['test_case_id'],
                'suite_id': row['suite_id']
            })

        logger.info(f"Found {len(execution_data)} test cases for execution_id: {execution_id}")
        return execution_data

    except Exception as e:
        logger.error(f"Error getting test execution data: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def list_executions(page: int = 1, page_size: int = 20):
    """
    List executions from database (execution_reports table) with pagination (Android).

    This function queries the execution_reports table to build the execution list.
    """
    try:
        import json
        from datetime import datetime

        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Calculate offset for pagination
        offset = (page - 1) * page_size

        # Query execution_reports table
        cursor.execute('''
            SELECT * FROM execution_reports
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ''', (page_size, offset))

        rows = cursor.fetchall()
        conn.close()

        executions = []
        for row in rows:
            row_dict = dict(row)

            # Get basic info
            execution_id = row_dict.get('test_execution_id') or row_dict.get('execution_id')
            if not execution_id:
                continue

            # Parse report data if available
            report_data = None
            if row_dict.get('report_data'):
                try:
                    report_data = json.loads(row_dict['report_data'])
                except:
                    pass
            elif row_dict.get('data_json'):
                try:
                    data_json = row_dict['data_json']
                    if isinstance(data_json, bytes):
                        report_data = json.loads(data_json.decode('utf-8'))
                    else:
                        report_data = json.loads(data_json)
                except:
                    pass

            # Extract metadata
            if report_data:
                suite_name = report_data.get('name', 'Unknown Suite')
                test_cases = report_data.get('test_cases', []) or report_data.get('testCases', [])
                summary = report_data.get('summary', {})

                total_tests = summary.get('total_tests', len(test_cases))
                passed_tests = summary.get('passed', sum(1 for tc in test_cases if tc.get('status') == 'passed'))
                failed_tests = summary.get('failed', sum(1 for tc in test_cases if tc.get('status') == 'failed'))
            else:
                suite_name = row_dict.get('suite_id', 'Unknown Suite')
                total_tests = 0
                passed_tests = 0
                failed_tests = 0

            # Determine overall status
            status = row_dict.get('status', 'unknown')
            if status == 'unknown':
                status = 'failed' if failed_tests > 0 else 'passed'

            # Format timestamp
            created_at = row_dict.get('created_at') or row_dict.get('start_time')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    timestamp_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    timestamp_str = created_at
            else:
                timestamp_str = 'Unknown'

            # Add to executions list
            executions.append({
                'execution_id': execution_id,
                'suite_name': suite_name,
                'started_at': row_dict.get('start_time') or row_dict.get('created_at'),
                'finished_at': row_dict.get('end_time') or row_dict.get('created_at'),
                'status': status,
                'total_tests': total_tests,
                'passed_count': passed_tests,
                'failed_count': failed_tests,
                'suite_id': row_dict.get('suite_id', execution_id),
                'platform': row_dict.get('platform', 'Android'),
                'duration': row_dict.get('duration', 0),
                'error_message': row_dict.get('error_message'),
                'created_at': timestamp_str
            })

        return executions

    except Exception as e:
        logger.error(f"Error listing executions (Android): {e}")
        logger.exception(e)
        return []


def count_executions():
    """
    Count total executions from database (execution_reports table) (Android).

    This function queries the execution_reports table to count total executions.
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Count execution_reports
        cursor.execute('SELECT COUNT(*) FROM execution_reports')
        count = cursor.fetchone()[0]
        conn.close()

        logger.info(f"Found {count} total executions in database")
        return count

    except Exception as e:
        logger.error(f"Error counting executions (Android): {e}")
        logger.exception(e)
        return 0

def update_data_json_with_retry_results(execution_id, suite_id):
    """
    Update the data.json file with the latest retry results from the database

    Args:
        execution_id (str): Test execution ID
        suite_id (str): Test suite ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"=== UPDATING DATA.JSON === Execution ID: {execution_id}, Suite ID: {suite_id}")

        # Find the data.json file for this execution
        from .directory_utils import get_reports_directory
        reports_base_dir = get_reports_directory()
        execution_dir = os.path.join(reports_base_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')

        if not os.path.exists(data_json_path):
            logger.warning(f"data.json not found at {data_json_path}")
            return False

        # Load the current data.json
        with open(data_json_path, 'r') as f:
            data = json.load(f)

        logger.info(f"Loaded data.json with {len(data.get('testCases', []))} test cases")

        # Get the latest execution data from database
        execution_data = get_execution_tracking_for_suite(suite_id)
        if not execution_data:
            logger.warning(f"No execution data found in database for suite_id: {suite_id}")
            return False

        # Group execution data by test case and action
        test_case_updates = {}
        for entry in execution_data:
            test_case_id = entry.get('test_case_id')
            filename = entry.get('filename', '')
            action_id = entry.get('action_id')
            action_type = entry.get('action_type')
            status = entry.get('status')
            retry_count = entry.get('retry_count', 0)

            if not test_case_id and filename:
                # Use filename as fallback identifier
                test_case_id = filename.replace('.json', '')

            if test_case_id not in test_case_updates:
                test_case_updates[test_case_id] = {
                    'filename': filename,
                    'actions': {},
                    'final_status': 'unknown'
                }

            if action_id and action_id not in test_case_updates[test_case_id]['actions']:
                test_case_updates[test_case_id]['actions'][action_id] = []

            if action_id:
                test_case_updates[test_case_id]['actions'][action_id].append({
                    'status': status,
                    'retry_count': retry_count,
                    'action_type': action_type,
                    'timestamp': entry.get('end_time')
                })

        # Determine final status for each test case
        for test_case_id, updates in test_case_updates.items():
            action_final_statuses = []
            for action_id, action_entries in updates['actions'].items():
                # Sort by retry_count to get the latest attempt
                action_entries.sort(key=lambda x: (x['retry_count'], x['timestamp'] or ''))
                if action_entries:
                    latest_entry = action_entries[-1]
                    action_final_statuses.append(latest_entry['status'])

            # Test case passes only if all actions pass
            if action_final_statuses:
                if all(status == 'passed' for status in action_final_statuses):
                    updates['final_status'] = 'passed'
                else:
                    updates['final_status'] = 'failed'

        # Update the data.json structure
        updated = False
        for test_case in data.get('testCases', []):
            test_case_id = test_case.get('id') or test_case.get('test_case_id')
            test_case_name = test_case.get('name', '')

            # Try to match by ID first, then by name
            matching_update = None
            if test_case_id and test_case_id in test_case_updates:
                matching_update = test_case_updates[test_case_id]
            else:
                # Try to match by filename/name
                for tc_id, tc_update in test_case_updates.items():
                    if tc_update['filename'] and test_case_name in tc_update['filename']:
                        matching_update = tc_update
                        break

            if matching_update and matching_update['final_status'] != 'unknown':
                old_status = test_case.get('status', 'unknown')
                new_status = matching_update['final_status']

                if old_status != new_status:
                    test_case['status'] = new_status
                    updated = True
                    logger.info(f"Updated test case '{test_case_name}' status: {old_status} -> {new_status}")

                    # Also update individual step statuses if available
                    if 'steps' in test_case and matching_update['actions']:
                        for step in test_case['steps']:
                            step_action_id = step.get('action_id')
                            if step_action_id and step_action_id in matching_update['actions']:
                                action_entries = matching_update['actions'][step_action_id]
                                if action_entries:
                                    latest_action = action_entries[-1]
                                    old_step_status = step.get('status', 'unknown')
                                    new_step_status = latest_action['status']
                                    if old_step_status != new_step_status:
                                        step['status'] = new_step_status
                                        step['retry_count'] = latest_action['retry_count']
                                        logger.info(f"Updated step '{step_action_id}' status: {old_step_status} -> {new_step_status}")

        # Update overall suite status
        if updated:
            test_case_statuses = [tc.get('status', 'unknown') for tc in data.get('testCases', [])]
            if all(status == 'passed' for status in test_case_statuses):
                data['status'] = 'passed'
            elif any(status == 'failed' for status in test_case_statuses):
                data['status'] = 'failed'

            # Add retry metadata
            data['has_retries'] = True
            data['last_updated'] = datetime.now().isoformat()

            # Create backup of original file
            backup_path = data_json_path + '.backup'
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(data_json_path, backup_path)
                logger.info(f"Created backup: {backup_path}")

            # Save updated data.json
            with open(data_json_path, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Successfully updated data.json with retry results")
            return True
        else:
            logger.info(f"No updates needed for data.json")
            return True

    except Exception as e:
        logger.error(f"Error updating data.json with retry results: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def get_final_test_case_status(suite_id, test_case_id=None, filename=None, test_idx=None):
    """
    Get the final status for a test case considering all retries

    Args:
        suite_id (str): Test suite ID
        test_case_id (str, optional): Test case ID
        filename (str, optional): Test case filename
        test_idx (int, optional): Test case index

    Returns:
        dict: Final status information including overall test case status
    """
    try:
        conn = sqlite3.connect(_detect_db_for_suite(suite_id))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Build query based on available identifiers
        # PRIORITIZE UUID-based lookups for reliability
        where_conditions = ['suite_id = ?']
        params = [suite_id]

        if test_case_id:
            # PREFERRED: Use UUID-based lookup
            where_conditions.append('test_case_id = ?')
            params.append(test_case_id)
            logger.debug(f"Using UUID-based lookup: test_case_id={test_case_id}")
        elif filename:
            # FALLBACK: Use filename-based lookup (less reliable)
            where_conditions.append('filename = ?')
            params.append(filename)
            logger.debug(f"Using filename-based lookup: filename={filename}")
        elif test_idx is not None:
            # FALLBACK: Use test_idx-based lookup (legacy)
            where_conditions.append('test_idx = ?')
            params.append(test_idx)
            logger.debug(f"Using test_idx-based lookup: test_idx={test_idx}")
        else:
            logger.warning("No test case identifier provided")
            return {'status': 'unknown', 'actions': []}

        where_clause = ' AND '.join(where_conditions)

        # Get all actions for this test case, prioritizing retry_update entries
        # Order by action_id, then prioritize retry_update entries, then by end_time DESC, id DESC
        cursor.execute(f'''
            SELECT action_id, status, retry_count, end_time, action_type, id
            FROM execution_tracking
            WHERE {where_clause}
            ORDER BY action_id,
                     CASE WHEN action_type = 'retry_update' THEN 0 ELSE 1 END,
                     end_time DESC, id DESC
        ''', params)

        rows = cursor.fetchall()
        conn.close()

        if not rows:
            return {'status': 'unknown', 'actions': []}

        # Group by action_id and get the latest status for each action, prioritizing retry_update
        action_statuses = {}
        for row in rows:
            action_id = row['action_id']
            action_type = row['action_type']
            retry_count = row['retry_count'] or 0

            if action_id not in action_statuses:
                # First entry for this action_id (most recent due to ORDER BY with retry_update priority)
                # Map database status to standard status values
                db_status = row['status']
                if db_status in ['success', 'passed']:
                    mapped_status = 'passed'
                elif db_status in ['error', 'failed']:
                    mapped_status = 'failed'
                elif db_status == 'running':
                    mapped_status = 'running'
                else:
                    mapped_status = 'unknown'

                action_statuses[action_id] = {
                    'status': mapped_status,
                    'retry_count': retry_count,
                    'end_time': row['end_time'],
                    'action_type': action_type
                }

                # Log retry information for debugging
                if retry_count > 0:
                    logger.debug(f"Action {action_id}: Using {action_type} status '{mapped_status}' (retry_count: {retry_count})")
                else:
                    logger.debug(f"Action {action_id}: Using {action_type} status '{mapped_status}' (no retries)")
            elif action_type == 'retry_update' and action_statuses[action_id]['action_type'] != 'retry_update':
                # Override with retry_update if we previously had a non-retry entry
                # This shouldn't happen with our improved ORDER BY, but keeping as safety net
                db_status = row['status']
                if db_status in ['success', 'passed']:
                    mapped_status = 'passed'
                elif db_status in ['error', 'failed']:
                    mapped_status = 'failed'
                elif db_status == 'running':
                    mapped_status = 'running'
                else:
                    mapped_status = 'unknown'

                logger.info(f"Action {action_id}: Overriding with retry_update status '{mapped_status}' (was: {action_statuses[action_id]['status']})")
                action_statuses[action_id] = {
                    'status': mapped_status,
                    'retry_count': retry_count,
                    'end_time': row['end_time'],
                    'action_type': action_type
                }

        # Determine overall test case status based on final action statuses
        final_statuses = [action['status'] for action in action_statuses.values()]

        # If all actions passed, test case passed
        if final_statuses and all(status == 'passed' for status in final_statuses):
            overall_status = 'passed'
        # If any action failed, test case failed
        elif any(status == 'failed' for status in final_statuses):
            overall_status = 'failed'
        # If any action is running, test case is running
        elif any(status == 'running' for status in final_statuses):
            overall_status = 'running'
        else:
            overall_status = 'unknown'

        return {
            'status': overall_status,
            'actions': action_statuses,
            'total_actions': len(action_statuses)
        }

    except Exception as e:
        logger.error(f"Error getting final test case status: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return {'status': 'unknown', 'actions': []}

def reset_test_case_execution_tracking(suite_id, test_idx):
    """
    No-op under database-first architecture (Android). Preserve prior executions.
    """
    logger.info("DATABASE-FIRST: reset_test_case_execution_tracking is a no-op (Android, preserving history)")
    return True

def has_test_case_failures(suite_id, test_idx, force_return_true=False):
    """
    Check if there are any failures in the current test case.
    This is the SINGLE SOURCE OF TRUTH for determining if a test case has failures.
    All code should use this function instead of tracking failures in memory.

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        force_return_true (bool): If True, always return True regardless of actual failures.

    Returns:
        tuple: (has_failures, failure_details)
            - has_failures (bool): True if there are failures, False otherwise
            - failure_details (str): Error message if there are failures, None otherwise
    """
    try:
        # If force_return_true is set, return True immediately without checking the database
        if force_return_true:
            logger.info(f"DEBUG: force_return_true is set, returning True without checking database")
            return True, "Force return true flag is set"

        # Log detailed information for debugging
        logger.info(f"DEBUG: Checking for failures in test case {test_idx} for suite {suite_id}")

        # Validate parameters
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        if test_idx is None:
            logger.warning("DEBUG: test_idx is None, using 0 instead")
            test_idx = 0

        # Convert test_idx to int if it's not already
        try:
            test_idx = int(test_idx)
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the current test run start time
        # First, check if there's a running entry for this test case
        query_start_time = '''
        SELECT MIN(created_at) as start_time
        FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND in_progress = 1
        '''
        cursor.execute(query_start_time, (suite_id, test_idx))
        start_time_row = cursor.fetchone()

        if start_time_row and start_time_row['start_time']:
            # We found a running entry, use its timestamp as the start time
            start_time = start_time_row['start_time']
            logger.info(f"DEBUG: Found running entry with start time: {start_time}")
        else:
            # No running entry found, check for the earliest entry for this test case in this run
            query_earliest = '''
            SELECT MIN(created_at) as start_time
            FROM execution_tracking
            WHERE suite_id = ? AND test_idx = ?
            '''
            cursor.execute(query_earliest, (suite_id, test_idx))
            earliest_row = cursor.fetchone()

            if earliest_row and earliest_row['start_time']:
                start_time = earliest_row['start_time']
                logger.info(f"DEBUG: Using earliest entry as start time: {start_time}")
            else:
                # No entries found at all, use a recent timestamp (last 10 minutes)
                from datetime import datetime, timedelta
                start_time = (datetime.now() - timedelta(minutes=10)).strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"DEBUG: No entries found, using default start time: {start_time}")

        # Check for any failed steps in the current test case, only considering entries after the start time
        query1 = '''
        SELECT * FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND status = "failed" AND created_at >= ?
        '''
        logger.info(f"DEBUG: Executing query: {query1} with params: ({suite_id}, {test_idx}, {start_time})")
        cursor.execute(query1, (suite_id, test_idx, start_time))
        failed_rows = cursor.fetchall()

        # Log the raw failed rows for debugging
        logger.info(f"DEBUG: Found {len(failed_rows)} failed rows in current run (after {start_time})")
        for i, row in enumerate(failed_rows[:3]):  # Only log the first 3 rows to avoid flooding the logs
            row_dict = dict(row)
            logger.info(f"DEBUG: Failed row {i+1}: {row_dict}")

        if len(failed_rows) > 3:
            logger.info(f"DEBUG: ... and {len(failed_rows) - 3} more failed rows")

        # Also check for steps with errors in the current run
        query2 = '''
        SELECT * FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND last_error IS NOT NULL AND last_error != "" AND created_at >= ?
        '''
        logger.info(f"DEBUG: Executing query: {query2} with params: ({suite_id}, {test_idx}, {start_time})")
        cursor.execute(query2, (suite_id, test_idx, start_time))
        error_rows = cursor.fetchall()

        # Log the raw error rows for debugging
        logger.info(f"DEBUG: Found {len(error_rows)} error rows in current run (after {start_time})")
        for i, row in enumerate(error_rows[:3]):  # Only log the first 3 rows to avoid flooding the logs
            row_dict = dict(row)
            logger.info(f"DEBUG: Error row {i+1}: {row_dict}")

        if len(error_rows) > 3:
            logger.info(f"DEBUG: ... and {len(error_rows) - 3} more error rows")

        conn.close()

        # Combine failed and error rows
        all_failures = failed_rows + error_rows

        if all_failures:
            # Get the error message from the first failure
            error_message = None
            for row in all_failures:
                row_dict = dict(row)
                if row_dict.get('last_error'):
                    error_message = row_dict['last_error']
                    break

            logger.info(f"DEBUG: Found {len(all_failures)} total failures in test case {test_idx} for suite {suite_id} in current run")
            if error_message:
                logger.info(f"DEBUG: Error message: {error_message[:100]}..." if len(str(error_message)) > 100 else f"DEBUG: Error message: {error_message}")

            return True, error_message

        logger.info(f"DEBUG: No failures found in test case {test_idx} for suite {suite_id} in current run")
        return False, None
    except Exception as e:
        logger.error(f"Error checking for test case failures: {str(e)}")
        logger.error(traceback.format_exc())  # Log the full traceback for better debugging
        if 'conn' in locals() and conn:
            conn.close()
        return False, None


def get_hook_execution_count(suite_id, test_idx, step_idx=None, action_type=None):
    """
    Get the number of times a hook action has been executed for a specific test case

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int, optional): Step index
        action_type (str, optional): Action type (e.g., 'hookAction')

    Returns:
        int: Number of times the hook action has been executed
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Build the query based on the provided parameters
        query = 'SELECT COUNT(*) FROM execution_tracking WHERE suite_id = ? AND test_idx = ?'
        params = [suite_id, test_idx]

        # Add step_idx condition if provided
        if step_idx is not None:
            query += ' AND step_idx = ?'
            params.append(step_idx)

        # Add action_type condition if provided
        if action_type is not None:
            query += ' AND action_type = ?'
            params.append(action_type)

        # Execute the query
        cursor.execute(query, params)
        count = cursor.fetchone()[0]
        conn.close()

        logger.info(f"Hook execution count for suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}, action_type={action_type}: {count}")
        return count
    except Exception as e:
        logger.error(f"Error getting hook execution count: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return 0

def clear_database():
    """
    Clear all test data from the database but preserve the screenshots table
    This is a less aggressive alternative to reset_database() that doesn't drop tables

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING DATABASE (PRESERVING SCREENSHOTS) ===")

        # Check if database file exists
        if not os.path.exists(get_db_path()):
            logger.warning(f"Database file does not exist: {get_db_path()}")
            # Create the database file and initialize it
            init_db()
            logger.info("Database initialized - it was empty to begin with")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        try:
            # Begin transaction
            conn.execute('BEGIN TRANSACTION')

            # Count before clearing
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count_before = cursor.fetchone()[0]

            logger.info(f"Before clearing - Database contains: {suites_count_before} suites, {cases_count_before} cases, {steps_count_before} steps, {screenshots_count_before} screenshots")

            # Clear all tables EXCEPT screenshots
            cursor.execute('DELETE FROM test_suites')
            cursor.execute('DELETE FROM test_cases')
            cursor.execute('DELETE FROM test_steps')
            # DO NOT clear screenshots: cursor.execute('DELETE FROM screenshots')
            logger.info("Cleared all tables except screenshots")

            # Verify the clear operation succeeded
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count_after = cursor.fetchone()[0]

            logger.info(f"After clearing - Database contains: {suites_count_after} suites, {cases_count_after} cases, {steps_count_after} steps, {screenshots_count_after} screenshots")

            # Verify screenshots were preserved
            if screenshots_count_after == screenshots_count_before:
                logger.info(f"Successfully preserved {screenshots_count_after} screenshots")
            else:
                logger.warning(f"Screenshot count changed: {screenshots_count_before} before, {screenshots_count_after} after")

            # Commit the transaction
            conn.commit()
            logger.info("Database clearing transaction committed")

            # Verify success
            clear_successful = (suites_count_after == 0 and cases_count_after == 0 and steps_count_after == 0)
            logger.info(f"Database clear successful: {clear_successful}")

            return clear_successful

        except Exception as e:
            # Rollback on error
            conn.rollback()
            logger.error(f"Error during database clearing: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Close connection
            conn.close()
            logger.info("Database connection closed after clear operation")

    except Exception as e:
        logger.error(f"Error in clear_database: {str(e)}")
        logger.error(traceback.format_exc())

        # Try the reset_database function as a last resort
        logger.info("Attempting reset_database as fallback after clear_database failure")
        reset_success = reset_database()
        return reset_success

def save_test_suite(suite_data):
    """
    Save test suite data to the database

    Args:
        suite_data (dict): Test suite data

    Returns:
        str: Suite ID
    """
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()

    suite_id = suite_data.get('id')
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Insert test suite
    cursor.execute('''
    INSERT INTO test_suites (suite_id, name, status, passed, failed, skipped, timestamp, report_dir, error)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        suite_id,
        suite_data.get('name', 'Unknown Suite'),
        suite_data.get('status', 'unknown'),
        suite_data.get('passed', 0),
        suite_data.get('failed', 0),
        suite_data.get('skipped', 0),
        timestamp,
        suite_data.get('report_dir', ''),
        suite_data.get('error', '')
    ))

    # Insert test cases and steps
    for test_idx, test_case in enumerate(suite_data.get('testCases', [])):
        cursor.execute('''
        INSERT INTO test_cases (suite_id, test_idx, name, status, duration, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            suite_id,
            test_idx,
            test_case.get('name', f'Test {test_idx}'),
            test_case.get('status', 'unknown'),
            test_case.get('duration', '0ms'),
            timestamp
        ))

        for step_idx, step in enumerate(test_case.get('steps', [])):
            # Extract action type from step data
            action_type = None
            if 'action' in step and isinstance(step['action'], dict) and 'type' in step['action']:
                action_type = step['action']['type']
            elif 'type' in step:
                action_type = step['type']
            elif 'name' in step and ':' in step['name']:
                # Try to extract from name (e.g., "Tap: element" -> "Tap")
                action_type = step['name'].split(':', 1)[0].strip()

            cursor.execute('''
            INSERT INTO test_steps (suite_id, test_idx, step_idx, name, action_type, status, duration, timestamp, screenshot_path, error)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id,
                test_idx,
                step_idx,
                step.get('name', f'Step {step_idx}'),
                action_type,
                step.get('status', 'unknown'),
                step.get('duration', '0ms'),
                step.get('timestamp', timestamp),
                step.get('screenshot', ''),
                step.get('error', '')
            ))

            # Save screenshot info if available
            if 'screenshot_filename' in step and step['screenshot_filename']:
                cursor.execute('''
                INSERT INTO screenshots (suite_id, test_idx, step_idx, filename, path, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    suite_id,
                    test_idx,
                    step_idx,
                    step.get('screenshot_filename', ''),
                    step.get('screenshot', ''),
                    timestamp
                ))

    conn.commit()
    conn.close()

    logger.info(f"Saved test suite {suite_id} to database")
    return suite_id

def get_test_suite(suite_id):
    """
    Get test suite data from the database

    Args:
        suite_id (str): Test suite ID

    Returns:
        dict: Test suite data
    """
    conn = sqlite3.connect(get_db_path())
    conn.row_factory = sqlite3.Row  # This enables column access by name
    cursor = conn.cursor()

    # Get test suite
    cursor.execute('SELECT * FROM test_suites WHERE suite_id = ?', (suite_id,))
    suite_row = cursor.fetchone()

    if not suite_row:
        conn.close()
        return None

    # Convert to dict
    suite_data = dict(suite_row)

    # Initialize screenshots map
    screenshots_map = {}

    # Get test cases
    cursor.execute('SELECT * FROM test_cases WHERE suite_id = ? ORDER BY test_idx', (suite_id,))
    test_cases = []

    for test_case_row in cursor.fetchall():
        test_case = dict(test_case_row)
        test_idx = test_case['test_idx']

        # Get steps for this test case
        cursor.execute('''
        SELECT * FROM test_steps
        WHERE suite_id = ? AND test_idx = ?
        ORDER BY step_idx
        ''', (suite_id, test_idx))

        steps = []
        for step_row in cursor.fetchall():
            step = dict(step_row)
            step_idx = step['step_idx']

            # Get screenshot for this step - prioritize standardized screenshots
            cursor.execute('''
            SELECT * FROM screenshots
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ?
            ''', (suite_id, test_idx, step_idx, f"step_{test_idx}_{step_idx}.png"))

            standardized_screenshot = cursor.fetchone()

            if standardized_screenshot:
                # Use the standardized screenshot
                screenshot = dict(standardized_screenshot)
                step['screenshot'] = screenshot['path']
                step['screenshot_filename'] = screenshot['filename']
                step['report_screenshot'] = screenshot['filename']
                step['resolved_screenshot'] = f"screenshots/{screenshot['filename']}"

                logger.info(f"Found standardized screenshot for step {step_idx}: {screenshot['filename']}")
            else:
                # Fall back to any screenshot for this step
                cursor.execute('''
                SELECT * FROM screenshots
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (suite_id, test_idx, step_idx))

                screenshot_row = cursor.fetchone()
                if screenshot_row:
                    screenshot = dict(screenshot_row)
                    step['screenshot'] = screenshot['path']
                    step['screenshot_filename'] = screenshot['filename']

                    # Also add the standardized format for the report
                    standardized_name = f"step_{test_idx}_{step_idx}.png"
                    step['report_screenshot'] = standardized_name
                    step['resolved_screenshot'] = f"screenshots/{standardized_name}"

                    logger.info(f"Using non-standardized screenshot for step {step_idx}: {screenshot['filename']}")

            # If we found any screenshot, add it to the screenshots map
            if 'screenshot_filename' in step:
                # Add to screenshots map for easy access in the report
                test_case_name = test_case.get('name', f'test_{test_idx}')
                test_case_name = re.sub(r'[^a-zA-Z0-9]', '', test_case_name)
                screenshot_key = f"{test_case_name}_{step_idx}"

                screenshots_map[screenshot_key] = {
                    'filename': step['screenshot_filename'],
                    'path': step['screenshot'],
                    'action_type': step.get('name', '').split(':')[0].strip() if ':' in step.get('name', '') else step.get('name', '')
                }

            steps.append(step)

        test_case['steps'] = steps
        test_cases.append(test_case)

    suite_data['testCases'] = test_cases
    suite_data['screenshots_map'] = screenshots_map

    conn.close()
    return suite_data

def get_latest_test_suite():
    """
    Get the latest test suite from the database

    Returns:
        dict: Latest test suite data
    """
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()

    cursor.execute('SELECT suite_id FROM test_suites ORDER BY id DESC LIMIT 1')
    result = cursor.fetchone()

    conn.close()

    if result:
        return get_test_suite(result[0])
    return None

def save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id=None, custom_screenshot_name=None, custom_screenshot_filename=None, custom_screenshot_path=None):
    """
    Save screenshot information to the database and ensure a standardized version exists

    Args:
        suite_id (str, optional): Test suite ID. If None or empty, will be treated as an empty string.
        test_idx (int): Test case index
        step_idx (int): Step index
        filename (str): Screenshot filename
        path (str): Screenshot path
        action_id (str, optional): Action ID from the test case
        custom_screenshot_name (str, optional): Custom screenshot name
        custom_screenshot_filename (str, optional): Custom screenshot filename
        custom_screenshot_path (str, optional): Custom screenshot path

    Returns:
        bool: Success status
    """
    # Ensure suite_id is a string, even if None is passed
    if suite_id is None:
        suite_id = ''

    logger.info("=" * 80)
    logger.info("save_screenshot_info() CALLED")
    logger.info(f"  suite_id: {suite_id}")
    logger.info(f"  test_idx: {test_idx}")
    logger.info(f"  step_idx: {step_idx}")
    logger.info(f"  filename: {filename}")
    logger.info(f"  path: {path}")
    logger.info(f"  action_id: {action_id}")
    logger.info(f"  custom_screenshot_name: {custom_screenshot_name}")
    logger.info(f"  custom_screenshot_filename: {custom_screenshot_filename}")
    logger.info(f"  custom_screenshot_path: {custom_screenshot_path}")
    logger.info("=" * 80)

    # Ensure test_idx and step_idx are integers
    try:
        test_idx = int(test_idx)
        step_idx = int(step_idx)
    except (ValueError, TypeError):
        logger.warning(f"Invalid test_idx or step_idx values: test_idx={test_idx}, step_idx={step_idx}")
        # Default to 0 for test_idx and 1 for step_idx if conversion fails
        if not isinstance(test_idx, int):
            test_idx = 0
        if not isinstance(step_idx, int):
            # Start from 1 instead of 0 to match UI display
            step_idx = 1

    logger.info(f"Using test_idx={test_idx}, step_idx={step_idx} after validation")
    # DB path diagnostic for screenshots
    try:
        import os as _os
        _dbp = get_db_path('execution_tracker')
        logger.info(f"DATABASE-FIRST: save_screenshot_info DB file: {_dbp} (exists={_os.path.exists(_dbp)})")
    except Exception as _e:
        logger.error(f"DATABASE-FIRST: save_screenshot_info unable to log DB path: {_e}")


    conn = sqlite3.connect(get_db_path('execution_tracker'))
    cursor = conn.cursor()

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    logger.info(f"timestamp: {timestamp}")

    # If action_id is not provided, try to get it from the execution_tracking table
    if not action_id:
        try:
            cursor.execute('''
            SELECT action_id FROM execution_tracking
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
            ''', (suite_id, test_idx, step_idx))
            row = cursor.fetchone()
            if row and row[0]:
                action_id = row[0]
                logger.info(f"Found action_id {action_id} in execution_tracking table for step {step_idx} in test {test_idx}")
        except Exception as e:
            logger.error(f"Error getting action_id from execution_tracking: {str(e)}")

    # If we still don't have an action_id, try to get it from the test_steps table
    if not action_id:
        try:
            cursor.execute('''
            SELECT action_id FROM test_steps
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
            ''', (suite_id, test_idx, step_idx))
            row = cursor.fetchone()
            if row and row[0]:
                action_id = row[0]
                logger.info(f"Found action_id {action_id} in test_steps table for step {step_idx} in test {test_idx}")
        except Exception as e:
            logger.error(f"Error getting action_id from test_steps: {str(e)}")

    # Create a standardized filename without timestamp
    standardized_filename = f"step_{test_idx}_{step_idx}.png"

    # If we have an action_id, use it for the filename
    if action_id:
        standardized_filename = f"{action_id}.png"
        logger.info(f"Using action_id {action_id} for screenshot filename")

    # Create a standardized path
    standardized_path = f"screenshots/{standardized_filename}"

    # Also create a standardized version of the screenshot file
    try:
        # Get the app's static screenshots directory
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        app_screenshots_dir = os.path.join(app_dir, 'app', 'static', 'screenshots')

        # Source path (with timestamp)
        source_path = None

        # Try different ways to find the source path
        if os.path.isabs(path) and os.path.exists(path):
            # Absolute path
            source_path = path
        elif path.startswith('screenshots/'):
            # Path relative to app's static directory
            potential_path = os.path.join(app_screenshots_dir, path.replace('screenshots/', ''))
            if os.path.exists(potential_path):
                source_path = potential_path
        elif os.path.exists(os.path.join(app_screenshots_dir, filename)):
            # Just the filename in app's static directory
            source_path = os.path.join(app_screenshots_dir, filename)
        elif os.path.exists(os.path.join(app_screenshots_dir, path)):
            # Path relative to app's static directory without 'screenshots/' prefix
            source_path = os.path.join(app_screenshots_dir, path)

        # If we still don't have a valid source path, try to find any file with test_idx and step_idx in the name
        if not source_path or not os.path.exists(source_path):
            pattern = f"*{test_idx}*{step_idx}*.png"
            matching_files = glob.glob(os.path.join(app_screenshots_dir, pattern))
            if matching_files:
                # Use the most recent file
                matching_files.sort(key=os.path.getmtime, reverse=True)
                source_path = matching_files[0]
                logger.info(f"Found matching screenshot: {source_path}")

        # If we still don't have a valid source path, use latest.png as fallback
        if not source_path or not os.path.exists(source_path):
            latest_path = os.path.join(app_screenshots_dir, 'latest.png')
            if os.path.exists(latest_path):
                source_path = latest_path
                logger.info(f"Using latest.png as fallback for step {test_idx}_{step_idx}")

        # Target path (standardized)
        target_path = os.path.join(app_screenshots_dir, standardized_filename)

        # Copy the screenshot to the standardized filename
        if source_path and os.path.exists(source_path):
            import shutil
            # Only copy if source and target are different files
            if source_path != target_path:
                logger.info(f"Copying from {source_path} to {target_path}")
                shutil.copy2(source_path, target_path)
                logger.info(f"Created standardized screenshot: {source_path} -> {target_path}")
            else:
                logger.info(f"Source and target are the same file, skipping copy: {source_path}")
        else:
            logger.warning(f"Could not find source screenshot for step {test_idx}_{step_idx}")
    except Exception as e:
        logger.error(f"Error creating standardized screenshot: {str(e)}")
        import traceback
        traceback.print_exc()

    # DATABASE-FIRST: Read screenshot file and convert to BLOB
    screenshot_blob = None
    screenshot_mime = 'image/png'

    if source_path and os.path.exists(source_path):
        try:
            with open(source_path, 'rb') as f:
                screenshot_blob = f.read()
            logger.info(f"✅ Read screenshot file as BLOB: {len(screenshot_blob)} bytes")
        except Exception as e:
            logger.error(f"Error reading screenshot file as BLOB: {e}")
    else:
        logger.warning(f"Screenshot file not found, BLOB will be NULL: {source_path}")

    # Get test_execution_id and test_case_id from execution_tracking
    test_execution_id = None
    test_case_id = None
    try:
        cursor.execute('''
            SELECT test_execution_id, test_case_id FROM execution_tracking
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
            ORDER BY id DESC LIMIT 1
        ''', (suite_id, test_idx, step_idx))
        tracking_row = cursor.fetchone()
        if tracking_row:
            test_execution_id = tracking_row[0]
            test_case_id = tracking_row[1]
            logger.info(f"Found test_execution_id={test_execution_id}, test_case_id={test_case_id}")
    except Exception as e:
        logger.warning(f"Could not get test_execution_id/test_case_id: {e}")

    # Check if record already exists for this suite_id, test_idx, and step_idx
    cursor.execute('''
    SELECT id FROM screenshots
    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
    ''', (suite_id, test_idx, step_idx))

    existing = cursor.fetchone()

    if existing:
        # Update existing record (DATABASE-FIRST: include BLOB and execution IDs)
        logger.info(f"🔵 UPDATING existing screenshot record with ID: {existing[0]}")
        logger.info(f"   Values: filename={standardized_filename}, path={standardized_path}, action_id={action_id}")
        logger.info(f"   BLOB size: {len(screenshot_blob) if screenshot_blob else 0} bytes")
        cursor.execute('''
        UPDATE screenshots
        SET filename = ?, path = ?, timestamp = ?, action_id = ?,
            test_execution_id = ?, test_case_id = ?,
            screenshot_blob = ?, screenshot_mime = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (standardized_filename, standardized_path, timestamp, action_id,
              test_execution_id, test_case_id, screenshot_blob, screenshot_mime,
              suite_id, test_idx, step_idx))
        logger.info(f"✅ UPDATE SUCCESSFUL (with BLOB)")
    else:
        # Insert new record (DATABASE-FIRST: include BLOB and execution IDs)
        logger.info(f"🔵 INSERTING new screenshot record")
        logger.info(f"   Values: suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}")
        logger.info(f"   filename={standardized_filename}, path={standardized_path}, action_id={action_id}")
        logger.info(f"   BLOB size: {len(screenshot_blob) if screenshot_blob else 0} bytes")
        cursor.execute('''
        INSERT INTO screenshots (suite_id, test_idx, step_idx, filename, path, timestamp, action_id,
                                 test_execution_id, test_case_id, screenshot_blob, screenshot_mime)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (suite_id, test_idx, step_idx, standardized_filename, standardized_path, timestamp, action_id,
              test_execution_id, test_case_id, screenshot_blob, screenshot_mime))
        logger.info(f"✅ INSERT SUCCESSFUL (with BLOB)")

    # Also update the test_steps table with the standardized path if there's a matching step
    cursor.execute('''
    UPDATE test_steps
    SET screenshot_path = ?, action_id = ?
    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
    ''', (standardized_path, action_id, suite_id, test_idx, step_idx))

    steps_updated = cursor.rowcount
    logger.info(f"Updated {steps_updated} test step records with screenshot path and action_id")

    # Commit changes and close connection
    try:
        logger.info(f"🔵 COMMITTING screenshot changes to database...")
        conn.commit()
        logger.info(f"✅ COMMIT SUCCESSFUL - Screenshot saved to database")
    except Exception as commit_error:
        logger.error(f"❌ ERROR committing screenshot changes: {str(commit_error)}")
        import traceback
        logger.error(f"❌ Traceback:\n{traceback.format_exc()}")
        conn.rollback()
        conn.close()
        logger.error("=" * 80)
        return False

    conn.close()
    logger.info(f"✅ Database connection closed")
    logger.info(f"✅ save_screenshot_info() COMPLETED SUCCESSFULLY")
    logger.info(f"   suite_id: {suite_id}, test_idx: {test_idx}, step_idx: {step_idx}")
    logger.info(f"   filename: {standardized_filename}, path: {standardized_path}, action_id: {action_id}")
    logger.info("=" * 80)
    return True

def update_test_suite(suite_id, test_suite_data):
    """
    Update test suite data in the database

    Args:
        suite_id (str): Test suite ID
        test_suite_data (dict): Test suite data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update test suite
        cursor.execute('''
        UPDATE test_suites
        SET name = ?, status = ?, timestamp = ?
        WHERE suite_id = ?
        ''', (
            test_suite_data.get('name', ''),
            test_suite_data.get('status', 'updated'),
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            suite_id
        ))

        # If report_dir is provided, update it
        if 'report_dir' in test_suite_data:
            cursor.execute('''
            UPDATE test_suites
            SET report_dir = ?
            WHERE suite_id = ?
            ''', (
                test_suite_data.get('report_dir', ''),
                suite_id
            ))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error updating test suite in database: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_all_test_suites():
    """
    Get all test suites from the database

    Returns:
        list: List of test suite summary data
    """
    conn = sqlite3.connect(get_db_path('test_suites'))
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('''
    SELECT id, suite_id, name, status, passed, failed, skipped, timestamp, report_dir
    FROM test_suites
    ORDER BY id DESC
    ''')

    suites = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return suites

def delete_report_by_name(report_name):
    """
    Delete a report from the database by name

    Args:
        report_name (str): Name of the report to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Find the suite_id for the report
        cursor.execute("SELECT suite_id FROM test_suites WHERE name = ?", (report_name,))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"No report found with name: {report_name}")
            conn.close()
            return False

        suite_id = result[0]
        logger.info(f"Found report with suite_id: {suite_id}")

        # Delete all related data
        cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
        screenshots_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
        steps_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
        cases_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
        suites_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Deleted report data: {screenshots_deleted} screenshots, {steps_deleted} steps, {cases_deleted} cases, {suites_deleted} suites")
        return True
    except Exception as e:
        logger.error(f"Error deleting report by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def delete_report_by_dir(dir_name):
    """
    Delete a report from the database by directory name

    Args:
        dir_name (str): Directory name of the report to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path('test_suites'))
        cursor = conn.cursor()

        # Find the suite_id for the report
        cursor.execute("SELECT suite_id FROM test_suites WHERE report_dir LIKE ?", (f"%{dir_name}%",))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"No report found with directory: {dir_name}")
            conn.close()
            return False

        suite_id = result[0]
        logger.info(f"Found report with suite_id: {suite_id}")

        # Delete all related data
        cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
        screenshots_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
        steps_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
        cases_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
        suites_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Deleted report data: {screenshots_deleted} screenshots, {steps_deleted} steps, {cases_deleted} cases, {suites_deleted} suites")
        return True
    except Exception as e:
        logger.error(f"Error deleting report by directory: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_all_reports():
    """
    Get all reports from the database, including both test suites with report directories
    and execution tracking data for test executions without proper report directories.
    If no reports are found, create virtual reports from test suites.

    Returns:
        list: List of report data
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        reports = []

        # First, get all test suites with report data (existing logic)
        cursor.execute('''
        SELECT
            ts.suite_id,
            ts.name AS suite_name,
            ts.status,
            ts.passed,
            ts.failed,
            ts.skipped,
            ts.timestamp,
            ts.report_dir,
            COUNT(tc.id) AS test_case_count
        FROM
            test_suites ts
        LEFT JOIN
            test_cases tc ON ts.suite_id = tc.suite_id
        WHERE
            ts.report_dir IS NOT NULL AND ts.report_dir != ''
        GROUP BY
            ts.suite_id
        ORDER BY
            ts.timestamp DESC
        ''')

        suite_ids_with_reports = set()
        for row in cursor.fetchall():
            row_dict = dict(row)

            # Skip reports with invalid or missing report directories
            if not row_dict.get('report_dir'):
                continue

            suite_ids_with_reports.add(row_dict['suite_id'])

            # Get the report directory name
            report_dir = row_dict['report_dir']
            dir_name = os.path.basename(report_dir)

            # Create the report URL
            report_url = f"/reports/{dir_name}/mainreport.html"

            # Check if there's a ZIP file
            from app_android.utils.directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
            zip_url = f"/api/reports/download_zip/{dir_name}.zip" if os.path.exists(zip_path) else None

            # Also check in the report directory itself
            if not os.path.exists(zip_path):
                report_parent_dir = os.path.dirname(report_dir)
                alt_zip_path = os.path.join(report_parent_dir, f"{dir_name}.zip")
                if os.path.exists(alt_zip_path):
                    zip_path = alt_zip_path
                    zip_url = f"/api/reports/download_zip/{dir_name}.zip"

            # Build the report data structure
            reports.append({
                'filename': dir_name,
                'suite_name': row_dict['suite_name'],
                'status': row_dict['status'] or 'Unknown',
                'passed': row_dict['passed'] or 0,
                'failed': row_dict['failed'] or 0,
                'skipped': row_dict['skipped'] or 0,
                'creation_time': row_dict['timestamp'],
                'report_id': dir_name,
                'url': report_url,
                'zip_url': zip_url,
                'test_case_count': row_dict['test_case_count'] or 0,
                'allure_report_url': None  # Currently not supported, could be added in the future
            })

        # Second, get execution tracking data for test executions that don't have proper report directories
        cursor.execute('''
        SELECT DISTINCT
            et.suite_id,
            et.test_execution_id,
            ts.name AS suite_name,
            MIN(et.start_time) AS start_time,
            MAX(et.end_time) AS end_time,
            COUNT(DISTINCT et.test_case_id) AS test_case_count,
            SUM(CASE WHEN et.status = 'passed' THEN 1 ELSE 0 END) AS passed_actions,
            SUM(CASE WHEN et.status = 'failed' THEN 1 ELSE 0 END) AS failed_actions
        FROM
            execution_tracking et
        LEFT JOIN
            test_suites ts ON et.suite_id = ts.suite_id
        WHERE
            et.test_execution_id IS NOT NULL
            AND et.test_execution_id != ''
            AND (ts.suite_id IS NULL OR ts.report_dir IS NULL OR ts.report_dir = '')
        GROUP BY
            et.suite_id, et.test_execution_id
        ORDER BY
            MIN(et.start_time) DESC
        ''')

        for row in cursor.fetchall():
            row_dict = dict(row)

            # Skip if this suite already has a report
            if row_dict['suite_id'] in suite_ids_with_reports:
                continue

            execution_id = row_dict['test_execution_id']
            suite_name = row_dict['suite_name'] or f"Test Suite {row_dict['suite_id']}"

            # Determine overall status based on action results
            passed_actions = row_dict['passed_actions'] or 0
            failed_actions = row_dict['failed_actions'] or 0

            if failed_actions > 0:
                status = 'Failed'
            elif passed_actions > 0:
                status = 'Passed'
            else:
                status = 'Unknown'

            # Use execution_id as the report identifier
            reports.append({
                'filename': execution_id,
                'suite_name': suite_name,
                'status': status,
                'passed': passed_actions,
                'failed': failed_actions,
                'skipped': 0,
                'creation_time': row_dict['start_time'] or row_dict['end_time'],
                'report_id': execution_id,
                'url': None,  # No HTML report for execution tracking data
                'zip_url': None,  # No ZIP file for execution tracking data
                'test_case_count': row_dict['test_case_count'] or 0,
                'allure_report_url': None,
                'is_execution_tracking': True  # Flag to indicate this is from execution tracking
            })

        # If no reports found, create virtual reports from test suites (like iOS version)
        if not reports:
            logger.info("No reports found in database, checking for test suites")
            test_suites = get_all_test_suites()
            if test_suites:
                logger.info(f"Found {len(test_suites)} test suites, creating virtual reports")
                for suite in test_suites:
                    # Create a virtual report entry for each test suite
                    suite_id = suite.get('suite_id', 'unknown')
                    suite_name = suite.get('name', f'Test Suite {suite_id}')
                    timestamp = suite.get('timestamp') or datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    # Get test case count for this suite
                    cursor.execute('SELECT COUNT(*) FROM test_cases WHERE suite_id = ?', (suite_id,))
                    test_case_count = cursor.fetchone()[0] if cursor.fetchone() else 0

                    virtual_report = {
                        'filename': f'suite_execution_{suite_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                        'suite_name': suite_name,
                        'status': 'Available for Execution',
                        'passed': None,
                        'failed': None,
                        'skipped': None,
                        'creation_time': timestamp,
                        'report_id': f'suite_execution_{suite_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                        'url': None,
                        'zip_url': None,
                        'test_case_count': test_case_count,
                        'allure_report_url': None,
                        'suite_id': suite_id,
                        'is_virtual': True
                    }
                    reports.append(virtual_report)

        # Sort all reports by creation time (most recent first)
        reports.sort(key=lambda x: x['creation_time'] or '', reverse=True)

        conn.close()
        return reports

    except Exception as e:
        logger.error(f"Error getting reports: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def reset_database():
    """
    Reset the database by dropping and recreating all tables
    Preserves the execution_tracking table for retry tracking
    Preserves the screenshots table to maintain history between retries

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== RESETTING DATABASE ===")

        # Check if database file exists
        if not os.path.exists(get_db_path('test_suites')):
            logger.warning(f"Database file does not exist: {get_db_path('test_suites')}")
            # Create the database file and initialize it
            init_db()
            logger.info("Database initialized - it was empty to begin with")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Begin transaction
        conn.execute('BEGIN TRANSACTION')

        try:
            # Save execution tracking data before dropping tables
            cursor.execute('SELECT * FROM execution_tracking')
            execution_tracking_data = cursor.fetchall()
            logger.info(f"Saved {len(execution_tracking_data)} execution tracking records before reset")

            # Save screenshots data before reset - this is critical for retry functionality
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count = cursor.fetchone()[0]
            logger.info(f"Preserving {screenshots_count} screenshots during database reset")

            # Drop all tables except execution_tracking and screenshots
            cursor.execute('DROP TABLE IF EXISTS test_suites')
            cursor.execute('DROP TABLE IF EXISTS test_cases')
            cursor.execute('DROP TABLE IF EXISTS test_steps')
            # Explicitly preserving screenshots table - DO NOT DROP
            # cursor.execute('DROP TABLE IF EXISTS screenshots')
            logger.info("Dropped all tables except screenshots and execution_tracking")

            # Recreate all tables
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                name TEXT,
                status TEXT,
                passed INTEGER,
                failed INTEGER,
                skipped INTEGER,
                timestamp TEXT,
                report_dir TEXT,
                error TEXT
            )
            ''')

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                name TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                error TEXT
            )
            ''')

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                name TEXT,
                action_type TEXT,
                action_id TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                screenshot_path TEXT,
                error TEXT
            )
            ''')

            # Create test_case_json_backups table for JSON editor functionality
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_case_json_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case_filename TEXT NOT NULL,
                test_case_id TEXT,
                json_data BLOB NOT NULL,
                backup_timestamp TEXT NOT NULL,
                session_id TEXT,
                created_by TEXT DEFAULT 'json_editor'
            )
            ''')

            # Ensure screenshots table exists but don't recreate it (to preserve data)
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='screenshots'")
            if not cursor.fetchone():
                logger.info("Screenshots table doesn't exist, creating it")
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS screenshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    test_idx INTEGER,
                    step_idx INTEGER,
                    filename TEXT,
                    path TEXT,
                    timestamp TEXT,
                    action_id TEXT
                )
                ''')
            else:
                logger.info("Screenshots table exists, preserving it for retry functionality")

            # Check if execution_tracking table exists, create it if not
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
            if not cursor.fetchone():
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    test_idx INTEGER,
                    step_idx INTEGER,
                    filename TEXT,
                    action_type TEXT,
                    action_params TEXT,
                    action_id TEXT,
                    status TEXT,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 0,
                    last_error TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    in_progress BOOLEAN DEFAULT 0
                )
                ''')
                logger.info("Created execution_tracking table")

            # Commit transaction
            conn.commit()
            logger.info("Database reset successful - tables reset while preserving screenshots")

            # Verify tables are empty except screenshots
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM execution_tracking')
            tracking_count = cursor.fetchone()[0]

            logger.info(f"After reset - Database contains: {suites_count} suites, {cases_count} cases, {steps_count} steps, {screenshots_count} screenshots, {tracking_count} tracking entries")
            logger.info(f"Screenshots preserved: {screenshots_count}")

            # Verify reset was successful
            reset_successful = (suites_count == 0 and cases_count == 0 and steps_count == 0)
            logger.info(f"Database reset successful: {reset_successful}")
            return reset_successful

        except Exception as e:
            # Rollback transaction if there's an error
            conn.rollback()
            logger.error(f"Error during database reset: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Close connection
            conn.close()
            logger.info("Database connection closed after reset operation")

    except Exception as e:
        logger.error(f"Error in reset_database: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_database_state():
    """
    Check the current state of the database and return a summary

    Returns:
        dict: Summary of database state
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Get counts from all tables
        cursor.execute('SELECT COUNT(*) FROM test_suites')
        suites_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM test_cases')
        cases_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM test_steps')
        steps_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM screenshots')
        screenshots_count = cursor.fetchone()[0]

        # Check if execution_tracking table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
        if cursor.fetchone():
            cursor.execute('SELECT COUNT(*) FROM execution_tracking')
            tracking_count = cursor.fetchone()[0]
        else:
            tracking_count = 0

        # Get sample data if available (tolerate schema differences across DB versions)
        sample_suites = []
        if suites_count > 0:
            try:
                cursor.execute('SELECT suite_id, name, status, timestamp FROM test_suites LIMIT 3')
                sample_suites = cursor.fetchall()
            except sqlite3.OperationalError:
                # Fallback if timestamp column does not exist
                try:
                    cursor.execute('SELECT suite_id, name, status FROM test_suites LIMIT 3')
                    rows = cursor.fetchall()
                    # Pad to 4-tuple for consistent logging shape
                    sample_suites = [(r[0], r[1], r[2], None) for r in rows]
                except Exception:
                    sample_suites = []

        conn.close()

        state = {
            'suites_count': suites_count,
            'cases_count': cases_count,
            'steps_count': steps_count,
            'screenshots_count': screenshots_count,
            'tracking_count': tracking_count,
            'sample_suites': sample_suites
        }

        logger.info(f"Database state: {suites_count} suites, {cases_count} cases, {steps_count} steps, {screenshots_count} screenshots, {tracking_count} tracking entries")
        if sample_suites:
            logger.info(f"Sample suites: {sample_suites}")

        return state
    except Exception as e:
        logger.error(f"Error checking database state: {str(e)}")
        return {
            'error': str(e),
            'suites_count': -1,
            'cases_count': -1,
            'steps_count': -1,
            'screenshots_count': -1,
            'tracking_count': -1,
            'sample_suites': []
        }



# Initialize the database when the module is imported
init_db()

def get_test_case_by_id(test_case_id):
    """
    Get a test case by its ID

    Args:
        test_case_id (str): Test case ID

    Returns:
        dict: Test case data including actions
    """
    try:
        # Check if the test_case_id contains a timestamp (format: Name_YYYYMMDD_HHMMSS.json)
        # If it does, extract the base name
        base_name = test_case_id
        if '_20' in test_case_id and test_case_id.endswith('.json'):
            # Extract the base name (everything before the timestamp)
            parts = test_case_id.split('_')
            timestamp_index = -1
            for i, part in enumerate(parts):
                if part.startswith('20') and len(part) >= 8:  # Looks like a timestamp starting with 20xx
                    timestamp_index = i
                    break

            if timestamp_index > 0:
                base_name = '_'.join(parts[:timestamp_index])
                logger.info(f"Extracted base name '{base_name}' from test case ID: {test_case_id}")

        # First, check if we have a test case file with the exact ID
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        test_cases_dir = os.path.join(app_dir, 'test_cases')
        test_case_file = os.path.join(test_cases_dir, test_case_id)

        # If the test case ID doesn't end with .json, add it
        if not test_case_file.endswith('.json'):
            test_case_file += '.json'

        logger.info(f"Looking for test case file: {test_case_file}")

        if os.path.exists(test_case_file):
            # Load the test case from the file
            with open(test_case_file, 'r') as f:
                test_case = json.load(f)
                logger.info(f"Loaded test case {test_case_id} from file")
                return test_case

        # If the exact file doesn't exist, try to find a file with the base name
        if base_name != test_case_id:
            base_file = os.path.join(test_cases_dir, f"{base_name}.json")
            logger.info(f"Looking for test case with base name: {base_file}")

            if os.path.exists(base_file):
                # Load the test case from the file
                with open(base_file, 'r') as f:
                    test_case = json.load(f)
                    logger.info(f"Loaded test case {base_name} from file (using base name)")
                    return test_case

        # If still not found, try to find any file that starts with the base name
        matching_files = [f for f in os.listdir(test_cases_dir) if f.startswith(f"{base_name}_") and f.endswith('.json')]
        if matching_files:
            # Sort by modification time (newest first)
            matching_files.sort(key=lambda f: os.path.getmtime(os.path.join(test_cases_dir, f)), reverse=True)
            latest_file = os.path.join(test_cases_dir, matching_files[0])
            logger.info(f"Found matching test case file: {latest_file}")

            # Load the test case from the file
            with open(latest_file, 'r') as f:
                test_case = json.load(f)
                logger.info(f"Loaded test case from matching file: {matching_files[0]}")
                return test_case

        # If no file exists, try to load from the database
        conn = sqlite3.connect(get_db_path('test_suites'))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the test case from the database
        cursor.execute('''
        SELECT tc.*, ts.name as suite_name
        FROM test_cases tc
        JOIN test_suites ts ON tc.suite_id = ts.suite_id
        WHERE tc.id = ?
        ''', (test_case_id,))

        test_case_row = cursor.fetchone()

        if not test_case_row:
            # Try with the base name
            if base_name != test_case_id:
                cursor.execute('''
                SELECT tc.*, ts.name as suite_name
                FROM test_cases tc
                JOIN test_suites ts ON tc.suite_id = ts.suite_id
                WHERE tc.name LIKE ?
                ''', (f"{base_name}%",))

                test_case_row = cursor.fetchone()

            if not test_case_row:
                logger.warning(f"Test case not found: {test_case_id}")
                conn.close()
                return None

        # Convert to dict
        test_case = dict(test_case_row)

        # Get the actions for this test case
        # For now, we'll use a placeholder since we don't store actions in the database
        # In a real implementation, you would load the actions from a file or another table
        test_case['actions'] = []

        conn.close()
        logger.info(f"Loaded test case {test_case_id} from database")
        return test_case
    except Exception as e:
        logger.error(f"Error getting test case by ID: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# Check initial database state
logger.info("Checking initial database state...")
check_database_state()

def save_screenshot_info_DUPLICATE(suite_id, test_idx, step_idx, filename, path, retry_number=None, action_id=None, custom_screenshot_name=None, custom_screenshot_filename=None, custom_screenshot_path=None):
    """
    DUPLICATE FUNCTION - This should be removed or merged with the other save_screenshot_info
    Save screenshot information to the database (and keep reports folder naming behavior).
    """
    logger.warning("⚠️ DUPLICATE save_screenshot_info function called - this should be merged with the primary function")
    try:
        if filename == "latest.png":
            logger.debug("Skipping latest.png processing")
            return True

        if retry_number is not None:
            filename = f"retry{retry_number}_{filename}"
            if path:
                if '/' in path:
                    path = path.rsplit('/', 1)[0] + '/' + filename
                else:
                    path = filename

        if action_id and not filename.startswith(str(action_id)):
            filename = f"{action_id}.png"
            if path:
                if '/' in path:
                    path = path.rsplit('/', 1)[0] + '/' + filename
                else:
                    path = filename

        import sqlite3, datetime
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("PRAGMA table_info(screenshots)")
        cols = [row[1] for row in cur.fetchall()]

        ts = datetime.datetime.utcnow().isoformat()

        updated = False
        if action_id:
            try:
                cur.execute('SELECT id FROM screenshots WHERE action_id = ?', (action_id,))
                row = cur.fetchone()
                if row:
                    set_parts = []
                    params = []
                    for col, val in [('filename', filename), ('path', path), ('timestamp', ts), ('suite_id', suite_id), ('test_idx', test_idx), ('step_idx', step_idx), ('custom_screenshot_name', custom_screenshot_name), ('custom_screenshot_filename', custom_screenshot_filename), ('custom_screenshot_path', custom_screenshot_path)]:
                        if col in cols:
                            set_parts.append(f"{col} = ?")
                            params.append(val)
                    params.append(action_id)
                    cur.execute(f"UPDATE screenshots SET {', '.join(set_parts)} WHERE action_id = ?", params)
                    updated = True
            except Exception:
                pass

        if not updated:
            insert_cols = []
            values = []
            def add(col, val):
                if col in cols:
                    insert_cols.append(col); values.append(val)
            add('suite_id', suite_id)
            add('test_idx', test_idx)
            add('step_idx', step_idx)
            add('filename', filename)
            add('path', path)
            if 'timestamp' in cols:
                add('timestamp', ts)
            add('action_id', action_id)
            add('custom_screenshot_name', custom_screenshot_name)
            add('custom_screenshot_filename', custom_screenshot_filename)
            add('custom_screenshot_path', custom_screenshot_path)

            placeholders = ','.join(['?'] * len(values))
            sql = f"INSERT INTO screenshots ({', '.join(insert_cols)}) VALUES ({placeholders})"
            cur.execute(sql, values)

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error saving screenshot info to DB: {str(e)}")
        try:
            conn.rollback(); conn.close()
        except Exception:
            pass
        return False


def check_screenshot_exists(action_id):
    """
    Check if a screenshot with the given action_id already exists in the database

    Args:
        action_id (str): The action_id to check

    Returns:
        bool: True if a screenshot with this action_id exists, False otherwise
    """
    if not action_id:
        return False

    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if a record with this action_id exists
        cursor.execute('SELECT id FROM screenshots WHERE action_id = ?', (action_id,))
        result = cursor.fetchone()

        conn.close()
        return result is not None
    except Exception as e:
        logger.error(f"Error checking if screenshot exists: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id=None):
    """
    Update the action_type field in the test_steps table

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        action_type (str): Action type
        action_id (str, optional): Unique action ID for the action

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if the step exists
        cursor.execute(
            'SELECT id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        existing = cursor.fetchone()

        if existing:
            # Update existing step
            if action_id:
                cursor.execute('''
                UPDATE test_steps
                SET action_type = ?, action_id = ?
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (
                    action_type,
                    action_id,
                    suite_id,
                    test_idx,
                    step_idx
                ))
                logger.info(f"Updated action_type and action_id for step {test_idx}_{step_idx} to {action_type}, {action_id}")
            else:
                cursor.execute('''
                UPDATE test_steps
                SET action_type = ?
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (
                    action_type,
                    suite_id,
                    test_idx,
                    step_idx
                ))
                logger.info(f"Updated action_type for step {test_idx}_{step_idx} to {action_type}")
        else:
            # Step doesn't exist, log a warning
            logger.warning(f"Step {test_idx}_{step_idx} not found in test_steps table, cannot update action_type")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error updating action_type in test_steps table: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_action_id(suite_id, test_idx, step_idx, action_id):
    """
    Update the action_id field in the test_steps and screenshots tables

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        action_id (str): Unique action ID for the action

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update test_steps table
        cursor.execute('''
        UPDATE test_steps
        SET action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (
            action_id,
            suite_id,
            test_idx,
            step_idx
        ))
        steps_updated = cursor.rowcount
        logger.info(f"Updated action_id for {steps_updated} steps in test_steps table")

        # Update screenshots table
        cursor.execute('''
        UPDATE screenshots
        SET action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (
            action_id,
            suite_id,
            test_idx,
            step_idx
        ))
        screenshots_updated = cursor.rowcount
        logger.info(f"Updated action_id for {screenshots_updated} screenshots in screenshots table")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error updating action_id: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def clear_screenshots():
    """
    Clear screenshots table in the database

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        import sqlite3
        import traceback

        logger.info("Clearing screenshots from database...")

        # Connect to the database
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Delete all records from the screenshots table
        cursor.execute("DELETE FROM screenshots")
        screenshots_deleted = cursor.rowcount

        # Also clear any standardized_screenshots table if it exists
        try:
            cursor.execute("DELETE FROM standardized_screenshots")
            std_screenshots_deleted = cursor.rowcount
        except sqlite3.OperationalError:
            std_screenshots_deleted = 0
            logger.info("standardized_screenshots table does not exist")

        # Commit and close
        conn.commit()
        conn.close()

        # Vacuum the database to reclaim space
        conn = sqlite3.connect(get_db_path())
        conn.execute("PRAGMA foreign_keys = OFF")
        conn.execute("VACUUM")
        conn.execute("PRAGMA foreign_keys = ON")
        conn.close()

        logger.info(f"Successfully cleared {screenshots_deleted} screenshots and {std_screenshots_deleted} standardized screenshots")
        return True

    except Exception as e:
        logger.error(f"Error in clear_screenshots: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def save_environment_variable(name, value, description=None):
    """
    Save or update an environment variable in the database

    Args:
        name (str): Variable name
        value (str): Variable value
        description (str, optional): Variable description

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Use INSERT OR REPLACE to handle both new and existing variables
        cursor.execute("""
            INSERT OR REPLACE INTO environment_variables (name, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        """, (name, value, description))

        conn.commit()
        conn.close()

        logger.info(f"Environment variable '{name}' saved successfully")
        return True

    except Exception as e:
        logger.error(f"Error saving environment variable '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def get_environment_variable(name):
    """
    Get an environment variable from the database

    Args:
        name (str): Variable name

    Returns:
        dict: Variable data or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path('environments'))
        cursor = conn.cursor()

        cursor.execute("""
            SELECT name, value, description, created_at, updated_at
            FROM environment_variables
            WHERE name = ?
        """, (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                'name': row[0],
                'value': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            }
        return None

    except Exception as e:
        logger.error(f"Error getting environment variable '{name}': {str(e)}")
        return None


def get_all_environment_variables():
    """
    Get all environment variables from the database

    Returns:
        list: List of environment variable dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT name, value, description, created_at, updated_at
            FROM environment_variables
            ORDER BY name
        """)

        rows = cursor.fetchall()
        conn.close()

        variables = []
        for row in rows:
            variables.append({
                'name': row[0],
                'value': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        return variables

    except Exception as e:
        logger.error(f"Error getting all environment variables: {str(e)}")
        return []


def delete_environment_variable(name):
    """
    Delete an environment variable from the database

    Args:
        name (str): Variable name

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("DELETE FROM environment_variables WHERE name = ?", (name,))

        conn.commit()
        conn.close()

        logger.info(f"Environment variable '{name}' deleted successfully")
        return True

    except Exception as e:
        logger.error(f"Error deleting environment variable '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_action_enabled_state(action_id, enabled):
    """
    Update the enabled state of an action in the database

    Args:
        action_id (str): Action ID
        enabled (bool): Whether the action is enabled

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update the enabled state in the actions table
        cursor.execute("""
            UPDATE actions SET enabled = ? WHERE action_id = ?
        """, (enabled, action_id))

        # If no rows were affected, the action might not exist yet
        if cursor.rowcount == 0:
            # Try to insert a new record (this might be needed for legacy actions)
            cursor.execute("""
                INSERT OR IGNORE INTO actions (action_id, enabled) VALUES (?, ?)
            """, (action_id, enabled))

        conn.commit()
        conn.close()

        logger.info(f"Action '{action_id}' enabled state updated to {enabled}")
        return True

    except Exception as e:
        logger.error(f"Error updating action enabled state for '{action_id}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


# Environment Management Functions
def get_environments():
    """
    Get all environments from the database

    Returns:
        list: List of environment dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, is_active, created_at, updated_at
            FROM env_environments
            ORDER BY name
        """)

        environments = []
        for row in cursor.fetchall():
            environments.append({
                'id': row[0],
                'name': row[1],
                'is_active': bool(row[2]),
                'created_at': row[3],
                'updated_at': row[4]
            })

        conn.close()
        return environments

    except Exception as e:
        logger.error(f"Error getting environments: {str(e)}")
        return []


def get_variables(environment_id):
    """
    Get all variables for a specific environment

    Args:
        environment_id (int): Environment ID

    Returns:
        list: List of variable dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, initial_value, current_value, type, created_at, updated_at
            FROM environment_variables
            WHERE environment_id = ?
            ORDER BY name
        """, (environment_id,))

        variables = []
        for row in cursor.fetchall():
            variables.append({
                'id': row[0],
                'name': row[1],
                'initial_value': row[2] or '',
                'current_value': row[3] or '',
                'type': row[4] or 'default',
                'created_at': row[5],
                'updated_at': row[6]
            })

        conn.close()
        return variables

    except Exception as e:
        logger.error(f"Error getting variables for environment {environment_id}: {str(e)}")
        return []


def create_environment(name):
    """
    Create a new environment

    Args:
        name (str): Environment name

    Returns:
        int: Environment ID if successful, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO env_environments (name, is_active, created_at, updated_at)
            VALUES (?, 0, datetime('now'), datetime('now'))
        """, (name,))

        environment_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"Environment '{name}' created with ID {environment_id}")
        return environment_id

    except Exception as e:
        logger.error(f"Error creating environment '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return None


def create_variable(environment_id, name, initial_value, current_value=None, var_type='default'):
    """
    Create a new environment variable

    Args:
        environment_id (int): Environment ID
        name (str): Variable name
        initial_value (str): Initial value
        current_value (str): Current value (defaults to initial_value)
        var_type (str): Variable type

    Returns:
        int: Variable ID if successful, None otherwise
    """
    try:
        if current_value is None:
            current_value = initial_value

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if variable already exists for this environment
        cursor.execute("""
            SELECT id FROM environment_variables
            WHERE environment_id = ? AND name = ?
        """, (environment_id, name))
        existing = cursor.fetchone()

        if existing:
            # Update existing variable
            cursor.execute("""
                UPDATE environment_variables
                SET current_value = ?, updated_at = datetime('now')
                WHERE environment_id = ? AND name = ?
            """, (current_value, environment_id, name))
            variable_id = existing[0]
            logger.info(f"Variable '{name}' updated for environment {environment_id}")
        else:
            # Create new variable
            cursor.execute("""
                INSERT INTO environment_variables
                (environment_id, name, initial_value, current_value, type, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (environment_id, name, initial_value, current_value, var_type))

            variable_id = cursor.lastrowid
            logger.info(f"Variable '{name}' created for environment {environment_id}")

        conn.commit()
        conn.close()

        return variable_id

    except Exception as e:
        logger.error(f"Error creating variable '{name}' for environment {environment_id}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return None

# ============================================================================
# TEST CASE AND TEST SUITE METADATA FUNCTIONS
# ============================================================================

def get_test_case_metadata(test_case_id):
    """
    Get test case metadata by ID

    Args:
        test_case_id (str): Test case ID

    Returns:
        dict: Test case metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_case_metadata WHERE id = ?
        ''', (test_case_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test case metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_case_metadata_by_name(name):
    """
    Get test case metadata by name

    Args:
        name (str): Test case name

    Returns:
        dict: Test case metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_case_metadata WHERE name = ?
        ''', (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test case metadata by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_all_test_cases_metadata():
    """
    Get all test case metadata

    Returns:
        list: List of test case metadata dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_case_metadata ORDER BY updated_date DESC
        ''')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    except Exception as e:
        logger.error(f"Error getting all test cases metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def upsert_test_case_metadata(test_case_id, name, description='', file_path='', device_id='', action_count=0, labels=None):
    """
    Insert or update test case metadata

    Args:
        test_case_id (str): Test case ID
        name (str): Test case name
        description (str): Test case description
        file_path (str): File path
        device_id (str): Device ID
        action_count (int): Number of actions
        labels (list): List of labels

    Returns:
        bool: Success status
    """
    try:
        import json
        from datetime import datetime

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        labels_json = json.dumps(labels or [])
        current_time = datetime.now().isoformat()

        # Check if record exists
        cursor.execute('SELECT id FROM test_case_metadata WHERE id = ?', (test_case_id,))
        existing = cursor.fetchone()

        if existing:
            # Update existing record
            cursor.execute('''
                UPDATE test_case_metadata
                SET name = ?, description = ?, updated_date = ?, device_id = ?,
                    action_count = ?, labels = ?
                WHERE id = ?
            ''', (name, description, current_time, device_id, action_count, labels_json, test_case_id))
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO test_case_metadata
                (id, name, description, file_path, created_date, updated_date, device_id, action_count, labels)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (test_case_id, name, description, file_path, current_time, current_time, device_id, action_count, labels_json))

        conn.commit()
        conn.close()

        logger.info(f"Upserted test case metadata: {test_case_id}")
        return True

    except Exception as e:
        logger.error(f"Error upserting test case metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def delete_test_case_metadata(test_case_id):
    """
    Delete test case metadata

    Args:
        test_case_id (str): Test case ID

    Returns:
        bool: Success status
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute('DELETE FROM test_case_metadata WHERE id = ?', (test_case_id,))

        conn.commit()
        conn.close()

        logger.info(f"Deleted test case metadata: {test_case_id}")
        return True

    except Exception as e:
        logger.error(f"Error deleting test case metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

# Test Suite Metadata Functions

def get_test_suite_metadata(suite_id):
    """
    Get test suite metadata by ID

    Args:
        suite_id (str): Test suite ID

    Returns:
        dict: Test suite metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_suite_metadata WHERE id = ?
        ''', (suite_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test suite metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_suite_metadata_by_name(name):
    """
    Get test suite metadata by name

    Args:
        name (str): Test suite name

    Returns:
        dict: Test suite metadata or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_suite_metadata WHERE name = ?
        ''', (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return dict(row)
        return None

    except Exception as e:
        logger.error(f"Error getting test suite metadata by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_all_test_suites_metadata():
    """
    Get all test suite metadata

    Returns:
        list: List of test suite metadata dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM test_suite_metadata ORDER BY updated_date DESC
        ''')

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    except Exception as e:
        logger.error(f"Error getting all test suites metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def upsert_test_suite_metadata(suite_id, name, description='', file_path='', test_case_files=None):
    """
    Insert or update test suite metadata

    Args:
        suite_id (str): Test suite ID
        name (str): Test suite name
        description (str): Test suite description
        file_path (str): File path
        test_case_files (list): List of test case files

    Returns:
        bool: Success status
    """
    try:
        import json
        from datetime import datetime

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        test_case_files_json = json.dumps(test_case_files or [])
        test_case_count = len(test_case_files or [])
        current_time = datetime.now().isoformat()

        # Check if record exists
        cursor.execute('SELECT id FROM test_suite_metadata WHERE id = ?', (suite_id,))
        existing = cursor.fetchone()

        if existing:
            # Update existing record
            cursor.execute('''
                UPDATE test_suite_metadata
                SET name = ?, description = ?, updated_date = ?, test_case_count = ?, test_case_files = ?
                WHERE id = ?
            ''', (name, description, current_time, test_case_count, test_case_files_json, suite_id))
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO test_suite_metadata
                (id, name, description, file_path, created_date, updated_date, test_case_count, test_case_files)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (suite_id, name, description, file_path, current_time, current_time, test_case_count, test_case_files_json))

        conn.commit()
        conn.close()

        logger.info(f"Upserted test suite metadata: {suite_id}")
        return True

    except Exception as e:
        logger.error(f"Error upserting test suite metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def delete_test_suite_metadata(suite_id):
    """
    Delete test suite metadata

    Args:
        suite_id (str): Test suite ID

    Returns:
        bool: Success status
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute('DELETE FROM test_suite_metadata WHERE id = ?', (suite_id,))

        conn.commit()
        conn.close()

        logger.info(f"Deleted test suite metadata: {suite_id}")
        return True

    except Exception as e:
        logger.error(f"Error deleting test suite metadata: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def get_execution_setting(setting_key, default_value=None):
    """
    Get execution setting value by key

    Args:
        setting_key (str): Setting key to retrieve
        default_value (str): Default value if setting not found

    Returns:
        str: Setting value or default value
    """
    conn = None
    try:
        conn = sqlite3.connect(get_db_path('settings'))
        cursor = conn.cursor()

        cursor.execute('SELECT setting_value FROM execution_settings WHERE setting_name = ?', (setting_key,))
        result = cursor.fetchone()

        if result:
            return result[0]
        return default_value

    except Exception as e:
        logger.error(f"Error getting execution setting {setting_key}: {e}")
        return default_value
    finally:
        if conn:
            conn.close()

def set_execution_setting(setting_key, setting_value, description=None):
    """
    Set execution setting value

    Args:
        setting_key (str): Setting key
        setting_value (str): Setting value
        description (str): Optional description

    Returns:
        bool: True if successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Use INSERT OR REPLACE to update existing or create new
        cursor.execute('''
        INSERT OR REPLACE INTO execution_settings (setting_name, setting_value, description, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ''', (setting_key, setting_value, description))

        conn.commit()
        logger.info(f"Set execution setting {setting_key} = {setting_value}")
        return True

    except Exception as e:
        logger.error(f"Error setting execution setting {setting_key}: {e}")
        return False
    finally:
        if conn:
            conn.close()

# --- Screenshot BLOB helpers ---
from typing import Optional, Tuple

def set_execution_step_screenshot_blob(suite_id: str, test_idx: int, step_idx: Optional[int], action_id: Optional[str], blob_bytes: bytes, thumb_bytes: Optional[bytes] = None, mime: str = 'image/png') -> bool:
    """
    Store screenshot bytes (and optional thumbnail) for a specific execution step.
    Matches by (suite_id, test_idx, step_idx) if provided, otherwise by action_id.
    """
    conn = None
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Prefer matching by action_id if provided and unique
        if action_id:
            cursor.execute(
                """
                UPDATE execution_tracking
                   SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                 WHERE action_id = ? AND (suite_id = ? OR ? IS NULL OR ? = '')
                """,
                (blob_bytes, thumb_bytes, mime, action_id, suite_id, suite_id, suite_id),
            )
            if cursor.rowcount == 0 and suite_id is not None:
                # Fallback without suite constraint if no rows updated
                cursor.execute(
                    """
                    UPDATE execution_tracking
                       SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                     WHERE action_id = ?
                    """,
                    (blob_bytes, thumb_bytes, mime, action_id),
                )
        else:
            # Match by composite key
            cursor.execute(
                """
                UPDATE execution_tracking
                   SET screenshot_blob = ?, screenshot_thumb_blob = ?, screenshot_mime = ?
                 WHERE suite_id = ? AND test_idx = ? AND (step_idx = ? OR (? IS NULL AND step_idx IS NULL))
                """,
                (blob_bytes, thumb_bytes, mime, suite_id, int(test_idx or 0), step_idx, step_idx),
            )

        conn.commit()
        return True
    except Exception as e:
        logger.error(f"Error setting screenshot blob: {e}")
        return False
    finally:
        if conn:
            conn.close()

def get_execution_step_screenshot_blob(suite_id: str, test_idx: int, step_idx: Optional[int]) -> Optional[Tuple[bytes, Optional[bytes], str]]:
    """Return (full_bytes, thumb_bytes, mime) for a specific step, or None."""
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
              FROM execution_tracking
             WHERE suite_id = ? AND test_idx = ? AND (step_idx = ? OR (? IS NULL AND step_idx IS NULL))
             ORDER BY id DESC LIMIT 1
            """,
            (suite_id, int(test_idx or 0), step_idx, step_idx),
        )
        row = cursor.fetchone()
        conn.close()
        if row and row[0]:
            return row[0], row[1], row[2]
        return None
    except Exception as e:
        logger.error(f"Error getting screenshot blob: {e}")
        return None

def get_execution_step_screenshot_by_action(action_id: str, suite_id: Optional[str] = None) -> Optional[Tuple[bytes, Optional[bytes], str]]:
    """Return (full_bytes, thumb_bytes, mime) by action_id (optionally constrained by suite_id)."""
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        if suite_id:
            cursor.execute(
                """
                SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                  FROM execution_tracking
                 WHERE action_id = ? AND suite_id = ?
                 ORDER BY id DESC LIMIT 1
                """,
                (action_id, suite_id),
            )
        else:
            cursor.execute(
                """
                SELECT screenshot_blob, screenshot_thumb_blob, COALESCE(screenshot_mime, 'image/png')
                  FROM execution_tracking
                 WHERE action_id = ?
                 ORDER BY id DESC LIMIT 1
                """,
                (action_id,),
            )
        row = cursor.fetchone()
        conn.close()
        if row and row[0]:
            return row[0], row[1], row[2]
        return None
    except Exception as e:
        logger.error(f"Error getting screenshot blob by action: {e}")
        return None

def get_video_recording_settings():
    """
    Get video recording settings from database

    Returns:
        dict: Video recording settings or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Create video_recording_settings table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_recording_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            video_quality TEXT,
            video_size TEXT,
            bit_rate INTEGER,
            video_fps INTEGER,
            time_limit INTEGER,
            video_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Get current platform (default to 'android' for this app)
        platform = 'android'

        cursor.execute('''
        SELECT video_quality, video_size, bit_rate, video_fps, time_limit, video_type
        FROM video_recording_settings
        WHERE platform = ?
        ORDER BY updated_at DESC
        LIMIT 1
        ''', (platform,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                'video_quality': result[0],
                'video_size': result[1],
                'bit_rate': result[2],
                'video_fps': result[3],
                'time_limit': result[4],
                'video_type': result[5]
            }

        return None

    except Exception as e:
        logger.error(f"Error getting video recording settings: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def save_video_recording_settings(platform, settings):
    """
    Save video recording settings to database

    Args:
        platform (str): Platform (ios/android)
        settings (dict): Video recording settings

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Create table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_recording_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            video_quality TEXT,
            video_size TEXT,
            bit_rate INTEGER,
            video_fps INTEGER,
            time_limit INTEGER,
            video_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Insert or update settings
        cursor.execute('''
        INSERT OR REPLACE INTO video_recording_settings
        (platform, video_quality, video_size, bit_rate, video_fps, time_limit, video_type, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            platform,
            settings.get('video_quality'),
            settings.get('video_size'),
            settings.get('bit_rate'),
            settings.get('video_fps'),
            settings.get('time_limit'),
            settings.get('video_type')
        ))

        conn.commit()
        conn.close()

        logger.info(f"Video recording settings saved for platform: {platform}")
        return True

    except Exception as e:
        logger.error(f"Error saving video recording settings: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_video_recording_settings():
    """
    Get video recording settings from database

    Returns:
        dict: Video recording settings or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Create video_recording_settings table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_recording_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            video_quality TEXT,
            video_size TEXT,
            bit_rate INTEGER,
            video_fps INTEGER,
            time_limit INTEGER,
            video_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Get current platform (default to 'android' for this app)
        platform = 'android'

        cursor.execute('''
        SELECT video_quality, video_size, bit_rate, video_fps, time_limit, video_type
        FROM video_recording_settings
        WHERE platform = ?
        ORDER BY updated_at DESC
        LIMIT 1
        ''', (platform,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                'video_quality': result[0],
                'video_size': result[1],
                'bit_rate': result[2],
                'video_fps': result[3],
                'time_limit': result[4],
                'video_type': result[5]
            }

        return None

    except Exception as e:
        logger.error(f"Error getting video recording settings: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def save_video_recording_settings(platform, settings):
    """
    Save video recording settings to database

    Args:
        platform (str): Platform (ios/android)
        settings (dict): Video recording settings

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Create table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_recording_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            video_quality TEXT,
            video_size TEXT,
            bit_rate INTEGER,
            video_fps INTEGER,
            time_limit INTEGER,
            video_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Insert or update settings
        cursor.execute('''
        INSERT OR REPLACE INTO video_recording_settings
        (platform, video_quality, video_size, bit_rate, video_fps, time_limit, video_type, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            platform,
            settings.get('video_quality'),
            settings.get('video_size'),
            settings.get('bit_rate'),
            settings.get('video_fps'),
            settings.get('time_limit'),
            settings.get('video_type')
        ))

        conn.commit()
        conn.close()

        logger.info(f"Video recording settings saved for platform: {platform}")
        return True

    except Exception as e:
        logger.error(f"Error saving video recording settings: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def create_test_execution_status_table():
    """
    Create the test_execution_status table if it doesn't exist
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_execution_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            execution_id TEXT UNIQUE NOT NULL,
            suite_id TEXT,
            status TEXT NOT NULL,
            total_tests INTEGER DEFAULT 0,
            passed_tests INTEGER DEFAULT 0,
            failed_tests INTEGER DEFAULT 0,
            start_time TEXT,
            end_time TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        conn.commit()
        conn.close()
        logger.info("Test execution status table created/verified")
        return True

    except Exception as e:
        logger.error(f"Error creating test execution status table: {e}")
        return False


def update_test_execution_status(execution_id, status, total_tests=None, passed_tests=None, failed_tests=None):
    """
    Update the overall test execution status

    Args:
        execution_id (str): Unique execution ID
        status (str): Overall execution status ('running', 'completed', 'failed')
        total_tests (int, optional): Total number of tests
        passed_tests (int, optional): Number of passed tests
        failed_tests (int, optional): Number of failed tests

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure the table exists
        create_test_execution_status_table()

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Check if record exists
        cursor.execute('SELECT id FROM test_execution_status WHERE execution_id = ?', (execution_id,))
        existing = cursor.fetchone()

        if existing:
            # Update existing record
            update_fields = ['status = ?', 'updated_at = ?']
            update_values = [status, timestamp]

            if total_tests is not None:
                update_fields.append('total_tests = ?')
                update_values.append(total_tests)
            if passed_tests is not None:
                update_fields.append('passed_tests = ?')
                update_values.append(passed_tests)
            if failed_tests is not None:
                update_fields.append('failed_tests = ?')
                update_values.append(failed_tests)
            if status in ['completed', 'failed']:
                update_fields.append('end_time = ?')
                update_values.append(timestamp)

            update_values.append(execution_id)  # For WHERE clause

            query = f'UPDATE test_execution_status SET {", ".join(update_fields)} WHERE execution_id = ?'
            cursor.execute(query, update_values)

            logger.info(f"Updated test execution status for {execution_id}: {status}")
        else:
            # Insert new record
            cursor.execute('''
            INSERT INTO test_execution_status
            (execution_id, status, total_tests, passed_tests, failed_tests, start_time, end_time, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                execution_id, status,
                total_tests or 0, passed_tests or 0, failed_tests or 0,
                timestamp if status == 'running' else None,
                timestamp if status in ['completed', 'failed'] else None,
                timestamp, timestamp
            ))

            logger.info(f"Created test execution status for {execution_id}: {status}")

        conn.commit()
        conn.close()
        return True

    except Exception as e:
        logger.error(f"Error updating test execution status: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def get_test_execution_status(execution_id):
    """
    Get the current test execution status

    Args:
        execution_id (str): Unique execution ID

    Returns:
        dict: Execution status information or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute('''
        SELECT execution_id, suite_id, status, total_tests, passed_tests, failed_tests,
               start_time, end_time, created_at, updated_at
        FROM test_execution_status
        WHERE execution_id = ?
        ''', (execution_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                'execution_id': result[0],
                'suite_id': result[1],
                'status': result[2],
                'total_tests': result[3],
                'passed_tests': result[4],
                'failed_tests': result[5],
                'start_time': result[6],
                'end_time': result[7],
                'created_at': result[8],
                'updated_at': result[9]
            }
        else:
            return None

    except Exception as e:
        logger.error(f"Error getting test execution status: {e}")
        return None


def get_execution_tracking_data(suite_id):
    """
    Get execution tracking data for a specific suite ID

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of execution tracking records
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT * FROM execution_tracking
        WHERE suite_id = ?
        ORDER BY test_idx, step_idx, retry_count DESC
        ''', (suite_id,))

        rows = cursor.fetchall()
        conn.close()

        # Convert rows to dictionaries
        execution_data = []
        for row in rows:
            execution_data.append(dict(row))

        logger.info(f"Retrieved {len(execution_data)} execution tracking records for suite {suite_id}")
        return execution_data

    except Exception as e:
        logger.error(f"Error getting execution tracking data: {e}")
        return []


# --- Execution Reports helpers ---

def upsert_execution_report(execution_id: str, suite_id: str, data_json, status: str | None = None) -> bool:
    """Create or update an execution_reports row (Android).

    - execution_id: UUID used in execution_tracking.test_execution_id
    - suite_id: the suite UUID used in execution_tracking.suite_id
    - data_json: dict/list/bytes/str representing data.json content
    - status: optional overall status (passed/failed/running/...)
    """
    try:
        import json as _json
        if isinstance(data_json, (dict, list)):
            data_bytes = _json.dumps(data_json).encode('utf-8')
        elif isinstance(data_json, (bytes, bytearray)):
            data_bytes = bytes(data_json)
        else:
            data_bytes = str(data_json).encode('utf-8')

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO execution_reports (execution_id, suite_id, data_json, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON CONFLICT(execution_id) DO UPDATE SET
                suite_id = excluded.suite_id,
                data_json = excluded.data_json,
                status = COALESCE(execution_reports.status, excluded.status),
                updated_at = CURRENT_TIMESTAMP
            """,
            (execution_id, suite_id, data_bytes, status)
        )
        conn.commit()
        conn.close()
        logger.info(f"upsert_execution_report (android): persisted execution_id={execution_id}, suite_id={suite_id}, status={status if status else 'n/a'}, bytes={len(data_bytes)}")
        return True
    except Exception as e:
        logger.error(f"upsert_execution_report (android) error: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def save_execution_report_snapshot(execution_id: str, suite_id: str, status: str | None = None) -> bool:
    """Build a minimal data.json from DB and persist it into execution_reports (Android)."""
    try:
        # Resolve suite_id properly
        resolved_suite_id = resolve_execution_id_to_suite_id(suite_id or execution_id)
        from .build_data_json import build_data_json_from_execution_tracker

        tracking_rows = get_execution_tracking_for_suite(resolved_suite_id)
        # Derive a conservative overall status if not provided
        overall = status
        if overall is None:
            any_failed = any((r.get('status') or '').lower() == 'failed' for r in tracking_rows)
            overall = 'failed' if any_failed else 'passed'

        data = build_data_json_from_execution_tracker(resolved_suite_id, tracking_rows)
        if not data:
            data = {
                'execution_id': execution_id,
                'suite_id': resolved_suite_id,
                'status': overall,
                'generated_from': 'database',
            }
        else:
            data['execution_id'] = data.get('execution_id') or execution_id
            data['suite_id'] = data.get('suite_id') or resolved_suite_id
            data['status'] = data.get('status') or overall

        return upsert_execution_report(execution_id, resolved_suite_id, data, overall)
    except Exception as e:
        logger.error(f"save_execution_report_snapshot (android) error: {e}")
        return False


# --- Reference Images (BLOB) helpers ---
def ensure_reference_images_table():
    """Create the reference_images table if it doesn't exist (Android)."""
    try:
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute(
            """
            CREATE TABLE IF NOT EXISTS reference_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                image BLOB NOT NULL,
                created_at TEXT DEFAULT (datetime('now'))
            )
            """
        )
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Failed to ensure reference_images table: {e}")
        return False


def reference_image_exists(name: str) -> bool:
    """Return True if a reference image with this name exists (Android, supports legacy schema)."""
    if not name:
        return False
    try:
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("PRAGMA table_info(reference_images)")
        cols = [row[1].lower() for row in cur.fetchall()]
        name_col = 'name' if 'name' in cols else ('image_name' if 'image_name' in cols else 'name')
        cur.execute(f"SELECT 1 FROM reference_images WHERE {name_col} = ? LIMIT 1", (name,))
        exists = cur.fetchone() is not None
        conn.close()
        return exists
    except Exception as e:
        logger.error(f"Failed checking reference image existence: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def save_reference_image(name: str, image_bytes: bytes) -> bool:
    """Save or replace a reference image BLOB by name (Android, supports legacy schema)."""
    if not name or not image_bytes:
        return False
    try:
        ensure_reference_images_table()
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("PRAGMA table_info(reference_images)")
        cols = [row[1].lower() for row in cur.fetchall()]
        name_col = 'name' if 'name' in cols else ('image_name' if 'image_name' in cols else 'name')
        image_col = 'image' if 'image' in cols else ('image_data' if 'image_data' in cols else 'image')

        columns = [name_col, image_col]
        params = [name, sqlite3.Binary(image_bytes)]

        if 'image_format' in cols:
            columns.append('image_format')
            params.append('png')
        if 'file_size' in cols:
            columns.append('file_size')
            params.append(len(image_bytes))
        if 'checksum' in cols:
            import hashlib
            columns.append('checksum')
            params.append(hashlib.md5(image_bytes).hexdigest())
        if 'created_at' in cols:
            columns.append('created_at')
            placeholders = ','.join(['?'] * (len(params))) + ", datetime('now')"
        else:
            placeholders = ','.join(['?'] * (len(params)))

        cols_sql = ', '.join(columns)
        sql = f"INSERT OR REPLACE INTO reference_images ({cols_sql}) VALUES ({placeholders})"
        cur.execute(sql, params)
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Failed to save reference image '{name}' to DB: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def list_reference_image_names_sorted():
    """Return list of reference image names sorted alphabetically (Android), supporting legacy schemas."""
    try:
        ensure_reference_images_table()
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        # Detect available column name for image name
        cur.execute("PRAGMA table_info(reference_images)")
        cols = [row[1].lower() for row in cur.fetchall()]
        name_col = 'name' if 'name' in cols else ('image_name' if 'image_name' in cols else 'name')
        cur.execute(f"SELECT {name_col} FROM reference_images ORDER BY LOWER({name_col}) ASC")
        rows = cur.fetchall()
        conn.close()
        return [r[0] for r in rows]
    except Exception as e:
        logger.error(f"Failed to list reference image names: {e}")
        return []


def save_test_run_data(unique_execution_id, test_case_name, action_id, step_status, testcase_status,
                       suite_status, testcase_retry, multistep, suite_id, test_idx, step_idx,
                       action_type=None, action_params=None, error=None):
    """
    Save test run data to execution_tracking table

    This function is called during test suite execution to track individual steps.
    It wraps track_test_execution() with the appropriate parameters.

    Args:
        unique_execution_id: Unique execution ID for this test run
        test_case_name: Name of the test case
        action_id: Unique action identifier
        step_status: Status of the step ('pass', 'fail', 'skip', 'running')
        testcase_status: Status of the test case ('pass', 'fail', 'skip', 'running')
        suite_status: Status of the suite ('pass', 'fail', 'running')
        testcase_retry: Whether this is a retry attempt
        multistep: Whether this is a multi-step action
        suite_id: Test suite ID
        test_idx: Test case index
        step_idx: Step index
        action_type: Type of action being executed (optional)
        action_params: Action parameters (optional)
        error: Error message if step failed (optional)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"save_test_run_data called: suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}")
        logger.info(f"  test_case_name={test_case_name}, action_id={action_id}")
        logger.info(f"  step_status={step_status}, testcase_status={testcase_status}")

        # Map status values to execution_tracking format
        status_map = {
            'pass': 'passed',
            'fail': 'failed',
            'skip': 'skipped',
            'running': 'running',
            'started': 'started'
        }

        # Convert status to execution_tracking format
        mapped_status = status_map.get(step_status, step_status)

        # Determine if execution is in progress
        in_progress = (mapped_status in ['running', 'started'])

        # Call track_test_execution with proper parameters
        result = track_test_execution(
            suite_id=suite_id,
            test_idx=test_idx,
            filename=test_case_name,
            status=mapped_status,
            retry_count=1 if testcase_retry else 0,
            max_retries=0,
            error=error,
            in_progress=in_progress,
            step_idx=step_idx,
            action_type=action_type or 'test_suite_step',
            action_params=action_params,
            action_id=action_id,
            test_case_id=test_case_name,
            test_execution_id=unique_execution_id
        )

        if result:
            logger.info(f"✅ Successfully saved test run data for {test_case_name} step {step_idx}")
        else:
            logger.warning(f"⚠️ Failed to save test run data for {test_case_name} step {step_idx}")

        try:
            execution_result_payload = None
            if mapped_status in ('passed', 'failed', 'skipped', 'running'):
                execution_result_payload = {
                    'step_status': step_status,
                    'testcase_status': testcase_status,
                    'suite_status': suite_status,
                    'success': mapped_status == 'passed',
                }

            ensure_execution_tracking_record(
                execution_id=unique_execution_id,
                suite_id=suite_id,
                test_case_id=test_case_name,
                test_idx=test_idx,
                step_idx=step_idx,
                action_id=action_id,
                status=mapped_status,
                filename=test_case_name,
                action_type=action_type or 'test_suite_step',
                action_params=action_params,
                error=error,
                execution_result=execution_result_payload,
                retry_count=1 if testcase_retry else 0,
                max_retries=0,
                screenshot_filename=None,
            )
        except Exception as ensure_error:
            logger.warning(f"⚠️ Could not verify execution_tracking entry for {unique_execution_id}: {ensure_error}")

        return result

    except Exception as e:
        logger.error(f"❌ Error in save_test_run_data: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def ensure_execution_tracking_record(
    execution_id,
    suite_id,
    test_case_id,
    test_idx,
    step_idx,
    action_id,
    status,
    filename,
    action_type=None,
    action_params=None,
    error=None,
    execution_result=None,
    retry_count=0,
    max_retries=0,
    screenshot_filename=None,
):
    """
    Ensure a row exists in execution_tracking for the given execution step.

    Returns:
        int or None: The row ID if verified/created, otherwise None.
    """
    if not execution_id:
        logger.debug("ensure_execution_tracking_record skipped - missing execution_id")
        return None

    normalized_test_idx = test_idx if isinstance(test_idx, int) else 0
    normalized_step_idx = step_idx if isinstance(step_idx, int) else 0

    query = None
    params = None

    if action_id:
        query = (
            "SELECT id FROM execution_tracking "
            "WHERE test_execution_id = ? AND action_id = ? "
            "ORDER BY id DESC LIMIT 1"
        )
        params = (execution_id, action_id)
    else:
        query = (
            "SELECT id FROM execution_tracking "
            "WHERE test_execution_id = ? AND suite_id = ? AND test_idx = ? AND step_idx = ? "
            "ORDER BY id DESC LIMIT 1"
        )
        params = (execution_id, suite_id, normalized_test_idx, normalized_step_idx)

    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(query, params)
        row = cursor.fetchone()
        conn.close()

        if row:
            return row[0]

        logger.warning(
            f"⚠️ Missing execution_tracking row detected for execution_id={execution_id}, "
            f"action_id={action_id}, step_idx={normalized_step_idx}. Attempting to insert fallback record."
        )

        fallback_status = status or 'running'
        fallback_in_progress = fallback_status in ('running', 'started')
        fallback_filename = filename or test_case_id or 'unknown'
        fallback_test_case_id = test_case_id or fallback_filename

        track_test_execution(
            suite_id=suite_id,
            test_idx=normalized_test_idx,
            step_idx=step_idx,
            filename=fallback_filename,
            status=fallback_status,
            retry_count=retry_count or 0,
            max_retries=max_retries or 0,
            error=error,
            in_progress=fallback_in_progress,
            action_type=action_type,
            action_params=action_params,
            action_id=action_id,
            execution_result=execution_result,
            test_case_id=fallback_test_case_id,
            test_execution_id=execution_id,
            screenshot_filename=screenshot_filename,
        )

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute(query, params)
        row = cursor.fetchone()
        conn.close()

        if row:
            logger.info(
                f"✅ Ensured execution_tracking row #{row[0]} for execution_id={execution_id}, "
                f"action_id={action_id or 'N/A'}, step_idx={normalized_step_idx}"
            )
            return row[0]

        logger.error(
            f"❌ Failed to ensure execution_tracking row for execution_id={execution_id}, "
            f"action_id={action_id}, step_idx={normalized_step_idx}"
        )
        return None

    except Exception as e:
        logger.error(f"Error ensuring execution_tracking record: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return None


def _resolve_screenshot_source_path(candidate_path, screenshot_filename, execution_id):
    """
    Try to resolve a filesystem path for a screenshot to seed the database entry.
    """
    if candidate_path and os.path.exists(candidate_path):
        return candidate_path

    if not screenshot_filename:
        return None

    potential_paths = []
    try:
        from pathlib import Path

        base_dir = Path(__file__).resolve().parents[2]
        reports_dir = base_dir / 'reports_android'

        if execution_id:
            potential_paths.append(reports_dir / execution_id / 'screenshots' / screenshot_filename)

        potential_paths.extend([
            reports_dir / 'screenshots' / screenshot_filename,
            base_dir / 'reports' / screenshot_filename,
            base_dir / 'reports' / execution_id / 'screenshots' / screenshot_filename if execution_id else None,
            base_dir / 'temp_android' / screenshot_filename,
            base_dir / 'custom_temp_android' / screenshot_filename,
            base_dir / 'failed_steps_metadata' / screenshot_filename,
        ])

        glob_patterns = [
            str(reports_dir / f"testsuite_execution_*" / 'screenshots' / screenshot_filename),
            str(reports_dir / f"suite_execution_*" / 'screenshots' / screenshot_filename),
        ]

        for pattern in glob_patterns:
            for match in glob.glob(pattern):
                if os.path.exists(match):
                    return match

        for path in potential_paths:
            if path and os.path.exists(path):
                return str(path)
    except Exception as resolve_error:
        logger.debug(f"Screenshot source resolution failed: {resolve_error}")

    return None


def ensure_screenshot_record(
    execution_id,
    suite_id,
    test_case_id,
    action_id,
    screenshot_filename,
    screenshot_path=None,
):
    """
    Ensure a screenshot row exists in the screenshots table for the given step.

    Returns:
        bool: True if a record exists or was created, False otherwise.
    """
    if not execution_id or not screenshot_filename:
        logger.debug("ensure_screenshot_record skipped - missing execution_id or screenshot_filename")
        return False

    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("PRAGMA table_info(screenshots)")
        columns = [col[1] for col in cursor.fetchall()]

        cursor.execute(
            "SELECT id FROM screenshots WHERE filename = ? ORDER BY timestamp DESC LIMIT 1",
            (screenshot_filename,),
        )
        existing = cursor.fetchone()
        conn.close()

        if existing:
            return True

        source_path = _resolve_screenshot_source_path(screenshot_path, screenshot_filename, execution_id)
        if not source_path or not os.path.exists(source_path):
            logger.warning(
                f"⚠️ Unable to locate screenshot file '{screenshot_filename}' for execution {execution_id}. "
                "Database entry not created."
            )
            return False

        try:
            with open(source_path, 'rb') as fp:
                screenshot_bytes = fp.read()
        except Exception as file_err:
            logger.error(f"Error reading screenshot file '{source_path}': {file_err}")
            return False

        try:
            from app_android.utils.screenshot_manager_db import ScreenshotManagerDB
        except Exception as import_err:
            logger.error(f"Unable to import ScreenshotManagerDB: {import_err}")
            return False

        manager = ScreenshotManagerDB(get_db_path())
        success = manager.save_screenshot_to_db(
            screenshot_data=screenshot_bytes,
            filename=screenshot_filename,
            execution_id=execution_id,
            test_case_id=test_case_id,
            action_id=action_id,
            original_size=len(screenshot_bytes),
            compressed_size=len(screenshot_bytes),
            suite_id=suite_id,
        )

        if success:
            logger.info(
                f"✅ Persisted screenshot '{screenshot_filename}' for execution {execution_id} into screenshots table"
            )
        else:
            logger.error(
                f"❌ Failed to persist screenshot '{screenshot_filename}' for execution {execution_id}"
            )

        return success

    except Exception as e:
        logger.error(f"Error ensuring screenshot record: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False


def ensure_execution_report_record(
    execution_id,
    suite_id=None,
    report_data=None,
    status=None,
    platform='Android',
    test_case_id=None,
    start_time=None,
    end_time=None,
    duration=None,
    error_message=None,
):
    """
    Ensure an execution_reports row exists with populated report_data.

    Returns:
        bool: True if ensured successfully, False otherwise.
    """
    if not execution_id:
        logger.debug("ensure_execution_report_record skipped - missing execution_id")
        return False

    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(execution_reports)")
        columns = [col[1] for col in cursor.fetchall()]

        cursor.execute(
            "SELECT rowid, start_time FROM execution_reports WHERE test_execution_id = ? LIMIT 1",
            (execution_id,),
        )
        row = cursor.fetchone()

        if not start_time:
            start_time = datetime.now().isoformat()
        if not end_time:
            end_time = datetime.now().isoformat()

        computed_duration = None
        if duration is not None:
            computed_duration = duration
        else:
            try:
                start_dt = datetime.fromisoformat(start_time)
                end_dt = datetime.fromisoformat(end_time)
                computed_duration = max(int((end_dt - start_dt).total_seconds()), 0)
            except Exception:
                computed_duration = 0

        if not report_data:
            try:
                from app_android.utils.database_execution_tracker import DatabaseExecutionTracker

                tracker = DatabaseExecutionTracker(get_db_path())
                tracking_conn = sqlite3.connect(get_db_path())
                tracking_conn.row_factory = sqlite3.Row
                tracking_cursor = tracking_conn.cursor()
                tracking_cursor.execute(
                    "SELECT * FROM execution_tracking WHERE test_execution_id = ? ORDER BY step_idx ASC",
                    (execution_id,),
                )
                steps = [dict(item) for item in tracking_cursor.fetchall()]
                tracking_conn.close()

                if steps:
                    report_data = tracker._build_report_data(execution_id, steps)
            except Exception as build_err:
                logger.warning(f"Could not build fallback report_data for {execution_id}: {build_err}")

        serialized_report_data = None
        if report_data is not None:
            try:
                serialized_report_data = json.dumps(report_data)
            except Exception as json_err:
                logger.error(f"Failed to serialize report_data for {execution_id}: {json_err}")
                serialized_report_data = None

        if row:
            updates = []
            params = []

            if status and 'status' in columns:
                updates.append("status = ?")
                params.append(status)
            if suite_id and 'suite_id' in columns:
                updates.append("suite_id = ?")
                params.append(suite_id)
            if test_case_id and 'test_case_id' in columns:
                updates.append("test_case_id = ?")
                params.append(test_case_id)
            if serialized_report_data and 'report_data' in columns:
                updates.append("report_data = ?")
                params.append(serialized_report_data)
            if 'end_time' in columns:
                updates.append("end_time = ?")
                params.append(end_time)
            if 'duration' in columns and computed_duration is not None:
                updates.append("duration = ?")
                params.append(computed_duration)
            if error_message and 'error_message' in columns:
                updates.append("error_message = ?")
                params.append(error_message)
            if 'platform' in columns and platform:
                updates.append("platform = ?")
                params.append(platform)

            if updates:
                params.append(execution_id)
                cursor.execute(
                    f"UPDATE execution_reports SET {', '.join(updates)} WHERE test_execution_id = ?",
                    tuple(params),
                )
                conn.commit()
                conn.close()
                return True

            conn.close()
            return True

        insert_columns = []
        placeholders = []
        values = []

        def _add(column_name, value):
            insert_columns.append(column_name)
            placeholders.append("?")
            values.append(value)

        if 'report_id' in columns:
            report_identifier = f"{execution_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            _add('report_id', report_identifier)

        _add('test_execution_id', execution_id)

        if 'suite_id' in columns:
            _add('suite_id', suite_id)
        if 'test_case_id' in columns:
            _add('test_case_id', test_case_id)
        if 'platform' in columns:
            _add('platform', platform)
        if 'status' in columns:
            _add('status', status or 'completed')
        if 'start_time' in columns:
            _add('start_time', start_time)
        if 'end_time' in columns:
            _add('end_time', end_time)
        if 'duration' in columns:
            _add('duration', computed_duration)
        if 'error_message' in columns:
            _add('error_message', error_message)
        if 'report_data' in columns and serialized_report_data:
            _add('report_data', serialized_report_data)
        if 'created_at' in columns:
            _add('created_at', datetime.now().isoformat())

        cursor.execute(
            f"INSERT INTO execution_reports ({', '.join(insert_columns)}) VALUES ({', '.join(placeholders)})",
            tuple(values),
        )
        conn.commit()
        conn.close()

        logger.info(f"✅ Created execution_reports entry for execution_id={execution_id}")
        return True

    except Exception as e:
        logger.error(f"Error ensuring execution report record: {e}")
        try:
            conn.close()
        except Exception:
            pass
        return False
