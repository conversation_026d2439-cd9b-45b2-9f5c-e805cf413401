"""
Comprehensive Report Service

This service handles report generation from database including:
- Generating HTML reports
- Generating PDF reports
- Generating JSON reports
- Reports for single execution, date range, suite, or all executions

Author: Mobile App Automation Tool
Date: 2025-01-07
"""

import sqlite3
import logging
import json
import base64
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)


class ComprehensiveReportService:
    """Service for generating comprehensive reports from database"""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the comprehensive report service
        
        Args:
            db_path: Path to database file (defaults to centralized Android db)
        """
        if db_path is None:
            base_dir = Path(__file__).resolve().parents[2]
            db_path = str(base_dir / 'db-data' / 'android.db')
        
        self.db_path = db_path
        logger.info(f"ComprehensiveReportService initialized with db: {db_path}")
    
    def generate_execution_report(
        self,
        execution_id: str,
        format: str = 'html'
    ) -> Optional[str]:
        """
        Generate report for a specific execution
        
        Args:
            execution_id: Execution identifier
            format: Report format ('html', 'json', 'pdf')
            
        Returns:
            str: Report content or None
        """
        try:
            # Get execution data
            execution_data = self._get_execution_data(execution_id)
            if not execution_data:
                logger.warning(f"No data found for execution {execution_id}")
                return None
            
            # Generate report based on format
            if format == 'html':
                return self._generate_html_report(execution_data)
            elif format == 'json':
                return json.dumps(execution_data, indent=2)
            elif format == 'pdf':
                # PDF generation would require additional library (e.g., weasyprint)
                # For now, return HTML that can be converted to PDF
                return self._generate_html_report(execution_data)
            else:
                logger.error(f"Unsupported format: {format}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating execution report: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _get_execution_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get complete execution data from database
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            dict: Complete execution data or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get execution summary
            cursor.execute('''
                SELECT * FROM execution_summary
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            summary_row = cursor.fetchone()
            if not summary_row:
                conn.close()
                return None
            
            execution_data = dict(summary_row)
            
            # Get test cases with steps
            cursor.execute('''
                SELECT DISTINCT test_case_id, filename
                FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY test_idx
            ''', (execution_id,))
            
            test_cases = []
            for row in cursor.fetchall():
                test_case_id, filename = row
                
                # Get steps for this test case
                cursor.execute('''
                    SELECT step_idx, action_id, action_type, action_params,
                           status, start_time, end_time, last_error
                    FROM execution_tracking
                    WHERE test_execution_id = ? AND test_case_id = ?
                    ORDER BY step_idx
                ''', (execution_id, test_case_id))
                
                steps = []
                for step_row in cursor.fetchall():
                    step_dict = dict(zip(['step_idx', 'action_id', 'action_type', 'action_params',
                                          'status', 'start_time', 'end_time', 'last_error'], step_row))
                    
                    # Get screenshot for this step
                    cursor.execute('''
                        SELECT screenshot_blob, screenshot_mime
                        FROM screenshots
                        WHERE test_execution_id = ? AND action_id = ?
                        LIMIT 1
                    ''', (execution_id, step_dict['action_id']))
                    
                    screenshot_row = cursor.fetchone()
                    if screenshot_row and screenshot_row[0]:
                        # Convert BLOB to base64 for HTML embedding
                        screenshot_blob, screenshot_mime = screenshot_row
                        step_dict['screenshot'] = base64.b64encode(screenshot_blob).decode('utf-8')
                        step_dict['screenshot_mime'] = screenshot_mime or 'image/png'
                    
                    steps.append(step_dict)
                
                # Calculate test case status
                test_case_status = 'passed'
                if any(s['status'] == 'failed' for s in steps):
                    test_case_status = 'failed'
                elif any(s['status'] == 'skipped' for s in steps):
                    test_case_status = 'skipped'
                
                test_cases.append({
                    'test_case_id': test_case_id,
                    'filename': filename,
                    'status': test_case_status,
                    'steps': steps,
                    'total_steps': len(steps),
                    'passed_steps': sum(1 for s in steps if s['status'] == 'passed'),
                    'failed_steps': sum(1 for s in steps if s['status'] == 'failed'),
                    'skipped_steps': sum(1 for s in steps if s['status'] == 'skipped')
                })
            
            execution_data['test_cases'] = test_cases
            
            # Get failed tests
            cursor.execute('''
                SELECT * FROM failed_tests_registry
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            failed_tests = [dict(row) for row in cursor.fetchall()]
            execution_data['failed_tests'] = failed_tests
            
            conn.close()
            return execution_data
            
        except Exception as e:
            logger.error(f"Error getting execution data: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _generate_html_report(self, execution_data: Dict[str, Any]) -> str:
        """
        Generate HTML report from execution data
        
        Args:
            execution_data: Complete execution data
            
        Returns:
            str: HTML report content
        """
        try:
            # Extract data
            execution_id = execution_data.get('test_execution_id', 'Unknown')
            suite_name = execution_data.get('suite_name', 'Unknown Suite')
            platform = execution_data.get('platform', 'Unknown')
            status = execution_data.get('status', 'Unknown')
            start_time = execution_data.get('start_time', '')
            end_time = execution_data.get('end_time', '')
            duration = execution_data.get('duration_seconds', 0)
            
            total_test_cases = execution_data.get('total_test_cases', 0)
            passed_test_cases = execution_data.get('passed_test_cases', 0)
            failed_test_cases = execution_data.get('failed_test_cases', 0)
            
            total_steps = execution_data.get('total_steps', 0)
            passed_steps = execution_data.get('passed_steps', 0)
            failed_steps = execution_data.get('failed_steps', 0)
            
            test_cases = execution_data.get('test_cases', [])
            
            # Generate HTML
            html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Execution Report - {execution_id}</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
               background: #f5f5f5; padding: 20px; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; 
                     box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 30px; }}
        .header {{ border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        .header h1 {{ color: #333; font-size: 28px; margin-bottom: 10px; }}
        .header .meta {{ color: #666; font-size: 14px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
                   gap: 20px; margin-bottom: 30px; }}
        .stat-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                     color: white; padding: 20px; border-radius: 8px; }}
        .stat-card.passed {{ background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }}
        .stat-card.failed {{ background: linear-gradient(135deg, #eb3349 0%, #f45c43 100%); }}
        .stat-card h3 {{ font-size: 14px; opacity: 0.9; margin-bottom: 10px; }}
        .stat-card .number {{ font-size: 32px; font-weight: bold; }}
        .test-case {{ background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px; }}
        .test-case-header {{ display: flex; justify-content: space-between; align-items: center; 
                            margin-bottom: 15px; padding-bottom: 15px; border-bottom: 2px solid #dee2e6; }}
        .test-case-header h2 {{ font-size: 18px; color: #333; }}
        .status-badge {{ padding: 6px 12px; border-radius: 4px; font-size: 12px; font-weight: bold; }}
        .status-badge.passed {{ background: #28a745; color: white; }}
        .status-badge.failed {{ background: #dc3545; color: white; }}
        .status-badge.skipped {{ background: #ffc107; color: #333; }}
        .step {{ background: white; border-left: 4px solid #007bff; padding: 15px; margin-bottom: 10px; 
                border-radius: 4px; }}
        .step.failed {{ border-left-color: #dc3545; }}
        .step.passed {{ border-left-color: #28a745; }}
        .step-header {{ display: flex; justify-content: space-between; margin-bottom: 10px; }}
        .step-header strong {{ color: #333; }}
        .step-details {{ font-size: 14px; color: #666; margin-bottom: 10px; }}
        .error-message {{ background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; 
                         margin-top: 10px; font-size: 13px; }}
        .screenshot {{ max-width: 100%; height: auto; border-radius: 4px; margin-top: 10px; 
                      cursor: pointer; transition: transform 0.2s; }}
        .screenshot:hover {{ transform: scale(1.02); }}
        .modal {{ display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; 
                 height: 100%; background: rgba(0,0,0,0.9); }}
        .modal-content {{ margin: auto; display: block; max-width: 90%; max-height: 90%; 
                         margin-top: 50px; }}
        .close {{ position: absolute; top: 20px; right: 40px; color: white; font-size: 40px; 
                 font-weight: bold; cursor: pointer; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Execution Report</h1>
            <div class="meta">
                <strong>Execution ID:</strong> {execution_id}<br>
                <strong>Suite:</strong> {suite_name}<br>
                <strong>Platform:</strong> {platform.upper()}<br>
                <strong>Status:</strong> <span class="status-badge {status.lower()}">{status.upper()}</span><br>
                <strong>Start Time:</strong> {start_time}<br>
                <strong>End Time:</strong> {end_time}<br>
                <strong>Duration:</strong> {duration} seconds
            </div>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <h3>Total Test Cases</h3>
                <div class="number">{total_test_cases}</div>
            </div>
            <div class="stat-card passed">
                <h3>Passed Test Cases</h3>
                <div class="number">{passed_test_cases}</div>
            </div>
            <div class="stat-card failed">
                <h3>Failed Test Cases</h3>
                <div class="number">{failed_test_cases}</div>
            </div>
            <div class="stat-card">
                <h3>Total Steps</h3>
                <div class="number">{total_steps}</div>
            </div>
        </div>
'''
            
            # Add test cases
            for tc in test_cases:
                html += f'''
        <div class="test-case">
            <div class="test-case-header">
                <h2>{tc['filename']}</h2>
                <span class="status-badge {tc['status']}">{tc['status'].upper()}</span>
            </div>
            <div style="margin-bottom: 15px; color: #666; font-size: 14px;">
                Total Steps: {tc['total_steps']} | 
                Passed: {tc['passed_steps']} | 
                Failed: {tc['failed_steps']} | 
                Skipped: {tc['skipped_steps']}
            </div>
'''
                
                # Add steps
                for step in tc['steps']:
                    action_params = step.get('action_params', '')
                    if action_params:
                        try:
                            params_dict = json.loads(action_params)
                            params_str = ', '.join([f"{k}: {v}" for k, v in params_dict.items()])
                        except:
                            params_str = action_params
                    else:
                        params_str = ''
                    
                    html += f'''
            <div class="step {step['status']}">
                <div class="step-header">
                    <strong>Step {step['step_idx'] + 1}: {step['action_type']}</strong>
                    <span class="status-badge {step['status']}">{step['status'].upper()}</span>
                </div>
                <div class="step-details">
                    <strong>Action ID:</strong> {step['action_id']}<br>
                    {f"<strong>Parameters:</strong> {params_str}<br>" if params_str else ""}
                    <strong>Time:</strong> {step.get('start_time', 'N/A')} - {step.get('end_time', 'N/A')}
                </div>
'''
                    
                    if step.get('last_error'):
                        html += f'''
                <div class="error-message">
                    <strong>Error:</strong> {step['last_error']}
                </div>
'''
                    
                    if step.get('screenshot'):
                        html += f'''
                <img src="data:{step.get('screenshot_mime', 'image/png')};base64,{step['screenshot']}" 
                     class="screenshot" alt="Screenshot" onclick="openModal(this.src)">
'''
                    
                    html += '''
            </div>
'''
                
                html += '''
        </div>
'''
            
            # Close HTML
            html += '''
    </div>
    
    <div id="screenshotModal" class="modal" onclick="closeModal()">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(src) {
            document.getElementById('screenshotModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }
        
        function closeModal() {
            document.getElementById('screenshotModal').style.display = 'none';
        }
    </script>
</body>
</html>
'''
            
            return html

        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            import traceback
            traceback.print_exc()
            return None

    def generate_date_range_report(
        self,
        start_date: str,
        end_date: str,
        format: str = 'html'
    ) -> Optional[str]:
        """
        Generate report for executions within a date range

        Args:
            start_date: Start date (ISO format)
            end_date: End date (ISO format)
            format: Report format ('html', 'json')

        Returns:
            str: Report content or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get executions in date range
            cursor.execute('''
                SELECT * FROM execution_summary
                WHERE start_time >= ? AND start_time <= ?
                ORDER BY start_time DESC
            ''', (start_date, end_date))

            executions = [dict(row) for row in cursor.fetchall()]
            conn.close()

            if format == 'json':
                return json.dumps({
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_executions': len(executions),
                    'executions': executions
                }, indent=2)
            elif format == 'html':
                return self._generate_date_range_html(executions, start_date, end_date)

            return None

        except Exception as e:
            logger.error(f"Error generating date range report: {e}")
            return None

    def _generate_date_range_html(
        self,
        executions: List[Dict[str, Any]],
        start_date: str,
        end_date: str
    ) -> str:
        """Generate HTML report for date range"""

        total_executions = len(executions)
        total_passed = sum(1 for e in executions if e.get('status') == 'completed')
        total_failed = sum(1 for e in executions if e.get('status') == 'failed')

        html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Date Range Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }}
        .header {{ border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background: #007bff; color: white; }}
        .status-badge {{ padding: 4px 8px; border-radius: 4px; font-size: 12px; }}
        .status-badge.completed {{ background: #28a745; color: white; }}
        .status-badge.failed {{ background: #dc3545; color: white; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Execution Report - Date Range</h1>
            <p><strong>Period:</strong> {start_date} to {end_date}</p>
            <p><strong>Total Executions:</strong> {total_executions} |
               <strong>Passed:</strong> {total_passed} |
               <strong>Failed:</strong> {total_failed}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Execution ID</th>
                    <th>Suite Name</th>
                    <th>Platform</th>
                    <th>Status</th>
                    <th>Test Cases</th>
                    <th>Duration</th>
                    <th>Start Time</th>
                </tr>
            </thead>
            <tbody>
'''

        for exec in executions:
            html += f'''
                <tr>
                    <td>{exec.get('test_execution_id', 'N/A')}</td>
                    <td>{exec.get('suite_name', 'N/A')}</td>
                    <td>{exec.get('platform', 'N/A').upper()}</td>
                    <td><span class="status-badge {exec.get('status', '')}">{exec.get('status', 'N/A').upper()}</span></td>
                    <td>{exec.get('total_test_cases', 0)} ({exec.get('passed_test_cases', 0)}P / {exec.get('failed_test_cases', 0)}F)</td>
                    <td>{exec.get('duration_seconds', 0)}s</td>
                    <td>{exec.get('start_time', 'N/A')}</td>
                </tr>
'''

        html += '''
            </tbody>
        </table>
    </div>
</body>
</html>
'''

        return html

