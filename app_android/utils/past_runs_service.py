"""
Past Runs Service

This service handles retrieval of historical test execution data including:
- Listing all executions with filtering
- Getting execution details
- Getting execution logs
- Getting failed tests
- Searching executions

Author: Mobile App Automation Tool
Date: 2025-01-07
"""

import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)


class PastRunsService:
    """Service for retrieving historical test execution data"""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the past runs service
        
        Args:
            db_path: Path to database file (defaults to centralized Android db)
        """
        if db_path is None:
            base_dir = Path(__file__).resolve().parents[2]
            db_path = str(base_dir / 'db-data' / 'android.db')
        
        self.db_path = db_path
        logger.info(f"PastRunsService initialized with db: {db_path}")
    
    def get_all_executions(
        self,
        platform: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        suite_id: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get all executions with optional filtering
        
        Args:
            platform: Filter by platform ('ios' or 'android')
            status: Filter by status ('running', 'completed', 'failed', 'cancelled')
            start_date: Filter by start date (ISO format)
            end_date: Filter by end date (ISO format)
            suite_id: Filter by suite ID
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            list: List of execution dictionaries
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Build query with filters
            query = "SELECT * FROM execution_summary WHERE 1=1"
            params = []
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            if status:
                query += " AND status = ?"
                params.append(status)
            
            if start_date:
                query += " AND start_time >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND start_time <= ?"
                params.append(end_date)
            
            if suite_id:
                query += " AND suite_id = ?"
                params.append(suite_id)
            
            query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()
            
            executions = [dict(row) for row in rows]
            logger.info(f"Retrieved {len(executions)} executions")
            return executions
            
        except Exception as e:
            logger.error(f"Error getting executions: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def get_execution_details(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed execution data including summary and test cases
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            dict: Execution details or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get execution summary
            cursor.execute('''
                SELECT * FROM execution_summary
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            summary_row = cursor.fetchone()
            if not summary_row:
                conn.close()
                return None
            
            summary = dict(summary_row)
            
            # Get test cases with their steps
            cursor.execute('''
                SELECT DISTINCT test_case_id, filename
                FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY test_idx
            ''', (execution_id,))
            
            test_cases = []
            for row in cursor.fetchall():
                test_case_id, filename = row
                
                # Get steps for this test case
                cursor.execute('''
                    SELECT step_idx, action_id, action_type, status, 
                           start_time, end_time, last_error
                    FROM execution_tracking
                    WHERE test_execution_id = ? AND test_case_id = ?
                    ORDER BY step_idx
                ''', (execution_id, test_case_id))
                
                steps = [dict(zip(['step_idx', 'action_id', 'action_type', 'status', 
                                   'start_time', 'end_time', 'last_error'], step))
                        for step in cursor.fetchall()]
                
                test_cases.append({
                    'test_case_id': test_case_id,
                    'filename': filename,
                    'steps': steps,
                    'total_steps': len(steps),
                    'passed_steps': sum(1 for s in steps if s['status'] == 'passed'),
                    'failed_steps': sum(1 for s in steps if s['status'] == 'failed')
                })
            
            summary['test_cases'] = test_cases
            
            conn.close()
            return summary
            
        except Exception as e:
            logger.error(f"Error getting execution details: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_execution_logs(self, execution_id: str) -> List[Dict[str, Any]]:
        """
        Get step-by-step execution logs
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            list: List of execution log entries
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM execution_tracking
                WHERE test_execution_id = ?
                ORDER BY test_idx, step_idx
            ''', (execution_id,))
            
            rows = cursor.fetchall()
            conn.close()
            
            logs = [dict(row) for row in rows]
            logger.info(f"Retrieved {len(logs)} log entries for execution {execution_id}")
            return logs
            
        except Exception as e:
            logger.error(f"Error getting execution logs: {e}")
            return []
    
    def get_failed_tests(self, execution_id: str) -> List[Dict[str, Any]]:
        """
        Get all failed tests for an execution
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            list: List of failed test dictionaries
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM failed_tests_registry
                WHERE test_execution_id = ?
                ORDER BY failure_timestamp DESC
            ''', (execution_id,))
            
            rows = cursor.fetchall()
            conn.close()
            
            failed_tests = [dict(row) for row in rows]
            logger.info(f"Retrieved {len(failed_tests)} failed tests for execution {execution_id}")
            return failed_tests
            
        except Exception as e:
            logger.error(f"Error getting failed tests: {e}")
            return []
    
    def search_executions(
        self,
        search_term: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search executions by execution ID or suite name
        
        Args:
            search_term: Search term
            filters: Additional filters (platform, status, etc.)
            
        Returns:
            list: List of matching executions
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = '''
                SELECT * FROM execution_summary
                WHERE (test_execution_id LIKE ? OR suite_name LIKE ?)
            '''
            params = [f'%{search_term}%', f'%{search_term}%']
            
            if filters:
                if filters.get('platform'):
                    query += " AND platform = ?"
                    params.append(filters['platform'])
                
                if filters.get('status'):
                    query += " AND status = ?"
                    params.append(filters['status'])
            
            query += " ORDER BY created_at DESC LIMIT 50"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()
            
            results = [dict(row) for row in rows]
            logger.info(f"Search for '{search_term}' returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Error searching executions: {e}")
            return []
    
    def get_execution_statistics(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed statistics for an execution
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            dict: Statistics dictionary or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get summary
            cursor.execute('''
                SELECT * FROM execution_summary
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            summary = cursor.fetchone()
            if not summary:
                conn.close()
                return None
            
            stats = dict(summary)
            
            # Get failed tests count
            cursor.execute('''
                SELECT COUNT(*) FROM failed_tests_registry
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            stats['failed_tests_count'] = cursor.fetchone()[0]
            
            # Get screenshots count
            cursor.execute('''
                SELECT COUNT(*) FROM screenshots
                WHERE test_execution_id = ?
            ''', (execution_id,))
            
            stats['screenshots_count'] = cursor.fetchone()[0]
            
            conn.close()
            return stats
            
        except Exception as e:
            logger.error(f"Error getting execution statistics: {e}")
            return None

