import logging
import os
import sqlite3
from pathlib import Path
from shared_directory_paths_db import SharedDirectoryPathsDB

logger = logging.getLogger(__name__)


class DirectoryPathsDB(SharedDirectoryPathsDB):
    """Android directory path helper with graceful defaults."""

    def __init__(self):
        self.platform = 'android'
        self.platform_db = None
        self.has_centralized_db = False

        try:
            import sys
            root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            if root_dir not in sys.path:
                sys.path.insert(0, root_dir)
            try:
                from utils.platform_db_adapter import PlatformDBAdapter
            except Exception:
                utils_dir = os.path.join(root_dir, 'utils')
                if utils_dir not in sys.path:
                    sys.path.insert(0, utils_dir)
                from platform_db_adapter import PlatformDBAdapter
            self.platform_db = PlatformDBAdapter(platform='android')
            self.has_centralized_db = True
            logger.info("Android DirectoryPathsDB initialized with centralized database")
        except Exception as exc:
            logger.warning(f"Android DirectoryPathsDB fallback due to platform adapter error: {exc}")
            self.platform_db = None

    def get_path(self, name: str, default=None):
        if not self.has_centralized_db or not self.platform_db:
            return default
        try:
            conn = self.platform_db.get_connection('settings')
            cursor = conn.cursor()
            for key in (f"{name}_ANDROID", name):
                try:
                    cursor.execute(
                        """
                        SELECT setting_value FROM execution_settings
                        WHERE setting_name = ? AND (category = 'directory_path' OR category = 'directory_paths')
                        """,
                        (key,),
                    )
                except sqlite3.OperationalError:
                    conn.close()
                    return default
                row = cursor.fetchone()
                if row and row[0]:
                    conn.close()
                    return row[0]
            conn.close()
        except Exception as exc:
            logger.warning(f"Unable to fetch directory path for {name}: {exc}")
        return default

    def save_path(self, name: str, path: str) -> bool:
        if not self.has_centralized_db or not self.platform_db:
            return False
        try:
            conn = self.platform_db.get_connection('settings')
            cursor = conn.cursor()
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS execution_settings (
                    setting_name TEXT PRIMARY KEY,
                    setting_value TEXT,
                    description TEXT,
                    category TEXT,
                    is_default INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            for key in (f"{name}_ANDROID", name):
                cursor.execute(
                    """
                    INSERT INTO execution_settings (setting_name, setting_value, description, category, is_default, created_at, updated_at)
                    VALUES (?, ?, ?, 'directory_path', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ON CONFLICT(setting_name) DO UPDATE SET
                        setting_value=excluded.setting_value,
                        updated_at=CURRENT_TIMESTAMP
                    """,
                    (key, path, f"Directory path for {name}"),
                )
            conn.commit()
            conn.close()
            return True
        except sqlite3.OperationalError:
            logger.debug("execution_settings table unavailable; skipping directory save")
            return False
        except Exception as exc:
            logger.error(f"Error saving directory path {name}: {exc}")
            return False

    def save_all_paths(self, paths_dict):
        if not paths_dict:
            return False
        saved = True
        for key, value in paths_dict.items():
            saved = self.save_path(key, value) and saved
        return saved

    def get_all_environments(self):
        if not self.has_centralized_db or not self.platform_db:
            return []
        try:
            conn = self.platform_db.get_connection('environments')
            cursor = conn.cursor()
            try:
                cursor.execute(
                    """
                    SELECT environment_id, name, description, is_active, created_at, updated_at
                    FROM environments
                    WHERE platform = 'android'
                    ORDER BY name
                    """
                )
            except sqlite3.OperationalError:
                conn.close()
                return []
            rows = cursor.fetchall()
            conn.close()
            results = []
            for row in rows:
                env_id, name, description, is_active, created_at, updated_at = row
                results.append(
                    {
                        'id': env_id,
                        'environment_id': env_id,
                        'name': name,
                        'description': description,
                        'is_active': bool(is_active),
                        'created_at': created_at,
                        'updated_at': updated_at,
                    }
                )
            return results
        except Exception as exc:
            logger.warning(f"Unable to fetch environments: {exc}")
            return []


# Provide a shared instance for convenience imports
directory_paths_db = DirectoryPathsDB()
