"""
Step Retry Manager for Mobile Automation Framework

This module provides step-level retry and rerun functionality while preserving
all existing performance optimizations including:
- 30-second timeout override for critical elements
- Intelligent timeout handling
- Fast element finding optimizations
- Threading-based timeout enforcement
"""

import time
import logging
import json
import os
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum


class RetryTrigger(Enum):
    """Types of failures that can trigger step retries"""
    ELEMENT_NOT_FOUND = "element_not_found"
    TIMEOUT_EXCEPTION = "timeout_exception"
    INTERACTION_FAILURE = "interaction_failure"
    NETWORK_ERROR = "network_error"
    DEVICE_CONNECTION_ERROR = "device_connection_error"
    STALE_ELEMENT = "stale_element"
    TEST_LOGIC_ERROR = "test_logic_error"
    INVALID_PARAMETERS = "invalid_parameters"
    DEVICE_UNAVAILABLE = "device_unavailable"


@dataclass
class StepFailureMetadata:
    """Metadata for failed steps to enable rerun functionality"""
    step_index: int
    action_type: str
    parameters: Dict[str, Any]
    failure_reason: str
    failure_type: RetryTrigger
    timestamp: str
    retry_count: int
    max_retries: int
    test_case_name: str
    suite_id: Optional[str] = None
    execution_id: Optional[str] = None
    screenshot_path: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None


class StepRetryManager:
    """
    Manages step-level retry and rerun functionality for mobile automation
    
    Features:
    - Step-level retry with configurable strategies
    - Failed step metadata storage for rerun capability
    - Integration with existing performance optimizations
    - Intelligent retry decision making based on failure types
    - Context preservation for step rerun functionality
    """
    
    def __init__(self, config=None):
        """
        Initialize the Step Retry Manager
        
        Args:
            config: Configuration dictionary for retry behavior
        """
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config)
        self.failed_steps_storage = {}  # In-memory storage for failed steps
        self.current_test_context = {}
        self.retry_statistics = {
            'total_retries': 0,
            'successful_retries': 0,
            'failed_retries': 0,
            'retry_by_type': {}
        }
        
        # Initialize failed steps storage directory
        self.failed_steps_dir = os.path.join(os.getcwd(), 'failed_steps_metadata')
        os.makedirs(self.failed_steps_dir, exist_ok=True)
        
        self.logger.info("Step Retry Manager initialized with configuration")
    
    def _load_config(self, config=None):
        """Load retry configuration from config or use defaults"""
        if config:
            return config
        
        try:
            import config_android
            return getattr(config_android, 'STEP_RETRY_CONFIG', self._get_default_config())
        except ImportError:
            self.logger.warning("Could not load config_android, using default retry configuration")
            return self._get_default_config()
    
    def _get_default_config(self):
        """Get default retry configuration"""
        return {
            'enabled': True,
            'default_max_retries': 2,
            'default_retry_delay': 1.5,
            'conditional_max_retries': 1,
            'conditional_retry_delay': 1.0,
            'critical_max_retries': 3,
            'critical_retry_delay': 2.0,
            'preserve_timeouts': True,
            'enable_rerun': True,
            'rerun_context_preservation': True,
            'retry_triggers': {
                'element_not_found': True,
                'timeout_exception': True,
                'interaction_failure': True,
                'network_error': True,
                'device_connection_error': True,
                'stale_element': True,
                'test_logic_error': False,
                'invalid_parameters': False,
                'device_unavailable': False
            }
        }
    
    def should_retry_step(self, failure_reason: str, action_type: str, retry_count: int) -> Tuple[bool, RetryTrigger]:
        """
        Determine if a step should be retried based on failure reason and configuration
        
        Args:
            failure_reason: The reason for step failure
            action_type: Type of action that failed
            retry_count: Current retry count for this step
            
        Returns:
            Tuple of (should_retry: bool, failure_type: RetryTrigger)
        """
        if not self.config.get('enabled', True):
            return False, RetryTrigger.TEST_LOGIC_ERROR
        
        # Classify the failure type
        failure_type = self._classify_failure(failure_reason)
        
        # Check if this failure type should trigger retries
        if not self.config['retry_triggers'].get(failure_type.value, False):
            self.logger.debug(f"Failure type {failure_type.value} not configured for retry")
            return False, failure_type
        
        # Determine max retries based on action type and failure
        max_retries = self._get_max_retries_for_action(action_type, failure_type)
        
        # Check if we've exceeded max retries
        if retry_count >= max_retries:
            self.logger.debug(f"Max retries ({max_retries}) exceeded for {action_type}")
            return False, failure_type
        
        self.logger.info(f"Step retry approved: {action_type}, attempt {retry_count + 1}/{max_retries}")
        return True, failure_type
    
    def _classify_failure(self, failure_reason: str) -> RetryTrigger:
        """Classify failure reason into retry trigger types"""
        failure_lower = failure_reason.lower()
        
        if any(keyword in failure_lower for keyword in ['element not found', 'no such element', 'element does not exist']):
            return RetryTrigger.ELEMENT_NOT_FOUND
        elif any(keyword in failure_lower for keyword in ['timeout', 'timed out', 'time out']):
            return RetryTrigger.TIMEOUT_EXCEPTION
        elif any(keyword in failure_lower for keyword in ['click failed', 'tap failed', 'interaction failed', 'not clickable']):
            return RetryTrigger.INTERACTION_FAILURE
        elif any(keyword in failure_lower for keyword in ['connection', 'network', 'socket']):
            return RetryTrigger.NETWORK_ERROR
        elif any(keyword in failure_lower for keyword in ['device', 'appium', 'driver']):
            return RetryTrigger.DEVICE_CONNECTION_ERROR
        elif any(keyword in failure_lower for keyword in ['stale', 'reference']):
            return RetryTrigger.STALE_ELEMENT
        elif any(keyword in failure_lower for keyword in ['parameter', 'argument', 'invalid']):
            return RetryTrigger.INVALID_PARAMETERS
        elif any(keyword in failure_lower for keyword in ['unavailable', 'not available', 'offline']):
            return RetryTrigger.DEVICE_UNAVAILABLE
        else:
            return RetryTrigger.TEST_LOGIC_ERROR
    
    def _get_max_retries_for_action(self, action_type: str, failure_type: RetryTrigger) -> int:
        """Get maximum retries based on action type and failure type"""
        # Check if this is a conditional action
        if action_type.lower() in ['tapiflocatorexists', 'tapifimageexists', 'tapiftextexists', 'checkifexists']:
            return self.config.get('conditional_max_retries', 1)
        
        # Check if this involves critical elements (based on action parameters or type)
        if any(keyword in action_type.lower() for keyword in ['checkout', 'continue', 'submit', 'confirm']):
            return self.config.get('critical_max_retries', 3)
        
        # Default retries
        return self.config.get('default_max_retries', 2)
    
    def get_retry_delay(self, action_type: str, failure_type: RetryTrigger, retry_count: int) -> float:
        """Get retry delay based on action type and failure type"""
        # Check if this is a conditional action
        if action_type.lower() in ['tapiflocatorexists', 'tapifimageexists', 'tapiftextexists', 'checkifexists']:
            return self.config.get('conditional_retry_delay', 1.0)
        
        # Check if this involves critical elements
        if any(keyword in action_type.lower() for keyword in ['checkout', 'continue', 'submit', 'confirm']):
            return self.config.get('critical_retry_delay', 2.0)
        
        # Default delay with exponential backoff for network errors
        base_delay = self.config.get('default_retry_delay', 1.5)
        if failure_type in [RetryTrigger.NETWORK_ERROR, RetryTrigger.DEVICE_CONNECTION_ERROR]:
            return base_delay * (1.5 ** retry_count)  # Exponential backoff
        
        return base_delay

    def store_failed_step(self, step_metadata: StepFailureMetadata) -> str:
        """
        Store failed step metadata for potential rerun

        Args:
            step_metadata: Metadata about the failed step

        Returns:
            str: Storage key for the failed step
        """
        if not self.config.get('enable_rerun', True):
            return ""

        # Generate unique key for this failed step
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        storage_key = f"{step_metadata.test_case_name}_{step_metadata.step_index}_{timestamp}"

        # Store in memory
        self.failed_steps_storage[storage_key] = step_metadata

        # Store to file for persistence
        try:
            file_path = os.path.join(self.failed_steps_dir, f"{storage_key}.json")
            # Convert to dict and handle enum serialization
            metadata_dict = asdict(step_metadata)
            metadata_dict['failure_type'] = step_metadata.failure_type.value  # Convert enum to string
            with open(file_path, 'w') as f:
                json.dump(metadata_dict, f, indent=2)
            self.logger.info(f"Failed step metadata stored: {storage_key}")
        except Exception as e:
            self.logger.error(f"Failed to store step metadata to file: {e}")

        return storage_key

    def get_failed_steps(self, test_case_name: str = None) -> List[StepFailureMetadata]:
        """
        Get list of failed steps, optionally filtered by test case

        Args:
            test_case_name: Optional filter by test case name

        Returns:
            List of failed step metadata
        """
        failed_steps = []

        # Load from files if not in memory
        if not self.failed_steps_storage:
            self._load_failed_steps_from_files()

        for key, metadata in self.failed_steps_storage.items():
            if test_case_name is None or metadata.test_case_name == test_case_name:
                failed_steps.append(metadata)

        return failed_steps

    def _load_failed_steps_from_files(self):
        """Load failed steps metadata from persistent storage"""
        try:
            for filename in os.listdir(self.failed_steps_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.failed_steps_dir, filename)
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        # Convert failure_type string back to enum
                        if 'failure_type' in data and isinstance(data['failure_type'], str):
                            data['failure_type'] = RetryTrigger(data['failure_type'])
                        metadata = StepFailureMetadata(**data)
                        key = filename.replace('.json', '')
                        self.failed_steps_storage[key] = metadata
        except Exception as e:
            self.logger.error(f"Failed to load step metadata from files: {e}")

    def clear_failed_steps(self, test_case_name: str = None):
        """
        Clear failed steps metadata

        Args:
            test_case_name: Optional filter to clear only specific test case failures
        """
        if test_case_name:
            # Clear specific test case failures
            keys_to_remove = [key for key, metadata in self.failed_steps_storage.items()
                            if metadata.test_case_name == test_case_name]
            for key in keys_to_remove:
                del self.failed_steps_storage[key]
                # Remove file
                try:
                    file_path = os.path.join(self.failed_steps_dir, f"{key}.json")
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    self.logger.error(f"Failed to remove step metadata file: {e}")
        else:
            # Clear all failures
            self.failed_steps_storage.clear()
            # Remove all files
            try:
                for filename in os.listdir(self.failed_steps_dir):
                    if filename.endswith('.json'):
                        os.remove(os.path.join(self.failed_steps_dir, filename))
            except Exception as e:
                self.logger.error(f"Failed to clear step metadata files: {e}")

    def update_retry_statistics(self, action_type: str, failure_type: RetryTrigger, success: bool):
        """Update retry statistics for monitoring and optimization"""
        self.retry_statistics['total_retries'] += 1

        if success:
            self.retry_statistics['successful_retries'] += 1
        else:
            self.retry_statistics['failed_retries'] += 1

        # Track by failure type
        if failure_type.value not in self.retry_statistics['retry_by_type']:
            self.retry_statistics['retry_by_type'][failure_type.value] = {
                'total': 0, 'successful': 0, 'failed': 0
            }

        self.retry_statistics['retry_by_type'][failure_type.value]['total'] += 1
        if success:
            self.retry_statistics['retry_by_type'][failure_type.value]['successful'] += 1
        else:
            self.retry_statistics['retry_by_type'][failure_type.value]['failed'] += 1

    def get_retry_statistics(self) -> Dict[str, Any]:
        """Get current retry statistics"""
        return self.retry_statistics.copy()

    def set_test_context(self, context: Dict[str, Any]):
        """Set current test context for rerun functionality"""
        if self.config.get('rerun_context_preservation', True):
            self.current_test_context = context.copy()

    def get_test_context(self) -> Dict[str, Any]:
        """Get current test context"""
        return self.current_test_context.copy()

    def is_retry_enabled(self) -> bool:
        """Check if retry mechanism is enabled"""
        return self.config.get('enabled', True)

    def preserve_performance_optimizations(self) -> bool:
        """Check if performance optimizations should be preserved"""
        return self.config.get('preserve_timeouts', True)
