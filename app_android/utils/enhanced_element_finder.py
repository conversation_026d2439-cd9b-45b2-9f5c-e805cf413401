"""
Enhanced Element Finder for Android Automation
Provides intelligent element identification with adaptive strategies and performance optimization
"""

import time
import logging
from typing import Optional, Dict, Any, List, Tuple
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

try:
    from appium.webdriver.common.appiumby import AppiumBy
except ImportError:
    from appium.webdriver.common.mobileby import MobileBy as AppiumBy

# Alumnium integration removed

# Import adaptive timeout manager
try:
    from .adaptive_timeout_manager import adaptive_timeout_manager
    ADAPTIVE_TIMEOUT_AVAILABLE = True
except ImportError:
    ADAPTIVE_TIMEOUT_AVAILABLE = False
    adaptive_timeout_manager = None

logger = logging.getLogger(__name__)

class EnhancedElementFinder:
    """
    Enhanced element finder with intelligent strategies and performance optimization
    """
    
    def __init__(self, controller):
        self.controller = controller
        self.logger = logger
        
        # Performance tracking
        self.search_attempts = 0
        self.successful_finds = 0
        self.fallback_successes = 0
        self.strategy_performance = {}
        
        # Adaptive timeout configuration
        self.base_timeout = 10
        self.max_timeout = 120  # Increased from 30 to 120 seconds
        self.min_timeout = 3
        self.timeout_increment = 5
        
    def find_element_intelligently(self, locator_type: str, locator_value: str,
                                 timeout: Optional[int] = None,
                                 context: str = None, action_type: str = None) -> Optional[Any]:
        """
        Find element using intelligent strategies with adaptive timeout and cross-platform validation

        Args:
            locator_type: Type of locator (id, xpath, accessibility_id, uiselector, etc.)
            locator_value: Value of the locator
            timeout: Optional timeout for element finding
            context: Context of the search (e.g., 'conditional', 'action', 'verification')
            action_type: Type of action being performed (for adaptive timeout calculation)

        Returns:
            WebElement or None if not found
        """
        start_time = time.time()
        self.search_attempts += 1

        try:
            # Import adaptive timeout manager
            from .adaptive_timeout_manager import adaptive_timeout_manager

            # Validate locator for Android platform
            is_valid, validation_message, suggested_locator = adaptive_timeout_manager.validate_locator_for_android(
                locator_type, locator_value
            )

            if not is_valid:
                self.logger.error(f"Invalid locator for Android: {validation_message}")
                if suggested_locator:
                    self.logger.info(f"Suggested Android locator: {suggested_locator}")
                    # Use the suggested locator
                    locator_value = suggested_locator
                else:
                    # Fast-fail for invalid locators
                    adaptive_timeout_manager.record_performance(
                        locator_type, locator_value, False, 0.1, timeout or 30
                    )
                    return None

            # Check for fast-fail conditions
            if adaptive_timeout_manager.should_fast_fail(locator_type, locator_value):
                self.logger.debug(f"Fast-fail applied for locator: {locator_type}='{locator_value}'")
                adaptive_timeout_manager.record_performance(
                    locator_type, locator_value, False, 0.1, timeout or 30
                )
                return None

            # Calculate adaptive timeout configuration
            timeout_config = adaptive_timeout_manager.calculate_adaptive_timeout(
                action_type or context or 'action', locator_type, locator_value, timeout
            )

            effective_timeout = timeout_config['timeout']
            poll_frequency = timeout_config['poll_frequency']
            early_termination = timeout_config['early_termination']

            self.logger.info(f"Intelligent element search: {locator_type}='{locator_value}' "
                           f"(timeout: {effective_timeout}s, strategy: {timeout_config['strategy_used']})")

            # Strategy 1: Fast primary search with adaptive timeout
            primary_timeout = effective_timeout * 0.4  # 40% of total timeout
            element = self._primary_element_search(locator_type, locator_value, primary_timeout, poll_frequency)
            if element:
                duration = time.time() - start_time
                self._record_success('primary', duration)
                adaptive_timeout_manager.record_performance(locator_type, locator_value, True, duration, effective_timeout)
                return element

            # Early termination check for conditional actions
            if early_termination and time.time() - start_time > effective_timeout * 0.6:
                self.logger.debug("Early termination triggered for conditional action")
                duration = time.time() - start_time
                adaptive_timeout_manager.record_performance(locator_type, locator_value, False, duration, effective_timeout)
                return None

            # Strategy 2: Enhanced search with optimizations
            remaining_time = effective_timeout - (time.time() - start_time)
            if remaining_time > 1.0:
                enhanced_timeout = min(remaining_time * 0.6, effective_timeout * 0.4)
                element = self._enhanced_element_search(locator_type, locator_value, enhanced_timeout, poll_frequency)
                if element:
                    duration = time.time() - start_time
                    self._record_success('enhanced', duration)
                    adaptive_timeout_manager.record_performance(locator_type, locator_value, True, duration, effective_timeout)
                    return element

            # Strategy 3: Fallback strategies (only if not conditional and time remaining)
            if context != 'conditional' and not early_termination:
                remaining_time = effective_timeout - (time.time() - start_time)
                if remaining_time > 1.0:
                    fallback_timeout = min(remaining_time, effective_timeout * 0.2)
                    element = self._fallback_element_search(locator_type, locator_value, fallback_timeout, poll_frequency)
                    if element:
                        duration = time.time() - start_time
                        self._record_success('fallback', duration)
                        adaptive_timeout_manager.record_performance(locator_type, locator_value, True, duration, effective_timeout)
                        return element

            # Strategy 4: AI-powered healing removed

            # Log failure for analysis
            duration = time.time() - start_time
            self.logger.warning(f"Element not found after {duration:.2f}s: {locator_type}='{locator_value}'")
            adaptive_timeout_manager.record_performance(locator_type, locator_value, False, duration, effective_timeout)
            return None

        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(f"Error in intelligent element search: {e}")
            try:
                from .adaptive_timeout_manager import adaptive_timeout_manager
                adaptive_timeout_manager.record_performance(locator_type, locator_value, False, duration, timeout or 30)
            except:
                pass
            return None
    
    def _calculate_adaptive_timeout(self, locator_type: str, locator_value: str, 
                                  user_timeout: Optional[int], context: str) -> int:
        """
        Calculate adaptive timeout based on context, locator type, and historical performance
        """
        # Use user timeout if specified for conditional actions
        if context == 'conditional' and user_timeout is not None:
            return max(user_timeout, self.min_timeout)
        
        # Base timeout calculation
        if user_timeout is not None:
            base = min(user_timeout, self.max_timeout)
        else:
            base = self.base_timeout
        
        # Adjust based on locator type complexity
        complexity_multiplier = self._get_locator_complexity_multiplier(locator_type, locator_value)
        adjusted_timeout = int(base * complexity_multiplier)
        
        # Apply context-based adjustments
        context_multiplier = {
            'conditional': 0.5,  # Faster for conditional checks
            'action': 1.0,       # Standard for actions
            'verification': 1.2,  # Slightly longer for verifications
            'critical': 1.5      # Longer for critical elements
        }.get(context, 1.0)
        
        final_timeout = int(adjusted_timeout * context_multiplier)
        return max(self.min_timeout, min(final_timeout, self.max_timeout))
    
    def _get_locator_complexity_multiplier(self, locator_type: str, locator_value: str) -> float:
        """
        Get complexity multiplier based on locator type and value
        """
        # Simple locators are faster
        if locator_type.lower() in ['id', 'accessibility_id']:
            return 0.8
        
        # UISelector can be fast if well-formed
        if locator_type.lower() == 'uiselector':
            if 'resourceId(' in locator_value or 'text(' in locator_value:
                return 0.9
            return 1.1
        
        # XPath complexity analysis
        if locator_type.lower() == 'xpath':
            if locator_value.count('//') > 2 or 'contains(' in locator_value:
                return 1.3  # Complex XPath
            elif locator_value.startswith('//') and locator_value.count('/') <= 3:
                return 1.0  # Simple XPath
            return 1.2  # Moderate XPath
        
        return 1.0  # Default multiplier
    
    def _primary_element_search(self, locator_type: str, locator_value: str, timeout: float, poll_frequency: float = 0.3) -> Optional[Any]:
        """
        Primary element search using the most direct method with optimized polling
        """
        try:
            # Handle UISelector specially
            if locator_type.lower() == 'uiselector':
                return self._find_by_uiselector(locator_value, timeout, poll_frequency)

            # Handle standard locators with optimized polling
            by_type = self._get_appium_by_type(locator_type)
            if by_type:
                wait = WebDriverWait(self.controller.driver, timeout, poll_frequency=poll_frequency)
                return wait.until(EC.presence_of_element_located((by_type, locator_value)))

        except TimeoutException:
            self.logger.debug(f"Primary search timeout: {locator_type}='{locator_value}' ({timeout}s)")
        except Exception as e:
            self.logger.debug(f"Primary search error: {e}")

        return None
    
    def _enhanced_element_search(self, locator_type: str, locator_value: str, timeout: float, poll_frequency: float = 0.3) -> Optional[Any]:
        """
        Enhanced element search with optimizations and element presence pre-checks
        """
        try:
            # Element presence pre-check for better performance
            by_type = self._get_appium_by_type(locator_type)
            if by_type:
                # First check if element exists at all (quick check)
                quick_wait = WebDriverWait(self.controller.driver, min(timeout * 0.3, 2.0), poll_frequency=poll_frequency)
                try:
                    element = quick_wait.until(EC.presence_of_element_located((by_type, locator_value)))
                    # If element exists, check if it's clickable for interactive elements
                    if locator_type.lower() in ['id', 'xpath', 'accessibility_id']:
                        remaining_timeout = timeout - min(timeout * 0.3, 2.0)
                        if remaining_timeout > 0.5:
                            clickable_wait = WebDriverWait(self.controller.driver, remaining_timeout, poll_frequency=poll_frequency)
                            return clickable_wait.until(EC.element_to_be_clickable((by_type, locator_value)))
                    return element
                except TimeoutException:
                    # Element doesn't exist, no point checking clickability
                    return None

        except TimeoutException:
            self.logger.debug(f"Enhanced search timeout: {locator_type}='{locator_value}' ({timeout}s)")
        except Exception as e:
            self.logger.debug(f"Enhanced search error: {e}")

        return None
    
    def _fallback_element_search(self, locator_type: str, locator_value: str, timeout: int) -> Optional[Any]:
        """
        Fallback element search using alternative strategies
        """
        fallback_strategies = [
            self._try_partial_matching,
            self._try_alternative_locators,
            self._try_relaxed_conditions
        ]
        
        strategy_timeout = max(1, timeout // len(fallback_strategies))
        
        for strategy in fallback_strategies:
            try:
                element = strategy(locator_type, locator_value, strategy_timeout)
                if element:
                    self.fallback_successes += 1
                    return element
            except Exception as e:
                self.logger.debug(f"Fallback strategy failed: {e}")
        
        return None
    
    def _find_by_uiselector(self, uiselector_value: str, timeout: float, poll_frequency: float = 0.3) -> Optional[Any]:
        """
        Find element using UISelector with proper error handling and optimized polling
        """
        try:
            wait = WebDriverWait(self.controller.driver, timeout, poll_frequency=poll_frequency)
            return wait.until(EC.presence_of_element_located((AppiumBy.ANDROID_UIAUTOMATOR, uiselector_value)))
        except Exception as e:
            self.logger.debug(f"UISelector search failed: {e}")
            return None
    
    def _get_appium_by_type(self, locator_type: str):
        """
        Get AppiumBy type for locator
        """
        locator_map = {
            'id': AppiumBy.ID,
            'xpath': AppiumBy.XPATH,
            'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
            'class': AppiumBy.CLASS_NAME,
            'class_name': AppiumBy.CLASS_NAME,
            'name': AppiumBy.NAME,
            'tag_name': AppiumBy.TAG_NAME
        }
        return locator_map.get(locator_type.lower())
    
    def _try_partial_matching(self, locator_type: str, locator_value: str, timeout: int) -> Optional[Any]:
        """
        Try partial text matching for text-based locators
        """
        # Implementation would go here - simplified for space
        return None
    
    def _try_alternative_locators(self, locator_type: str, locator_value: str, timeout: int) -> Optional[Any]:
        """
        Try alternative locator strategies
        """
        # Implementation would go here - simplified for space
        return None
    
    def _try_relaxed_conditions(self, locator_type: str, locator_value: str, timeout: int) -> Optional[Any]:
        """
        Try with relaxed waiting conditions
        """
        # Implementation would go here - simplified for space
        return None

    # AI healing method removed

    def find_element_with_ai_fallback(self, locator_type: str, locator_value: str,
                                    timeout: float = 30, context: str = None) -> Optional[Any]:
        """
        Find element with automatic AI healing fallback for any action

        Args:
            locator_type: Type of locator (id, xpath, etc.)
            locator_value: Value of the locator
            timeout: Maximum time to wait for element
            context: Context of the search operation

        Returns:
            WebElement if found, None otherwise
        """
        # First try normal element finding
        element = self.find_element_intelligently(locator_type, locator_value, timeout, context)

        if element:
            return element

        # AI healing removed
        self.logger.warning(f"Element not found: {locator_type}='{locator_value}'")
        return None
    
    def _record_success(self, strategy: str, duration: float):
        """
        Record successful element finding for performance analysis
        """
        self.successful_finds += 1
        if strategy not in self.strategy_performance:
            self.strategy_performance[strategy] = {'count': 0, 'total_time': 0}
        
        self.strategy_performance[strategy]['count'] += 1
        self.strategy_performance[strategy]['total_time'] += duration
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for analysis
        """
        success_rate = (self.successful_finds / self.search_attempts * 100) if self.search_attempts > 0 else 0
        fallback_rate = (self.fallback_successes / self.successful_finds * 100) if self.successful_finds > 0 else 0
        
        return {
            'search_attempts': self.search_attempts,
            'successful_finds': self.successful_finds,
            'success_rate': round(success_rate, 2),
            'fallback_successes': self.fallback_successes,
            'fallback_rate': round(fallback_rate, 2),
            'strategy_performance': self.strategy_performance
        }
