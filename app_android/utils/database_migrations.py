"""
Database migration utilities for Android automation.
Handles schema updates for database-only execution tracking.
"""

import sqlite3
import logging
from typing import List, Tuple

logger = logging.getLogger(__name__)


class DatabaseMigrations:
    """
    Handle database schema migrations for execution tracking.
    """
    
    def __init__(self, db_path: str):
        """Initialize migrations with database path"""
        self.db_path = db_path
        logger.info(f"DatabaseMigrations initialized with DB: {db_path}")
    
    def run_all_migrations(self) -> bool:
        """
        Run all pending migrations.
        
        Returns:
            success: Boolean indicating if all migrations succeeded
        """
        try:
            logger.info("Starting database migrations...")
            
            # Migration 1: Add screenshot BLOB columns
            if not self.migrate_screenshots_table():
                logger.error("Failed to migrate screenshots table")
                return False
            
            # Migration 2: Add execution_reports columns
            if not self.migrate_execution_reports_table():
                logger.error("Failed to migrate execution_reports table")
                return False
            
            # Migration 3: Add execution_tracking screenshot_filename column
            if not self.migrate_execution_tracking_table():
                logger.error("Failed to migrate execution_tracking table")
                return False
            
            # Migration 4: Create indexes
            if not self.create_database_indexes():
                logger.error("Failed to create database indexes")
                return False
            
            logger.info("✅ All database migrations completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error running migrations: {e}")
            return False
    
    def migrate_screenshots_table(self) -> bool:
        """
        Add new columns to screenshots table for BLOB storage.
        
        New columns:
        - screenshot_blob BLOB
        - screenshot_mime TEXT
        - test_execution_id TEXT
        - test_case_id TEXT
        - compressed_size INTEGER
        - original_size INTEGER
        
        Returns:
            success: Boolean
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get existing columns
            cursor.execute("PRAGMA table_info(screenshots)")
            existing_columns = [col[1] for col in cursor.fetchall()]
            
            # Define new columns to add
            new_columns = [
                ('screenshot_blob', 'BLOB'),
                ('screenshot_mime', 'TEXT DEFAULT "image/png"'),
                ('test_execution_id', 'TEXT'),
                ('test_case_id', 'TEXT'),
                ('compressed_size', 'INTEGER'),
                ('original_size', 'INTEGER')
            ]
            
            # Add missing columns
            for col_name, col_type in new_columns:
                if col_name not in existing_columns:
                    try:
                        cursor.execute(f'ALTER TABLE screenshots ADD COLUMN {col_name} {col_type}')
                        logger.info(f"✅ Added column: screenshots.{col_name}")
                    except sqlite3.OperationalError as e:
                        if 'duplicate column name' not in str(e).lower():
                            raise
                        logger.debug(f"Column already exists: screenshots.{col_name}")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Screenshots table migration completed")
            return True
            
        except Exception as e:
            logger.error(f"Error migrating screenshots table: {e}")
            return False
    
    def migrate_execution_reports_table(self) -> bool:
        """
        Add new columns to execution_reports table.
        
        New columns:
        - test_case_id TEXT
        - platform TEXT
        - start_time TIMESTAMP
        - end_time TIMESTAMP
        - duration INTEGER
        - error_message TEXT
        - report_data TEXT
        
        Returns:
            success: Boolean
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get existing columns
            cursor.execute("PRAGMA table_info(execution_reports)")
            existing_columns = [col[1] for col in cursor.fetchall()]
            
            # Define new columns to add
            new_columns = [
                ('test_case_id', 'TEXT'),
                ('platform', 'TEXT'),
                ('start_time', 'TIMESTAMP'),
                ('end_time', 'TIMESTAMP'),
                ('duration', 'INTEGER'),
                ('error_message', 'TEXT'),
                ('report_data', 'TEXT')
            ]
            
            # Add missing columns
            for col_name, col_type in new_columns:
                if col_name not in existing_columns:
                    try:
                        cursor.execute(f'ALTER TABLE execution_reports ADD COLUMN {col_name} {col_type}')
                        logger.info(f"✅ Added column: execution_reports.{col_name}")
                    except sqlite3.OperationalError as e:
                        if 'duplicate column name' not in str(e).lower():
                            raise
                        logger.debug(f"Column already exists: execution_reports.{col_name}")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Execution reports table migration completed")
            return True
            
        except Exception as e:
            logger.error(f"Error migrating execution_reports table: {e}")
            return False
    
    def migrate_execution_tracking_table(self) -> bool:
        """
        Add screenshot_filename column to execution_tracking table.
        
        New column:
        - screenshot_filename VARCHAR(255)
        
        Returns:
            success: Boolean
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get existing columns
            cursor.execute("PRAGMA table_info(execution_tracking)")
            existing_columns = [col[1] for col in cursor.fetchall()]
            
            # Add screenshot_filename column if missing
            if 'screenshot_filename' not in existing_columns:
                try:
                    cursor.execute('ALTER TABLE execution_tracking ADD COLUMN screenshot_filename VARCHAR(255)')
                    logger.info("✅ Added column: execution_tracking.screenshot_filename")
                except sqlite3.OperationalError as e:
                    if 'duplicate column name' not in str(e).lower():
                        raise
                    logger.debug("Column already exists: execution_tracking.screenshot_filename")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Execution tracking table migration completed")
            return True
            
        except Exception as e:
            logger.error(f"Error migrating execution_tracking table: {e}")
            return False
    
    def create_database_indexes(self) -> bool:
        """
        Create indexes for better query performance.
        
        Indexes:
        - idx_screenshots_execution_id ON screenshots(test_execution_id)
        - idx_screenshots_action_id ON screenshots(action_id)
        - idx_screenshots_test_case_id ON screenshots(test_case_id)
        - idx_execution_reports_execution_id ON execution_reports(execution_id)
        - idx_execution_reports_suite_id ON execution_reports(suite_id)
        - idx_execution_tracking_execution_id ON execution_tracking(test_execution_id)
        - idx_execution_tracking_screenshot_filename ON execution_tracking(screenshot_filename)
        
        Returns:
            success: Boolean
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Define indexes to create
            indexes = [
                ('idx_screenshots_execution_id', 'screenshots', 'test_execution_id'),
                ('idx_screenshots_action_id', 'screenshots', 'action_id'),
                ('idx_screenshots_test_case_id', 'screenshots', 'test_case_id'),
                ('idx_execution_reports_execution_id', 'execution_reports', 'execution_id'),
                ('idx_execution_reports_suite_id', 'execution_reports', 'suite_id'),
                ('idx_execution_tracking_execution_id', 'execution_tracking', 'test_execution_id'),
                ('idx_execution_tracking_screenshot_filename', 'execution_tracking', 'screenshot_filename')
            ]
            
            # Create each index
            for index_name, table_name, column_name in indexes:
                try:
                    cursor.execute(f'''
                        CREATE INDEX IF NOT EXISTS {index_name} 
                        ON {table_name}({column_name})
                    ''')
                    logger.info(f"✅ Created index: {index_name}")
                except sqlite3.OperationalError as e:
                    logger.debug(f"Index may already exist: {index_name} - {e}")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Database indexes created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error creating database indexes: {e}")
            return False
    
    def verify_schema_migration(self) -> Tuple[bool, List[str]]:
        """
        Verify that all schema migrations have been applied.
        
        Returns:
            (success, missing_items): Tuple of success boolean and list of missing items
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            missing_items = []
            
            # Check screenshots table columns
            cursor.execute("PRAGMA table_info(screenshots)")
            screenshots_columns = [col[1] for col in cursor.fetchall()]
            
            required_screenshots_columns = [
                'screenshot_blob', 'screenshot_mime', 'test_execution_id',
                'test_case_id', 'compressed_size', 'original_size'
            ]
            
            for col in required_screenshots_columns:
                if col not in screenshots_columns:
                    missing_items.append(f"screenshots.{col}")
            
            # Check execution_reports table columns
            cursor.execute("PRAGMA table_info(execution_reports)")
            reports_columns = [col[1] for col in cursor.fetchall()]
            
            required_reports_columns = [
                'test_case_id', 'platform', 'start_time', 'end_time',
                'duration', 'error_message', 'report_data'
            ]
            
            for col in required_reports_columns:
                if col not in reports_columns:
                    missing_items.append(f"execution_reports.{col}")
            
            # Check execution_tracking table columns
            cursor.execute("PRAGMA table_info(execution_tracking)")
            tracking_columns = [col[1] for col in cursor.fetchall()]
            
            if 'screenshot_filename' not in tracking_columns:
                missing_items.append("execution_tracking.screenshot_filename")
            
            # Check indexes
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            existing_indexes = [row[0] for row in cursor.fetchall()]
            
            required_indexes = [
                'idx_screenshots_execution_id',
                'idx_screenshots_action_id',
                'idx_execution_reports_execution_id',
                'idx_execution_tracking_execution_id'
            ]
            
            for idx in required_indexes:
                if idx not in existing_indexes:
                    missing_items.append(f"index.{idx}")
            
            conn.close()
            
            if missing_items:
                logger.warning(f"Missing schema items: {', '.join(missing_items)}")
                return False, missing_items
            else:
                logger.info("✅ All schema migrations verified successfully")
                return True, []
            
        except Exception as e:
            logger.error(f"Error verifying schema migration: {e}")
            return False, [f"Error: {str(e)}"]


def run_migrations_for_database(db_path: str) -> bool:
    """
    Convenience function to run all migrations for a database.
    
    Args:
        db_path: Path to database file
        
    Returns:
        success: Boolean
    """
    migrations = DatabaseMigrations(db_path)
    return migrations.run_all_migrations()


def verify_migrations_for_database(db_path: str) -> Tuple[bool, List[str]]:
    """
    Convenience function to verify migrations for a database.
    
    Args:
        db_path: Path to database file
        
    Returns:
        (success, missing_items): Tuple of success boolean and list of missing items
    """
    migrations = DatabaseMigrations(db_path)
    return migrations.verify_schema_migration()

