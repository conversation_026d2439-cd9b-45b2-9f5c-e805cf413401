import os
import json
import logging
import tempfile
from datetime import datetime
from pathlib import Path
from .locators_repository import locators_repository

logger = logging.getLogger(__name__)

class LocatorsReportGenerator:
    """
    Generates HTML reports for locators repository data.
    """
    
    def __init__(self):
        self.template_html = self._get_html_template()
    
    def _get_html_template(self):
        """
        Get the HTML template for the locators report.
        
        Returns:
            str: HTML template string
        """
        return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Locators Repository Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .locator-type-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .platform-badge {
            font-size: 0.75rem;
            padding: 2px 6px;
        }
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .search-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .locator-value {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            word-break: break-all;
        }
        .action-id {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="bi bi-search"></i> Locators Repository Report
                </h1>
                <p class="text-center text-muted mb-4">
                    Generated on {{ timestamp }}
                </p>
            </div>
        </div>
        
        <!-- Summary Cards -->
        <div class="summary-card">
            <div class="row">
                <div class="col-md-3 stat-item">
                    <span class="stat-number">{{ total_locators }}</span>
                    <span class="stat-label">Total Locators</span>
                </div>
                <div class="col-md-3 stat-item">
                    <span class="stat-number">{{ total_test_cases }}</span>
                    <span class="stat-label">Test Cases</span>
                </div>
                <div class="col-md-3 stat-item">
                    <span class="stat-number">{{ total_locator_types }}</span>
                    <span class="stat-label">Locator Types</span>
                </div>
                <div class="col-md-3 stat-item">
                    <span class="stat-number">{{ total_platforms }}</span>
                    <span class="stat-label">Platforms</span>
                </div>
            </div>
        </div>
        
        <!-- Search and Filter -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-4">
                    <label for="platformFilter" class="form-label">Filter by Platform:</label>
                    <select id="platformFilter" class="form-select">
                        <option value="">All Platforms</option>
                        <option value="iOS">iOS</option>
                        <option value="Android">Android</option>
                        <option value="Both">Both</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="locatorTypeFilter" class="form-label">Filter by Locator Type:</label>
                    <select id="locatorTypeFilter" class="form-select">
                        <option value="">All Types</option>
                        {{ locator_type_options }}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="globalSearch" class="form-label">Global Search:</label>
                    <input type="text" id="globalSearch" class="form-control" placeholder="Search all columns...">
                </div>
            </div>
        </div>
        
        <!-- Locators Table -->
        <div class="table-container">
            <table id="locatorsTable" class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Test Case</th>
                        <th>Action ID</th>
                        <th>Locator Type</th>
                        <th>Locator Value</th>
                        <th>Platform</th>
                        <th>Last Used</th>
                    </tr>
                </thead>
                <tbody>
                    {{ table_rows }}
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#locatorsTable').DataTable({
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                order: [[0, 'asc'], [1, 'asc']],
                columnDefs: [
                    { targets: [3], orderable: false }, // Locator value column
                    { targets: [1], className: 'action-id' },
                    { targets: [3], className: 'locator-value' }
                ],
                language: {
                    search: "Search in table:",
                    lengthMenu: "Show _MENU_ entries per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ locators",
                    infoEmpty: "No locators found",
                    infoFiltered: "(filtered from _MAX_ total locators)"
                }
            });
            
            // Platform filter
            $('#platformFilter').on('change', function() {
                var platform = $(this).val();
                table.column(4).search(platform).draw();
            });
            
            // Locator type filter
            $('#locatorTypeFilter').on('change', function() {
                var locatorType = $(this).val();
                table.column(2).search(locatorType).draw();
            });
            
            // Global search
            $('#globalSearch').on('keyup', function() {
                table.search($(this).val()).draw();
            });
            
            // Clear DataTable's default search when using global search
            $('#globalSearch').on('focus', function() {
                $('.dataTables_filter input').val('');
                table.search('').draw();
            });
        });
    </script>
</body>
</html>
        '''
    
    def _get_locator_type_badge_class(self, locator_type):
        """
        Get Bootstrap badge class for locator type.
        
        Args:
            locator_type: Type of locator
            
        Returns:
            str: Bootstrap badge class
        """
        type_classes = {
            'xpath': 'bg-primary',
            'id': 'bg-success',
            'accessibility_id': 'bg-info',
            'ui_selector': 'bg-warning',
            'class_name': 'bg-secondary',
            'name': 'bg-dark',
            'tag_name': 'bg-light text-dark'
        }
        return type_classes.get(locator_type.lower(), 'bg-secondary')
    
    def _get_platform_badge_class(self, platform):
        """
        Get Bootstrap badge class for platform.
        
        Args:
            platform: Platform name
            
        Returns:
            str: Bootstrap badge class
        """
        platform_classes = {
            'ios': 'bg-primary',
            'android': 'bg-success',
            'both': 'bg-info'
        }
        return platform_classes.get(platform.lower(), 'bg-secondary')
    
    def _format_date(self, date_string):
        """
        Format date string for display.
        
        Args:
            date_string: ISO format date string
            
        Returns:
            str: Formatted date string
        """
        try:
            if date_string:
                dt = datetime.fromisoformat(date_string.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d %H:%M')
            return 'N/A'
        except:
            return date_string or 'N/A'
    
    def generate_report(self):
        """
        Generate HTML report for locators repository.
        
        Returns:
            dict: Result with success status and file path or error message
        """
        try:
            # Update locators repository first
            update_result = locators_repository.update_locators_repository()
            if not update_result.get('success'):
                return {
                    'success': False,
                    'error': f"Failed to update locators repository: {update_result.get('error', 'Unknown error')}"
                }
            
            # Get all locators
            locators = locators_repository.get_all_locators()
            
            if not locators:
                return {
                    'success': False,
                    'error': 'No locators found in repository'
                }
            
            # Calculate statistics
            total_locators = len(locators)
            total_test_cases = len(set(loc['test_case_name'] for loc in locators))
            total_locator_types = len(set(loc['locator_type'] for loc in locators))
            total_platforms = len(set(loc['platform'] for loc in locators))
            
            # Generate locator type options for filter
            locator_types = sorted(set(loc['locator_type'] for loc in locators))
            locator_type_options = '\n'.join([
                f'<option value="{loc_type}">{loc_type}</option>' 
                for loc_type in locator_types
            ])
            
            # Generate table rows
            table_rows = []
            for locator in locators:
                locator_type_badge = self._get_locator_type_badge_class(locator['locator_type'])
                platform_badge = self._get_platform_badge_class(locator['platform'])
                formatted_date = self._format_date(locator['last_used_date'])
                
                row = f'''
                <tr>
                    <td>{locator['test_case_name']}</td>
                    <td><span class="action-id">{locator['action_id']}</span></td>
                    <td><span class="badge locator-type-badge {locator_type_badge}">{locator['locator_type']}</span></td>
                    <td><span class="locator-value">{locator['locator_value']}</span></td>
                    <td><span class="badge platform-badge {platform_badge}">{locator['platform']}</span></td>
                    <td>{formatted_date}</td>
                </tr>
                '''
                table_rows.append(row)
            
            # Replace template variables
            html_content = self.template_html.replace('{{ timestamp }}', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            html_content = html_content.replace('{{ total_locators }}', str(total_locators))
            html_content = html_content.replace('{{ total_test_cases }}', str(total_test_cases))
            html_content = html_content.replace('{{ total_locator_types }}', str(total_locator_types))
            html_content = html_content.replace('{{ total_platforms }}', str(total_platforms))
            html_content = html_content.replace('{{ locator_type_options }}', locator_type_options)
            html_content = html_content.replace('{{ table_rows }}', '\n'.join(table_rows))
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
            temp_file.write(html_content)
            temp_file.close()
            
            return {
                'success': True,
                'file_path': temp_file.name,
                'total_locators': total_locators,
                'total_test_cases': total_test_cases,
                'total_locator_types': total_locator_types,
                'scanned_locators': update_result.get('scanned_locators', 0)
            }
            
        except Exception as e:
            logger.error(f"Error generating locators report: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# Create singleton instance
locators_report_generator = LocatorsReportGenerator()
