#!/usr/bin/env python3
"""
OCR Configuration module for Android app with optimized Tesseract settings

This module provides comprehensive configuration management for OCR operations,
including optimized Tesseract parameters, preprocessing settings, and performance
tuning options specifically designed for mobile app automation.
"""

import logging
from typing import Dict, Any, List, Optional
import json
import os

# Set up logging
logger = logging.getLogger(__name__)

class OCRConfig:
    """
    Comprehensive OCR configuration management for optimized text detection
    
    This class manages all aspects of OCR configuration including:
    - Tesseract engine parameters
    - Image preprocessing settings
    - Performance optimization options
    - Auto-detection rules for different text types
    """
    
    def __init__(self):
        """Initialize OCR configuration with optimized defaults"""
        self.logger = logging.getLogger("OCRConfig")
        
        # Optimized Tesseract configurations for different scenarios
        self.tesseract_configs = {
            'fast': {
                'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
                'description': 'Fast processing with character whitelist',
                'use_case': 'General text detection with good speed/accuracy balance',
                'preprocessing': 'medium'
            },
            'accurate': {
                'config': '--oem 1 --psm 3',
                'description': 'High accuracy mode using LSTM engine',
                'use_case': 'Complex layouts requiring maximum accuracy',
                'preprocessing': 'heavy'
            },
            'sparse': {
                'config': '--oem 3 --psm 11',
                'description': 'Sparse text detection',
                'use_case': 'Scattered text elements on screen',
                'preprocessing': 'light'
            },
            'single_word': {
                'config': '--oem 3 --psm 8',
                'description': 'Single word detection',
                'use_case': 'Button labels, single words',
                'preprocessing': 'medium'
            },
            'single_char': {
                'config': '--oem 3 --psm 10',
                'description': 'Single character detection',
                'use_case': 'Individual characters, icons with text',
                'preprocessing': 'heavy'
            },
            'digits_only': {
                'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',
                'description': 'Numeric text only',
                'use_case': 'Phone numbers, prices, quantities',
                'preprocessing': 'light'
            },
            'alpha_only': {
                'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
                'description': 'Alphabetic text only',
                'use_case': 'Names, labels without numbers',
                'preprocessing': 'medium'
            }
        }
        
        # Performance settings
        self.performance_settings = {
            'cache_enabled': True,
            'cache_size': 100,
            'max_workers': 4,
            'timeout_seconds': 30,
            'retry_attempts': 2,
            'parallel_processing': True
        }
        
        # Image preprocessing parameters
        self.preprocessing_params = {
            'light': {
                'gaussian_blur_kernel': (1, 1),
                'threshold_method': 'otsu',
                'morph_operations': False,
                'noise_reduction': False
            },
            'medium': {
                'gaussian_blur_kernel': (3, 3),
                'threshold_method': 'adaptive',
                'morph_operations': True,
                'morph_kernel_size': (3, 3),
                'noise_reduction': True,
                'dilation_iterations': 1,
                'erosion_iterations': 1
            },
            'heavy': {
                'gaussian_blur_kernel': (5, 5),
                'threshold_method': 'adaptive',
                'morph_operations': True,
                'morph_kernel_size': (3, 3),
                'noise_reduction': True,
                'clahe_enabled': True,
                'bilateral_filter': True,
                'dilation_iterations': 2,
                'erosion_iterations': 1
            }
        }
        
        # Auto-detection rules for choosing optimal configuration
        self.auto_detection_rules = {
            'digits_only': {
                'pattern': r'^\d+$',
                'config': 'digits_only',
                'confidence_boost': 0.1
            },
            'single_char': {
                'pattern': r'^.$',
                'config': 'single_char',
                'confidence_boost': 0.05
            },
            'single_word': {
                'pattern': r'^\w+$',
                'max_length': 15,
                'config': 'single_word',
                'confidence_boost': 0.05
            },
            'alpha_only': {
                'pattern': r'^[a-zA-Z\s]+$',
                'config': 'alpha_only',
                'confidence_boost': 0.05
            }
        }
        
        # Default settings
        self.default_config = 'fast'
        self.default_preprocessing = 'medium'
        self.default_similarity_threshold = 0.7
        
        self.logger.info("OCRConfig initialized with %d configurations", len(self.tesseract_configs))
    
    def get_tesseract_config(self, config_name: str) -> str:
        """
        Get Tesseract configuration string by name
        
        Args:
            config_name: Name of the configuration
            
        Returns:
            Tesseract configuration string
        """
        if config_name not in self.tesseract_configs:
            self.logger.warning(f"Unknown config '{config_name}', using default '{self.default_config}'")
            config_name = self.default_config
        
        return self.tesseract_configs[config_name]['config']
    
    def get_config_info(self, config_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a configuration
        
        Args:
            config_name: Name of the configuration
            
        Returns:
            Dictionary with configuration details
        """
        if config_name not in self.tesseract_configs:
            return {}
        
        return self.tesseract_configs[config_name].copy()
    
    def list_configurations(self) -> Dict[str, str]:
        """
        List all available configurations with descriptions
        
        Returns:
            Dictionary mapping config names to descriptions
        """
        return {name: config['description'] 
                for name, config in self.tesseract_configs.items()}
    
    def get_preprocessing_params(self, level: str) -> Dict[str, Any]:
        """
        Get preprocessing parameters for a specific level
        
        Args:
            level: Preprocessing level ('light', 'medium', 'heavy')
            
        Returns:
            Dictionary with preprocessing parameters
        """
        if level not in self.preprocessing_params:
            self.logger.warning(f"Unknown preprocessing level '{level}', using default '{self.default_preprocessing}'")
            level = self.default_preprocessing
        
        return self.preprocessing_params[level].copy()
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """
        Get current performance settings
        
        Returns:
            Dictionary with performance settings
        """
        return self.performance_settings.copy()
    
    def update_performance_settings(self, **kwargs):
        """
        Update performance settings
        
        Args:
            **kwargs: Performance settings to update
        """
        for key, value in kwargs.items():
            if key in self.performance_settings:
                old_value = self.performance_settings[key]
                self.performance_settings[key] = value
                self.logger.info(f"Updated {key}: {old_value} -> {value}")
            else:
                self.logger.warning(f"Unknown performance setting: {key}")
    
    def auto_select_config(self, text_to_find: str) -> str:
        """
        Automatically select the best configuration based on text characteristics
        
        Args:
            text_to_find: The text we're looking for
            
        Returns:
            Best configuration name
        """
        import re
        
        text_clean = text_to_find.strip()
        
        # Apply auto-detection rules
        for rule_name, rule in self.auto_detection_rules.items():
            if 'pattern' in rule:
                if re.match(rule['pattern'], text_clean):
                    # Check additional constraints
                    if 'max_length' in rule and len(text_clean) > rule['max_length']:
                        continue
                    
                    self.logger.debug(f"Auto-selected config '{rule['config']}' for text '{text_clean}' (rule: {rule_name})")
                    return rule['config']
        
        # Default fallback
        self.logger.debug(f"Using default config '{self.default_config}' for text '{text_clean}'")
        return self.default_config
    
    def get_recommended_preprocessing(self, config_name: str) -> str:
        """
        Get recommended preprocessing level for a configuration
        
        Args:
            config_name: Name of the Tesseract configuration
            
        Returns:
            Recommended preprocessing level
        """
        if config_name in self.tesseract_configs:
            return self.tesseract_configs[config_name].get('preprocessing', self.default_preprocessing)
        
        return self.default_preprocessing
    
    def validate_config(self, config_name: str) -> bool:
        """
        Validate if a configuration name exists
        
        Args:
            config_name: Name of the configuration to validate
            
        Returns:
            True if configuration exists, False otherwise
        """
        return config_name in self.tesseract_configs
    
    def export_config(self, file_path: str):
        """
        Export current configuration to a JSON file
        
        Args:
            file_path: Path to save the configuration file
        """
        config_data = {
            'tesseract_configs': self.tesseract_configs,
            'performance_settings': self.performance_settings,
            'preprocessing_params': self.preprocessing_params,
            'auto_detection_rules': self.auto_detection_rules,
            'defaults': {
                'config': self.default_config,
                'preprocessing': self.default_preprocessing,
                'similarity_threshold': self.default_similarity_threshold
            }
        }
        
        try:
            with open(file_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            self.logger.info(f"Configuration exported to {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to export configuration: {e}")
    
    def import_config(self, file_path: str):
        """
        Import configuration from a JSON file
        
        Args:
            file_path: Path to the configuration file
        """
        try:
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            # Update configurations
            if 'tesseract_configs' in config_data:
                self.tesseract_configs.update(config_data['tesseract_configs'])
            
            if 'performance_settings' in config_data:
                self.performance_settings.update(config_data['performance_settings'])
            
            if 'preprocessing_params' in config_data:
                self.preprocessing_params.update(config_data['preprocessing_params'])
            
            if 'auto_detection_rules' in config_data:
                self.auto_detection_rules.update(config_data['auto_detection_rules'])
            
            if 'defaults' in config_data:
                defaults = config_data['defaults']
                self.default_config = defaults.get('config', self.default_config)
                self.default_preprocessing = defaults.get('preprocessing', self.default_preprocessing)
                self.default_similarity_threshold = defaults.get('similarity_threshold', self.default_similarity_threshold)
            
            self.logger.info(f"Configuration imported from {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to import configuration: {e}")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        Get a summary of current configuration
        
        Returns:
            Dictionary with configuration summary
        """
        return {
            'total_configs': len(self.tesseract_configs),
            'available_configs': list(self.tesseract_configs.keys()),
            'preprocessing_levels': list(self.preprocessing_params.keys()),
            'default_config': self.default_config,
            'default_preprocessing': self.default_preprocessing,
            'performance_settings': self.performance_settings,
            'cache_enabled': self.performance_settings['cache_enabled'],
            'parallel_processing': self.performance_settings['parallel_processing']
        }


# Global configuration instance
_global_config = None

def get_ocr_config() -> OCRConfig:
    """
    Get a global instance of OCR configuration
    
    Returns:
        Global OCRConfig instance
    """
    global _global_config
    if _global_config is None:
        _global_config = OCRConfig()
    return _global_config


# Convenience functions for backward compatibility
def get_tesseract_config(config_name: str) -> str:
    """
    Get Tesseract configuration string by name
    
    Args:
        config_name: Name of the configuration
        
    Returns:
        Tesseract configuration string
    """
    return get_ocr_config().get_tesseract_config(config_name)


def list_available_configs() -> Dict[str, str]:
    """
    List all available configurations
    
    Returns:
        Dictionary mapping config names to descriptions
    """
    return get_ocr_config().list_configurations()


def auto_select_config(text_to_find: str) -> str:
    """
    Automatically select the best configuration for given text
    
    Args:
        text_to_find: The text we're looking for
        
    Returns:
        Best configuration name
    """
    return get_ocr_config().auto_select_config(text_to_find)