import re
import logging
import os
from datetime import datetime
from .directory_paths_db import directory_paths_db

logger = logging.getLogger(__name__)

# Platform validation patterns
IOS_APP_ID_PATTERNS = [
    r'^[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+$',  # Standard bundle ID format (com.company.app)
    r'^[a-zA-Z]{2}\.[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+$',     # Country-specific format (au.com.app)
]

ANDROID_APP_ID_PATTERNS = [
    r'^[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+\.android$',  # Explicit Android suffix
    r'^[a-zA-Z]{2}\.[a-zA-Z0-9\-]+\.android$',                     # Country-specific Android format
    r'^com\.[a-zA-Z0-9\-]+\.android$',                             # Standard Android format
]

def is_valid_ios_app_id(app_id):
    """
    Validate if an app ID is appropriate for iOS platform.

    Args:
        app_id (str): The app ID to validate

    Returns:
        bool: True if valid for iOS, False otherwise
    """
    if not app_id or not isinstance(app_id, str):
        return False

    # Check if it's explicitly an Android app ID
    for pattern in ANDROID_APP_ID_PATTERNS:
        if re.match(pattern, app_id, re.IGNORECASE):
            return False

    # Check if it matches iOS patterns
    for pattern in IOS_APP_ID_PATTERNS:
        if re.match(pattern, app_id, re.IGNORECASE):
            return True

    # Allow TestFlight and other Apple-specific apps
    if 'apple' in app_id.lower() or 'testflight' in app_id.lower():
        return True

    return False

def is_valid_android_app_id(app_id):
    """
    Validate if an app ID is appropriate for Android platform.

    Args:
        app_id (str): The app ID to validate

    Returns:
        bool: True if valid for Android, False otherwise
    """
    if not app_id or not isinstance(app_id, str):
        return False

    # Check if it matches Android patterns
    for pattern in ANDROID_APP_ID_PATTERNS:
        if re.match(pattern, app_id, re.IGNORECASE):
            return True

    # Check if it's a standard package format without explicit iOS indicators
    if re.match(r'^[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+$', app_id) and not is_valid_ios_app_id(app_id):
        return True

    return False

def validate_environment_platform_isolation(environment_variables, platform):
    """
    Validate that environment variables contain only appropriate app IDs for the platform.

    Args:
        environment_variables (list): List of environment variable dictionaries
        platform (str): Either 'ios' or 'android'

    Returns:
        dict: Validation result with success status and any issues found
    """
    issues = []
    app_id_fields = ['appid', 'app_id', 'bundle_id', 'package_id']

    for var in environment_variables:
        var_name = var.get('name', '').lower()
        var_value = var.get('current_value', '')

        if var_name in app_id_fields and var_value:
            if platform.lower() == 'ios':
                if not is_valid_ios_app_id(var_value):
                    issues.append({
                        'variable': var_name,
                        'value': var_value,
                        'issue': f'Invalid iOS app ID: {var_value} (appears to be Android app ID)'
                    })
            elif platform.lower() == 'android':
                if not is_valid_android_app_id(var_value):
                    issues.append({
                        'variable': var_name,
                        'value': var_value,
                        'issue': f'Invalid Android app ID: {var_value} (appears to be iOS app ID)'
                    })

    return {
        'success': len(issues) == 0,
        'platform': platform,
        'issues': issues,
        'message': f'Platform isolation validation {"passed" if len(issues) == 0 else "failed"} for {platform}'
    }

def resolve_text_with_env_variables(text_to_process: str, environment_id: int):
    """
    Enhanced environment variable resolver that supports multiple resolution methods:
    1. env[variable_name] format - resolves placeholders wrapped in env[]
    2. Direct variable name substitution - if text exactly matches a variable name, substitute its value
    3. Fallback to OS environment variables

    Args:
        text_to_process: The string containing potential environment variable placeholders or variable names.
        environment_id: The ID of the environment to fetch variables from.

    Returns:
        The string with placeholders/variables replaced by their resolved values.
        If an environment or variable is not found, or if environment_id is None,
        the original text is returned.
    """
    if environment_id is None:
        logger.warning("No environment selected. Cannot resolve environment variables.")
        return text_to_process

    if not isinstance(text_to_process, str):
        # If the input is not a string (e.g. int, bool, list, dict), return it as is.
        # This prevents errors if non-string action parameters are passed through the resolver.
        return text_to_process

    # Get all variables for the environment once to avoid multiple database calls
    try:
        variables_in_env = directory_paths_db.get_variables_for_environment(environment_id)
        logger.debug(f"Retrieved {len(variables_in_env) if variables_in_env else 0} variables for environment ID {environment_id}")

        # Create a lookup dictionary for faster access
        var_lookup = {}
        if variables_in_env:
            for var in variables_in_env:
                var_lookup[var['name']] = var.get('current_value', '')
            logger.debug(f"Variable lookup created with keys: {list(var_lookup.keys())}")
    except Exception as e:
        logger.error(f"Error retrieving variables for environment ID {environment_id}: {str(e)}")
        variables_in_env = []
        var_lookup = {}

    # Method 1: Direct variable name substitution
    # If the entire text exactly matches a variable name, substitute its value
    if text_to_process in var_lookup:
        resolved_value = var_lookup[text_to_process]
        logger.info(f"Direct variable substitution: '{text_to_process}' -> '{resolved_value}' (env ID {environment_id})")
        return str(resolved_value) if resolved_value is not None else text_to_process

    # Method 2: env[variable_name] format resolution
    def replace_env_match(match):
        variable_name = match.group(1)
        try:
            if variable_name in var_lookup:
                resolved_value = var_lookup[variable_name]
                if resolved_value is None:
                    logger.warning(f"Variable '{variable_name}' in environment ID {environment_id} has a current_value of None.")
                    return ""
                logger.info(f"Resolved 'env[{variable_name}]' to '{resolved_value}' for environment ID {environment_id}")
                return str(resolved_value)
            else:
                logger.warning(f"Variable '{variable_name}' not found in environment ID {environment_id}.")
                return match.group(0)  # Return original placeholder
        except Exception as e:
            logger.error(f"Error resolving variable '{variable_name}' for environment ID {environment_id}: {str(e)}")
            return match.group(0)  # Return original placeholder on error

    # Apply env[variable_name] substitution
    processed_text = re.sub(r'env\[(.+?)\]', replace_env_match, text_to_process)

    # Method 3: Fallback to OS environment variables if no substitution occurred
    if processed_text == text_to_process and text_to_process not in var_lookup:
        os_env_value = os.environ.get(text_to_process)
        if os_env_value is not None:
            logger.info(f"Fallback to OS environment variable: '{text_to_process}' -> '{os_env_value}'")
            return os_env_value

    return processed_text

def get_resolved_variable_value(variable_name: str, environment_id: int):
    """
    Fetches the current_value of a specific variable from a specific environment.
    Enhanced version with better error handling and fallback to OS environment variables.

    Args:
        variable_name: The name of the variable to resolve.
        environment_id: The ID of the environment.

    Returns:
        The current_value of the variable, or None if not found or an error occurs.
    """
    if environment_id is None:
        logger.warning("No environment ID provided for get_resolved_variable_value.")
        return None

    try:
        variables_in_env = directory_paths_db.get_variables_for_environment(environment_id)
        if not variables_in_env:
            logger.warning(f"No variables found for environment ID {environment_id} or environment does not exist when trying to get value for '{variable_name}'.")
            # Fallback to OS environment variable
            os_value = os.environ.get(variable_name)
            if os_value is not None:
                logger.info(f"Fallback to OS environment variable for '{variable_name}': '{os_value}'")
                return os_value
            return None

        for var in variables_in_env:
            if var['name'] == variable_name:
                value = var.get('current_value')
                logger.debug(f"Found variable '{variable_name}' in environment ID {environment_id}: '{value}'")
                return value

        logger.warning(f"Variable '{variable_name}' not found in environment ID {environment_id} for get_resolved_variable_value.")
        # Fallback to OS environment variable
        os_value = os.environ.get(variable_name)
        if os_value is not None:
            logger.info(f"Fallback to OS environment variable for '{variable_name}': '{os_value}'")
            return os_value
        return None
    except Exception as e:
        logger.error(f"Error in get_resolved_variable_value for '{variable_name}', env ID {environment_id}: {str(e)}")
        return None




def get_active_environment_id():
    """
    Get the currently active environment ID from the database.

    Returns:
        int: The active environment ID, or None if not set or error occurs.
    """
    try:
        active_env_id = directory_paths_db.get_active_environment()
        if active_env_id is not None:
            logger.debug(f"Retrieved active environment ID: {active_env_id}")
            return active_env_id
        else:
            logger.warning("No active environment set in database")
            return None
    except Exception as e:
        logger.error(f"Error getting active environment ID: {str(e)}")
        return None


def set_active_environment_id(environment_id: int):
    """
    Set the currently active environment ID in the database.

    Args:
        environment_id: The environment ID to set as active.

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        success = directory_paths_db.set_active_environment(environment_id)
        if success:
            logger.info(f"Set active environment ID to: {environment_id}")
        else:
            logger.error(f"Failed to set active environment ID to: {environment_id}")
        return success
    except Exception as e:
        logger.error(f"Error setting active environment ID to {environment_id}: {str(e)}")
        return False


def debug_environment_resolution(environment_id: int = None, text_samples: list = None):
    """
    Debug utility function to inspect environment resolution process.

    Args:
        environment_id: Optional environment ID to debug. If None, uses active environment.
        text_samples: Optional list of text samples to test resolution on.

    Returns:
        dict: Debug information about the environment resolution system.
    """
    debug_info = {
        'timestamp': str(datetime.now()),
        'environment_id': environment_id,
        'active_environment': None,
        'variables': [],
        'resolution_tests': []
    }

    try:
        # Get active environment if none specified
        if environment_id is None:
            environment_id = get_active_environment_id()
            debug_info['environment_id'] = environment_id

        # Get active environment info
        try:
            active_env_id = directory_paths_db.get_active_environment()
            debug_info['active_environment'] = active_env_id
        except Exception as e:
            debug_info['active_environment'] = f"Error: {str(e)}"

        # Get environment variables
        if environment_id is not None:
            try:
                variables = directory_paths_db.get_variables_for_environment(environment_id)
                debug_info['variables'] = variables or []
                logger.info(f"Debug: Found {len(debug_info['variables'])} variables for environment {environment_id}")
            except Exception as e:
                debug_info['variables'] = f"Error: {str(e)}"

        # Test resolution on sample texts
        if text_samples and environment_id is not None:
            for sample_text in text_samples:
                try:
                    resolved = resolve_text_with_env_variables(sample_text, environment_id)
                    debug_info['resolution_tests'].append({
                        'original': sample_text,
                        'resolved': resolved,
                        'changed': resolved != sample_text
                    })
                except Exception as e:
                    debug_info['resolution_tests'].append({
                        'original': sample_text,
                        'resolved': f"Error: {str(e)}",
                        'changed': False
                    })

        # Log debug summary
        logger.info(f"Environment Resolution Debug Summary:")
        logger.info(f"  Environment ID: {debug_info['environment_id']}")
        logger.info(f"  Variables found: {len(debug_info['variables']) if isinstance(debug_info['variables'], list) else 'Error'}")
        logger.info(f"  Resolution tests: {len(debug_info['resolution_tests'])}")

        return debug_info

    except Exception as e:
        logger.error(f"Error in debug_environment_resolution: {str(e)}")
        debug_info['error'] = str(e)
        return debug_info


def log_environment_state(environment_id: int = None):
    """
    Log detailed information about the current environment state.

    Args:
        environment_id: Optional environment ID. If None, uses active environment.
    """
    try:
        import datetime

        logger.info("=" * 60)
        logger.info("ENVIRONMENT RESOLUTION STATE REPORT")
        logger.info("=" * 60)
        logger.info(f"Timestamp: {datetime.datetime.now()}")

        # Active environment
        try:
            active_env_id = directory_paths_db.get_active_environment()
            logger.info(f"Active Environment ID: {active_env_id}")
        except Exception as e:
            logger.error(f"Error getting active environment: {e}")

        # Target environment
        target_env_id = environment_id or get_active_environment_id()
        logger.info(f"Target Environment ID: {target_env_id}")

        if target_env_id is not None:
            # Environment details
            try:
                env_details = directory_paths_db.get_environment_by_id(target_env_id)
                logger.info(f"Environment Details: {env_details}")
            except Exception as e:
                logger.error(f"Error getting environment details: {e}")

            # Variables
            try:
                variables = directory_paths_db.get_variables_for_environment(target_env_id)
                logger.info(f"Variables Count: {len(variables) if variables else 0}")
                if variables:
                    logger.info("Variables:")
                    for var in variables[:10]:  # Limit to first 10 for readability
                        logger.info(f"  {var['name']}: '{var['current_value']}'")
                    if len(variables) > 10:
                        logger.info(f"  ... and {len(variables) - 10} more variables")
            except Exception as e:
                logger.error(f"Error getting variables: {e}")

        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"Error in log_environment_state: {str(e)}")

if __name__ == '__main__':
    # Example Usage (requires a running DB setup or mocks)
    # This part is for testing and won't be run when imported

    # Mocking directory_paths_db for standalone testing:
    class MockDirectoryPathsDB:
        def get_variables_for_environment(self, env_id):
            if env_id == 1: # Mock Env 1
                return [
                    {"id": 1, "name": "username", "type": "string", "initial_value": "user_init", "current_value": "user_current"},
                    {"id": 2, "name": "password", "type": "secret", "initial_value": "pass_init", "current_value": "pass_current"},
                    {"id": 3, "name": "api_url", "type": "string", "initial_value": "url_init", "current_value": "http://test.com/api"},
                    {"id": 4, "name": "timeout", "type": "integer", "initial_value": "30", "current_value": "60"},
                    {"id": 5, "name": "empty_var", "type": "string", "initial_value": "", "current_value": ""},
                    {"id": 6, "name": "null_var", "type": "string", "initial_value": None, "current_value": None},
                ]
            elif env_id == 2: # Mock Env 2 (empty)
                return []
            return [] # Default for other env_ids

        def get_environment_by_id(self, env_id):
            if env_id == 1:
                return {"id": 1, "name": "Test Env 1"}
            return None

    # Replace the actual db instance with the mock for this test block
    original_db = directory_paths_db
    directory_paths_db = MockDirectoryPathsDB()
    
    logging.basicConfig(level=logging.INFO)
    logger.info("Testing environment variable resolution...")

    test_env_id = 1
    
    print(f"\\n--- Testing with Environment ID: {test_env_id} ---")
    
    strings_to_test = [
        "User is env[username] with password env[password]",
        "URL: env[api_url], Timeout: env[timeout] seconds",
        "No variables here.",
        "env[non_existent_var]",
        "Empty: env[empty_var], Null: env[null_var]",
        "Multiple: env[username] and env[username] again.",
        "env[username], env[password], env[api_url]",
        {"key": "value", "text": "User is env[username]"}, # Test non-string input
        12345 # Test integer input
    ]

    for s in strings_to_test:
        original_s = s
        if isinstance(s, dict): # make a copy for dicts to show original
            original_s = s.copy()

        print(f"Original: {original_s}")
        if isinstance(s, dict): # Handle dict case for processing
             processed_s = {k: resolve_text_with_env_variables(v, test_env_id) if isinstance(v, str) else v for k,v in s.items()}
        else:
            processed_s = resolve_text_with_env_variables(s, test_env_id)
        print(f"Resolved: {processed_s}\\n")

    print("\\n--- Testing with Non-existent Environment ID: 3 ---")
    print(f"Original: User is env[username]")
    print(f"Resolved: {resolve_text_with_env_variables('User is env[username]', 3)}\\n")
    
    print("\\n--- Testing with None Environment ID ---")
    print(f"Original: User is env[username]")
    print(f"Resolved: {resolve_text_with_env_variables('User is env[username]', None)}\\n")

    print("\\n--- Testing get_resolved_variable_value ---")
    print(f"Value of 'username' in env {test_env_id}: {get_resolved_variable_value('username', test_env_id)}")
    print(f"Value of 'non_existent_var' in env {test_env_id}: {get_resolved_variable_value('non_existent_var', test_env_id)}")
    print(f"Value of 'username' in env 3: {get_resolved_variable_value('username', 3)}")
    print(f"Value of 'username' with env None: {get_resolved_variable_value('username', None)}")

    # Restore original db instance if it was mocked
    directory_paths_db = original_db 