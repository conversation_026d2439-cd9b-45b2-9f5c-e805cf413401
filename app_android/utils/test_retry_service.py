"""
Test Retry Service

This service handles retry logic for failed tests including:
- Retrying individual failed tests
- Retrying all failed tests from an execution
- Retrying entire test suite
- Tracking retry history

Author: Mobile App Automation Tool
Date: 2025-01-07
"""

import sqlite3
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)


class TestRetryService:
    """Service for handling test retry operations"""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the test retry service
        
        Args:
            db_path: Path to database file (defaults to centralized Android db)
        """
        if db_path is None:
            base_dir = Path(__file__).resolve().parents[2]
            db_path = str(base_dir / 'db-data' / 'android.db')
        
        self.db_path = db_path
        logger.info(f"TestRetryService initialized with db: {db_path}")
    
    def get_failed_test_info(
        self,
        original_execution_id: str,
        test_case_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get information about a failed test
        
        Args:
            original_execution_id: Original execution ID where test failed
            test_case_id: Test case identifier
            
        Returns:
            dict: Failed test information or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM failed_tests_registry
                WHERE test_execution_id = ? AND test_case_id = ?
                ORDER BY failure_timestamp DESC
                LIMIT 1
            ''', (original_execution_id, test_case_id))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return dict(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting failed test info: {e}")
            return None
    
    def get_all_failed_tests(
        self,
        original_execution_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get all failed tests from an execution
        
        Args:
            original_execution_id: Original execution ID
            
        Returns:
            list: List of failed test dictionaries
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM failed_tests_registry
                WHERE test_execution_id = ?
                AND retry_status IN ('pending', 'failed_again')
                ORDER BY failure_timestamp
            ''', (original_execution_id,))
            
            rows = cursor.fetchall()
            conn.close()
            
            failed_tests = [dict(row) for row in rows]
            logger.info(f"Found {len(failed_tests)} failed tests for execution {original_execution_id}")
            return failed_tests
            
        except Exception as e:
            logger.error(f"Error getting all failed tests: {e}")
            return []
    
    def update_retry_status(
        self,
        failed_test_id: int,
        new_execution_id: str,
        status: str
    ) -> bool:
        """
        Update retry status for a failed test
        
        Args:
            failed_test_id: Failed test registry ID
            new_execution_id: New execution ID for retry
            status: New retry status ('retried', 'passed', 'failed_again')
            
        Returns:
            bool: True if successful
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get current retry count
            cursor.execute('''
                SELECT retry_count FROM failed_tests_registry
                WHERE id = ?
            ''', (failed_test_id,))
            
            result = cursor.fetchone()
            if not result:
                conn.close()
                return False
            
            retry_count = result[0] + 1
            
            # Update retry status
            cursor.execute('''
                UPDATE failed_tests_registry
                SET retry_count = ?,
                    last_retry_execution_id = ?,
                    last_retry_timestamp = ?,
                    retry_status = ?
                WHERE id = ?
            ''', (
                retry_count,
                new_execution_id,
                datetime.now().isoformat(),
                status,
                failed_test_id
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Updated retry status for failed test {failed_test_id} to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating retry status: {e}")
            return False
    
    def get_retry_history(self, test_case_id: str) -> List[Dict[str, Any]]:
        """
        Get retry history for a test case
        
        Args:
            test_case_id: Test case identifier
            
        Returns:
            list: List of retry history entries
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM failed_tests_registry
                WHERE test_case_id = ?
                ORDER BY failure_timestamp DESC
            ''', (test_case_id,))
            
            rows = cursor.fetchall()
            conn.close()
            
            history = [dict(row) for row in rows]
            logger.info(f"Retrieved {len(history)} retry history entries for test case {test_case_id}")
            return history
            
        except Exception as e:
            logger.error(f"Error getting retry history: {e}")
            return []
    
    def get_test_case_filename(
        self,
        original_execution_id: str,
        test_case_id: str
    ) -> Optional[str]:
        """
        Get test case filename from execution tracking
        
        Args:
            original_execution_id: Original execution ID
            test_case_id: Test case identifier
            
        Returns:
            str: Test case filename or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT filename FROM execution_tracking
                WHERE test_execution_id = ? AND test_case_id = ?
                LIMIT 1
            ''', (original_execution_id, test_case_id))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0]
            return None
            
        except Exception as e:
            logger.error(f"Error getting test case filename: {e}")
            return None
    
    def get_suite_info(self, original_execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get suite information from execution summary
        
        Args:
            original_execution_id: Original execution ID
            
        Returns:
            dict: Suite information or None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT suite_id, suite_name, device_id, device_name
                FROM execution_summary
                WHERE test_execution_id = ?
            ''', (original_execution_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return dict(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting suite info: {e}")
            return None
    
    def mark_test_as_retried(
        self,
        original_execution_id: str,
        test_case_id: str,
        new_execution_id: str
    ) -> bool:
        """
        Mark a test as retried
        
        Args:
            original_execution_id: Original execution ID
            test_case_id: Test case identifier
            new_execution_id: New execution ID for retry
            
        Returns:
            bool: True if successful
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE failed_tests_registry
                SET retry_count = retry_count + 1,
                    last_retry_execution_id = ?,
                    last_retry_timestamp = ?,
                    retry_status = 'retried'
                WHERE test_execution_id = ? AND test_case_id = ?
            ''', (
                new_execution_id,
                datetime.now().isoformat(),
                original_execution_id,
                test_case_id
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Marked test {test_case_id} as retried")
            return True
            
        except Exception as e:
            logger.error(f"Error marking test as retried: {e}")
            return False

