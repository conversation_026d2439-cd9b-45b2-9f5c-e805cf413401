#!/usr/bin/env python3
"""
Optimized text detector for Android app with improved Tesseract configuration,
enhanced preprocessing, and performance optimizations

This module provides significant performance improvements over the standard
text detection implementation through:
- Optimized Tesseract configurations for different text types
- Advanced image preprocessing techniques
- Intelligent caching system
- Parallel processing capabilities
"""

import cv2
import numpy as np
import pytesseract
import logging
import os
import threading
import hashlib
import time
from typing import List, Dict, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor
from difflib import SequenceMatcher

# Set up logging
logger = logging.getLogger(__name__)

class OptimizedTextDetector:
    """
    Optimized text detector with improved Tesseract configuration,
    enhanced preprocessing, and performance optimizations
    """
    
    def __init__(self, cache_size: int = 100):
        """Initialize the optimized text detector"""
        self.logger = logging.getLogger("OptimizedTextDetector")
        
        # Cache for OCR results to avoid reprocessing same images
        self._cache = {}
        self._cache_size = cache_size
        self._cache_lock = threading.Lock()
        
        # Optimized Tesseract configurations for different scenarios
        self.configs = {
            'fast': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
            'accurate': '--oem 1 --psm 3',
            'sparse': '--oem 3 --psm 11',
            'single_word': '--oem 3 --psm 8',
            'single_char': '--oem 3 --psm 10',
            'digits_only': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',
            'alpha_only': '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
        }
        
        # Default configuration
        self.default_config = 'fast'
        
        # Preprocessing parameters
        self.preprocessing_params = {
            'gaussian_blur_kernel': (3, 3),
            'morph_kernel_size': (3, 3),
            'dilation_iterations': 1,
            'erosion_iterations': 1
        }
        
        # Performance tracking
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_detections': 0,
            'total_time': 0.0
        }
        
        self.logger.info("OptimizedTextDetector initialized with cache size: %d", cache_size)
    
    def _get_image_hash(self, image_path: str) -> str:
        """Generate a hash for the image to use as cache key"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            return hashlib.md5(image_data).hexdigest()
        except Exception as e:
            self.logger.warning(f"Could not generate hash for {image_path}: {e}")
            return str(time.time())  # Fallback to timestamp
    
    def _manage_cache(self):
        """Remove oldest entries if cache exceeds size limit"""
        if len(self._cache) > self._cache_size:
            # Remove oldest 20% of entries
            remove_count = max(1, len(self._cache) // 5)
            oldest_keys = sorted(self._cache.keys(), 
                               key=lambda k: self._cache[k].get('timestamp', 0))[:remove_count]
            for key in oldest_keys:
                del self._cache[key]
    
    def _preprocess_image(self, image: np.ndarray, level: str = 'medium') -> np.ndarray:
        """
        Apply preprocessing to improve OCR accuracy
        
        Args:
            image: Input image
            level: Preprocessing level ('light', 'medium', 'heavy')
            
        Returns:
            Preprocessed image
        """
        if level == 'light':
            # Basic preprocessing
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            return cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        elif level == 'medium':
            # Enhanced preprocessing
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, self.preprocessing_params['gaussian_blur_kernel'], 0)
            
            # Apply adaptive threshold
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
            
            # Apply morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, 
                                             self.preprocessing_params['morph_kernel_size'])
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return processed
        
        elif level == 'heavy':
            # Advanced preprocessing
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Apply bilateral filter to reduce noise while preserving edges
            filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            # Apply adaptive threshold
            thresh = cv2.adaptiveThreshold(filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
            
            # Apply morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            processed = cv2.dilate(processed, kernel, 
                                 iterations=self.preprocessing_params['dilation_iterations'])
            processed = cv2.erode(processed, kernel, 
                                iterations=self.preprocessing_params['erosion_iterations'])
            
            return processed
        
        else:
            raise ValueError(f"Unknown preprocessing level: {level}")
    
    def _auto_detect_config(self, text_to_find: str) -> str:
        """
        Automatically detect the best Tesseract configuration based on text characteristics
        
        Args:
            text_to_find: The text we're looking for
            
        Returns:
            Best configuration name
        """
        text_lower = text_to_find.lower().strip()
        
        # Check for digits only
        if text_lower.isdigit():
            return 'digits_only'
        
        # Check for single character
        if len(text_lower) == 1:
            return 'single_char'
        
        # Check for single word
        if ' ' not in text_lower and len(text_lower) <= 15:
            return 'single_word'
        
        # Check for alphabetic only
        if text_lower.isalpha():
            return 'alpha_only'
        
        # Default to fast for mixed content
        return 'fast'
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def _group_nearby_matches(self, matches: List[Dict], proximity_threshold: int = 50) -> List[Dict]:
        """
        Group nearby text matches and remove duplicates
        
        Args:
            matches: List of text matches with coordinates
            proximity_threshold: Maximum distance to consider matches as duplicates
            
        Returns:
            Filtered list of matches
        """
        if not matches:
            return matches
        
        # Sort matches by confidence (highest first)
        sorted_matches = sorted(matches, key=lambda x: x.get('confidence', 0), reverse=True)
        
        filtered_matches = []
        
        for match in sorted_matches:
            is_duplicate = False
            match_x, match_y = match['center_x'], match['center_y']
            
            for existing in filtered_matches:
                existing_x, existing_y = existing['center_x'], existing['center_y']
                distance = np.sqrt((match_x - existing_x)**2 + (match_y - existing_y)**2)
                
                if distance < proximity_threshold:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered_matches.append(match)
        
        return filtered_matches
    
    def find_text(self, image_path: str, text_to_find: str, 
                  similarity_threshold: float = 0.7,
                  tesseract_config: Optional[str] = None,
                  preprocessing_level: str = 'medium',
                  use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        Find text in image with optimized processing
        
        Args:
            image_path: Path to the image file
            text_to_find: Text to search for
            similarity_threshold: Minimum similarity score (0.0-1.0)
            tesseract_config: Specific Tesseract configuration to use
            preprocessing_level: Level of image preprocessing ('light', 'medium', 'heavy')
            use_cache: Whether to use caching
            
        Returns:
            List of dictionaries containing match information
        """
        start_time = time.time()
        self.stats['total_detections'] += 1
        
        # Generate cache key
        cache_key = None
        if use_cache:
            image_hash = self._get_image_hash(image_path)
            cache_key = f"{image_hash}_{text_to_find}_{similarity_threshold}_{tesseract_config}_{preprocessing_level}"
            
            with self._cache_lock:
                if cache_key in self._cache:
                    self.stats['cache_hits'] += 1
                    self.logger.debug(f"Cache hit for {text_to_find}")
                    return self._cache[cache_key]['result']
                else:
                    self.stats['cache_misses'] += 1
        
        try:
            # Read and validate image
            img = cv2.imread(image_path)
            if img is None:
                self.logger.error(f"Could not read image at {image_path}")
                return []
            
            # Auto-detect configuration if not specified
            if tesseract_config is None:
                tesseract_config = self._auto_detect_config(text_to_find)
            
            config = self.configs.get(tesseract_config, self.configs[self.default_config])
            self.logger.debug(f"Using Tesseract config '{tesseract_config}': {config}")
            
            # Preprocess image
            processed_img = self._preprocess_image(img, preprocessing_level)
            
            # Perform OCR with detailed output
            data = pytesseract.image_to_data(processed_img, config=config, 
                                           output_type=pytesseract.Output.DICT)
            
            # Find matching text
            matches = []
            texts = data['text']
            confidences = data['conf']
            
            for i, detected_text in enumerate(texts):
                if detected_text.strip() and int(confidences[i]) > 30:  # Filter low confidence
                    similarity = self._calculate_similarity(detected_text, text_to_find)
                    
                    if similarity >= similarity_threshold:
                        # Calculate center coordinates
                        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        match_info = {
                            'center_x': center_x,
                            'center_y': center_y,
                            'detected_text': detected_text,
                            'similarity': similarity,
                            'confidence': int(confidences[i]),
                            'bbox': {'x': x, 'y': y, 'width': w, 'height': h},
                            'config_used': tesseract_config
                        }
                        matches.append(match_info)
            
            # Group nearby matches and remove duplicates
            filtered_matches = self._group_nearby_matches(matches)
            
            # Sort by similarity (highest first)
            filtered_matches.sort(key=lambda x: x['similarity'], reverse=True)
            
            # Cache the result
            if use_cache and cache_key:
                with self._cache_lock:
                    self._cache[cache_key] = {
                        'result': filtered_matches,
                        'timestamp': time.time()
                    }
                    self._manage_cache()
            
            processing_time = time.time() - start_time
            self.stats['total_time'] += processing_time
            
            self.logger.info(f"Found {len(filtered_matches)} matches for '{text_to_find}' in {processing_time:.3f}s")
            return filtered_matches
            
        except Exception as e:
            self.logger.error(f"Error in optimized text detection: {e}")
            return []
    
    def batch_find_text(self, image_paths: List[str], text_to_find: str,
                       similarity_threshold: float = 0.7,
                       max_workers: int = 4) -> Dict[str, List[Dict]]:
        """
        Process multiple images in parallel
        
        Args:
            image_paths: List of image file paths
            text_to_find: Text to search for
            similarity_threshold: Minimum similarity score
            max_workers: Maximum number of worker threads
            
        Returns:
            Dictionary mapping image paths to their results
        """
        results = {}
        
        def process_image(image_path):
            return image_path, self.find_text(image_path, text_to_find, similarity_threshold)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_image, path) for path in image_paths]
            
            for future in futures:
                try:
                    image_path, matches = future.result()
                    results[image_path] = matches
                except Exception as e:
                    self.logger.error(f"Error processing image in batch: {e}")
        
        return results
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache and performance statistics"""
        with self._cache_lock:
            cache_size = len(self._cache)
            hit_rate = (self.stats['cache_hits'] / 
                       max(1, self.stats['cache_hits'] + self.stats['cache_misses'])) * 100
            
            avg_time = (self.stats['total_time'] / 
                       max(1, self.stats['total_detections']))
            
            return {
                'cache_size': cache_size,
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses'],
                'hit_rate_percent': round(hit_rate, 2),
                'total_detections': self.stats['total_detections'],
                'average_time_seconds': round(avg_time, 3),
                'total_time_seconds': round(self.stats['total_time'], 3)
            }
    
    def clear_cache(self):
        """Clear the OCR result cache"""
        with self._cache_lock:
            self._cache.clear()
            self.logger.info("OCR cache cleared")
    
    def list_configurations(self) -> Dict[str, str]:
        """List available Tesseract configurations"""
        return {name: config for name, config in self.configs.items()}


def detect_text_with_tesseract_optimized(image_path: str, text_to_find: str, 
                                        output_dir: Optional[str] = None,
                                        similarity_threshold: float = 0.6) -> Optional[Tuple[int, int, Dict]]:
    """
    Optimized version of detect_text_with_tesseract function
    
    This function provides a drop-in replacement for the standard text detection
    with significant performance improvements.
    
    Args:
        image_path: Path to the screenshot image
        text_to_find: Text to search for in the image
        output_dir: Directory to save debug images (optional)
        similarity_threshold: Minimum similarity score (0.0-1.0)
        
    Returns:
        Tuple of (center_x, center_y, match_info) or None if not found
    """
    detector = OptimizedTextDetector()
    matches = detector.find_text(image_path, text_to_find, similarity_threshold)
    
    if matches:
        # Return the best match in the expected format
        best_match = matches[0]
        return (best_match['center_x'], best_match['center_y'], best_match)
    
    return None


# Global instance for backward compatibility
_global_detector = None

def get_optimized_detector() -> OptimizedTextDetector:
    """Get a global instance of the optimized text detector"""
    global _global_detector
    if _global_detector is None:
        _global_detector = OptimizedTextDetector()
    return _global_detector