import logging
import subprocess
import time
import json
from typing import Dict, List, Optional, Tuple, Any

logger = logging.getLogger(__name__)

class UIAutomator2Helper:
    """
    Helper class for UIAutomator2 fallback methods when primary Appium-based element identification fails.
    This provides direct access to UIAutomator2 functionality for enhanced element finding and interaction.
    """
    
    def __init__(self, device_id: str, controller=None):
        """
        Initialize UIAutomator2 helper
        
        Args:
            device_id (str): The device ID/serial number
            controller: The main device controller (optional)
        """
        self.device_id = device_id
        self.controller = controller
        self.logger = logging.getLogger(f"{__name__}.{device_id}")
        self.adb_available = False
        self._check_adb_availability()
        
    def _check_adb_availability(self):
        """Check if ADB is available and device is connected"""
        try:
            # First check if adb command exists
            result = subprocess.run(['adb', 'version'], capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                self.logger.warning("ADB command not found in PATH")
                return

            # Then check device connection
            result = subprocess.run(
                ['adb', '-s', self.device_id, 'shell', 'echo', 'test'],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0 and 'test' in result.stdout:
                self.adb_available = True
                self.logger.info(f"ADB connection confirmed for UIAutomator2 helper (device: {self.device_id})")
            else:
                self.logger.warning(f"ADB connection not available for device {self.device_id}: {result.stderr}")
        except FileNotFoundError:
            self.logger.warning("ADB command not found - please ensure Android SDK tools are installed")
        except subprocess.TimeoutExpired:
            self.logger.warning("ADB connection check timed out")
        except Exception as e:
            self.logger.warning(f"Error checking ADB availability: {e}")
            
    def _run_adb_command(self, command: List[str], timeout: int = 30) -> str:
        """
        Run an ADB command and return the output

        Args:
            command (List[str]): ADB command parts
            timeout (int): Command timeout in seconds

        Returns:
            str: Command output
        """
        if not self.adb_available:
            raise Exception("ADB not available")

        full_command = ['adb', '-s', self.device_id] + command
        try:
            result = subprocess.run(
                full_command, capture_output=True, text=True, timeout=timeout
            )
            if result.returncode != 0:
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                raise Exception(f"ADB command failed (code {result.returncode}): {error_msg}")
            return result.stdout.strip()
        except subprocess.TimeoutExpired:
            raise Exception(f"ADB command timed out after {timeout} seconds")

    def is_available(self) -> bool:
        """
        Check if the UIAutomator2 helper is available and ready for use

        Returns:
            bool: True if helper is ready, False otherwise
        """
        if not self.adb_available:
            # Try to recheck ADB availability
            self._check_adb_availability()

        return self.adb_available

    def test_ui_dump(self) -> bool:
        """
        Test if UI dump functionality is working

        Returns:
            bool: True if UI dump works, False otherwise
        """
        if not self.is_available():
            return False

        try:
            # Try a simple UI dump test with shorter timeout
            dump_command = ['shell', 'uiautomator', 'dump', '/data/local/tmp/ui_dump_test.xml']
            self._run_adb_command(dump_command, timeout=5)

            # Check if file was created
            check_command = ['shell', 'ls', '/data/local/tmp/ui_dump_test.xml']
            result = self._run_adb_command(check_command, timeout=3)

            if 'ui_dump_test.xml' in result:
                # Clean up test file
                cleanup_command = ['shell', 'rm', '/data/local/tmp/ui_dump_test.xml']
                self._run_adb_command(cleanup_command, timeout=3)
                return True
            else:
                return False

        except Exception as e:
            self.logger.warning(f"UI dump test failed: {e}")
            # Check if it's the SIGKILL issue (code 137)
            if "137" in str(e):
                self.logger.warning("UI dump failed with SIGKILL - device has security restrictions")
            return False

    def get_device_capabilities(self) -> dict:
        """
        Get device capabilities and restrictions

        Returns:
            dict: Device capability information
        """
        capabilities = {
            'adb_available': self.is_available(),
            'ui_dump_working': False,
            'coordinate_tap_available': False,
            'input_tap_available': False,
            'device_info': {}
        }

        if not self.is_available():
            return capabilities

        try:
            # Test UI dump capability
            capabilities['ui_dump_working'] = self.test_ui_dump()

            # Test coordinate tap capability
            try:
                # Just test the command format, don't actually tap
                test_result = self._run_adb_command(['shell', 'input', 'tap', '1', '1'], timeout=3)
                capabilities['coordinate_tap_available'] = True
            except:
                capabilities['coordinate_tap_available'] = False

            # Get device info
            try:
                android_version = self._run_adb_command(['shell', 'getprop', 'ro.build.version.release'], timeout=3)
                capabilities['device_info']['android_version'] = android_version

                device_model = self._run_adb_command(['shell', 'getprop', 'ro.product.model'], timeout=3)
                capabilities['device_info']['model'] = device_model

                api_level = self._run_adb_command(['shell', 'getprop', 'ro.build.version.sdk'], timeout=3)
                capabilities['device_info']['api_level'] = api_level
            except:
                pass

        except Exception as e:
            self.logger.warning(f"Error getting device capabilities: {e}")

        return capabilities
            
    def find_element_by_text(self, text: str, exact_match: bool = True) -> Optional[Dict]:
        """
        Find element by text using UIAutomator2 UiSelector
        
        Args:
            text (str): Text to search for
            exact_match (bool): Whether to use exact text match or contains
            
        Returns:
            Optional[Dict]: Element information if found
        """
        try:
            if exact_match:
                selector = f'new UiSelector().text("{text}")'
            else:
                selector = f'new UiSelector().textContains("{text}")'
                
            # Use UIAutomator2 to find element
            # First dump the UI hierarchy
            dump_command = ['shell', 'uiautomator', 'dump', '/sdcard/ui_dump.xml']
            self._run_adb_command(dump_command)

            # Then read the XML file
            cat_command = ['shell', 'cat', '/sdcard/ui_dump.xml']
            xml_output = self._run_adb_command(cat_command)
            
            # Parse XML to find element with text
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_output)
            
            for node in root.iter():
                if node.get('text') == text or (not exact_match and text in (node.get('text') or '')):
                    bounds = node.get('bounds')
                    if bounds:
                        # Parse bounds like "[x1,y1][x2,y2]"
                        import re
                        match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                        if match:
                            x1, y1, x2, y2 = map(int, match.groups())
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            
                            return {
                                'bounds': {'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2},
                                'center': {'x': center_x, 'y': center_y},
                                'text': node.get('text'),
                                'resource_id': node.get('resource-id'),
                                'class': node.get('class'),
                                'content_desc': node.get('content-desc'),
                                'clickable': node.get('clickable') == 'true'
                            }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding element by text with UIAutomator2: {e}")
            return None
            
    def find_element_by_resource_id(self, resource_id: str) -> Optional[Dict]:
        """
        Find element by resource ID using UIAutomator2
        
        Args:
            resource_id (str): Resource ID to search for
            
        Returns:
            Optional[Dict]: Element information if found
        """
        try:
            # First dump the UI hierarchy
            dump_command = ['shell', 'uiautomator', 'dump', '/sdcard/ui_dump.xml']
            self._run_adb_command(dump_command)

            # Then read the XML file
            cat_command = ['shell', 'cat', '/sdcard/ui_dump.xml']
            xml_output = self._run_adb_command(cat_command)
            
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_output)
            
            for node in root.iter():
                if node.get('resource-id') == resource_id:
                    bounds = node.get('bounds')
                    if bounds:
                        import re
                        match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                        if match:
                            x1, y1, x2, y2 = map(int, match.groups())
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            
                            return {
                                'bounds': {'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2},
                                'center': {'x': center_x, 'y': center_y},
                                'text': node.get('text'),
                                'resource_id': node.get('resource-id'),
                                'class': node.get('class'),
                                'content_desc': node.get('content-desc'),
                                'clickable': node.get('clickable') == 'true'
                            }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding element by resource ID with UIAutomator2: {e}")
            return None
            
    def find_element_by_content_desc(self, content_desc: str) -> Optional[Dict]:
        """
        Find element by content description using UIAutomator2
        
        Args:
            content_desc (str): Content description to search for
            
        Returns:
            Optional[Dict]: Element information if found
        """
        try:
            # First dump the UI hierarchy
            dump_command = ['shell', 'uiautomator', 'dump', '/sdcard/ui_dump.xml']
            self._run_adb_command(dump_command)

            # Then read the XML file
            cat_command = ['shell', 'cat', '/sdcard/ui_dump.xml']
            xml_output = self._run_adb_command(cat_command)
            
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_output)
            
            for node in root.iter():
                if node.get('content-desc') == content_desc:
                    bounds = node.get('bounds')
                    if bounds:
                        import re
                        match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                        if match:
                            x1, y1, x2, y2 = map(int, match.groups())
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            
                            return {
                                'bounds': {'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2},
                                'center': {'x': center_x, 'y': center_y},
                                'text': node.get('text'),
                                'resource_id': node.get('resource-id'),
                                'class': node.get('class'),
                                'content_desc': node.get('content-desc'),
                                'clickable': node.get('clickable') == 'true'
                            }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding element by content desc with UIAutomator2: {e}")
            return None
            
    def tap_at_coordinates(self, x: int, y: int) -> bool:
        """
        Tap at specific coordinates using ADB input tap
        
        Args:
            x (int): X coordinate
            y (int): Y coordinate
            
        Returns:
            bool: True if successful
        """
        try:
            # Prefer native uiautomator2 Python client click() over TouchAction/AirTest
            try:
                import uiautomator2 as u2
                d = u2.connect(self.device_id)
                d.click(int(x), int(y))
                self.logger.info(f"UIAutomator2.click(): Tapped at coordinates ({x}, {y})")
                return True
            except Exception as u2_err:
                # Fallback to ADB input tap if uiautomator2 python client is unavailable
                self.logger.warning(f"UIAutomator2 python client click failed, falling back to ADB: {u2_err}")
                command = ['shell', 'input', 'tap', str(x), str(y)]
                self._run_adb_command(command)
                self.logger.info(f"ADB input tap: Tapped at coordinates ({x}, {y})")
                return True
        except Exception as e:
            self.logger.error(f"Error tapping at coordinates with UIAutomator2/ADB: {e}")
            return False
            
    def input_text(self, text: str) -> bool:
        """
        Input text using ADB shell input
        
        Args:
            text (str): Text to input
            
        Returns:
            bool: True if successful
        """
        try:
            # Escape special characters and spaces
            escaped_text = text.replace(' ', '%s').replace('&', '\\&').replace('"', '\\"')
            command = ['shell', 'input', 'text', escaped_text]
            self._run_adb_command(command)
            self.logger.info(f"UIAutomator2: Input text '{text}'")
            return True
        except Exception as e:
            self.logger.error(f"Error inputting text with UIAutomator2: {e}")
            return False
            
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 300) -> bool:
        """
        Perform swipe gesture using ADB input swipe
        
        Args:
            start_x (int): Start X coordinate
            start_y (int): Start Y coordinate
            end_x (int): End X coordinate
            end_y (int): End Y coordinate
            duration (int): Swipe duration in milliseconds
            
        Returns:
            bool: True if successful
        """
        try:
            command = ['shell', 'input', 'swipe', str(start_x), str(start_y), str(end_x), str(end_y), str(duration)]
            self._run_adb_command(command)
            self.logger.info(f"UIAutomator2: Swiped from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            return True
        except Exception as e:
            self.logger.error(f"Error swiping with UIAutomator2: {e}")
            return False

    def find_element_by_uiselector_alternative(self, uiselector: str) -> Optional[Dict]:
        """
        Alternative method to find element using UISelector without UI dump
        Uses input tap with coordinate estimation

        Args:
            uiselector (str): UISelector string

        Returns:
            Optional[Dict]: Element information if found
        """
        try:
            # Parse the UISelector to understand what we're looking for
            attributes = self._parse_uiselector_attributes(uiselector)

            if not attributes:
                self.logger.warning("Could not parse UISelector attributes")
                return None

            # Try alternative approaches based on what we're looking for
            if 'description' in attributes:
                return self._find_by_content_description_alternative(attributes['description'])
            elif 'text' in attributes:
                return self._find_by_text_alternative(attributes['text'])
            elif 'resourceId' in attributes:
                return self._find_by_resource_id_alternative(attributes['resourceId'])
            else:
                self.logger.warning("UISelector contains unsupported attributes for alternative method")
                return None

        except Exception as e:
            self.logger.error(f"Alternative UISelector search failed: {e}")
            return None

    def _parse_uiselector_attributes(self, uiselector: str) -> Dict[str, str]:
        """Parse UISelector string to extract attributes"""
        import re
        attributes = {}

        # Find all method calls like .text("value") or .description("value") in the full string
        # Handle both single and double quotes, and escaped quotes
        pattern = r'\.(\w+)\(["\']([^"\']*)["\']'
        matches = re.findall(pattern, uiselector)

        for method, value in matches:
            attributes[method] = value

        return attributes

    def _find_by_content_description_alternative(self, content_desc: str) -> Optional[Dict]:
        """
        Alternative method to find element by content description
        Uses accessibility service queries if available
        """
        try:
            # Try using accessibility service to find element
            # This is a simplified approach that returns a mock element for coordinate tapping
            self.logger.info(f"Looking for element with content-desc: {content_desc}")

            # For now, return a mock element that suggests coordinate-based interaction
            # In a real implementation, this could use accessibility services or other methods
            return {
                'method': 'alternative_search',
                'content_desc': content_desc,
                'suggestion': 'Use coordinate-based tapping or Appium driver methods',
                'fallback_available': True
            }

        except Exception as e:
            self.logger.error(f"Alternative content description search failed: {e}")
            return None

    def _find_by_text_alternative(self, text: str) -> Optional[Dict]:
        """Alternative method to find element by text"""
        try:
            self.logger.info(f"Looking for element with text: {text}")
            return {
                'method': 'alternative_search',
                'text': text,
                'suggestion': 'Use coordinate-based tapping or Appium driver methods',
                'fallback_available': True
            }
        except Exception as e:
            self.logger.error(f"Alternative text search failed: {e}")
            return None

    def _find_by_resource_id_alternative(self, resource_id: str) -> Optional[Dict]:
        """Alternative method to find element by resource ID"""
        try:
            self.logger.info(f"Looking for element with resource-id: {resource_id}")
            return {
                'method': 'alternative_search',
                'resource_id': resource_id,
                'suggestion': 'Use coordinate-based tapping or Appium driver methods',
                'fallback_available': True
            }
        except Exception as e:
            self.logger.error(f"Alternative resource ID search failed: {e}")
            return None
