#!/usr/bin/env python3
"""
Example script demonstrating optimized OCR features for Android app

This script showcases the performance improvements and features of the
optimized Tesseract OCR implementation in the Android application.
"""

import os
import sys
import time
import logging
from typing import List, Dict

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from text_detector import TextDetector
    from ocr_config import get_ocr_config, OCRConfig
except ImportError:
    # Fallback for import issues
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    from text_detector import TextDetector
    try:
        from ocr_config import get_ocr_config, OCRConfig
    except ImportError:
        # Create a mock function if ocr_config is not available
        def get_ocr_config(config_name=None):
            return None
        OCRConfig = None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demonstrate_basic_usage():
    """Demonstrate basic OCR usage with optimization"""
    print("\n=== Basic OCR Usage Demo ===")
    
    # Initialize text detector with optimization enabled
    detector = TextDetector(use_optimized=True)
    
    # Example image path (replace with actual image)
    image_path = "path/to/your/screenshot.png"
    text_to_find = "Login"
    
    if not os.path.exists(image_path):
        logger.warning(f"Image file not found: {image_path}")
        print("Please update the image_path variable with a valid image file")
        return
    
    # Perform OCR
    start_time = time.time()
    results = detector.find_text(image_path, text_to_find)
    end_time = time.time()
    
    print(f"Found {len(results)} matches for '{text_to_find}'")
    print(f"Processing time: {end_time - start_time:.3f} seconds")
    
    for i, bbox in enumerate(results):
        print(f"  Match {i+1}: {bbox}")

def demonstrate_performance_comparison():
    """Compare optimized vs standard OCR performance"""
    print("\n=== Performance Comparison Demo ===")
    
    image_path = "path/to/your/screenshot.png"
    text_to_find = "Button"
    
    if not os.path.exists(image_path):
        logger.warning(f"Image file not found: {image_path}")
        return
    
    # Test with optimization
    detector_optimized = TextDetector(use_optimized=True)
    start_time = time.time()
    results_opt = detector_optimized.find_text(image_path, text_to_find)
    time_optimized = time.time() - start_time
    
    # Test without optimization
    detector_standard = TextDetector(use_optimized=False)
    start_time = time.time()
    results_std = detector_standard.find_text(image_path, text_to_find)
    time_standard = time.time() - start_time
    
    print(f"Optimized OCR: {time_optimized:.3f}s, {len(results_opt)} matches")
    print(f"Standard OCR:  {time_standard:.3f}s, {len(results_std)} matches")
    
    if time_standard > 0:
        speedup = time_standard / time_optimized
        print(f"Speedup: {speedup:.2f}x")

def demonstrate_cache_performance():
    """Demonstrate cache performance benefits"""
    print("\n=== Cache Performance Demo ===")
    
    detector = TextDetector(use_optimized=True)
    image_path = "path/to/your/screenshot.png"
    text_to_find = "Settings"
    
    if not os.path.exists(image_path):
        logger.warning(f"Image file not found: {image_path}")
        return
    
    # First run (cache miss)
    start_time = time.time()
    results1 = detector.find_text(image_path, text_to_find)
    time_first = time.time() - start_time
    
    # Second run (cache hit)
    start_time = time.time()
    results2 = detector.find_text(image_path, text_to_find)
    time_second = time.time() - start_time
    
    print(f"First run (cache miss):  {time_first:.3f}s")
    print(f"Second run (cache hit):  {time_second:.3f}s")
    
    if time_second > 0:
        cache_speedup = time_first / time_second
        print(f"Cache speedup: {cache_speedup:.2f}x")
    
    # Show cache statistics
    stats = detector.get_performance_stats()
    if stats:
        print(f"Cache stats: {stats}")

def demonstrate_batch_processing():
    """Demonstrate batch processing capabilities"""
    print("\n=== Batch Processing Demo ===")
    
    detector = TextDetector(use_optimized=True)
    
    # Example image paths (replace with actual images)
    image_paths = [
        "path/to/screenshot1.png",
        "path/to/screenshot2.png",
        "path/to/screenshot3.png"
    ]
    
    # Filter existing images
    existing_images = [path for path in image_paths if os.path.exists(path)]
    
    if not existing_images:
        logger.warning("No image files found for batch processing")
        print("Please update image_paths with valid image files")
        return
    
    text_to_find = "Click"
    
    # Batch processing
    start_time = time.time()
    results = detector.batch_find_text(existing_images, text_to_find)
    batch_time = time.time() - start_time
    
    print(f"Processed {len(existing_images)} images in {batch_time:.3f}s")
    print(f"Average time per image: {batch_time/len(existing_images):.3f}s")
    
    for image_path, matches in results.items():
        print(f"  {os.path.basename(image_path)}: {len(matches)} matches")

def demonstrate_custom_configuration():
    """Demonstrate custom OCR configuration"""
    print("\n=== Custom Configuration Demo ===")
    
    # Get custom configuration
    try:
        config = get_ocr_config('fast') if get_ocr_config else None
    except TypeError:
        # Handle case where get_ocr_config doesn't accept arguments
        config = get_ocr_config() if get_ocr_config else None
    
    detector = TextDetector(use_optimized=True)
    
    image_path = "path/to/your/screenshot.png"
    text_to_find = "Menu"
    
    if not os.path.exists(image_path):
        logger.warning(f"Image file not found: {image_path}")
        return
    
    try:
        # Use custom configuration
        start_time = time.time()
        results = detector.find_text(image_path, text_to_find)
        processing_time = time.time() - start_time
        
        print(f"Custom config results: {len(results)} matches in {processing_time:.3f}s")
        
        # List available configurations
        configs = detector.list_optimized_configurations()
        if configs:
            print(f"Available configurations: {list(configs.keys())}")
            
    except Exception as e:
        logger.error(f"Error with custom configuration: {e}")

def demonstrate_configuration_options():
    """Show available OCR configurations"""
    print("\n=== Available OCR Configurations ===")
    
    detector = TextDetector(use_optimized=True)
    configs = detector.list_optimized_configurations()
    
    if configs:
        for name, description in configs.items():
            print(f"  {name}: {description}")
    else:
        print("No optimized configurations available")

def main():
    """Main demonstration function"""
    print("Android OCR Optimization Demo")
    print("=============================")
    
    try:
        # Run all demonstrations
        demonstrate_basic_usage()
        demonstrate_performance_comparison()
        demonstrate_cache_performance()
        demonstrate_batch_processing()
        demonstrate_custom_configuration()
        demonstrate_configuration_options()
        
        print("\n=== Demo Complete ===")
        print("The optimized OCR implementation is ready for use in your Android app!")
        
    except Exception as e:
        logger.error(f"Demo error: {e}")
        print(f"Error during demonstration: {e}")

if __name__ == "__main__":
    main()