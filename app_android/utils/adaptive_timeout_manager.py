"""
Android Adaptive Timeout Manager

This module provides intelligent timeout management for Android automation,
implementing context-aware timeouts, cross-platform locator validation,
and performance-optimized element finding strategies.
"""

import re
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum


class ElementContext(Enum):
    """Element context types for adaptive timeout calculation"""
    CONDITIONAL = "conditional"  # tapIfExists, checkIfExists actions
    ACTION = "action"           # tap, text input, swipe actions
    NAVIGATION = "navigation"   # page transitions, app launches
    VERIFICATION = "verification"  # waitTill, exists checks


class LocatorComplexity(Enum):
    """Locator complexity levels for timeout calculation"""
    SIMPLE = "simple"      # ID, accessibility_id
    MODERATE = "moderate"  # class_name, name
    COMPLEX = "complex"    # xpath, uiselector


@dataclass
class TimeoutStrategy:
    """Timeout strategy configuration"""
    base_timeout: float
    max_timeout: float
    poll_frequency: float
    retry_count: int
    early_termination: bool


class AdaptiveTimeoutManager:
    """
    Manages adaptive timeouts for Android element finding operations
    with cross-platform locator validation and performance optimization
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # iOS locator patterns for cross-platform validation
        self.ios_locator_patterns = [
            r'XCUIElementType\w+',  # XCUIElementTypeButton, XCUIElementTypeTextField, etc.
            r'@name=',              # iOS accessibility identifier pattern
            r'@label=',             # iOS label pattern
            r'@value=',             # iOS value pattern
        ]
        
        # Android equivalent mappings for iOS locators
        self.ios_to_android_mapping = {
            'XCUIElementTypeButton': 'android.widget.Button',
            'XCUIElementTypeTextField': 'android.widget.EditText',
            'XCUIElementTypeSecureTextField': 'android.widget.EditText[@password="true"]',
            'XCUIElementTypeStaticText': 'android.widget.TextView',
            'XCUIElementTypeImage': 'android.widget.ImageView',
            'XCUIElementTypeCell': 'android.widget.LinearLayout',
            'XCUIElementTypeTable': 'android.widget.ListView',
            'XCUIElementTypeScrollView': 'android.widget.ScrollView',
            '@name=': '@content-desc=',
            '@label=': '@text=',
            '@value=': '@text=',
        }
        
        # Context-based timeout strategies
        self.timeout_strategies = {
            ElementContext.CONDITIONAL: TimeoutStrategy(
                base_timeout=3.0,
                max_timeout=10.0,
                poll_frequency=0.5,
                retry_count=1,
                early_termination=True
            ),
            ElementContext.ACTION: TimeoutStrategy(
                base_timeout=10.0,
                max_timeout=30.0,
                poll_frequency=0.3,
                retry_count=2,
                early_termination=False
            ),
            ElementContext.NAVIGATION: TimeoutStrategy(
                base_timeout=15.0,
                max_timeout=45.0,
                poll_frequency=0.5,
                retry_count=3,
                early_termination=False
            ),
            ElementContext.VERIFICATION: TimeoutStrategy(
                base_timeout=5.0,
                max_timeout=20.0,
                poll_frequency=0.3,
                retry_count=2,
                early_termination=True
            )
        }
        
        # Performance tracking
        self.performance_history = {}
        self.fast_fail_patterns = set()
        
    def validate_locator_for_android(self, locator_type: str, locator_value: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate locator for Android platform and detect iOS cross-contamination
        
        Args:
            locator_type: Type of locator (xpath, id, etc.)
            locator_value: Value of the locator
            
        Returns:
            Tuple of (is_valid, validation_message, suggested_android_locator)
        """
        if not locator_value:
            return False, "Empty locator value", None
            
        # Check for iOS locator patterns
        for pattern in self.ios_locator_patterns:
            if re.search(pattern, locator_value, re.IGNORECASE):
                self.logger.warning(f"iOS locator detected on Android: {locator_value}")
                
                # Attempt to convert to Android equivalent
                android_locator = self._convert_ios_to_android_locator(locator_value)
                
                return False, f"iOS locator '{locator_value}' not compatible with Android UIAutomator2", android_locator
        
        # Validate Android-specific patterns
        if locator_type.lower() == 'xpath':
            is_valid, message = self._validate_android_xpath(locator_value)
            return is_valid, message, None
        elif locator_type.lower() == 'id':
            is_valid, message = self._validate_android_id(locator_value)
            return is_valid, message, None
        elif locator_type.lower() == 'uiselector':
            is_valid, message = self._validate_uiselector(locator_value)
            return is_valid, message, None

        return True, "Valid Android locator", None
    
    def _convert_ios_to_android_locator(self, ios_locator: str) -> Optional[str]:
        """Convert iOS locator to Android equivalent"""
        android_locator = ios_locator
        
        # Apply mappings
        for ios_pattern, android_replacement in self.ios_to_android_mapping.items():
            android_locator = android_locator.replace(ios_pattern, android_replacement)
        
        # If conversion was made, return the result
        if android_locator != ios_locator:
            self.logger.info(f"Converted iOS locator to Android: {ios_locator} -> {android_locator}")
            return android_locator
            
        return None
    
    def _validate_android_xpath(self, xpath: str) -> Tuple[bool, str]:
        """Validate Android XPath locator"""
        # Check for iOS-specific elements
        if 'XCUIElementType' in xpath:
            return False, f"XPath contains iOS-specific XCUIElementType: {xpath}"
        
        # Check for valid Android elements
        android_elements = [
            'android.widget.', 'android.view.', 'android.webkit.',
            'androidx.', 'com.android.'
        ]
        
        if any(element in xpath for element in android_elements):
            return True, "Valid Android XPath"
        
        # Generic elements are also valid
        if any(element in xpath.lower() for element in ['button', 'edittext', 'textview', 'imageview']):
            return True, "Valid generic XPath"
            
        return True, "XPath appears valid for Android"
    
    def _validate_android_id(self, resource_id: str) -> Tuple[bool, str]:
        """Validate Android resource ID"""
        # Android resource IDs should follow package:id/name format
        if ':id/' in resource_id:
            return True, "Valid Android resource ID format"
        
        # Simple IDs without package are also valid
        if resource_id and not resource_id.startswith('@'):
            return True, "Valid simple Android ID"
            
        return False, f"Invalid Android resource ID format: {resource_id}"
    
    def _validate_uiselector(self, uiselector: str) -> Tuple[bool, str]:
        """Validate UISelector syntax"""
        # Basic UISelector validation
        if 'new UiSelector()' in uiselector or uiselector.startswith('.'):
            return True, "Valid UISelector syntax"

        return False, f"Invalid UISelector syntax: {uiselector}"

    def determine_element_context(self, action_type: str) -> ElementContext:
        """
        Determine element context based on action type

        Args:
            action_type: Type of action being performed

        Returns:
            ElementContext enum value
        """
        conditional_actions = [
            'tapIfLocatorExists', 'tapIfImageExists', 'tapIfTextExists',
            'checkIfExists', 'existsAction'
        ]

        navigation_actions = [
            'launchApp', 'terminateApp', 'switchToApp', 'goBack', 'goHome'
        ]

        verification_actions = [
            'waitTill', 'waitTillVisible', 'waitTillClickable'
        ]

        if action_type in conditional_actions:
            return ElementContext.CONDITIONAL
        elif action_type in navigation_actions:
            return ElementContext.NAVIGATION
        elif action_type in verification_actions:
            return ElementContext.VERIFICATION
        else:
            return ElementContext.ACTION

    def determine_locator_complexity(self, locator_type: str, locator_value: str) -> LocatorComplexity:
        """
        Determine locator complexity for timeout calculation

        Args:
            locator_type: Type of locator
            locator_value: Value of the locator

        Returns:
            LocatorComplexity enum value
        """
        if locator_type.lower() in ['id', 'accessibility_id']:
            return LocatorComplexity.SIMPLE
        elif locator_type.lower() in ['class_name', 'name']:
            return LocatorComplexity.MODERATE
        elif locator_type.lower() in ['xpath', 'uiselector']:
            # Check XPath complexity
            if locator_type.lower() == 'xpath':
                # Complex XPath indicators
                complex_indicators = ['//', '[', 'contains(', 'text()', 'following-sibling', 'preceding-sibling']
                if any(indicator in locator_value for indicator in complex_indicators):
                    return LocatorComplexity.COMPLEX
                else:
                    return LocatorComplexity.MODERATE
            else:
                return LocatorComplexity.COMPLEX
        else:
            return LocatorComplexity.MODERATE

    def calculate_adaptive_timeout(self, action_type: str, locator_type: str,
                                 locator_value: str, user_timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate adaptive timeout based on context, complexity, and performance history

        Args:
            action_type: Type of action being performed
            locator_type: Type of locator
            locator_value: Value of the locator
            user_timeout: User-specified timeout (if any)

        Returns:
            Dictionary with timeout configuration
        """
        # Determine context and complexity
        context = self.determine_element_context(action_type)
        complexity = self.determine_locator_complexity(locator_type, locator_value)

        # Get base strategy
        strategy = self.timeout_strategies[context]

        # Calculate base timeout
        base_timeout = strategy.base_timeout

        # Adjust for complexity
        complexity_multipliers = {
            LocatorComplexity.SIMPLE: 1.0,
            LocatorComplexity.MODERATE: 1.5,
            LocatorComplexity.COMPLEX: 2.0
        }

        adjusted_timeout = base_timeout * complexity_multipliers[complexity]

        # Apply user timeout if provided and reasonable
        if user_timeout is not None:
            if context == ElementContext.CONDITIONAL:
                # For conditional actions, respect user timeout exactly
                final_timeout = min(user_timeout, strategy.max_timeout)
            else:
                # For other actions, use user timeout but apply reasonable bounds
                final_timeout = max(min(user_timeout, strategy.max_timeout), strategy.base_timeout)
        else:
            final_timeout = min(adjusted_timeout, strategy.max_timeout)

        # Check performance history for this locator pattern
        locator_key = f"{locator_type}:{locator_value}"
        if locator_key in self.performance_history:
            history = self.performance_history[locator_key]
            if history['success_rate'] < 0.3:  # Low success rate
                final_timeout = min(final_timeout * 0.7, strategy.max_timeout)  # Reduce timeout
                self.logger.debug(f"Reduced timeout for low-success locator: {locator_key}")

        # Check for fast-fail patterns
        if locator_key in self.fast_fail_patterns:
            final_timeout = min(final_timeout, 5.0)  # Cap at 5 seconds for known failures
            self.logger.debug(f"Applied fast-fail timeout for pattern: {locator_key}")

        return {
            'timeout': final_timeout,
            'poll_frequency': strategy.poll_frequency,
            'retry_count': strategy.retry_count,
            'early_termination': strategy.early_termination,
            'context': context.value,
            'complexity': complexity.value,
            'strategy_used': f"{context.value}_{complexity.value}"
        }

    def should_fast_fail(self, locator_type: str, locator_value: str) -> bool:
        """
        Determine if a locator should fast-fail based on validation or history

        Args:
            locator_type: Type of locator
            locator_value: Value of the locator

        Returns:
            True if should fast-fail, False otherwise
        """
        # Check locator validation
        is_valid, message, _ = self.validate_locator_for_android(locator_type, locator_value)
        if not is_valid:
            self.logger.warning(f"Fast-fail due to invalid locator: {message}")
            return True

        # Check performance history
        locator_key = f"{locator_type}:{locator_value}"
        if locator_key in self.fast_fail_patterns:
            return True

        if locator_key in self.performance_history:
            history = self.performance_history[locator_key]
            if history['success_rate'] < 0.1 and history['attempt_count'] > 5:
                self.fast_fail_patterns.add(locator_key)
                self.logger.info(f"Added to fast-fail patterns: {locator_key}")
                return True

        return False

    def record_performance(self, locator_type: str, locator_value: str,
                          success: bool, duration: float, timeout_used: float):
        """
        Record performance metrics for a locator

        Args:
            locator_type: Type of locator
            locator_value: Value of the locator
            success: Whether the operation was successful
            duration: Actual duration of the operation
            timeout_used: Timeout that was used
        """
        locator_key = f"{locator_type}:{locator_value}"

        if locator_key not in self.performance_history:
            self.performance_history[locator_key] = {
                'success_count': 0,
                'attempt_count': 0,
                'total_duration': 0.0,
                'success_rate': 0.0,
                'avg_duration': 0.0,
                'last_success': None
            }

        history = self.performance_history[locator_key]
        history['attempt_count'] += 1
        history['total_duration'] += duration

        if success:
            history['success_count'] += 1
            history['last_success'] = time.time()

        # Update calculated metrics
        history['success_rate'] = history['success_count'] / history['attempt_count']
        history['avg_duration'] = history['total_duration'] / history['attempt_count']

        # Log performance insights
        if history['attempt_count'] % 10 == 0:  # Log every 10 attempts
            self.logger.info(f"Performance summary for {locator_key}: "
                           f"Success rate: {history['success_rate']:.2%}, "
                           f"Avg duration: {history['avg_duration']:.2f}s")

    def get_performance_insights(self) -> Dict[str, Any]:
        """
        Get performance insights and recommendations

        Returns:
            Dictionary with performance insights
        """
        insights = {
            'total_locators_tracked': len(self.performance_history),
            'fast_fail_patterns': len(self.fast_fail_patterns),
            'low_success_locators': [],
            'high_performance_locators': [],
            'recommendations': []
        }

        for locator_key, history in self.performance_history.items():
            if history['attempt_count'] >= 5:  # Only consider locators with sufficient data
                if history['success_rate'] < 0.3:
                    insights['low_success_locators'].append({
                        'locator': locator_key,
                        'success_rate': history['success_rate'],
                        'avg_duration': history['avg_duration']
                    })
                elif history['success_rate'] > 0.9 and history['avg_duration'] < 2.0:
                    insights['high_performance_locators'].append({
                        'locator': locator_key,
                        'success_rate': history['success_rate'],
                        'avg_duration': history['avg_duration']
                    })

        # Generate recommendations
        if insights['low_success_locators']:
            insights['recommendations'].append(
                f"Consider reviewing {len(insights['low_success_locators'])} low-success locators"
            )

        if insights['fast_fail_patterns']:
            insights['recommendations'].append(
                f"Fast-fail enabled for {insights['fast_fail_patterns']} problematic patterns"
            )

        return insights


# Global instance for use across the application
adaptive_timeout_manager = AdaptiveTimeoutManager()
