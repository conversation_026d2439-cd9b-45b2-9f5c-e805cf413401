"""
Method Selection Strategy for Android Automation

This module implements the strategy to use:
- AirTest ONLY for text recognition and image-based actions
- UIAutomator/Appium for all other actions

This prevents AirTest from triggering unwanted keyboards (like Yosemite keyboard).
"""

import logging

class MethodSelector:
    """Utility class to determine which automation method to use for different action types"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # AirTest usage minimized for stability - only for essential image-based operations
        # Restrict to core image detection functionality only
        self.airtest_actions = {
            'image_recognition',
            'image_matching',
            'template_matching',
            'find_image'
        }
        
        # Define action types that should NEVER use AirTest
        self.non_airtest_actions = {
            'tap',
            'click', 
            'swipe',
            'input_text',
            'type_text',
            'clear_text',
            'press_key',
            'device_back',
            'launch_app',
            'terminate_app',
            'restart_app',
            'wait',
            'scroll',
            'drag',
            'pinch',
            'zoom',
            'rotate',
            'shake',
            'volume_up',
            'volume_down',
            'home',
            'menu',
            'recent_apps'
        }
    
    def should_use_airtest(self, action_type, operation_type=None):
        """
        Determine if AirTest should be used for a given action
        
        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)
            
        Returns:
            bool: True if AirTest should be used, False otherwise
        """
        # Normalize action type
        action_type = action_type.lower() if action_type else ''
        operation_type = operation_type.lower() if operation_type else ''
        
        # Check if this is explicitly an AirTest operation
        if operation_type in self.airtest_actions:
            self.logger.info(f"Using AirTest for operation: {operation_type}")
            return True
            
        # Check if this action type should use AirTest
        if action_type in self.airtest_actions:
            self.logger.info(f"Using AirTest for action: {action_type}")
            return True
            
        # Check for specific patterns that indicate text/image operations
        # Be more specific to avoid false positives
        if operation_type:
            # Check operation type for specific AirTest operations (minimized for stability)
            airtest_operation_patterns = [
                'image_recognition',
                'template_matching',
                'find_image',
                'image_matching'
            ]

            for pattern in airtest_operation_patterns:
                if pattern in operation_type:
                    self.logger.info(f"Using AirTest for operation pattern: {pattern} in {operation_type}")
                    return True

        # Check action type for specific AirTest actions (minimized for stability)
        airtest_action_patterns = [
            'find_image',
            'template_match',
            'image_match'
        ]

        for pattern in airtest_action_patterns:
            if action_type == pattern:  # Exact match only
                self.logger.info(f"Using AirTest for exact action match: {pattern}")
                return True
        
        # Check if this action should explicitly NOT use AirTest
        if action_type in self.non_airtest_actions:
            self.logger.info(f"NOT using AirTest for action: {action_type}")
            return False
            
        # Default to NOT using AirTest to prevent unwanted keyboard triggers
        self.logger.info(f"Default: NOT using AirTest for action: {action_type}")
        return False
    
    def get_preferred_method(self, action_type, operation_type=None, available_methods=None):
        """
        Get the preferred automation method for a given action
        
        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)
            available_methods (list): List of available methods ['airtest', 'uiautomator', 'appium', 'adb']
            
        Returns:
            str: The preferred method to use
        """
        if available_methods is None:
            available_methods = ['uiautomator', 'appium', 'adb', 'airtest']
        
        # If AirTest should be used, prioritize it
        if self.should_use_airtest(action_type, operation_type):
            if 'airtest' in available_methods:
                return 'airtest'
        
        # For non-AirTest actions, prioritize UIAutomator/Appium
        method_priority = ['uiautomator', 'appium', 'adb']
        
        for method in method_priority:
            if method in available_methods:
                return method
                
        # Fallback to first available method
        return available_methods[0] if available_methods else 'uiautomator'
    
    def get_method_fallback_order(self, action_type, operation_type=None):
        """
        Get the fallback order of methods to try for a given action
        
        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)
            
        Returns:
            list: Ordered list of methods to try
        """
        # For text/image operations, try AirTest first
        if self.should_use_airtest(action_type, operation_type):
            return ['airtest', 'uiautomator', 'appium', 'adb']
        
        # For all other operations, avoid AirTest
        return ['uiautomator', 'appium', 'adb']
    
    def log_method_selection(self, action_type, operation_type, selected_method, reason=""):
        """
        Log the method selection decision for debugging
        
        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action
            selected_method (str): The method that was selected
            reason (str): Additional reason for the selection
        """
        self.logger.info(
            f"Method Selection: action='{action_type}', operation='{operation_type}', "
            f"selected='{selected_method}', reason='{reason}'"
        )

# Global instance for easy access
method_selector = MethodSelector()

def should_use_airtest(action_type, operation_type=None):
    """Convenience function to check if AirTest should be used"""
    return method_selector.should_use_airtest(action_type, operation_type)

def get_preferred_method(action_type, operation_type=None, available_methods=None):
    """Convenience function to get preferred method"""
    return method_selector.get_preferred_method(action_type, operation_type, available_methods)

def get_method_fallback_order(action_type, operation_type=None):
    """Convenience function to get method fallback order"""
    return method_selector.get_method_fallback_order(action_type, operation_type)
