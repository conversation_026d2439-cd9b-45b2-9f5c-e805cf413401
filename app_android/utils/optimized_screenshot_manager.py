# Optimized Screenshot Manager for Android Performance
# Implements Step 1 optimizations from ANDROID_PERFORMANCE_RECOMMENDATIONS.md

import os
import time
import base64
import hashlib
import threading
from typing import Optional, Dict, Any
from PIL import Image
import io
import logging

from app_android.config.performance_config import performance_config

class OptimizedScreenshotManager:
    """
    Optimized screenshot manager with caching, frequency limiting, and compression
    """
    
    def __init__(self, report_dir: str, device_controller=None):
        self.report_dir = report_dir
        self.device_controller = device_controller
        self.logger = logging.getLogger(__name__)
        
        # Create screenshots directory
        self.screenshots_dir = os.path.join(report_dir, "screenshots")
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # Performance tracking
        self.screenshot_count = 0
        self.cache_hits = 0
        self.frequency_skips = 0
        self.compression_savings = 0
        
        # Thread safety
        self._operation_lock = threading.Lock()
        
        self.logger.info(f"OptimizedScreenshotManager initialized with report_dir: {report_dir}")
    
    def save_screenshot(self, screenshot_data: Optional[bytes] = None, 
                      action_id: str = None, 
                      custom_name: str = None,
                      force_capture: bool = False) -> Optional[str]:
        """
        Save screenshot with optimization features
        
        Args:
            screenshot_data: Raw screenshot data (optional)
            action_id: Action ID for the screenshot
            custom_name: Custom filename
            force_capture: Force capture even if frequency limited
            
        Returns:
            str or None: Path to saved screenshot
        """
        start_time = time.time()
        
        try:
            with self._operation_lock:
                # Check if we should skip this screenshot due to frequency limiting
                if not force_capture and not performance_config.should_take_screenshot(action_id):
                    self.frequency_skips += 1
                    self.logger.debug(f"Screenshot skipped due to frequency limit (action_id: {action_id})")
                    
                    # Try to return cached screenshot if available
                    cached_path = performance_config.get_cached_screenshot(action_id)
                    if cached_path and os.path.exists(cached_path):
                        self.cache_hits += 1
                        return cached_path
                    
                    return None
                
                # Check cache first
                if action_id and not force_capture:
                    cached_path = performance_config.get_cached_screenshot(action_id)
                    if cached_path and os.path.exists(cached_path):
                        self.cache_hits += 1
                        self.logger.debug(f"Using cached screenshot for action_id: {action_id}")
                        return cached_path
                
                # Generate filename
                if custom_name:
                    filename = f"{custom_name}.png"
                elif action_id:
                    filename = f"{action_id}.png"
                else:
                    timestamp = int(time.time() * 1000)
                    filename = f"screenshot_{timestamp}.png"
                
                screenshot_path = os.path.join(self.screenshots_dir, filename)
                
                # Get screenshot data if not provided
                if screenshot_data is None:
                    if self.device_controller and hasattr(self.device_controller, 'take_screenshot'):
                        screenshot_data = self._capture_screenshot_data()
                        if screenshot_data is None:
                            self.logger.warning("Failed to capture screenshot data")
                            return None
                    else:
                        self.logger.warning("No screenshot data provided and no device controller available")
                        return None
                
                # Process and save screenshot
                success = self._save_screenshot_data(screenshot_data, screenshot_path)
                
                if success:
                    self.screenshot_count += 1
                    
                    # Update cache
                    if action_id:
                        performance_config.update_screenshot_cache(action_id, screenshot_path)
                    
                    # Log performance metrics
                    duration = time.time() - start_time
                    if duration > performance_config.performance_warning_threshold:
                        self.logger.warning(f"Screenshot save took {duration:.2f}s (threshold: {performance_config.performance_warning_threshold}s)")
                    
                    self.logger.debug(f"Screenshot saved: {screenshot_path} (took {duration:.2f}s)")
                    return screenshot_path
                else:
                    self.logger.error(f"Failed to save screenshot: {screenshot_path}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"Error in save_screenshot: {e}")
            return None
    
    def _capture_screenshot_data(self) -> Optional[bytes]:
        """
        Capture screenshot data from device controller
        
        Returns:
            bytes or None: Screenshot data
        """
        try:
            if hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                # Use Appium driver to get screenshot
                screenshot_base64 = self.device_controller.driver.get_screenshot_as_base64()
                return base64.b64decode(screenshot_base64)
            else:
                self.logger.warning("Device controller driver not available for screenshot")
                return None
        except Exception as e:
            self.logger.error(f"Error capturing screenshot data: {e}")
            return None
    
    def _save_screenshot_data(self, screenshot_data: bytes, screenshot_path: str) -> bool:
        """
        Save screenshot data to file with optional compression
        
        Args:
            screenshot_data: Raw screenshot data
            screenshot_path: Path to save the screenshot
            
        Returns:
            bool: True if successful
        """
        try:
            # Handle base64 encoded data
            if isinstance(screenshot_data, str):
                screenshot_data = base64.b64decode(screenshot_data)
            
            # Apply compression if enabled
            if performance_config.screenshot_compression_enabled:
                screenshot_data = self._compress_screenshot(screenshot_data)
            
            # Save to file
            with open(screenshot_path, 'wb') as f:
                f.write(screenshot_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving screenshot data: {e}")
            return False
    
    def _compress_screenshot(self, screenshot_data: bytes) -> bytes:
        """
        Compress screenshot data to reduce file size
        
        Args:
            screenshot_data: Original screenshot data
            
        Returns:
            bytes: Compressed screenshot data
        """
        try:
            # Load image
            image = Image.open(io.BytesIO(screenshot_data))
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Compress and save to bytes
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=performance_config.screenshot_compression_quality, optimize=True)
            compressed_data = output.getvalue()
            
            # Track compression savings
            original_size = len(screenshot_data)
            compressed_size = len(compressed_data)
            savings = original_size - compressed_size
            self.compression_savings += savings
            
            compression_ratio = (savings / original_size) * 100 if original_size > 0 else 0
            self.logger.debug(f"Screenshot compressed: {original_size} -> {compressed_size} bytes ({compression_ratio:.1f}% reduction)")
            
            return compressed_data
            
        except Exception as e:
            self.logger.warning(f"Screenshot compression failed, using original: {e}")
            return screenshot_data
    
    def take_screenshot_optimized(self, action_id: str = None, 
                                 custom_name: str = None,
                                 force_capture: bool = False) -> Optional[str]:
        """
        Take an optimized screenshot with all performance features
        
        Args:
            action_id: Action ID for the screenshot
            custom_name: Custom filename
            force_capture: Force capture even if frequency limited
            
        Returns:
            str or None: Path to saved screenshot
        """
        return self.save_screenshot(
            screenshot_data=None,
            action_id=action_id,
            custom_name=custom_name,
            force_capture=force_capture
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for this screenshot manager
        
        Returns:
            dict: Performance statistics
        """
        cache_hit_rate = (self.cache_hits / max(1, self.screenshot_count + self.cache_hits)) * 100
        
        return {
            'total_screenshots': self.screenshot_count,
            'cache_hits': self.cache_hits,
            'frequency_skips': self.frequency_skips,
            'cache_hit_rate_percent': round(cache_hit_rate, 2),
            'compression_savings_bytes': self.compression_savings,
            'screenshots_dir': self.screenshots_dir
        }
    
    def clear_cache(self):
        """
        Clear the screenshot cache
        """
        performance_config._screenshot_cache.clear()
        self.logger.info("Screenshot cache cleared")
    
    def cleanup(self):
        """
        Cleanup resources and log final statistics
        """
        stats = self.get_performance_stats()
        self.logger.info(f"OptimizedScreenshotManager cleanup - Stats: {stats}")
        self.clear_cache()