"""
Test Case Routes

This module contains all test case routes for the application.
"""

import os
import json
import base64
import time
import logging
import glob
import sys
import traceback
import shutil
import threading
import signal
import uuid
from pathlib import Path
from datetime import datetime
from functools import wraps

# Flask imports
from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session, Response
from werkzeug.utils import secure_filename

# PIL imports
import PIL.Image
import PIL.ImageDraw
import io

# Other imports
import xml.etree.ElementTree as ET
import requests
import re

# Import app instance from core
from app_android.core import app

# Import state variables
from app_android.core.state_manager import (
    device_controllers, players, action_factories,
    current_device, current_device_id, recording_actions,
    test_case_manager, test_suites_manager,
    get_session_device_id, get_session_id, get_client_session_id,
    TEST_CASES_DIR, REFERENCE_IMAGES_DIR, SCREENSHOTS_DIR,
    screenshots_dir, app_screenshots_dir,
    socketio,
    current_test_idx, current_step_idx, current_suite_id,
)

# Import utilities
from app_android.utils.appium_device_controller import AppiumDeviceController
from app_android.utils.recorder import Recorder
from app_android.utils.player import Player
from app_android.actions.action_factory import ActionFactory
from app_android.utils.reportGenerator import generateReport
from app_android.utils.custom_report_generator import generate_custom_report
from app_android.utils.screenshot_manager import screenshot_manager
from app_android.utils.directory_paths_db import directory_paths_db
from app_android.utils.environment_resolver import resolve_text_with_env_variables, get_resolved_variable_value

# Set up logger
logger = logging.getLogger(__name__)

# ============================================================================
# ROUTE HANDLERS
# ============================================================================

@app.route('/api/recording/start', methods=['POST'])
def start_recording():
    # Use session-specific device controller lookup
    session_id = get_session_id()
    client_session_id = get_client_session_id()
    device_id = get_session_device_id()

    if not device_id:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    # Create session-specific key
    session_device_key = f"{session_id}_{client_session_id}_{device_id}"

    # Get device controller using session-specific lookup
    device_controller = None
    if session_device_key in device_controllers:
        device_controller = device_controllers[session_device_key]
    elif device_id in device_controllers:
        device_controller = device_controllers[device_id]

    if not device_controller:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        # Initialize the recorder if not already initialized
        if not hasattr(device_controller, 'recorder'):
            device_controller.recorder = Recorder(device_controller)

        # Start recording
        device_controller.recorder.start_recording()

        return jsonify({
            "status": "recording_started",
            "message": "Recording started successfully"
        })
    except Exception as e:
        logger.error(f"Error starting recording: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/recording/stop', methods=['POST'])
def stop_recording():
    # Use session-specific device controller lookup
    session_id = get_session_id()
    client_session_id = get_client_session_id()
    device_id = get_session_device_id()

    if not device_id:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    # Create session-specific key
    session_device_key = f"{session_id}_{client_session_id}_{device_id}"

    # Get device controller using session-specific lookup
    device_controller = None
    if session_device_key in device_controllers:
        device_controller = device_controllers[session_device_key]
    elif device_id in device_controllers:
        device_controller = device_controllers[device_id]

    if not device_controller:
        return jsonify({"status": "error", "error": "No device connected"}), 400

    try:
        if not hasattr(device_controller, 'recorder') or not device_controller.recorder.is_recording:
            return jsonify({"status": "error", "error": "No recording in progress"}), 400

        # Stop recording and get the recorded actions
        recording_actions = device_controller.recorder.stop_recording()

        return jsonify({
            "status": "recording_stopped",
            "action_count": len(recording_actions)
        })
    except Exception as e:
        logger.error(f"Error stopping recording: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/recording/save', methods=['POST'])
def save_recording():
    """Save recording session to file"""
    global recording_actions, current_device, test_case_manager

    data = request.json
    name = data.get('name', '')
    actions = data.get('currentActions', recording_actions)
    is_save_as = data.get('isSaveAs', False)
    filename = data.get('filename', None)
    labels = data.get('labels', [])

    # Validate actions if provided in request
    if not actions:
        logger.error("No actions to save")
        return jsonify({"status": "error", "error": "No actions to save"}), 400

    # Validate name
    if not name:
        logger.error("No test case name provided")
        return jsonify({"status": "error", "error": "Please provide a test case name"}), 400

    try:
        logger.info(f"Saving test case: {name} with {len(actions)} actions (Save As: {is_save_as}, Filename: {filename if filename else 'new'})")

        # Log actions for debugging
        for i, action in enumerate(actions):
            if 'fallback_locators' in action:
                logger.info(f"Action {i+1} has {len(action['fallback_locators'])} fallback locators")
                for j, fallback in enumerate(action['fallback_locators']):
                    logger.info(f"  Fallback {j+1}: {fallback.get('locator_type')}={fallback.get('locator_value')}")

        # Create test case data
        test_case = {
            "name": name,
            "created": time.strftime("%Y-%m-%d %H:%M:%S"),
            "device_id": current_device,
            "actions": actions,
            "labels": labels
        }

        # Save using TestCaseManager - pass filename for direct saves
        if not is_save_as and filename:
            saved_filename = test_case_manager.save_test_case(test_case, filename=filename, is_save_as=is_save_as)
        else:
            saved_filename = test_case_manager.save_test_case(test_case, is_save_as=is_save_as)

        if not saved_filename:
            return jsonify({"status": "error", "error": "Failed to save test case"}), 500

        logger.info(f"Test case saved as {saved_filename}")
        return jsonify({
            "status": "saved",
            "filename": saved_filename,
            "message": f"Test case saved as {saved_filename}"
        })
    except Exception as e:
        logger.error(f"Error saving recording: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/recording/list', methods=['GET'])
def list_recordings():
    global test_case_manager

    try:
        logger.info("Received request to list test cases")

        # Note: In DB-only mode, directory presence is irrelevant, but keep log for visibility
        if not os.path.exists(TEST_CASES_DIR):
            logger.info(f"Test cases directory not used in DB-only mode: {TEST_CASES_DIR}")

        # Fetch test case metadata from DB
        test_cases_list = test_case_manager.get_test_cases() or []
        logger.info(f"Found {len(test_cases_list)} test case metadata entries from DB")

        detailed_test_cases = []
        for meta in test_cases_list:
            try:
                ident = meta.get('filename') or meta.get('test_case_id')
                if not ident:
                    logger.warning(f"Skipping test case meta without identifier: {meta}")
                    continue

                logger.info(f"Loading test case by identifier: {ident}")
                test_case = test_case_manager.load_test_case(str(ident))
                if test_case:
                    # Propagate identifier back for UI buttons
                    test_case['filename'] = meta.get('filename') or str(ident)
                    detailed_test_cases.append(test_case)
                else:
                    logger.warning(f"Failed to load test case: {ident}")
            except Exception as load_error:
                logger.error(f"Error loading test case {meta}: {str(load_error)}")
                continue

        logger.info(f"Successfully loaded {len(detailed_test_cases)} test cases")

        return jsonify({
            "status": "success",
            "test_cases": detailed_test_cases
        })
    except Exception as e:
        logger.error(f"Error listing test cases: {str(e)}")
        traceback.print_exc()
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/execute_test_case', methods=['POST'])
def execute_test_case():
    """
    Execute a test case by filename with retry logic based on global settings
    """
    global device_controller, current_device, player, test_case_manager, current_test_idx, current_step_idx, device_controllers, action_factories, players

    # Try to get device_id from request data first, then fall back to session
    data = request.get_json() or {}
    device_id = data.get('device_id') or get_session_device_id()

    if device_id:
        # Create session-specific device key using client session ID
        client_session_id = get_client_session_id()
        session_device_key = f"{device_id}_{client_session_id}"

        # Try session-specific first, then legacy
        if session_device_key in device_controllers:
            device_controller = device_controllers[session_device_key]
            player = players.get(session_device_key)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using session-specific controller for test execution: {session_device_key}")
        elif device_id in device_controllers:
            device_controller = device_controllers[device_id]
            player = players.get(device_id)
            current_device = device_id  # For backward compatibility
            logger.debug(f"Using legacy controller for test execution: {device_id}")
        else:
            # No device found in controllers
            logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
            return jsonify({
                'status': 'error',
                'error': 'No device connected'
            }), 400
    else:
        # No device found in controllers
        logger.warning(f"No device connected. Requested device ID: {device_id}, Session device ID: {get_session_device_id()}, Available devices: {list(device_controllers.keys())}")
        return jsonify({
            'status': 'error',
            'error': 'No device connected'
        }), 400

    try:
        # Get the filename from the request
        data = request.json
        if not data or 'filename' not in data:
            return jsonify({
                'status': 'error',
                'error': 'No filename provided'
            }), 400

        filename = data['filename']
        logger.info(f"=== EXECUTING TEST CASE: {filename} ===")

        # Get the Test Run Retry value from global settings
        import config
        max_retries = 0  # Default to no retries
        if hasattr(config, 'GLOBAL_VALUES') and isinstance(config.GLOBAL_VALUES, dict):
            if 'Test Run Retry' in config.GLOBAL_VALUES:
                try:
                    max_retries = int(config.GLOBAL_VALUES['Test Run Retry'])
                    logger.info(f"Test Run Retry value from global settings: {max_retries}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid Test Run Retry value in global settings: {config.GLOBAL_VALUES['Test Run Retry']}")

        # Initialize retry counter
        retry_count = 0
        success = False
        last_error = None

        # Find the correct suite_id for this test case from the database
        from app_android.utils.database import find_suite_id_for_test_case, get_test_case_id_mapping_for_suite, find_test_case_id_for_filename
        suite_id = find_suite_id_for_test_case(filename)

        if not suite_id:
            # Fallback: Generate a unique suite ID for this execution if not found
            import uuid
            suite_id = str(uuid.uuid4())
            logger.warning(f"Could not find suite_id for {filename}, generated fallback: {suite_id}")
        else:
            logger.info(f"Found suite_id for {filename}: {suite_id}")

        # ENHANCEMENT: Get test case ID mapping for proper execution tracking
        test_case_id_mapping = get_test_case_id_mapping_for_suite(suite_id)

        # Also get the specific test_case_id for this individual test case
        test_case_id = find_test_case_id_for_filename(filename)
        logger.info(f"Individual test case execution - test_case_id: {test_case_id}, mapping: {test_case_id_mapping}")

        # Reset any existing execution context to start fresh
        if hasattr(app, 'current_execution_id'):
            old_execution_id = app.current_execution_id
            app.current_execution_id = None
            logger.info(f"Reset previous execution_id: {old_execution_id}")

        # Set up execution context for test runs tracking
        execution_id = str(uuid.uuid4())  # UUID-based ID
        app.current_execution_id = execution_id
        app.current_suite_id = suite_id
        app.current_test_idx = 0
        app.current_test_case_name = filename
        app.current_test_case_id_mapping = test_case_id_mapping
        app.current_test_case_id = test_case_id

        # Set the global flag to indicate individual test case execution (not test suite)
        global is_test_suite_execution
        is_test_suite_execution = False
        logger.info(f"Set execution context - ID: {execution_id}, Suite: {suite_id}, Test: {filename}, is_test_suite_execution: False")

        # Import database tracking functions
        from app_android.utils.database import track_test_execution, get_test_execution_status

        # DATABASE-FIRST ARCHITECTURE: Do NOT clear execution_tracking table
        # Records persist indefinitely for historical analysis (30-day retention policy applies)
        logger.info("Preserving execution_tracking historical data (database-first architecture)")

        # Execute the test case with retries
        while retry_count <= max_retries and not success:
            # Update execution tracking in database
            track_test_execution(
                suite_id=suite_id,
                test_idx=0,  # Single test case execution always uses test_idx=0
                filename=filename,
                status="running",
                retry_count=retry_count,
                max_retries=max_retries,
                in_progress=True,
                test_case_id=test_case_id
            )

            if retry_count > 0:
                logger.info(f"=== RETRYING TEST CASE: {filename} (Attempt {retry_count} of {max_retries}) ===")

                # Notify the UI that we're retrying
                # Removed socketio emit for test retry
                # Give the UI time to update
                time.sleep(2)

                # Log that we're retrying but don't reset the database
                # This preserves screenshot history between retries
                logger.info("Preparing for retry without resetting database to preserve screenshots...")

            try:
                # Check database state before clearing
                from app_android.utils.database import check_database_state, clear_database, reset_database
                logger.info("Checking database state before clearing...")
                before_state = check_database_state()

                # Reset the database but preserve screenshots
                logger.info("Resetting database before test case execution (preserving screenshots)...")
                reset_success = reset_database()

                if reset_success:
                    logger.info("Successfully reset database before test case execution (screenshots preserved)")
                else:
                    # If reset fails, try the standard clear method
                    logger.warning("Database reset failed, trying standard clearing...")
                    clear_success = clear_database()

                    if not clear_success:
                        logger.error("BOTH reset_database AND clear_database failed - this may cause issues with test results")

                # Check database state after clearing
                logger.info("Checking database state after clearing...")
                after_state = check_database_state()
                logger.info(f"Database before: {before_state['suites_count']} suites, {before_state['cases_count']} cases, {before_state['steps_count']} steps, {before_state['screenshots_count']} screenshots")
                logger.info(f"Database after: {after_state['suites_count']} suites, {after_state['cases_count']} cases, {after_state['steps_count']} steps, {after_state['screenshots_count']} screenshots (screenshots are preserved)")

                # Final verification - we now expect screenshots to be preserved
                if after_state['screenshots_count'] > 0:
                    logger.info(f"Database contains {after_state['screenshots_count']} screenshots as expected (screenshots are preserved)")

                # Only perform emergency clearing if other tables still have data
                if after_state['suites_count'] > 0 or after_state['cases_count'] > 0 or after_state['steps_count'] > 0:
                    logger.error(f"CRITICAL: Database still contains data after clearing attempts: {after_state['suites_count']} suites, {after_state['cases_count']} cases, {after_state['steps_count']} steps")

                    # Last resort - try direct SQL with VACUUM
                    try:
                        import sqlite3
                        from app_android.utils.database import get_db_path

                        logger.info("Attempting emergency database clearing with VACUUM...")
                        conn = sqlite3.connect(get_db_path())

                        # Use PRAGMA to disable foreign keys and journal
                        conn.execute('PRAGMA foreign_keys = OFF')
                        conn.execute('PRAGMA journal_mode = OFF')

                        # Delete all data except screenshots
                        # conn.execute('DELETE FROM screenshots')  # Preserve screenshots - don't delete this table
                        conn.execute('DELETE FROM test_steps')
                        conn.execute('DELETE FROM test_cases')
                        conn.execute('DELETE FROM test_suites')

                        # Vacuum the database to reclaim space and reset
                        conn.execute('VACUUM')

                        # Restore settings
                        conn.execute('PRAGMA foreign_keys = ON')
                        conn.execute('PRAGMA journal_mode = DELETE')

                        conn.close()

                        logger.info("Emergency database clearing completed")

                        # Final check
                        final_state = check_database_state()
                        logger.info(f"Database after emergency clearing: {final_state['suites_count']} suites, {final_state['screenshots_count']} screenshots")
                    except Exception as db_error:
                        logger.error(f"Emergency database clearing also failed: {str(db_error)}")
                        # Continue execution anyway

                # Reset test_idx and step_idx for this execution
                # Start test_idx from 0 (first test case)
                # Start step_idx from 1 (first step) to match UI display
                current_test_idx.value = 0
                current_step_idx.value = 1
                logger.info(f"Reset test indices: test_idx={current_test_idx.value}, step_idx={current_step_idx.value}")

                # NOTE: Execution tracking records are now persistent and should NOT be cleared automatically
                # They should only be deleted through the UI as per user requirements
                # Reset execution tracking for this test case - DISABLED
                # try:
                #     from app_android.utils.database import reset_test_case_execution_tracking
                #     reset_test_case_execution_tracking(suite_id, current_test_idx.value)
                #     logger.info(f"Reset execution tracking for test case {current_test_idx.value} in suite {suite_id}")
                # except Exception as reset_error:
                #     logger.error(f"Error resetting execution tracking: {str(reset_error)}")
                logger.info(f"Execution tracking records will be preserved for test case {current_test_idx.value} in suite {suite_id}")

                # Load the test case
                test_case = test_case_manager.load_test_case(filename)
                if not test_case:
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="failed",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        error=f"Test case not found: {filename}",
                        in_progress=False,
                        test_case_id=test_case_id
                    )
                    return jsonify({
                        'status': 'error',
                        'error': f"Test case not found: {filename}",
                        'suite_id': suite_id
                    }), 404

                # Get the actions from the test case
                actions = test_case.get('actions', [])
                if not actions:
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="failed",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        error=f"No actions found in test case: {filename}",
                        in_progress=False,
                        test_case_id=test_case_id
                    )
                    return jsonify({
                        'status': 'error',
                        'error': f"No actions found in test case: {filename}",
                        'suite_id': suite_id
                    }), 400

                # Initialize player if needed with test_idx=0 for single test case execution
                if not player:
                    player = Player(device_controller=device_controller, test_cases_dir=TEST_CASES_DIR, test_idx=current_test_idx.value)
                    logger.info(f"Created player with test_idx={current_test_idx.value} for test case {filename}")
                else:
                    # Ensure the player has the correct test_idx
                    player.current_test_idx = current_test_idx.value
                    logger.info(f"Updated player's current_test_idx to {current_test_idx.value} for test case {filename}")
                    # We now use the database for tracking failures instead of last_error
                    logger.info("Using database for tracking failures in test case execution")

                # Set the current suite_id for screenshots
                global current_suite_id
                current_suite_id = suite_id

                # Execute the actions with test case boundaries
                # For individual test case execution, set start_index=0 and end_index=len(actions)
                # Explicitly pass the test_idx to ensure it's used consistently
                result, message = player.play(actions, suite_id=suite_id, test_case_start_index=0, test_case_end_index=len(actions), test_idx=current_test_idx.value)

                # Log the result for debugging
                logger.info(f"Test case execution result: success={result}, message={message}")

                # If execution was successful, set success flag to exit retry loop
                if result:
                    success = True
                    logger.info(f"Test case executed successfully on attempt {retry_count + 1}")

                    # Update execution tracking in database
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="passed",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        in_progress=False,
                        test_case_id=test_case_id
                    )
                else:
                    # If execution failed, store the error message and continue to next retry
                    last_error = message
                    logger.error(f"Test case execution failed on attempt {retry_count + 1}: {message}")

                    # Update execution tracking in database
                    track_test_execution(
                        suite_id=suite_id,
                        test_idx=0,
                        filename=filename,
                        status="failed" if retry_count >= max_retries else "retrying",
                        retry_count=retry_count,
                        max_retries=max_retries,
                        error=message,
                        in_progress=retry_count < max_retries,
                        test_case_id=test_case_id
                    )

                # Check database state after test execution
                logger.info("Checking database state after test execution...")
                from app_android.utils.database import check_database_state
                after_execution_state = check_database_state()
                logger.info(f"Database after test execution: {after_execution_state['suites_count']} suites, {after_execution_state['cases_count']} cases, {after_execution_state['steps_count']} steps, {after_execution_state['screenshots_count']} screenshots")

                # Do NOT clear screenshots after test execution - preserve them for retry
                # This was previously causing screenshots to be lost between retries
                # try:
                #     logger.info("Clearing screenshots after test execution...")
                #     clear_screenshots_response = clear_screenshots_route()
                #     logger.info(f"Screenshots cleared after test execution: {clear_screenshots_response.json}")
                # except Exception as clear_error:
                #     logger.error(f"Error clearing screenshots after test execution: {str(clear_error)}")

            except Exception as e:
                # If an exception occurred during execution, store the error and continue to next retry
                last_error = str(e)
                logger.error(f"Exception during test case execution on attempt {retry_count + 1}: {str(e)}")
                traceback.print_exc()

                # Update execution tracking in database
                track_test_execution(
                    suite_id=suite_id,
                    test_idx=0,
                    filename=filename,
                    status="error",
                    retry_count=retry_count,
                    max_retries=max_retries,
                    error=str(e),
                    in_progress=False,
                    test_case_id=test_case_id
                )

            # Increment retry counter
            retry_count += 1

        # After all retries, generate a report for the test case
        report_url = None
        zip_url = None
        report_id = None

        try:
            # Get the execution tracking data for this suite
            from app_android.utils.database import get_execution_tracking_for_suite
            global current_report_dir
            execution_data = get_execution_tracking_for_suite(suite_id)
            logger.info(f"Retrieved {len(execution_data)} execution tracking entries from database for report")

            # PRIORITY 1: Build data.json from execution tracker database (single source of truth)
            from app_android.utils.build_data_json import build_data_json_from_execution_tracker, build_data_json_from_test_case

            logger.info(f"Building data.json from execution tracker database for suite_id: {suite_id}")
            suite_data = build_data_json_from_execution_tracker(suite_id, execution_data)

            # FALLBACK: If execution tracker data is insufficient, try test case file
            if not suite_data:
                logger.warning("Failed to build data.json from execution tracker, falling back to test case file")

                # Get the full path to the test case file
                test_case_path = filename
                if not os.path.exists(test_case_path):
                    test_case_path = os.path.join('test_cases', filename)
                if not os.path.exists(test_case_path):
                    test_case_path = os.path.join('test_cases', f"{filename}.json")

                logger.info(f"Using test case path for data.json fallback: {test_case_path}")

                suite_data = build_data_json_from_test_case(
                    test_case_file=test_case_path,
                    suite_id=suite_id,
                    execution_data=execution_data,
                    success=success,
                    error=last_error if not success else None
                )

            if not suite_data:
                logger.error(f"Failed to build data.json from test case file: {filename}")
                # Fallback to using the database
                logger.info("Falling back to using the database for report generation")

                # Get the steps from the database for the report
                from app_android.utils.database import get_test_steps_for_suite
                test_steps = get_test_steps_for_suite(suite_id)
                logger.info(f"Retrieved {len(test_steps)} steps from database for report")

                # Try to enhance the steps with original test case data for INFO actions
                try:
                    # Load the original test case to get INFO action text
                    if os.path.exists(test_case_path):
                        with open(test_case_path, 'r') as f:
                            test_case = json.load(f)

                        # Create a mapping of action_id to original action data
                        action_map = {}
                        for action in test_case.get('actions', []):
                            action_id = action.get('action_id')
                            if action_id:
                                action_map[action_id] = action

                        # Enhance database steps with original action data
                        for step in test_steps:
                            step_action_id = step.get('action_id')
                            if step_action_id and step_action_id in action_map:
                                original_action = action_map[step_action_id]

                                # For INFO actions, add the original text content
                                if original_action.get('type') == 'info' and 'text' in original_action:
                                    step['text'] = original_action['text']
                                    step['original_text'] = original_action['text']
                                    step['name'] = original_action['text']  # Update step name
                                    logger.info(f"Enhanced INFO action step with text: {original_action['text']}")

                        logger.info("Successfully enhanced database steps with original test case data")
                except Exception as enhance_error:
                    logger.warning(f"Could not enhance steps with original test case data: {enhance_error}")

                # Create a screenshots map for the report based on action_id
                screenshots_map = {}
                for entry in execution_data:
                    if entry['action_id']:
                        # Use test_idx and step_idx as keys
                        key = f"{entry['test_idx']}_{entry['step_idx']}"
                        # Use action_id for the screenshot filename
                        screenshots_map[key] = f"{entry['action_id']}.png"
                        logger.info(f"Added screenshot mapping: {key} -> {entry['action_id']}.png")

                # Create a test suite data structure for the report
                suite_data = {
                    'name': f"Test Case: {os.path.basename(filename)}",
                    'id': suite_id,
                    'testCases': [
                        {
                            'name': os.path.basename(filename),
                            'id': filename,
                            'status': 'passed' if success else 'failed',
                            'duration': '0ms',  # We don't have accurate duration
                            'steps': test_steps  # Use the steps from the database
                        }
                    ],
                    'status': 'passed' if success else 'failed',
                    'passed': 1 if success else 0,
                    'failed': 0 if success else 1,
                    'skipped': 0,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'error': last_error if not success else None,
                    'screenshots_map': screenshots_map  # Add the screenshots map
                }

            # Make sure we have a report directory
            if not current_report_dir or not os.path.exists(current_report_dir):
                # Create report directory structure if it doesn't exist
                # Get reports folder from Settings tab configuration
                try:
                    from app_android.utils.directory_paths_db import directory_paths_db
                    reports_folder = directory_paths_db.get_path('REPORTS')
                    if not reports_folder:
                        # If not found in database, use config fallback
                        reports_folder = app.config.get('REPORTS_FOLDER')
                        if not reports_folder:
                            # Ultimate fallback - should not happen with proper validation
                            raise Exception("No reports folder configured in Settings tab")

                    # Ensure absolute path
                    if not os.path.isabs(reports_folder):
                        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                        reports_folder = os.path.join(base_dir, reports_folder)

                    logger.info(f"Using reports folder from Settings tab: {reports_folder}")
                except Exception as e:
                    logger.error(f"Error getting reports folder from Settings: {str(e)}")
                    return jsonify({
                        'status': 'error',
                        'error': 'Reports folder not configured in Settings tab. Please configure it before running tests.'
                    }), 400

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                testsuite_dir = f"testsuite_execution_{timestamp}"
                report_dir = os.path.join(reports_folder, testsuite_dir)
                screenshots_dir = os.path.join(report_dir, 'screenshots')

                # Create both directories
                os.makedirs(report_dir, exist_ok=True)
                os.makedirs(screenshots_dir, exist_ok=True)

                # Update global variables
                current_report_dir = report_dir
                current_screenshots_dir = screenshots_dir
                current_report_timestamp = timestamp

                logger.info(f"Created report directory for test case: {report_dir}")
                logger.info(f"Created screenshots directory: {screenshots_dir}")

            # Save test suite data to database
            try:
                from app_android.utils.database import save_test_suite
                # Add report directory to suite data
                suite_data['report_dir'] = current_report_dir
                # Save to database
                save_test_suite(suite_data)
                logger.info(f"Saved test suite data to database for single test case execution")
            except Exception as db_error:
                logger.error(f"Error saving test suite data to database: {str(db_error)}")
                traceback.print_exc()

            # Generate the report only if this is a test suite execution
            if is_test_suite_execution:
                try:
                    from app_android.utils.reportGenerator import generateReport
                    # Pass the test case file path to ensure correct action_ids are used
                    report_path, zip_path = generateReport(suite_data, test_case_path)
                    logger.info(f"Generated test case report: {report_path}")

                    # Store the report path for later retrieval
                    app.config['LATEST_REPORT_PATH'] = report_path

                    # Get the report directory (parent of the report file)
                    report_dir = os.path.dirname(report_path)
                    # Use the directory name as the report identifier
                    report_id = os.path.basename(report_dir)
                    # Set the report URL and ZIP URL
                    report_url = f"/reports/{report_id}/mainreport.html"
                    zip_url = f"/api/reports/download_zip/{os.path.basename(zip_path)}"

                    logger.info(f"Report URLs generated - report_url: {report_url}, zip_url: {zip_url}")
                except Exception as report_error:
                    logger.error(f"Error generating report: {str(report_error)}")
                    traceback.print_exc()
            else:
                logger.info("Skipping report generation - individual test case execution (not from test suite)")
                report_path = None
                zip_path = None
        except Exception as report_gen_error:
            logger.error(f"Error preparing report data: {str(report_gen_error)}")
            traceback.print_exc()

        # Complete the test execution and update final status
        try:
            from app_android.utils.database import update_test_execution_status
            final_status = 'completed' if success else 'failed'
            update_test_execution_status(execution_id, final_status)
            logger.info(f"Test execution {execution_id} marked as {final_status}")

            # Clear the session execution ID
            app.current_execution_id = None
            logger.info(f"Cleared session execution_id after test completion")
        except Exception as completion_error:
            logger.error(f"Error completing test execution: {str(completion_error)}")

        # After all retries, return appropriate response with report URLs if available
        response_data = {
            'status': 'success' if success else 'error',
            'message': f"Test case executed successfully: {filename}" if success else "Test case execution failed",
            'retry_count': retry_count - 1,  # Subtract 1 because we increment after success
            'suite_id': suite_id,
            'execution_id': execution_id
        }

        # Add error message if execution failed
        if not success:
            response_data['error'] = last_error or "Unknown error occurred during test execution"

        # Add report URLs if available
        if report_url:
            response_data['report_url'] = report_url
        if zip_url:
            response_data['zip_url'] = zip_url
        if report_id:
            response_data['report_id'] = report_id

        # Return with appropriate status code
        if success:
            return jsonify(response_data)
        else:
            return jsonify(response_data), 500

    except Exception as e:
        logger.error(f"Error in execute_test_case route: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500



@app.route('/api/test_cases/files', methods=['GET'])
def list_test_case_files():
    """List test case files from database and filesystem"""
    try:
        # Check if metadata is requested
        include_metadata = request.args.get('include_metadata', 'false').lower() == 'true'
        logger.info(f"list_test_case_files called with include_metadata={include_metadata}")

        if include_metadata:
            # Return full metadata including action counts
            test_cases_list = test_case_manager.get_test_cases() or []
            logger.info(f"Found {len(test_cases_list)} test cases with metadata from database")
            logger.info(f"Sample test case: {test_cases_list[0] if test_cases_list else 'None'}")
            return jsonify({
                "status": "success",
                "files": test_cases_list
            })
        else:
            # Original behavior - return just filenames
            test_cases_from_db = []
            try:
                test_cases_list = test_case_manager.get_test_cases()
                # Prefer filename if present; otherwise use test_case_id so DB-only mode works
                for tc in test_cases_list:
                    ident = tc.get('filename') or tc.get('test_case_id')
                    if ident:
                        test_cases_from_db.append(str(ident))
                logger.info(f"Found {len(test_cases_from_db)} test cases from database (identifiers)")
            except Exception as db_error:
                logger.warning(f"Could not load test cases from database: {db_error}")

            # DB-only mode: do not check filesystem
            all_files = list(sorted(set(test_cases_from_db)))

            return jsonify({
                "status": "success",
                "files": all_files
            })
    except Exception as e:
        logger.error(f"Error listing test case files: {str(e)}")
        return jsonify({"status": "error", "error": str(e)})



@app.route('/api/test_cases/load_file/<filename>', methods=['GET'])
def load_test_case_file(filename):
    """Disabled: File-based test case loading is not allowed in DB-only mode."""
    logger.info("/api/test_cases/load_file endpoint called but is disabled in DB-only mode")
    return jsonify({
        "status": "error",
        "error": "File-based test case loading is disabled. Use database-backed endpoints.",
        "mode": "DB_ONLY"
    }), 410



@app.route('/api/test_cases/load/<filename>', methods=['GET'])
def load_specific_test_case(filename):
    """Load a specific test case by filename"""
    global test_case_manager

    try:
        # Load the test case using TestCaseManager (database then file fallback)
        test_case = test_case_manager.load_test_case_hybrid(filename)

        if not test_case:
            return jsonify({
                "status": "error",
                "error": f"Test case not found: {filename}"
            }), 404

        # Process and validate actions, especially hook actions
        if 'actions' in test_case and test_case['actions']:
            for action in test_case['actions']:
                # Ensure action type is valid
                if 'type' in action:
                    # Special handling for hookAction
                    if action['type'] == 'hookAction':
                        logger.info(f"Processing hookAction in test case {filename}")
                        # Ensure hook_type is present
                        if 'hook_type' not in action:
                            logger.warning(f"hookAction missing hook_type in {filename}")
                            action['hook_type'] = 'tap'  # Default to tap as fallback
                else:
                    logger.warning(f"Action missing type in {filename}")
                    action['type'] = 'unknown'  # Add a default type

        return jsonify({
            "status": "success",
            "test_case": test_case
        })
    except Exception as e:
        logger.error(f"Error loading test case {filename}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/test_cases_for_multi_step', methods=['GET'])
def get_test_cases_for_multi_step():
    """Get all test cases for multi-step action dropdown"""
    global test_case_manager

    try:
        # Get all test cases (DB-backed metadata)
        test_cases_list = test_case_manager.get_test_cases()

        # Format test cases for dropdown
        formatted_test_cases = []
        for test_case in test_cases_list:
            identifier = test_case.get('test_case_id') or test_case['filename']

            # Prefer hybrid loader by canonical ID; fall back to DB metadata only
            full_test_case = None
            try:
                full_test_case = test_case_manager.load_test_case_hybrid(identifier)
            except Exception:
                full_test_case = None

            steps_count = (
                len(full_test_case.get('actions', [])) if full_test_case and isinstance(full_test_case.get('actions'), list)
                else int(test_case.get('step_count') or test_case.get('actions_count') or 0)
            )

            formatted_test_cases.append({
                "id": identifier,
                "test_case_id": test_case.get('test_case_id'),
                "filename": test_case.get('filename'),
                "name": test_case.get('name'),
                "steps_count": steps_count,
                "description": (full_test_case or {}).get("description", test_case.get("description", ""))
            })

        return jsonify({"test_cases": formatted_test_cases})
    except Exception as e:
        logger.error(f"Error getting test cases for multi-step: {str(e)}")
        return jsonify({"error": str(e)}), 500

# This endpoint was removed because it was a duplicate of the one above
# The original endpoint at line ~1800 is now the only one handling /api/reference_images



@app.route('/api/recording/rename', methods=['POST'])
def rename_recording():
    """Rename a test case by filename"""
    global test_case_manager

    data = request.json
    filename = data.get('filename')
    new_name = data.get('new_name')

    if not filename:
        return jsonify({"status": "error", "error": "No filename provided"}), 400

    if not new_name:
        return jsonify({"status": "error", "error": "No new name provided"}), 400

    try:
        # Call the manager's method to rename the test case
        result = test_case_manager.rename_test_case(filename, new_name)

        if result:
            logger.info(f"Renamed test case {filename} to '{new_name}'")
            return jsonify({
                "status": "success",
                "filename": result['filename'],
                "test_case": result['test_case']
            })
        else:
            logger.error(f"Failed to rename test case file: {filename}")
            return jsonify({"status": "error", "error": "Failed to rename test case file"}), 500
    except Exception as e:
        logger.error(f"Error renaming test case: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/recording/duplicate', methods=['POST'])
def duplicate_recording():
    global test_case_manager

    data = request.json
    filename = data.get('filename')
    # new_name = data.get('new_name') # Removed

    if not filename:
        return jsonify({"status": "error", "error": "No filename provided"}), 400

    # Removed check for new_name
    # if not new_name: ...

    try:
        # Call the manager's method which now handles naming internally
        new_filename = test_case_manager.duplicate_test_case(filename)

        if new_filename:
            # Load the duplicated file to get the actual name saved within it
            new_test_case_data = test_case_manager.load_test_case(new_filename)
            actual_new_name = new_test_case_data.get('name', '') if new_test_case_data else ''

            logger.info(f"Duplicated test case {filename} to {new_filename} (Name: {actual_new_name})")
            return jsonify({
                "status": "success",
                "new_filename": new_filename,
                "new_name": actual_new_name # Return the name from the file
            })
        else:
            logger.error(f"Failed to duplicate test case file: {filename}")
            return jsonify({"status": "error", "error": "Failed to duplicate test case file"}), 500

    except Exception as e:
        logger.error(f"Error duplicating test case file {filename}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/delete_test_case/<filename>', methods=['DELETE'])
def delete_test_case_route(filename):
    global test_case_manager
    try:
        logger.info(f"Attempting to delete test case via DELETE /api/ route: {filename}")
        success = test_case_manager.delete_test_case(filename)
        if success:
            logger.info(f"Successfully deleted {filename}")
            # Return a success response matching frontend expectation
            return jsonify({"success": True, "message": "Test case deleted successfully", "deleted_name": filename})
        else:
            logger.warning(f"delete_test_case method returned False for {filename}")
            # Consider 404 if file not found vs 500 for other errors
            return jsonify({"success": False, "message": "Failed to delete test case (not found or permission issue)"}), 404
    except Exception as e:
        logger.exception(f"Error in /api/delete_test_case route for {filename}: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# Test Cases Import/Export Routes


@app.route('/api/test_cases/export', methods=['POST'])
def export_test_cases():
    """Export all test cases to a ZIP file"""
    try:
        # Create temporary file for the ZIP
        import tempfile
        temp_fd, temp_path = tempfile.mkstemp(suffix='.zip')
        os.close(temp_fd)

        # Export test cases
        success = import_export_manager.export_test_cases(temp_path)

        if success:
            # Generate filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"test_cases_export_{timestamp}.zip"

            return send_file(
                temp_path,
                as_attachment=True,
                download_name=filename,
                mimetype='application/zip'
            )
        else:
            os.unlink(temp_path)
            return jsonify({"error": "Failed to export test cases"}), 500

    except Exception as e:
        logger.error(f"Error exporting test cases: {str(e)}")
        return jsonify({"error": str(e)}), 500



@app.route('/api/test_cases/import', methods=['POST'])
def import_test_cases():
    """Import test cases from a ZIP file"""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        if not file.filename.lower().endswith('.zip'):
            return jsonify({"error": "File must be a ZIP archive"}), 400

        # Get conflict resolution strategy
        conflict_resolution = request.form.get('conflict_resolution', 'skip')

        # Save uploaded file temporarily
        import tempfile
        temp_fd, temp_path = tempfile.mkstemp(suffix='.zip')
        os.close(temp_fd)

        try:
            file.save(temp_path)

            # Import test cases
            results = import_export_manager.import_test_cases(temp_path, conflict_resolution)

            return jsonify(results)

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except:
                pass

    except Exception as e:
        logger.error(f"Error importing test cases: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Test Suites Import/Export Routes


@app.route('/api/test_cases/search', methods=['POST'])
def search_test_cases():
    """Search test cases with multiple filter criteria"""
    try:
        data = request.get_json() or {}

        # Extract search criteria
        action_types = data.get('action_types', [])
        locator_types = data.get('locator_types', [])
        locator_value = data.get('locator_value', '')
        image_files = data.get('image_files', [])
        text_values = data.get('text_values', [])
        test_case_name = data.get('test_case_name', '')
        description = data.get('description', '')

        # Perform search
        results = test_case_manager.search_test_cases(
            action_types=action_types,
            locator_types=locator_types,
            locator_value=locator_value,
            image_files=image_files,
            text_values=text_values,
            test_case_name=test_case_name,
            description=description
        )

        return jsonify({
            "status": "success",
            "results": results
        })

    except Exception as e:
        logger.error(f"Error searching test cases: {str(e)}")
        return jsonify({"error": str(e)}), 500



@app.route('/api/test_cases/action_types', methods=['GET'])
def get_action_types():
    """Get all available action types from existing test cases"""
    try:
        action_types = test_case_manager.get_all_action_types()
        return jsonify({
            "status": "success",
            "action_types": action_types
        })
    except Exception as e:
        logger.error(f"Error getting action types: {str(e)}")
        return jsonify({"error": str(e)}), 500



@app.route('/api/test_cases/locator_types', methods=['GET'])
def get_locator_types():
    """Get all available locator types from existing test cases"""
    try:
        locator_types = test_case_manager.get_all_locator_types()
        return jsonify({
            "status": "success",
            "locator_types": locator_types
        })
    except Exception as e:
        logger.error(f"Error getting locator types: {str(e)}")
        return jsonify({"error": str(e)}), 500



@app.route('/api/test_cases/save_modified', methods=['POST'])
def save_modified_test_case():
    """Save a modified test case with validation"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        filename = data.get('filename')
        test_case_data = data.get('test_case')
        validate_only = data.get('validate_only', False)

        if not filename or not test_case_data:
            return jsonify({"error": "Filename and test case data are required"}), 400

        # Validate test case data
        validation_result = test_case_manager.validate_test_case(test_case_data)
        if not validation_result['valid']:
            return jsonify({
                "error": "Validation failed",
                "validation_errors": validation_result['errors']
            }), 400

        # If only validation was requested, return success
        if validate_only:
            return jsonify({
                "status": "success",
                "message": "Test case validation passed"
            })

        # Save the test case
        saved_filename = test_case_manager.save_test_case(test_case_data, filename=filename, is_save_as=False)

        if saved_filename:
            # Update database records if needed
            test_case_manager.update_database_after_modification(saved_filename, test_case_data)

            return jsonify({
                "status": "success",
                "message": "Test case saved successfully",
                "filename": saved_filename
            })
        else:
            return jsonify({"error": "Failed to save test case"}), 500

    except Exception as e:
        logger.error(f"Error saving modified test case: {str(e)}")
        return jsonify({"error": str(e)}), 500


# JSON Editor API Routes


@app.route('/api/test_cases/json_backup', methods=['POST'])
def create_json_backup():
    """Create a backup of test case JSON for JSON editor"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        filename = data.get('filename')
        json_data = data.get('json_data')

        if not filename or not json_data:
            return jsonify({"error": "Filename and JSON data are required"}), 400

        # Store backup in database
        from app_android.utils.database import get_db_path
        import sqlite3
        import json as json_module

        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO test_case_json_backups
            (test_case_filename, json_data, backup_timestamp, session_id)
            VALUES (?, ?, ?, ?)
        ''', (
            filename,
            json_module.dumps(json_data),
            datetime.now().isoformat(),
            'json_editor_session'
        ))

        conn.commit()
        conn.close()

        return jsonify({
            "status": "success",
            "message": "Backup created successfully"
        })

    except Exception as e:
        logger.error(f"Error creating JSON backup: {str(e)}")
        return jsonify({"error": str(e)}), 500




@app.route('/api/test_cases/validate_json', methods=['POST'])
def validate_json_test_case():
    """Validate JSON test case data"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        test_case_data = data.get('test_case')
        filename = data.get('filename')

        if not test_case_data:
            return jsonify({"error": "Test case data is required"}), 400

        # Validate test case data using existing validation
        validation_result = test_case_manager.validate_test_case(test_case_data)

        if validation_result['valid']:
            return jsonify({
                "status": "success",
                "message": "JSON validation passed"
            })
        else:
            return jsonify({
                "status": "error",
                "validation_errors": validation_result['errors']
            }), 400

    except Exception as e:
        logger.error(f"Error validating JSON test case: {str(e)}")
        return jsonify({"error": str(e)}), 500




@app.route('/api/test_cases/save_json', methods=['POST'])
def save_json_test_case():
    """Save JSON test case data"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        filename = data.get('filename')
        test_case_data = data.get('test_case')

        if not filename or not test_case_data:
            return jsonify({"error": "Filename and test case data are required"}), 400

        # Validate test case data first
        validation_result = test_case_manager.validate_test_case(test_case_data)
        if not validation_result['valid']:
            return jsonify({
                "status": "error",
                "validation_errors": validation_result['errors']
            }), 400

        # Add action IDs to any new actions
        from app_android.utils.id_generator import add_action_ids_to_test_case
        add_action_ids_to_test_case(test_case_data)

        # Save the test case
        saved_filename = test_case_manager.save_test_case(test_case_data, filename=filename, is_save_as=False)

        if saved_filename:
            # Update database records if needed
            test_case_manager.update_database_after_modification(saved_filename, test_case_data)

            return jsonify({
                "status": "success",
                "message": "Test case saved successfully",
                "filename": saved_filename
            })
        else:
            return jsonify({"error": "Failed to save test case"}), 500

    except Exception as e:
        logger.error(f"Error saving JSON test case: {str(e)}")
        return jsonify({"error": str(e)}), 500




@app.route('/api/test_cases/backup/<filename>', methods=['POST'])
def backup_test_case(filename):
    """Create a backup of a specific test case"""
    try:
        success = test_case_manager.backup_test_case(filename)

        if success:
            return jsonify({
                "status": "success",
                "message": f"Backup created for {filename}"
            })
        else:
            return jsonify({"error": "Failed to create backup"}), 500

    except Exception as e:
        logger.error(f"Error creating backup for {filename}: {str(e)}")
        return jsonify({"error": str(e)}), 500





@app.route('/api/test_cases/update_info', methods=['POST'])
def update_test_case_info():
    """Update test case information (description and notes)"""
    global test_case_manager

    data = request.json
    filename = data.get('filename')
    description = data.get('description')
    notes = data.get('notes')
    labels = data.get('labels', [])

    if not filename:
        return jsonify({"status": "error", "error": "No filename provided"}), 400

    try:
        # Load the existing test case
        test_case = test_case_manager.load_test_case(filename)
        if not test_case:
            return jsonify({"status": "error", "error": f"Test case not found: {filename}"}), 404

        # Update the information
        test_case['description'] = description
        test_case['notes'] = notes
        test_case['labels'] = labels
        test_case['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Save the updated test case
        saved_filename = test_case_manager.save_test_case(test_case, filename=filename, is_save_as=False)

        if saved_filename:
            logger.info(f"Updated test case information for {filename}")
            return jsonify({
                "status": "success",
                "message": "Test case information updated successfully"
            })
        else:
            logger.error(f"Failed to save updated test case file: {filename}")
            return jsonify({"status": "error", "error": "Failed to save test case file"}), 500
    except Exception as e:
        logger.error(f"Error updating test case information: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500



@app.route('/api/test_cases/files_with_metadata', methods=['GET'])
def list_test_cases_with_metadata():
    """List test cases with full metadata including action counts from database."""
    global test_case_manager

    try:
        logger.info("Received request to list test cases with metadata")

        # Get test case metadata from database
        test_cases_list = test_case_manager.get_test_cases() or []
        logger.info(f"Found {len(test_cases_list)} test cases with metadata from database")

        return jsonify({
            "status": "success",
            "files": test_cases_list
        })
    except Exception as e:
        logger.error(f"Error listing test cases with metadata: {str(e)}")
        return jsonify({"status": "error", "error": str(e)})



@app.route('/api/test_cases/<test_case_name>', methods=['GET'])
def get_test_case_by_name(test_case_name):
    """Get a test case by name for retry functionality"""
    try:
        from app_android.utils.directory_utils import get_test_cases_directory
        test_cases_dir = get_test_cases_directory()

        # Try different file name variations
        possible_names = [
            f"{test_case_name}.json",
            f"{test_case_name}",
            test_case_name
        ]

        test_case_file = None
        for name in possible_names:
            potential_path = os.path.join(test_cases_dir, name)
            if os.path.exists(potential_path):
                test_case_file = potential_path
                break

        if not test_case_file:
            # Try to find a file that starts with the test case name
            for filename in os.listdir(test_cases_dir):
                if filename.startswith(test_case_name) and filename.endswith('.json'):
                    test_case_file = os.path.join(test_cases_dir, filename)
                    break

        if not test_case_file:
            return jsonify({
                'status': 'error',
                'message': f'Test case not found: {test_case_name}'
            }), 404

        # Load the test case
        with open(test_case_file, 'r') as f:
            test_case = json.load(f)

        logger.info(f"Loaded test case {test_case_name} with {len(test_case.get('actions', []))} actions")

        return jsonify(test_case)

    except Exception as e:
        logger.error(f"Error loading test case {test_case_name}: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



# Directory paths management has been migrated to database storage
# All directory configuration is now handled through the centralized database system


