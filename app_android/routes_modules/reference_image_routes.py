"""
Reference Image Routes

This module contains all reference image routes for the application.
"""

import os
import json
import base64
import time
import logging
import glob
import sys
import traceback
import shutil
import threading
import signal
import uuid
from pathlib import Path
from datetime import datetime
from functools import wraps

# Flask imports
from flask import Flask, request, jsonify, render_template, send_file, send_from_directory, session, Response
from werkzeug.utils import secure_filename

# PIL imports
import PIL.Image
import PIL.ImageDraw
import io

# Other imports
import xml.etree.ElementTree as ET
import requests
import re

# Import app instance from core
from app_android.core import app

# Import state variables
from app_android.core.state_manager import (
    device_controllers, players, action_factories,
    current_device, current_device_id, recording_actions,
    test_case_manager, test_suites_manager,
    get_session_device_id, get_session_id, get_client_session_id,
    socketio,
)

# Import utilities
from app_android.utils.appium_device_controller import AppiumDeviceController
from app_android.utils.recorder import Recorder
from app_android.utils.player import Player
from app_android.actions.action_factory import ActionFactory
from app_android.utils.reportGenerator import generateReport
from app_android.utils.custom_report_generator import generate_custom_report
from app_android.utils.screenshot_manager import screenshot_manager
from app_android.utils.directory_paths_db import directory_paths_db
from app_android.utils.environment_resolver import resolve_text_with_env_variables, get_resolved_variable_value

# Set up logger
logger = logging.getLogger(__name__)

# ============================================================================
# ROUTE HANDLERS
# ============================================================================

@app.route('/api/reference_images', methods=['GET'])
def get_reference_images():
    """List Android reference images from the centralized Android DB only (strict filtering)."""
    try:
        from app_android.utils.database import list_reference_image_names_sorted
        from urllib.parse import quote

        names = list_reference_image_names_sorted() or []
        images = []
        for name in names:
            # path keeps backward-compat field name used by the UI dropdown
            images.append({
                'path': name,
                'preview': f"/api/reference_image_preview?name={quote(name)}"
            })

        return jsonify({
            'status': 'success',
            'images': images
        })
    except Exception as e:
        logger.error(f"Error getting reference images from Android DB: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error getting reference images: {str(e)}"
        }), 500



@app.route('/api/reference_image_preview', methods=['GET'])
def get_reference_image_preview():
    """Get a preview of a reference image.
    Android app: prefer centralized Android DB (strict). Fallback to filesystem only if 'path' is provided.
    """
    try:
        name = request.args.get('name')
        if name:
            # Read from Android DB BLOB
            try:
                import sqlite3
                from app_android.utils.database import get_db_path, ensure_reference_images_table
                ensure_reference_images_table()
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                # Support legacy column names
                cur.execute("PRAGMA table_info(reference_images)")
                cols = [row[1].lower() for row in cur.fetchall()]
                name_col = 'name' if 'name' in cols else ('image_name' if 'image_name' in cols else 'name')
                image_col = 'image' if 'image' in cols else ('image_data' if 'image_data' in cols else 'image')
                cur.execute(f"SELECT {image_col} FROM reference_images WHERE {name_col} = ? LIMIT 1", (name,))
                row = cur.fetchone(); conn.close()
                if not row or row[0] is None:
                    return jsonify({'status': 'error', 'message': 'Reference image not found in Android DB'}), 404
                blob = row[0]
                if isinstance(blob, memoryview):
                    blob = bytes(blob)
                encoded_string = base64.b64encode(blob).decode('utf-8')
                return jsonify({'status': 'success', 'preview': f'data:image/png;base64,{encoded_string}'})
            except Exception as db_err:
                logger.error(f"DB preview fetch failed for '{name}': {db_err}")
                return jsonify({'status': 'error', 'message': 'Failed to load image from Android DB'}), 500

        # Filesystem fallback path (legacy) when 'path' is explicitly provided
        image_path = request.args.get('path')
        if not image_path:
            return jsonify({'status': 'error', 'message': 'No image identifier provided'}), 400

        full_path = os.path.join(REFERENCE_IMAGES_DIR, image_path)
        if not os.path.exists(full_path):
            logger.warning(f"Reference image not found: {full_path}")
            return jsonify({'status': 'error', 'message': 'Reference image not found'}), 404

        with open(full_path, 'rb') as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        return jsonify({'status': 'success', 'preview': f"data:image/png;base64,{encoded_string}"})
    except Exception as e:
        logger.error(f"Error preparing reference image preview: {str(e)}")
        return jsonify({'status': 'error', 'message': f"Error generating preview: {str(e)}"}), 500



@app.route('/api/reference_images/save', methods=['POST'])
def save_reference_image_api():
    """Save a captured reference image to DB (BLOB) and filesystem (Android)."""
    try:
        data = request.get_json() or {}
        image_name = (data.get('image_name') or '').strip()
        image_data = data.get('image_data') or data.get('image_base64') or ''
        logger.info(f"[Android save_reference_image] received: name='{image_name}', data_len={len(image_data) if isinstance(image_data, str) else 'n/a'}")

        if not image_name:
            return jsonify({'status': 'error', 'message': 'Image name is required'}), 400

        # Normalize extension to .png by default
        if not image_name.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_name = f"{image_name}.png"

        # Extract base64 part if data URL was provided
        if ',' in image_data:
            image_data = image_data.split(',', 1)[1]
        if not image_data:
            return jsonify({'status': 'error', 'message': 'Image data is required'}), 400

        import base64
        try:
            image_bytes = base64.b64decode(image_data)
            logger.info(f"[Android save_reference_image] decoded_bytes={len(image_bytes)}")
        except Exception as dec_err:
            logger.exception(f"[Android save_reference_image] base64 decode failed: {dec_err}")
            return jsonify({'status': 'error', 'message': 'Invalid image data'}), 400

        # Duplicate checks with auto-increment rename: file system and DB
        from app_android.utils.database import ensure_reference_images_table, reference_image_exists, save_reference_image, get_db_path
        ensure_reference_images_table()

        # Determine a unique file name by appending _1, _2, ... if needed
        base, ext = os.path.splitext(image_name)
        if not ext:
            ext = '.png'
        candidate = f"{base}{ext}"
        file_path = os.path.join(REFERENCE_IMAGES_DIR, candidate)
        counter = 1
        while os.path.exists(file_path) or reference_image_exists(candidate):
            candidate = f"{base}_{counter}{ext}"
            file_path = os.path.join(REFERENCE_IMAGES_DIR, candidate)
            counter += 1
        if candidate != image_name:
            logger.info(f"[Android save_reference_image] Auto-renamed '{image_name}' to '{candidate}' to avoid duplicate")
        image_name = candidate

        # Log DB path and schema columns
        try:
            import sqlite3
            db_path = get_db_path()
            logger.info(f"[Android save_reference_image] db_path={db_path}, db_exists={os.path.exists(db_path)}, file_path={file_path}, ref_dir_exists={os.path.isdir(REFERENCE_IMAGES_DIR)}")
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            cur.execute("PRAGMA table_info(reference_images)")
            cols = [row[1] for row in cur.fetchall()]
            logger.info(f"[Android save_reference_image] reference_images columns: {cols}")
            conn.close()
        except Exception as pragma_err:
            logger.exception(f"[Android save_reference_image] PRAGMA or DB path check failed: {pragma_err}")

        # Save to DB (BLOB)
        if not save_reference_image(image_name, image_bytes):
            logger.error("[Android save_reference_image] save_reference_image returned False")
            return jsonify({'status': 'error', 'message': 'Failed to save image to database'}), 500

        # Also save to filesystem to preserve existing flows
        os.makedirs(REFERENCE_IMAGES_DIR, exist_ok=True)
        with open(file_path, 'wb') as f:
            f.write(image_bytes)

        # Build preview URL and relative path
        rel_path = os.path.relpath(file_path, REFERENCE_IMAGES_DIR)
        preview_url = f"/api/reference_image_preview?path={rel_path}"

        # Also record into screenshots table for audit/history
        try:
            from app_android.utils.database import save_screenshot_info
            save_screenshot_info(
                suite_id=None,
                test_idx=None,
                step_idx=None,
                filename=image_name,
                path=rel_path,
                action_id=f"refimg:{image_name}",
                custom_screenshot_name=image_name,
                custom_screenshot_filename=image_name,
                custom_screenshot_path=rel_path,
            )
        except Exception as _e:
            logger.warning(f"[Android save_reference_image] could not record screenshot meta: {_e}")

        return jsonify({'status': 'success', 'filename': image_name, 'path': rel_path, 'preview': preview_url})
    except Exception as e:
        logger.exception(f"Error saving reference image: {e}")
        return jsonify({'status': 'error', 'message': f'Internal server error: {str(e)}'}), 500


# API endpoints


@app.route('/api/reference_images/validate', methods=['POST'])
def validate_reference_image():
    """Validate if an image exists in the reference images directory"""
    try:
        data = request.get_json()
        image_name = data.get('image_name')

        if not image_name:
            return jsonify({
                'status': 'error',
                'message': 'No image name provided'
            }), 400

        # Check if it's an environment variable
        if image_name.startswith('env[') and image_name.endswith(']'):
            # Extract the variable name
            var_name = image_name[4:-1]

            # Get the current environment ID from session
            env_id = get_current_environment_id_from_session()

            if env_id:
                # Use the environment_resolver to get the resolved value
                resolved_value = get_resolved_variable_value(var_name, env_id)

                if resolved_value:
                    image_name = resolved_value
                else:
                    return jsonify({
                        'status': 'error',
                        'message': f'Environment variable not found: {var_name}'
                    }), 404
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'No active environment selected'
                }), 400

        # Get the full path to the image
        full_path = os.path.join(REFERENCE_IMAGES_DIR, image_name)

        # Check if the file exists
        if os.path.exists(full_path) and os.path.isfile(full_path):
            return jsonify({
                'status': 'success',
                'message': 'Image exists',
                'path': image_name
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'Image not found: {image_name}'
            }), 404
    except Exception as e:
        logger.error(f"Error validating reference image: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Error validating reference image: {str(e)}"
        }), 500



@app.route('/api/validate_screenshot_name', methods=['POST'])
def validate_screenshot_name():
    """
    Validate if a screenshot name is unique for test case creation
    """
    try:
        data = request.get_json()
        screenshot_name = data.get('screenshot_name')

        if not screenshot_name:
            return jsonify({
                'valid': False,
                'message': 'Screenshot name is required'
            }), 400

        # Import and use the validation method from TakeScreenshotAction
        from app_android.actions.take_screenshot_action import TakeScreenshotAction
        result = TakeScreenshotAction.validate_screenshot_name(screenshot_name)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error validating screenshot name: {str(e)}")
        return jsonify({
            'valid': True,
            'message': 'Unable to validate, but proceeding'
        }), 200

# ... (the rest of app.py, including action execution routes that will be modified next) ...

# Example of where to integrate resolve_text_with_env_variables
# This will be done more thoroughly in the next step.
# For instance, in `/api/action/text`:
# @app.route('/api/action/text', methods=['POST'])
# def text_action():
#     data = request.get_json()
#     text_to_input = data.get('text')
#     # ... other params ...
#
#     current_env_id = get_current_environment_id_from_session()
#     if current_env_id is not None:
#         text_to_input = resolve_text_with_env_variables(text_to_input, current_env_id)
#         # also resolve other string parameters if they can contain env vars
#
#     # ... rest of the text_action logic ...
#     return jsonify({"status": "success", "message": f"Text input action prepared for '{text_to_input}'"}), 200

# Make sure to set a secret key for session management
# app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'your_very_secret_key_here') # Already added earlier

# Add this new endpoint after the existing report endpoints



@app.route('/api/reference_images/metadata', methods=['GET'])
def reference_image_metadata_android():
    try:
        name = request.args.get('name')
        path_q = request.args.get('path')
        if not name and path_q:
            name = os.path.basename(path_q)
        if not name:
            return jsonify({'status':'error','message':'name or path is required'}), 400
        import sqlite3
        from app_android.utils.database import get_db_path
        db_path = get_db_path()
        meta = {'name': name}
        try:
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            cur.execute("PRAGMA table_info(reference_images)")
            cols = [r[1].lower() for r in cur.fetchall()]
            name_col = 'image_name' if 'image_name' in cols else ('name' if 'name' in cols else 'image_name')
            cur.execute(f"SELECT {name_col}, LENGTH(image_data), image_format, file_size, checksum, created_at FROM reference_images WHERE {name_col} = ?", (name,))
            row = cur.fetchone()
            if row:
                meta.update({'name': row[0], 'size': row[1], 'format': row[2], 'file_size': row[3], 'checksum': row[4], 'created_at': row[5]})
            conn.close()
        except Exception as _e:
            logger.warning(f"[Android reference_image_metadata] DB lookup failed: {_e}")
        full_path = os.path.join(REFERENCE_IMAGES_DIR, name)
        meta['path'] = name
        meta['exists_on_disk'] = os.path.exists(full_path)
        if os.path.exists(full_path):
            meta['file_size_bytes'] = os.path.getsize(full_path)
            meta['created_fs'] = int(os.path.getmtime(full_path))
        meta['preview'] = f"/api/reference_image_preview?path={name}"
        return jsonify({'status':'success','metadata': meta})
    except Exception as e:
        logger.exception(f"reference_image_metadata failed: {e}")
        return jsonify({'status':'error','message':str(e)}), 500



@app.route('/api/reference_images/delete', methods=['POST','DELETE'])
def reference_image_delete_android():
    try:
        data = request.get_json(silent=True) or {}
        name = (data.get('image_name') or data.get('name') or '').strip() if isinstance(data, dict) else ''
        if not name:
            name = request.args.get('name') or ''
        if not name:
            path_q = request.args.get('path')
            if path_q:
                name = os.path.basename(path_q)
        if not name:
            return jsonify({'status':'error','message':'image_name is required'}), 400
        import sqlite3
        from app_android.utils.database import get_db_path
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("PRAGMA table_info(reference_images)")
        cols = [r[1].lower() for r in cur.fetchall()]
        name_col = 'image_name' if 'image_name' in cols else ('name' if 'name' in cols else 'image_name')
        cur.execute(f"DELETE FROM reference_images WHERE {name_col} = ?", (name,))
        conn.commit(); conn.close()
        full_path = os.path.join(REFERENCE_IMAGES_DIR, name)
        if os.path.exists(full_path):
            os.remove(full_path)
        return jsonify({'status':'success','message':f"Image '{name}' deleted"})
    except Exception as e:
        logger.exception(f"reference_image_delete failed: {e}")
        return jsonify({'status':'error','message':str(e)}), 500


# ============================================================================
# DATABASE-ONLY API ENDPOINTS
# ============================================================================

