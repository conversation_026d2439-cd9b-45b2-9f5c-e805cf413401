#!/usr/bin/env python3
"""
Test script for database-only execution tracking.
Verifies that all components work correctly.
"""

import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.database_execution_tracker import DatabaseExecutionTracker
from utils.screenshot_manager_db import ScreenshotManagerDB
from utils.report_generator_db import ReportGeneratorDB
from utils.database import get_db_path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_database_execution_tracker():
    """Test DatabaseExecutionTracker"""
    print("\n" + "=" * 80)
    print("Testing DatabaseExecutionTracker")
    print("=" * 80)
    
    try:
        tracker = DatabaseExecutionTracker()
        
        # Test 1: Start execution
        print("\n1. Testing start_execution()...")
        execution_id = tracker.start_execution(
            suite_id='test_suite_001',
            suite_name='Test Suite 1',
            platform='Android'
        )
        print(f"   ✅ Execution started: {execution_id}")
        
        # Test 2: Track steps
        print("\n2. Testing track_step()...")
        for i in range(3):
            step_id = tracker.track_step(
                execution_id=execution_id,
                test_case_id='test_case_001',
                step_idx=i,
                action_type='click' if i % 2 == 0 else 'input',
                status='passed',
                error_message=None,
                screenshot_filename=f'screenshot_{i}.png',
                action_id=f'action_{i}',
                suite_id='test_suite_001',
                filename='test_case_001.json'
            )
            print(f"   ✅ Step {i} tracked: ID={step_id}")
        
        # Test 3: Complete execution
        print("\n3. Testing complete_execution()...")
        report_data = tracker.complete_execution(execution_id, status='completed')
        print(f"   ✅ Execution completed")
        print(f"   Report data keys: {list(report_data.keys())}")
        print(f"   Total test cases: {report_data.get('summary', {}).get('total_tests', 0)}")
        print(f"   Total steps: {report_data.get('summary', {}).get('total_steps', 0)}")
        
        # Test 4: Get execution data
        print("\n4. Testing get_execution_data()...")
        execution_data = tracker.get_execution_data(execution_id)
        if execution_data:
            print(f"   ✅ Execution data retrieved")
            print(f"   Execution ID: {execution_data.get('execution_id')}")
            print(f"   Status: {execution_data.get('status')}")
        else:
            print(f"   ❌ Failed to retrieve execution data")
        
        print("\n✅ DatabaseExecutionTracker tests PASSED")
        return True
        
    except Exception as e:
        print(f"\n❌ DatabaseExecutionTracker tests FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_screenshot_manager_db():
    """Test ScreenshotManagerDB"""
    print("\n" + "=" * 80)
    print("Testing ScreenshotManagerDB")
    print("=" * 80)
    
    try:
        screenshot_manager = ScreenshotManagerDB()
        
        # Test 1: Create a dummy screenshot
        print("\n1. Creating dummy screenshot...")
        import io
        from PIL import Image
        
        # Create a simple test image
        img = Image.new('RGB', (800, 600), color='red')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        screenshot_data = img_bytes.getvalue()
        print(f"   ✅ Created test image: {len(screenshot_data)} bytes")
        
        # Test 2: Compress screenshot
        print("\n2. Testing compress_screenshot()...")
        compressed_data, compression_ratio = screenshot_manager.compress_screenshot(screenshot_data)
        print(f"   ✅ Compressed: {len(screenshot_data)} -> {len(compressed_data)} bytes")
        print(f"   Compression ratio: {compression_ratio:.1f}%")
        
        # Test 3: Save screenshot to database
        print("\n3. Testing save_screenshot_to_db()...")
        success = screenshot_manager.save_screenshot_to_db(
            screenshot_data=compressed_data,
            filename='test_screenshot.png',
            execution_id='test_exec_001',
            test_case_id='test_case_001',
            action_id='action_001',
            original_size=len(screenshot_data),
            compressed_size=len(compressed_data),
            suite_id='test_suite_001'
        )
        if success:
            print(f"   ✅ Screenshot saved to database")
        else:
            print(f"   ❌ Failed to save screenshot")
        
        # Test 4: Retrieve screenshot from database
        print("\n4. Testing get_screenshot_from_db()...")
        retrieved_data = screenshot_manager.get_screenshot_from_db('test_screenshot.png')
        if retrieved_data:
            print(f"   ✅ Screenshot retrieved: {len(retrieved_data)} bytes")
        else:
            print(f"   ❌ Failed to retrieve screenshot")
        
        # Test 5: Get screenshot as base64
        print("\n5. Testing get_screenshot_base64()...")
        base64_data = screenshot_manager.get_screenshot_base64('test_screenshot.png')
        if base64_data:
            print(f"   ✅ Screenshot as base64: {len(base64_data)} characters")
            print(f"   Starts with: {base64_data[:50]}...")
        else:
            print(f"   ❌ Failed to get base64 screenshot")
        
        print("\n✅ ScreenshotManagerDB tests PASSED")
        return True
        
    except Exception as e:
        print(f"\n❌ ScreenshotManagerDB tests FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_report_generator_db():
    """Test ReportGeneratorDB"""
    print("\n" + "=" * 80)
    print("Testing ReportGeneratorDB")
    print("=" * 80)
    
    try:
        report_generator = ReportGeneratorDB()
        
        # First create a test execution
        tracker = DatabaseExecutionTracker()
        execution_id = tracker.start_execution(
            suite_id='test_suite_002',
            suite_name='Test Suite 2',
            platform='Android'
        )
        
        # Add some steps
        for i in range(2):
            tracker.track_step(
                execution_id=execution_id,
                test_case_id='test_case_002',
                step_idx=i,
                action_type='click',
                status='passed',
                screenshot_filename=f'screenshot_{i}.png',
                action_id=f'action_{i}',
                suite_id='test_suite_002',
                filename='test_case_002.json'
            )
        
        # Complete execution
        report_data = tracker.complete_execution(execution_id, status='completed')
        
        # Test 1: Get report data from database
        print("\n1. Testing get_report_data_from_db()...")
        retrieved_report_data = report_generator.get_report_data_from_db(execution_id)
        if retrieved_report_data:
            print(f"   ✅ Report data retrieved")
            print(f"   Test cases: {len(retrieved_report_data.get('test_cases', []))}")
        else:
            print(f"   ❌ Failed to retrieve report data")
        
        # Test 2: Generate HTML report
        print("\n2. Testing generate_html_from_database()...")
        html_content = report_generator.generate_html_from_database(execution_id)
        if html_content:
            print(f"   ✅ HTML report generated: {len(html_content)} characters")
            print(f"   Contains 'Test Execution Report': {'Test Execution Report' in html_content}")
        else:
            print(f"   ❌ Failed to generate HTML report")
        
        print("\n✅ ReportGeneratorDB tests PASSED")
        return True
        
    except Exception as e:
        print(f"\n❌ ReportGeneratorDB tests FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("\n" + "=" * 80)
    print("DATABASE-ONLY EXECUTION TRACKING - TEST SUITE")
    print("=" * 80)
    
    # Check database path
    db_path = get_db_path('execution_tracker')
    print(f"\nDatabase path: {db_path}")
    print(f"Database exists: {os.path.exists(db_path)}")
    
    if not os.path.exists(db_path):
        print("\n⚠️  Database does not exist. Please run the app first to create it.")
        return 1
    
    # Run tests
    results = []
    
    results.append(("DatabaseExecutionTracker", test_database_execution_tracker()))
    results.append(("ScreenshotManagerDB", test_screenshot_manager_db()))
    results.append(("ReportGeneratorDB", test_report_generator_db()))
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(result[1] for result in results)
    
    print("\n" + "=" * 80)
    if all_passed:
        print("✅ ALL TESTS PASSED")
        print("=" * 80)
        return 0
    else:
        print("❌ SOME TESTS FAILED")
        print("=" * 80)
        return 1


if __name__ == '__main__':
    sys.exit(main())

